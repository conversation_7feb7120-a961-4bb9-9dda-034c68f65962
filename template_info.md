# 源码自动生成模板 pandora-boot-archetype-docker

### 概述

* 模板: pandora-boot-archetype-docker
* 模板答疑人: [子观](https://work.alibaba-inc.com/nwpipe/u/64988)
* 模板使用时间: 2020-01-07 19:57:11

### Docker
* Image: reg.docker.alibaba-inc.com/bootstrap/image
* Tag: 0.1
* SHA256: e4b70f4f7d0b60aa3e5666eba441a376b31ec6e0bd550a4efc5af8f057c6d7d8

### 用户输入参数
* repoUrl: "**************************:AliexpressDataBank/ae-qa-trade-data-s.git" 
* appName: "ae-qa-trade-data-s" 
* javaVersion: "1.8" 
* groupId: "com.aliexpress.databank" 
* artifactId: "ae-qa-trade-data-s" 
* style: "hsf" 
* operator: "68715" 

### 上下文参数
* appName: ae-qa-trade-data-s
* operator: 68715
* gitUrl: **************************:AliexpressDataBank/ae-qa-trade-data-s.git
* branch: master


### 命令行
	sudo docker run --rm -v `pwd`:/workspace -e repoUrl="**************************:AliexpressDataBank/ae-qa-trade-data-s.git" -e appName="ae-qa-trade-data-s" -e javaVersion="1.8" -e groupId="com.aliexpress.databank" -e artifactId="ae-qa-trade-data-s" -e style="hsf" -e operator="68715"  reg.docker.alibaba-inc.com/bootstrap/image:0.1

