<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

    <parent>
        <groupId>com.taobao</groupId>
        <artifactId>parent</artifactId>
        <version>2.0.0</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <groupId>com.aliexpress.databank</groupId>
    <artifactId>ae-qa-trade-data-s</artifactId>
    <packaging>pom</packaging>
    <version>1.0.2-SNAPSHOT</version>
    <name>ae-qa-trade-data-s</name>

    <properties>
        <maven.compiler.target>1.8</maven.compiler.target>
        <maven.compiler.source>1.8</maven.compiler.source>
        <java.version>1.8</java.version>
        <project.build.sgourceEncoding>UTF-8</project.build.sgourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <mockito-all.version>1.10.19</mockito-all.version>
        <maven-antrun.version>1.8</maven-antrun.version>
        <spring-boot.version>1.5.22.RELEASE</spring-boot.version>
        <pandora-boot.version>2020-11-release-mesh-0429</pandora-boot.version>
        <pandora-boot-maven-plugin.version>2.1.15-mesh</pandora-boot-maven-plugin.version>
        <bings.framework.version>1.3.0-SNAPSHOT</bings.framework.version>
    </properties>

    <modules>
        <module>ae-qa-trade-data-s-service</module>
        <module>ae-qa-trade-data-s-start</module>
        <module>test-case-generate</module>
        <module>testcase</module>
        <module>ae-qa-trade-data-api</module>
    </modules>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.alibaba.fpark</groupId>
                <artifactId>bings-framework-et</artifactId>
                <version>${bings.framework.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.alibaba.fpark</groupId>
                <artifactId>bings-framework-base</artifactId>
                <version>${bings.framework.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.fpark</groupId>
                <artifactId>bings-framework-executor</artifactId>
                <version>${bings.framework.version}</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.noear/snack3 -->
            <dependency>
                <groupId>com.jayway.jsonpath</groupId>
                <artifactId>json-path</artifactId>
                <version>2.5.0</version>
            </dependency>
            <dependency>
                <groupId>org.noear</groupId>
                <artifactId>snack3</artifactId>
                <version>3.1.14</version>
                <scope>provided</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.taobao.pandora</groupId>
                <artifactId>pandora-boot-starter-bom</artifactId>
                <version>${pandora-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.boot</groupId>
                <artifactId>pandora-apphealth-spring-boot-starter</artifactId>
                <version>2020-11-release-mesh-0429</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloudnative</groupId>
                <artifactId>cloudn-application-lifecycle</artifactId>
                <version>1.1.0</version>
            </dependency>

            <dependency>
                <groupId>com.aliexpress.databank</groupId>
                <artifactId>ae-qa-trade-data-s-service</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.aliexpress.databank</groupId>
                <artifactId>testcase</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.aliexpress.databank</groupId>
                <artifactId>test-case-generate</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-all</artifactId>
                <version>${mockito-all.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>jcl-over-slf4j</artifactId>
                <version>1.7.26</version>
            </dependency>
            <dependency>
                <groupId>org.slf4j</groupId>
                <artifactId>slf4j-log4j12</artifactId>
                <version>999-not-exist-v3</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.boot</groupId>
                <artifactId>eagleeye-spring-boot-starter</artifactId>
                <version>1.7.4.8.1</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.saiga</groupId>
                <artifactId>saiga-link-case-api</artifactId>
                <version>1.0.1-SNAPSHOT</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.ae.group</groupId>
                <artifactId>ae-trade-reverse-api</artifactId>
                <version>2.1.95-AE-RELEASE</version>
                <exclusions>
                    <exclusion>
                        <groupId>*</groupId>
                        <artifactId>*</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            <dependency>
                <groupId>com.alibaba.global</groupId>
                <artifactId>reverse-platform-api</artifactId>
                <version>1.1.80-baseline-gray</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba.global</groupId>
                <artifactId>global-reverse-api</artifactId>
                <version>1.1.10-AE-cancelOrderKernel-RELEASE</version>
            </dependency>
        </dependencies>

    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <artifactId>maven-antrun-plugin</artifactId>
                    <version>${maven-antrun.version}</version>
                </plugin>
                <plugin>
                    <groupId>com.taobao.pandora</groupId>
                    <artifactId>pandora-boot-maven-plugin</artifactId>
                    <version>${pandora-boot-maven-plugin.version}</version>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>
</project>
