package com.aliexpress.testcase.gen.service;

import com.aliexpress.testcase.gen.model.Module;
import com.aliexpress.testcase.gen.model.SubModule;
import com.aliexpress.testcase.gen.model.TestCase;
import org.xmind.core.CoreException;

import java.io.IOException;
import java.util.Optional;

public interface TestCaseService {

    void generateTestCases(String module) throws IOException, CoreException;

    Module getModule(String fileName) throws IOException;

    Optional<SubModule> getSubModule(Module module, String subModule) throws IOException;

    Optional<SubModule> getSubModule(String fileName, String subModule) throws IOException;

    TestCase getTestCase(SubModule subModule, int id, String description);

    SubModule validSubModule(Optional<SubModule> subModule, String subModuleName);

    TestCase validTestCase(Optional<TestCase> testCase, String caseInfo);
}
