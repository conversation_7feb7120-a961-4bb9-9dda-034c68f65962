package com.aliexpress.testcase.gen.model;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
public class SubModule {

    private String subModule;
    private int caseCount;
    private List<TestCase> subTopics;
    private boolean isSuccess;
    private String comment;
//
//    public SubModule(String scenario) {
//        this.scenario = scenario;
//    }
//
//    public boolean getSuccessResult() {
//        if (checkPoints.isEmpty()) {
//            return false;
//        } else {
//            for (CheckPoint checkPoint : checkPoints) {
//                if (!checkPoint.isSuccess()) {
//                    return false;
//                }
//            }
//        }
//        return true;
//    }

}
