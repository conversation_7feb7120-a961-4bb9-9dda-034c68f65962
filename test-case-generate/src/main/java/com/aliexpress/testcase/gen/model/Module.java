package com.aliexpress.testcase.gen.model;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Data
public class Module {

    // 模块名
    private String module;
    // 需求文档
    private String brd;
    // 技术文档
    private String techDoc;
    // 测试用例
    private List<SubModule> subModules = new ArrayList<>();

    // 是否成功
    private boolean isSuccess;

    // 用例总数
    private int caseCount;
    // 通过率
    private String passingRate;
    // 总结
    private String summary;
    // 创建时间
    private LocalDateTime gmtCreate = LocalDateTime.now();

    public Module(){

    }


    public Module(String module, String brd, String techDoc) {
        this.module = module;
        this.brd = brd;
        this.techDoc = techDoc;
    }

//    public Module generateTestCasesReport(boolean isSuccess, String summary) {
//        this.setSuccess(isSuccess);
//        this.setCaseCount(subModules == null ? 0 : this.subModules.size());
//        this.setSummary(summary);
//        this.setPassingRate(getPassingRate());
//        return this;
//    }
    //    public String getPassingRate() {
//        if (testCases == null || testCases.size() == 0) {
//            return "0%";
//        } else {
//            float successCases = 0;
//            for (SubModule subModule : testCases) {
//                if (subModule.isSuccess()) {
//                    successCases++;
//                }
//            }
//            float passingRate = successCases / testCases.size();
//            return String.format("%.2f", passingRate);
//        }
//    }

}

