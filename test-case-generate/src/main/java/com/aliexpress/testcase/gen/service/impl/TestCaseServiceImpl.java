package com.aliexpress.testcase.gen.service.impl;

import com.alibaba.common.lang.StringUtil;
import com.alibaba.fastjson.JSON;
import com.aliexpress.testcase.gen.model.Module;
import com.aliexpress.testcase.gen.model.SubModule;
import com.aliexpress.testcase.gen.model.TestCase;
import com.aliexpress.testcase.gen.service.TestCaseService;
import com.aliexpress.testcase.gen.util.FileUtil;
import com.taobao.hsf.app.spring.util.annotation.HSFProvider;
import org.apache.commons.io.FileUtils;
import org.codehaus.plexus.util.StringUtils;
import org.springframework.core.io.ClassPathResource;
import org.xmind.core.*;

import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@HSFProvider(serviceInterface = TestCaseService.class, serviceGroup = "HSF", clientTimeout = 20000)
public class TestCaseServiceImpl implements TestCaseService {

    private static final String PREFIX = "tc:";
    private static final String SUFFIX = "_m";

    @Override
    public Module getModule(String fileName) throws IOException {
        ClassPathResource classPathResource = new ClassPathResource(fileName);
        File file = classPathResource.getFile();
        String content = FileUtils.readFileToString(file, "UTF-8");
        return JSON.parseObject(content, Module.class);
    }

    @Override
    public Optional<SubModule> getSubModule(String fileName, String subModule) throws IOException {
        Module module = getModule(fileName);
        return getSubModule(module, subModule);
    }

    @Override
    public Optional<SubModule> getSubModule(Module module, String subModule) throws IOException {
        return module.getSubModules().stream().filter(it -> it.getSubModule().trim().equalsIgnoreCase(subModule.trim())).findFirst();
    }

    @Override
    public TestCase getTestCase(SubModule subModule, int id, String description) {
        Optional<TestCase> testCase = subModule.getSubTopics().stream().filter(i -> i.getId() == id).findFirst();
        return validTestCase(testCase, description);
    }

    @Override
    public SubModule validSubModule(Optional<SubModule> subModule, String subModuleName) {
        if (!subModule.isPresent()) {
            SubModule emptyModule = new SubModule();
            emptyModule.setSuccess(false);
            emptyModule.setComment("Fail to get " + subModuleName + " test case. Pls check tc.");
            emptyModule.setCaseCount(0);
            return emptyModule;
        }
        SubModule testCases = subModule.get();
        testCases.setSuccess(true);
        testCases.setCaseCount(testCases.getSubTopics().size());
        return testCases;
    }

    @Override
    public TestCase validTestCase(Optional<TestCase> testCase, String caseInfo) {
        if (testCase.isPresent()) {
            TestCase emptyCase = new TestCase();
            emptyCase.setSuccess(false);
            emptyCase.setDescription("Fail to get " + caseInfo + " case info. Please check tc.");
            return emptyCase;
        }
        return testCase.get();
    }

    @Override
    public void generateTestCases(String module) throws IOException, CoreException {
        String xmind = getXmindName(module);
        IWorkbookBuilder builder = Core.getWorkbookBuilder();
        IWorkbook workbook = builder.createWorkbook(xmind);
        ISheet isheet = workbook.getPrimarySheet();
        if (StringUtils.isNotBlank(module)) {
            String content = FileUtil.getFileContent(module);
            Module testCases = JSON.parseObject(content, Module.class);
            ITopic rootTopic = isheet.getRootTopic();
            rootTopic.setTitleText(xmind.split("_")[0]);
            rootTopic.setFolded(true);

            ITopic docTopic = getDoc(workbook, testCases);
            rootTopic.add(docTopic);

            for (SubModule subModule : testCases.getSubModules()) {
                ITopic subModuleTopic = getSubModuleTopics(workbook, subModule);
                rootTopic.add(subModuleTopic);
            }
        } else {
            // TODO
        }
        workbook.save(xmind);
    }

    private String getXmindName(String module) {
        StringBuilder name = new StringBuilder();
        if (StringUtil.isNotBlank(module)) {
            String[] modules = module.split("/");
            String fileName = modules[modules.length - 1];
            name.append(fileName.replace(".json", ""));
        } else {
            name.append("all");
        }
        LocalDateTime now = LocalDateTime.now();
        name.append("_")
                .append(now.getMonth().getValue())
                .append(now.getDayOfMonth())
                .append("_")
                .append(now.getHour())
                .append(":")
                .append(now.getMinute())
                .append(":")
                .append(now.getSecond())
                .append(".xmind");
        return name.toString();
    }

    private ITopic getSubModuleTopics(IWorkbook workbook, SubModule subModule) {
        ITopic subModuleTopic = workbook.createTopic();
        subModuleTopic.setTitleText(subModule.getSubModule());
        subModule.getSubTopics().forEach(it -> {
            ITopic testCase = workbook.createTopic();
            testCase.setTitleText(getPath(it.getPath(), it.getFaultLevel()));
            ITopic checkPoint = workbook.createTopic();
            checkPoint.setTitleText(formatCheckPoints(it.getCheckPoints()));
            testCase.add(checkPoint);
            subModuleTopic.add(testCase);
        });
        return subModuleTopic;
    }

    private String getPath(String path, String faultLevel) {
        StringBuilder sb = new StringBuilder();
        sb.append(PREFIX).append(faultLevel.toLowerCase()).append(SUFFIX).append(" ").append(path);
        return sb.toString();
    }

    private String formatCheckPoints(List<String> checkPoints) {
        StringBuilder text = new StringBuilder();
        checkPoints.forEach(it -> text.append(it).append("\n"));
        text.deleteCharAt(text.length() - 1);
        return text.toString();

    }

    private ITopic getDoc(IWorkbook workbook, Module testCases) {
        ITopic doc = workbook.createTopic();
        doc.setTitleText("文档");
        ITopic brd = workbook.createTopic();
        brd.setTitleText(testCases.getBrd());

        ITopic tecDoc = workbook.createTopic();
        tecDoc.setTitleText(testCases.getTechDoc());

        doc.add(brd);
        doc.add(tecDoc);
        return doc;
    }

    public static void main(String[] args) throws IOException, CoreException {
        TestCaseServiceImpl test = new TestCaseServiceImpl();
        test.generateTestCases("tc/spserver/guarantee.json");
    }


}
