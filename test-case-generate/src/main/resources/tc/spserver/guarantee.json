{"module": "售后宝开放协商签约", "brd": "https://yuque.antfin-inc.com/saf7ph/yeof8g/qy02uf", "techDoc": "https://yuque.antfin.com/docs/share/ea229dcd-7d53-47a6-8eac-4be742a79e20?#", "subModules": [{"subModule": "页面回归", "subTopics": [{"id": 1, "path": "商品/模版/售后和服务", "faultLevel": "P3", "checkPoints": ["多语言：售后和服务list && 管理服务(售后宝纠纷订单协商功能)", "特色服务/售后宝纠纷订单协商功能", "服务状态：已加入/已退出/未加入", "服务状态为未加入。卖家在售后和服务list中点击申请加入进入detail页面，坐下和右上有申请加入按钮，点击申请加入，提示加入规则，未勾选服务条款，点击加入服务，提示必填。勾选服务条款，点击加入服务，提示success，跳转到售后和服务list页面。", "服务状态为已加入。按钮展示查看详情。进入管理服务。右上角更多按钮，点开展示退出服务。点击退出服务，弹出提示框。显示7天后才可以加入(前端文案写死，后台可配置)，点击确定，提示success，跳转到售后和服务list页面。服务状态:已退出（xxxx-xx-xx xx:xx:xx 后可再次加入）。申请加入按钮置灰，点击标题，左下和右上的申请加入按钮置灰"]}]}, {"subModule": "签约功能", "subTopics": [{"id": 1, "path": "卖家未加入售后宝开放协商：卖家点击申请加入按钮，进到详情页面，点击加入按钮，加入成功。买家购买售后宝类目商品，走售后宝开放协商流程", "faultLevel": "P3", "checkPoints": ["getPromiseList(): 服务状态为未加入，button为申请加入按钮", "getPromiseDetail(): ", "joinOrEditPromise(): 成功加入服务", "getPromiseDetail(): ", ""]}, {"id": 2, "path": "卖家已加入售后宝开放协商：卖家点击查看详情后，选择退出服务，退出服务成功。买家购买售后宝类目商品，走到售后宝直接上升仲裁流程", "faultLevel": "P3", "checkPoints": ["getPromiseDetail(): visible|name|content", "getPromiseList(): 服务状态为已加入，button为查看详情", "joinOrEditPromise(): 成功退出服务", "getPromiseDetail(): "]}]}, {"subModule": "子账号权限校验功能", "subTopics": [{"id": 1, "path": "卖家未加入售后宝开放协商：子账号点击申请加入，加入成功", "faultLevel": "P3", "checkPoints": ["joinOrEditPromise(): 成功加入服务"]}, {"id": 2, "path": "卖家已加入售后宝开放协商：子账号点击退出服务，推出成功", "faultLevel": "P3", "checkPoints": ["joinOrEditPromise(): 成功退出服务"]}]}]}