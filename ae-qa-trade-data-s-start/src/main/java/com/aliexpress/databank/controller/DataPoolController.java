package com.aliexpress.databank.controller;

import com.alibaba.common.lang.StringUtil;
import com.alibaba.fastjson.JSONObject;
import com.aliexpress.databank.dataobject.ProductPool;
import com.aliexpress.databank.dataobject.ResponseDTO;
import com.aliexpress.databank.dataobject.UserPool;
import com.aliexpress.databank.hsf.DataPoolService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Controller
public class DataPoolController {

	private static final Logger logger = LoggerFactory.getLogger(DataPoolController.class);

	@Autowired
	DataPoolService dataPoolService;

	/**
	 * 查询用户信息
	 * @return Boolean
	 */
	@GetMapping("/queryUserInfo")
	@ResponseBody
	public ResponseDTO queryUserInfo(HttpServletRequest request){
		ResponseDTO responseDTO = new ResponseDTO();
		String userName = (String)request.getParameter("userName");
		String accountId = (String) request.getParameter("accountId");
		String address = (String)request.getParameter("address");
		String enableType = (String)request.getParameter("enableType");
		String isUsed = (String)request.getParameter("isUsed");
		String num = (String)request.getParameter("num");
		String userTag = (String)request.getParameter("userTag");
		UserPool userPool = new UserPool();
        if (StringUtil.isNotBlank(isUsed)) {
            userPool.setIsUsed(Integer.parseInt(isUsed));
		}
		if (StringUtil.isNotBlank(userName)){
			userPool.setUserName(userName);
		}
		if (StringUtil.isNotBlank(accountId)){
			userPool.setAccountId(Long.parseLong(accountId));
		}
		if (StringUtil.isNotBlank(address)){
			userPool.setAddressTag(address);
		}
		if (StringUtil.isNotBlank(enableType)){
			userPool.setEnableType(Integer.parseInt(enableType));
		}
		if (StringUtil.isNotBlank(userTag)){
			userPool.setUserTag(userTag);
		}
		int tempNum = 1000;
		if (StringUtil.isNotBlank(num)){
			tempNum = Integer.parseInt(num);
		}
		try {
			List<UserPool> userPoolList = dataPoolService.queryUserInfo(userPool,tempNum);
			if (userPoolList != null && !userPoolList.isEmpty()){
				responseDTO.setData(userPoolList);
				responseDTO.setMessage("查询成功");
			}
			else {
				responseDTO.setMessage("查询无数据");
			}
			responseDTO.setSuccess(true);
		}catch (Exception e){
			responseDTO.setSuccess(false);
			responseDTO.setData(new JSONObject());
		}
		return responseDTO;
	}


	/**
	 * 查询商品信息
	 * @return Boolean
	 */
	@GetMapping("/queryProductInfo")
	@ResponseBody
	public ResponseDTO queryProductInfo(HttpServletRequest request){
		ResponseDTO responseDTO = new ResponseDTO();
		String productType = (String)request.getParameter("productType");
		String categoryId = (String) request.getParameter("categoryId");
		String productId = (String)request.getParameter("productId");
		String enableType = (String)request.getParameter("enableType");
		String isUsed = (String)request.getParameter("isUsed");
		String lessPrice = (String)request.getParameter("lessPrice");
		String largePrice = (String)request.getParameter("largePrice");
		String num = (String)request.getParameter("num");
		ProductPool productPool = new ProductPool();
		if (StringUtil.isNotBlank(isUsed)){
			productPool.setIsUsed(Integer.parseInt(isUsed));
		}
		if (StringUtil.isNotBlank(productType)){
			productPool.setProductType(productType);
		}
		if (StringUtil.isNotBlank(productId)){
			productPool.setProductId(Long.parseLong(productId));
		}
		if (StringUtil.isNotBlank(categoryId)){
			productPool.setCategoryId(Long.parseLong(categoryId));
		}
		if (StringUtil.isNotBlank(enableType)){
			productPool.setEnableType(Integer.parseInt(enableType));
		}
		int tempNum = 1000;
		if (StringUtil.isNotBlank(num)){
			tempNum = Integer.parseInt(num);
		}
		long less = 0L;
		if (StringUtil.isNotBlank(lessPrice) && !"0".equals(lessPrice)){
			less = Long.parseLong(lessPrice);
		}
		long large = 0L;
		if (StringUtil.isNotBlank(largePrice) && !"0".equals(largePrice)){
			large = Long.parseLong(largePrice);
		}
		try {
			List<ProductPool> productPoolList = dataPoolService.queryProductInfo(productPool,tempNum, less, large);
			if (productPoolList != null && !productPoolList.isEmpty()){
				responseDTO.setData(productPoolList);
				responseDTO.setMessage("查询成功");
			}
			else {
				responseDTO.setMessage("查询无数据");
			}
			responseDTO.setSuccess(true);
		}catch (Exception e){
			responseDTO.setSuccess(false);
			responseDTO.setData(new JSONObject());
		}
		return responseDTO;
	}

}
