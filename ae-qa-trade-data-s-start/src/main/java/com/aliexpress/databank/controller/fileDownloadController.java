package com.aliexpress.databank.controller;

import org.apache.commons.io.IOUtils;
import org.apache.poi.hpsf.Date;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;

@Controller
public class fileDownloadController {


    @RequestMapping(value = "/fileDownload")
    @ResponseBody
    public String fileDownload(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String msg = "ok";
//        // 用来检测程序运行时间
//        long startTime = System.currentTimeMillis();
//        String filename = "/home/<USER>/ae-qa-trade-data-s/target/addressResult.xlsx";
//        File file = new File(filename);
//        byte[] data = getFileByteArray(filename);
//        Date date = new Date();
//        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
//        String now = sdf.format(date);
//        try {
//            if (data == null) {
//                throw new FileNotFoundException("文件没有找到！");
//            }
//            String filename2 = "eclopppppp.zip";
//            response.setContentType("application/binary;charset=UTF-8");
//            response.setHeader("Content-Disposition", "attachment;fileName=" + URLEncoder.encode(filename2, "UTF-8"));
//            response.setContentLength(data.length);
//            response.getOutputStream().write(data);
//            response.getOutputStream().flush();
//            response.getOutputStream().close();
//            msg = "download success!!!";
//        } catch (FileNotFoundException e) {
//            e.printStackTrace();
//            msg = "download fail!!!";
//        }
//
//        long endTime = System.currentTimeMillis();
//        System.out.println("下载的运行时间：" + String.valueOf(endTime - startTime) + "ms");
        return msg;
    }

    /**
     * 写入本地磁盘
     *
     * @param filename
     * @param data
     */
    public void createNewFile(String filename, byte[] data) {
        File file = new File(filename);
        OutputStream output = null;
        try {
            if (!file.exists())
                file.createNewFile();
            output = new FileOutputStream(file);
            IOUtils.write(data, output);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (null != output) {
                try {
                    output.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
                output = null;
            }
        }

    }

    /**
     * 下载文件
     *
     * @param filename
     * @return
     */
    public byte[] getFileByteArray(String filename) {
        File file = new File(filename);
        InputStream input = null;
        byte[] data = null;
        try {
            if (!file.exists())
                return null;

            input = new FileInputStream(file);
            data = IOUtils.toByteArray(input);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (null != input) {
                try {
                    input.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
                input = null;
            }
        }
        return data;
    }
}
