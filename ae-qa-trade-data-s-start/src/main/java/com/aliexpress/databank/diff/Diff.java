package com.aliexpress.databank.diff;

import com.alibaba.fastjson.JSONObject;
import org.javers.core.Javers;
import org.javers.core.JaversBuilder;

public class Diff {

    public static void main(String[] args) {

        String pre = "{\"success\":true,\"module\":{\"seller\":{\"phone\":null,\"sellerKeyEnum\":null,\"fullName\":\"Bape-qianduoduo Store\",\"extraParams\":{},\"sellerType\":null,\"userId\":**********,\"ttid\":null,\"email\":null},\"gmtModified\":{\"dateTime\":{\"date\":{\"month\":1,\"year\":2022,\"day\":6},\"time\":{\"hour\":22,\"nano\":0,\"minute\":30,\"second\":1}},\"offset\":{\"seconds\":28800},\"zoneId\":\"Asia/Shanghai\"},\"timeoutType\":null,\"bizCode\":\"ali.global.ae.general\",\"tradeOrderId\":****************,\"timeouts\":[],\"operatorDTO\":null,\"createOperatorDTO\":null,\"applyReasons\":null,\"features\":{\"featureMap\":{\"ae_old_issue_status\":\"processing\",\"ae_old_issue_id\":\"****************\",\"ae_solution_status\":\"1\",\"platformMars\":\"true\",\"ae_dispute_sub_status\":\"2\",\"ae_business_type\":\"italy_local_issue\",\"ae_tci\":\"200000343\",\"ae_is_old\":\"1\",\"ae_lid\":\"200000344\",\"ae_solution_type\":\"1\",\"ae_solution_owner\":\"2\"}},\"reverseOrderLineId\":230686822754399,\"reverseType\":{\"name\":\"RETURN\"},\"rejectReason\":null,\"remainAmount\":null,\"fundOrderDTO\":null,\"paymentId\":null,\"applyRefundAmount\":null,\"timeoutTrigger\":null,\"refundMoneyPostWithOutAmount\":null,\"reverseStatusEnum\":{\"name\":\"REFUND_PENDING\"},\"refundAmount\":null,\"refundPaymentMethod\":null,\"ofcStatus\":null,\"reverseOrderId\":225558582754399,\"applyReason\":{\"features\":{\"featureMap\":{}},\"code\":0,\"tipsList\":null,\"multiLanguageText\":{\"localText\":null,\"defaultText\":null,\"mcmsKey\":null},\"extraParams\":{},\"id\":0,\"text\":null,\"parentReason\":null,\"ttid\":null},\"gmtCreate\":{\"dateTime\":{\"date\":{\"month\":1,\"year\":2022,\"day\":3},\"time\":{\"hour\":22,\"nano\":0,\"minute\":23,\"second\":58}},\"offset\":{\"seconds\":28800},\"zoneId\":\"Asia/Shanghai\"},\"clawbackDTO\":null,\"paidShippingAmount\":null,\"itemDTO\":{\"itemId\":1005003359706067,\"itemPicUrl\":\"H1511b3747f6f4ec3808b5cdf5edcb8c3N.jpg\",\"itemUnitPrice\":null,\"itemTitle\":\"THRASHER's new flame-Blood Raven Black Flame-back fashion jacket loose pullovers versatile casual hoodie\",\"skuId\":null},\"buyer\":{\"mobilePhone\":null,\"phone\":null,\"nickName\":null,\"memberLevel\":null,\"fullName\":\"ES d\",\"extraParams\":{},\"userId\":2059344399,\"ttid\":null,\"email\":null},\"arbitrationDTO\":{\"disputeReasonId\":\"null\",\"reasonText\":null,\"disputeStatus\":1,\"disputeId\":\"40021241993\",\"disputeDisplayStatus\":null,\"params\":{},\"clawbackDTO\":null,\"inDispute\":false},\"maxRefundFeeDTO\":null,\"paymentMethod\":null,\"siteId\":null,\"orderFrom\":null,\"issueOperatorDTO\":null,\"refundMoneyPostAmount\":null,\"reverseOperationRecordDTOList\":[],\"paidAmount\":null,\"tradeOrderLineId\":3014617216714399,\"goodsStatusEnum\":{\"name\":\"INIT\"}},\"errorCode\":null,\"repeated\":false,\"retry\":false}";
        String online = "{\"success\":true,\"module\":{\"seller\":{\"phone\":null,\"sellerKeyEnum\":null,\"fullName\":\"Bape-qianduoduo Store\",\"extraParams\":{},\"sellerType\":null,\"userId\":**********,\"ttid\":null,\"email\":null},\"gmtModified\":{\"dateTime\":{\"date\":{\"month\":1,\"year\":2022,\"day\":6},\"time\":{\"hour\":22,\"nano\":0,\"minute\":30,\"second\":1}},\"offset\":{\"seconds\":28800},\"zoneId\":\"Asia/Shanghai\"},\"timeoutType\":null,\"bizCode\":\"ali.global.ae.general\",\"tradeOrderId\":****************,\"timeouts\":[],\"operatorDTO\":null,\"createOperatorDTO\":null,\"applyReasons\":null,\"features\":{\"featureMap\":{\"ae_old_issue_status\":\"processing\",\"ae_old_issue_id\":\"****************\",\"ae_solution_status\":\"1\",\"platformMars\":\"true\",\"ae_dispute_sub_status\":\"2\",\"ae_business_type\":\"italy_local_issue\",\"ae_tci\":\"200000343\",\"ae_is_old\":\"1\",\"ae_lid\":\"200000344\",\"ae_solution_type\":\"1\",\"ae_solution_owner\":\"2\"}},\"reverseOrderLineId\":230686822754399,\"reverseType\":{\"name\":\"RETURN\"},\"rejectReason\":null,\"remainAmount\":null,\"fundOrderDTO\":{\"features\":{\"featureMap\":{\"ae_fund_detail\":\"{\\\"tradeOrderId\\\":****************,\\\"tradeOrderLinerId\\\":3014617216714399,\\\"reverseOrderId\\\":225558582754399,\\\"reverseOrderLineId\\\":230686822754399,\\\"refundMoneyPostAmount\\\":{\\\"amount\\\":2900.00,\\\"currency\\\":\\\"EUR\\\"},\\\"refundMoneyPostWithoutTaxAmount\\\":{\\\"amount\\\":2900.00,\\\"currency\\\":\\\"EUR\\\"},\\\"cash\\\":{\\\"amount\\\":2900.00,\\\"currency\\\":\\\"EUR\\\"},\\\"cashPost\\\":{\\\"amount\\\":2900.00,\\\"currency\\\":\\\"EUR\\\"},\\\"coupon\\\":{\\\"amount\\\":0.00,\\\"currency\\\":\\\"EUR\\\"},\\\"couponPost\\\":{\\\"amount\\\":0.00,\\\"currency\\\":\\\"EUR\\\"},\\\"tax\\\":{\\\"amount\\\":0.00,\\\"currency\\\":\\\"EUR\\\"},\\\"taxPost\\\":{\\\"amount\\\":0.00,\\\"currency\\\":\\\"EUR\\\"}}\"}},\"reverseOrderLineId\":230686822754399,\"maxOrderLineRefundDTO\":{\"refundLimitList\":[],\"totalMaxRefund\":{\"cent\":0,\"currencyCode\":\"USD\"},\"tradeOrderLineId\":null},\"fundDetailDTO\":{\"funds\":{}},\"payerList\":[],\"paidAmount\":{\"cent\":0,\"currencyCode\":\"USD\"},\"refundAmount\":{\"cent\":2900,\"currencyCode\":\"EUR\"}},\"paymentId\":null,\"applyRefundAmount\":{\"cent\":2900,\"currencyCode\":\"USD\"},\"timeoutTrigger\":null,\"refundMoneyPostWithOutAmount\":null,\"reverseStatusEnum\":{\"name\":\"REFUND_PENDING\"},\"refundAmount\":{\"$ref\":\"$.body.response.module.fundOrderDTO.refundAmount\"},\"refundPaymentMethod\":null,\"ofcStatus\":null,\"reverseOrderId\":225558582754399,\"applyReason\":{\"features\":{\"featureMap\":{}},\"code\":null,\"tipsList\":null,\"multiLanguageText\":{\"localText\":null,\"defaultText\":null,\"mcmsKey\":null},\"extraParams\":{},\"id\":0,\"text\":null,\"parentReason\":null,\"ttid\":null},\"gmtCreate\":{\"dateTime\":{\"date\":{\"month\":1,\"year\":2022,\"day\":3},\"time\":{\"hour\":22,\"nano\":0,\"minute\":23,\"second\":58}},\"offset\":{\"seconds\":28800},\"zoneId\":\"Asia/Shanghai\"},\"clawbackDTO\":null,\"paidShippingAmount\":{\"cent\":0,\"currencyCode\":\"USD\"},\"itemDTO\":{\"itemId\":1005003359706067,\"itemPicUrl\":\"H1511b3747f6f4ec3808b5cdf5edcb8c3N.jpg\",\"itemUnitPrice\":{\"cent\":29,\"currencyCode\":\"USD\"},\"itemTitle\":\"THRASHER's new flame-Blood Raven Black Flame-back fashion jacket loose pullovers versatile casual hoodie\",\"skuId\":null},\"buyer\":{\"mobilePhone\":null,\"phone\":null,\"nickName\":null,\"memberLevel\":null,\"fullName\":\"ES d\",\"extraParams\":{},\"userId\":2059344399,\"ttid\":null,\"email\":null},\"arbitrationDTO\":{\"disputeReasonId\":\"0\",\"reasonText\":null,\"disputeStatus\":1,\"disputeId\":\"40021241993\",\"disputeDisplayStatus\":null,\"params\":{},\"clawbackDTO\":null,\"inDispute\":false},\"maxRefundFeeDTO\":{\"maxRefundFee\":{\"cent\":0,\"currencyCode\":\"USD\"},\"maxForwardShippingFee\":null},\"paymentMethod\":\"MIXEDCARD\",\"siteId\":null,\"orderFrom\":1,\"issueOperatorDTO\":null,\"refundMoneyPostAmount\":null,\"reverseOperationRecordDTOList\":[],\"paidAmount\":{\"$ref\":\"$.body.response.module.fundOrderDTO.paidAmount\"},\"tradeOrderLineId\":3014617216714399,\"goodsStatusEnum\":{\"name\":\"INIT\"}},\"errorCode\":null,\"repeated\":false,\"retry\":false}";

        JSONObject preJSON = JSONObject.parseObject(pre);
        JSONObject onlineJSON = JSONObject.parseObject(online);
        Javers javers = JaversBuilder.javers().build();
        org.javers.core.diff.Diff pre2Online = javers.compare(preJSON, onlineJSON);
        System.err.println(javers.getJsonConverter().toJson(pre2Online));
        System.err.println("====");
        org.javers.core.diff.Diff online2Pre = javers.compare(onlineJSON, preJSON);
        System.err.println(javers.getJsonConverter().toJson(online2Pre));

    }
}
