package com.aliexpress.databank.easyreturn.warehouseid;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import org.apache.commons.io.FileUtils;
import org.springframework.core.io.ClassPathResource;

import java.io.File;
import java.io.IOException;
import java.lang.reflect.Type;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

public class WarehouseDiff {

    public static void main(String[] args) throws IOException {
        // ae part
        Map<String, String> aeWarehouseIds = new HashMap<>();
        String aeTxt = getRequestFromJson("warehouse/ae.txt");
        JSONObject aeJsonObj = JSON.parseObject(aeTxt);
        Set<String> countries = aeJsonObj.keySet();
        countries.forEach(country -> {
            String warehouseCode = JSONObject.parseObject(aeJsonObj.getString(country)).getString("warehouseCode");
            String address = JSONObject.parseObject(aeJsonObj.getString(country)).getString("address");
            aeWarehouseIds.put(address, warehouseCode);
        });

        // ant part
        String antTxt = getRequestFromJson("warehouse/ant.txt");
        JSONObject antJsonObj = JSON.parseObject(antTxt);
        String antCountries = antJsonObj.getString("recipientAddressAndWarehouseCodeMapping");
        Gson gson = new Gson();
        Type type = new TypeToken<Map<String, String>>() {
        }.getType();
        Map<String, String> antWarehouseIds = gson.fromJson(antCountries, type);
        if (aeWarehouseIds.size() != antWarehouseIds.size()) {
            System.err.println("海外仓ae与蚂蚁数量不一致！ ae: " + aeWarehouseIds.size() + ", ant: " + antWarehouseIds.size());
        }
        aeWarehouseIds.keySet().forEach(it -> {
            if (antWarehouseIds.get(it).isEmpty()) {
                System.err.println("ant missing " + it);
            } else if (!aeWarehouseIds.get(it).equals(antWarehouseIds.get(it))) {
                System.err.println("warehouse not match. address: " + it + ". AE: " + aeWarehouseIds.get(it) + ". Ant: " + antWarehouseIds.get(it));
            }
        });

    }

    private static String getRequestFromJson(String fileName) throws IOException {
        ClassPathResource classPathResource = new ClassPathResource(fileName);
        File file = classPathResource.getFile();
        return FileUtils.readFileToString(file, "UTF-8");
    }

}
