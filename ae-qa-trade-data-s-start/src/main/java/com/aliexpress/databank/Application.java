package com.aliexpress.databank;

import com.taobao.pandora.boot.PandoraBootstrap;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * Pandora Boot应用的入口类
 *
 * <AUTHOR>
 */
@SpringBootApplication(scanBasePackages = {"com.aliexpress"})
@MapperScan("com.aliexpress.databank.mapper")
public class Application {

    public static void main(String[] args) {
        PandoraBootstrap.run(args);
        SpringApplication.run(Application.class, args);
        PandoraBootstrap.markStartupAndWait();
    }
}
