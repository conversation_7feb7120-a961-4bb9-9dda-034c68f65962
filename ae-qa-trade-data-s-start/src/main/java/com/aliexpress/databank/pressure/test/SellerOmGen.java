package com.aliexpress.databank.pressure.test;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.io.FileUtils;
import org.springframework.core.io.ClassPathResource;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class SellerOmGen {

    public void getQueryOrderListPressureInfoInTest(String[] keys, String fileName, String domain) throws IOException {
        String request = getRequestFromJson("pressure/seller/ns.queryOrderList/" + fileName);
        ClassPathResource classPathResource = new ClassPathResource("pressure/seller/ns.queryOrderList/");
        File file = new File(classPathResource.getPath() + fileName + LocalDateTime.now().toString() + ".txt");
        if (!file.getParentFile().exists()) {
            file.getParentFile().mkdirs();
        }
        boolean isCreated = file.createNewFile();
        OutputStream os = new FileOutputStream(file);
        for (String key : keys) {
            try {
                String info = getQueryOrderListPressureInfoInTest(request, domain, key);
                byte[] data = info.getBytes();
                os.write(data);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    private String getRequestFromJson(String fileName) throws IOException {
        ClassPathResource classPathResource = new ClassPathResource(fileName);
        File file = classPathResource.getFile();
        return FileUtils.readFileToString(file, "UTF-8");
    }

    public String[] getKeys(String fileName) throws IOException {
        String request = getRequestFromJson("pressure/seller/ns.queryOrderList/test/" + fileName);
        return request.split("\n");
    }

    public List<String> getSellerIds(String fileName) throws IOException {
        List<String> sellerIds = new ArrayList<>();
        String content = getRequestFromJson("pressure/seller/" + fileName);
        for (String str : content.split("\n")) {
            JSONArray jsonArray = JSONArray.parseArray(str);
            String sellerId = jsonArray.getJSONObject(0).getString("sellerId");
            sellerIds.add(sellerId);
        }
        return sellerIds;
    }

    public String getQueryOrderListPressureInfo(String request, String sellerId) throws IOException {
        String pre = "{\"argsTypes\":[\"com.alibaba.global.merchant.order.request.OrderQueryRequest\"],\"argsObjs\":[";
        String content = generateQueryOrderListRequest(request, sellerId);
        String end = "]}\n";
        return pre + content + end;
    }

    public String getQueryOrderListPressureInfoInTest(String request, String key, String value) throws IOException {
        String pre = "{\"argsTypes\":[\"com.alibaba.global.merchant.order.request.OrderQueryRequest\"],\"argsObjs\":[";
        String content = generateQueryOrderListRequestInTest(request, key, value);
        String end = "]}\n";
        return pre + content + end;
    }

    private String generateQueryOrderListRequestInTest(String request, String key, String value) {
        JSONObject jsonObject = JSONObject.parseObject(request);
        jsonObject.put(key, value);
        return jsonObject.toJSONString();
    }

    public String generateQueryOrderListRequest(String request, String sellerId) throws IOException {
        JSONObject jsonObject = JSONObject.parseObject(request);
        jsonObject.put("userId", sellerId);
        jsonObject.put("sellerId", sellerId);
        return jsonObject.toJSONString();
    }

    public void getQueryOrderListOfMerchantPressureText(List<String> sellerIds, String fileName) throws IOException {
        String request = getRequestFromJson("pressure/seller/open.queryOrderList/" + fileName).replace("\n", "").replace(" ", "");
        ClassPathResource classPathResource = new ClassPathResource("pressure/seller/open.queryOrderList/");
        File file = new File(classPathResource.getPath() + fileName + LocalDateTime.now().toString() + ".txt");
        if (!file.getParentFile().exists()) {
            file.getParentFile().mkdirs();
        }
        boolean isCreated = file.createNewFile();
        OutputStream os = new FileOutputStream(file);
        if (isCreated) {
            sellerIds.forEach(sellerId -> {
                try {
                    String info = getQueryOrderListOfMerchantPressureInfo(request, sellerId);
                    byte[] data = info.getBytes();
                    os.write(data);
                } catch (IOException e) {
                    e.printStackTrace();
                }
            });
        }
    }

    public String getQueryOrderListOfMerchantPressureInfo(String request, String sellerLoginId) throws IOException {
        String pre = "{\"argsTypes\":[\"com.alibaba.intl.ae.biz.aeop.share.dataobject.trade.AeopOrderQuery\"],\"argsObjs\":[";
        String content = generateQueryOrderListOfMerchantRequest(request, sellerLoginId);
        String end = "]}\n";
        return pre + content + end;
    }

    private String generateQueryOrderListOfMerchantRequest(String request, String sellerLoginId) {
        JSONObject jsonObject = JSONObject.parseObject(request);
        jsonObject.put("adminLoginId", sellerLoginId);
        return jsonObject.toJSONString();
    }

    public void getQueryBatchTradeOrdersPressureText(List<String> sellerIds, String fileName) throws IOException {
        String request = getRequestFromJson("pressure/seller/ns.queryBatchTradeOrders/" + fileName).replace("\n", "").replace(" ", "");
        ClassPathResource classPathResource = new ClassPathResource("pressure/seller/ns.queryBatchTradeOrders/");
        File file = new File(classPathResource.getPath() + fileName + LocalDateTime.now().toString() + ".txt");
        if (!file.getParentFile().exists()) {
            file.getParentFile().mkdirs();
        }
        boolean isCreated = file.createNewFile();
        OutputStream os = new FileOutputStream(file);
        if (isCreated) {
            sellerIds.forEach(sellerId -> {
                try {
                    String info = getQueryBatchTradeOrdersInfo(request, sellerId);
                    byte[] data = info.getBytes();
                    os.write(data);
                } catch (IOException e) {
                    e.printStackTrace();
                }
            });
        }
    }

    public String getQueryBatchTradeOrdersInfo(String request, String sellerId) throws IOException {
        String pre = "{\"argsTypes\":[\"com.alibaba.global.order.management.api.request.QueryForSellerRequest\"],\"argsObjs\":[";
        String content = getQueryBatchTradeOrdersRequest(request, sellerId);
        String end = "]}\n";
        return pre + content + end;
    }

    private String getQueryBatchTradeOrdersRequest(String request, String sellerId) {
        JSONObject jsonObject = JSONObject.parseObject(request);
        jsonObject.put("sellerId", sellerId);
        return jsonObject.toJSONString();
    }

    public String getQueryOrderDetails(String request, String sellerLoginId, String orderId) throws IOException {
        String pre = "{\"argsTypes\":[\"java.util.Map\",\"com.alibaba.intl.ae.biz.aeop.share.dataobject.trade.AeopTpSingleOrderQuery\"],\"argsObjs\":";
        String content = getQueryOrderDetailsRequest(request, sellerLoginId, orderId);
        String end = "}\n";
        return pre + content + end;
    }

    /**
     * @param orderInfo key:orderId, value:sellerLoginId
     * @param fileName
     * @throws IOException
     */
    public void getQueryOrderDetailsPressureText(Map<String, String> orderInfo, String fileName) throws IOException {
        String request = getRequestFromJson("pressure/seller/open.queryOrderDetail/" + fileName).replace("\n", "").replace(" ", "");
        ClassPathResource classPathResource = new ClassPathResource("pressure/seller/open.queryOrderDetail/");
        File file = new File(classPathResource.getPath() + fileName + LocalDateTime.now().toString() + ".txt");
        if (!file.getParentFile().exists()) {
            file.getParentFile().mkdirs();
        }
        boolean isCreated = file.createNewFile();
        OutputStream os = new FileOutputStream(file);
        if (isCreated) {
            orderInfo.keySet().forEach(orderId -> {
                try {
                    String info = getQueryOrderDetails(request, orderInfo.get(orderId), orderId);
                    byte[] data = info.getBytes();
                    os.write(data);
                } catch (IOException e) {
                    e.printStackTrace();
                }
            });
        }
    }


    private String getQueryOrderDetailsRequest(String request, String sellerLoginId, String orderId) {
        JSONArray jsonArray = JSONArray.parseArray(request);
        jsonArray.getJSONObject(0).put("login_id", sellerLoginId);
        jsonArray.getJSONObject(1).put("orderId", orderId);
        return JSONArray.toJSONString(jsonArray);
    }

    /**
     * @param orderInfo key:orderId, value:sellerLoginId
     * @param fileName
     * @throws IOException
     */
    public void getQueryOrderListPressureText(Map<String, String> orderInfo, String fileName) throws IOException {
        String request = getRequestFromJson("pressure/seller/open.queryOrderList/" + fileName).replace("\n", "").replace(" ", "");
        ClassPathResource classPathResource = new ClassPathResource("pressure/seller/open.queryOrderList/");
        File file = new File(classPathResource.getPath() + fileName + LocalDateTime.now().toString() + ".txt");
        if (!file.getParentFile().exists()) {
            file.getParentFile().mkdirs();
        }
        boolean isCreated = file.createNewFile();
        OutputStream os = new FileOutputStream(file);
        if (isCreated) {
            orderInfo.keySet().forEach(orderId -> {
                try {
                    String info = getQueryOrderDetailsRequest(request, orderInfo.get(orderId), orderId);
                    byte[] data = info.getBytes();
                    os.write(data);
                } catch (IOException e) {
                    e.printStackTrace();
                }
            });
        }
    }

    private List<String> getSellerLoginIds(String fileName) throws IOException {
        List<String> sellerLoginIds = new ArrayList<>();
        String content = getRequestFromJson("pressure/seller/" + fileName);
        for (String line : content.split("\n")) {
            sellerLoginIds.add(line.split(",")[1].trim());
        }
        return sellerLoginIds;
    }

    // key: orderId, value:loginId
    public Map<String, String> getOrderInfo(String fileName) throws IOException {
        Map<String, String> orderInfo = new ConcurrentHashMap<>();
        String content = getRequestFromJson("pressure/seller/" + fileName);
        for (String line : content.split("\n")) {
            String[] info = line.split(",");
            if (info.length == 4) {
                orderInfo.put(info[0].trim(), info[3].trim());
            }
        }
        return orderInfo;
    }

    public static void main(String[] args) throws IOException {
        SellerOmGen sellerOmGen = new SellerOmGen();
//         共19w+卖家 分为4个场景压测 all - 占比1/3  newOrder/awaitingPayment/toBeShippedAndPartialShipped/inCancel - 占比1/6
//        List<String> sellerIds = sellerOmGen.getSellerIds("sellerId1.txt");
//        List<String> all = sellerIds.subList(0, 60000);
//        sellerOmGen.getQueryOrderListPressureText(all, "all.json");
//        List<String> newOrder = sellerIds.subList(60001, 91000);
//        sellerOmGen.getQueryOrderListPressureText(newOrder, "newOrder.json");
//        List<String> awaitingPayment = sellerIds.subList(60001, 91000);
//        sellerOmGen.getQueryOrderListPressureText(awaitingPayment, "awaitingPayment.json");
//        List<String> toBeShippedAndPartialShipped = sellerIds.subList(91001, 122000);
//        sellerOmGen.getQueryOrderListPressureText(toBeShippedAndPartialShipped, "toBeShippedAndPartialShipped.json");
//        List<String> inCancel = sellerIds.subList(122000, sellerIds.size());
//        sellerOmGen.getQueryOrderListPressureText(inCancel, "inCancel.json");

        // 模糊查询 -- 根据买家名字
//        String[] buyerNamesKeys = sellerOmGen.getKeys("buyerName.txt");
//        sellerOmGen.getQueryOrderListPressureInfoInTest(buyerNamesKeys, "searchByBuyerName.json", "buyerName");

        // 模糊查询 -- 根据商品名字
//        String[] productNameKeys = sellerOmGen.getKeys("productName.txt");
//        sellerOmGen.getQueryOrderListPressureInfoInTest(productNameKeys, "searchByProductName.json", "lastSelectOrderStatus");

        // queryOrderList -- pageSize
//        List<String> sellerIds = sellerOmGen.getSellerIds("sellerId1.txt");
//        List<String> pageSize10 = sellerIds.subList(0, 62500);
//        sellerOmGen.getQueryOrderListPressureText(pageSize10, "page/page10.json");
//        List<String> pageSize20 = sellerIds.subList(62501, 75000);
//        sellerOmGen.getQueryOrderListPressureText(pageSize20, "page/page20.json");
//        List<String> pageSize50 = sellerIds.subList(75001, 87500);
//        sellerOmGen.getQueryOrderListPressureText(pageSize50, "page/page50.json");
//        List<String> pageSize100 = sellerIds.subList(87501, 100000);
//        sellerOmGen.getQueryOrderListPressureText(pageSize100, "page/page100.json");

        // batchQueryOrderList
//        List<String> sellerIds = sellerOmGen.getSellerIds("sellerId1.txt");
//        List<String> all = sellerIds.subList(0, 60000);
//        sellerOmGen.getQueryBatchTradeOrdersPressureText(all, "all.json");
//        List<String> awaitingConfirm = sellerIds.subList(60001, 91000);
//        sellerOmGen.getQueryBatchTradeOrdersPressureText(awaitingConfirm, "awaitingConfirm_1day.json");
//        sellerOmGen.getQueryBatchTradeOrdersPressureText(awaitingConfirm, "awaitingConfirm_3d.json");
//        List<String> finish = sellerIds.subList(91001, 122000);
//        sellerOmGen.getQueryBatchTradeOrdersPressureText(finish, "finish_1d.json");
//        sellerOmGen.getQueryBatchTradeOrdersPressureText(finish, "finish_3d.json");
//        List<String> inCancel = sellerIds.subList(122000, sellerIds.size());
//        sellerOmGen.getQueryBatchTradeOrdersPressureText(inCancel, "inCancel_1d.json");
//        sellerOmGen.getQueryBatchTradeOrdersPressureText(inCancel, "inCancel_3d.json");

        // merchant-open order list
//        List<String> sellerLoginIds = sellerOmGen.getSellerLoginIds("sellerLoginId.txt");
//        List<String> all = sellerLoginIds.subList(0, 30000);
//        sellerOmGen.getQueryOrderListOfMerchantPressureText(all, "all.json");
//        List<String> all1 = sellerLoginIds.subList(30001, 50000);
//        sellerOmGen.getQueryOrderListOfMerchantPressureText(all1, "all.json");
//        List<String> all2 = sellerLoginIds.subList(50001, 80000);
//        sellerOmGen.getQueryOrderListOfMerchantPressureText(all2, "all.json");
//        List<String> awaitingPayment = sellerLoginIds.subList(80001, 90001);
//        sellerOmGen.getQueryOrderListOfMerchantPressureText(awaitingPayment, "awaitingPayment.json");
//        List<String> waitingShipment = sellerLoginIds.subList(90001, 120000);
//        sellerOmGen.getQueryOrderListOfMerchantPressureText(waitingShipment, "waitingShipment.json");
//        List<String> waitingShipment1 = sellerLoginIds.subList(120001, 140000);
//        sellerOmGen.getQueryOrderListOfMerchantPressureText(waitingShipment1, "waitingShipment.json");
//        List<String> inCancel = sellerLoginIds.subList(140001, 160000);
//        sellerOmGen.getQueryOrderListOfMerchantPressureText(inCancel, "inCancel.json");
//        List<String> insDispute = sellerLoginIds.subList(160001, 170000);
//        sellerOmGen.getQueryOrderListOfMerchantPressureText(insDispute, "inDispute.json");
//        List<String> finish = sellerLoginIds.subList(170001, 200000);
//        sellerOmGen.getQueryOrderListOfMerchantPressureText(finish, "finish.json");


        // merchant-open order detail
//        Map<String, String> orderInfo = sellerOmGen.getOrderInfo("orders.txt");
//        sellerOmGen.getQueryOrderDetailsPressureText(orderInfo, "detail.json");


    }

}


