project.name=ae-qa-trade-data-s

management.securitmvny.enabled = false

# hsféç½®ï¼è¯¦è§ http://gitlab.alibaba-inc.com/middleware-container/pandora-boot/wikis/spring-boot-hsf
spring.hsf.group=HSF
spring.hsf.version=1.0.0
spring.hsf.timeout=20000
spring.env = pre
spring.security.jsonp.enabled = false

#diamond
spring.diamond.group-id=ae-qa-databank
tradeIp.target=***********

metaq.client.url=*************

daily.supported.tenantIds=AE_GLOBAL
daily.default.tenantId=AE_GLOBAL
supported.oeIds=AE

# tddl éç½®
spring.datasource.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.url=***********************************************************
mybatis.mapper-locations=classpath*:mapper/*.xml

### é¢ååºç¨ç®¡çæ¥çgroupId
#spring.schedulerx2.groupId=ae-qa-trade-data-s.sg.pre
## 1.1.0ä»¥åçæ¬ï¼éè¦è®¾ç½®appKeyï¼åºç¨ç®¡çæ¥çappKey
#spring.schedulerx2.appKey=sNJox0SI7yMhfhm6eoWmyA==
#spring.schedulerx2.domainName = pre-sg-schedulerx2.alibaba-inc.com

#çº¿ä¸åºç¨ç®¡çæ¥çgroupId
global.exchange.bizCode=ali.internation.ae.common

# çº¿ä¸åºç¨ç®¡çæ¥çgroupId
spring.schedulerx2.groupId=ae-qa-trade-data-s.sg
## 1.1.0ä»¥åçæ¬ï¼éè¦è®¾ç½®appKeyï¼åºç¨ç®¡çæ¥çappKey
spring.schedulerx2.appKey=m0o/IOp05Y/UUs3QCgTJEw==
spring.schedulerx2.domainName = sg.schedulerx2.alibaba-inc.com
#spring.schedulerx2.groupId=ae-qa-trade-data-s.sg
# 1.1.0ä»¥åçæ¬ï¼éè¦è®¾ç½®appKeyï¼åºç¨ç®¡çæ¥çappKey
#spring.schedulerx2.appKey=m0o/IOp05Y/UUs3QCgTJEw==
#spring.schedulerx2.domainName = sg.schedulerx2.alibaba-inc.com
