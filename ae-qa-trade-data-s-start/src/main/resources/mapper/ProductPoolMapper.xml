<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aliexpress.databank.mapper.ProductPoolMapper">

    <resultMap id="BaseResultMap" type="com.aliexpress.databank.dataobject.ProductPool">
        <id column="id" jdbcType="BIGINT" property="id" />
        <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
        <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
        <result column="product_id" jdbcType="BIGINT" property="productId" />
        <result column="enable_type" jdbcType="TINYINT" property="enableType" />
        <result column="stock_num" jdbcType="BIGINT" property="stockNum" />
        <result column="product_type" jdbcType="VARCHAR" property="productType" />
        <result column="price" jdbcType="BIGINT" property="price" />
        <result column="is_used" jdbcType="TINYINT" property="isUsed" />
        <result column="seller_id" jdbcType="BIGINT" property="sellerId" />
        <result column="category_id" jdbcType="BIGINT" property="categoryId" />
        <result column="is_del" jdbcType="TINYINT" property="isDel" />
    </resultMap>

    <sql id="Base_Column_List">
        id, product_id, product_type, price, enable_type, seller_id, is_used,
        stock_num, category_id, features, gmt_create, gmt_modified, is_del
    </sql>

    <sql id="ResultMap">
        id,product_id, product_type, price, enable_type, seller_id,
        stock_num, category_id, features
    </sql>

    <insert id="insertProduct">
        insert into ae_product_pool (<include refid="Base_Column_List"/>)
        values
        (
        null, #{productPool.productId}, #{productPool.productType}, #{productPool.price}, 1, #{productPool.sellerId}, 0,
        #{productPool.stockNum}, #{productPool.categoryId}, #{productPool.features}, now(), now(), 0

        )
    </insert>

    <insert id="insertProductList" parameterType="java.util.List">
        insert into ae_product_pool (<include refid="Base_Column_List"/>)
        values
        <foreach collection="productPoolList" item="item" index="index" separator=",">
            (
            null,#{item.productId}, #{item.productType},#{item.price}, 1, #{item.sellerId}, 0,
            #{item.stockNum}, #{item.categoryId}, #{item.features}, now(), now(), 0
            )
        </foreach>
    </insert>

    <update id="updateProduct" parameterType="com.aliexpress.databank.dataobject.ProductPool">
        update ae_product_pool
        <set>
            gmt_modified = now(),
            <if test="productPool.productType != null and productPool.productType != ''">
                product_type= #{productPool.productType},
            </if>
            <if test="productPool.price != null">
                price= #{productPool.price},
            </if>
            <if test="productPool.enableType != null">
                enable_type= #{productPool.enableType},
            </if>
            <if test="productPool.isUsed != null">
                is_used= #{productPool.isUsed},
            </if>
            <if test="productPool.stockNum != null">
                stock_num= #{productPool.stockNum},
            </if>
            <if test="productPool.features != null and productPool.features != ''">
                features= #{productPool.features},
            </if>
            <if test="productPool.isDel != null">
                is_del= #{productPool.isDel}
            </if>
        </set>
        where product_id = #{productPool.productId}
    </update>
    
    <select id="productList" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from ae_product_pool where `is_del` = 0
        <if test="productPool.productType != null and productPool.productType != ''">
            and (`product_type` LIKE CONCAT('%',#{productPool.productType},'%'))
        </if>
        <if test="largePrice != null and largePrice != 0">
            and #{largePrice} >= `price`
        </if>
        <if test="lessPrice != null and lessPrice != 0">
            and `price` >= #{lessPrice}
        </if>
        <if test="productPool.categoryId != null ">
            and `category_id` = #{productPool.categoryId}
        </if>
        <if test="productPool.enableType != null">
            and enable_type = #{productPool.enableType}
        </if>
        <if test="productPool.productId != null">
            and product_id = #{productPool.productId}
        </if>
        <if test="productPool.isUsed != null">
            and is_used= #{productPool.isUsed}
        </if>
        ORDER BY RAND()
        LIMIT #{num}
    </select>

    <select id="productCount" resultType="java.lang.Integer">
        SELECT count(*) FROM
        ae_product_pool where 1=1
        <if test="isDel != null">
            and is_del = #{isDel}
        </if>
        <if test="enableType != null">
            and enable_type= #{enableType}
        </if>
    </select>

    <select id="productCountById" resultType="java.lang.Integer">
        SELECT count(*) FROM
        ae_product_pool where product_id = #{productId}
    </select>

    <delete id="deleteProductByProductId" parameterType="java.lang.Long">
        delete from ae_product_pool
        where product_id = #{productId}
    </delete>

    <delete id="deleteProduct" parameterType="com.aliexpress.databank.dataobject.ProductPool">
        delete from ae_product_pool
        where 1=1
        <if test="productPool.productType != null and productPool.productType != ''">
            and (`product_type` LIKE CONCAT('%',#{productPool.productType},'%'))
        </if>
        <if test="productPool.categoryId != null ">
            and `category_id` = #{productPool.categoryId}
        </if>
        <if test="productPool.enableType != null">
            and enable_type = #{productPool.enableType}
        </if>
        <if test="productPool.productId != null">
            and product_id = #{productPool.productId}
        </if>
        <if test="productPool.isUsed != null">
            and is_used= #{productPool.isUsed}
        </if>
    </delete>

</mapper>