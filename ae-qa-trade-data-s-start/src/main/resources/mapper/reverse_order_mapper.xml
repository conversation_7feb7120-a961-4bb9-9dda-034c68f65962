<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aliexpress.databank.mapper.ReverseOrderMapper">

    <resultMap id="BaseResultMap" type="com.aliexpress.databank.dataobject.ReverseOrder">
        <result column="id" property="id"/>
        <result column="order_id" property="orderId"/>
        <result column="buyer_id" property="buyerId"/>
        <result column="buyer_location" property="buyerLocation"/>
        <result column="buyer_pre_env" property="buyerPreEnv"/>
        <result column="buyer_online_env" property="buyerOnlineEnv"/>
        <result column="seller_id" property="sellerId"/>
        <result column="seller_location" property="sellerLocation"/>
        <result column="seller_pre_env" property="sellerPreEnv"/>
        <result column="seller_online_env" property="sellerOnlineEnv"/>
        <result column="is_used" property="used"/>
        <result column="is_refund" property="refund"/>
        <result column="promise_template" property="promiseTemplate"/>
        <result column="reverse_type" property="reverseType"/>
        <result column="shipping_method" property="shippingMethod"/>
        <result column="is_multi_order_lines" property="multiOrderLines"/>
        <result column="is_over_dipuste_protection" property="overDisputeProtection"/>
        <result column="is_shipped" property="shipped"/>
        <result column="is_confirmed" property="confirmed"/>
        <result column="features" property="features"/>
        <result column="scenario" property="scenario"/>
        <result column="scenario_index" property="scenarioIndex"/>
        <result column="gmt_used" property="gmtUsed"/>
        <result column="gmt_created" property="gmtCreated"/>
        <result column="gmt_modified" property="gmtModified"/>
    </resultMap>


    <sql id="allColumns">
        id, order_id, buyer_id, buyer_pre_env, buyer_online_env,
        seller_id, seller_location, seller_pre_env, seller_online_env, is_used,
        is_refund, promise_template, reverse_type, shipping_method, is_multi_order_lines,
        is_over_dipuste_protection, is_confirmed, features, scenario, scenario_index
        gmt_used, gmt_created, gmt_modified
    </sql>

    <sql id="insertColumns">
        order_id, buyer_id, buyer_pre_env, buyer_online_env, seller_id,
        seller_location, seller_pre_env, seller_online_env, is_used, is_refund,
        promise_template, reverse_type, shipping_method, is_multi_order_lines, is_over_dipuste_protection,
        is_confirmed, features, scenario, scenario_index, gmt_created,
        gmt_modified, buyer_location, is_shipped
    </sql>

    <insert id="insertReverseOrders" parameterType="java.util.List">
        insert into reverse_order_pool (<include refid="insertColumns"/>)
        values
        <foreach collection="reverseOrders" item="it" index="index" separator=",">
            (
            #{it.orderId}, #{it.buyerId}, #{it.buyerPreEnv}, #{it.buyerOnlineEnv}, #{it.sellerId},
            #{it.sellerLocation}, #{it.sellerPreEnv}, #{it.sellerOnlineEnv}, #{it.used}, #{it.refund},
            #{it.promiseTemplate}, #{it.reverseType}, #{it.shippingMethod}, #{it.multiOrderLines},
            #{it.overDisputeProtection},
            #{it.confirmed}, #{it.features}, #{it.scenario}, #{it.scenarioIndex}, #{it.gmtCreated},
            #{it.gmtModified}, #{it.buyerLocation}, #{it.shipped}
            )
        </foreach>
    </insert>

    <select id="findOrderCountByBuyerIdAndScenario" resultType="java.lang.Integer">
        select count(id)
        from reverse_order_pool
        where buyer_id = #{buyerId} and is_used = 0 and is_refund = 0
        and scenario = #{scenario} and scenario_index = #{scenarioIndex}
    </select>

    <select id="findById" resultType="com.aliexpress.databank.dataobject.ReverseOrder">
        select
        <include refid="insertColumns"/>
        from reverse_order_pool
        where id = #{id}
    </select>

    <select id="findByIds" resultType="com.aliexpress.databank.dataobject.ReverseOrder">
        select
        <include refid="allColumns"/>
        from reverse_order_pool
        where id in
        <foreach collection="ids" item="id" index="index"
                 separator=",">
            #{id}
        </foreach>
    </select>


    <select id="findUnShipmentOrder" resultMap="BaseResultMap">
        select
        <include refid="allColumns"/>
        from reverse_order_pool
        where is_shipped = 0 and is_confirmed = 0 and is_used = 0 and is_refund = 0
    </select>

    <select id="findUnConfirmOrder" resultMap="BaseResultMap">
        select
        <include refid="allColumns"/>
        from reverse_order_pool
        where is_shipped = 1 and is_confirmed = 0 and is_used = 0 and is_refund = 0
    </select>

    <select id="getOrderIdsByScenarioIndex" resultType="java.lang.Long">
        select order_id
        from reverse_order_pool
        where is_used = 0 and scenario = #{scenario} and scenario_index = #{scenarioIndex}
        limit #{size}
    </select>

    <select id="getOrderIdsByScenario" resultType="java.lang.Long">
        select order_id
        from reverse_order_pool
        where is_used = 0 and scenario = #{scenario}
        limit #{size}
    </select>


    <update id="updateUsedByOrderId">
        update reverse_order_pool
        <set>
            gmt_modified = now(),
            is_used = 1
        </set>
        where order_id= #{orderId}
    </update>


    <update id="updateReverseOrder" parameterType="com.aliexpress.databank.dataobject.ReverseOrder">
        update reverse_order_pool
        <set>
            gmt_modified = now(),
            buyer_pre_env = #{reverseOrder.buyerPreEnv},
            buyer_online_env = #{reverseOrder.buyerOnlineEnv},
            seller_location = #{reverseOrder.sellerLocation},
            seller_pre_env = #{reverseOrder.sellerPreEnv},
            seller_online_env = #{reverseOrder.sellerOnlineEnv},
            is_used = #{reverseOrder.used},
            is_refund = #{reverseOrder.refund},
            promise_template = #{reverseOrder.promiseTemplate},
            shipping_method = #{reverseOrder.shippingMethod},
            is_multi_order_lines = #{reverseOrder.multiOrderLines},
            is_over_dipuste_protection = #{reverseOrder.overDisputeProtection},
            is_confirmed = #{reverseOrder.confirmed},
            features = #{reverseOrder.features},
            gmt_used = #{reverseOrder.gmtUsed},
            is_shipped = #{reverseOrder.shipped}
        </set>
        where order_id= #{reverseOrder.orderId}
    </update>

</mapper>