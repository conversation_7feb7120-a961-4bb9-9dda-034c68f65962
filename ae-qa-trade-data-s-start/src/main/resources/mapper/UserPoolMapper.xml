<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
    <mapper namespace="com.aliexpress.databank.mapper.UserPoolMapper">

    <resultMap id="BaseResultMap" type="com.aliexpress.databank.dataobject.UserPool">
        <result property="id" column="id"/>
        <result property="userName" column="user_name"/>
        <result property="password" column="password"/>
        <result property="enableType" column="enable_type"/>
        <result property="accountId" column="account_id"/>
        <result property="isUsed" column="is_used"/>
        <result property="userTag" column="user_tag"/>
        <result property="addressTag" column="address_tag"/>
        <result property="buyerLocation" column="buyer_location"/>
        <result property="features" column="features"/>
        <result property="gmtCreate" column="gmt_create"/>
        <result property="gmtModified" column="gmt_modified"/>
        <result property="isDel" column="is_del"/>
        <result property="scene" column="scene"/>
        <result property="memberId" column="member_id"/>
        <result property="gmtDead" column="gmt_dead"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, user_name, password, enable_type, account_id, is_used,
        user_tag, address_tag, buyer_location, features,gmt_create, gmt_modified,is_del,scene,member_id,gmt_dead
    </sql>

    <sql id="ResultMap">
        id, user_name, password, enable_type, account_id,
        user_tag, address_tag, buyer_location, features,scene,member_id,gmt_dead
    </sql>

    <insert id="insertUser">
        insert into ae_user_pool (<include refid="Base_Column_List"/>)
        values
        (
        null, #{userPool.userName}, #{userPool.password}, 1, #{userPool.accountId}, 0,
        #{userPool.userTag}, #{userPool.addressTag},#{userPool.buyerLocation}, #{userPool.features}, now(), now(), 0, #{userPool.scene},
        #{userPool.memberId},#{userPool.gmtDead}
        )
    </insert>

    <insert id="insertUserList" parameterType="java.util.List">
        insert into ae_user_pool (<include refid="Base_Column_List"/>)
        values
        <foreach collection="userPoolList" item="item" index="index" separator=",">
            (
            null, #{item.userName}, #{item.password}, 1, #{item.accountId}, 0,
            #{item.userTag}, #{item.addressTag},#{item.buyerLocation}, #{item.features}, now(), now(), 0, #{item.scene},
            #{item.memberId},#{item.gmtDead}
            )
        </foreach>
    </insert>

    <update id="updateUsed">
        update ae_user_pool
        <set>
            gmt_modified = now(),is_used= 0,
        </set>
        where is_used= 1
    </update>

    <update id="updateUser" parameterType="com.aliexpress.databank.dataobject.UserPool">
        update ae_user_pool
        <set>
            gmt_modified = now(),
            <if test="userPool.password != null and userPool.password != ''">
                password= #{userPool.password},
            </if>
            <if test="userPool.enableType != null">
                enable_type= #{userPool.enableType},
            </if>
            <if test="userPool.accountId != null">
                account_id= #{userPool.accountId},
            </if>
            <if test="userPool.isUsed != null">
                is_used= #{userPool.isUsed},
            </if>
            <if test="userPool.userTag != null and userPool.userTag != ''">
                user_tag= #{userPool.userTag},
            </if>
            <if test="userPool.addressTag != null and userPool.addressTag != ''">
                address_tag= #{userPool.addressTag},
            </if>
            <if test="userPool.buyerLocation != null and userPool.buyerLocation != ''">
                buyer_location= #{userPool.buyerLocation},
            </if>
            <if test="userPool.features != null and userPool.features != ''">
                features= #{userPool.features},
            </if>
            <if test="userPool.isDel != null">
                is_del= #{userPool.isDel},
            </if>
            <if test="userPool.gmtDead != null">
                gmt_dead= #{userPool.gmtDead},
            </if>
            <if test="userPool.memberId != null">
                member_id= #{userPool.memberId},
            </if>
            <if test="userPool.scene != null">
                scene= #{userPool.scene}
            </if>
        </set>
        where user_name= #{userPool.userName}
    </update>

    <select id="userList" parameterType="com.aliexpress.databank.dataobject.UserPool" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from ae_user_pool where `is_del` = 0
        <if test="userPool.userTag != null and userPool.userTag != ''">
            and (`user_tag` LIKE CONCAT('%',#{userPool.userTag},'%'))
        </if>
        <if test="userPool.addressTag != null ">
            and (`address_tag` LIKE CONCAT('%',#{userPool.addressTag},'%'))
        </if>
        <if test="userPool.userName != null and userPool.userName != ''">
            and (`user_name` LIKE CONCAT('%',#{userPool.userName},'%'))
        </if>
        <if test="userPool.enableType != null">
            and enable_type= #{userPool.enableType}
        </if>
        <if test="userPool.accountId != null">
            and account_id= #{userPool.accountId}
        </if>
        <if test="userPool.isUsed != null">
            and is_used= #{userPool.isUsed}
        </if>
        ORDER BY `gmt_create` DESC
        LIMIT #{num}
    </select>


    <select id="userCount" resultType="java.lang.Integer">
        SELECT count(*) FROM
        ae_user_pool where 1=1
        <if test="isDel != null">
            and is_del = #{isDel}
        </if>
        <if test="enableType != null">
            and enable_type= #{enableType}
        </if>
    </select>

    <select id="userCountById" resultType="java.lang.Integer">
        SELECT count(*) FROM
        ae_user_pool where account_id= #{accountId}
    </select>

    <delete id="deleteUserByUserName" parameterType="java.lang.String">
        delete from ae_user_pool
        where user_name = #{userName}
    </delete>

    <delete id="deleteUser" parameterType="com.aliexpress.databank.dataobject.UserPool">
        delete from ae_user_pool
        where 1=1
        <if test="userPool.userTag != null and userPool.userTag != ''">
            and (`user_tag` LIKE CONCAT('%',#{userPool.userTag},'%'))
        </if>
        <if test="userPool.addressTag != null ">
            and (`address_tag` LIKE CONCAT('%',#{userPool.addressTag},'%'))
        </if>
        <if test="userPool.userName != null and userPool.userName != ''">
            and (`user_name` LIKE CONCAT('%',#{userPool.userName},'%'))
        </if>
        <if test="userPool.enableType != null">
            and enable_type= #{userPool.enableType}
        </if>
        <if test="userPool.accountId != null">
            and account_id= #{userPool.accountId}
        </if>
        <if test="userPool.isUsed != null">
            and is_used= #{userPool.isUsed}
        </if>
    </delete>

</mapper>