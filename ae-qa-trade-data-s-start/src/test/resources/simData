{"respTime": "2021-04-28 20:35:10.716", "body": {"appName": "ae-trade-ns-s", "betaContext": null, "caseCategory": null, "dataSize": 296611, "envResourceId": null, "features": {"app_name": "ae-trade-ns-s", "mid": "com.alibaba.global.buy.api.facade.CheckoutMultiterminalFacade:1.0.0@renderOrder(RenderOrderRequest)", "compare_task_id": "550", "online_trace_id": "0b8f77e416196132610802351e8282_0.1.2.5"}, "mainInvokeIdentity": "com.alibaba.global.buy.api.facade.CheckoutMultiterminalFacade@renderOrder(RenderOrderRequest):1.0.0", "multiAppInvokeFlag": 0, "onlineContext": {"customInvokes": {}, "executedSql": [], "extInfo": {"invokeTimeConsuming": 0.0, "client_config": "{\"allowHsfInvoke\":true,\"allowLwpInvoke\":false,\"allowReplayDBRealRequest\":false,\"analysisMode\":false,\"aopMockerIdentities\":[\"com.alibaba.global.category.StandardCategoryClient@getStdCategory(Long,Locale)\",\"com.alibaba.global.ic.dto.scenario.query.FulfillmentModeQueryClient@queryFulfillmentMode(List,boolean)\",\"com.alibaba.ecommerce.international.impl.Java8TimeZoneLocalizedServiceImpl@currentInstant()\",\"com.alibaba.global.inventory.client.InventoryTradeClient@reduceInventory(Collection,TradeOptions)\",\"com.alibaba.global.supplychain.expression.client.DeliveryOptionClient@queryShippingOptionForSourceClient(List)\",\"com.alibaba.trade.function.delivery.repository.DeliveryOptionRepositoryImpl@generateSequence(int)\",\"com.alibaba.global.buy.utils.CheckoutDuplicateOrderValidateUtils@newValidate()\",\"com.alibaba.global.reverse.api.rich.client.ReverseRuleFacadeClient@queryReturnPolicy(ReturnPolicyQueryRequest)\",\"com.alibaba.global.ic.dto.scenario.query.ProductSellToControlQueryClient@queryProductSellControlInfo(ProductSellControlRequest,boolean)\",\"com.alibaba.trade.shared.utils.SequenceGenerator@next(String)\",\"com.aliyun.opensearch.SearcherClient@execute(SearchParamsBuilder)\",\"com.taobao.payment.exchangeplatform.client.api.impl.ExchangeQueryServiceImpl@queryExchangeRate(String,String,String)\",\"com.alibaba.global.supplychain.expression.client.DeliveryOptionClient@confirmShippingOptionForSourceClient(List)\",\"com.taobao.payment.exchangeplatform.client.api.impl.ExchangeQueryServiceImpl@queryExchangeRateFromCache(List,String,String)\",\"com.alibaba.aliexpress.price.client.common.service.impl.product.ProductPriceQueryClientImpl@queryBatchProductPrice(BatchProductPriceQueryParamDTO)\"],\"appControlInfo\":{\"analysisMode\":false,\"betaIpList\":[],\"collectIpList\":[],\"containerId\":436,\"ifMaxCollectCountMap\":{},\"multiAppMode\":false,\"run\":false,\"source\":\"tmall\",\"taskVerifyValue\":0},\"appId\":100004,\"appName\":\"ae-trade-ns-s\",\"autoMockDB\":true,\"betaFlowAccessClass\":\"com.global.trade.brp.TradeBrpCollectFlowAccessor\",\"blizzardCollectCaseNum\":\"5000,1000\",\"clientUpPeriod\":0,\"cloneSubInvokeParam\":false,\"cloneSubInvokeReturn\":false,\"closeSpecialEnumSerialize\":false,\"collectFlowAccessClass\":\"com.global.trade.brp.TradeCenterBetaFlowAccessor\",\"collectHostThrowMode\":false,\"collectSql\":false,\"containerId\":436,\"delayStartupTime\":100,\"disconnectComponents\":[\"hsf\",\"tair\",\"tddl\",\"mysqlDriver\",\"metaq\",\"tmf\"],\"disconnectType\":\"replay\",\"doomLibVersion\":\"8.0.7\",\"enableSwitch\":true,\"env\":0,\"envSync\":false,\"exceptionNewMode\":false,\"extra\":{\"msg_channel\":\"simulator-topic-1\",\"invoke.auto.find.filter.strategy\":\"{\\r\\n    \\\"HTTP\\\": [\\r\\n        {\\r\\n            \\\"type\\\": \\\"regex\\\",\\r\\n            \\\"value\\\": [\\r\\n                \\\".*/[0-9]+\\\\\\\\.(php|htm|html)\\\"\\r\\n            ]\\r\\n        },\\r\\n        {\\r\\n            \\\"type\\\": \\\"suffix\\\",\\r\\n            \\\"value\\\": [\\r\\n                \\\".xml\\\",\\r\\n                \\\".temp\\\",\\r\\n                \\\".war\\\",\\r\\n                \\\".zip\\\",\\r\\n                \\\".tar\\\",\\r\\n                \\\".bak\\\",\\r\\n                \\\".gz\\\",\\r\\n                \\\".rar\\\",\\r\\n                \\\".jpg\\\",\\r\\n                \\\".js\\\",\\r\\n                \\\".css\\\",\\r\\n                \\\".bmp\\\",\\r\\n                \\\".jpeg\\\",\\r\\n                \\\".png\\\"\\r\\n            ]\\r\\n        }\\r\\n    ],\\r\\n    \\\"HSF\\\": [],\\r\\n    \\\"JAVA\\\": []\\r\\n}\",\"domainOnline\":\"doom.alibaba-inc.com\",\"brp_coai\":\"LTAIYHIopvdy15qQ\",\"brp_coak\":\"wW1dLqs72YjVIp5by0UFFkQCAi3U7D\",\"brp_cob\":\"biz-sim-envresource\",\"configId\":\"ldbcommon-daily\",\"cob\":\"biz-sim-online-data\",\"coak\":\"bMAPyTC9aI7IVR1OFDsihDRlAxepwE\",\"coe\":\"oss-cn-zhangjiakou.aliyuncs.com\",\"brp_coe\":\"cn-hangzhou.oss-cdn.aliyun-inc.com\",\"coai\":\"LTAITCYo2H2l7y8a\",\"nameSpace\":\"1897\"},\"extraConfigJson\":\"{\\\"LoaderType\\\": \\\"SELECT_MOST\\\",\\\"plugin-paths\\\":[\\\"doom-plugin-external-4.0.1-native-SNAPSHOT.zip\\\"],\\\"ep-paths\\\":[\\\"ae-trade-handle-plugin-1.0.0-SNAPSHOT.zip\\\"]}\",\"forceCompare\":false,\"hsfProxyAfterSpringBeanPoster\":false,\"httpFilterSpecialFlag\":false,\"httpSendOnly\":false,\"httpServerPort\":8765,\"ignoreDBRead\":false,\"ignoreHsfPublishCheck\":false,\"ignoreTairRead\":false,\"ignoreTestRequest\":false,\"invokeConfigs\":[{\"identity\":\"com.alibaba.global.order.management.api.facade:1.0.0@queryBizFeatures(QueryTradeBizFeaturesRequest）\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.buy.api.facade.pc.PcCheckoutFacade:1.0.0@renderOrder(RenderQueryParams)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.buy.api.facade.pc.PcCheckoutFacade:1.0.0@adjustRender(AdjustQueryParams)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.buy.api.facade.pc.PcCheckoutFacade:1.0.0@createOrder(CreateQueryParams)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.buy.api.facade.CheckoutMultiterminalFacade:1.0.0@renderOrder(RenderOrderRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.buy.api.facade.CheckoutMultiterminalFacade:1.0.0@createOrder(CreateOrderRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade:1.0.0@queryBizFeatures(QueryTradeBizFeaturesRequest）\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade:1.0.0@batchQueryTradeOrderLinesBizFeatures(QueryTradeOrderLinesBizFeaturesRequest）\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderDeleteFacade:1.0.0@deleteTradeOrder(DeleteTradeOrderRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderDeleteFacade:1.0.0@permanentDeleteTradeOrder(DeleteTradeOrderRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderDeleteFacade:1.0.0@permanentDeleteTradeOrder(DeleteTradeOrdersRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderGroupManagementFacade:1.0.0@queryTradeOrderGroup(QueryTradeOrderGroupRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderHistoryFacade:1.0.0@queryOrderLineHistory(OrderLineHistoryRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderMatchFacade:1.0.0@convertTradeOrderId(Long)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderMatchFacade:1.0.0@convertTradeOrderIds(List<Long>)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderMatchFacade:1.0.0@convertTradeOrderLineId(Long)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderMatchFacade:1.0.0@convertTradeOrderLineIds(List<Long>)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderNotificationFacade:1.0.0@sendImbaMsg(OrderNotificationRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderSequenceFacade:1.0.0@queryOrderSequence(int,Long)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.BizFeaturesQueryFacade:1.0.0@queryBizFeatures(QueryTradeBizFeaturesRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.BizFeaturesQueryFacade:1.0.0@batchQueryTradeOrderLinesBizFeatures(QueryTradeOrderLinesBizFeaturesRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.buy.api.facade.pressure.PressureFacade:1.0.0@shipping(RenderOrderRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.buy.api.facade.pressure.PressureFacade:1.0.0@placeOrder(CreateOrderRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.buy.api.facade.CheckoutFacade:1.0.0@renderOrder(RenderOrderRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.buy.api.facade.CheckoutFacade:1.0.0@createOrder(CreateOrderRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.buy.api.facade.ErrorCodeFacade:1.0.0@queryDisplayMessage(String)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderMatchFacade:1.0.0@convertTradeOrderIds(List)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderMatchFacade:1.0.0@convertTradeOrderLineIds(List)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.buy.api.facade.pressure.wireless.WirelessPressureFacade:1.0.0@shipping(RenderQueryParams)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.buy.api.facade.pressure.wireless.WirelessPressureFacade:1.0.0@placeOrder(AdjustQueryParams)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.tradebrp.client.service.BrpReplayService:1.0.0.brp@doReplay(BrpReplayDO)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.tradebrp.client.service.BrpReplayService:1.0.0.brp@closeMetaQReceiver()\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.tradebrp.client.service.BrpReplayService:1.0.0.brp@doBatchReplay(BrpReplayDO)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.buy.api.facade.pc.PcCheckoutFacade:1.0.0@oneKeyCreateOrder(CreateQueryParams)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderRecoveryFacade:1.0.0@recoverOrder(RecoverTradeOrderRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderRecoveryFacade:1.0.0@recoverOrder(RecoverTradeOrdersRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.FeaturesOperateFacade:1.0.0@updateOrderFeatures(UpdateOrderFeaturesRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.FeaturesOperateFacade:1.0.0@updateOrderLineFeatures(UpdateOrderLineFeaturesRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.FeaturesOperateFacade:1.0.0@updateWholeOrderFeatures(UpdateWholeOrderFeaturesRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.TestFacade:1.0.0@test(String)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderCountFacade:1.0.0@countTradeOrder(QueryTradeOrderCountRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderCountFacade:1.0.0@countTradeOrderLine(QueryTradeOrderLineCountRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderCountFacade:1.0.0@tradeSummary(QueryTradeSummaryRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderCountFacade:1.0.0@tradeStatusBarCalculator(TradeStatusBarCalculatorRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderCountFacade:1.0.0@purchaseFrequency(PurchaseFrequencyRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderCountFacade:1.0.0@queryOrderLineCount(QueryTradeLineCountReq)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderCountFacade:1.0.0@countTradeOrderForWireless(QueryTradeCountForWirelessRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderCountFacade:1.0.0@countTradeOrderForNewCustomer(QueryTradeCountForNewCustomerRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderQueryFacade:1.0.0@queryTradeOrderById(Long)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderQueryFacade:1.0.0@queryTradeOrderLineById(Long)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderViewFacade:1.0.0.taiwan@*(*)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.carts.api.facade.CartFacade:2.0.0@*(*)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.carts.api.facade.CartMtopFacade:2.0.0@*(*)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderViewFacade:2.0.0@*(*)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderCallbackFacade:1.0.0.taiwan@*(*)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderQueryForSellerFacade:2.0.0@*(*)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderCallbackFacade:2.0.0@*(*)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderRegulateFacade:1.0.0@backUp(String,String,String,String,String)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderRegulateFacade:1.0.0@delete(String,String,String)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.buy.api.facade.pc.PcCheckoutFacade:2.0.0@*(*)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.buy.api.facade.wireless.WirelessCheckoutFacade:2.0.0@*(*)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.lazada.buy.api.facade.pc.PcCheckoutFacade:1.0.0@*(*)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.lazada.buy.api.facade.wireless.WirelessCheckoutFacade:1.0.0@*(*)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.lazada.carts.api.facade.CartFacade:1.0.0@*(*)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.lazada.carts.api.facade.CartMtopFacade:1.0.0@*(*)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.lazada.order.management.api.facade.OrderCallbackFacade:1.0.0@*(*)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.lazada.order.management.api.facade.OrderViewFacade:1.0.0@*(*)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\" com.alibaba.global.address.api.facade.LocationGstFreeFacade:1.0.0@*(*)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.buy.api.facade.wireless.WirelessCheckoutFacade:2.0.0_GL@*(*)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.BizFeaturesQueryFacade:1.0.0@batchQueryOrderLineBizFeatures(BatchQueryTradeOrderLineBizFeaturesRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.lazada.buy.api.facade.pc.PcCheckoutFacade:2.0.0@*(*)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.lazada.buy.api.facade.wireless.WirelessCheckoutFacade:2.0.0@*(*)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\" com.alibaba.global.order.management.api.facade.OrderQueryForBuyerFacade:2.0.0@*(*)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.ump.client.service.GlobalPromotionCalculateService:1.0.0@*(*)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.user.api.facade.UserReadFacade:1.0.0@*(*)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.shop.api.facade.ShopReadFacade:1.0.0@*(*)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderViewFacade:2.0.0_GL@*(*)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.lazada.order.management.api.facade.OrderQueryForSellerFacade:1.0.0@*(*)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.lazada.order.management.api.facade.OrderQueryForBuyerFacade:1.0.0@*(*)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.carts.api.facade.CartMtopFacade:1.0.0@*(*)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.carts.api.facade.CartDBFacade:1.0.0@*(*)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.lazada.wishlist.api.facade.WishlistMtopFacade:1.0.0@*(*)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.lazada.wishlist.api.facade.WishlistFacade:1.0.0@*(*)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.buy.api.facade.wireless.WirelessCheckoutFacade:1.0.0@renderOrder(RenderQueryParams)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.buy.api.facade.wireless.WirelessCheckoutFacade:1.0.0@adjustRender(AdjustQueryParams)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.buy.api.facade.wireless.WirelessCheckoutFacade:1.0.0@createOrder(CreateQueryParams)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.buy.api.facade.wireless.WirelessCheckoutFacade:1.0.0@oneKeyCreateOrder(CreateQueryParams)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.lazada.buy.api.facade.CheckoutFacade:1.0.0@*(*)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderManagementRegionFacade:2.0.0@updateBatchReverseInfo(UpdateBatchReverseInfoRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderManagementRegionFacade:2.0.0@closeOrder(CloseTradeRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.trade.open.define.facade.CheckoutFacade:1.0.0@*(*)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.merchant.order.facade.api.service.web.SellerOrderQueryWebFacade:2.0.0.POWERSEEKER_GL@*(*)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.merchant.order.facade.api.service.web.SellerOrderPrintWebFacade:2.0.0.POWERSEEKER_GL@*(*)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.trade.open.define.facade.OrderCancelFacade:1.0.0@*(*)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.merchant.order.facade.api.service.SellerOrderQueryFacade:2.0.0.POWERSEEKER_GL@*(*)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.merchant.order.facade.api.service.SellerOrderExportFacade:2.0.0.POWERSEEKER_GL@*(*)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.taobao.order.query.seller.facade.SellerOrderQueryOpenApiFacade:1.0.0@*(*)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderAddCartFacade:1.0.0@addOrderToCart(AddOrderToCartRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.wishlist.api.facade.WishlistMtopFacade:2.0.0@getItemCount(WishlistBaseRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.wishlist.api.facade.WishlistMtopFacade:2.0.0@addWishlistItem(WishlistAddItemRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.wishlist.api.facade.WishlistMtopFacade:2.0.0@deleteWishlistItem(WishlistDeleteItemRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.wishlist.api.facade.WishlistMtopFacade:2.0.0@getPassPurchaseItems(WishlistMtopQueryRequst)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.wishlist.api.facade.WishlistMtopFacade:2.0.0@getUserWishlistItems(WishlistBaseRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderTimeoutFacade:1.0.0@addTimeout(TimeoutAddRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.buy.api.facade.wireless.WirelessCheckoutFacade:1.0.0@queryAmendPackage(QueryAmendPackageParams)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.BizFeaturesQueryFacade:1.0.0@queryOrderLineBizFeatures(QueryTradeOrderLineBizFeaturesRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderHistoryFacade:1.0.0@createOrderLineHistory(CreateOrderLineHistoryRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.TestFacade:1.0.0@cache(String,String,Integer)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.TestFacade:1.0.0@getCache(String)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.TestFacade:1.0.0@removeCache(String)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.TestFacade:1.0.0@sendMsg(SendMessageRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderGroupManagementFacade:1.0.0@queryOrderLinesByGroup(QueryTradeOrderGroupRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.carts.api.facade.CartMtopFacade:2.0.0_GL@*(*)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.satellite.api.SatelliteRouteFacade:1.0.0@invoke(HsfRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.satellite.api.SatelliteRouteFacade:1.0.0@invoke(MetaqRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderManagementRegionFacade:1.0.0@closeOrder(CloseTradeRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderManagementRegionFacade:1.0.0@updateBatchReverseInfo(UpdateBatchReverseInfoRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.lazada.order.management.api.facade.OrderQueryForBuyerFacade:2.0.0@*(*)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.merchant.order.facade.OrderQueryWebFacade:1.0.0@*(*)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.merchant.order.facade.OrderQueryWebFacade:1.0.0@queryOrderList(OrderQueryRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.merchant.order.facade.OrderQueryWebFacade:1.0.0@queryOrderDetail(Long,OrderDetailQueryOption)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderViewFacade:1.0.0@*(*)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderQueryForBuyerFacade:1.0.0@*(*)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderQueryForSellerFacade:1.0.0@*(*)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.lazada.order.management.api.facade.OrderManagementFacade:1.0.0_GL@recalculateOrder(RecalculateOrderRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderManagementRegionFacade:1.0.0@updateSnapshot(SnapshotRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderManagementRegionFacade:1.0.0@unfreezeTradeOrder(UnfreezeTradeOrderRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderManagementRegionFacade:1.0.0@unFreezeTimeout(UnFreezeTimeoutRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderManagementRegionFacade:1.0.0@sendGoods(SellerSendGoodsRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderManagementRegionFacade:1.0.0@securityManualCheck(SecurityManualCheckRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderManagementRegionFacade:1.0.0@removeCache(CacheRemoveRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderManagementRegionFacade:1.0.0@receiveGoods(ReceiveGoodsRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderManagementRegionFacade:1.0.0@pushOrder(PushOrderRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderManagementRegionFacade:1.0.0@paymentIntentionConfirmed(PaymentIntentionConfirmedRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderManagementRegionFacade:1.0.0@paymentBuffering(PaymentBufferRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderManagementRegionFacade:1.0.0@paySuccess(PaySuccessRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderManagementRegionFacade:1.0.0@payRiskPassed(PaySuccessRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderManagementRegionFacade:1.0.0@freezeTradeOrder(FreezeTradeOrderRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderManagementRegionFacade:1.0.0@freezeTimeout(FreezeTimeoutRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderManagementRegionFacade:1.0.0@extendTimeout(ExtendTimeoutRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderManagementRegionFacade:1.0.0@editPayPrice(EditPayPriceRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderManagementRegionFacade:1.0.0@deliveryGoods(DeliveryGoodsRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderManagementRegionFacade:1.0.0@createTimeout(CreateTimeoutRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderManagementRegionFacade:1.0.0@createReverse(CreateReverseRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderManagementRegionFacade:1.0.0@createPaymentOrder(CreatePaymentOrderRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderManagementRegionFacade:1.0.0@createDeliveryOrder(CreateDeliveryOrderRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderManagementRegionFacade:1.0.0@checkPayAmount(CheckPayAmountRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderManagementRegionFacade:1.0.0@cancelReverse(CancelReverseRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderManagementRegionFacade:1.0.0@buyerConfirmGoods(BuyerConfirmGoodsRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderManagementRegionFacade:1.0.0@asyncEditPayPrice(EditPayPriceRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderQueryForSellerFacade:1.0.0@regionalQueryBatchTradeOrderLines(QueryBatchTradeOrderLinesRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderQueryForSellerFacade:1.0.0@querySearchValue(QueryESSearchValueRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderQueryForSellerFacade:1.0.0@queryBatchTradeOrders(QueryESTradeOrdersRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderQueryForSellerFacade:1.0.0@queryBatchTradeOrders(QueryESTradeOrdersByShopOwnerIdRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderQueryForSellerFacade:1.0.0@queryBatchTradeOrderLines(QueryESTradeOrderLinesRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.carts.api.facade.CartDBFacade:1.0.0.sirius@queryCartItems(Long)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.carts.api.facade.CartDBFacade:1.0.0.sirius@updateCartItemFeature(Long,Long,String,String)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.carts.api.facade.CartDBFacade:1.0.0.sirius@querySkuNum(Long)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.carts.api.facade.CartDBFacade:1.0.0.sirius@getMultiBuy(long,long)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.carts.api.facade.CartDBFacade:1.0.0.sirius@count(Long)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.trade.open.define.facade.CheckoutFacade:1.0.0-AE@preCreateOrderCheck(PreCreateOrderRequest,ClientInfo)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.trade.open.define.facade.CheckoutFacade:1.0.0-AE@renderOrder(RenderOrderRequest,ClientInfo,RenderOrderOption)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.trade.open.define.facade.CheckoutFacade:1.0.0-AE@preCreateOrder(PreCreateOrderRequest,ClientInfo,PreCreateOption)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.trade.open.define.facade.CheckoutFacade:1.0.0-AE@preCreateOrderForMtop(PreCreateOrderRequestForMtop)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.trade.open.define.facade.CheckoutFacade:1.0.0-AE@createOrder(CreateOrderRequest,ClientInfo,CreateOption)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.trade.open.define.facade.CheckoutFacade:1.0.0-AE@createOrderForMtop(CreateOrderRequestForMtop)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.trade.open.define.facade.CheckoutFacade:1.0.0-AE@bizCode()\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.carts.api.facade.CartMtopFacade:1.0.0.sirius@alihot(CartAlihotRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.carts.api.facade.CartMtopFacade:1.0.0.sirius@calculateProgressBar(CartProgressBarRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.carts.api.facade.CartMtopFacade:1.0.0.sirius@appointCalculate(CartMtopAppointCalculateRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.carts.api.facade.CartMtopFacade:1.0.0.sirius@getPromotionDetail(CartPromotionDetailRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderManagementFacade:1.0.0@createTimeout(CreateTimeoutRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.wishlist.api.facade.WishlistFacade:2.0.0@getItemCount(Long)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.wishlist.api.facade.WishlistFacade:2.0.0@getItemCount(String)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.wishlist.api.facade.WishlistFacade:2.0.0@addItems(List)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.wishlist.api.facade.WishlistFacade:2.0.0@addItem(WishlistItemDTO)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.wishlist.api.facade.WishlistFacade:2.0.0@addItem(WishlistAddTagForItemRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.wishlist.api.facade.WishlistFacade:2.0.0@getPassPurchaseItems(WishlistQueryRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.wishlist.api.facade.WishlistFacade:2.0.0@getPassPurchaseItems(Long,Integer)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.wishlist.api.facade.WishlistFacade:2.0.0@updateWishlistLastSeen(Long)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.wishlist.api.facade.WishlistFacade:2.0.0@findWishlistItems(Long)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.wishlist.api.facade.WishlistFacade:2.0.0@findWishlistItems(String)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.wishlist.api.facade.WishlistFacade:2.0.0@findWishlistItems(Long,QueryOptionDTO)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.wishlist.api.facade.WishlistFacade:2.0.0@findWishlistItems(Long,QueryExtendParamDTO)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.wishlist.api.facade.WishlistFacade:2.0.0@findBySkuId(Long,Long)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.wishlist.api.facade.WishlistFacade:2.0.0@findBySkuId(String,Long)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.wishlist.api.facade.WishlistFacade:2.0.0@findByItemId(Long,Long)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.wishlist.api.facade.WishlistFacade:2.0.0@findByItemId(String,Long)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.wishlist.api.facade.WishlistFacade:2.0.0@addItemToWishlist(WishlistItemDTO)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.wishlist.api.facade.WishlistFacade:2.0.0@addItemsToWishlist(List)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.wishlist.api.facade.WishlistFacade:2.0.0@addItemToWishlist2(WishlistItemDTO)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.wishlist.api.facade.WishlistFacade:2.0.0@deleteItem(String,Long)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.wishlist.api.facade.WishlistFacade:2.0.0@deleteItem(Long,Long)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.wishlist.api.facade.WishlistFacade:2.0.0@constructAggregate(WishlistUser,List)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.wishlist.api.facade.WishlistFacade:2.0.0@getWishlist(WishlistQueryRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.wishlist.api.facade.WishlistFacade:2.0.0@getWishlistStatisInfo(Long,Set)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.wishlist.api.facade.WishlistFacade:2.0.0@findWishlist(Long)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.wishlist.api.facade.WishlistFacade:2.0.0@deleteItemById(String,Long)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.wishlist.api.facade.WishlistFacade:2.0.0@deleteItemById(Long,Long)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.wishlist.api.facade.WishlistFacade:2.0.0@addUsrTagForItem(WishlistAddTagForItemRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.wishlist.api.facade.WishlistFacade:2.0.0@removeUsrTagForItem(WishlistRemoveTagForItemRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.trade.open.define.facade.OrderQueryFacade:1.0.0-AE@bizCode()\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.trade.open.define.facade.OrderQueryFacade:1.0.0-AE@query(OrderQueryRequest,ClientInfo)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderQueryForBuyerFacade:1.0.0@queryTradeOrderById(Long)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderQueryForBuyerFacade:1.0.0@queryBatchTradeOrdersByIds(QueryESTradeOrdersByIdRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderQueryForBuyerFacade:1.0.0@queryTradeOrderBySellerId(QueryOrderBySellerBuyerIdRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderQueryForBuyerFacade:1.0.0@queryTradeOrderLineById(Long)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderViewFacade:1.0.0@recentChangeOrder(RecentChangeOrderRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderViewFacade:1.0.0@adjustDeliveryInfo(AdjustDeliveryInfoRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderViewFacade:1.0.0@renderDeliveryInfo(QueryDeliveryInfoRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderViewFacade:1.0.0@renderTrackingMyOrderForm(RenderTrackingMyOrderRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderViewFacade:1.0.0@queryTradeResultReadyStatus(TradeResultReadyRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderViewFacade:1.0.0@adjustBuyerOrderList(AdjustTradeOrdersRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderViewFacade:1.0.0@renderBuyerOrderList(QueryBuyerOrderListRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderViewFacade:1.0.0@renderOrderDetail(QueryBuyerOrderDetailRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderViewFacade:1.0.0@adjustOrderDetail(AdjustBuyerOrderDetailRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderViewFacade:1.0.0@validateTrackingMyOrder(TrackingMyOrderRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderViewFacade:1.0.0@validateTrackingMyOrder(String,Long)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderViewFacade:1.0.0@recentOrders(Long)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderViewFacade:1.0.0@queryOrderDeliveryStatus(OrderDeliveryStatusQueryRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderViewFacade:1.0.0@getOrderDetailURL(PlatformType,Long)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.merchant.order.facade.OrderExportInitFacade:1.0.0@initExportPage(OrderExportInitRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.query.seller.repository.TradeOrderSearchRepo:1.0.0.DAILY@queryOrderLineIdsByOS(OrderListQueryRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.query.seller.repository.TradeOrderSearchRepo:1.0.0.DAILY@queryDistinctTradeOrderIdsByOS(OrderListQueryRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.merchant.order.facade.OrderExportQueryFacade:1.0.0@getShippingListExportPage(OrderExportQueryRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.merchant.order.facade.OrderOperatingFacade:1.0.0@extendsReceiveGoodsTime(ExtendsReceiveGoodsRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.merchant.order.facade.OrderOperatingFacade:1.0.0@changeOrderMemo(ChangeOrderMemoRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.merchant.order.facade.OrderOperatingFacade:1.0.0@changeOrderAmount(ChangeOrderAmountRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.merchant.order.facade.OrderOperatingFacade:1.0.0@confirmCancel(CancelOperateRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.merchant.toolbox.gateway.service.IMerchantGatewayService:1.0.0@checkPreload()\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.merchant.toolbox.gateway.service.IMerchantGatewayService:1.0.0@getVersion()\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.merchant.mtop.facade.SellerOrderQueryService:2.0.0@queryOrderBoard(OrderBoardRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.merchant.common.batch.api.facade.IBatchTaskFacade:1.0.0.999-not-exist@createTask(BatchTaskCreateRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.merchant.common.batch.api.service.BackGroundTaskService:1.0.0.999-not-exist@ximport(String,String,JSONObject)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.merchant.common.batch.api.service.BackGroundTaskService:1.0.0.999-not-exist@createTask(BatchTaskCreateRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.merchant.common.batch.api.service.BackGroundTaskService:1.0.0.999-not-exist@customizeImport(String,String,JSONObject)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.merchant.common.batch.api.service.BackGroundTaskService:1.0.0.999-not-exist@export(String,String,JSONObject)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.merchant.common.batch.api.service.BackGroundTaskService:1.0.0.999-not-exist@customizeExport(String,String,JSONObject)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.merchant.common.batch.api.facade.IBatchTaskFacade:1.0.0.batch-order-export-new@createTask(BatchTaskCreateRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.merchant.common.batch.api.service.BackGroundTaskService:1.0.0.batch-order-export-new@ximport(String,String,JSONObject)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.merchant.common.batch.api.service.BackGroundTaskService:1.0.0.batch-order-export-new@createTask(BatchTaskCreateRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.merchant.common.batch.api.service.BackGroundTaskService:1.0.0.batch-order-export-new@customizeImport(String,String,JSONObject)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.merchant.common.batch.api.service.BackGroundTaskService:1.0.0.batch-order-export-new@export(String,String,JSONObject)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.merchant.common.batch.api.service.BackGroundTaskService:1.0.0.batch-order-export-new@customizeExport(String,String,JSONObject)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.query.seller.servcie.OrderQueryScenarioService:1.0.0.DAILY@queryOrderList(OrderListQueryRequest,String)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.query.seller.servcie.OrderQueryScenarioService:1.0.0.DAILY@queryOrderDetail(OrderDetailQueryRequest,String)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.query.seller.servcie.OrderQueryScenarioService:1.0.0.DAILY@queryOrderStatistics(OrderCountQueryRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.query.seller.servcie.OrderQueryScenarioService:1.0.0.DAILY@queryOrderCount(OrderListQueryRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.query.seller.servcie.OrderQueryScenarioService:1.0.0.DAILY@queryOrderLineList(OrderListQueryRequest,String)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.carts.api.facade.CartMtopFacade:1.0.0@ultron(CartMtopUltronRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.carts.api.facade.CartMtopFacade:1.0.0@count(CartCountRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.carts.api.facade.CartMtopFacade:1.0.0@add(CartMtopAddRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderManagementRegionFacade:1.0.0.sirius@editPayPrice(EditPayPriceRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderManagementFacade:1.0.0@editPayPrice(EditPayPriceRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.carts.api.facade.CartMtopFacade:1.0.0.sirius@ultron(CartMtopUltronRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.carts.api.facade.CartFacade:1.0.0.sirius@add(CartAddRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.carts.api.facade.CartFacade:1.0.0.sirius@count(CartCountRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.carts.api.facade.CartFacade:1.0.0.sirius@delete(CartDeleteRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.carts.api.facade.CartFacade:1.0.0.sirius@query(CartQueryRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.carts.api.facade.CartFacade:1.0.0.sirius@update(CartUpdateRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.carts.api.facade.CartFacade:1.0.0.sirius@checkout(CartCheckOutRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.carts.message.callback.OrderCreatedCallBackToCart@dealMessage(byte[])\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.daraz.trade.adaptor.message.executor.oldpayment.DarazOldPaySuccessMsgExecutor@execute(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.daraz.trade.adaptor.message.executor.uop.DarazUopStatusUpdateMsgExecutor@execute(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.daraz.trade.adaptor.message.executor.uop.DarazUopShippedMsgExecutor@execute(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.taobao.trade.message.executor.timeout.TaobaoConfirmGoodsTimeoutReminderCallback@onTimeout(AlarmClockEvent)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.taobao.trade.message.executor.timeout.TaobaoPayTimeoutCallback@onTimeout(AlarmClockEvent)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.taobao.trade.message.executor.timeout.TaobaoPayTimeoutReminderCallback@onTimeout(AlarmClockEvent)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.taobao.trade.message.executor.timeout.TaobaoSendGoodsTimeoutCallback@onTimeout(AlarmClockEvent)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.taobao.trade.message.executor.fulfillment.TaobaoShippedMsgExecutor@execute(TaobaoInputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.taobao.trade.message.executor.fulfillment.TaobaoDeliveredMsgExecutor@execute(TaobaoInputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.taobao.trade.message.executor.fulfillment.TaobaoCreatePaymentOrderWhenShippedMsgExecutor@execute(TaobaoInputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.taobao.trade.message.executor.reverse.TaobaoReverseConfirmGoodsTimeoutMsgExecutor@execute(TaobaoInputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.daraz.trade.adaptor.message.executor.payment.DarazPayFailedMsgExecutor@execute(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.daraz.trade.adaptor.message.executor.payment.DarazPayIntentionMsgExecutor@execute(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.taobao.trade.message.executor.timeout.ConfirmGoodsTimeoutCallback@onTimeout(AlarmClockEvent)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.daraz.trade.adaptor.message.executor.payment.DarazPaySuccessMsgExecutor@execute(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.taobao.trade.message.executor.payment.TaobaoPaymentFailedMsgExecutor@execute(TaobaoInputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.taobao.trade.message.executor.payment.TaobaoPaymentIntentionExecutor@execute(TaobaoInputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.taobao.trade.message.executor.fulfillment.TaobaoOFCTagMsgExecutor@execute(TaobaoInputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.global.timeout.message.AlarmMessageListener@consume(List,ConsumeConcurrentlyContext)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.daraz.trade.adaptor.message.executor.timeout.DarazPayTimeoutReminderCallback@onTimeout(AlarmClockEvent)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.daraz.trade.adaptor.message.executor.timeout.DarazPayTimeoutCallback@onTimeout(AlarmClockEvent)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.daraz.trade.adaptor.message.executor.timeout.DarazOtcPayTimeoutReminderCallback@onTimeout(AlarmClockEvent)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.taobao.trade.message.executor.payment.TaobaoPaySuccessMsgExecutor@execute(TaobaoInputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.taobao.trade.message.executor.ov.TaobaoOVAsyncCloseOrderMsgExecutor@execute(TaobaoInputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.daraz.trade.adaptor.message.executor.uop.DarazUopDeliveredMsgExecutor@execute(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.daraz.trade.adaptor.message.executor.oldpayment.DarazOldPayIntentionMsgExecutor@execute(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.daraz.trade.adaptor.message.executor.oldpayment.DarazOldPayFailedMsgExecutor@execute(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.daraz.trade.adaptor.message.executor.ov.OrderRiskCheckMsgExecutor@execute(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.daraz.trade.adaptor.message.executor.ov.DarazOVAsyncCloseOrderMsgExecutor@execute(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.daraz.trade.adaptor.message.executor.uop.DarazUopStatusUpdateAssExecutor@execute(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.message.executor.pay.PaymentFailedMsgExecutor@execute(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.lazada.trade.adaptor.message.executor.oldpayment.LazadaOldPaySuccessMsgExecutor@execute(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.lazada.trade.adaptor.message.executor.oldpayment.LazadaOldPayIntentionMsgExecutor@execute(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.lazada.trade.adaptor.message.executor.oldpayment.LazadaOldPayFailedMsgExecutor@execute(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.lazada.trade.adaptor.message.executor.uop.LazadaUopStatusUpdateAssExecutor@execute(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.lazada.trade.adaptor.message.executor.uop.LazadaUopStatusUpdateMsgExecutor@execute(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.lazada.trade.adaptor.message.executor.uop.LazadaUopShippedMsgExecutor@execute(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.lazada.trade.adaptor.message.executor.uop.LazadaUopDeliveredMsgExecutor@execute(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.lazada.trade.adaptor.message.executor.uop.UpdateO2OFeaturesExecutor@execute(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.lazada.trade.adaptor.message.executor.ov.LazadaOVAsyncCloseOrderMsgExecutor@execute(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.lazada.trade.adaptor.message.executor.timeout.LazadaPayTimeoutReminderCallback@onTimeout(AlarmClockEvent)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.lazada.trade.adaptor.message.executor.timeout.LazadaPayTimeoutCallback@onTimeout(AlarmClockEvent)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.lazada.trade.adaptor.message.executor.timeout.LazadaOtcPayTimeoutReminderCallback@onTimeout(AlarmClockEvent)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.lazada.trade.adaptor.message.executor.trade.LazadaPureZeroPaySuccessMsgExecutor@execute(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.daraz.trade.adaptor.message.executor.uop.DarazCreatePaymentOrderWhenShippedMsgExecutor@execute(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.lazada.trade.adaptor.message.executor.payment.LazadaPayIntentionMsgExecutor@execute(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.lazada.trade.adaptor.message.executor.payment.LazadaPayFailedMsgExecutor@execute(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.lazada.trade.adaptor.message.executor.uop.LazadaCreatePaymentOrderWhenShippedMsgExecutor@execute(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.lazada.trade.adaptor.message.executor.oldpayment.presale.LazadaDepositOldPaySuccessMsgExecutorImpl@execute(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.lazada.trade.adaptor.message.executor.oldpayment.presale.LazadaDepositOldPayAcceptMsgExecutorImpl@execute(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.lazada.trade.adaptor.message.executor.oldpayment.presale.LazadaFinalOldPayAcceptMsgExecutorImpl@execute(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.lazada.trade.adaptor.message.executor.oldpayment.presale.LazadaFinalOldPaySuccessMsgExecutorImpl@execute(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.lazada.trade.adaptor.message.executor.groupbuy.LazadaGroupBuyPushOrderMsgExecutor@execute(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.lazada.trade.adaptor.message.executor.timeout.presale.LazadaFinalPayNotificationCallBack@onTimeout(AlarmClockEvent)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.mock.paymentFacadeV2.MockBatchCheckoutV2@response(Object[],Map)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.executor.reverse.AeReturnFinishedExecutor@filter(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.executor.reverse.AeReturnFinishedExecutor@execute(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.executor.uop.AeDeliveredMsgExecutor@filter(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.executor.uop.AeDeliveredMsgExecutor@execute(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.executor.uop.AeDeliveredMsgExecutor@buildInputMsgRequest(String,Object)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.executor.uop.AeDeliveredMsgExecutor@buildInputMsgRequest(String,FulfillmentOrderStatusUpdatedDTO)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.executor.timeout.AeCreateConfirmGoodsTimeoutMsgExecutor@filter(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.executor.timeout.AeCreateConfirmGoodsTimeoutMsgExecutor@execute(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.executor.timeout.AePayTimeoutCallback@onTimeout(AlarmClockEvent)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.executor.reverse.AeCreatedReturnExecutor@filter(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.executor.reverse.AeCreatedReturnExecutor@execute(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.spi.save.AeCheckoutPageEnrich@generateErrorInfo(CheckoutErrorInfoInput)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.spi.save.AeCheckoutPageEnrich@generateBusinessCheckoutPage(ICheckoutPage)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.spi.save.AeCheckoutPageEnrich@enrichCheckoutPage(CheckoutPageEnrichInput)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.handler.AeTradeMsgHandler@receiveMessage(Message)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.handler.AeUOPMsgHandler@receiveMessage(Message)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.executor.trade.AeCODCreateFulfillmentOrderExecutor@filter(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.executor.trade.AeCODCreateFulfillmentOrderExecutor@buildInputMsgRequest(String,TradeOrderMsgDTO)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.executor.trade.AeCODCreateFulfillmentOrderExecutor@buildInputMsgRequest(String,Object)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.executor.payment.AePaySuccessMsgExecutor@filter(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.executor.payment.AePaySuccessMsgExecutor@execute(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.executor.payment.AePaySuccessMsgExecutor@buildInputMsgRequest(String,Object)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.executor.timeout.AeCreateSendGoodsTimeoutMsgExecutor@filter(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.executor.timeout.AeCreateSendGoodsTimeoutMsgExecutor@execute(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.executor.timeout.AeSendGoodsTimeoutCallback@onTimeout(AlarmClockEvent)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.handler.AeReverseMsgHandler@receiveMessage(Message)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.handler.AePaymentMsgHandler@receiveMessage(Message)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.executor.uop.AeItemShippedMsgExecutor@filter(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.executor.uop.AeItemShippedMsgExecutor@execute(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.executor.timeout.AeConfirmGoodsTimeoutCallback@onTimeout(AlarmClockEvent)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.executor.timeout.AeConfirmGoodsTimeoutReminderCallback@onTimeout(AlarmClockEvent)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.mock.mockFulfillmentOrderCreateFacade.MockCreateFulfillmentOrder@response(Object[],Map)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.executor.trade.AePushCODOrderToConfirmedExecutor@filter(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.executor.trade.AePushCODOrderToConfirmedExecutor@execute(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.executor.timeout.AeCreatePayTimeoutMsgExecutor@filter(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.executor.timeout.AeCreatePayTimeoutMsgExecutor@execute(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.executor.uop.AeHBAMsgExecutor@filter(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.executor.uop.AeHBAMsgExecutor@execute(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.executor.trade.AeOpLogExecutor@filter(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.executor.trade.AeOpLogExecutor@execute(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.mock.inventoryReduceFacade.MockReduce@response(Object[],Map)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.executor.timeout.AePayTimeoutReminderCallback@onTimeout(AlarmClockEvent)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.mock.inventorySourcingFacade.MockSourcing@response(Object[],Map)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.handler.AeGroupBuyShareMsgHandler@receiveMessage(Message)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.handler.AeHBAHandler@receiveMessage(Message)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.handler.AeReverseEndMsgHandler@receiveMessage(Message)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.executor.reverse.AeCancelReturnExecutor@filter(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.executor.reverse.AeCancelReturnExecutor@execute(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.executor.notification.AePartialShippedNotificationExecutor@filter(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.executor.notification.AePartialShippedNotificationExecutor@execute(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.executor.payment.AeChargeBackFreezeExecutor@filter(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.executor.payment.AeChargeBackFreezeExecutor@execute(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.executor.notification.AeShippedNotificationExecutor@isTopUpOrder(TradeOrderLineDTO)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.executor.notification.AeShippedNotificationExecutor@filter(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.executor.notification.AeShippedNotificationExecutor@execute(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.executor.payment.AePayAmountCheckExecutor@filter(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.executor.payment.AePayAmountCheckExecutor@execute(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.executor.notification.AeModifyPriceNotificationExecutor@filter(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.executor.notification.AeModifyPriceNotificationExecutor@execute(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.executor.notification.AeExtendShipTimeoutNotificationExecutor@filter(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.executor.notification.AeExtendShipTimeoutNotificationExecutor@execute(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.executor.notification.AeExtendConfirmTimeoutNotificationExecutor@filter(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.executor.notification.AeExtendConfirmTimeoutNotificationExecutor@execute(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.executor.notification.AeOrderConfirmedNotificationExecutor@filter(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.executor.notification.AeOrderConfirmedNotificationExecutor@execute(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.executor.payment.AePaySuccessMsgExecutor@buildInputMsgRequest(String,CheckoutEvent)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.app.topup.utils.TopUpQueryFacade@queryRechargeLimit(Long,Long,Long,String)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.executor.adapt.AeCODOrderDeliveredAdaptor@filter(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.executor.adapt.AeCODOrderDeliveredAdaptor@execute(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.executor.payment.AePaymentBufferExecutor@filter(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.executor.payment.AePaymentBufferExecutor@execute(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.executor.payment.AePaymentBufferExecutor@buildInputMsgRequest(String,PaymentCacheEvent)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.executor.payment.AePaymentBufferExecutor@buildInputMsgRequest(String,Object)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.executor.payment.AeModifyPriceMsgExecutor@getModifyPriceInfo(Long)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.executor.payment.AeModifyPriceMsgExecutor@filter(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.executor.payment.AeModifyPriceMsgExecutor@execute(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.executor.payment.AeModifyPriceMsgExecutor@buildInputMsgRequest(String,Object)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.executor.payment.AeModifyPriceMsgExecutor@buildInputMsgRequest(String,ModifyPriceEvent)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.executor.interactive.AeGroupBuyShareExecutor@filter(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.executor.interactive.AeGroupBuyShareExecutor@execute(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.executor.notification.AePaySuccessNotificationExecutor@filter(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.executor.notification.AePaySuccessNotificationExecutor@execute(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.executor.notification.AeCODOrderCreatedNotificationExecutor@filter(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.executor.notification.AeCODOrderCreatedNotificationExecutor@execute(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.lazada.trade.adaptor.message.handler.settlement.SettlementMsgHandler@receiveMessageFromSettleCallback(*)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.lazada.trade.adaptor.message.executor.payment.presale.LazadaDepositPayAcceptMsgExecutorImpl@execute(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.lazada.trade.adaptor.message.executor.payment.presale.LazadaDepositPaySuccessMsgExecutorImpl@execute(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.lazada.trade.adaptor.message.executor.payment.presale.LazadaFinalPayAcceptMsgExecutorImpl@execute(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.lazada.trade.adaptor.message.executor.payment.presale.LazadaFinalPaySuccessMsgExecutorImpl@execute(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.lazada.trade.adaptor.message.executor.adaptor.promotion.PromotionDiscardOutputMsgExecutor@execute(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.lazada.trade.adaptor.message.executor.groupbuy.LazadaGroupBuyAfterReduceMsgExecutor@execute(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.lazada.trade.adaptor.message.executor.oldpayment.LazadaOldPayOtcCodeMsgExecutor@execute(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.lazada.trade.adaptor.message.executor.adaptor.presale.DepositTimeOutNotificationOutPutMsgExecutor@execute(*)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.lazada.trade.adaptor.message.executor.payment.LazadaPayFailedMsgExecutor@execute(*)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.lazada.trade.adaptor.message.executor.trade.LazadaDigitalShippedMsgExecutor@execute(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.facade.OrderManageFacadeImpl@createTimeout(CreateTimeoutRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.global.solution.trade.open.facade.OpenCheckoutFacadeImpl@renderOrder(RenderOrderRequest,ClientInfo,RenderOrderOption)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.global.solution.trade.open.facade.OpenCheckoutFacadeImpl@preCreateOrder(PreCreateOrderRequest,ClientInfo,PreCreateOption)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.global.solution.trade.open.facade.OpenCheckoutFacadeImpl@createOrder(CreateOrderRequest,ClientInfo,CreateOption)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.global.buy.api.facade.wireless.WirelessCheckoutFacadeImpl@renderOrder(RenderQueryParams)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.global.buy.api.facade.wireless.WirelessCheckoutFacadeImpl@queryAmendPackage(QueryAmendPackageParams)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.global.buy.api.facade.wireless.WirelessCheckoutFacadeImpl@oneKeyCreateOrder(CreateQueryParams)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.global.buy.api.facade.wireless.WirelessCheckoutFacadeImpl@createOrder(CreateQueryParams)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.global.buy.api.facade.wireless.WirelessCheckoutFacadeImpl@adjustRender(AdjustQueryParams)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.global.solution.trade.open.facade.order.OpenOrderQueryFacadeImpl@query(OrderQueryRequest,ClientInfo)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.facade.OrderQueryFacadeImpl@queryTradeOrderLineById(Long)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.facade.OrderQueryFacadeImpl@queryTradeOrderById(Long)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.taobao.order.query.seller.facade.impl.SellerOrderPrintWebFacadeFacadeImpl@validateCanPrint(BaseRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.taobao.order.query.seller.facade.impl.SellerOrderPrintWebFacadeFacadeImpl@printWayBill(SellerOrderListRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.taobao.order.query.seller.facade.impl.SellerOrderPrintWebFacadeFacadeImpl@printPickUpOrder(SellerOrderListRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.daraz.trade.adaptor.message.executor.trade.DarazCreateFulfillmentOrderMsgExecutor@execute(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.lazada.trade.adaptor.message.executor.trade.LazadaCreateFulfillmentOrderMsgExecutor@execute(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.daraz.trade.adaptor.message.executor.trade.DarazPureZeroPaySuccessMsgExecutor@execute(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.lazada.trade.adaptor.message.executor.payment.LazadaPaySuccessMsgExecutor@execute(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.facade.OrderManageFacadeImpl@updateOrderAfterPaid(TradeOrderUpdateRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.facade.OrderQueryForSellerFacadeImpl@regionalQueryBatchTradeOrderLines(QueryBatchTradeOrderLinesRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.facade.OrderQueryForSellerFacadeImpl@queryTradeOrderLines(QueryESOrderLinesRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.facade.OrderQueryForSellerFacadeImpl@queryTradeOrder(QueryESTradeOrderRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.facade.OrderQueryForSellerFacadeImpl@querySearchValue(QueryESSearchValueRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.facade.OrderQueryForSellerFacadeImpl@queryCountTradeOrder(QueryESTradeOrderCountRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.facade.OrderQueryForSellerFacadeImpl@queryBatchTradeOrdersByIds(QueryESTradeOrdersByIdRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.facade.OrderQueryForSellerFacadeImpl@queryBatchTradeOrders(QueryForSellerRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.facade.OrderQueryForSellerFacadeImpl@queryBatchTradeOrders(QueryESTradeOrdersRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.facade.OrderQueryForSellerFacadeImpl@queryBatchTradeOrders(QueryESTradeOrdersByShopOwnerIdRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.facade.OrderQueryForSellerFacadeImpl@queryBatchTradeOrderLines(QueryESTradeOrderLinesRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.facade.OrderQueryForSellerFacadeImpl@limitSubTradeOrderId(Long)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.facade.OrderQueryForSellerFacadeImpl@batchCountTradeOrderForSeller(SellerBatchCountRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderCallbackRegionFacade:1.0.0@*(*)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.ae.trade.message.executor.trade.AeCODCreateFulfillmentOrderExecutor@execute(InputTradeMsgRequest)\",\"invokeId\":0,\"invokeType\":\"JAVA\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderCallbackRegionFacade:1.0.0@canPayCheck(CanPayCheckRequest)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0},{\"identity\":\"com.alibaba.global.order.management.api.facade.OrderCallbackRegionFacade:1.0.0@asyncInventoryReduce(Long)\",\"invokeId\":0,\"invokeType\":\"ALI_HSF\",\"rate\":0}],\"isolateLevel\":\"strict\",\"logLevel\":\"debug\",\"middleUpPeriod\":0,\"mockThowable\":false,\"mockTimeAtBeta\":true,\"monitorOpen\":true,\"onlineSupportHotCode\":false,\"openAutoFilter\":false,\"openDevSystemStatusMonitor\":false,\"serverType\":4,\"source\":\"biz_platform\",\"subInvokeHandlerClass\":\"\",\"subInvokeNotFoundHandlerClass\":\"com.global.trade.brp.TradeCenterSubInvokeNotFoundHandler\",\"supportAsyncThreadSupport\":true,\"supportCollectAfterCaseCompareSuccessed\":false,\"supportHotCode\":true,\"supportMultiMainInvoke\":false,\"userSerializerClass\":\"com.global.trade.brp.CustomerizationSerializer\",\"webSocketMode\":false,\"zoneCode\":\"dev_test\"}", "invokeExludeSITimeConsuming": 0.0, "libVersion": "8.0.7"}, "features": {"envResourceId": "1387384690081054720", "bind-app-model": "default"}, "gmtCreate": 1619613261133, "host": "***********", "invokeFlag": 0, "invokeTree": null, "mainInvoke": {"afterExecuteParams": null, "endTime": 0, "exceptionStackTrace": null, "extInvokeIdentity": null, "extraPrams": null, "fuzzyInvokeIdentity": null, "generic": false, "identity": "com.alibaba.global.buy.api.facade.CheckoutMultiterminalFacade@renderOrder(RenderOrderRequest):1.0.0", "index": 0, "isSerialized": null, "method": "renderOrder", "params": [{"promotionDTO": {"promotionOrderLineDTOs": [], "spreadCode": null, "chosenPromotionIds": {}, "noUseCollectibleCouponSellers": null, "extraParams": {}, "clearSpreadCode": false}, "placeOrder": false, "commonDTO": {"extraParams": {}, "headerDTO": {"wua": "ARdT_VFcHFYafHHLicsAEoeQUUv9HchiD+ny4pvnH9w/Kxd/EdL59+5HZdZwdsUBfO1RRL/bJxPsnKtplGEV4nNRkRzV5YZ6xUCmy/zdZTe0ReoK8d/7PY8MQ3GOkz7lx32NcsbvlN2BEHkXniRnXZyB2VJN9iqH0F6fjgNcbSrz3WzvTIod9FTIOEbzSIORYwW9nGAHFkcvlOnZv2wEMvlDvu94TI5XhBFpBdCAs0bCtLQQgz95W9+e2YrieNHozG7x88iYqeVvFfRTHkht0aYvXs4782MGuWK5Am3p/xbjVPGl8YveoposVOJjjKEChzJzaTdMuZ6C/HnH1j0A6VmuNIsmAHkk2OZGoHTVZkjEHIjC3S6Lx8DEK2WRou7MYm05n", "umidTokenType": "SECURITY_TOKEN", "cookie": null, "utdid": null, "ip": "*************", "userAgent": null, "umidToken": "2ztLfkFLOljE8jV5FxCtVImGSy9U2Qme", "ua": "ARdT_VFcHFYafHHLicsAEoeQUUv9HchiD+ny4pvnH9w/Kxd/EdL59+5HZdZwdsUBfO1RRL/bJxPsnKtplGEV4nNRkRzV5YZ6xUCmy/zdZTe0ReoK8d/7PY8MQ3GOkz7lx32NcsbvlN2BEHkXniRnXZyB2VJN9iqH0F6fjgNcbSrz3WzvTIod9FTIOEbzSIORYwW9nGAHFkcvlOnZv2wEMvlDvu94TI5XhBFpBdCAs0bCtLQQgz95W9+e2YrieNHozG7x88iYqeVvFfRTHkht0aYvXs4782MGuWK5Am3p/xbjVPGl8YveoposVOJjjKEChzJzaTdMuZ6C/HnH1j0A6VmuNIsmAHkk2OZGoHTVZkjEHIjC3S6Lx8DEK2WRou7MYm05n", "sid": null, "adid": null, "alipayUmidToken": "", "osVersion": null, "guest": false, "entrance": null, "cid": "YIeGkaCk/4EDAHxDYnmBQkxK"}, "source": {"platformType": {"name": "WIRELESS"}, "mobile": {"ultronVersion": "", "appVersion": "255", "apiName": "", "wua": "ARdT_VFcHFYafHHLicsAEoeQUUv9HchiD+ny4pvnH9w/Kxd/EdL59+5HZdZwdsUBfO1RRL/bJxPsnKtplGEV4nNRkRzV5YZ6xUCmy/zdZTe0ReoK8d/7PY8MQ3GOkz7lx32NcsbvlN2BEHkXniRnXZyB2VJN9iqH0F6fjgNcbSrz3WzvTIod9FTIOEbzSIORYwW9nGAHFkcvlOnZv2wEMvlDvu94TI5XhBFpBdCAs0bCtLQQgz95W9+e2YrieNHozG7x88iYqeVvFfRTHkht0aYvXs4782MGuWK5Am3p/xbjVPGl8YveoposVOJjjKEChzJzaTdMuZ6C/HnH1j0A6VmuNIsmAHkk2OZGoHTVZkjEHIjC3S6Lx8DEK2WRou7MYm05n", "appName": "", "ua": null, "ttid": "201200@Aliexpress_iphone_8.16.0", "deviceId": "x86_64", "sid": "", "apiVersion": null, "feature": null, "sysEntrance": null, "appType": "", "serverEnvRaw": "", "appKey": "21371581", "miniWua": "ARdT_VFcHFYafHHLicsAEoeQUUv9HchiD+ny4pvnH9w/Kxd/EdL59+5HZdZwdsUBfO1RRL/bJxPsnKtplGEV4nNRkRzV5YZ6xUCmy/zdZTe0ReoK8d/7PY8MQ3GOkz7lx32NcsbvlN2BEHkXniRnXZyB2VJN9iqH0F6fjgNcbSrz3WzvTIod9FTIOEbzSIORYwW9nGAHFkcvlOnZv2wEMvlDvu94TI5XhBFpBdCAs0bCtLQQgz95W9+e2YrieNHozG7x88iYqeVvFfRTHkht0aYvXs4782MGuWK5Am3p/xbjVPGl8YveoposVOJjjKEChzJzaTdMuZ6C/HnH1j0A6VmuNIsmAHkk2OZGoHTVZkjEHIjC3S6Lx8DEK2WRou7MYm05n"}, "orderFrom": {"name": "BUY_NOW"}, "renderType": {"name": "PLACE_ORDER"}}, "buyerId": **********}, "addressDTO": {"deliveryAddressDTO": null, "billingAddressDTO": null, "deliveryAddressType": {"name": "ShippingAddress"}, "selectedAddressesOfSignatures": null, "relatedAddressId": **********}, "inventoryOrderDTO": null, "extraParams": {"buyerCountry": "US", "saasRegion": null, "intentional_currency": "USD", "isVirtualProduct": false, "locale": {"country": "en", "variant": "", "language": "en"}, "shippingMethodType": "residential"}, "traceDTO": null, "priceDTO": null, "async": false, "taxDTO": null, "renderProtocol": {"name": "NATIVE"}, "uniqueToken": null, "orders": null, "paymentDTO": {"channelParams": null, "riskInfo": null, "paymentCostFee": null, "paymentMethod": null, "cashierDTO": {"cardBrandCurrency": null, "cardBinCountry": null, "cashierOption": null, "cardBin": null, "extraMap": {"paymentProtocolVersion": null, "payChannelEchoExtAttribute": null, "frontComponentDataJsonStr": null}, "subCashierOption": null, "planId": null, "cardBrand": null}, "extraParams": null, "paymentChannel": null}, "items": [{"gift": false, "quantity": 1, "signature": null, "cartId": 81007272905415, "memo": null, "main": false, "extraParams": {"carAdditionalInfo": "{}"}, "sample": false, "promotionId": null, "itemId": ****************, "bundleDTO": null, "instanceIds": null, "deliveryOrderLineDTO": {"transportMethodDTO": {"supportCod": false, "savedShippingFee": null, "featuresForTrade": null, "deliveryCutTime": null, "deliveryProviderName": null, "liveUp": false, "deliveryOptionLineCode": null, "incentiveFee": null, "guaranteeTag": null, "deliveryWorkTimeMax": null, "shippingFee": null, "shippingFeeDiscount": null, "selectedSlot": null, "capKey": null, "guaranteeCutoffTime": null, "shippingSurchargeFee": null, "solutionCodeForCaiNiao": null, "deliveryOption": "CAINIAO_STANDARD", "deliveryWorkTimeMin": null, "deliveryProviderCode": null, "originDeliveryOption": null, "degrade": false}, "availableTransportMethodDTO": null}, "dynamicGroupDTO": null, "skuAttr": "14:193;*********:*********;*********:*********", "skuId": "*****************", "channelCode": null, "status": {"name": "VALID"}}]}], "paramsInstType": null, "paramsType": ["com.alibaba.global.buy.api.request.RenderOrderRequest"], "relatedCollectIndex": 0, "response": {"success": true, "module": {"addresses": [{"spareDetailAddress": "test111", "encryptPassportNo": "", "city": "test", "locationTreeAddressId": "", "supportCollectionPoint": false, "countryId": "US", "rutNo": null, "addressId": "**********", "thirdLevelAddressName": null, "feature": {}, "countryCode": "US", "mobilePrefixCode": "+1", "fullPhoneNumber": null, "expressCode": null, "cpf": null, "state": "Other", "selected": false, "stationId": null, "passportNo": null, "encryptCpf": "", "addressType": "NORMAL", "contactName": "ch test", "mobile": "*********", "fullName": null, "addressFieldValidates": null, "addressSubType": {"name": "DOMESTIC_ADDRESS"}, "collectionPointTag": null, "cityLanguage": null, "fourthLevelAddressName": null, "collectionPoint": false, "phonePrefixCode": "+1", "phone": null, "passportNoDate": null, "regionId": null, "taxId": null, "fullAddress": null, "phoneArea": null, "detailAddress": "ch test", "postCode": "12345", "countryName": "United States", "relatedAddressId": null, "mobileNoVerified": false, "passportOrganization": null, "defaultAddress": false, "stateLanguage": null}, {"spareDetailAddress": "<PERSON><PERSON>", "encryptPassportNo": "", "city": "Chieti", "locationTreeAddressId": "910100010000000000-910100010001000000", "supportCollectionPoint": false, "countryId": "IT", "rutNo": null, "addressId": "**********", "thirdLevelAddressName": null, "feature": {}, "countryCode": "IT", "mobilePrefixCode": "+39", "fullPhoneNumber": null, "expressCode": null, "cpf": null, "state": "Abruzzo", "selected": true, "stationId": null, "passportNo": null, "encryptCpf": "", "addressType": "NORMAL", "contactName": "<PERSON><PERSON> sada", "mobile": "*********", "fullName": null, "addressFieldValidates": null, "addressSubType": {"$ref": "$.body.onlineContext.mainInvoke.response.module.addresses[0].addressSubType"}, "collectionPointTag": null, "cityLanguage": null, "fourthLevelAddressName": null, "collectionPoint": false, "phonePrefixCode": "+39", "phone": null, "passportNoDate": null, "regionId": null, "taxId": null, "fullAddress": null, "phoneArea": null, "detailAddress": "<PERSON><PERSON>", "postCode": "12354", "countryName": "Italy", "relatedAddressId": null, "mobileNoVerified": false, "passportOrganization": null, "defaultAddress": false, "stateLanguage": null}], "promotions": [{"shopPromotionGroup": null, "gatherOrderDeliveryOptions": null, "typeSubCode": null, "discountFee": {"cent": 2508, "currencyCode": "USD"}, "gatherOrderText": null, "gatherOrderFreeShippingPercent": null, "description": null, "gapType": null, "threshold": null, "phaseType": {"name": "TRADE"}, "activityId": "1", "gatherOrder": null, "typeDisplay": false, "gatherOrderVoucherTag": null, "levelType": {"name": "PRODUCT"}, "price": null, "sponsors": [], "gatherOrderLiveUpGapValue": null, "outId": "992d1d4ca035593a870320d8d97abf0e", "id": "52", "prices": null, "selected": true, "channelCode": null, "gapDiscountType": null, "creator": {"creatorType": {"name": "PLATFORM"}, "id": 1}, "sponsorType": {"name": "PLATFORM"}, "gapValue": null, "quantityDiscounts": null, "gatherOrderTags": null, "targetType": {"name": "TRADE_ITEM"}, "typeCode": "mockCoveredShippingSalePrice", "gatherOrderLiveUpSaveValue": null, "spreadCode": null, "gapDiscountValue": null, "extensions": {"promotionOrderLine2NumMap": "{\"992d1d4ca035593a870320d8d97abf0e\":1}", "promotionSubScopeMap": "{\"orderLineId2SubDetailIdList\":{\"992d1d4ca035593a870320d8d97abf0e\":[\"originalPrice_992d1d4ca035593a870320d8d97abf0e\",\"tax_origin_prex_992d1d4ca035593a870320d8d97abf0e\"]}}", "isPromotionUniqueIdDowngraded": "true", "calcM": "1", "originPromotionId": "1", "umpMockTargetShareMoneyMap": "{\"tax_origin_prex_992d1d4ca035593a870320d8d97abf0e\":\"{\\\"currency\\\":\\\"USD\\\",\\\"price\\\":400}\",\"originalPrice_992d1d4ca035593a870320d8d97abf0e\":\"{\\\"currency\\\":\\\"USD\\\",\\\"price\\\":2108}\"}"}, "rebatePercentage": null, "displayVoucher": null, "endTime": 1619616861948, "channelSellOutPolicy": null, "shopKey": [], "voucherInvalidCondition": {"itemNotMatch": false, "isUserLimitTimes": false, "discountFee": null, "excludedByOtherPromotion": false, "maxDiscountFee": null, "paymentNotMatch": false, "categoryNotMatch": false, "mutexByOtherPromotion": false, "dataInvalid": false, "forLiveUp": false, "sellerNotMatch": false, "rightMoney": null, "isVoucherLimitTimes": false, "discountCent": 0, "userNotMatch": false, "creditCardNotMatch": false, "totalFeeNotMatch": false, "dataExpired": false, "rightValue": null, "minOrderFee": null, "brandNotMatch": false, "deviceNotMatch": false}, "status": {"name": "VALID"}}], "orders": [{"seller": {"address": {"spareDetailAddress": null, "city": null, "locationTreeAddressId": null, "addressSubType": null, "countryId": null, "collectionPointTag": null, "addressId": null, "cityLanguage": null, "thirdLevelAddressName": null, "fourthLevelAddressName": null, "regionId": null, "countryCode": null, "fullAddress": null, "detailAddress": null, "postCode": null, "countryName": null, "state": null, "selected": false, "defaultAddress": false, "stateLanguage": null, "stationId": null}, "sellerStatus": {"name": "NORMAL"}, "sellerName": "Shop5423335 Store", "flagship": false, "paymentMethodBlackList": [], "tags": null, "nick": null, "sellerId": *********, "phonePrefixCode": null, "phone": "", "vatRegistered": true, "sellerType": {"name": "<PERSON>_<PERSON>"}, "email": "<EMAIL>", "shortCode": null}, "promotions": [{"$ref": "$.body.onlineContext.mainInvoke.response.module.promotions[0]"}], "shop": {"owner": {"$ref": "$.body.onlineContext.mainInvoke.response.module.orders[0].seller"}, "shopName": "Shop5423335 Store", "shopLogoUrl": "https://ae01.alicdn.com/kf/HTB1Ih7oboLrK1Rjy1zb763enFXar.png", "shopId": 5423335, "shopUrl": null}, "orderLines": [{"seller": {"address": {"spareDetailAddress": null, "city": null, "locationTreeAddressId": null, "addressSubType": null, "countryId": null, "collectionPointTag": null, "addressId": null, "cityLanguage": null, "thirdLevelAddressName": null, "fourthLevelAddressName": null, "regionId": null, "countryCode": null, "fullAddress": null, "detailAddress": null, "postCode": null, "countryName": null, "state": null, "selected": false, "defaultAddress": false, "stateLanguage": null, "stationId": null}, "sellerStatus": {"$ref": "$.body.onlineContext.mainInvoke.response.module.orders[0].seller.sellerStatus"}, "sellerName": "Shop5423335 Store", "flagship": false, "paymentMethodBlackList": [], "tags": null, "nick": null, "sellerId": *********, "phonePrefixCode": null, "phone": "", "vatRegistered": true, "sellerType": {"$ref": "$.body.onlineContext.mainInvoke.response.module.orders[0].seller.sellerType"}, "email": "<EMAIL>", "shortCode": null}, "parent": {"$ref": "$.body.onlineContext.mainInvoke.response.module.orders[0]"}, "product": {"seller": null, "unitPrice": {"number": 670.89, "monetaryContext": {"data": {"amountType": {"name": "org.javamoney.moneta.Money"}, "precision": 256, "java.lang.Class": {"$ref": "$.body.onlineContext.mainInvoke.response.module.orders[0].orderLines[0].product.unitPrice.monetaryContext.data.amountType"}, "java.math.RoundingMode": "HALF_EVEN"}}, "currency": {"context": {"data": {"provider": "java.util.Currency"}}, "baseCurrency": {"currencyCode": "USD"}}}, "productId": ****************, "extraMap": {"carAdditionalInfo": "{}", "p6": "{\"p6c0\":\"{\\\"price\\\":\\\"111.00\\\",\\\"currency\\\":\\\"USD\\\"}\",\"p6c4\":\"2\",\"p6c1\":\"{\\\"price\\\":\\\"107.00\\\",\\\"currency\\\":\\\"USD\\\"}\",\"p6c5\":\"0.2\"}", "fullfillmentMode": "other"}, "salePrice": {"cent": 67089, "currencyCode": "USD"}, "cartId": 81007272905415, "lotNum": 1, "saleProperties": [{"unit": null, "valueText": "Black", "valueId": 193, "imageUrl": null, "end": null, "propertyText": "Color", "valueAliasText": null, "propertyId": 14}, {"unit": null, "valueText": "China", "valueId": *********, "imageUrl": null, "end": null, "propertyText": "Ships From", "valueAliasText": null, "propertyId": *********}, {"unit": null, "valueText": "Bundle 1", "valueId": *********, "imageUrl": null, "end": null, "propertyText": "Bundle", "valueAliasText": null, "propertyId": *********}], "title": "testNoDPRegion", "url": "https://www.aliexpress.com/item/****************.html?mp=1", "tags": [82052], "itemId": ****************, "picUrl": "https://ae04.alicdn.com/kf/H251f297e9e13462fa52ca2ff1ae5ad3fB.jpg", "unit": *********, "l2lCurrencyCode": "USD", "descProperties": [], "productServices": [{"supplier": "allianz", "warrantyType": "basicWarranty"}], "category": {"path": [509, 5090301], "categoryName": "Mobile Phones", "categoryId": 5090301}, "skuAttr": "14:193;*********:*********;*********:*********", "brand": {"name": "", "id": 200658765}, "retailPrice": {"cent": 69597, "currencyCode": "USD"}, "skuId": "*****************"}, "shop": {"owner": {"$ref": "$.body.onlineContext.mainInvoke.response.module.orders[0].orderLines[0].seller"}, "shopName": "Shop5423335 Store", "shopLogoUrl": "https://ae01.alicdn.com/kf/HTB1Ih7oboLrK1Rjy1zb763enFXar.png", "shopId": 5423335, "shopUrl": null}, "bizCode": "ali.global.ae.trade.general", "checkLimitDetails": [{"reason": null, "buyAbleQuantity": 1, "originalQuantity": 1, "step": 1, "minBuyQuantity": null, "limitType": "STOCK"}], "memo": null, "summaryPrice": {"shippingDiscountFee": {"cent": 0, "currencyCode": "USD"}, "originalFee": {"cent": 69597, "currencyCode": "USD"}, "shippingSavedFee": null, "taxActualFee": null, "taxRebateFee": null, "shopTotalFee": {"cent": 68163, "currencyCode": "USD"}, "unitFee": {"cent": 67089, "currencyCode": "USD"}, "taxBaseFee": null, "payableFee": {"cent": 68163, "currencyCode": "USD"}, "buyAmount": {"originAmount": 0, "actualAmount": 1}, "totalFee": {"cent": 67089, "currencyCode": "USD"}, "discountFeeDetails": [], "baseShippingFee": {"cent": 1074, "currencyCode": "USD"}, "saleDiscountFee": {"cent": 0, "currencyCode": "USD"}, "shippingActualFee": {"cent": 1074, "currencyCode": "USD"}, "actualFee": {"cent": 68163, "currencyCode": "USD"}, "adjustFee": null}, "errorCode": null, "inventory": {"inventoryType": 200, "maxAvailableStock": 1, "currentAvailableStock": 1, "checkLimitDetails": null, "id": null, "channelCode": null}, "afterSales": {"period": null, "freeDays": null, "policyText": null, "periodText": null, "typeText": null, "id": null, "type": null, "policy": null}, "transportMethods": [{"shippingDiscountFee": {"cent": 0, "currencyCode": "USD"}, "maxTime": 3024000, "savedShippingFee": {"cent": 0, "currencyCode": "USD"}, "shippingCode": "CAINIAO_STANDARD", "transportMethodId": "14617", "shippingName": "AliExpress Standard Shipping", "estimateDeliveryDate": "Jun 01", "tracked": true, "deliveryAgingTextI18nMap": {"deliveryDateHelpStr": "If you finish the payment today, your order will arrive within the estimated delivery time.", "deliveryTextType": "deliveryDate", "estimateDeliveryDate": "Jun 01", "deliveryDateStr": "Estimated delivery on <font color='#000000'>Jun 01</font>", "freightAmountStr": "Shipping: {0}", "shippingToStr": "To <font color='#000000'>Italy</font> via AliExpress Standard Shipping"}, "additionalShippingFee": {"cent": 0, "currencyCode": "USD"}, "deliveryAgingTextType": "deliveryDate", "minTime": 3024000, "actualShippingFee": {"cent": 1074, "currencyCode": "USD"}, "baseShippingFee": {"cent": 1074, "currencyCode": "USD"}, "deliveryOption": "CAINIAO_STANDARD", "selected": true, "originDeliveryOption": null}, {"shippingDiscountFee": {"cent": 0, "currencyCode": "USD"}, "maxTime": 2851200, "savedShippingFee": {"cent": 0, "currencyCode": "USD"}, "shippingCode": "EMS", "transportMethodId": "14618", "shippingName": "EMS", "estimateDeliveryDate": "14-33", "tracked": true, "deliveryAgingTextI18nMap": {"deliveryTextType": "deliveryTime", "estimateDeliveryDate": "14-33", "freightAmountStr": "Shipping: {0}", "shippingToStr": "To <font color='#000000'>Italy</font> via EMS"}, "additionalShippingFee": {"cent": 0, "currencyCode": "USD"}, "deliveryAgingTextType": "deliveryTime", "minTime": 1209600, "actualShippingFee": {"cent": 9138, "currencyCode": "USD"}, "baseShippingFee": {"cent": 9138, "currencyCode": "USD"}, "deliveryOption": "EMS", "selected": false, "originDeliveryOption": null}], "features": {}, "promotions": [{"shopPromotionGroup": null, "gatherOrderDeliveryOptions": null, "typeSubCode": null, "discountFee": {"cent": 2508, "currencyCode": "USD"}, "gatherOrderText": null, "gatherOrderFreeShippingPercent": null, "description": null, "gapType": null, "threshold": null, "phaseType": {"$ref": "$.body.onlineContext.mainInvoke.response.module.promotions[0].phaseType"}, "activityId": "1", "gatherOrder": null, "typeDisplay": false, "gatherOrderVoucherTag": null, "levelType": {"$ref": "$.body.onlineContext.mainInvoke.response.module.promotions[0].levelType"}, "price": null, "sponsors": [], "gatherOrderLiveUpGapValue": null, "outId": "992d1d4ca035593a870320d8d97abf0e", "id": "52", "prices": null, "selected": true, "channelCode": null, "gapDiscountType": null, "creator": {"creatorType": {"$ref": "$.body.onlineContext.mainInvoke.response.module.promotions[0].creator.creatorType"}, "id": 1}, "sponsorType": {"$ref": "$.body.onlineContext.mainInvoke.response.module.promotions[0].sponsorType"}, "gapValue": null, "quantityDiscounts": null, "gatherOrderTags": null, "targetType": {"$ref": "$.body.onlineContext.mainInvoke.response.module.promotions[0].targetType"}, "typeCode": "mockCoveredShippingSalePrice", "gatherOrderLiveUpSaveValue": null, "spreadCode": null, "gapDiscountValue": null, "extensions": {"$ref": "$.body.onlineContext.mainInvoke.response.module.promotions[0].extensions"}, "rebatePercentage": null, "displayVoucher": null, "endTime": 1619616861948, "channelSellOutPolicy": null, "shopKey": [], "voucherInvalidCondition": {"$ref": "$.body.onlineContext.mainInvoke.response.module.promotions[0].voucherInvalidCondition"}, "status": {"$ref": "$.body.onlineContext.mainInvoke.response.module.promotions[0].status"}}], "buyAmount": {"originAmount": 0, "actualAmount": 1}, "invalidReason": null, "status": {"name": "VALID"}}], "receiveAddress": {"spareDetailAddress": "<PERSON><PERSON>", "encryptPassportNo": "", "city": "Chieti", "locationTreeAddressId": "910100010000000000-910100010001000000", "supportCollectionPoint": false, "countryId": "IT", "rutNo": null, "addressId": "**********", "thirdLevelAddressName": null, "feature": {}, "countryCode": "IT", "mobilePrefixCode": "+39", "fullPhoneNumber": null, "expressCode": null, "cpf": null, "state": "Abruzzo", "selected": false, "stationId": null, "passportNo": null, "encryptCpf": "", "addressType": "NORMAL", "contactName": "<PERSON><PERSON> sada", "mobile": "*********", "fullName": null, "addressFieldValidates": null, "addressSubType": {"$ref": "$.body.onlineContext.mainInvoke.response.module.addresses[0].addressSubType"}, "collectionPointTag": null, "cityLanguage": null, "fourthLevelAddressName": null, "collectionPoint": false, "phonePrefixCode": "+39", "phone": null, "passportNoDate": null, "regionId": null, "taxId": null, "fullAddress": null, "phoneArea": null, "detailAddress": "<PERSON><PERSON>", "postCode": "12354", "countryName": "Italy", "relatedAddressId": null, "mobileNoVerified": false, "passportOrganization": null, "defaultAddress": false, "stateLanguage": null}, "orderStatus": null, "summaryPrice": {"shippingDiscountFee": {"cent": 0, "currencyCode": "USD"}, "originalFee": {"cent": 0, "currencyCode": "USD"}, "shippingSavedFee": {"cent": 0, "currencyCode": "USD"}, "taxActualFee": {"cent": 0, "currencyCode": "USD"}, "taxRebateFee": {"cent": 0, "currencyCode": "USD"}, "shopTotalFee": {"cent": 68163, "currencyCode": "USD"}, "unitFee": {"cent": 0, "currencyCode": "USD"}, "taxBaseFee": {"cent": 0, "currencyCode": "USD"}, "payableFee": {"cent": 0, "currencyCode": "USD"}, "buyAmount": null, "totalFee": {"cent": 67089, "currencyCode": "USD"}, "discountFeeDetails": null, "baseShippingFee": {"cent": 1074, "currencyCode": "USD"}, "saleDiscountFee": {"cent": 0, "currencyCode": "USD"}, "shippingActualFee": {"cent": 1074, "currencyCode": "USD"}, "actualFee": {"cent": 68163, "currencyCode": "USD"}, "adjustFee": {"cent": 0, "currencyCode": "USD"}}, "orderStages": null, "orderSignature": "4433d4c6651e23cea9f67c92f2de6bf9"}], "payment": {"component": "{\"checkoutComponent_**********\":{\"fields\":{\"billingAddressDTO\":{\"address\":\"Asda\",\"address2\":\"Asdas\",\"city\":\"Chieti\",\"country\":\"IT\",\"creditCardNeedBillingAddress\":false,\"province\":\"Abruzzo\",\"zipCode\":\"12354\"},\"checkoutChannelList\":[{\"bindCardAllowed\":true,\"boundCreditCardList\":[],\"canSelectByDefault\":false,\"cardBinBlackList\":[\"506722\",\"627780\",\"439267\",\"404025\",\"506776\",\"482481\",\"471233\",\"486323\",\"400199\",\"506760\",\"523791\",\"400162\",\"472667\",\"451412\",\"506761\",\"510372\",\"459115\"],\"extAttributes\":{\"saveCard\":\"false\",\"cardHolderNameRule\":{\"BR\":[{\"msg\":\"Please enter a card holder name (within 50 characters).\",\"regex\":\"^.{1,50}$\",\"tag\":\"RegexItem\"},{\"msg\":\"Please enter a card holder name (within 50 English characters).\",\"regex\":\"^[a-zA-Z ]+$\",\"tag\":\"RegexItem\"},{\"msg\":\"Use a space to separate first and last name.\",\"regex\":\"^.+[ ]+.+$\",\"tag\":\"RegexItem\"}],\"RU\":[{\"msg\":\"Please enter a card holder name (within 40 characters).\",\"regex\":\"^.{1,40}$\",\"tag\":\"RegexItem\"},{\"msg\":\"Use a space to separate first and last name.\",\"regex\":\"^.+[ ]+.+$\",\"tag\":\"RegexItem\"}],\"OTHERS\":[{\"msg\":\"Please enter a card holder name (within 50 characters).\",\"regex\":\"^.{1,50}$\",\"tag\":\"RegexItem\"},{\"msg\":\"Use a space to separate first and last name.\",\"regex\":\"^.+[ ]+.+$\",\"tag\":\"RegexItem\"}],\"DEFAULT\":[{\"msg\":\"Please enter a card holder name (within 40 characters).\",\"regex\":\"^.{1,40}$\",\"tag\":\"RegexItem\"},{\"msg\":\"Use a space to separate first and last name.\",\"regex\":\"^.+[ ]+.+$\",\"tag\":\"RegexItem\"}]},\"safeMind\":{\"async\":false,\"iconUrl\":\"https://img.alicdn.com/tfs/TB1simweRKw3KVjSZFOXXarDVXa-53-63.png\",\"id\":\"XxX11048\",\"identity\":\"safeMind_XxX11048\",\"imageUrl\":\"https://img.alicdn.com/tfs/TB1.S1TaMgP7K4jSZFqXXamhVXa-544-98.png\",\"input\":false,\"parentId\":\"XxX11031\",\"submit\":false,\"tag\":\"safeMind\",\"text\":\"100% secure payment with\",\"type\":\"biz\",\"validator\":{\"notEmpty\":false}}},\"needChangeCurrency\":false,\"needCpfInput\":false,\"pmtOpt\":\"MIXEDCARD\",\"screen\":0,\"subPaymentMethodList\":[{\"currencyList\":[\"CHF\",\"EUR\",\"CLP\",\"USD\",\"CAD\",\"INR\",\"AUD\",\"SGD\",\"KRW\",\"JPY\",\"PLN\",\"GBP\",\"CZK\",\"HUF\",\"SEK\",\"NZD\",\"TRY\",\"RUB\"],\"extAttributes\":{\"subPaymentIcon\":\"https://img.alicdn.com/tfs/TB1xcMWdEKF3KVjSZFEXXXExFXa-68-48.png\",\"id\":100000},\"payChannelEchoExtAttribute\":\"{\\\"paymentChannelSupportCurrencies\\\":[\\\"CHF\\\",\\\"EUR\\\",\\\"CLP\\\",\\\"USD\\\",\\\"CAD\\\",\\\"INR\\\",\\\"AUD\\\",\\\"SGD\\\",\\\"KRW\\\",\\\"JPY\\\",\\\"PLN\\\",\\\"GBP\\\",\\\"CZK\\\",\\\"HUF\\\",\\\"SEK\\\",\\\"NZD\\\",\\\"TRY\\\",\\\"RUB\\\"]}\",\"paymentGateway\":\"aepay\",\"paymentMethodName\":\"VISA\"},{\"currencyList\":[\"CHF\",\"EUR\",\"CLP\",\"USD\",\"CAD\",\"INR\",\"AUD\",\"SGD\",\"KRW\",\"JPY\",\"PLN\",\"GBP\",\"CZK\",\"HUF\",\"SEK\",\"NZD\",\"TRY\",\"RUB\"],\"extAttributes\":{\"subPaymentIcon\":\"https://img.alicdn.com/tfs/TB19TEYdB1D3KVjSZFyXXbuFpXa-53-48.png\",\"id\":100000},\"payChannelEchoExtAttribute\":\"{\\\"paymentChannelSupportCurrencies\\\":[\\\"CHF\\\",\\\"EUR\\\",\\\"CLP\\\",\\\"USD\\\",\\\"CAD\\\",\\\"INR\\\",\\\"AUD\\\",\\\"SGD\\\",\\\"KRW\\\",\\\"JPY\\\",\\\"PLN\\\",\\\"GBP\\\",\\\"CZK\\\",\\\"HUF\\\",\\\"SEK\\\",\\\"NZD\\\",\\\"TRY\\\",\\\"RUB\\\"]}\",\"paymentGateway\":\"aepay\",\"paymentMethodName\":\"MASTERCARD\"},{\"currencyList\":[\"RUB\"],\"extAttributes\":{\"subPaymentIcon\":\"https://img.alicdn.com/tfs/TB10xg6dq1s3KVjSZFAXXX_ZXXa-77-48.png\",\"id\":100000},\"payChannelEchoExtAttribute\":\"{\\\"paymentChannelSupportCurrencies\\\":[\\\"RUB\\\"]}\",\"paymentGateway\":\"aepay\",\"paymentMethodName\":\"MIR\"},{\"currencyList\":[\"CHF\",\"EUR\",\"CLP\",\"USD\",\"CAD\",\"AUD\",\"KRW\",\"SGD\",\"JPY\",\"PLN\",\"GBP\",\"CZK\",\"HUF\",\"SEK\",\"NZD\",\"RUB\"],\"extAttributes\":{\"subPaymentIcon\":\"https://img.alicdn.com/tfs/TB1Zv3WdEWF3KVjSZPhXXXclXXa-53-48.png\",\"id\":100000},\"payChannelEchoExtAttribute\":\"{\\\"paymentChannelSupportCurrencies\\\":[\\\"CHF\\\",\\\"EUR\\\",\\\"CLP\\\",\\\"USD\\\",\\\"CAD\\\",\\\"AUD\\\",\\\"KRW\\\",\\\"SGD\\\",\\\"JPY\\\",\\\"PLN\\\",\\\"GBP\\\",\\\"CZK\\\",\\\"HUF\\\",\\\"SEK\\\",\\\"NZD\\\",\\\"RUB\\\"]}\",\"paymentGateway\":\"aepay\",\"paymentMethodName\":\"MAESTRO\"},{\"currencyList\":[\"AUD\",\"EUR\",\"GBP\",\"USD\"],\"extAttributes\":{\"subPaymentIcon\":\"https://img.alicdn.com/tfs/TB18So3dBKw3KVjSZFOXXarDVXa-41-48.png\",\"id\":100000},\"payChannelEchoExtAttribute\":\"{\\\"paymentChannelSupportCurrencies\\\":[\\\"AUD\\\",\\\"EUR\\\",\\\"GBP\\\",\\\"USD\\\"]}\",\"paymentGateway\":\"aepay\",\"paymentMethodName\":\"AMEX\"},{\"currencyList\":[\"CHF\",\"EUR\",\"USD\",\"CAD\",\"AUD\",\"KRW\",\"JPY\",\"PLN\",\"GBP\",\"HUF\",\"SEK\",\"NZD\",\"RUB\"],\"extAttributes\":{\"subPaymentIcon\":\"https://img.alicdn.com/tfs/TB19qM7drus3KVjSZKbXXXqkFXa-39-48.png\",\"id\":100000},\"payChannelEchoExtAttribute\":\"{\\\"paymentChannelSupportCurrencies\\\":[\\\"CHF\\\",\\\"EUR\\\",\\\"USD\\\",\\\"CAD\\\",\\\"AUD\\\",\\\"KRW\\\",\\\"JPY\\\",\\\"PLN\\\",\\\"GBP\\\",\\\"HUF\\\",\\\"SEK\\\",\\\"NZD\\\",\\\"RUB\\\"]}\",\"paymentGateway\":\"aepay\",\"paymentMethodName\":\"JCB\"},{\"currencyList\":[\"CHF\",\"EUR\",\"USD\",\"CAD\",\"INR\",\"AUD\",\"KRW\",\"JPY\",\"PLN\",\"GBP\",\"SEK\",\"NZD\",\"RUB\"],\"extAttributes\":{\"subPaymentIcon\":\"https://img.alicdn.com/tfs/TB1zzRoawFY.1VjSZFqXXadbXXa-47-48.png\",\"id\":100000},\"payChannelEchoExtAttribute\":\"{\\\"paymentChannelSupportCurrencies\\\":[\\\"CHF\\\",\\\"EUR\\\",\\\"USD\\\",\\\"CAD\\\",\\\"INR\\\",\\\"AUD\\\",\\\"KRW\\\",\\\"JPY\\\",\\\"PLN\\\",\\\"GBP\\\",\\\"SEK\\\",\\\"NZD\\\",\\\"RUB\\\"]}\",\"paymentGateway\":\"aepay\",\"paymentMethodName\":\"DISCOVER\"},{\"currencyList\":[\"CHF\",\"EUR\",\"USD\",\"CAD\",\"INR\",\"AUD\",\"KRW\",\"JPY\",\"PLN\",\"GBP\",\"HUF\",\"SEK\",\"NZD\",\"RUB\"],\"extAttributes\":{\"subPaymentIcon\":\"https://img.alicdn.com/tfs/TB1YB.VdwaH3KVjSZFjXXcFWpXa-36-48.png\",\"id\":100000},\"payChannelEchoExtAttribute\":\"{\\\"paymentChannelSupportCurrencies\\\":[\\\"CHF\\\",\\\"EUR\\\",\\\"USD\\\",\\\"CAD\\\",\\\"INR\\\",\\\"AUD\\\",\\\"KRW\\\",\\\"JPY\\\",\\\"PLN\\\",\\\"GBP\\\",\\\"HUF\\\",\\\"SEK\\\",\\\"NZD\\\",\\\"RUB\\\"]}\",\"paymentGateway\":\"aepay\",\"paymentMethodName\":\"DINERS\"},{\"currencyList\":[\"TRY\"],\"extAttributes\":{\"subPaymentIcon\":\"https://img.alicdn.com/tfs/TB1_CsJckxz61VjSZFrXXXeLFXa-66-48.png\",\"id\":100000},\"payChannelEchoExtAttribute\":\"{\\\"paymentChannelSupportCurrencies\\\":[\\\"TRY\\\"]}\",\"paymentGateway\":\"aepay\",\"paymentMethodName\":\"TROY\"}]},{\"bindCardAllowed\":true,\"boundCreditCardList\":[],\"canSelectByDefault\":false,\"extAttributes\":{\"paymentIcon\":\"https://img.alicdn.com/tfs/TB1qOgBSxjaK1RjSZFAXXbdLFXa-123-33.png\"},\"needChangeCurrency\":false,\"needCpfInput\":false,\"pmtOpt\":\"WALLET_PAYPAL\",\"screen\":0,\"subPaymentMethodList\":[{\"available\":true,\"channelRate\":\"\",\"currencyList\":[\"AUD\",\"EUR\",\"GBP\",\"USD\",\"CAD\"],\"extAttributes\":{\"subPaymentIcon\":\"https://img.alicdn.com/tfs/TB1qOgBSxjaK1RjSZFAXXbdLFXa-123-33.png\",\"id\":5600},\"payChannelEchoExtAttribute\":\"{\\\"paymentChannelSupportCurrencies\\\":[\\\"AUD\\\",\\\"EUR\\\",\\\"GBP\\\",\\\"USD\\\",\\\"CAD\\\"]}\",\"paymentGateway\":\"aepay\",\"paymentMethodName\":\"WALLET_PAYPAL\",\"requiredList\":[]}]},{\"bindCardAllowed\":true,\"extAttributes\":{},\"needChangeCurrency\":false,\"needCpfInput\":false,\"pmtOpt\":\"others\",\"screen\":1,\"subPaymentMethodList\":[{\"available\":true,\"extAttributes\":{},\"paymentGateway\":\"aepay\",\"paymentMethodName\":\"...\"}]}],\"checkoutNotice\":\"\",\"defaultChosenChannel\":{},\"echoPaymentExtAttribute\":\"{\\\"payRequestIdempotentNo\\\":\\\"658f6382-8022-4e08-9ab7-d65c074a0e46\\\",\\\"ipayProtocolKey\\\":\\\"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\\\"}\",\"isNeedFrontCashierBackend\":true,\"paymentAuthKey\":\"**********\",\"paymentExtraInfo\":{\"sirius\":\"true\",\"queryCardBinUrl\":\"https://open-na.alipay.com/api/v301/alipay/intl/user/card/queryCardBinInfo.htm\",\"clientId\":\"5J5XSL382YMHBT04\",\"countryCode\":\"+7\",\"aghCacheCardUrl\":\"https://open-na.alipay.com/amsin/api/v1/paymentMethods/cacheCard.htm\",\"aghRsaPublicKey\":\"MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA32TIUEPpDC9NHzB9pVB8GGAY2/oIBCCCXd0WOmYbtWiWPDWhgvSyNQLRQ9HbcPOg74NKDpZNL1rhR2GTQagy2EM5RJp/2R+1R0w0MWykl7aJ/yNNQdLsE+kE5X1NkbjZuME4VZFKn4un2BdSph7xIQYPgxo7DMMCQtrEun2xoh3f7W2gpKJj/ubAzoPHHFXJ4KwwgixPcnHyFYpQabX6hRWwDMJ7iet3bz9Mz/qE3Z89sVgVfKqrOohEMVzRHiJVOWrrTtUup7OBu0aYwWq9pVv+9W9U8dXWbxguIEOhKpeM9NnKPJs8sjGN0s87du4i7C5Ou/ZCZvWdzQSgtLqrVwIDAQAB\",\"rsaPublicKey\":\"MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAuIezfUe4zTOtH6WRpUR4y+o0i74DUDSBXuv+eJdNhpGkH9WRwb6Raj/mZ/Lj6a1AvvRujI28GGpA2u7o8RbKR2QB4Tv2reX1dL+T8C9XsIdLQu7KXZI30p72U1CkQ+HoSTzHXCnpuVSOlhzRRT8S7IGSoBSUqoNnG/Zv1FopHN0pZZhHN0CapUdDwSb2adC94yL/Y3EPuvBZTXBMNtjLb/qzpu93cW2L4lkOBMQ2erhh096nEc9jhiDMPcO91seZCSmmL1wBQK97HtNvQ7vLiZwcUMsFPoeI8vBWtNGOZC1ZX5oSOqStanTGZoDtq2c2TiAfT2lBUUIAhGr8FEmhiwIDAQAB\",\"cacheCardUrl\":\"https://open-na.alipay.com/api/v301/alipay/intl/user/asset/cacheCard.htm\",\"assetType\":\"MIXCARD\"},\"paymentPromotionDTO\":{}}},\"checkoutPriceComponent_**********\":{\"fields\":{\"exchangeRate\":0.0,\"extAttributes\":{},\"totalCashAmount\":\"US $681.63\",\"totalCashAmountValue\":681.63,\"totalCashCurrency\":\"USD\"}}}", "payCostFee": null, "extensions": {"protocolVersion": null}, "totalPayFee": {"cent": 68163, "currencyCode": "USD"}, "paymentChannels": ["Visa", "master", "MAESTRO", "Amex", "Diners", "DISCOVER", "JCB", "WebMoney", "WU", "TT"], "payPromotionFee": null}, "isNewUser": false, "shoppingSummaryPrice": {"shoppingTotalFee": {"cent": 68163, "currencyCode": "USD"}}, "marketingTips": null}, "errorCode": null, "repeated": false, "retry": false}, "rpcContext": {"param.name.from.hsf.context": "US|en-US|USD|America/Los_Angeles", "_GL_K": "AE_GLOBAL@US|en-US|USD|America/Los_Angeles"}, "serviceName": "com.alibaba.global.buy.api.facade.CheckoutMultiterminalFacade", "showIdentity": "com.alibaba.global.buy.api.facade.CheckoutMultiterminalFacade@renderOrder(RenderOrderRequest):1.0.0", "showInvokeIdentity": null, "stackTrace": null, "startTime": 0, "subInvokeType": null, "throwable": null, "timeCost": "0.000ms", "type": "ALI_HSF", "uniqueName": "com.alibaba.global.buy.api.facade.CheckoutMultiterminalFacade:1.0.0", "version": "1.0.0"}, "subInvokes": {"com.alibaba.global.expression.protocol.sdk.facade.FulfillmentServiceFacade@splitTradeOrder(SplitTradeOrderRequest)": [{"afterExecuteParams": null, "endTime": "26120001562207198", "exceptionStackTrace": null, "extInvokeIdentity": null, "extraPrams": null, "fuzzyInvokeIdentity": null, "identity": "com.alibaba.global.expression.protocol.sdk.facade.FulfillmentServiceFacade@splitTradeOrder(SplitTradeOrderRequest)", "index": 23, "isSerialized": null, "method": "splitTradeOrder", "params": [{"extendParam": {"extensionDTOMap": {"/traderOrders/traderOrderLines/$90": {"digital": false, "fulfillmentMode": "other", "sellerId": *********, "subWarehouseType": null, "skuTags": ["82052", "110572"], "uniqueId": "90"}}}, "routingInfo": {"sellerId": null, "buyerId": null}, "invokeInfo": {"appName": null}, "tradeOrders": [{"tradeOrderId": "0", "tradeOrderLines": [{"warehouse": null, "tradeOrderLineId": "90"}]}], "buyer": {"receiverAddress": {"addressType": "home", "locationTreeAddressId": "910100010000000000-910100010001000000", "countryISOCode": "IT", "latitude": null, "longitude": null}, "buyerId": **********}}], "paramsInstType": null, "paramsType": ["com.alibaba.global.expression.protocol.sdk.model.request.SplitTradeOrderRequest"], "relatedCollectIndex": 0, "response": {"result": {"tradeOrderFulfillmentGroups": [{"invalidTradeOrderLines": [], "validFulfillmentGroups": [{"features": {"sourceKey": "*********_false_deliver"}, "tradeOrderLineIds": ["90"]}], "tradeOrderId": "0"}]}, "success": true, "errorCode": null}, "rpcContext": null, "serviceName": "com.alibaba.global.expression.protocol.sdk.facade.FulfillmentServiceFacade", "showIdentity": "com.alibaba.global.expression.protocol.sdk.facade.FulfillmentServiceFacade@splitTradeOrder(SplitTradeOrderRequest)", "showInvokeIdentity": null, "stackTrace": "com.alibaba.global.satellite.proxy.hsf.utils.hook.SatelliteHSFConsumerProxyHook.invoke@92\ncom.alibaba.global.satellite.proxy.hsf.utils.hook.SatelliteHSFConsumerProxyHook.invoke@29\ncom.alibaba.global.satellite.utils.SentinelUtils.invoke@37\ncom.alibaba.global.satellite.proxy.hsf.utils.hook.SatelliteHSFConsumerProxyHook.proxy@58\ncom.alibaba.global.common.proxy.AbstractProxyHandler.invoke@63\ncom.alibaba.global.common.proxy.JdkProxy$Handler.invoke@66\ncom.alibaba.trade.function.delivery.repository.protocol.GlobalFulfillmentServiceRepositoryImpl.splitLogicSource@62\ncom.alibaba.trade.function.delivery.repository.DeliveryOptionRepositoryImpl.splitLogicSource@1011\ncom.alibaba.trade.function.delivery.service.GlobalTradeDeliveryTransportMethodDomainServiceImpl.splitLogicSource@39\ncom.alibaba.trade.scenario.buy.global.ShoppingDeliverySplitActivity.callDomainService@56\ncom.alibaba.trade.scenario.buy.global.ShoppingDeliverySplitActivity.callDomainService@34\ncom.alibaba.trade.scenario.activity.BaseTradeActivity.execute@37\ncom.alibaba.global.buy.activities.order.OrderGroupingActivity.execute@44\ncom.alibaba.smart.framework.engine.modules.smart.provider.performer.JavaPerformer.perform@56\ncom.alibaba.smart.framework.engine.provider.impl.ComboInvoker.invoke@27\ncom.alibaba.smart.framework.engine.pvm.impl.AbstractPvmElement.invoke@47\ncom.alibaba.smart.framework.engine.provider.impl.DefaultExecutePolicyBehavior.execute@63\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmActivity.execute@59\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmActivity.enter@53\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmTransition.execute@43\n", "startTime": "26120001554642002", "subInvokeType": "rpc", "throwable": null, "timeCost": "7.565ms", "type": "JAVA"}], "com.alibaba.global.payment.api.facade.GlobalPaymentCashierServiceFacade@querySupportPaymentChannels(QuerySupportPaymentChannelRequest)": [{"afterExecuteParams": null, "endTime": "26120005075720506", "exceptionStackTrace": null, "extInvokeIdentity": null, "extraPrams": null, "fuzzyInvokeIdentity": null, "identity": "com.alibaba.global.payment.api.facade.GlobalPaymentCashierServiceFacade@querySupportPaymentChannels(QuerySupportPaymentChannelRequest)", "index": 33, "isSerialized": null, "method": "querySupportPaymentChannels", "params": [{"routeId": null, "countryCode": "GLOBAL", "siteId": null, "attributes": {}, "source": null, "platform": null}], "paramsInstType": null, "paramsType": ["com.alibaba.global.payment.api.request.QuerySupportPaymentChannelRequest"], "relatedCollectIndex": 0, "response": {"success": true, "module": ["Visa", "master", "MAESTRO", "Amex", "Diners", "DISCOVER", "JCB", "WebMoney", "WU", "TT"], "errorCode": null, "repeated": false, "retry": false}, "rpcContext": null, "serviceName": "com.alibaba.global.payment.api.facade.GlobalPaymentCashierServiceFacade", "showIdentity": "com.alibaba.global.payment.api.facade.GlobalPaymentCashierServiceFacade@querySupportPaymentChannels(QuerySupportPaymentChannelRequest)", "showInvokeIdentity": null, "stackTrace": "com.alibaba.global.satellite.proxy.hsf.utils.hook.SatelliteHSFConsumerProxyHook.invoke@92\ncom.alibaba.global.satellite.proxy.hsf.utils.hook.SatelliteHSFConsumerProxyHook.invoke@29\ncom.alibaba.global.satellite.utils.SentinelUtils.invoke@37\ncom.alibaba.global.satellite.proxy.hsf.utils.hook.SatelliteHSFConsumerProxyHook.proxy@58\ncom.alibaba.global.common.proxy.AbstractProxyHandler.invoke@63\ncom.alibaba.global.common.proxy.JdkProxy$Handler.invoke@66\ncom.alibaba.trade.function.payment.repository.impl.PaymentRepositoryImpl.queryPaymentMethod@318\ncom.alibaba.trade.function.payment.ability.cashier.GlobalPaymentCashierAbility.getTenantCashier@57\ncom.alibaba.trade.function.payment.ability.cashier.GlobalPaymentCashierAbility.getTenantCashier@33\ncom.alibaba.trade.function.payment.service.TradePaymentCashierDomainServiceImpl.getTenantExtra@103\ncom.alibaba.trade.function.payment.service.TradePaymentCashierDomainServiceImpl.renderCashier@76\ncom.alibaba.global.buy.activities.payment.PaymentRenderActivityV2.execute@78\ncom.alibaba.smart.framework.engine.modules.smart.provider.performer.JavaPerformer.perform@56\ncom.alibaba.smart.framework.engine.provider.impl.ComboInvoker.invoke@27\ncom.alibaba.smart.framework.engine.pvm.impl.AbstractPvmElement.invoke@47\ncom.alibaba.smart.framework.engine.provider.impl.DefaultExecutePolicyBehavior.execute@63\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmActivity.execute@59\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmActivity.enter@53\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmTransition.execute@43\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmActivity.executeRecursively@103\n", "startTime": "26120005069885842", "subInvokeType": "rpc", "throwable": null, "timeCost": "5.835ms", "type": "JAVA"}], "com.alibaba.global.ic.api.CustomerProductServiceFacade@queryProduct(ProductQueryRequest)": [{"afterExecuteParams": null, "endTime": "26120001161411265", "exceptionStackTrace": null, "extInvokeIdentity": null, "extraPrams": null, "fuzzyInvokeIdentity": null, "identity": "com.alibaba.global.ic.api.CustomerProductServiceFacade@queryProduct(ProductQueryRequest)", "index": 1, "isSerialized": null, "method": "queryProduct", "params": [{"productQueryConditions": [{"innerId": null, "sellerId": null, "productId": ****************, "configId": null, "outerId": null, "type": 1, "skuId": "*****************"}], "productQueryOption": null, "requestLocaleList": null, "appInfo": null, "regionCondition": null, "requireContentTypes": [3, 5, 6], "requireSellerExtraDataBizTypes": null, "requestLocale": null, "tenant": null, "operator": null, "requestSourceType": null, "sourceAppInfo": null}], "paramsInstType": null, "paramsType": ["com.alibaba.global.ic.dto.scenario.query.ProductQueryRequest"], "relatedCollectIndex": 0, "response": {"success": true, "errorCodes": [], "singleQueryErrorCodeMap": {}, "model": [{"redMart": null, "featureMap": {"company_id": "*********", "gpfSource": "common-market-100", "src": "post", "detail_type": "text", "owner_id": "*********", "delivery_time": "1", "publish_by_new_service": "************", "npp": "1", "translate_flag": "00", "creator_nick": "cn1528892749ywrs", "source_app": "gpf-i18n", "dp_post_id": "***********", "dp_status": "2", "creator_uid": "*********", "owner_nick": "cn1528892749ywrs", "publish_biz_code": "ali.ae.general", "write_trunk_date": "1618753297743"}, "descriptionList": null, "unitMetric": null, "unitNumber": null, "productUnit": *********, "itemPerEach": null, "packageType": "sell_by_piece", "postageId": ***********, "productId": ****************, "productUnitOddName": "piece", "productUnitMultiName": "pieces", "tags": [82052], "createTimeMillis": 1618753296000, "sellerExtraDataQueryResultList": null, "virtualBundle": null, "returnPolicyId": null, "skuIdAndSkuMap": {"*****************": {"featureMap": {"inv_code": "14:193;*********:*********;*********:*********", "sku_bulk_discount": "10", "sku_bulk_order": "6"}, "showInCatalog": true, "packageWeight": {"unit": null, "valueId": -1, "valueText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "0.100", "autoTranslate": null}}}, "imageDTO": null, "start": null, "end": null, "propertyText": null, "attributes": {}, "hasInterval": null, "propertyId": 224407806, "valueAliasText": null}, "groupId": null, "packageWidth": {"unit": null, "valueId": -1, "valueText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "12", "autoTranslate": null}}}, "imageDTO": null, "start": null, "end": null, "propertyText": null, "attributes": {}, "hasInterval": null, "propertyId": 224404847, "valueAliasText": null}, "packageContent": null, "bigGift": null, "bundleSubRelationList": null, "priceDTO": {"promotionPrice": {"promotionStart": null, "promotionEnd": null, "price": {"number": 300.0, "monetaryContext": {"data": {"amountType": {"$ref": "$.body.onlineContext.mainInvoke.response.module.orders[0].orderLines[0].product.unitPrice.monetaryContext.data.amountType"}, "precision": 256, "java.lang.Class": {"$ref": "$.body.onlineContext.mainInvoke.response.module.orders[0].orderLines[0].product.unitPrice.monetaryContext.data.amountType"}, "java.math.RoundingMode": "HALF_EVEN"}}, "currency": {"context": {"data": {"provider": "java.util.Currency"}}, "baseCurrency": {"$ref": "$.body.onlineContext.mainInvoke.response.module.orders[0].orderLines[0].product.unitPrice.currency.baseCurrency"}}}, "bulkPrice": {"number": 270.0, "monetaryContext": {"$ref": "$.body.onlineContext.subInvokes.com\\.alibaba\\.global\\.ic\\.api\\.CustomerProductServiceFacade\\@queryProduct\\(ProductQueryRequest\\)[0].response.model[0].skuIdAndSkuMap.*****************.priceDTO.promotionPrice.price.monetaryContext"}, "currency": {"$ref": "$.body.onlineContext.subInvokes.com\\.alibaba\\.global\\.ic\\.api\\.CustomerProductServiceFacade\\@queryProduct\\(ProductQueryRequest\\)[0].response.model[0].skuIdAndSkuMap.*****************.priceDTO.promotionPrice.price.currency"}}}, "bulkOriginalPrice": {"number": 450.0, "monetaryContext": {"$ref": "$.body.onlineContext.subInvokes.com\\.alibaba\\.global\\.ic\\.api\\.CustomerProductServiceFacade\\@queryProduct\\(ProductQueryRequest\\)[0].response.model[0].skuIdAndSkuMap.*****************.priceDTO.promotionPrice.price.monetaryContext"}, "currency": {"$ref": "$.body.onlineContext.subInvokes.com\\.alibaba\\.global\\.ic\\.api\\.CustomerProductServiceFacade\\@queryProduct\\(ProductQueryRequest\\)[0].response.model[0].skuIdAndSkuMap.*****************.priceDTO.promotionPrice.price.currency"}}, "countryPriceList": [{"regionId": 223, "promotionPrice": {"number": 535, "monetaryContext": {"$ref": "$.body.onlineContext.subInvokes.com\\.alibaba\\.global\\.ic\\.api\\.CustomerProductServiceFacade\\@queryProduct\\(ProductQueryRequest\\)[0].response.model[0].skuIdAndSkuMap.*****************.priceDTO.promotionPrice.price.monetaryContext"}, "currency": {"$ref": "$.body.onlineContext.subInvokes.com\\.alibaba\\.global\\.ic\\.api\\.CustomerProductServiceFacade\\@queryProduct\\(ProductQueryRequest\\)[0].response.model[0].skuIdAndSkuMap.*****************.priceDTO.promotionPrice.price.currency"}}, "countryCode": "IT", "price": {"number": 555, "monetaryContext": {"$ref": "$.body.onlineContext.subInvokes.com\\.alibaba\\.global\\.ic\\.api\\.CustomerProductServiceFacade\\@queryProduct\\(ProductQueryRequest\\)[0].response.model[0].skuIdAndSkuMap.*****************.priceDTO.promotionPrice.price.monetaryContext"}, "currency": {"$ref": "$.body.onlineContext.subInvokes.com\\.alibaba\\.global\\.ic\\.api\\.CustomerProductServiceFacade\\@queryProduct\\(ProductQueryRequest\\)[0].response.model[0].skuIdAndSkuMap.*****************.priceDTO.promotionPrice.price.currency"}}, "bulkPrice": {"number": 499.5, "monetaryContext": {"$ref": "$.body.onlineContext.subInvokes.com\\.alibaba\\.global\\.ic\\.api\\.CustomerProductServiceFacade\\@queryProduct\\(ProductQueryRequest\\)[0].response.model[0].skuIdAndSkuMap.*****************.priceDTO.promotionPrice.price.monetaryContext"}, "currency": {"$ref": "$.body.onlineContext.subInvokes.com\\.alibaba\\.global\\.ic\\.api\\.CustomerProductServiceFacade\\@queryProduct\\(ProductQueryRequest\\)[0].response.model[0].skuIdAndSkuMap.*****************.priceDTO.promotionPrice.price.currency"}}, "bulkPromotionPrice": {"number": 481.5, "monetaryContext": {"$ref": "$.body.onlineContext.subInvokes.com\\.alibaba\\.global\\.ic\\.api\\.CustomerProductServiceFacade\\@queryProduct\\(ProductQueryRequest\\)[0].response.model[0].skuIdAndSkuMap.*****************.priceDTO.promotionPrice.price.monetaryContext"}, "currency": {"$ref": "$.body.onlineContext.subInvokes.com\\.alibaba\\.global\\.ic\\.api\\.CustomerProductServiceFacade\\@queryProduct\\(ProductQueryRequest\\)[0].response.model[0].skuIdAndSkuMap.*****************.priceDTO.promotionPrice.price.currency"}}, "includeCountryCodes": ["IT"], "preSalePriceList": null}], "preSalePriceList": null, "fixedPrice": {"number": 500.0, "monetaryContext": {"$ref": "$.body.onlineContext.subInvokes.com\\.alibaba\\.global\\.ic\\.api\\.CustomerProductServiceFacade\\@queryProduct\\(ProductQueryRequest\\)[0].response.model[0].skuIdAndSkuMap.*****************.priceDTO.promotionPrice.price.monetaryContext"}, "currency": {"$ref": "$.body.onlineContext.subInvokes.com\\.alibaba\\.global\\.ic\\.api\\.CustomerProductServiceFacade\\@queryProduct\\(ProductQueryRequest\\)[0].response.model[0].skuIdAndSkuMap.*****************.priceDTO.promotionPrice.price.currency"}}, "dpCostPriceDTO": null}, "bobConfigId": ****************, "descPropertyPairList": [], "detailPageUrl": "https://www.aliexpress.com/item/****************.html", "mediaInfoDTO": {"deleteAllMarketImages": null, "images": [], "videos": [], "deleteAllVideos": false, "deleteAllGeneralImages": null, "deleteAllImagesWhenEmpty": false}, "onlyMiniShop": null, "showInDetailPage": true, "comingSoonTimeMillis": null, "packageLength": {"unit": null, "valueId": -1, "valueText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "11", "autoTranslate": null}}}, "imageDTO": null, "start": null, "end": null, "propertyText": null, "attributes": {}, "hasInterval": null, "propertyId": *********, "valueAliasText": null}, "shipFromCountryCode": "CN", "barcode": "", "bundleParentRelationList": null, "bundleScItemRelationList": null, "skuId": "*****************", "miniShop": null, "taxClass": null, "bobSimpleId": "*****************", "sellerPromotion": null, "productId": ****************, "majorImageDTO": {"featureMap": {"measure": "400x400", "sub_type": "11"}, "timeRangeDTO": null, "imageId": null, "modifyDate": null, "imageUrl": "H251f297e9e13462fa52ca2ff1ae5ad3fB.jpg", "name": null, "main": true, "position": 0, "type": {"value": 1}, "properties": null, "status": null, "createDate": null}, "miniShopDetailPageUrl": null, "showInSearch": 2, "salable": true, "expandMap": {"sku_attr": {"valueStart": null, "syncVersion": null, "childExtendMap": {}, "valueEnd": null, "version": null, "key": "sku_attr", "content": "14:193;*********:*********;*********:*********"}, "price_for_dp": {"valueStart": null, "syncVersion": null, "childExtendMap": {}, "valueEnd": null, "version": 0, "key": "price_for_dp", "content": "{\"currency\":\"USD\",\"price\":50000,\"promotionPrice\":30000,\"regionPriceMap\":{\"IT\":{\"countryCode\":\"IT\",\"currency\":\"USD\",\"price\":55500,\"promotionPrice\":53500}}}"}, "adjusted_price": {"valueStart": null, "syncVersion": null, "childExtendMap": {}, "valueEnd": null, "version": 1, "key": "adjusted_price", "content": "{\"sku\":{\"GLO\":\"USD:50000:50100\",\"IT\":\"USD:55500:55600\"},\"salePrice\":{\"GLO\":\"USD:30000:30100\",\"IT\":\"USD:53500:53600\"},\"info\":\"ver1.0\"}"}, "multi_dim_tags": {"valueStart": null, "syncVersion": null, "childExtendMap": {}, "valueEnd": null, "version": 0, "key": "multi_dim_tags", "content": "[{\"value\":110572}]"}, "tags": {"valueStart": null, "syncVersion": null, "childExtendMap": {}, "valueEnd": null, "version": 0, "key": "tags", "content": "110572"}}, "salePropertyPairList": [{"unit": null, "valueId": 193, "valueText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "Black", "autoTranslate": false}}}, "imageDTO": null, "start": null, "end": null, "propertyText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "Color", "autoTranslate": false}}}, "attributes": {}, "hasInterval": null, "propertyId": 14, "valueAliasText": null}, {"unit": null, "valueId": *********, "valueText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "China", "autoTranslate": false}}}, "imageDTO": null, "start": null, "end": null, "propertyText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "Ships From", "autoTranslate": false}}}, "attributes": {}, "hasInterval": null, "propertyId": *********, "valueAliasText": null}, {"unit": null, "valueId": *********, "valueText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "Bundle 1", "autoTranslate": false}}}, "imageDTO": null, "start": null, "end": null, "propertyText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "Bundle", "autoTranslate": false}}}, "attributes": {}, "hasInterval": null, "propertyId": *********, "valueAliasText": null}], "tags": [82052, 110572], "innerId": "****************_AE-*****************", "crazyDealDTO": null, "packageHeight": {"unit": null, "valueId": -1, "valueText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "13", "autoTranslate": null}}}, "imageDTO": null, "start": null, "end": null, "propertyText": null, "attributes": {}, "hasInterval": null, "propertyId": 224495602, "valueAliasText": null}, "forceFBL": null, "installment": null, "auditStatus": 1, "outerId": "****************_*****************", "bulkMinQuantity": 6, "status": 1}}, "brandId": 200658765, "warrantyType": null, "digitalEmail": null, "status": 1, "descriptionFeatureMap": null, "topUp": false, "shopQueryResultDTO": null, "netflix": null, "title": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "testNoDPRegion", "autoTranslate": true}}}, "retail": null, "propertyPairList": [{"unit": null, "valueId": 200658765, "valueText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "<PERSON><PERSON>", "autoTranslate": false}}}, "imageDTO": null, "start": null, "end": null, "propertyText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "Brand Name", "autoTranslate": false}}}, "attributes": {}, "hasInterval": null, "propertyId": 2, "valueAliasText": null}, {"unit": null, "valueId": 203486261, "valueText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "Redmi 6A 2GB 16GB", "autoTranslate": false}}}, "imageDTO": null, "start": null, "end": null, "propertyText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "<PERSON><PERSON>", "autoTranslate": false}}}, "attributes": {}, "hasInterval": null, "propertyId": 200006205, "valueAliasText": null}, {"unit": null, "valueId": 200005443, "valueText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "Detachable", "autoTranslate": false}}}, "imageDTO": null, "start": null, "end": null, "propertyText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "Battery Type", "autoTranslate": false}}}, "attributes": {}, "hasInterval": null, "propertyId": 200001051, "valueAliasText": null}, {"unit": null, "valueId": 8177, "valueText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "4G", "autoTranslate": false}}}, "imageDTO": null, "start": null, "end": null, "propertyText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "ROM", "autoTranslate": false}}}, "attributes": {}, "hasInterval": null, "propertyId": 200001047, "valueAliasText": null}, {"unit": null, "valueId": 200214849, "valueText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "18W", "autoTranslate": false}}}, "imageDTO": null, "start": null, "end": null, "propertyText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "Charging Power", "autoTranslate": false}}}, "attributes": {}, "hasInterval": null, "propertyId": 247081304, "valueAliasText": null}, {"unit": null, "valueId": 9441741844, "valueText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "CN(Origin)", "autoTranslate": false}}}, "imageDTO": null, "start": null, "end": null, "propertyText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "Origin", "autoTranslate": false}}}, "attributes": {}, "hasInterval": null, "propertyId": 219, "valueAliasText": null}, {"unit": null, "valueId": 360543, "valueText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "New", "autoTranslate": false}}}, "imageDTO": null, "start": null, "end": null, "propertyText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "Item Condition", "autoTranslate": false}}}, "attributes": {}, "hasInterval": null, "propertyId": 200000649, "valueAliasText": null}, {"unit": null, "valueId": 361828, "valueText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "Android", "autoTranslate": false}}}, "imageDTO": null, "start": null, "end": null, "propertyText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "Operation System", "autoTranslate": false}}}, "attributes": {}, "hasInterval": null, "propertyId": 190, "valueAliasText": null}, {"unit": null, "valueId": 3611170127, "valueText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "Iris Recognition", "autoTranslate": false}}}, "imageDTO": null, "start": null, "end": null, "propertyText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "Biometrics Technology", "autoTranslate": false}}}, "attributes": {}, "hasInterval": null, "propertyId": 224877540, "valueAliasText": null}, {"unit": null, "valueId": 201450545, "valueText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "Face Recognition", "autoTranslate": false}}}, "imageDTO": null, "start": null, "end": null, "propertyText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "Biometrics Technology", "autoTranslate": false}}}, "attributes": {}, "hasInterval": null, "propertyId": 224877540, "valueAliasText": null}, {"unit": null, "valueId": 200003454, "valueText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "NONE", "autoTranslate": false}}}, "imageDTO": null, "start": null, "end": null, "propertyText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "Front Camera Quantity", "autoTranslate": false}}}, "attributes": {}, "hasInterval": null, "propertyId": 224873177, "valueAliasText": null}, {"unit": null, "valueId": 202433821, "valueText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "12MP", "autoTranslate": false}}}, "imageDTO": null, "start": null, "end": null, "propertyText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "Rear Camera Pixel", "autoTranslate": false}}}, "attributes": {}, "hasInterval": null, "propertyId": 224878612, "valueAliasText": null}, {"unit": null, "valueId": 24614255, "valueText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "13MP", "autoTranslate": false}}}, "imageDTO": null, "start": null, "end": null, "propertyText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "Rear Camera Pixel", "autoTranslate": false}}}, "attributes": {}, "hasInterval": null, "propertyId": 224878612, "valueAliasText": null}, {"unit": null, "valueId": 8654, "valueText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "1200", "autoTranslate": false}}}, "imageDTO": null, "start": null, "end": null, "propertyText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "Battery Capacity(mAh)", "autoTranslate": false}}}, "attributes": {}, "hasInterval": null, "propertyId": 200001054, "valueAliasText": null}, {"unit": null, "valueId": 349907, "valueText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "Yes", "autoTranslate": false}}}, "imageDTO": null, "start": null, "end": null, "propertyText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "Touch Screen", "autoTranslate": false}}}, "attributes": {}, "hasInterval": null, "propertyId": 19093, "valueAliasText": null}, {"unit": null, "valueId": 985260610, "valueText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "Quick Charge 2.0", "autoTranslate": false}}}, "imageDTO": null, "start": null, "end": null, "propertyText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "Fast Charging", "autoTranslate": false}}}, "attributes": {}, "hasInterval": null, "propertyId": 224876238, "valueAliasText": null}, {"unit": null, "valueId": 11592944546, "valueText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "Charge Turbo", "autoTranslate": false}}}, "imageDTO": null, "start": null, "end": null, "propertyText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "Fast Charging", "autoTranslate": false}}}, "attributes": {}, "hasInterval": null, "propertyId": 224876238, "valueAliasText": null}, {"unit": null, "valueId": 200000752, "valueText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "english", "autoTranslate": false}}}, "imageDTO": null, "start": null, "end": null, "propertyText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "Language", "autoTranslate": false}}}, "attributes": {}, "hasInterval": null, "propertyId": 200001052, "valueAliasText": null}, {"unit": null, "valueId": 200000711, "valueText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "French", "autoTranslate": false}}}, "imageDTO": null, "start": null, "end": null, "propertyText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "Language", "autoTranslate": false}}}, "attributes": {}, "hasInterval": null, "propertyId": 200001052, "valueAliasText": null}, {"unit": null, "valueId": 46619268, "valueText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "240x320", "autoTranslate": false}}}, "imageDTO": null, "start": null, "end": null, "propertyText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "Display Resolution", "autoTranslate": false}}}, "attributes": {}, "hasInterval": null, "propertyId": 200001050, "valueAliasText": null}, {"unit": null, "valueId": 11521410472, "valueText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "≤512M", "autoTranslate": false}}}, "imageDTO": null, "start": null, "end": null, "propertyText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "RAM", "autoTranslate": false}}}, "attributes": {}, "hasInterval": null, "propertyId": 200001046, "valueAliasText": null}, {"unit": null, "valueId": 698, "valueText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "5", "autoTranslate": false}}}, "imageDTO": null, "start": null, "end": null, "propertyText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "Rear Camera Quantity", "autoTranslate": false}}}, "attributes": {}, "hasInterval": null, "propertyId": 224876552, "valueAliasText": null}, {"unit": null, "valueId": 200003454, "valueText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "NONE", "autoTranslate": false}}}, "imageDTO": null, "start": null, "end": null, "propertyText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "Certification", "autoTranslate": false}}}, "attributes": {}, "hasInterval": null, "propertyId": 348, "valueAliasText": null}, {"unit": null, "valueId": 202452822, "valueText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "TYPE-C", "autoTranslate": false}}}, "imageDTO": null, "start": null, "end": null, "propertyText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "Charging Interface Type", "autoTranslate": false}}}, "attributes": {}, "hasInterval": null, "propertyId": 224880215, "valueAliasText": null}, {"unit": null, "valueId": 200003415, "valueText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "8MP", "autoTranslate": false}}}, "imageDTO": null, "start": null, "end": null, "propertyText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "Front Camera Pixel", "autoTranslate": false}}}, "attributes": {}, "hasInterval": null, "propertyId": 224879507, "valueAliasText": null}, {"unit": null, "valueId": 2468, "valueText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "YES", "autoTranslate": false}}}, "imageDTO": null, "start": null, "end": null, "propertyText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "Wireless Charging", "autoTranslate": false}}}, "attributes": {}, "hasInterval": null, "propertyId": 224879916, "valueAliasText": null}, {"unit": null, "valueId": 361809, "valueText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "Flip", "autoTranslate": false}}}, "imageDTO": null, "start": null, "end": null, "propertyText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "Design", "autoTranslate": false}}}, "attributes": {}, "hasInterval": null, "propertyId": 20303, "valueAliasText": null}, {"unit": null, "valueId": 3606758119, "valueText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "2 SIM Card", "autoTranslate": false}}}, "imageDTO": null, "start": null, "end": null, "propertyText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "SIM Card Quantity", "autoTranslate": false}}}, "attributes": {}, "hasInterval": null, "propertyId": 200000791, "valueAliasText": null}, {"unit": null, "valueId": 200006050, "valueText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "4.5", "autoTranslate": false}}}, "imageDTO": null, "start": null, "end": null, "propertyText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "<PERSON><PERSON><PERSON>", "autoTranslate": false}}}, "attributes": {}, "hasInterval": null, "propertyId": 200000233, "valueAliasText": null}, {"unit": null, "valueId": 2467, "valueText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "NO", "autoTranslate": false}}}, "imageDTO": null, "start": null, "end": null, "propertyText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "3.5mm Headphone Port", "autoTranslate": false}}}, "attributes": {}, "hasInterval": null, "propertyId": 224871185, "valueAliasText": null}, {"unit": null, "valueId": 3611397106, "valueText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "Curved Screen", "autoTranslate": false}}}, "imageDTO": null, "start": null, "end": null, "propertyText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "Screen Type", "autoTranslate": false}}}, "attributes": {}, "hasInterval": null, "propertyId": 200001081, "valueAliasText": null}, {"unit": null, "valueId": -1, "valueText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "*********", "autoTranslate": false}}}, "imageDTO": null, "start": null, "end": null, "propertyText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "Measurement unit", "autoTranslate": false}}}, "attributes": {}, "hasInterval": null, "propertyId": 224407805, "valueAliasText": null}, {"unit": null, "valueId": -1, "valueText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "sell_by_piece", "autoTranslate": false}}}, "imageDTO": null, "start": null, "end": null, "propertyText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "Sold in", "autoTranslate": false}}}, "attributes": {}, "hasInterval": null, "propertyId": 224404845, "valueAliasText": null}, {"unit": null, "valueId": -1, "valueText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "1", "autoTranslate": false}}}, "imageDTO": null, "start": null, "end": null, "propertyText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "each pack", "autoTranslate": false}}}, "attributes": {}, "hasInterval": null, "propertyId": 224482205, "valueAliasText": null}, {"unit": null, "valueId": -1, "valueText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "0.100", "autoTranslate": false}}}, "imageDTO": null, "start": null, "end": null, "propertyText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "Package weight", "autoTranslate": false}}}, "attributes": {}, "hasInterval": null, "propertyId": 224407806, "valueAliasText": null}, {"unit": null, "valueId": -1, "valueText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "11", "autoTranslate": false}}}, "imageDTO": null, "start": null, "end": null, "propertyText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "Package size - length (cm)", "autoTranslate": false}}}, "attributes": {}, "hasInterval": null, "propertyId": *********, "valueAliasText": null}, {"unit": null, "valueId": -1, "valueText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "13", "autoTranslate": false}}}, "imageDTO": null, "start": null, "end": null, "propertyText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "Package size - height (cm)", "autoTranslate": false}}}, "attributes": {}, "hasInterval": null, "propertyId": 224495602, "valueAliasText": null}, {"unit": null, "valueId": -1, "valueText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "12", "autoTranslate": false}}}, "imageDTO": null, "start": null, "end": null, "propertyText": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "Package size - width (cm)", "autoTranslate": false}}}, "attributes": {}, "hasInterval": null, "propertyId": 224404847, "valueAliasText": null}], "highlight": null, "detailPageUrl": "https://www.aliexpress.com/item/****************.html", "originalLocale": {"country": "en", "variant": "", "language": "en"}, "descriptionExpandMap": null, "warrantyPeriod": null, "mediaInfoDTO": {"deleteAllMarketImages": null, "images": [{"featureMap": {"measure": "400x400", "sub_type": "11"}, "timeRangeDTO": null, "imageId": null, "modifyDate": null, "imageUrl": "H251f297e9e13462fa52ca2ff1ae5ad3fB.jpg", "name": null, "main": true, "position": 0, "type": {"$ref": "$.body.onlineContext.subInvokes.com\\.alibaba\\.global\\.ic\\.api\\.CustomerProductServiceFacade\\@queryProduct\\(ProductQueryRequest\\)[0].response.model[0].skuIdAndSkuMap.*****************.majorImageDTO.type"}, "properties": null, "status": null, "createDate": null}, {"featureMap": {"measure": "610x609"}, "timeRangeDTO": null, "imageId": null, "modifyDate": null, "imageUrl": "He620e4b342494140b4578bb95a16946a4.jpeg", "name": null, "main": false, "position": 1, "type": {"$ref": "$.body.onlineContext.subInvokes.com\\.alibaba\\.global\\.ic\\.api\\.CustomerProductServiceFacade\\@queryProduct\\(ProductQueryRequest\\)[0].response.model[0].skuIdAndSkuMap.*****************.majorImageDTO.type"}, "properties": null, "status": null, "createDate": null}], "videos": [], "deleteAllVideos": false, "deleteAllGeneralImages": null, "deleteAllImagesWhenEmpty": false}, "categoryQueryResultDTO": {"leafId": 5090301, "topId": 509, "topCategoryName": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "Phones & Telecommunications", "autoTranslate": null}}}, "leafCategoryName": {"defaultLocale": {"country": "en", "variant": "", "language": "en"}, "langAndValueMap": {"MapKey-hidden": {"value": "Mobile Phones", "autoTranslate": null}}}}, "warrantyPolicy": null, "originalCurrencyCode": "USD", "isDigital": null, "ticket": false, "digitalSms": null, "cardCode": false, "lotNum": 1, "sellerQueryResultDTO": {"subId": null, "id": *********}, "isDigitalUtilities": null, "expandMap": {"11": {"valueStart": null, "syncVersion": null, "childExtendMap": {}, "valueEnd": null, "version": 1, "key": "11", "content": "{\"CN\":{\"free_ship_to\":[\"PR\",\"PW\",\"QA\",\"AD\",\"AE\",\"AF\",\"AG\",\"AI\",\"AL\",\"AM\",\"AN\",\"AO\",\"AQ\",\"AS\",\"AT\",\"RE\",\"AU\",\"AW\",\"BA\",\"BB\",\"BD\",\"RU\",\"BE\",\"BF\",\"BG\",\"BH\",\"BI\",\"BJ\",\"BM\",\"BN\",\"BO\",\"SA\",\"SB\",\"BQ\",\"SC\",\"BR\",\"BS\",\"ASC\",\"SE\",\"BT\",\"SG\",\"BV\",\"BW\",\"SH\",\"SI\",\"SK\",\"BZ\",\"SL\",\"SM\",\"SN\",\"CA\",\"SR\",\"SS\",\"CC\",\"ST\",\"SV\",\"CG\",\"CH\",\"CI\",\"SZ\",\"CK\",\"CL\",\"CM\",\"CO\",\"TC\",\"CR\",\"TD\",\"TF\",\"TG\",\"CV\",\"TH\",\"CX\",\"CY\",\"CZ\",\"TK\",\"TO\",\"SGS\",\"TR\",\"JEY\",\"TT\",\"DE\",\"TV\",\"TW\",\"TZ\",\"DJ\",\"DK\",\"DM\",\"DO\",\"DZ\",\"UM\",\"US\",\"EE\",\"EG\",\"EH\",\"ER\",\"VC\",\"ES\",\"ET\",\"VG\",\"VI\",\"VN\",\"VU\",\"FI\",\"FJ\",\"MNE\",\"FK\",\"FM\",\"FO\",\"FR\",\"WF\",\"GA\",\"TLS\",\"WS\",\"GD\",\"GE\",\"GF\",\"GH\",\"GI\",\"GL\",\"GM\",\"GN\",\"GP\",\"GQ\",\"GR\",\"GT\",\"GU\",\"GW\",\"GY\",\"HK\",\"HM\",\"HN\",\"HR\",\"HT\",\"YE\",\"HU\",\"ID\",\"IE\",\"IL\",\"IO\",\"IS\",\"IT\",\"ZM\",\"ZR\",\"EAZ\",\"BLM\",\"JM\",\"JO\",\"JP\",\"KE\",\"KG\",\"KH\",\"KI\",\"KN\",\"KR\",\"KY\",\"KZ\",\"LA\",\"LC\",\"GGY\",\"LI\",\"LK\",\"LR\",\"LS\",\"LT\",\"LU\",\"LV\",\"MA\",\"MD\",\"MG\",\"MH\",\"ML\",\"MM\",\"MN\",\"MP\",\"MQ\",\"MR\",\"MS\",\"MT\",\"MV\",\"MW\",\"MX\",\"MY\",\"MZ\",\"NA\",\"NC\",\"NE\",\"NF\",\"NG\",\"NI\",\"NL\",\"NO\",\"SRB\",\"NR\",\"NU\",\"NZ\",\"OM\",\"PA\",\"PE\",\"PF\",\"PG\",\"PH\",\"PK\",\"PL\",\"PM\",\"PN\"],\"ship_to\":[\"PT\",\"RW\",\"GBA\",\"UA\",\"UG\",\"UK\",\"BY\",\"SJ\",\"MK\",\"MO\",\"IM\",\"KP\",\"SX\",\"UZ\",\"SY\",\"IQ\",\"IR\",\"VA\",\"KW\",\"AR\",\"LB\",\"ALA\",\"AZ\",\"TN\",\"RO\"]},\"ES\":{\"free_ship_to\":[\"DE\",\"FI\",\"BE\",\"PT\",\"BG\",\"DK\",\"LT\",\"LU\",\"LV\",\"HR\",\"FR\",\"HU\",\"SE\",\"SI\",\"MC\",\"UK\",\"SK\",\"IE\",\"EE\",\"GR\",\"IT\",\"ES\",\"AT\",\"CY\",\"CZ\",\"PL\",\"RO\",\"NL\"],\"ship_to\":[]}}"}, "12": {"valueStart": null, "syncVersion": null, "childExtendMap": {}, "valueEnd": null, "version": 0, "key": "12", "content": "payment_success_deduct"}, "measure": {"valueStart": null, "syncVersion": null, "childExtendMap": {}, "valueEnd": null, "version": 0, "key": "measure", "content": "H251f297e9e13462fa52ca2ff1ae5ad3fB.jpg:400x400,He620e4b342494140b4578bb95a16946a4.jpeg:610x609"}, "msr_eu_id": {"valueStart": null, "syncVersion": null, "childExtendMap": {}, "valueEnd": null, "version": 0, "key": "msr_eu_id", "content": "-2"}, "freight_for_search": {"valueStart": null, "syncVersion": null, "childExtendMap": {}, "valueEnd": null, "version": 2, "key": "freight_for_search", "content": "{\"CN\":{\"free_ship_to\":[\"PR\",\"PW\",\"QA\",\"AD\",\"AE\",\"AF\",\"AG\",\"AI\",\"AL\",\"AM\",\"AN\",\"AO\",\"AQ\",\"AS\",\"RE\",\"AT\",\"AU\",\"AW\",\"BA\",\"BB\",\"BD\",\"RU\",\"BE\",\"BF\",\"BG\",\"BH\",\"BI\",\"BJ\",\"BM\",\"BN\",\"BO\",\"SA\",\"BQ\",\"SB\",\"SC\",\"BR\",\"ASC\",\"BS\",\"SE\",\"BT\",\"BV\",\"SG\",\"SH\",\"BW\",\"SI\",\"SK\",\"BZ\",\"SL\",\"SM\",\"SN\",\"CA\",\"SR\",\"CC\",\"SS\",\"ST\",\"SV\",\"CG\",\"CH\",\"CI\",\"SZ\",\"CK\",\"CL\",\"CM\",\"CO\",\"TC\",\"CR\",\"TD\",\"TF\",\"TG\",\"CV\",\"TH\",\"CX\",\"CY\",\"TK\",\"CZ\",\"TO\",\"SGS\",\"TR\",\"JEY\",\"TT\",\"DE\",\"TV\",\"TW\",\"TZ\",\"DJ\",\"DK\",\"DM\",\"DO\",\"DZ\",\"UM\",\"US\",\"EE\",\"EG\",\"EH\",\"ER\",\"VC\",\"ES\",\"ET\",\"VG\",\"VI\",\"VN\",\"VU\",\"FI\",\"FJ\",\"FK\",\"MNE\",\"FM\",\"FO\",\"FR\",\"WF\",\"GA\",\"TLS\",\"WS\",\"GD\",\"GE\",\"GF\",\"GH\",\"GI\",\"GL\",\"GM\",\"GN\",\"GP\",\"GQ\",\"GR\",\"GT\",\"GU\",\"GW\",\"GY\",\"HK\",\"HM\",\"HN\",\"HR\",\"YE\",\"HT\",\"HU\",\"ID\",\"IE\",\"IL\",\"IO\",\"IS\",\"IT\",\"ZM\",\"ZR\",\"EAZ\",\"BLM\",\"JM\",\"JO\",\"JP\",\"KE\",\"KG\",\"KH\",\"KI\",\"KN\",\"KR\",\"KY\",\"KZ\",\"LA\",\"LC\",\"GGY\",\"LI\",\"LK\",\"LR\",\"LS\",\"LT\",\"LU\",\"LV\",\"MA\",\"MD\",\"MG\",\"MH\",\"ML\",\"MM\",\"MN\",\"MP\",\"MQ\",\"MR\",\"MS\",\"MT\",\"MV\",\"MW\",\"MX\",\"MY\",\"MZ\",\"NA\",\"NC\",\"NE\",\"NF\",\"NG\",\"NI\",\"NL\",\"NO\",\"SRB\",\"NR\",\"NU\",\"NZ\",\"OM\",\"PA\",\"PE\",\"PF\",\"PG\",\"PH\",\"PK\",\"PL\",\"PM\",\"PN\"],\"ship_to\":[\"PT\",\"IM\",\"IQ\",\"IR\",\"AR\",\"AZ\",\"RO\",\"RW\",\"BY\",\"SJ\",\"SX\",\"KP\",\"SY\",\"KW\",\"ALA\",\"LB\",\"TN\",\"UA\",\"UG\",\"UK\",\"MK\",\"MO\",\"UZ\",\"VA\",\"GBA\"]},\"ES\":{\"free_ship_to\":[\"DE\",\"FI\",\"BE\",\"PT\",\"BG\",\"DK\",\"LT\",\"LU\",\"LV\",\"HR\",\"FR\",\"HU\",\"SE\",\"SI\",\"MC\",\"UK\",\"SK\",\"IE\",\"EE\",\"GR\",\"IT\",\"ES\",\"AT\",\"CY\",\"CZ\",\"PL\",\"RO\",\"NL\"],\"ship_to\":[]}}"}, "iqc": {"valueStart": null, "syncVersion": null, "childExtendMap": {}, "valueEnd": null, "version": 0, "key": "iqc", "content": "{}"}, "min_price_oversea_free": {"valueStart": null, "syncVersion": null, "childExtendMap": {}, "valueEnd": null, "version": 1, "key": "min_price_oversea_free", "content": "{\"CN\":{\"free_ship_to\":[\"PR\",\"PW\",\"QA\",\"AD\",\"AE\",\"AF\",\"AG\",\"AI\",\"AL\",\"AM\",\"AN\",\"AO\",\"AQ\",\"AS\",\"AT\",\"RE\",\"AU\",\"AW\",\"BA\",\"BB\",\"BD\",\"RU\",\"BE\",\"BF\",\"BG\",\"BH\",\"BI\",\"BJ\",\"BM\",\"BN\",\"BO\",\"SA\",\"SB\",\"BQ\",\"SC\",\"BR\",\"BS\",\"ASC\",\"SE\",\"BT\",\"SG\",\"BV\",\"BW\",\"SH\",\"SI\",\"SK\",\"BZ\",\"SL\",\"SM\",\"SN\",\"CA\",\"SR\",\"SS\",\"CC\",\"ST\",\"SV\",\"CG\",\"CH\",\"CI\",\"SZ\",\"CK\",\"CL\",\"CM\",\"CO\",\"TC\",\"CR\",\"TD\",\"TF\",\"TG\",\"CV\",\"TH\",\"CX\",\"CY\",\"CZ\",\"TK\",\"TO\",\"SGS\",\"TR\",\"JEY\",\"TT\",\"DE\",\"TV\",\"TW\",\"TZ\",\"DJ\",\"DK\",\"DM\",\"DO\",\"DZ\",\"UM\",\"US\",\"EE\",\"EG\",\"EH\",\"ER\",\"VC\",\"ES\",\"ET\",\"VG\",\"VI\",\"VN\",\"VU\",\"FI\",\"FJ\",\"MNE\",\"FK\",\"FM\",\"FO\",\"FR\",\"WF\",\"GA\",\"TLS\",\"WS\",\"GD\",\"GE\",\"GF\",\"GH\",\"GI\",\"GL\",\"GM\",\"GN\",\"GP\",\"GQ\",\"GR\",\"GT\",\"GU\",\"GW\",\"GY\",\"HK\",\"HM\",\"HN\",\"HR\",\"HT\",\"YE\",\"HU\",\"ID\",\"IE\",\"IL\",\"IO\",\"IS\",\"IT\",\"ZM\",\"ZR\",\"EAZ\",\"BLM\",\"JM\",\"JO\",\"JP\",\"KE\",\"KG\",\"KH\",\"KI\",\"KN\",\"KR\",\"KY\",\"KZ\",\"LA\",\"LC\",\"GGY\",\"LI\",\"LK\",\"LR\",\"LS\",\"LT\",\"LU\",\"LV\",\"MA\",\"MD\",\"MG\",\"MH\",\"ML\",\"MM\",\"MN\",\"MP\",\"MQ\",\"MR\",\"MS\",\"MT\",\"MV\",\"MW\",\"MX\",\"MY\",\"MZ\",\"NA\",\"NC\",\"NE\",\"NF\",\"NG\",\"NI\",\"NL\",\"NO\",\"SRB\",\"NR\",\"NU\",\"NZ\",\"OM\",\"PA\",\"PE\",\"PF\",\"PG\",\"PH\",\"PK\",\"PL\",\"PM\",\"PN\"],\"ship_to\":[\"PT\",\"RW\",\"GBA\",\"UA\",\"UG\",\"UK\",\"BY\",\"SJ\",\"MK\",\"MO\",\"IM\",\"KP\",\"SX\",\"UZ\",\"SY\",\"IQ\",\"IR\",\"VA\",\"KW\",\"AR\",\"LB\",\"ALA\",\"AZ\",\"TN\",\"RO\"]},\"ES\":{\"free_ship_to\":[\"DE\",\"FI\",\"BE\",\"PT\",\"BG\",\"DK\",\"LT\",\"LU\",\"LV\",\"HR\",\"FR\",\"HU\",\"SE\",\"SI\",\"MC\",\"UK\",\"SK\",\"IE\",\"EE\",\"GR\",\"IT\",\"ES\",\"AT\",\"CY\",\"CZ\",\"PL\",\"RO\",\"NL\"],\"ship_to\":[]}}"}, "multi_dim_tags": {"valueStart": null, "syncVersion": null, "childExtendMap": {}, "valueEnd": null, "version": 0, "key": "multi_dim_tags", "content": "[{\"value\":82052}]"}, "qualification_config": {"valueStart": null, "syncVersion": null, "childExtendMap": {}, "valueEnd": null, "version": 0, "key": "qualification_config", "content": "[{\"key\":\"Package_Label_Photo\",\"type\":\"image\"},{\"key\":\"item_EU_CE_certificate\",\"type\":\"image\"}]"}, "national_quote_config_type": {"valueStart": null, "syncVersion": null, "childExtendMap": {}, "valueEnd": null, "version": 9, "key": "national_quote_config_type", "content": "absolute"}, "tags": {"valueStart": null, "syncVersion": null, "childExtendMap": {}, "valueEnd": null, "version": 0, "key": "tags", "content": "82052"}}, "sample": null, "liveUp": null, "modifyTimeMillis": 1618912364530, "service": null, "deliveryOptionBlackList": null, "tenantId": "AE_GLOBAL", "auditStatus": 1}], "failedSingleQueryConditions": []}, "rpcContext": null, "serviceName": "com.alibaba.global.ic.api.CustomerProductServiceFacade", "showIdentity": "com.alibaba.global.ic.api.CustomerProductServiceFacade@queryProduct(ProductQueryRequest)", "showInvokeIdentity": null, "stackTrace": "com.alibaba.global.satellite.proxy.hsf.utils.hook.SatelliteHSFConsumerProxyHook.invoke@92\ncom.alibaba.global.satellite.proxy.hsf.utils.hook.SatelliteHSFConsumerProxyHook.invoke@29\ncom.alibaba.global.satellite.utils.SentinelUtils.invoke@37\ncom.alibaba.global.satellite.proxy.hsf.utils.hook.SatelliteHSFConsumerProxyHook.proxy@58\ncom.alibaba.global.common.proxy.AbstractProxyHandler.invoke@63\ncom.alibaba.global.common.proxy.JdkProxy$Handler.invoke@66\ncom.alibaba.trade.function.product.repository.ProductRepositoryImpl.queryProducts@157\ncom.alibaba.trade.function.product.repository.ProductRepositoryImpl.queryProductsAndBuildDeleteIds@119\ncom.alibaba.trade.function.product.repository.ProductRepositoryImpl.batchQueryProducts@110\ncom.alibaba.trade.function.product.repository.ProductRepositoryImpl.query@51\ncom.alibaba.trade.scenario.build.common.BaseProductBuilder.build@38\ncom.alibaba.trade.scenario.build.RequestBuilderFactory.lambda$executeAll$1@56\ncom.alibaba.trade.scenario.build.RequestBuilderFactory.executeAll@56\ncom.alibaba.global.buy.request.ShoppingRequest.createShoppingRequest@108\ncom.alibaba.global.buy.request.ShoppingRequest.of@90\ncom.alibaba.global.buy.api.facade.protocol.GlobalCheckoutFacadeImpl$1._execute@81\ncom.alibaba.global.buy.api.facade.protocol.GlobalCheckoutFacadeImpl$1._execute@69\ncom.alibaba.tmf.session.OnceInvoker.invoke@42\ncom.alibaba.global.buy.api.facade.protocol.GlobalCheckoutFacadeImpl.renderOrder@102\ncom.alibaba.tmf.scenario.ServiceEntranceAspect.doAround@39\n", "startTime": "26120001155289711", "subInvokeType": "rpc", "throwable": null, "timeCost": "6.122ms", "type": "JAVA"}], "com.alibaba.global.ump.protocol.sdk.facade.GlobalTradePromotionMultiCalculateFacade@calculateItemPromotion4Checkout(GlobalTradeMultiCalculateRequest)": [{"afterExecuteParams": null, "endTime": "26120001871919468", "exceptionStackTrace": null, "extInvokeIdentity": null, "extraPrams": null, "fuzzyInvokeIdentity": null, "identity": "com.alibaba.global.ump.protocol.sdk.facade.GlobalTradePromotionMultiCalculateFacade@calculateItemPromotion4Checkout(GlobalTradeMultiCalculateRequest)", "index": 24, "isSerialized": null, "method": "calculateItemPromotion4Checkout", "params": [{"fulfillmentItemClusters": [{"receiverAddress": {"locationTreeAddressName": "Abruzzo,Chieti", "addressType": "HOME", "locationTreeAddressId": "910100010000000000-910100010001000000", "countryISOCode": "IT", "latitude": null, "addressTag": "local", "detailAddress": "<PERSON><PERSON>", "longitude": null}, "seller": {"baseInfo": {"gender": null, "userTags": null, "idNumber": null}, "userId": *********, "email": "<EMAIL>"}, "fulfillmentItems": [{"shippingFees": [{"targetId": "992d1d4ca035593a870320d8d97abf0e", "price": {"amount": 0, "currencyCode": "USD"}}], "quantity": 1, "targetIds": null, "price": null, "packageId": "A0ca5f76b-43d3-4926-b90b-95f404d6009f", "fulfillmentService": {"serviceCode": "CAINIAO_STANDARD"}, "leadingTime": 0, "warehouse": {"locationTreeAddressId": null, "type": "dropshipping"}, "selected": true}]}], "buyInfo": {"defaultReceiverAddress": {"locationTreeAddressName": "Abruzzo,Chieti", "addressType": "HOME", "locationTreeAddressId": "910100010000000000-910100010001000000", "countryISOCode": "IT", "latitude": null, "addressTag": "local", "detailAddress": "<PERSON><PERSON>", "longitude": null}, "interactionInfo": {"selectedPromotions": [], "notUsePromotionTools": []}, "sellerPromotionCodes": null, "platformPromotionCode": null}, "sourceInfo": {"systemInfo": {"appVersion": "255", "osVersion": null, "appName": "", "osType": "IOS", "platformType": "NATIVE", "appKey": "21371581", "ttid": "201200@Aliexpress_iphone_8.16.0"}, "networkInfo": {"wua": "ARdT_VFcHFYafHHLicsAEoeQUUv9HchiD+ny4pvnH9w/Kxd/EdL59+5HZdZwdsUBfO1RRL/bJxPsnKtplGEV4nNRkRzV5YZ6xUCmy/zdZTe0ReoK8d/7PY8MQ3GOkz7lx32NcsbvlN2BEHkXniRnXZyB2VJN9iqH0F6fjgNcbSrz3WzvTIod9FTIOEbzSIORYwW9nGAHFkcvlOnZv2wEMvlDvu94TI5XhBFpBdCAs0bCtLQQgz95W9+e2YrieNHozG7x88iYqeVvFfRTHkht0aYvXs4782MGuWK5Am3p/xbjVPGl8YveoposVOJjjKEChzJzaTdMuZ6C/HnH1j0A6VmuNIsmAHkk2OZGoHTVZkjEHIjC3S6Lx8DEK2WRou7MYm05n", "clientIp": "*************", "cookieId": "YIeGkaCk/4EDAHxDYnmBQkxK", "userAgent": null, "sessionId": null, "umidToken": "2ztLfkFLOljE8jV5FxCtVImGSy9U2Qme", "ua": "ARdT_VFcHFYafHHLicsAEoeQUUv9HchiD+ny4pvnH9w/Kxd/EdL59+5HZdZwdsUBfO1RRL/bJxPsnKtplGEV4nNRkRzV5YZ6xUCmy/zdZTe0ReoK8d/7PY8MQ3GOkz7lx32NcsbvlN2BEHkXniRnXZyB2VJN9iqH0F6fjgNcbSrz3WzvTIod9FTIOEbzSIORYwW9nGAHFkcvlOnZv2wEMvlDvu94TI5XhBFpBdCAs0bCtLQQgz95W9+e2YrieNHozG7x88iYqeVvFfRTHkht0aYvXs4782MGuWK5Am3p/xbjVPGl8YveoposVOJjjKEChzJzaTdMuZ6C/HnH1j0A6VmuNIsmAHkk2OZGoHTVZkjEHIjC3S6Lx8DEK2WRou7MYm05n", "miniWua": "ARdT_VFcHFYafHHLicsAEoeQUUv9HchiD+ny4pvnH9w/Kxd/EdL59+5HZdZwdsUBfO1RRL/bJxPsnKtplGEV4nNRkRzV5YZ6xUCmy/zdZTe0ReoK8d/7PY8MQ3GOkz7lx32NcsbvlN2BEHkXniRnXZyB2VJN9iqH0F6fjgNcbSrz3WzvTIod9FTIOEbzSIORYwW9nGAHFkcvlOnZv2wEMvlDvu94TI5XhBFpBdCAs0bCtLQQgz95W9+e2YrieNHozG7x88iYqeVvFfRTHkht0aYvXs4782MGuWK5Am3p/xbjVPGl8YveoposVOJjjKEChzJzaTdMuZ6C/HnH1j0A6VmuNIsmAHkk2OZGoHTVZkjEHIjC3S6Lx8DEK2WRou7MYm05n"}, "extraParams": {}, "deviceInfo": {"deviceType": "MOBILE", "name": null, "id": "x86_64"}, "localeInfo": {"countryCode": "US", "language": "en", "currency": null, "locale": {"country": "en", "variant": "", "language": "en"}}}, "extendParam": {"extensionDTOMap": {"/fulfillmentItemClusters/fulfillmentItems/$A0ca5f76b-43d3-4926-b90b-95f404d6009f#CAINIAO_STANDARD": {"serviceCode": "CAINIAO_STANDARD", "packageId": "A0ca5f76b-43d3-4926-b90b-95f404d6009f", "attributes": {}, "uniqueId": "A0ca5f76b-43d3-4926-b90b-95f404d6009f#CAINIAO_STANDARD"}, "/tradeItemClusters/tradeItems/skus/$992d1d4ca035593a870320d8d97abf0e": {"targetId": "992d1d4ca035593a870320d8d97abf0e", "priceTag": 2, "attributes": {}, "priceDetailDTO": {"originalPriceTaxFee": {"amount": 11100, "currencyCode": "USD"}, "postageCostTaxFeeAmountTargetId": null, "postageCostAmount": null, "originalPriceTaxFeeTargetId": "tax_origin_prex_992d1d4ca035593a870320d8d97abf0e", "salePriceTaxFee": {"amount": 10700, "currencyCode": "USD"}, "salePriceTaxFeeTargetId": "tax_sale_prex_992d1d4ca035593a870320d8d97abf0e", "postageCostAmountTargetId": null, "postageCostTaxFeeAmount": null}}, "/buyer": {"attributes": {"drop_shipper_advanced_info": null, "buyer_tax_info": null, "gbrain_crowd": null, "ae_newuser": "-2"}}, "/": {"attributes": {"saasRegion": null}, "adjustPriceInfo": null, "tradeOrders": null}}}, "routingInfo": {"sellerId": null, "buyerId": null}, "invokeInfo": {"appName": null}, "exchangeRates": [{"quoteCurrency": "USD", "exchangeRate": 1, "intentionCurrency": "USD"}], "tradeItemClusters": [{"seller": {"baseInfo": {"gender": null, "userTags": null, "idNumber": null}, "userId": *********, "email": "<EMAIL>"}, "tradeItems": [{"virtual": false, "quantity": 1, "skus": [{"quantity": 1, "targetIds": ["992d1d4ca035593a870320d8d97abf0e"], "price": {"amount": 69597, "currencyCode": "USD"}, "inventoryChannelInfos": null, "prices": {"salePrice": {"amount": 67089, "currencyCode": "USD"}, "retailPrice": {"amount": 69597, "currencyCode": "USD"}}, "skuTags": [82052, 110572], "skuId": "*****************", "selected": true}], "itemTags": [82052], "channelInfo": null, "targetIds": ["992d1d4ca035593a870320d8d97abf0e"], "saleable": true, "categoryInfo": {"categoryIds": [5090301, 509], "leafCategoryId": 5090301, "rootCategoryId": 509}, "itemId": ****************, "price": {"amount": 69597, "currencyCode": "USD"}, "brandId": 200658765, "shardingId": null, "selected": true}], "store": null}], "itemRelations": [], "buyer": {"baseInfo": null, "userMemberInfos": [{"memberLevel": 0, "domain": null, "startTime": 0, "endTime": 0, "memberTags": null}], "userId": **********, "email": "<EMAIL>"}}], "paramsInstType": null, "paramsType": ["com.alibaba.global.ump.protocol.sdk.model.request.GlobalTradeMultiCalculateRequest"], "relatedCollectIndex": 0, "response": {"result": {"features": {"kernel": "1"}, "itemPromotions": [{"promotionResults": [], "features": {}, "displayResults": [{"features": {"quoteDiscount": 1342, "promotionSubScopeMap": "{\"orderLineId2SubDetailIdList\":{\"992d1d4ca035593a870320d8d97abf0e\":[\"originalPrice_992d1d4ca035593a870320d8d97abf0e\",\"tax_origin_prex_992d1d4ca035593a870320d8d97abf0e\"]}}", "ae_site_channel": "500000001213073", "marketPlace": "ALIEXPRESS", "ae_payTimeLimit": "86400000", "srcApp": "global-campaign-merchants-base-s", "b_participateType": "1", "intentionDiscount": 1342, "b_activityScope": "1", "participateType": "1", "consKey": "true", "calcM": "1", "sku": "*****************", "b_campaignLevel": "6"}, "displayType": "TEASING", "promotionTips": [], "budgets": [], "itemRelation": null, "promotionScope": {"targetIds": ["992d1d4ca035593a870320d8d97abf0e"]}, "inventoryChannelInfo": {"channelStock": null, "selected": false, "channelCode": "500000001213073"}, "promotion": {"platformOwner": true, "toolCode": "proEngine", "promotionType": "ITEM", "sellerId": *********, "effectiveTime": {"teasingStartTime": 1619359200000, "startTime": 1619625600000, "endTime": 1620230400000}, "name": "ump预热测试", "params": {"p_discountType": "2", "exchangeRate": "USD(QUOTE)_USD(INTENTION)_1", "p_quoteCurrency": "USD", "buyLimitRuleType": "3", "aeNewFlow": "1", "p_calculateCurrency": "USD", "p_discountRate_off": "20", "buyLimitRuleName": "lmtBuyerActivityItem", "p_intentionCurrency": "USD"}, "promotionItemType": "TRADE_ITEM", "version": 1, "promotionUniqueId": {"activityId": 1907423001, "promGroupType": 1, "creatorId": 1, "promotionCode": null, "idValue": "proEngine-1907423001_5000007562825014", "promotionId": 5000007562825014, "promGroupId": "992d1d4ca035593a870320d8d97abf0e"}}}], "groupId": "992d1d4ca035593a870320d8d97abf0e"}, {"promotionResults": [{"features": {}, "promotionResultDetails": [{"features": {"promotionOrderLine2NumMap": "{\"992d1d4ca035593a870320d8d97abf0e\":1}", "promotionSubScopeMap": "{\"orderLineId2SubDetailIdList\":{\"992d1d4ca035593a870320d8d97abf0e\":[\"originalPrice_992d1d4ca035593a870320d8d97abf0e\",\"tax_origin_prex_992d1d4ca035593a870320d8d97abf0e\"]}}", "isPromotionUniqueIdDowngraded": "true", "calcM": "1", "originPromotionId": 1, "umpMockTargetShareMoneyMap": "{\"tax_origin_prex_992d1d4ca035593a870320d8d97abf0e\":\"{\\\"currency\\\":\\\"USD\\\",\\\"price\\\":400}\",\"originalPrice_992d1d4ca035593a870320d8d97abf0e\":\"{\\\"currency\\\":\\\"USD\\\",\\\"price\\\":2108}\"}"}, "budgets": [], "priceResult": {"intentionSponsorDetails": [{"targetId": "992d1d4ca035593a870320d8d97abf0e", "discounts": [{"amount": {"amount": 2508, "currencyCode": "USD"}, "sponsorType": "SELLER"}]}], "discount": {"sharedAmounts": [{"amount": {"amount": 2508, "currencyCode": "USD"}, "targetId": "992d1d4ca035593a870320d8d97abf0e"}], "platformSponsoredSharedAmounts": []}, "sponsorDetails": [{"targetId": "992d1d4ca035593a870320d8d97abf0e", "discounts": [{"amount": {"amount": 2508, "currencyCode": "USD"}, "sponsorType": "SELLER"}]}], "intentionDiscount": {"sharedAmounts": [{"amount": {"amount": 2508, "currencyCode": "USD"}, "targetId": "992d1d4ca035593a870320d8d97abf0e"}], "platformSponsoredSharedAmounts": []}}, "benefitResult": null, "itemRelation": null, "promotionScope": {"targetIds": ["992d1d4ca035593a870320d8d97abf0e"]}, "inventoryChannelInfo": null, "promotion": {"platformOwner": true, "toolCode": "mockCoveredShippingSalePrice", "promotionType": "ITEM", "sellerId": 1, "effectiveTime": {"teasingStartTime": -1, "startTime": 1619609661551, "endTime": 1619616861551}, "name": "mockCoveredShippingSalePrice", "params": {"p_currencyConfig": "{\"default\":\"USD\",\"USD\":{\"p_decreaseMoney\":\"2508\"}}", "p_activityCurrency": "USD", "exchangeRate": "USD(QUOTE)_USD(INTENTION)_1,USD(QUOTE)_USD(ACTIVITY)_1", "p_quoteCurrency": "USD", "aeNewFlow": "1", "p_calculateCurrency": "USD", "p_decreaseMoney": "2508", "p_intentionCurrency": "USD"}, "promotionItemType": "TRADE_ITEM", "version": 0, "promotionUniqueId": {"activityId": 1, "promGroupType": 2, "creatorId": 1, "promotionCode": null, "idValue": "mockCoveredShippingSalePrice-1_1", "promotionId": 52, "promGroupId": "s_*********_240"}}}], "selected": true}], "features": {}, "displayResults": [], "groupId": "s_*********_240"}], "relationPromotions": []}, "success": true, "errorCode": null}, "rpcContext": null, "serviceName": "com.alibaba.global.ump.protocol.sdk.facade.GlobalTradePromotionMultiCalculateFacade", "showIdentity": "com.alibaba.global.ump.protocol.sdk.facade.GlobalTradePromotionMultiCalculateFacade@calculateItemPromotion4Checkout(GlobalTradeMultiCalculateRequest)", "showInvokeIdentity": null, "stackTrace": "com.alibaba.global.satellite.proxy.hsf.utils.hook.SatelliteHSFConsumerProxyHook.invoke@92\ncom.alibaba.global.satellite.proxy.hsf.utils.hook.SatelliteHSFConsumerProxyHook.invoke@29\ncom.alibaba.global.satellite.utils.SentinelUtils.invoke@37\ncom.alibaba.global.satellite.proxy.hsf.utils.hook.SatelliteHSFConsumerProxyHook.proxy@58\ncom.alibaba.global.common.proxy.AbstractProxyHandler.invoke@63\ncom.alibaba.global.common.proxy.JdkProxy$Handler.invoke@66\ncom.alibaba.trade.function.promotion.repository.protocol.GlobalMultiTradePromotionRepositoryImpl.invokeCheckoutQuery@143\ncom.alibaba.trade.function.promotion.repository.protocol.GlobalMultiTradePromotionRepositoryImpl.queryItemPromotion@74\ncom.alibaba.trade.function.promotion.repository.PromotionRepositoryImpl.queryItemPromotion@109\ncom.alibaba.trade.function.promotion.service.GlobalTradePromotionQueryDomainServiceImpl.query@70\ncom.alibaba.global.buy.activities.promotion.PromotionQueryActivity.execute@63\ncom.alibaba.smart.framework.engine.modules.smart.provider.performer.JavaPerformer.perform@56\ncom.alibaba.smart.framework.engine.provider.impl.ComboInvoker.invoke@27\ncom.alibaba.smart.framework.engine.pvm.impl.AbstractPvmElement.invoke@47\ncom.alibaba.smart.framework.engine.provider.impl.DefaultExecutePolicyBehavior.execute@63\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmActivity.execute@59\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmActivity.enter@53\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmTransition.execute@43\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmActivity.executeRecursively@103\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmActivity.execute@68\n", "startTime": "26120001562905938", "subInvokeType": "rpc", "throwable": null, "timeCost": "309.014ms", "type": "JAVA"}], "com.taobao.payment.exchangeplatform.client.api.ExchangeQueryService@queryExchangeRateFromCache(List,String,String)": [{"afterExecuteParams": null, "endTime": "26120001550547535", "exceptionStackTrace": null, "extInvokeIdentity": null, "extraPrams": null, "fuzzyInvokeIdentity": null, "identity": "com.taobao.payment.exchangeplatform.client.api.ExchangeQueryService@queryExchangeRateFromCache(List,String,String)", "index": 19, "isSerialized": null, "method": "queryExchangeRateFromCache", "params": [["ae_common_insured_rate", "oanda_rate"], "USD", "USD"], "paramsInstType": null, "paramsType": ["java.util.List", "java.lang.String", "java.lang.String"], "relatedCollectIndex": 0, "response": {"rateInfo": {"quoteCurrency": "USD", "instExchangeRateNo": null, "expireTime": null, "thresholdTime": null, "rate": 1, "tradable": true, "validTime": null, "baseCurrency": "USD", "exchangeRateNo": null}, "responseMessage": null, "succeeded": true, "responseCode": null, "extraInfo": null}, "rpcContext": null, "serviceName": "com.taobao.payment.exchangeplatform.client.api.ExchangeQueryService", "showIdentity": "com.taobao.payment.exchangeplatform.client.api.ExchangeQueryService@queryExchangeRateFromCache(List,String,String)", "showInvokeIdentity": null, "stackTrace": "com.taobao.payment.exchangeplatform.client.api.impl.ExchangeQueryServiceImpl.queryExchangeRateFromCache@-1\ncom.alibaba.ecommerce.exchange.facade.impl.GlobalExchangeFacadeImpl.getGlobalRateByAgreeNoList@123\ncom.alibaba.ecommerce.exchange.facade.impl.GlobalExchangeFacadeImpl.getExchangeRate@80\ncom.alibaba.global.buy.activities.price.ExchangeInitActivity.lambda$execute$2@140\ncom.alibaba.global.buy.activities.price.ExchangeInitActivity.execute@136\ncom.alibaba.smart.framework.engine.modules.smart.provider.performer.JavaPerformer.perform@56\ncom.alibaba.smart.framework.engine.provider.impl.ComboInvoker.invoke@27\ncom.alibaba.smart.framework.engine.pvm.impl.AbstractPvmElement.invoke@47\ncom.alibaba.smart.framework.engine.provider.impl.DefaultExecutePolicyBehavior.execute@63\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmActivity.execute@59\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmActivity.enter@53\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmTransition.execute@43\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmActivity.executeRecursively@103\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmActivity.execute@68\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmActivity.enter@53\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmTransition.execute@43\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmActivity.executeRecursively@103\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmActivity.execute@68\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmActivity.enter@53\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmTransition.execute@43\n", "startTime": "26120001550236082", "subInvokeType": null, "throwable": null, "timeCost": "0.311ms", "type": "JAVA"}], "com.alibaba.intl.ae.biz.promisetemplate.share.service.interfaces.PromiseTemplateService@calculatePromiseResultsByProductList(List)": [{"afterExecuteParams": null, "endTime": "26120001256548048", "exceptionStackTrace": null, "extInvokeIdentity": null, "extraPrams": null, "fuzzyInvokeIdentity": null, "identity": "com.alibaba.intl.ae.biz.promisetemplate.share.service.interfaces.PromiseTemplateService@calculatePromiseResultsByProductList(List)", "index": 17, "isSerialized": null, "method": "calculatePromiseResultsByProductList", "params": [[{"aliId": *********, "productId": ****************, "calculateParaMeter": {"buyerAliMemberId": "**********", "sellerCountry": "CN", "productSrc": "post", "resultKey": "81007272905415", "locale": "en_US", "countryTo": "IT", "sellerInsuranceTag": "1", "cateId": "5090301", "selectPromiseInstance": "SELECTALL", "sellerAliMemberId": "*********", "isLocalProduct": "false", "sellerTag": "CN_USD", "productPrice": "500", "itemCondition": "360543", "productTags": "82052"}}]], "paramsInstType": null, "paramsType": ["java.util.List"], "relatedCollectIndex": 0, "response": {"81007272905415": {"valuablePromiseDetailList": [{"brief": null, "detailDescription": null, "oldPromiseId": null, "description": "basicwarranty_ship_to_IT_desc@3c_guarantte", "type": 7, "iconStyleForDetail": null, "iconStyle": null, "descriptionForSeller": null, "isValuable": true, "descriptionPretty": null, "iconStylePretty": null, "iconStyleForSeller": null, "name": "basicwarranty_ship_to_IT_name@3c_guarantte", "promiseInstanceId": "10-27", "valueMap": {"k1": "0.045", "k2": "5", "k3": "IT", "k4": "2", "k5": "0", "k6": "1"}, "id": 10, "iconUrl": null, "imgSrc": null}], "name": null, "id": null, "promiseDetailList": [{"brief": "returns1_bp@content", "detailDescription": "returns_details1_bp_tab@content ", "oldPromiseId": "2", "description": "returns1_bp@content", "type": 1, "iconStyleForDetail": null, "iconStyle": null, "descriptionForSeller": "returns1seller_bp@content ", "isValuable": false, "descriptionPretty": "returns1_bp@content  ", "iconStylePretty": null, "iconStyleForSeller": null, "name": "servicename@return", "promiseInstanceId": null, "valueMap": {}, "id": 2, "iconUrl": null, "imgSrc": null}]}}, "rpcContext": null, "serviceName": "com.alibaba.intl.ae.biz.promisetemplate.share.service.interfaces.PromiseTemplateService", "showIdentity": "com.alibaba.intl.ae.biz.promisetemplate.share.service.interfaces.PromiseTemplateService@calculatePromiseResultsByProductList(List)", "showInvokeIdentity": null, "stackTrace": "com.alibaba.global.satellite.proxy.hsf.utils.hook.SatelliteHSFConsumerProxyHook.invoke@92\ncom.alibaba.global.satellite.proxy.hsf.utils.hook.SatelliteHSFConsumerProxyHook.invoke@29\ncom.alibaba.global.satellite.utils.SentinelUtils.invoke@37\ncom.alibaba.global.satellite.proxy.hsf.utils.hook.SatelliteHSFConsumerProxyHook.proxy@58\ncom.alibaba.global.common.proxy.AbstractProxyHandler.invoke@63\ncom.alibaba.global.common.proxy.JdkProxy$Handler.invoke@66\ncom.alibaba.trade.function.aftersale.repository.AfterSaleRepositoryImpl.getWarrantyServices@228\ncom.alibaba.trade.function.aftersale.repository.AfterSaleRepositoryImpl.queryProductServiceFee@81\ncom.alibaba.trade.function.aftersale.ability.DefaultCustomerRightsAbility.enrichAfterSale@84\ncom.alibaba.trade.function.aftersale.service.TradeAfterSaleDomainServiceImpl.queryProductServiceFee@25\ncom.alibaba.trade.scenario.activity.shopping.aftersale.ShoppingAfterSaleQueryActivity.callDomainService@35\ncom.alibaba.trade.scenario.activity.shopping.aftersale.ShoppingAfterSaleQueryActivity.callDomainService@19\ncom.alibaba.trade.scenario.activity.BaseTradeActivity.execute@37\ncom.alibaba.global.buy.activities.aftersale.ServiceQueryActivity.execute@35\ncom.alibaba.smart.framework.engine.modules.smart.provider.performer.JavaPerformer.perform@56\ncom.alibaba.smart.framework.engine.provider.impl.ComboInvoker.invoke@27\ncom.alibaba.smart.framework.engine.pvm.impl.AbstractPvmElement.invoke@47\ncom.alibaba.smart.framework.engine.provider.impl.DefaultExecutePolicyBehavior.execute@63\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmActivity.execute@59\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmActivity.enter@53\n", "startTime": "26120001249110618", "subInvokeType": "rpc", "throwable": null, "timeCost": "7.437ms", "type": "JAVA"}], "com.alibaba.global.payment.api.facade.GlobalPaymentCashierServiceFacade@renderIntention(RenderIntentionRequest)": [{"afterExecuteParams": null, "endTime": "26120005069738142", "exceptionStackTrace": null, "extInvokeIdentity": null, "extraPrams": null, "fuzzyInvokeIdentity": null, "identity": "com.alibaba.global.payment.api.facade.GlobalPaymentCashierServiceFacade@renderIntention(RenderIntentionRequest)", "index": 32, "isSerialized": null, "method": "renderIntention", "params": [{"disableReasonCode": null, "cashierToken": null, "commonDTO": {"ultronVersion": null, "pageType": null, "mobile": {"appClientVersion": null, "isAndriod": false, "apiName": null, "appVersion": "255", "wua": "ARdT_VFcHFYafHHLicsAEoeQUUv9HchiD+ny4pvnH9w/Kxd/EdL59+5HZdZwdsUBfO1RRL/bJxPsnKtplGEV4nNRkRzV5YZ6xUCmy/zdZTe0ReoK8d/7PY8MQ3GOkz7lx32NcsbvlN2BEHkXniRnXZyB2VJN9iqH0F6fjgNcbSrz3WzvTIod9FTIOEbzSIORYwW9nGAHFkcvlOnZv2wEMvlDvu94TI5XhBFpBdCAs0bCtLQQgz95W9+e2YrieNHozG7x88iYqeVvFfRTHkht0aYvXs4782MGuWK5Am3p/xbjVPGl8YveoposVOJjjKEChzJzaTdMuZ6C/HnH1j0A6VmuNIsmAHkk2OZGoHTVZkjEHIjC3S6Lx8DEK2WRou7MYm05n", "osTypeEnum": null, "deviceOsVersion": null, "isAppAndroid": false, "ua": "ARdT_VFcHFYafHHLicsAEoeQUUv9HchiD+ny4pvnH9w/Kxd/EdL59+5HZdZwdsUBfO1RRL/bJxPsnKtplGEV4nNRkRzV5YZ6xUCmy/zdZTe0ReoK8d/7PY8MQ3GOkz7lx32NcsbvlN2BEHkXniRnXZyB2VJN9iqH0F6fjgNcbSrz3WzvTIod9FTIOEbzSIORYwW9nGAHFkcvlOnZv2wEMvlDvu94TI5XhBFpBdCAs0bCtLQQgz95W9+e2YrieNHozG7x88iYqeVvFfRTHkht0aYvXs4782MGuWK5Am3p/xbjVPGl8YveoposVOJjjKEChzJzaTdMuZ6C/HnH1j0A6VmuNIsmAHkk2OZGoHTVZkjEHIjC3S6Lx8DEK2WRou7MYm05n", "deviceId": null, "sid": null, "apiVersion": null, "feature": null, "sysEntrance": null, "appType": null, "appKey": "21371581", "isIpadIOS": false, "miniWua": "ARdT_VFcHFYafHHLicsAEoeQUUv9HchiD+ny4pvnH9w/Kxd/EdL59+5HZdZwdsUBfO1RRL/bJxPsnKtplGEV4nNRkRzV5YZ6xUCmy/zdZTe0ReoK8d/7PY8MQ3GOkz7lx32NcsbvlN2BEHkXniRnXZyB2VJN9iqH0F6fjgNcbSrz3WzvTIod9FTIOEbzSIORYwW9nGAHFkcvlOnZv2wEMvlDvu94TI5XhBFpBdCAs0bCtLQQgz95W9+e2YrieNHozG7x88iYqeVvFfRTHkht0aYvXs4782MGuWK5Am3p/xbjVPGl8YveoposVOJjjKEChzJzaTdMuZ6C/HnH1j0A6VmuNIsmAHkk2OZGoHTVZkjEHIjC3S6Lx8DEK2WRou7MYm05n", "appName": null, "isAppIOS": false, "deviceVersion": null, "ttid": "201200@Aliexpress_iphone_8.16.0", "isMsite": false, "serverEnvRaw": "", "deviceModel": null, "attributes": {}, "isIOS": false}, "siteId": null, "deviceDTO": {"userMacAddress": null, "appVersion": null, "latitude": null, "language": null, "osVersion": null, "longitude": null, "tokenId": null, "os": null, "webLanguage": null, "clientUmid": null, "mobile": null, "userAgent": null, "sessionId": null, "terminal": null, "accept": null, "terminalType": null, "clientUmidToken": null, "webSiteLanguage": null, "acceptLanguage": null, "operateEntrance": null, "userIpAddress": null, "clientIp": null, "verifyEnvData": null, "imei": null, "header": null, "deviceModel": null}, "extraParams": {}, "attributes": {}, "headerDTO": {"tokenId": null, "os": null, "lng": null, "sdkEnvData": null, "umid": null, "utdid": "ARdT_VFcHFYafHHLicsAEoeQUUv9HchiD+ny4pvnH9w/Kxd/EdL59+5HZdZwdsUBfO1RRL/bJxPsnKtplGEV4nNRkRzV5YZ6xUCmy/zdZTe0ReoK8d/7PY8MQ3GOkz7lx32NcsbvlN2BEHkXniRnXZyB2VJN9iqH0F6fjgNcbSrz3WzvTIod9FTIOEbzSIORYwW9nGAHFkcvlOnZv2wEMvlDvu94TI5XhBFpBdCAs0bCtLQQgz95W9+e2YrieNHozG7x88iYqeVvFfRTHkht0aYvXs4782MGuWK5Am3p/xbjVPGl8YveoposVOJjjKEChzJzaTdMuZ6C/HnH1j0A6VmuNIsmAHkk2OZGoHTVZkjEHIjC3S6Lx8DEK2WRou7MYm05n", "ip": "*************", "language": null, "umidToken": "2ztLfkFLOljE8jV5FxCtVImGSy9U2Qme", "ua": null, "sid": null, "browserType": null, "adid": null, "alipayUmidToken": null, "osVersion": null, "browserVersion": null, "imei": null, "deviceModel": null, "attributes": {}, "lat": null, "cid": "YIeGkaCk/4EDAHxDYnmBQkxK"}, "source": {"acceptLanguage": null, "scenario": null, "platformType": "WIRELESS", "subPlatformType": null, "attributes": {}}}, "orderPayAmt": {"cent": 68163, "currencyCode": "USD"}, "echoPaymentExtAttribute": null, "source": null, "chosenIntentionDTO": {"intentionLineId": null, "intentionContext": {}, "params": {"cardBrandCurrency": null, "cardBinCountry": null, "cardBin": null, "planId": null, "selectListId": null, "payChannelEchoExtAttribute": null, "token": null}}, "extraFeatures": {"featureMap": {"ORIGINAL_INTENTION_CCY": "USD"}}, "payer": {"lastName": null, "signupTime": null, "userName": "CN shopper", "userCountryCode": null, "aliId": **********, "accountStatus": null, "firstName": null, "lastLoginTime": null, "features": {"featureMap": {}}, "phoneNumber": "", "phoneNoCountryCode": "", "userType": null, "email": "<EMAIL>", "extAttrMap": null, "alipayUserId": null}, "platform": null, "routeId": null, "payable": null, "checkoutRequests": [{"fxRate": {"fxRate": 1, "baseCcy": "USD", "quoteCcy": "USD", "outId": null, "id": null}, "orderPromotionFee": {"cent": 0, "currencyCode": "USD"}, "payTermRequests": [], "orderPayFee": {"$ref": "$.body.onlineContext.subInvokes.com\\.alibaba\\.global\\.payment\\.api\\.facade\\.GlobalPaymentCashierServiceFacade\\@renderIntention\\(RenderIntentionRequest\\)[0].null[0].orderPayAmt"}, "supportMultiTerm": null, "orderPromotionSnapshotId": null, "promotionCode": null, "payerId": **********, "source": null, "platform": null, "payeeName": "Shop5423335 Store", "routeId": null, "orderAmount": {"cent": 68163, "currencyCode": "USD"}, "tradeOrderInfo": {"seller": {"lastName": null, "signupTime": null, "userName": "Shop5423335 Store", "userCountryCode": null, "aliId": *********, "accountStatus": null, "firstName": null, "lastLoginTime": null, "features": {"featureMap": {}}, "phoneNumber": null, "phoneNoCountryCode": null, "userType": null, "email": "<EMAIL>", "extAttrMap": null, "alipayUserId": null}, "totalAmount": {"cent": 68163, "currencyCode": "USD"}, "bizType": null, "subTradeOrderInfos": [{"seller": {"$ref": "$.body.onlineContext.subInvokes.com\\.alibaba\\.global\\.payment\\.api\\.facade\\.GlobalPaymentCashierServiceFacade\\@renderIntention\\(RenderIntentionRequest\\)[0].null[0].checkoutRequests[0].tradeOrderInfo.seller"}, "subOrderNo": "90", "amount": {"cent": 68163, "currencyCode": "USD"}, "productCodeList": ["ali.global.ae.trade.general", "ali.global.ae.russain.selfpick", "ali.global.ae.address.restricted", "com.alibaba.business.trade.general.GeneralTradeAbility"], "itemInfos": [{"itemId": "****************", "itemName": "testNoDPRegion", "itemTags": null, "num": 1, "unitAmount": {"cent": 67089, "currencyCode": "USD"}, "attributes": {}, "categoryTree": null, "sku": "*****************", "categoryName": null, "categoryId": "5090301", "extAttrMap": {"ITERM_FEATURE": "{\"carAdditionalInfo\":\"{}\",\"company_id\":\"*********\",\"creator_nick\":\"cn1528892749ywrs\",\"creator_uid\":\"*********\",\"delivery_time\":\"1\",\"detail_type\":\"text\",\"dp_post_id\":\"***********\",\"dp_status\":\"2\",\"fullfillmentMode\":\"other\",\"gpfSource\":\"common-market-100\",\"isPriceReplace\":\"true\",\"new_price\":\"new_price_model\",\"npp\":\"1\",\"o_s_t_p\":\"USD 107\",\"o_t_p\":\"USD 111\",\"owner_id\":\"*********\",\"owner_nick\":\"cn1528892749ywrs\",\"publish_biz_code\":\"ali.ae.general\",\"publish_by_new_service\":\"************\",\"source_app\":\"gpf-i18n\",\"src\":\"post\",\"tax_rate\":\"0.2\",\"translate_flag\":\"00\",\"warrantyFeeRateFor3C\":\"0.045\",\"write_trunk_date\":\"1618753297743\"}"}}], "bizCode": "ali.global.ae.trade.general", "logisticInfo": {"logisticType": "CAINIAO_STANDARD", "country": "IT", "lastName": null, "zipCode": "12354", "address2": "<PERSON><PERSON>", "city": "Chieti", "contactName": "<PERSON><PERSON> sada", "address1": "<PERSON><PERSON>", "fullName": null, "mobileNo": "*********", "phoneNo": null, "firstName": null, "divisionCode": null, "logisticAmount": {"cent": 1074, "currencyCode": "USD"}, "phoneCountryCode": "+39", "areaName": null, "mobileCountryNo": "+39", "detailAddress": null, "phoneArea": null, "attributes": {}, "thirdLevelAddress": null, "state": "Abruzzo", "email": null, "extAttrMap": {"cpf": null, "needValidatePhoneNumber": "true", "addressId": "**********"}}, "mainOrderNo": "812", "attributes": {}}], "orderEnvInfo": "{\"os\":\"ios\",\"extendInfo\":{\"ttid\":\"201200@Aliexpress_iphone_8.16.0\"},\"device\":\"x86_64\"}", "orderId": "812", "logisticInfo": {"$ref": "$.body.onlineContext.subInvokes.com\\.alibaba\\.global\\.payment\\.api\\.facade\\.GlobalPaymentCashierServiceFacade\\@renderIntention\\(RenderIntentionRequest\\)[0].null[0].checkoutRequests[0].tradeOrderInfo.subTradeOrderInfos[0].logisticInfo"}, "transactionTime": null, "orderDesc": null, "extAttrMap": {"collectionPoint": "false"}, "buyer": {"lastName": null, "signupTime": null, "userName": "CN shopper", "userCountryCode": null, "aliId": **********, "accountStatus": null, "firstName": null, "lastLoginTime": null, "features": {"featureMap": {}}, "phoneNumber": "", "phoneNoCountryCode": null, "userType": null, "email": "<EMAIL>", "extAttrMap": null, "alipayUserId": null}}, "payerName": "CN shopper", "siteId": null, "attributes": {}, "payeeId": *********, "extAttrMap": {"isVirtualProduct": "false"}, "bizOrderNo": "812"}], "payCurrency": "USD", "siteId": null, "attributes": {"frontComponentDataJsonStr": null}}], "paramsInstType": null, "paramsType": ["com.alibaba.global.payment.api.request.RenderIntentionRequest"], "relatedCollectIndex": 0, "response": {"success": true, "module": {"cashierResult": {"payIntentionDTO": null, "fee": {"paymentCommission": {"calculateCommission": null, "attributes": {}}, "pmntAmt": {"cent": 68163, "currencyCode": "USD"}, "promotionUse": null, "chosenCurrency": "USD", "attributes": {"FEE_LIST": "[{\"money\":{\"cent\":68163,\"currencyCode\":\"USD\"},\"title\":\"Total:\",\"type\":\"ORDER_FEE\"},{\"money\":{\"cent\":0,\"currencyCode\":\"USD\"},\"title\":\"Instant Discount\",\"type\":\"PROMOTION_FEE\"},{\"money\":{\"cent\":0,\"currencyCode\":\"USD\"},\"title\":\"Transaction Fee: \",\"type\":\"COMMISSION_FEE\"},{\"money\":{\"cent\":68163,\"currencyCode\":\"USD\"},\"title\":\"Summary\",\"type\":\"PAYMENT_FEE\"}]", "orderAmt": {"cent": 68163, "currencyCode": "USD"}}, "orderAmt": {"$ref": "$.body.onlineContext.subInvokes.com\\.alibaba\\.global\\.payment\\.api\\.facade\\.GlobalPaymentCashierServiceFacade\\@renderIntention\\(RenderIntentionRequest\\)[0].response.module.cashierResult.fee.attributes.orderAmt"}, "pmntLocalAmt": null}, "payIntentionView": null, "cashierComponent": "{\"checkoutComponent_**********\":{\"fields\":{\"billingAddressDTO\":{\"address\":\"Asda\",\"address2\":\"Asdas\",\"city\":\"Chieti\",\"country\":\"IT\",\"creditCardNeedBillingAddress\":false,\"province\":\"Abruzzo\",\"zipCode\":\"12354\"},\"checkoutChannelList\":[{\"bindCardAllowed\":true,\"boundCreditCardList\":[],\"canSelectByDefault\":false,\"cardBinBlackList\":[\"506722\",\"627780\",\"439267\",\"404025\",\"506776\",\"482481\",\"471233\",\"486323\",\"400199\",\"506760\",\"523791\",\"400162\",\"472667\",\"451412\",\"506761\",\"510372\",\"459115\"],\"extAttributes\":{\"saveCard\":\"false\",\"cardHolderNameRule\":{\"BR\":[{\"msg\":\"Please enter a card holder name (within 50 characters).\",\"regex\":\"^.{1,50}$\",\"tag\":\"RegexItem\"},{\"msg\":\"Please enter a card holder name (within 50 English characters).\",\"regex\":\"^[a-zA-Z ]+$\",\"tag\":\"RegexItem\"},{\"msg\":\"Use a space to separate first and last name.\",\"regex\":\"^.+[ ]+.+$\",\"tag\":\"RegexItem\"}],\"RU\":[{\"msg\":\"Please enter a card holder name (within 40 characters).\",\"regex\":\"^.{1,40}$\",\"tag\":\"RegexItem\"},{\"msg\":\"Use a space to separate first and last name.\",\"regex\":\"^.+[ ]+.+$\",\"tag\":\"RegexItem\"}],\"OTHERS\":[{\"msg\":\"Please enter a card holder name (within 50 characters).\",\"regex\":\"^.{1,50}$\",\"tag\":\"RegexItem\"},{\"msg\":\"Use a space to separate first and last name.\",\"regex\":\"^.+[ ]+.+$\",\"tag\":\"RegexItem\"}],\"DEFAULT\":[{\"msg\":\"Please enter a card holder name (within 40 characters).\",\"regex\":\"^.{1,40}$\",\"tag\":\"RegexItem\"},{\"msg\":\"Use a space to separate first and last name.\",\"regex\":\"^.+[ ]+.+$\",\"tag\":\"RegexItem\"}]},\"safeMind\":{\"async\":false,\"iconUrl\":\"https://img.alicdn.com/tfs/TB1simweRKw3KVjSZFOXXarDVXa-53-63.png\",\"id\":\"XxX11048\",\"identity\":\"safeMind_XxX11048\",\"imageUrl\":\"https://img.alicdn.com/tfs/TB1.S1TaMgP7K4jSZFqXXamhVXa-544-98.png\",\"input\":false,\"parentId\":\"XxX11031\",\"submit\":false,\"tag\":\"safeMind\",\"text\":\"100% secure payment with\",\"type\":\"biz\",\"validator\":{\"notEmpty\":false}}},\"needChangeCurrency\":false,\"needCpfInput\":false,\"pmtOpt\":\"MIXEDCARD\",\"screen\":0,\"subPaymentMethodList\":[{\"currencyList\":[\"CHF\",\"EUR\",\"CLP\",\"USD\",\"CAD\",\"INR\",\"AUD\",\"SGD\",\"KRW\",\"JPY\",\"PLN\",\"GBP\",\"CZK\",\"HUF\",\"SEK\",\"NZD\",\"TRY\",\"RUB\"],\"extAttributes\":{\"subPaymentIcon\":\"https://img.alicdn.com/tfs/TB1xcMWdEKF3KVjSZFEXXXExFXa-68-48.png\",\"id\":100000},\"payChannelEchoExtAttribute\":\"{\\\"paymentChannelSupportCurrencies\\\":[\\\"CHF\\\",\\\"EUR\\\",\\\"CLP\\\",\\\"USD\\\",\\\"CAD\\\",\\\"INR\\\",\\\"AUD\\\",\\\"SGD\\\",\\\"KRW\\\",\\\"JPY\\\",\\\"PLN\\\",\\\"GBP\\\",\\\"CZK\\\",\\\"HUF\\\",\\\"SEK\\\",\\\"NZD\\\",\\\"TRY\\\",\\\"RUB\\\"]}\",\"paymentGateway\":\"aepay\",\"paymentMethodName\":\"VISA\"},{\"currencyList\":[\"CHF\",\"EUR\",\"CLP\",\"USD\",\"CAD\",\"INR\",\"AUD\",\"SGD\",\"KRW\",\"JPY\",\"PLN\",\"GBP\",\"CZK\",\"HUF\",\"SEK\",\"NZD\",\"TRY\",\"RUB\"],\"extAttributes\":{\"subPaymentIcon\":\"https://img.alicdn.com/tfs/TB19TEYdB1D3KVjSZFyXXbuFpXa-53-48.png\",\"id\":100000},\"payChannelEchoExtAttribute\":\"{\\\"paymentChannelSupportCurrencies\\\":[\\\"CHF\\\",\\\"EUR\\\",\\\"CLP\\\",\\\"USD\\\",\\\"CAD\\\",\\\"INR\\\",\\\"AUD\\\",\\\"SGD\\\",\\\"KRW\\\",\\\"JPY\\\",\\\"PLN\\\",\\\"GBP\\\",\\\"CZK\\\",\\\"HUF\\\",\\\"SEK\\\",\\\"NZD\\\",\\\"TRY\\\",\\\"RUB\\\"]}\",\"paymentGateway\":\"aepay\",\"paymentMethodName\":\"MASTERCARD\"},{\"currencyList\":[\"RUB\"],\"extAttributes\":{\"subPaymentIcon\":\"https://img.alicdn.com/tfs/TB10xg6dq1s3KVjSZFAXXX_ZXXa-77-48.png\",\"id\":100000},\"payChannelEchoExtAttribute\":\"{\\\"paymentChannelSupportCurrencies\\\":[\\\"RUB\\\"]}\",\"paymentGateway\":\"aepay\",\"paymentMethodName\":\"MIR\"},{\"currencyList\":[\"CHF\",\"EUR\",\"CLP\",\"USD\",\"CAD\",\"AUD\",\"KRW\",\"SGD\",\"JPY\",\"PLN\",\"GBP\",\"CZK\",\"HUF\",\"SEK\",\"NZD\",\"RUB\"],\"extAttributes\":{\"subPaymentIcon\":\"https://img.alicdn.com/tfs/TB1Zv3WdEWF3KVjSZPhXXXclXXa-53-48.png\",\"id\":100000},\"payChannelEchoExtAttribute\":\"{\\\"paymentChannelSupportCurrencies\\\":[\\\"CHF\\\",\\\"EUR\\\",\\\"CLP\\\",\\\"USD\\\",\\\"CAD\\\",\\\"AUD\\\",\\\"KRW\\\",\\\"SGD\\\",\\\"JPY\\\",\\\"PLN\\\",\\\"GBP\\\",\\\"CZK\\\",\\\"HUF\\\",\\\"SEK\\\",\\\"NZD\\\",\\\"RUB\\\"]}\",\"paymentGateway\":\"aepay\",\"paymentMethodName\":\"MAESTRO\"},{\"currencyList\":[\"AUD\",\"EUR\",\"GBP\",\"USD\"],\"extAttributes\":{\"subPaymentIcon\":\"https://img.alicdn.com/tfs/TB18So3dBKw3KVjSZFOXXarDVXa-41-48.png\",\"id\":100000},\"payChannelEchoExtAttribute\":\"{\\\"paymentChannelSupportCurrencies\\\":[\\\"AUD\\\",\\\"EUR\\\",\\\"GBP\\\",\\\"USD\\\"]}\",\"paymentGateway\":\"aepay\",\"paymentMethodName\":\"AMEX\"},{\"currencyList\":[\"CHF\",\"EUR\",\"USD\",\"CAD\",\"AUD\",\"KRW\",\"JPY\",\"PLN\",\"GBP\",\"HUF\",\"SEK\",\"NZD\",\"RUB\"],\"extAttributes\":{\"subPaymentIcon\":\"https://img.alicdn.com/tfs/TB19qM7drus3KVjSZKbXXXqkFXa-39-48.png\",\"id\":100000},\"payChannelEchoExtAttribute\":\"{\\\"paymentChannelSupportCurrencies\\\":[\\\"CHF\\\",\\\"EUR\\\",\\\"USD\\\",\\\"CAD\\\",\\\"AUD\\\",\\\"KRW\\\",\\\"JPY\\\",\\\"PLN\\\",\\\"GBP\\\",\\\"HUF\\\",\\\"SEK\\\",\\\"NZD\\\",\\\"RUB\\\"]}\",\"paymentGateway\":\"aepay\",\"paymentMethodName\":\"JCB\"},{\"currencyList\":[\"CHF\",\"EUR\",\"USD\",\"CAD\",\"INR\",\"AUD\",\"KRW\",\"JPY\",\"PLN\",\"GBP\",\"SEK\",\"NZD\",\"RUB\"],\"extAttributes\":{\"subPaymentIcon\":\"https://img.alicdn.com/tfs/TB1zzRoawFY.1VjSZFqXXadbXXa-47-48.png\",\"id\":100000},\"payChannelEchoExtAttribute\":\"{\\\"paymentChannelSupportCurrencies\\\":[\\\"CHF\\\",\\\"EUR\\\",\\\"USD\\\",\\\"CAD\\\",\\\"INR\\\",\\\"AUD\\\",\\\"KRW\\\",\\\"JPY\\\",\\\"PLN\\\",\\\"GBP\\\",\\\"SEK\\\",\\\"NZD\\\",\\\"RUB\\\"]}\",\"paymentGateway\":\"aepay\",\"paymentMethodName\":\"DISCOVER\"},{\"currencyList\":[\"CHF\",\"EUR\",\"USD\",\"CAD\",\"INR\",\"AUD\",\"KRW\",\"JPY\",\"PLN\",\"GBP\",\"HUF\",\"SEK\",\"NZD\",\"RUB\"],\"extAttributes\":{\"subPaymentIcon\":\"https://img.alicdn.com/tfs/TB1YB.VdwaH3KVjSZFjXXcFWpXa-36-48.png\",\"id\":100000},\"payChannelEchoExtAttribute\":\"{\\\"paymentChannelSupportCurrencies\\\":[\\\"CHF\\\",\\\"EUR\\\",\\\"USD\\\",\\\"CAD\\\",\\\"INR\\\",\\\"AUD\\\",\\\"KRW\\\",\\\"JPY\\\",\\\"PLN\\\",\\\"GBP\\\",\\\"HUF\\\",\\\"SEK\\\",\\\"NZD\\\",\\\"RUB\\\"]}\",\"paymentGateway\":\"aepay\",\"paymentMethodName\":\"DINERS\"},{\"currencyList\":[\"TRY\"],\"extAttributes\":{\"subPaymentIcon\":\"https://img.alicdn.com/tfs/TB1_CsJckxz61VjSZFrXXXeLFXa-66-48.png\",\"id\":100000},\"payChannelEchoExtAttribute\":\"{\\\"paymentChannelSupportCurrencies\\\":[\\\"TRY\\\"]}\",\"paymentGateway\":\"aepay\",\"paymentMethodName\":\"TROY\"}]},{\"bindCardAllowed\":true,\"boundCreditCardList\":[],\"canSelectByDefault\":false,\"extAttributes\":{\"paymentIcon\":\"https://img.alicdn.com/tfs/TB1qOgBSxjaK1RjSZFAXXbdLFXa-123-33.png\"},\"needChangeCurrency\":false,\"needCpfInput\":false,\"pmtOpt\":\"WALLET_PAYPAL\",\"screen\":0,\"subPaymentMethodList\":[{\"available\":true,\"channelRate\":\"\",\"currencyList\":[\"AUD\",\"EUR\",\"GBP\",\"USD\",\"CAD\"],\"extAttributes\":{\"subPaymentIcon\":\"https://img.alicdn.com/tfs/TB1qOgBSxjaK1RjSZFAXXbdLFXa-123-33.png\",\"id\":5600},\"payChannelEchoExtAttribute\":\"{\\\"paymentChannelSupportCurrencies\\\":[\\\"AUD\\\",\\\"EUR\\\",\\\"GBP\\\",\\\"USD\\\",\\\"CAD\\\"]}\",\"paymentGateway\":\"aepay\",\"paymentMethodName\":\"WALLET_PAYPAL\",\"requiredList\":[]}]},{\"bindCardAllowed\":true,\"extAttributes\":{},\"needChangeCurrency\":false,\"needCpfInput\":false,\"pmtOpt\":\"others\",\"screen\":1,\"subPaymentMethodList\":[{\"available\":true,\"extAttributes\":{},\"paymentGateway\":\"aepay\",\"paymentMethodName\":\"...\"}]}],\"checkoutNotice\":\"\",\"defaultChosenChannel\":{},\"echoPaymentExtAttribute\":\"{\\\"payRequestIdempotentNo\\\":\\\"658f6382-8022-4e08-9ab7-d65c074a0e46\\\",\\\"ipayProtocolKey\\\":\\\"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\\\"}\",\"isNeedFrontCashierBackend\":true,\"paymentAuthKey\":\"**********\",\"paymentExtraInfo\":{\"sirius\":\"true\",\"queryCardBinUrl\":\"https://open-na.alipay.com/api/v301/alipay/intl/user/card/queryCardBinInfo.htm\",\"clientId\":\"5J5XSL382YMHBT04\",\"countryCode\":\"+7\",\"aghCacheCardUrl\":\"https://open-na.alipay.com/amsin/api/v1/paymentMethods/cacheCard.htm\",\"aghRsaPublicKey\":\"MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA32TIUEPpDC9NHzB9pVB8GGAY2/oIBCCCXd0WOmYbtWiWPDWhgvSyNQLRQ9HbcPOg74NKDpZNL1rhR2GTQagy2EM5RJp/2R+1R0w0MWykl7aJ/yNNQdLsE+kE5X1NkbjZuME4VZFKn4un2BdSph7xIQYPgxo7DMMCQtrEun2xoh3f7W2gpKJj/ubAzoPHHFXJ4KwwgixPcnHyFYpQabX6hRWwDMJ7iet3bz9Mz/qE3Z89sVgVfKqrOohEMVzRHiJVOWrrTtUup7OBu0aYwWq9pVv+9W9U8dXWbxguIEOhKpeM9NnKPJs8sjGN0s87du4i7C5Ou/ZCZvWdzQSgtLqrVwIDAQAB\",\"rsaPublicKey\":\"MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAuIezfUe4zTOtH6WRpUR4y+o0i74DUDSBXuv+eJdNhpGkH9WRwb6Raj/mZ/Lj6a1AvvRujI28GGpA2u7o8RbKR2QB4Tv2reX1dL+T8C9XsIdLQu7KXZI30p72U1CkQ+HoSTzHXCnpuVSOlhzRRT8S7IGSoBSUqoNnG/Zv1FopHN0pZZhHN0CapUdDwSb2adC94yL/Y3EPuvBZTXBMNtjLb/qzpu93cW2L4lkOBMQ2erhh096nEc9jhiDMPcO91seZCSmmL1wBQK97HtNvQ7vLiZwcUMsFPoeI8vBWtNGOZC1ZX5oSOqStanTGZoDtq2c2TiAfT2lBUUIAhGr8FEmhiwIDAQAB\",\"cacheCardUrl\":\"https://open-na.alipay.com/api/v301/alipay/intl/user/asset/cacheCard.htm\",\"assetType\":\"MIXCARD\"},\"paymentPromotionDTO\":{}}},\"checkoutPriceComponent_**********\":{\"fields\":{\"exchangeRate\":0.0,\"extAttributes\":{},\"totalCashAmount\":\"US $681.63\",\"totalCashAmountValue\":681.63,\"totalCashCurrency\":\"USD\"}}}", "protocolVersionTag": null}}, "errorCode": null, "repeated": false, "retry": false}, "rpcContext": null, "serviceName": "com.alibaba.global.payment.api.facade.GlobalPaymentCashierServiceFacade", "showIdentity": "com.alibaba.global.payment.api.facade.GlobalPaymentCashierServiceFacade@renderIntention(RenderIntentionRequest)", "showInvokeIdentity": null, "stackTrace": "com.alibaba.global.satellite.proxy.hsf.utils.hook.SatelliteHSFConsumerProxyHook.invoke@92\ncom.alibaba.global.satellite.proxy.hsf.utils.hook.SatelliteHSFConsumerProxyHook.invoke@29\ncom.alibaba.global.satellite.utils.SentinelUtils.invoke@37\ncom.alibaba.global.satellite.proxy.hsf.utils.hook.SatelliteHSFConsumerProxyHook.proxy@58\ncom.alibaba.global.common.proxy.AbstractProxyHandler.invoke@63\ncom.alibaba.global.common.proxy.JdkProxy$Handler.invoke@66\ncom.alibaba.trade.function.payment.repository.impl.PaymentRepositoryImpl.renderCashier@112\ncom.alibaba.trade.function.payment.service.TradePaymentCashierDomainServiceImpl.renderCashier@67\ncom.alibaba.global.buy.activities.payment.PaymentRenderActivityV2.execute@78\ncom.alibaba.smart.framework.engine.modules.smart.provider.performer.JavaPerformer.perform@56\ncom.alibaba.smart.framework.engine.provider.impl.ComboInvoker.invoke@27\ncom.alibaba.smart.framework.engine.pvm.impl.AbstractPvmElement.invoke@47\ncom.alibaba.smart.framework.engine.provider.impl.DefaultExecutePolicyBehavior.execute@63\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmActivity.execute@59\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmActivity.enter@53\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmTransition.execute@43\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmActivity.executeRecursively@103\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmActivity.execute@68\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmActivity.enter@53\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmTransition.execute@43\n", "startTime": "26120003204371103", "subInvokeType": "rpc", "throwable": null, "timeCost": "1865.367ms", "type": "JAVA"}], "com.alibaba.global.address.api.facade.UserAddressReadFacade@getSelectedUserAddress(SelectedAddressRequest)": [{"afterExecuteParams": null, "endTime": "26120001248675029", "exceptionStackTrace": null, "extInvokeIdentity": null, "extraPrams": null, "fuzzyInvokeIdentity": null, "identity": "com.alibaba.global.address.api.facade.UserAddressReadFacade@getSelectedUserAddress(SelectedAddressRequest)", "index": 16, "isSerialized": null, "method": "getSelectedUserAddress", "params": [{"country": null, "appVersion": null, "appName": null, "bizScene": null, "language": "en", "sessionId": null, "locale": null, "userId": **********, "platform": "PC", "deliveryProviderCodeList": ["CAINIAO_STANDARD"], "features": {}, "userAddressId": **********, "clientIp": null, "paymentMethod": null, "siteId": null}], "paramsInstType": null, "paramsType": ["com.alibaba.global.address.api.request.SelectedAddressRequest"], "relatedCollectIndex": 0, "response": {"success": true, "module": {"collectionPointAddress": null, "selectedUserAddress": {"gmtModified": {"seconds": **********, "nanos": 0}, "extendAddress": "<PERSON><PERSON>", "geoCodeSource": null, "locationTreeAddressId": "910100010000000000-910100010001000000", "postCodeType": null, "latitude": null, "isDefaultBilling": null, "forClient": false, "isJumpDropPin": false, "updateAction": null, "features": {"firstName": null, "lastName": null, "mobile_no_verified": "false", "locale": "local"}, "sellerId": null, "userAddressId": **********, "isDefaultShipping": false, "longitude": null, "showDetailAddress": null, "snapshotId": *********, "addressType": "HOME", "addressTag": "local", "gmtCreate": {"seconds": **********, "nanos": 0}, "userId": **********, "collectionPointInfo": null, "locationTreeAddressName": "Abruzzo,Chieti", "fieldErrorMessageList": [], "detailAddress": "<PERSON><PERSON>", "postCode": "12354", "contactUser": {"birthday": null, "lastName": null, "encryptPassportNo": "", "idCardNo": null, "isForeigner": null, "rutNo": null, "cpf": null, "certificateVerified": false, "passportFullName": null, "email": null, "passportNo": null, "encryptCpf": "", "passportVisaURL": null, "contactName": "<PERSON><PERSON> sada", "fullName": null, "telephone": null, "faxArea": null, "firstName": null, "phonePrefixCode": "+39", "phone": "*********", "passportNoDate": null, "taxId": null, "faxNumber": null, "middleName": null, "telephoneArea": null, "passportOrganization": null, "passportPhotoURL": null}, "relatedAddressId": null, "addressLocationTree": {"l5Node": null, "countryNode": {"displayName": null, "name": "Italy", "id": "IT", "nameLocal": null}, "countryIsoCode": "IT", "stateNode": {"displayName": null, "name": "Abruzzo", "id": "910100010000000000", "nameLocal": null}, "districtNode": {"displayName": null, "name": null, "id": null, "nameLocal": null}, "cityNode": {"displayName": null, "name": "Chieti", "id": "910100010001000000", "nameLocal": null}}, "returnType": null, "status": null}}, "errorCode": null, "repeated": false, "retry": false}, "rpcContext": null, "serviceName": "com.alibaba.global.address.api.facade.UserAddressReadFacade", "showIdentity": "com.alibaba.global.address.api.facade.UserAddressReadFacade@getSelectedUserAddress(SelectedAddressRequest)", "showInvokeIdentity": null, "stackTrace": "com.alibaba.global.satellite.proxy.hsf.utils.hook.SatelliteHSFConsumerProxyHook.invoke@92\ncom.alibaba.global.satellite.proxy.hsf.utils.hook.SatelliteHSFConsumerProxyHook.invoke@29\ncom.alibaba.global.satellite.utils.SentinelUtils.invoke@37\ncom.alibaba.global.satellite.proxy.hsf.utils.hook.SatelliteHSFConsumerProxyHook.proxy@58\ncom.alibaba.global.common.proxy.AbstractProxyHandler.invoke@63\ncom.alibaba.global.common.proxy.JdkProxy$Handler.invoke@66\ncom.alibaba.trade.function.delivery.repository.AddressRepositoryImpl.querySelectAddress@179\ncom.alibaba.trade.function.delivery.service.TradeDeliveryAddressDomainServiceImpl.queryReceiveInfo@134\ncom.alibaba.trade.scenario.activity.shopping.delivery.ShoppingDeliveryQueryAddressActivity.callDomainService@36\ncom.alibaba.trade.scenario.activity.shopping.delivery.ShoppingDeliveryQueryAddressActivity.callDomainService@17\ncom.alibaba.trade.scenario.activity.BaseTradeActivity.execute@37\ncom.alibaba.global.buy.activities.address.AddressQueryActivityV2.execute@34\ncom.alibaba.smart.framework.engine.modules.smart.provider.performer.JavaPerformer.perform@56\ncom.alibaba.smart.framework.engine.provider.impl.ComboInvoker.invoke@27\ncom.alibaba.smart.framework.engine.pvm.impl.AbstractPvmElement.invoke@47\ncom.alibaba.smart.framework.engine.provider.impl.DefaultExecutePolicyBehavior.execute@63\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmActivity.execute@59\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmActivity.enter@53\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmTransition.execute@43\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmActivity.executeRecursively@103\n", "startTime": "26120001229507324", "subInvokeType": "rpc", "throwable": null, "timeCost": "19.168ms", "type": "JAVA"}], "com.alibaba.global.merchant.seller.api.facade.SellerTagFacade@getSellerBizCodes(Long)": [{"afterExecuteParams": null, "endTime": "26120001197338176", "exceptionStackTrace": null, "extInvokeIdentity": null, "extraPrams": null, "fuzzyInvokeIdentity": null, "identity": "com.alibaba.global.merchant.seller.api.facade.SellerTagFacade@getSellerBizCodes(Long)", "index": 5, "isSerialized": null, "method": "getSellerBizCodes", "params": [*********], "paramsInstType": null, "paramsType": ["java.lang.Long"], "relatedCollectIndex": 0, "response": {"success": true, "module": [{"shipFrom": "cn", "bu": "ae", "bizIndustryList": [], "bizModeList": [], "shipTo": "global"}], "errorCode": null, "repeated": false, "retry": false}, "rpcContext": null, "serviceName": "com.alibaba.global.merchant.seller.api.facade.SellerTagFacade", "showIdentity": "com.alibaba.global.merchant.seller.api.facade.SellerTagFacade@getSellerBizCodes(Long)", "showInvokeIdentity": null, "stackTrace": "com.alibaba.global.satellite.proxy.hsf.utils.hook.SatelliteHSFConsumerProxyHook.invoke@92\ncom.alibaba.global.satellite.proxy.hsf.utils.hook.SatelliteHSFConsumerProxyHook.invoke@29\ncom.alibaba.global.satellite.utils.SentinelUtils.invoke@37\ncom.alibaba.global.satellite.proxy.hsf.utils.hook.SatelliteHSFConsumerProxyHook.proxy@58\ncom.alibaba.global.common.proxy.AbstractProxyHandler.invoke@63\ncom.alibaba.global.common.proxy.JdkProxy$Handler.invoke@66\ncom.alibaba.trade.function.product.repository.FulfillmentModeRepositoryImpl.queryFulfillmentMode@126\ncom.alibaba.global.buy.request.builder.impl.FulfillmentModeBuilder.build@39\ncom.alibaba.global.buy.request.builder.impl.FulfillmentModeBuilder.build@26\ncom.alibaba.trade.scenario.build.RequestBuilderFactory.lambda$executeAll$1@56\ncom.alibaba.trade.scenario.build.RequestBuilderFactory.executeAll@56\ncom.alibaba.global.buy.request.ShoppingRequest.createShoppingRequest@108\ncom.alibaba.global.buy.request.ShoppingRequest.of@90\ncom.alibaba.global.buy.api.facade.protocol.GlobalCheckoutFacadeImpl$1._execute@81\ncom.alibaba.global.buy.api.facade.protocol.GlobalCheckoutFacadeImpl$1._execute@69\ncom.alibaba.tmf.session.OnceInvoker.invoke@42\ncom.alibaba.global.buy.api.facade.protocol.GlobalCheckoutFacadeImpl.renderOrder@102\ncom.alibaba.tmf.scenario.ServiceEntranceAspect.doAround@39\ncom.alibaba.ae.trade.adaptor.checkout.AECheckoutMultiterminalFacadeImpl.renderOrder@65\ncom.alibaba.global.satellite.proxy.hsf.utils.hook.SatelliteHSFProviderProxyHook.invoke@68\n", "startTime": "26120001187128906", "subInvokeType": "rpc", "throwable": null, "timeCost": "10.209ms", "type": "JAVA"}], "com.taobao.tair.TairManager@mget(int,List)": [{"afterExecuteParams": null, "endTime": "26120001210617069", "exceptionStackTrace": null, "extInvokeIdentity": null, "extraPrams": null, "fuzzyInvokeIdentity": null, "identity": "com.taobao.tair.TairManager@mget(int,List)", "index": 13, "isSerialized": null, "method": "mget", "params": [1422, ["sellerblack_**********_*********"]], "paramsInstType": null, "paramsType": ["int", "java.util.List"], "relatedCollectIndex": 0, "response": {"rc": {"code": 1}, "flag": 0, "value": []}, "rpcContext": null, "serviceName": "com.taobao.tair.TairManager", "showIdentity": "com.taobao.tair.TairManager@mget(int,List)", "showInvokeIdentity": null, "stackTrace": "com.taobao.tair.impl.mc.MultiClusterTairManager.mget@-1\ncom.alibaba.intl.biz.wssellercrm.cacheclient.service.impl.SellerBlackListTairServiceImpl.getSellerBlackResultBySellerSeqsFromTair@61\ncom.alibaba.business.ae.ability.general.trade.utils.SellerBlackListTairServiceHelper.isBuyerInSellerBlackList@29\ncom.alibaba.business.ae.ability.general.trade.activity.common.order.validate.AeOrderValidateExt.validateSellerBlackList@150\ncom.alibaba.business.ae.ability.general.trade.activity.common.order.validate.AeOrderValidateExt.validateBuyer@140\ncom.alibaba.business.ae.ability.general.trade.activity.common.order.validate.AeOrderValidateExt.validateOrder@73\ncom.alibaba.business.trade.general.ability.order.common.validate.GeneralTradeOrderValidateAbility.lambda$initialValidateOrderLineBeforeRendering$1@60\ncom.alibaba.tmf.function.specific.impl.ExtensionJavaRunner.runFirstMatched@69\ncom.alibaba.tmf.function.specific.impl.ExtensionJavaRunner.runAllMatched@85\ncom.alibaba.tmf.function.specific.impl.RuleBaseAbility.reduceExecuteWithDetailResult@887\ncom.alibaba.tmf.function.specific.impl.RuleBaseAbility.reduceExecuteWithDetailResult@-1\ncom.alibaba.tmf.function.specific.impl.RuleBaseAbility.reduceExecute@917\ncom.alibaba.business.trade.general.ability.order.common.validate.GeneralTradeOrderValidateAbility.initialValidateOrderLineBeforeRendering@59\ncom.alibaba.business.trade.general.realization.order.orderline.validate.GeneralOrderLineValidateExt.lambda$internalInitialValidateOrderLineBeforeRendering$7@83\ncom.alibaba.business.trade.general.realization.order.orderline.validate.GeneralOrderLineValidateExt.internalInitialValidateOrderLineBeforeRendering@83\ncom.alibaba.trade.function.order.ability.orderline.GlobalOrderLineValidateAbility.lambda$initialValidateOrderLineBeforeRendering$2@130\ncom.alibaba.tmf.function.specific.impl.ExtensionJavaRunner.runFirstMatched@69\ncom.alibaba.tmf.function.specific.impl.ExtensionJavaRunner.runAllMatched@85\ncom.alibaba.tmf.function.specific.impl.RuleBaseAbility.reduceExecuteWithDetailResult@887\ncom.alibaba.tmf.function.specific.impl.RuleBaseAbility.reduceExecuteWithDetailResult@-1\n", "startTime": "26120001208972337", "subInvokeType": "r<PERSON><PERSON>", "throwable": null, "timeCost": "1.645ms", "type": "JAVA"}, {"afterExecuteParams": null, "endTime": "26120001211517405", "exceptionStackTrace": null, "extInvokeIdentity": null, "extraPrams": null, "fuzzyInvokeIdentity": null, "identity": "com.taobao.tair.TairManager@mget(int,List)", "index": 14, "isSerialized": null, "method": "mget", "params": [1422, ["sellerblack_**********_*********"]], "paramsInstType": null, "paramsType": ["int", "java.util.List"], "relatedCollectIndex": 0, "response": {"rc": {"code": 1}, "flag": 0, "value": []}, "rpcContext": null, "serviceName": "com.taobao.tair.TairManager", "showIdentity": "com.taobao.tair.TairManager@mget(int,List)", "showInvokeIdentity": null, "stackTrace": "com.taobao.tair.impl.mc.MultiClusterTairManager.mget@-1\ncom.alibaba.intl.biz.wssellercrm.cacheclient.service.impl.SellerBlackListTairServiceImpl.getSellerBlackResultBySellerSeqsFromTair@61\ncom.alibaba.business.ae.ability.general.trade.utils.SellerBlackListTairServiceHelper.isBuyerInSellerBlackList@29\ncom.alibaba.business.ae.ability.general.trade.activity.common.order.validate.AeOrderValidateExt.validateSellerBlackList@150\ncom.alibaba.business.ae.ability.general.trade.activity.common.order.validate.AeOrderValidateExt.validateBuyer@140\ncom.alibaba.business.ae.ability.general.trade.activity.common.order.validate.AeOrderValidateExt.validateOrder@73\ncom.alibaba.business.trade.general.ability.order.common.validate.GeneralTradeOrderValidateAbility.lambda$initialValidateOrderLineBeforeRendering$1@60\ncom.alibaba.tmf.function.specific.impl.ExtensionJavaRunner.runFirstMatched@69\ncom.alibaba.tmf.function.specific.impl.ExtensionJavaRunner.runAllMatched@85\ncom.alibaba.tmf.function.specific.impl.RuleBaseAbility.reduceExecuteWithDetailResult@887\ncom.alibaba.tmf.function.specific.impl.RuleBaseAbility.reduceExecuteWithDetailResult@-1\ncom.alibaba.tmf.function.specific.impl.RuleBaseAbility.reduceExecute@917\ncom.alibaba.business.trade.general.ability.order.common.validate.GeneralTradeOrderValidateAbility.initialValidateOrderLineBeforeRendering@59\ncom.alibaba.business.trade.general.realization.order.orderline.validate.GeneralOrderLineValidateExt.lambda$internalInitialValidateOrderLineBeforeRendering$7@83\ncom.alibaba.business.trade.general.realization.order.orderline.validate.GeneralOrderLineValidateExt.internalInitialValidateOrderLineBeforeRendering@83\ncom.alibaba.trade.function.order.ability.orderline.GlobalOrderLineValidateAbility.lambda$initialValidateOrderLineBeforeRendering$2@130\ncom.alibaba.tmf.function.specific.impl.ExtensionJavaRunner.runFirstMatched@69\ncom.alibaba.tmf.function.specific.impl.ExtensionJavaRunner.runAllMatched@85\ncom.alibaba.tmf.function.specific.impl.RuleBaseAbility.reduceExecuteWithDetailResult@887\ncom.alibaba.tmf.function.specific.impl.RuleBaseAbility.reduceExecuteWithDetailResult@-1\n", "startTime": "26120001210807056", "subInvokeType": "r<PERSON><PERSON>", "throwable": null, "timeCost": "0.710ms", "type": "JAVA"}], "com.alibaba.global.address.api.facade.UserAddressReadFacade@listUserAddress(UserAddressQueryRequest)": [{"afterExecuteParams": null, "endTime": "26120001229203664", "exceptionStackTrace": null, "extInvokeIdentity": null, "extraPrams": null, "fuzzyInvokeIdentity": null, "identity": "com.alibaba.global.address.api.facade.UserAddressReadFacade@listUserAddress(UserAddressQueryRequest)", "index": 15, "isSerialized": null, "method": "listUserAddress", "params": [{"country": "US", "appVersion": null, "addressType": null, "appName": null, "addressTag": null, "recommendCollectionPointFlag": false, "bizScene": null, "sessionId": null, "orderSummary": {"featureMap": null, "orderAmount": null, "userAddressId": null, "shippingFee": null, "orderId": null, "userId": null, "items": null}, "locale": null, "userId": **********, "platform": null, "features": {}, "userAddressId": null, "sortType": null, "clientIp": null, "addressTagList": null, "siteId": null, "itemList": [{"itemId": null, "featureMap": null, "sellerId": null, "quantity": null, "supportSelfPickProviderList": null, "defaultSelfPickup": false, "currencyCode": "USD", "deliveryProviderCode": "CAINIAO_STANDARD", "skuId": null, "supportAddressTypeList": null}]}], "paramsInstType": null, "paramsType": ["com.alibaba.global.address.api.request.UserAddressQueryRequest"], "relatedCollectIndex": 0, "response": {"success": true, "module": [{"gmtModified": {"seconds": **********, "nanos": 0}, "extendAddress": "test111", "geoCodeSource": null, "locationTreeAddressId": "", "postCodeType": null, "latitude": null, "isDefaultBilling": null, "forClient": false, "isJumpDropPin": false, "updateAction": null, "features": {"firstName": null, "lastName": null, "mobile_no_verified": "false", "locale": "en_US"}, "sellerId": null, "userAddressId": **********, "isDefaultShipping": false, "longitude": null, "showDetailAddress": null, "snapshotId": 48791273, "addressType": "HOME", "addressTag": null, "gmtCreate": {"seconds": **********, "nanos": 0}, "userId": **********, "collectionPointInfo": null, "locationTreeAddressName": "Other,test", "fieldErrorMessageList": [], "detailAddress": "ch test", "postCode": "12345", "contactUser": {"birthday": null, "lastName": null, "encryptPassportNo": "", "idCardNo": null, "isForeigner": null, "rutNo": null, "cpf": null, "certificateVerified": false, "passportFullName": null, "email": null, "passportNo": null, "encryptCpf": "", "passportVisaURL": null, "contactName": "ch test", "fullName": null, "telephone": null, "faxArea": null, "firstName": null, "phonePrefixCode": "+1", "phone": "*********", "passportNoDate": null, "taxId": null, "faxNumber": null, "middleName": null, "telephoneArea": null, "passportOrganization": null, "passportPhotoURL": null}, "relatedAddressId": null, "addressLocationTree": {"l5Node": null, "countryNode": {"displayName": null, "name": "United States", "id": "US", "nameLocal": null}, "countryIsoCode": "US", "stateNode": {"displayName": null, "name": "Other", "id": null, "nameLocal": null}, "districtNode": {"displayName": null, "name": null, "id": null, "nameLocal": null}, "cityNode": {"displayName": null, "name": "test", "id": null, "nameLocal": null}}, "returnType": null, "status": null}, {"gmtModified": {"seconds": **********, "nanos": 0}, "extendAddress": "<PERSON><PERSON>", "geoCodeSource": null, "locationTreeAddressId": "910100010000000000-910100010001000000", "postCodeType": null, "latitude": null, "isDefaultBilling": null, "forClient": false, "isJumpDropPin": false, "updateAction": null, "features": {"firstName": null, "lastName": null, "mobile_no_verified": "false", "locale": "local"}, "sellerId": null, "userAddressId": **********, "isDefaultShipping": false, "longitude": null, "showDetailAddress": null, "snapshotId": *********, "addressType": "HOME", "addressTag": "local", "gmtCreate": {"seconds": **********, "nanos": 0}, "userId": **********, "collectionPointInfo": null, "locationTreeAddressName": "Abruzzo,Chieti", "fieldErrorMessageList": [], "detailAddress": "<PERSON><PERSON>", "postCode": "12354", "contactUser": {"birthday": null, "lastName": null, "encryptPassportNo": "", "idCardNo": null, "isForeigner": null, "rutNo": null, "cpf": null, "certificateVerified": false, "passportFullName": null, "email": null, "passportNo": null, "encryptCpf": "", "passportVisaURL": null, "contactName": "<PERSON><PERSON> sada", "fullName": null, "telephone": null, "faxArea": null, "firstName": null, "phonePrefixCode": "+39", "phone": "*********", "passportNoDate": null, "taxId": null, "faxNumber": null, "middleName": null, "telephoneArea": null, "passportOrganization": null, "passportPhotoURL": null}, "relatedAddressId": null, "addressLocationTree": {"l5Node": null, "countryNode": {"displayName": null, "name": "Italy", "id": "IT", "nameLocal": null}, "countryIsoCode": "IT", "stateNode": {"displayName": null, "name": "Abruzzo", "id": "910100010000000000", "nameLocal": null}, "districtNode": {"displayName": null, "name": null, "id": null, "nameLocal": null}, "cityNode": {"displayName": null, "name": "Chieti", "id": "910100010001000000", "nameLocal": null}}, "returnType": null, "status": null}], "errorCode": null, "repeated": false, "retry": false}, "rpcContext": null, "serviceName": "com.alibaba.global.address.api.facade.UserAddressReadFacade", "showIdentity": "com.alibaba.global.address.api.facade.UserAddressReadFacade@listUserAddress(UserAddressQueryRequest)", "showInvokeIdentity": null, "stackTrace": "com.alibaba.global.satellite.proxy.hsf.utils.hook.SatelliteHSFConsumerProxyHook.invoke@92\ncom.alibaba.global.satellite.proxy.hsf.utils.hook.SatelliteHSFConsumerProxyHook.invoke@29\ncom.alibaba.global.satellite.utils.SentinelUtils.invoke@37\ncom.alibaba.global.satellite.proxy.hsf.utils.hook.SatelliteHSFConsumerProxyHook.proxy@58\ncom.alibaba.global.common.proxy.AbstractProxyHandler.invoke@63\ncom.alibaba.global.common.proxy.JdkProxy$Handler.invoke@66\ncom.alibaba.trade.function.delivery.repository.AddressRepositoryImpl.queryAddressByConditional@117\ncom.alibaba.trade.function.delivery.service.TradeDeliveryAddressDomainServiceImpl.queryReceiveInfo@110\ncom.alibaba.trade.scenario.activity.shopping.delivery.ShoppingDeliveryQueryAddressActivity.callDomainService@36\ncom.alibaba.trade.scenario.activity.shopping.delivery.ShoppingDeliveryQueryAddressActivity.callDomainService@17\ncom.alibaba.trade.scenario.activity.BaseTradeActivity.execute@37\ncom.alibaba.global.buy.activities.address.AddressQueryActivityV2.execute@34\ncom.alibaba.smart.framework.engine.modules.smart.provider.performer.JavaPerformer.perform@56\ncom.alibaba.smart.framework.engine.provider.impl.ComboInvoker.invoke@27\ncom.alibaba.smart.framework.engine.pvm.impl.AbstractPvmElement.invoke@47\ncom.alibaba.smart.framework.engine.provider.impl.DefaultExecutePolicyBehavior.execute@63\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmActivity.execute@59\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmActivity.enter@53\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmTransition.execute@43\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmActivity.executeRecursively@103\n", "startTime": "26120001211930583", "subInvokeType": "rpc", "throwable": null, "timeCost": "17.273ms", "type": "JAVA"}], "com.alibaba.global.user.api.facade.UserDataTagFacade@getUserTag(Long,List)": [{"afterExecuteParams": null, "endTime": "26120001204125370", "exceptionStackTrace": null, "extInvokeIdentity": null, "extraPrams": null, "fuzzyInvokeIdentity": null, "identity": "com.alibaba.global.user.api.facade.UserDataTagFacade@getUserTag(Long,List)", "index": 8, "isSerialized": null, "method": "getUserTag", "params": [**********, ["drop_shipper_advanced_info", "ae_newuser", "buyer_tax_info", "gbrain_crowd"]], "paramsInstType": null, "paramsType": ["java.lang.Long", "java.util.List"], "relatedCollectIndex": 0, "response": {"success": true, "module": {"$ref": "$.body.onlineContext.subInvokes.com\\.alibaba\\.global\\.ump\\.protocol\\.sdk\\.facade\\.GlobalTradePromotionMultiCalculateFacade\\@calculateItemPromotion4Checkout\\(GlobalTradeMultiCalculateRequest\\)[0].null[0].extendParam.extensionDTOMap.\\/buyer.attributes"}, "errorCode": null, "repeated": false, "retry": false}, "rpcContext": null, "serviceName": "com.alibaba.global.user.api.facade.UserDataTagFacade", "showIdentity": "com.alibaba.global.user.api.facade.UserDataTagFacade@getUserTag(Long,List)", "showInvokeIdentity": null, "stackTrace": "com.alibaba.global.satellite.proxy.hsf.utils.hook.SatelliteHSFConsumerProxyHook.invoke@92\ncom.alibaba.global.satellite.proxy.hsf.utils.hook.SatelliteHSFConsumerProxyHook.invoke@29\ncom.alibaba.global.satellite.utils.SentinelUtils.invoke@37\ncom.alibaba.global.satellite.proxy.hsf.utils.hook.SatelliteHSFConsumerProxyHook.proxy@58\ncom.alibaba.global.common.proxy.AbstractProxyHandler.invoke@63\ncom.alibaba.global.common.proxy.JdkProxy$Handler.invoke@66\ncom.alibaba.trade.function.actor.repository.Impl.ActorRepositoryImpl.getUserTag@133\ncom.alibaba.trade.function.actor.repository.Impl.ActorRepositoryImpl.queryBuyer@120\ncom.alibaba.trade.scenario.build.common.BaseBuyerBuilder.build@30\ncom.alibaba.trade.scenario.build.RequestBuilderFactory.lambda$executeAll$1@56\ncom.alibaba.trade.scenario.build.RequestBuilderFactory.executeAll@56\ncom.alibaba.global.buy.request.ShoppingRequest.createShoppingRequest@108\ncom.alibaba.global.buy.request.ShoppingRequest.of@90\ncom.alibaba.global.buy.api.facade.protocol.GlobalCheckoutFacadeImpl$1._execute@81\ncom.alibaba.global.buy.api.facade.protocol.GlobalCheckoutFacadeImpl$1._execute@69\ncom.alibaba.tmf.session.OnceInvoker.invoke@42\ncom.alibaba.global.buy.api.facade.protocol.GlobalCheckoutFacadeImpl.renderOrder@102\ncom.alibaba.tmf.scenario.ServiceEntranceAspect.doAround@39\ncom.alibaba.ae.trade.adaptor.checkout.AECheckoutMultiterminalFacadeImpl.renderOrder@65\ncom.alibaba.global.satellite.proxy.hsf.utils.hook.SatelliteHSFProviderProxyHook.invoke@68\n", "startTime": "26120001202222421", "subInvokeType": "rpc", "throwable": null, "timeCost": "1.903ms", "type": "JAVA"}], "com.alibaba.aliexpress.price.api.common.client.product.ProductPriceQueryClient@queryBatchProductPrice(BatchProductPriceQueryParamDTO)": [{"afterExecuteParams": null, "endTime": "26120001551859423", "exceptionStackTrace": null, "extInvokeIdentity": null, "extraPrams": null, "fuzzyInvokeIdentity": null, "identity": "com.alibaba.aliexpress.price.api.common.client.product.ProductPriceQueryClient@queryBatchProductPrice(BatchProductPriceQueryParamDTO)", "index": 20, "isSerialized": null, "method": "queryBatchProductPrice", "params": [{"sourceCustomerSetting": {"featureMap": null, "language": "en", "region": {"countryCode": "IT", "provinceCode": "910100010000000000", "cityCode": "910100010001000000"}, "currencyCode": "USD"}, "taxCalculateTime": null, "featureMap": null, "sourceClient": null, "sourceDevice": {"featureMap": null, "id": "x86_64"}, "productQueryOptionsList": [{"seller": null, "featureMap": null, "productPurchaseOptions": {"skuPurchaseOptionsList": [{"productId": ****************, "purchaseQuantity": 1, "skuId": "*****************"}]}, "productQueryResult": {"$ref": "$.body.onlineContext.subInvokes.com\\.alibaba\\.global\\.ic\\.api\\.CustomerProductServiceFacade\\@queryProduct\\(ProductQueryRequest\\)[0].response.model[0]"}}], "timeMillis": 1619613261530, "scene": "TradeBuy", "buyer": {"address": null, "userTag": null, "id": **********}}], "paramsInstType": null, "paramsType": ["com.alibaba.aliexpress.price.api.common.client.product.model.scenario.query.BatchProductPriceQueryParamDTO"], "relatedCollectIndex": 0, "response": {"data": {"featureMap": null, "productPriceQueryResultMap": {"****************": {"featureMap": null, "productId": ****************, "priceSemanticTags": {"featureMap": {"npp": "true"}}, "skuIdAndSkuPriceQueryResultMap": {"*****************": {"featureMap": null, "productId": ****************, "productSkuRegionPrice": {"originalSuppliedPriceSummary": {"platformSuppliedPrice": {"featureMap": {"sku_bulk_discount": "10.000"}, "postageCostPrice": null, "supplier": "platform", "productPrice": {"quotePrice": {"amount": {"$ref": "$.body.onlineContext.subInvokes.com\\.alibaba\\.global\\.ic\\.api\\.CustomerProductServiceFacade\\@queryProduct\\(ProductQueryRequest\\)[0].response.model[0].skuIdAndSkuMap.*****************.priceDTO.countryPriceList[0].price"}, "featureMap": null, "rate": null, "amountDisplay": null}, "intentionPrice": null, "featureMap": null, "taxFeeIntentionPrice": null, "belongTo": null, "taxFeeQuotePrice": {"amount": {"number": 111, "monetaryContext": {"data": {"amountType": {"$ref": "$.body.onlineContext.mainInvoke.response.module.orders[0].orderLines[0].product.unitPrice.monetaryContext.data.amountType"}, "precision": 256, "java.lang.Class": {"$ref": "$.body.onlineContext.mainInvoke.response.module.orders[0].orderLines[0].product.unitPrice.monetaryContext.data.amountType"}, "java.math.RoundingMode": "HALF_EVEN"}}, "currency": {"$ref": "$.body.onlineContext.subInvokes.com\\.alibaba\\.global\\.ic\\.api\\.CustomerProductServiceFacade\\@queryProduct\\(ProductQueryRequest\\)[0].response.model[0].skuIdAndSkuMap.*****************.priceDTO.promotionPrice.price.currency"}}, "featureMap": null, "rate": 0.2, "amountDisplay": null}}}, "featureMap": null, "priceType": "original", "useBulkPrice": false, "merchantSuppliedPrice": {"featureMap": {"sku_bulk_discount": "10.000"}, "postageCostPrice": null, "supplier": "merchant", "productPrice": {"quotePrice": {"amount": {"$ref": "$.body.onlineContext.subInvokes.com\\.alibaba\\.global\\.ic\\.api\\.CustomerProductServiceFacade\\@queryProduct\\(ProductQueryRequest\\)[0].response.model[0].skuIdAndSkuMap.*****************.priceDTO.countryPriceList[0].price"}, "featureMap": null, "rate": null, "amountDisplay": null}, "intentionPrice": null, "featureMap": null, "taxFeeIntentionPrice": null, "belongTo": null, "taxFeeQuotePrice": {"amount": {"number": 111, "monetaryContext": {"$ref": "$.body.onlineContext.subInvokes.com\\.alibaba\\.aliexpress\\.price\\.api\\.common\\.client\\.product\\.ProductPriceQueryClient\\@queryBatchProductPrice\\(BatchProductPriceQueryParamDTO\\)[0].response.data.productPriceQueryResultMap.****************.skuIdAndSkuPriceQueryResultMap.*****************.productSkuRegionPrice.originalSuppliedPriceSummary.platformSuppliedPrice.productPrice.taxFeeQuotePrice.amount.monetaryContext"}, "currency": {"$ref": "$.body.onlineContext.subInvokes.com\\.alibaba\\.global\\.ic\\.api\\.CustomerProductServiceFacade\\@queryProduct\\(ProductQueryRequest\\)[0].response.model[0].skuIdAndSkuMap.*****************.priceDTO.promotionPrice.price.currency"}}, "featureMap": null, "rate": 0.2, "amountDisplay": null}}}}, "featureMap": {"ic_sku_bulk_discount": "10", "ic_sku_bulk_order": "6"}, "countryCode": "IT", "promotionSuppliedPriceSummary": {"platformSuppliedPrice": {"featureMap": {"sku_bulk_discount": "10.000"}, "postageCostPrice": null, "supplier": "platform", "productPrice": {"quotePrice": {"amount": {"$ref": "$.body.onlineContext.subInvokes.com\\.alibaba\\.global\\.ic\\.api\\.CustomerProductServiceFacade\\@queryProduct\\(ProductQueryRequest\\)[0].response.model[0].skuIdAndSkuMap.*****************.priceDTO.countryPriceList[0].promotionPrice"}, "featureMap": null, "rate": null, "amountDisplay": null}, "intentionPrice": null, "featureMap": null, "taxFeeIntentionPrice": null, "belongTo": null, "taxFeeQuotePrice": {"amount": {"number": 107, "monetaryContext": {"$ref": "$.body.onlineContext.subInvokes.com\\.alibaba\\.aliexpress\\.price\\.api\\.common\\.client\\.product\\.ProductPriceQueryClient\\@queryBatchProductPrice\\(BatchProductPriceQueryParamDTO\\)[0].response.data.productPriceQueryResultMap.****************.skuIdAndSkuPriceQueryResultMap.*****************.productSkuRegionPrice.originalSuppliedPriceSummary.platformSuppliedPrice.productPrice.taxFeeQuotePrice.amount.monetaryContext"}, "currency": {"$ref": "$.body.onlineContext.subInvokes.com\\.alibaba\\.global\\.ic\\.api\\.CustomerProductServiceFacade\\@queryProduct\\(ProductQueryRequest\\)[0].response.model[0].skuIdAndSkuMap.*****************.priceDTO.promotionPrice.price.currency"}}, "featureMap": null, "rate": 0.2, "amountDisplay": null}}}, "featureMap": null, "priceType": "promotion", "useBulkPrice": false, "merchantSuppliedPrice": {"featureMap": {"sku_bulk_discount": "10.000"}, "postageCostPrice": null, "supplier": "merchant", "productPrice": {"quotePrice": {"amount": {"$ref": "$.body.onlineContext.subInvokes.com\\.alibaba\\.global\\.ic\\.api\\.CustomerProductServiceFacade\\@queryProduct\\(ProductQueryRequest\\)[0].response.model[0].skuIdAndSkuMap.*****************.priceDTO.countryPriceList[0].promotionPrice"}, "featureMap": null, "rate": null, "amountDisplay": null}, "intentionPrice": null, "featureMap": null, "taxFeeIntentionPrice": null, "belongTo": null, "taxFeeQuotePrice": {"amount": {"number": 107, "monetaryContext": {"$ref": "$.body.onlineContext.subInvokes.com\\.alibaba\\.aliexpress\\.price\\.api\\.common\\.client\\.product\\.ProductPriceQueryClient\\@queryBatchProductPrice\\(BatchProductPriceQueryParamDTO\\)[0].response.data.productPriceQueryResultMap.****************.skuIdAndSkuPriceQueryResultMap.*****************.productSkuRegionPrice.originalSuppliedPriceSummary.platformSuppliedPrice.productPrice.taxFeeQuotePrice.amount.monetaryContext"}, "currency": {"$ref": "$.body.onlineContext.subInvokes.com\\.alibaba\\.global\\.ic\\.api\\.CustomerProductServiceFacade\\@queryProduct\\(ProductQueryRequest\\)[0].response.model[0].skuIdAndSkuMap.*****************.priceDTO.promotionPrice.price.currency"}}, "featureMap": null, "rate": 0.2, "amountDisplay": null}}}}}, "skuId": "*****************"}}}}, "errorCode": null}, "errorCode": null, "errorMsg": null}, "rpcContext": null, "serviceName": "com.alibaba.aliexpress.price.api.common.client.product.ProductPriceQueryClient", "showIdentity": "com.alibaba.aliexpress.price.api.common.client.product.ProductPriceQueryClient@queryBatchProductPrice(BatchProductPriceQueryParamDTO)", "showInvokeIdentity": null, "stackTrace": "com.alibaba.aliexpress.price.client.common.service.impl.product.ProductPriceQueryClientImpl.queryBatchProductPrice@-1\ncom.alibaba.business.ae.ability.general.trade.utils.PriceUtils.queryPrice@466\ncom.alibaba.business.ae.ability.general.trade.utils.PriceUtils.invokePriceNewSdk@226\ncom.alibaba.business.ae.ability.general.trade.activity.common.order.enrich.AeOrderEnrichExt.enrichOrderBaseInfo@762\ncom.alibaba.business.trade.general.ability.order.common.enrich.GeneralTradeOrderBaseInfoEnrichAbility.lambda$enrichOrderBaseInfo$0@35\ncom.alibaba.tmf.function.specific.impl.ExtensionJavaRunner.runFirstMatched@69\ncom.alibaba.tmf.function.specific.impl.ExtensionJavaRunner.runAllMatched@85\ncom.alibaba.tmf.function.specific.impl.RuleBaseAbility.reduceExecuteWithDetailResult@887\ncom.alibaba.tmf.function.specific.impl.RuleBaseAbility.reduceExecuteWithDetailResult@-1\ncom.alibaba.tmf.function.specific.impl.RuleBaseAbility.reduceExecute@917\ncom.alibaba.business.trade.general.ability.order.common.enrich.GeneralTradeOrderBaseInfoEnrichAbility.enrichOrderBaseInfo@33\ncom.alibaba.business.trade.general.realization.order.orderline.build.GeneralOrderLineBuildExt.internalEnrichBuyer@25\ncom.alibaba.trade.function.order.ability.orderline.GlobalOrderLineBuildAbility.lambda$extInitBuyer$0@58\ncom.alibaba.tmf.function.specific.impl.ExtensionJavaRunner.runFirstMatched@69\ncom.alibaba.tmf.function.specific.impl.ExtensionJavaRunner.runAllMatched@85\ncom.alibaba.tmf.function.specific.impl.RuleBaseAbility.reduceExecuteWithDetailResult@887\ncom.alibaba.tmf.function.specific.impl.RuleBaseAbility.reduceExecuteWithDetailResult@-1\ncom.alibaba.tmf.function.specific.impl.RuleBaseAbility.reduceExecute@917\ncom.alibaba.trade.function.order.ability.orderline.GlobalOrderLineBuildAbility.extInitBuyer@56\ncom.alibaba.trade.function.order.ability.orderline.OrderLineBuildAbility.initBuyer@28\n", "startTime": "26120001550859681", "subInvokeType": null, "throwable": null, "timeCost": "1.000ms", "type": "JAVA"}, {"afterExecuteParams": null, "endTime": "26120001552951838", "exceptionStackTrace": null, "extInvokeIdentity": null, "extraPrams": null, "fuzzyInvokeIdentity": null, "identity": "com.alibaba.aliexpress.price.api.common.client.product.ProductPriceQueryClient@queryBatchProductPrice(BatchProductPriceQueryParamDTO)", "index": 21, "isSerialized": null, "method": "queryBatchProductPrice", "params": [{"sourceCustomerSetting": {"featureMap": null, "language": "en", "region": {"countryCode": "IT", "provinceCode": "910100010000000000", "cityCode": "910100010001000000"}, "currencyCode": "USD"}, "taxCalculateTime": null, "featureMap": null, "sourceClient": null, "sourceDevice": {"featureMap": null, "id": "x86_64"}, "productQueryOptionsList": [{"seller": null, "featureMap": null, "productPurchaseOptions": {"skuPurchaseOptionsList": [{"productId": ****************, "purchaseQuantity": 1, "skuId": "*****************"}]}, "productQueryResult": {"$ref": "$.body.onlineContext.subInvokes.com\\.alibaba\\.global\\.ic\\.api\\.CustomerProductServiceFacade\\@queryProduct\\(ProductQueryRequest\\)[0].response.model[0]"}}], "timeMillis": 1619613261531, "scene": "TradeBuy", "buyer": {"address": null, "userTag": null, "id": **********}}], "paramsInstType": null, "paramsType": ["com.alibaba.aliexpress.price.api.common.client.product.model.scenario.query.BatchProductPriceQueryParamDTO"], "relatedCollectIndex": 0, "response": {"data": {"featureMap": null, "productPriceQueryResultMap": {"****************": {"featureMap": null, "productId": ****************, "priceSemanticTags": {"featureMap": {"npp": "true"}}, "skuIdAndSkuPriceQueryResultMap": {"*****************": {"featureMap": null, "productId": ****************, "productSkuRegionPrice": {"originalSuppliedPriceSummary": {"platformSuppliedPrice": {"featureMap": {"sku_bulk_discount": "10.000"}, "postageCostPrice": null, "supplier": "platform", "productPrice": {"quotePrice": {"amount": {"$ref": "$.body.onlineContext.subInvokes.com\\.alibaba\\.global\\.ic\\.api\\.CustomerProductServiceFacade\\@queryProduct\\(ProductQueryRequest\\)[0].response.model[0].skuIdAndSkuMap.*****************.priceDTO.countryPriceList[0].price"}, "featureMap": null, "rate": null, "amountDisplay": null}, "intentionPrice": null, "featureMap": null, "taxFeeIntentionPrice": null, "belongTo": null, "taxFeeQuotePrice": {"amount": {"number": 111, "monetaryContext": {"$ref": "$.body.onlineContext.subInvokes.com\\.alibaba\\.aliexpress\\.price\\.api\\.common\\.client\\.product\\.ProductPriceQueryClient\\@queryBatchProductPrice\\(BatchProductPriceQueryParamDTO\\)[0].response.data.productPriceQueryResultMap.****************.skuIdAndSkuPriceQueryResultMap.*****************.productSkuRegionPrice.originalSuppliedPriceSummary.platformSuppliedPrice.productPrice.taxFeeQuotePrice.amount.monetaryContext"}, "currency": {"$ref": "$.body.onlineContext.subInvokes.com\\.alibaba\\.global\\.ic\\.api\\.CustomerProductServiceFacade\\@queryProduct\\(ProductQueryRequest\\)[0].response.model[0].skuIdAndSkuMap.*****************.priceDTO.promotionPrice.price.currency"}}, "featureMap": null, "rate": 0.2, "amountDisplay": null}}}, "featureMap": null, "priceType": "original", "useBulkPrice": false, "merchantSuppliedPrice": {"featureMap": {"sku_bulk_discount": "10.000"}, "postageCostPrice": null, "supplier": "merchant", "productPrice": {"quotePrice": {"amount": {"$ref": "$.body.onlineContext.subInvokes.com\\.alibaba\\.global\\.ic\\.api\\.CustomerProductServiceFacade\\@queryProduct\\(ProductQueryRequest\\)[0].response.model[0].skuIdAndSkuMap.*****************.priceDTO.countryPriceList[0].price"}, "featureMap": null, "rate": null, "amountDisplay": null}, "intentionPrice": null, "featureMap": null, "taxFeeIntentionPrice": null, "belongTo": null, "taxFeeQuotePrice": {"amount": {"number": 111, "monetaryContext": {"$ref": "$.body.onlineContext.subInvokes.com\\.alibaba\\.aliexpress\\.price\\.api\\.common\\.client\\.product\\.ProductPriceQueryClient\\@queryBatchProductPrice\\(BatchProductPriceQueryParamDTO\\)[0].response.data.productPriceQueryResultMap.****************.skuIdAndSkuPriceQueryResultMap.*****************.productSkuRegionPrice.originalSuppliedPriceSummary.platformSuppliedPrice.productPrice.taxFeeQuotePrice.amount.monetaryContext"}, "currency": {"$ref": "$.body.onlineContext.subInvokes.com\\.alibaba\\.global\\.ic\\.api\\.CustomerProductServiceFacade\\@queryProduct\\(ProductQueryRequest\\)[0].response.model[0].skuIdAndSkuMap.*****************.priceDTO.promotionPrice.price.currency"}}, "featureMap": null, "rate": 0.2, "amountDisplay": null}}}}, "featureMap": {"ic_sku_bulk_discount": "10", "ic_sku_bulk_order": "6"}, "countryCode": "IT", "promotionSuppliedPriceSummary": {"platformSuppliedPrice": {"featureMap": {"sku_bulk_discount": "10.000"}, "postageCostPrice": null, "supplier": "platform", "productPrice": {"quotePrice": {"amount": {"$ref": "$.body.onlineContext.subInvokes.com\\.alibaba\\.global\\.ic\\.api\\.CustomerProductServiceFacade\\@queryProduct\\(ProductQueryRequest\\)[0].response.model[0].skuIdAndSkuMap.*****************.priceDTO.countryPriceList[0].promotionPrice"}, "featureMap": null, "rate": null, "amountDisplay": null}, "intentionPrice": null, "featureMap": null, "taxFeeIntentionPrice": null, "belongTo": null, "taxFeeQuotePrice": {"amount": {"number": 107, "monetaryContext": {"$ref": "$.body.onlineContext.subInvokes.com\\.alibaba\\.aliexpress\\.price\\.api\\.common\\.client\\.product\\.ProductPriceQueryClient\\@queryBatchProductPrice\\(BatchProductPriceQueryParamDTO\\)[0].response.data.productPriceQueryResultMap.****************.skuIdAndSkuPriceQueryResultMap.*****************.productSkuRegionPrice.originalSuppliedPriceSummary.platformSuppliedPrice.productPrice.taxFeeQuotePrice.amount.monetaryContext"}, "currency": {"$ref": "$.body.onlineContext.subInvokes.com\\.alibaba\\.global\\.ic\\.api\\.CustomerProductServiceFacade\\@queryProduct\\(ProductQueryRequest\\)[0].response.model[0].skuIdAndSkuMap.*****************.priceDTO.promotionPrice.price.currency"}}, "featureMap": null, "rate": 0.2, "amountDisplay": null}}}, "featureMap": null, "priceType": "promotion", "useBulkPrice": false, "merchantSuppliedPrice": {"featureMap": {"sku_bulk_discount": "10.000"}, "postageCostPrice": null, "supplier": "merchant", "productPrice": {"quotePrice": {"amount": {"$ref": "$.body.onlineContext.subInvokes.com\\.alibaba\\.global\\.ic\\.api\\.CustomerProductServiceFacade\\@queryProduct\\(ProductQueryRequest\\)[0].response.model[0].skuIdAndSkuMap.*****************.priceDTO.countryPriceList[0].promotionPrice"}, "featureMap": null, "rate": null, "amountDisplay": null}, "intentionPrice": null, "featureMap": null, "taxFeeIntentionPrice": null, "belongTo": null, "taxFeeQuotePrice": {"amount": {"number": 107, "monetaryContext": {"$ref": "$.body.onlineContext.subInvokes.com\\.alibaba\\.aliexpress\\.price\\.api\\.common\\.client\\.product\\.ProductPriceQueryClient\\@queryBatchProductPrice\\(BatchProductPriceQueryParamDTO\\)[0].response.data.productPriceQueryResultMap.****************.skuIdAndSkuPriceQueryResultMap.*****************.productSkuRegionPrice.originalSuppliedPriceSummary.platformSuppliedPrice.productPrice.taxFeeQuotePrice.amount.monetaryContext"}, "currency": {"$ref": "$.body.onlineContext.subInvokes.com\\.alibaba\\.global\\.ic\\.api\\.CustomerProductServiceFacade\\@queryProduct\\(ProductQueryRequest\\)[0].response.model[0].skuIdAndSkuMap.*****************.priceDTO.promotionPrice.price.currency"}}, "featureMap": null, "rate": 0.2, "amountDisplay": null}}}}}, "skuId": "*****************"}}}}, "errorCode": null}, "errorCode": null, "errorMsg": null}, "rpcContext": null, "serviceName": "com.alibaba.aliexpress.price.api.common.client.product.ProductPriceQueryClient", "showIdentity": "com.alibaba.aliexpress.price.api.common.client.product.ProductPriceQueryClient@queryBatchProductPrice(BatchProductPriceQueryParamDTO)", "showInvokeIdentity": null, "stackTrace": "com.alibaba.aliexpress.price.client.common.service.impl.product.ProductPriceQueryClientImpl.queryBatchProductPrice@-1\ncom.alibaba.business.ae.ability.general.trade.utils.PriceUtils.queryPrice@466\ncom.alibaba.business.ae.ability.general.trade.utils.PriceUtils.invokePriceNewSdk@226\ncom.alibaba.business.ae.ability.general.trade.activity.common.order.enrich.AeOrderEnrichExt.enrichOrderBaseInfo@762\ncom.alibaba.business.trade.general.ability.order.common.enrich.GeneralTradeOrderBaseInfoEnrichAbility.lambda$enrichOrderBaseInfo$0@35\ncom.alibaba.tmf.function.specific.impl.ExtensionJavaRunner.runFirstMatched@69\ncom.alibaba.tmf.function.specific.impl.ExtensionJavaRunner.runAllMatched@85\ncom.alibaba.tmf.function.specific.impl.RuleBaseAbility.reduceExecuteWithDetailResult@887\ncom.alibaba.tmf.function.specific.impl.RuleBaseAbility.reduceExecuteWithDetailResult@-1\ncom.alibaba.tmf.function.specific.impl.RuleBaseAbility.reduceExecute@917\ncom.alibaba.business.trade.general.ability.order.common.enrich.GeneralTradeOrderBaseInfoEnrichAbility.enrichOrderBaseInfo@33\ncom.alibaba.business.trade.general.realization.order.orderline.build.GeneralOrderLineBuildExt.internalEnrichSeller@36\ncom.alibaba.trade.function.order.ability.orderline.GlobalOrderLineBuildAbility.lambda$extInitSeller$2@84\ncom.alibaba.tmf.function.specific.impl.ExtensionJavaRunner.runFirstMatched@69\ncom.alibaba.tmf.function.specific.impl.ExtensionJavaRunner.runAllMatched@85\ncom.alibaba.tmf.function.specific.impl.RuleBaseAbility.reduceExecuteWithDetailResult@887\ncom.alibaba.tmf.function.specific.impl.RuleBaseAbility.reduceExecuteWithDetailResult@-1\ncom.alibaba.tmf.function.specific.impl.RuleBaseAbility.reduceExecute@917\ncom.alibaba.trade.function.order.ability.orderline.GlobalOrderLineBuildAbility.extInitSeller@82\ncom.alibaba.trade.function.order.ability.orderline.OrderLineBuildAbility.initSeller@32\n", "startTime": "26120001552359875", "subInvokeType": null, "throwable": null, "timeCost": "0.592ms", "type": "JAVA"}, {"afterExecuteParams": null, "endTime": "26120001553821194", "exceptionStackTrace": null, "extInvokeIdentity": null, "extraPrams": null, "fuzzyInvokeIdentity": null, "identity": "com.alibaba.aliexpress.price.api.common.client.product.ProductPriceQueryClient@queryBatchProductPrice(BatchProductPriceQueryParamDTO)", "index": 22, "isSerialized": null, "method": "queryBatchProductPrice", "params": [{"sourceCustomerSetting": {"featureMap": null, "language": "en", "region": {"countryCode": "IT", "provinceCode": "910100010000000000", "cityCode": "910100010001000000"}, "currencyCode": "USD"}, "taxCalculateTime": null, "featureMap": null, "sourceClient": null, "sourceDevice": {"featureMap": null, "id": "x86_64"}, "productQueryOptionsList": [{"seller": null, "featureMap": null, "productPurchaseOptions": {"skuPurchaseOptionsList": [{"productId": ****************, "purchaseQuantity": 1, "skuId": "*****************"}]}, "productQueryResult": {"$ref": "$.body.onlineContext.subInvokes.com\\.alibaba\\.global\\.ic\\.api\\.CustomerProductServiceFacade\\@queryProduct\\(ProductQueryRequest\\)[0].response.model[0]"}}], "timeMillis": 1619613261532, "scene": "TradeBuy", "buyer": {"address": null, "userTag": null, "id": **********}}], "paramsInstType": null, "paramsType": ["com.alibaba.aliexpress.price.api.common.client.product.model.scenario.query.BatchProductPriceQueryParamDTO"], "relatedCollectIndex": 0, "response": {"data": {"featureMap": null, "productPriceQueryResultMap": {"****************": {"featureMap": null, "productId": ****************, "priceSemanticTags": {"featureMap": {"npp": "true"}}, "skuIdAndSkuPriceQueryResultMap": {"*****************": {"featureMap": null, "productId": ****************, "productSkuRegionPrice": {"originalSuppliedPriceSummary": {"platformSuppliedPrice": {"featureMap": {"sku_bulk_discount": "10.000"}, "postageCostPrice": null, "supplier": "platform", "productPrice": {"quotePrice": {"amount": {"$ref": "$.body.onlineContext.subInvokes.com\\.alibaba\\.global\\.ic\\.api\\.CustomerProductServiceFacade\\@queryProduct\\(ProductQueryRequest\\)[0].response.model[0].skuIdAndSkuMap.*****************.priceDTO.countryPriceList[0].price"}, "featureMap": null, "rate": null, "amountDisplay": null}, "intentionPrice": null, "featureMap": null, "taxFeeIntentionPrice": null, "belongTo": null, "taxFeeQuotePrice": {"amount": {"number": 111, "monetaryContext": {"$ref": "$.body.onlineContext.subInvokes.com\\.alibaba\\.aliexpress\\.price\\.api\\.common\\.client\\.product\\.ProductPriceQueryClient\\@queryBatchProductPrice\\(BatchProductPriceQueryParamDTO\\)[0].response.data.productPriceQueryResultMap.****************.skuIdAndSkuPriceQueryResultMap.*****************.productSkuRegionPrice.originalSuppliedPriceSummary.platformSuppliedPrice.productPrice.taxFeeQuotePrice.amount.monetaryContext"}, "currency": {"$ref": "$.body.onlineContext.subInvokes.com\\.alibaba\\.global\\.ic\\.api\\.CustomerProductServiceFacade\\@queryProduct\\(ProductQueryRequest\\)[0].response.model[0].skuIdAndSkuMap.*****************.priceDTO.promotionPrice.price.currency"}}, "featureMap": null, "rate": 0.2, "amountDisplay": null}}}, "featureMap": null, "priceType": "original", "useBulkPrice": false, "merchantSuppliedPrice": {"featureMap": {"sku_bulk_discount": "10.000"}, "postageCostPrice": null, "supplier": "merchant", "productPrice": {"quotePrice": {"amount": {"$ref": "$.body.onlineContext.subInvokes.com\\.alibaba\\.global\\.ic\\.api\\.CustomerProductServiceFacade\\@queryProduct\\(ProductQueryRequest\\)[0].response.model[0].skuIdAndSkuMap.*****************.priceDTO.countryPriceList[0].price"}, "featureMap": null, "rate": null, "amountDisplay": null}, "intentionPrice": null, "featureMap": null, "taxFeeIntentionPrice": null, "belongTo": null, "taxFeeQuotePrice": {"amount": {"number": 111, "monetaryContext": {"$ref": "$.body.onlineContext.subInvokes.com\\.alibaba\\.aliexpress\\.price\\.api\\.common\\.client\\.product\\.ProductPriceQueryClient\\@queryBatchProductPrice\\(BatchProductPriceQueryParamDTO\\)[0].response.data.productPriceQueryResultMap.****************.skuIdAndSkuPriceQueryResultMap.*****************.productSkuRegionPrice.originalSuppliedPriceSummary.platformSuppliedPrice.productPrice.taxFeeQuotePrice.amount.monetaryContext"}, "currency": {"$ref": "$.body.onlineContext.subInvokes.com\\.alibaba\\.global\\.ic\\.api\\.CustomerProductServiceFacade\\@queryProduct\\(ProductQueryRequest\\)[0].response.model[0].skuIdAndSkuMap.*****************.priceDTO.promotionPrice.price.currency"}}, "featureMap": null, "rate": 0.2, "amountDisplay": null}}}}, "featureMap": {"ic_sku_bulk_discount": "10", "ic_sku_bulk_order": "6"}, "countryCode": "IT", "promotionSuppliedPriceSummary": {"platformSuppliedPrice": {"featureMap": {"sku_bulk_discount": "10.000"}, "postageCostPrice": null, "supplier": "platform", "productPrice": {"quotePrice": {"amount": {"$ref": "$.body.onlineContext.subInvokes.com\\.alibaba\\.global\\.ic\\.api\\.CustomerProductServiceFacade\\@queryProduct\\(ProductQueryRequest\\)[0].response.model[0].skuIdAndSkuMap.*****************.priceDTO.countryPriceList[0].promotionPrice"}, "featureMap": null, "rate": null, "amountDisplay": null}, "intentionPrice": null, "featureMap": null, "taxFeeIntentionPrice": null, "belongTo": null, "taxFeeQuotePrice": {"amount": {"number": 107, "monetaryContext": {"$ref": "$.body.onlineContext.subInvokes.com\\.alibaba\\.aliexpress\\.price\\.api\\.common\\.client\\.product\\.ProductPriceQueryClient\\@queryBatchProductPrice\\(BatchProductPriceQueryParamDTO\\)[0].response.data.productPriceQueryResultMap.****************.skuIdAndSkuPriceQueryResultMap.*****************.productSkuRegionPrice.originalSuppliedPriceSummary.platformSuppliedPrice.productPrice.taxFeeQuotePrice.amount.monetaryContext"}, "currency": {"$ref": "$.body.onlineContext.subInvokes.com\\.alibaba\\.global\\.ic\\.api\\.CustomerProductServiceFacade\\@queryProduct\\(ProductQueryRequest\\)[0].response.model[0].skuIdAndSkuMap.*****************.priceDTO.promotionPrice.price.currency"}}, "featureMap": null, "rate": 0.2, "amountDisplay": null}}}, "featureMap": null, "priceType": "promotion", "useBulkPrice": false, "merchantSuppliedPrice": {"featureMap": {"sku_bulk_discount": "10.000"}, "postageCostPrice": null, "supplier": "merchant", "productPrice": {"quotePrice": {"amount": {"$ref": "$.body.onlineContext.subInvokes.com\\.alibaba\\.global\\.ic\\.api\\.CustomerProductServiceFacade\\@queryProduct\\(ProductQueryRequest\\)[0].response.model[0].skuIdAndSkuMap.*****************.priceDTO.countryPriceList[0].promotionPrice"}, "featureMap": null, "rate": null, "amountDisplay": null}, "intentionPrice": null, "featureMap": null, "taxFeeIntentionPrice": null, "belongTo": null, "taxFeeQuotePrice": {"amount": {"number": 107, "monetaryContext": {"$ref": "$.body.onlineContext.subInvokes.com\\.alibaba\\.aliexpress\\.price\\.api\\.common\\.client\\.product\\.ProductPriceQueryClient\\@queryBatchProductPrice\\(BatchProductPriceQueryParamDTO\\)[0].response.data.productPriceQueryResultMap.****************.skuIdAndSkuPriceQueryResultMap.*****************.productSkuRegionPrice.originalSuppliedPriceSummary.platformSuppliedPrice.productPrice.taxFeeQuotePrice.amount.monetaryContext"}, "currency": {"$ref": "$.body.onlineContext.subInvokes.com\\.alibaba\\.global\\.ic\\.api\\.CustomerProductServiceFacade\\@queryProduct\\(ProductQueryRequest\\)[0].response.model[0].skuIdAndSkuMap.*****************.priceDTO.promotionPrice.price.currency"}}, "featureMap": null, "rate": 0.2, "amountDisplay": null}}}}}, "skuId": "*****************"}}}}, "errorCode": null}, "errorCode": null, "errorMsg": null}, "rpcContext": null, "serviceName": "com.alibaba.aliexpress.price.api.common.client.product.ProductPriceQueryClient", "showIdentity": "com.alibaba.aliexpress.price.api.common.client.product.ProductPriceQueryClient@queryBatchProductPrice(BatchProductPriceQueryParamDTO)", "showInvokeIdentity": null, "stackTrace": "com.alibaba.aliexpress.price.client.common.service.impl.product.ProductPriceQueryClientImpl.queryBatchProductPrice@-1\ncom.alibaba.business.ae.ability.general.trade.utils.PriceUtils.queryPrice@466\ncom.alibaba.business.ae.ability.general.trade.utils.PriceUtils.invokePriceNewSdk@226\ncom.alibaba.business.ae.ability.general.trade.activity.common.order.enrich.AeOrderEnrichExt.enrichOrderBaseInfo@762\ncom.alibaba.business.trade.general.ability.order.common.enrich.GeneralTradeOrderBaseInfoEnrichAbility.lambda$enrichOrderBaseInfo$0@35\ncom.alibaba.tmf.function.specific.impl.ExtensionJavaRunner.runFirstMatched@69\ncom.alibaba.tmf.function.specific.impl.ExtensionJavaRunner.runAllMatched@85\ncom.alibaba.tmf.function.specific.impl.RuleBaseAbility.reduceExecuteWithDetailResult@887\ncom.alibaba.tmf.function.specific.impl.RuleBaseAbility.reduceExecuteWithDetailResult@-1\ncom.alibaba.tmf.function.specific.impl.RuleBaseAbility.reduceExecute@917\ncom.alibaba.business.trade.general.ability.order.common.enrich.GeneralTradeOrderBaseInfoEnrichAbility.enrichOrderBaseInfo@33\ncom.alibaba.business.trade.general.realization.order.orderline.build.GeneralOrderLineBuildExt.internalEnrichProduct@47\ncom.alibaba.trade.function.order.ability.orderline.GlobalOrderLineBuildAbility.lambda$extInitProduct$1@71\ncom.alibaba.tmf.function.specific.impl.ExtensionJavaRunner.runFirstMatched@69\ncom.alibaba.tmf.function.specific.impl.ExtensionJavaRunner.runAllMatched@85\ncom.alibaba.tmf.function.specific.impl.RuleBaseAbility.reduceExecuteWithDetailResult@887\ncom.alibaba.tmf.function.specific.impl.RuleBaseAbility.reduceExecuteWithDetailResult@-1\ncom.alibaba.tmf.function.specific.impl.RuleBaseAbility.reduceExecute@917\ncom.alibaba.trade.function.order.ability.orderline.GlobalOrderLineBuildAbility.extInitProduct@69\ncom.alibaba.trade.function.order.ability.orderline.OrderLineBuildAbility.initProduct@36\n", "startTime": "26120001553310031", "subInvokeType": null, "throwable": null, "timeCost": "0.511ms", "type": "JAVA"}], "com.alibaba.global.ump.protocol.sdk.facade.GlobalTradePromotionMultiCalculateFacade@calculatePromotion4Checkout(GlobalTradeMultiCalculateRequest)": [{"afterExecuteParams": null, "endTime": "26120003192377930", "exceptionStackTrace": null, "extInvokeIdentity": null, "extraPrams": null, "fuzzyInvokeIdentity": null, "identity": "com.alibaba.global.ump.protocol.sdk.facade.GlobalTradePromotionMultiCalculateFacade@calculatePromotion4Checkout(GlobalTradeMultiCalculateRequest)", "index": 30, "isSerialized": null, "method": "calculatePromotion4Checkout", "params": [{"fulfillmentItemClusters": [{"receiverAddress": {"locationTreeAddressName": "Abruzzo,Chieti", "addressType": "HOME", "locationTreeAddressId": "910100010000000000-910100010001000000", "countryISOCode": "IT", "latitude": null, "addressTag": "local", "detailAddress": "<PERSON><PERSON>", "longitude": null}, "seller": {"baseInfo": {"gender": null, "userTags": null, "idNumber": null}, "userId": *********, "email": "<EMAIL>"}, "fulfillmentItems": [{"shippingFees": [{"targetId": "992d1d4ca035593a870320d8d97abf0e", "price": {"amount": 9138, "currencyCode": "USD"}}], "quantity": 1, "targetIds": null, "price": null, "packageId": "A0ca5f76b-43d3-4926-b90b-95f404d6009f", "fulfillmentService": {"serviceCode": "EMS"}, "leadingTime": 2851200, "warehouse": {"locationTreeAddressId": null, "type": "dropshipping"}, "selected": false}, {"shippingFees": [{"targetId": "992d1d4ca035593a870320d8d97abf0e", "price": {"amount": 1074, "currencyCode": "USD"}}], "quantity": 1, "targetIds": null, "price": null, "packageId": "A0ca5f76b-43d3-4926-b90b-95f404d6009f", "fulfillmentService": {"serviceCode": "CAINIAO_STANDARD"}, "leadingTime": 3024000, "warehouse": {"locationTreeAddressId": null, "type": "dropshipping"}, "selected": true}]}], "buyInfo": {"defaultReceiverAddress": {"locationTreeAddressName": "Abruzzo,Chieti", "addressType": "HOME", "locationTreeAddressId": "910100010000000000-910100010001000000", "countryISOCode": "IT", "latitude": null, "addressTag": "local", "detailAddress": "<PERSON><PERSON>", "longitude": null}, "interactionInfo": {"selectedPromotions": [], "notUsePromotionTools": []}, "sellerPromotionCodes": null, "platformPromotionCode": null}, "sourceInfo": {"systemInfo": {"appVersion": "255", "osVersion": null, "appName": "", "osType": "IOS", "platformType": "NATIVE", "appKey": "21371581", "ttid": "201200@Aliexpress_iphone_8.16.0"}, "networkInfo": {"wua": "ARdT_VFcHFYafHHLicsAEoeQUUv9HchiD+ny4pvnH9w/Kxd/EdL59+5HZdZwdsUBfO1RRL/bJxPsnKtplGEV4nNRkRzV5YZ6xUCmy/zdZTe0ReoK8d/7PY8MQ3GOkz7lx32NcsbvlN2BEHkXniRnXZyB2VJN9iqH0F6fjgNcbSrz3WzvTIod9FTIOEbzSIORYwW9nGAHFkcvlOnZv2wEMvlDvu94TI5XhBFpBdCAs0bCtLQQgz95W9+e2YrieNHozG7x88iYqeVvFfRTHkht0aYvXs4782MGuWK5Am3p/xbjVPGl8YveoposVOJjjKEChzJzaTdMuZ6C/HnH1j0A6VmuNIsmAHkk2OZGoHTVZkjEHIjC3S6Lx8DEK2WRou7MYm05n", "clientIp": "*************", "cookieId": "YIeGkaCk/4EDAHxDYnmBQkxK", "userAgent": null, "sessionId": null, "umidToken": "2ztLfkFLOljE8jV5FxCtVImGSy9U2Qme", "ua": "ARdT_VFcHFYafHHLicsAEoeQUUv9HchiD+ny4pvnH9w/Kxd/EdL59+5HZdZwdsUBfO1RRL/bJxPsnKtplGEV4nNRkRzV5YZ6xUCmy/zdZTe0ReoK8d/7PY8MQ3GOkz7lx32NcsbvlN2BEHkXniRnXZyB2VJN9iqH0F6fjgNcbSrz3WzvTIod9FTIOEbzSIORYwW9nGAHFkcvlOnZv2wEMvlDvu94TI5XhBFpBdCAs0bCtLQQgz95W9+e2YrieNHozG7x88iYqeVvFfRTHkht0aYvXs4782MGuWK5Am3p/xbjVPGl8YveoposVOJjjKEChzJzaTdMuZ6C/HnH1j0A6VmuNIsmAHkk2OZGoHTVZkjEHIjC3S6Lx8DEK2WRou7MYm05n", "miniWua": "ARdT_VFcHFYafHHLicsAEoeQUUv9HchiD+ny4pvnH9w/Kxd/EdL59+5HZdZwdsUBfO1RRL/bJxPsnKtplGEV4nNRkRzV5YZ6xUCmy/zdZTe0ReoK8d/7PY8MQ3GOkz7lx32NcsbvlN2BEHkXniRnXZyB2VJN9iqH0F6fjgNcbSrz3WzvTIod9FTIOEbzSIORYwW9nGAHFkcvlOnZv2wEMvlDvu94TI5XhBFpBdCAs0bCtLQQgz95W9+e2YrieNHozG7x88iYqeVvFfRTHkht0aYvXs4782MGuWK5Am3p/xbjVPGl8YveoposVOJjjKEChzJzaTdMuZ6C/HnH1j0A6VmuNIsmAHkk2OZGoHTVZkjEHIjC3S6Lx8DEK2WRou7MYm05n"}, "extraParams": {}, "deviceInfo": {"deviceType": "MOBILE", "name": null, "id": "x86_64"}, "localeInfo": {"countryCode": "US", "language": "en", "currency": null, "locale": {"country": "en", "variant": "", "language": "en"}}}, "extendParam": {"extensionDTOMap": {"/fulfillmentItemClusters/fulfillmentItems/$A0ca5f76b-43d3-4926-b90b-95f404d6009f#CAINIAO_STANDARD": {"serviceCode": "CAINIAO_STANDARD", "packageId": "A0ca5f76b-43d3-4926-b90b-95f404d6009f", "attributes": {"supportCod": "false", "deliveryProviderName": "AliExpress Standard Shipping", "displayName": "AliExpress Standard Shipping", "sendGoodsCountryCode": "CN", "calculation_ShippingFeeUnitCount": "0.100", "freightAmountStr": "Shipping: {0}", "provider": "cainiao", "LOGISTICS_ALL_TAGS": "{\"discountForCainiaoSettlement\":\"9\"}", "hbaService": "false", "deliveryServicePudoDate": "2021-06-01", "deliveryDate": "2021-06-01", "hbaFullMailLine": "false", "featuresForTrade": "", "guaranteedDeliveryTime": "75", "freightCommitDay": "75", "ITEM_ID": "****************", "savedShippingCost": "USD 0.00", "deliveryDateStr": "Estimated delivery on <font color='#000000'>Jun 01</font>", "deliveryCanTracking": "true", "shippingToStr": "To <font color='#000000'>Italy</font> via AliExpress Standard Shipping", "rateAddressLevel": "COUNTRY", "isTracked": "true", "shippingFee": "USD 10.74", "deliveryDateHelpStr": "If you finish the payment today, your order will arrive within the estimated delivery time.", "shippingFeeDiscount": "0", "deliveryProviderCode": "CAINIAO_STANDARD", "accurateFee": "false"}, "uniqueId": "A0ca5f76b-43d3-4926-b90b-95f404d6009f#CAINIAO_STANDARD"}, "/tradeItemClusters/tradeItems/skus/$992d1d4ca035593a870320d8d97abf0e": {"targetId": "992d1d4ca035593a870320d8d97abf0e", "priceTag": 2, "attributes": {}, "priceDetailDTO": {"originalPriceTaxFee": {"amount": 11100, "currencyCode": "USD"}, "postageCostTaxFeeAmountTargetId": null, "postageCostAmount": null, "originalPriceTaxFeeTargetId": "tax_origin_prex_992d1d4ca035593a870320d8d97abf0e", "salePriceTaxFee": {"amount": 10700, "currencyCode": "USD"}, "salePriceTaxFeeTargetId": "tax_sale_prex_992d1d4ca035593a870320d8d97abf0e", "postageCostAmountTargetId": null, "postageCostTaxFeeAmount": null}}, "/fulfillmentItemClusters/fulfillmentItems/$A0ca5f76b-43d3-4926-b90b-95f404d6009f#EMS": {"serviceCode": "EMS", "packageId": "A0ca5f76b-43d3-4926-b90b-95f404d6009f", "attributes": {"supportCod": "false", "deliveryProviderName": "EMS", "displayName": "EMS", "sendGoodsCountryCode": "CN", "calculation_ShippingFeeUnitCount": "0.100", "freightAmountStr": "Shipping: {0}", "search_calculate_delivery_days": "33", "provider": "ae_config", "search_calculate_strategy": "OTHER", "LOGISTICS_ALL_TAGS": "{\"discountForCainiaoSettlement\":\"9\"}", "hbaService": "false", "hbaFullMailLine": "false", "featuresForTrade": "", "guaranteedDeliveryTime": "75", "freightCommitDay": "75", "ITEM_ID": "****************", "savedShippingCost": "USD 0.00", "deliveryCanTracking": "true", "shippingToStr": "To <font color='#000000'>Italy</font> via EMS", "rateAddressLevel": "COUNTRY", "isTracked": "true", "shippingFee": "USD 91.38", "shippingFeeDiscount": "0", "deliveryProviderCode": "EMS", "accurateFee": "false"}, "uniqueId": "A0ca5f76b-43d3-4926-b90b-95f404d6009f#EMS"}, "/buyer": {"attributes": {"$ref": "$.body.onlineContext.subInvokes.com\\.alibaba\\.global\\.ump\\.protocol\\.sdk\\.facade\\.GlobalTradePromotionMultiCalculateFacade\\@calculateItemPromotion4Checkout\\(GlobalTradeMultiCalculateRequest\\)[0].null[0].extendParam.extensionDTOMap.\\/buyer.attributes"}}, "/": {"attributes": {"saasRegion": null}, "adjustPriceInfo": null, "tradeOrders": null}}}, "routingInfo": {"sellerId": null, "buyerId": null}, "invokeInfo": {"appName": null}, "exchangeRates": [{"quoteCurrency": "USD", "exchangeRate": 1, "intentionCurrency": "USD"}], "tradeItemClusters": [{"seller": {"baseInfo": {"gender": null, "userTags": null, "idNumber": null}, "userId": *********, "email": "<EMAIL>"}, "tradeItems": [{"virtual": false, "quantity": 1, "skus": [{"quantity": 1, "targetIds": ["992d1d4ca035593a870320d8d97abf0e"], "price": {"amount": 69597, "currencyCode": "USD"}, "inventoryChannelInfos": null, "prices": {"salePrice": {"amount": 67089, "currencyCode": "USD"}, "retailPrice": {"amount": 69597, "currencyCode": "USD"}}, "skuTags": [82052, 110572], "skuId": "*****************", "selected": true}], "itemTags": [82052], "channelInfo": null, "targetIds": ["992d1d4ca035593a870320d8d97abf0e"], "saleable": true, "categoryInfo": {"categoryIds": [5090301, 509], "leafCategoryId": 5090301, "rootCategoryId": 509}, "itemId": ****************, "price": {"amount": 69597, "currencyCode": "USD"}, "brandId": 200658765, "shardingId": null, "selected": true}], "store": null}], "itemRelations": [], "buyer": {"baseInfo": null, "userMemberInfos": [{"memberLevel": 0, "domain": null, "startTime": 0, "endTime": 0, "memberTags": null}], "userId": **********, "email": "<EMAIL>"}}], "paramsInstType": null, "paramsType": ["com.alibaba.global.ump.protocol.sdk.model.request.GlobalTradeMultiCalculateRequest"], "relatedCollectIndex": 0, "response": {"result": {"features": {"kernel": "1"}, "itemPromotions": [{"promotionResults": [], "features": {}, "displayResults": [{"features": {"quoteDiscount": 1342, "promotionSubScopeMap": "{\"orderLineId2SubDetailIdList\":{\"992d1d4ca035593a870320d8d97abf0e\":[\"originalPrice_992d1d4ca035593a870320d8d97abf0e\",\"tax_origin_prex_992d1d4ca035593a870320d8d97abf0e\"]}}", "ae_site_channel": "500000001213073", "marketPlace": "ALIEXPRESS", "ae_payTimeLimit": "86400000", "srcApp": "global-campaign-merchants-base-s", "b_participateType": "1", "intentionDiscount": 1342, "b_activityScope": "1", "participateType": "1", "consKey": "true", "calcM": "1", "sku": "*****************", "b_campaignLevel": "6"}, "displayType": "TEASING", "promotionTips": [], "budgets": [], "itemRelation": null, "promotionScope": {"targetIds": ["992d1d4ca035593a870320d8d97abf0e"]}, "inventoryChannelInfo": {"channelStock": null, "selected": false, "channelCode": "500000001213073"}, "promotion": {"platformOwner": true, "toolCode": "proEngine", "promotionType": "ITEM", "sellerId": *********, "effectiveTime": {"teasingStartTime": 1619359200000, "startTime": 1619625600000, "endTime": 1620230400000}, "name": "ump预热测试", "params": {"p_discountType": "2", "exchangeRate": "USD(QUOTE)_USD(INTENTION)_1", "p_quoteCurrency": "USD", "buyLimitRuleType": "3", "aeNewFlow": "1", "p_calculateCurrency": "USD", "p_discountRate_off": "20", "buyLimitRuleName": "lmtBuyerActivityItem", "p_intentionCurrency": "USD"}, "promotionItemType": "TRADE_ITEM", "version": 1, "promotionUniqueId": {"activityId": 1907423001, "promGroupType": 1, "creatorId": 1, "promotionCode": null, "idValue": "proEngine-1907423001_5000007562825014", "promotionId": 5000007562825014, "promGroupId": "992d1d4ca035593a870320d8d97abf0e"}}}], "groupId": "992d1d4ca035593a870320d8d97abf0e"}, {"promotionResults": [{"features": {}, "promotionResultDetails": [{"features": {"promotionOrderLine2NumMap": "{\"992d1d4ca035593a870320d8d97abf0e\":1}", "promotionSubScopeMap": "{\"orderLineId2SubDetailIdList\":{\"992d1d4ca035593a870320d8d97abf0e\":[\"originalPrice_992d1d4ca035593a870320d8d97abf0e\",\"tax_origin_prex_992d1d4ca035593a870320d8d97abf0e\"]}}", "isPromotionUniqueIdDowngraded": "true", "calcM": "1", "originPromotionId": 1, "umpMockTargetShareMoneyMap": "{\"tax_origin_prex_992d1d4ca035593a870320d8d97abf0e\":\"{\\\"currency\\\":\\\"USD\\\",\\\"price\\\":400}\",\"originalPrice_992d1d4ca035593a870320d8d97abf0e\":\"{\\\"currency\\\":\\\"USD\\\",\\\"price\\\":2108}\"}"}, "budgets": [], "priceResult": {"intentionSponsorDetails": [{"targetId": "992d1d4ca035593a870320d8d97abf0e", "discounts": [{"amount": {"amount": 2508, "currencyCode": "USD"}, "sponsorType": "SELLER"}]}], "discount": {"sharedAmounts": [{"amount": {"amount": 2508, "currencyCode": "USD"}, "targetId": "992d1d4ca035593a870320d8d97abf0e"}], "platformSponsoredSharedAmounts": []}, "sponsorDetails": [{"targetId": "992d1d4ca035593a870320d8d97abf0e", "discounts": [{"amount": {"amount": 2508, "currencyCode": "USD"}, "sponsorType": "SELLER"}]}], "intentionDiscount": {"sharedAmounts": [{"amount": {"amount": 2508, "currencyCode": "USD"}, "targetId": "992d1d4ca035593a870320d8d97abf0e"}], "platformSponsoredSharedAmounts": []}}, "benefitResult": null, "itemRelation": null, "promotionScope": {"targetIds": ["992d1d4ca035593a870320d8d97abf0e"]}, "inventoryChannelInfo": null, "promotion": {"platformOwner": true, "toolCode": "mockCoveredShippingSalePrice", "promotionType": "ITEM", "sellerId": 1, "effectiveTime": {"teasingStartTime": -1, "startTime": 1619609661948, "endTime": 1619616861948}, "name": "mockCoveredShippingSalePrice", "params": {"p_currencyConfig": "{\"default\":\"USD\",\"USD\":{\"p_decreaseMoney\":\"2508\"}}", "p_activityCurrency": "USD", "exchangeRate": "USD(QUOTE)_USD(INTENTION)_1,USD(QUOTE)_USD(ACTIVITY)_1", "p_quoteCurrency": "USD", "aeNewFlow": "1", "p_calculateCurrency": "USD", "p_decreaseMoney": "2508", "p_intentionCurrency": "USD"}, "promotionItemType": "TRADE_ITEM", "version": 0, "promotionUniqueId": {"activityId": 1, "promGroupType": 2, "creatorId": 1, "promotionCode": null, "idValue": "mockCoveredShippingSalePrice-1_1", "promotionId": 52, "promGroupId": "s_*********_240"}}}], "selected": true}], "features": {}, "displayResults": [], "groupId": "s_*********_240"}], "shopPromotions": [], "acrossPromotions": []}, "success": true, "errorCode": null}, "rpcContext": null, "serviceName": "com.alibaba.global.ump.protocol.sdk.facade.GlobalTradePromotionMultiCalculateFacade", "showIdentity": "com.alibaba.global.ump.protocol.sdk.facade.GlobalTradePromotionMultiCalculateFacade@calculatePromotion4Checkout(GlobalTradeMultiCalculateRequest)", "showInvokeIdentity": null, "stackTrace": "com.alibaba.global.satellite.proxy.hsf.utils.hook.SatelliteHSFConsumerProxyHook.invoke@92\ncom.alibaba.global.satellite.proxy.hsf.utils.hook.SatelliteHSFConsumerProxyHook.invoke@29\ncom.alibaba.global.satellite.utils.SentinelUtils.invoke@37\ncom.alibaba.global.satellite.proxy.hsf.utils.hook.SatelliteHSFConsumerProxyHook.proxy@58\ncom.alibaba.global.common.proxy.AbstractProxyHandler.invoke@63\ncom.alibaba.global.common.proxy.JdkProxy$Handler.invoke@66\ncom.alibaba.trade.function.promotion.repository.protocol.GlobalMultiTradePromotionRepositoryImpl.invokeCheckoutCalculate@203\ncom.alibaba.trade.function.promotion.repository.protocol.GlobalMultiTradePromotionRepositoryImpl.calculateMulti@345\ncom.alibaba.trade.function.promotion.repository.PromotionRepositoryImpl.calculateMulti@389\ncom.alibaba.trade.function.promotion.service.GlobalTradePromotionQueryDomainServiceImpl.calculateMulti@276\ncom.alibaba.global.buy.activities.promotion.PromotionMultiCalculateActivity.execute@76\ncom.alibaba.smart.framework.engine.modules.smart.provider.performer.JavaPerformer.perform@56\ncom.alibaba.smart.framework.engine.provider.impl.ComboInvoker.invoke@27\ncom.alibaba.smart.framework.engine.pvm.impl.AbstractPvmElement.invoke@47\ncom.alibaba.smart.framework.engine.provider.impl.DefaultExecutePolicyBehavior.execute@63\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmActivity.execute@59\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmActivity.enter@53\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmTransition.execute@43\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmActivity.executeRecursively@103\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmActivity.execute@68\n", "startTime": "26120001957166334", "subInvokeType": "rpc", "throwable": null, "timeCost": "1235.212ms", "type": "JAVA"}], "com.alibaba.global.inventory.protocol.sdk.facade.GlobalInventoryTradeFacade@sourcingInventory(SourcingInventoryRequest)": [{"afterExecuteParams": null, "endTime": "26120003202899799", "exceptionStackTrace": null, "extInvokeIdentity": null, "extraPrams": null, "fuzzyInvokeIdentity": null, "identity": "com.alibaba.global.inventory.protocol.sdk.facade.GlobalInventoryTradeFacade@sourcingInventory(SourcingInventoryRequest)", "index": 31, "isSerialized": null, "method": "sourcingInventory", "params": [{"sourcingInventoryOption": {"receiverAddressDTO": {"countryISOcode": "IT", "locationTreeAddressId": "910100010000000000-910100010001000000"}}, "extendParam": {"extensionDTOMap": {"/sourcingInventorySkus/$*****************": {"scItemId": null, "skuId": "*****************"}}}, "routingInfo": {"sellerId": null, "buyerId": **********}, "sourcingInventorySkus": [{"sellerId": *********, "demandQuantity": 1, "sourcingInventorySkuOption": {"warehouseGroupId": "*****************", "startTime": null, "endTime": null, "channelCode": null}, "skuId": "*****************"}], "invokeInfo": {"appName": "global-trade-center-s"}, "invokeScene": "PLACE_ORDER"}], "paramsInstType": null, "paramsType": ["com.alibaba.global.inventory.protocol.sdk.model.request.SourcingInventoryRequest"], "relatedCollectIndex": 0, "response": {"result": {"features": {}, "sourcingInventorySkus": [{"warehouseGroupId": "*****************", "preferWarehouseDTO": {"warehouseOwnerId": *********, "warehouseType": "dropshipping", "warehouseAddressDTO": {"locationTreeAddressName": null, "locationTreeAddressId": "", "latitude": null, "detailAddress": null, "addressId": null, "longitude": null}, "warehouseCode": "ae_marketplace"}, "features": {"oldInventoryType": 1000, "oldPreferWarehouseType": "ae_marketplace"}, "inventoryType": 100, "inventoryLines": [{"features": {"invSnapshot": "0^1_1^*****************_2^5000000003550838547"}, "expireTime": null, "estimateInboundTime": null, "warehouseDTO": {"warehouseOwnerId": null, "warehouseType": "dropshipping", "warehouseAddressDTO": null, "warehouseCode": "ae_marketplace"}, "batchCode": null, "activeTime": null, "sellableQuantity": 1}], "invSnapshot": "0^1_1^*****************_2^5000000003550838547", "preferSellableQuantity": 1, "maxSellableQuantity": 1, "channelInventoryLine": null, "skuId": "*****************"}]}, "success": true, "errorCode": null}, "rpcContext": null, "serviceName": "com.alibaba.global.inventory.protocol.sdk.facade.GlobalInventoryTradeFacade", "showIdentity": "com.alibaba.global.inventory.protocol.sdk.facade.GlobalInventoryTradeFacade@sourcingInventory(SourcingInventoryRequest)", "showInvokeIdentity": null, "stackTrace": "com.alibaba.global.satellite.proxy.hsf.utils.hook.SatelliteHSFConsumerProxyHook.invoke@92\ncom.alibaba.global.satellite.proxy.hsf.utils.hook.SatelliteHSFConsumerProxyHook.invoke@29\ncom.alibaba.global.satellite.utils.SentinelUtils.invoke@37\ncom.alibaba.global.satellite.proxy.hsf.utils.hook.SatelliteHSFConsumerProxyHook.proxy@58\ncom.alibaba.global.common.proxy.AbstractProxyHandler.invoke@63\ncom.alibaba.global.common.proxy.JdkProxy$Handler.invoke@66\ncom.alibaba.trade.function.inventory.repository.protocol.GlobalInventoryTradeRepositoryImpl.query@45\ncom.alibaba.trade.function.inventory.repository.InventoryRepositoryImpl.query@63\ncom.alibaba.trade.function.inventory.service.TradeInventoryModelDomainServiceImpl.query@62\ncom.alibaba.trade.scenario.activity.shopping.inventory.ShoppingInventoryQueryActivity.callDomainService@48\ncom.alibaba.trade.scenario.activity.shopping.inventory.ShoppingInventoryQueryActivity.callDomainService@23\ncom.alibaba.trade.scenario.activity.BaseTradeActivity.execute@37\ncom.alibaba.global.buy.activities.inventory.InventoryQueryActivityV2.execute@35\ncom.alibaba.smart.framework.engine.modules.smart.provider.performer.JavaPerformer.perform@56\ncom.alibaba.smart.framework.engine.provider.impl.ComboInvoker.invoke@27\ncom.alibaba.smart.framework.engine.pvm.impl.AbstractPvmElement.invoke@47\ncom.alibaba.smart.framework.engine.provider.impl.DefaultExecutePolicyBehavior.execute@63\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmActivity.execute@59\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmActivity.enter@53\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmTransition.execute@43\n", "startTime": "26120003193720344", "subInvokeType": "rpc", "throwable": null, "timeCost": "9.179ms", "type": "JAVA"}], "com.alibaba.global.category.api.StdCategoryService@getStdCategory(Long,Locale)": [{"afterExecuteParams": null, "endTime": "26120001161747557", "exceptionStackTrace": null, "extInvokeIdentity": null, "extraPrams": null, "fuzzyInvokeIdentity": null, "identity": "com.alibaba.global.category.api.StdCategoryService@getStdCategory(Long,Locale)", "index": 2, "isSerialized": null, "method": "getStdCategory", "params": [5090301, {"country": "en", "variant": "", "language": "en"}], "paramsInstType": null, "paramsType": ["java.lang.Long", "java.util.Locale"], "relatedCollectIndex": 0, "response": {"firstLevelCategoryId": 509, "isRoot": false, "featureKeySet": ["AE_POST_TAGS", "AE_UPLOAD_VIDEO", "fixedcp", "AE_ISRECOMMENDVISIBLEB", "AE_LEADTIME", "MSR_economic_operator"], "pathList": [{"firstLevelCategoryId": 509, "isRoot": false, "allFeatures": [{"hierarchy": false, "value": "Y", "key": "AE_UPLOAD_VIDEO"}, {"hierarchy": false, "value": "true", "key": "AE_ISRECOMMENDVISIBLEB"}, {"hierarchy": false, "value": "7", "key": "AE_LEADTIME"}, {"hierarchy": false, "value": "2021-01-06 14:22:29", "key": "catUpTime"}], "featureKeySet": ["AE_UPLOAD_VIDEO", "AE_ISRECOMMENDVISIBLEB", "AE_LEADTIME", "catUpTime"], "name": "Phones & Telecommunications", "pathList": null, "isLeaf": false, "nameLocal": "Phones & Telecommunications", "parentId": 0, "categoryId": 509, "featureDO": null, "status": 0}, {"firstLevelCategoryId": 509, "isRoot": false, "allFeatures": [{"hierarchy": false, "value": "wtl", "key": "AE_POST_TAGS"}, {"hierarchy": false, "value": "Y", "key": "AE_UPLOAD_VIDEO"}, {"hierarchy": false, "value": "1", "key": "fixedcp"}, {"hierarchy": false, "value": "true", "key": "AE_ISRECOMMENDVISIBLEB"}, {"hierarchy": false, "value": "30", "key": "AE_LEADTIME"}, {"hierarchy": true, "value": "YES", "key": "MSR_economic_operator"}], "featureKeySet": ["AE_POST_TAGS", "AE_UPLOAD_VIDEO", "fixedcp", "AE_ISRECOMMENDVISIBLEB", "AE_LEADTIME", "MSR_economic_operator"], "name": "Mobile Phones", "pathList": null, "isLeaf": true, "nameLocal": "Mobile Phones", "parentId": 509, "categoryId": 5090301, "featureDO": null, "status": 0}], "isLeaf": true, "parentId": 509, "featureDO": null, "allFeatures": [{"hierarchy": false, "value": "wtl", "key": "AE_POST_TAGS"}, {"hierarchy": false, "value": "Y", "key": "AE_UPLOAD_VIDEO"}, {"hierarchy": false, "value": "1", "key": "fixedcp"}, {"hierarchy": false, "value": "true", "key": "AE_ISRECOMMENDVISIBLEB"}, {"hierarchy": false, "value": "30", "key": "AE_LEADTIME"}, {"hierarchy": true, "value": "YES", "key": "MSR_economic_operator"}], "name": "Mobile Phones", "nameLocal": "Mobile Phones", "categoryId": 5090301, "status": 0}, "rpcContext": null, "serviceName": "com.alibaba.global.category.api.StdCategoryService", "showIdentity": "com.alibaba.global.category.api.StdCategoryService@getStdCategory(Long,Locale)", "showInvokeIdentity": null, "stackTrace": "com.alibaba.global.category.StandardCategoryClient.getStdCategory@-1\ncom.alibaba.global.common.proxy.AbstractProxyHandler.invoke@68\ncom.alibaba.global.common.proxy.CglibProxy$Handler.intercept@103\ncom.alibaba.global.category.StandardCategoryClient$$EnhancerByCGLIB$$3db20e51.getStdCategory@-1\ncom.alibaba.trade.function.product.repository.CategoryRepositoryImpl.getStdCatDefinition@128\ncom.alibaba.trade.function.product.repository.CategoryRepositoryImpl.queryById@47\ncom.alibaba.trade.function.product.converter.ProductConverter.getCategory@222\ncom.alibaba.trade.function.product.converter.ProductConverter.toProduct@105\ncom.alibaba.trade.function.product.repository.ProductRepositoryImpl.query@62\ncom.alibaba.trade.scenario.build.common.BaseProductBuilder.build@38\ncom.alibaba.trade.scenario.build.RequestBuilderFactory.lambda$executeAll$1@56\ncom.alibaba.trade.scenario.build.RequestBuilderFactory.executeAll@56\ncom.alibaba.global.buy.request.ShoppingRequest.createShoppingRequest@108\ncom.alibaba.global.buy.request.ShoppingRequest.of@90\ncom.alibaba.global.buy.api.facade.protocol.GlobalCheckoutFacadeImpl$1._execute@81\ncom.alibaba.global.buy.api.facade.protocol.GlobalCheckoutFacadeImpl$1._execute@69\ncom.alibaba.tmf.session.OnceInvoker.invoke@42\ncom.alibaba.global.buy.api.facade.protocol.GlobalCheckoutFacadeImpl.renderOrder@102\ncom.alibaba.tmf.scenario.ServiceEntranceAspect.doAround@39\ncom.alibaba.ae.trade.adaptor.checkout.AECheckoutMultiterminalFacadeImpl.renderOrder@65\n", "startTime": "26120001161480960", "subInvokeType": null, "throwable": null, "timeCost": "0.267ms", "type": "JAVA"}, {"afterExecuteParams": null, "endTime": "26120001161971550", "exceptionStackTrace": null, "extInvokeIdentity": null, "extraPrams": null, "fuzzyInvokeIdentity": null, "identity": "com.alibaba.global.category.api.StdCategoryService@getStdCategory(Long,Locale)", "index": 3, "isSerialized": null, "method": "getStdCategory", "params": [5090301, {"country": "en", "variant": "", "language": "en"}], "paramsInstType": null, "paramsType": ["java.lang.Long", "java.util.Locale"], "relatedCollectIndex": 0, "response": {"firstLevelCategoryId": 509, "isRoot": false, "featureKeySet": ["AE_POST_TAGS", "AE_UPLOAD_VIDEO", "fixedcp", "AE_ISRECOMMENDVISIBLEB", "AE_LEADTIME", "MSR_economic_operator"], "pathList": [{"firstLevelCategoryId": 509, "isRoot": false, "allFeatures": [{"hierarchy": false, "value": "Y", "key": "AE_UPLOAD_VIDEO"}, {"hierarchy": false, "value": "true", "key": "AE_ISRECOMMENDVISIBLEB"}, {"hierarchy": false, "value": "7", "key": "AE_LEADTIME"}, {"hierarchy": false, "value": "2021-01-06 14:22:29", "key": "catUpTime"}], "featureKeySet": ["AE_UPLOAD_VIDEO", "AE_ISRECOMMENDVISIBLEB", "AE_LEADTIME", "catUpTime"], "name": "Phones & Telecommunications", "pathList": null, "isLeaf": false, "nameLocal": "Phones & Telecommunications", "parentId": 0, "categoryId": 509, "featureDO": null, "status": 0}, {"firstLevelCategoryId": 509, "isRoot": false, "allFeatures": [{"hierarchy": false, "value": "wtl", "key": "AE_POST_TAGS"}, {"hierarchy": false, "value": "Y", "key": "AE_UPLOAD_VIDEO"}, {"hierarchy": false, "value": "1", "key": "fixedcp"}, {"hierarchy": false, "value": "true", "key": "AE_ISRECOMMENDVISIBLEB"}, {"hierarchy": false, "value": "30", "key": "AE_LEADTIME"}, {"hierarchy": true, "value": "YES", "key": "MSR_economic_operator"}], "featureKeySet": ["AE_POST_TAGS", "AE_UPLOAD_VIDEO", "fixedcp", "AE_ISRECOMMENDVISIBLEB", "AE_LEADTIME", "MSR_economic_operator"], "name": "Mobile Phones", "pathList": null, "isLeaf": true, "nameLocal": "Mobile Phones", "parentId": 509, "categoryId": 5090301, "featureDO": null, "status": 0}], "isLeaf": true, "parentId": 509, "featureDO": null, "allFeatures": [{"hierarchy": false, "value": "wtl", "key": "AE_POST_TAGS"}, {"hierarchy": false, "value": "Y", "key": "AE_UPLOAD_VIDEO"}, {"hierarchy": false, "value": "1", "key": "fixedcp"}, {"hierarchy": false, "value": "true", "key": "AE_ISRECOMMENDVISIBLEB"}, {"hierarchy": false, "value": "30", "key": "AE_LEADTIME"}, {"hierarchy": true, "value": "YES", "key": "MSR_economic_operator"}], "name": "Mobile Phones", "nameLocal": "Mobile Phones", "categoryId": 5090301, "status": 0}, "rpcContext": null, "serviceName": "com.alibaba.global.category.api.StdCategoryService", "showIdentity": "com.alibaba.global.category.api.StdCategoryService@getStdCategory(Long,Locale)", "showInvokeIdentity": null, "stackTrace": "com.alibaba.global.category.StandardCategoryClient.getStdCategory@-1\ncom.alibaba.global.common.proxy.AbstractProxyHandler.invoke@68\ncom.alibaba.global.common.proxy.CglibProxy$Handler.intercept@103\ncom.alibaba.global.category.StandardCategoryClient$$EnhancerByCGLIB$$3db20e51.getStdCategory@-1\ncom.alibaba.trade.function.product.repository.CategoryRepositoryImpl.getRootCategoryId@136\ncom.alibaba.trade.function.product.repository.CategoryRepositoryImpl.queryById@54\ncom.alibaba.trade.function.product.converter.ProductConverter.getCategory@222\ncom.alibaba.trade.function.product.converter.ProductConverter.toProduct@105\ncom.alibaba.trade.function.product.repository.ProductRepositoryImpl.query@62\ncom.alibaba.trade.scenario.build.common.BaseProductBuilder.build@38\ncom.alibaba.trade.scenario.build.RequestBuilderFactory.lambda$executeAll$1@56\ncom.alibaba.trade.scenario.build.RequestBuilderFactory.executeAll@56\ncom.alibaba.global.buy.request.ShoppingRequest.createShoppingRequest@108\ncom.alibaba.global.buy.request.ShoppingRequest.of@90\ncom.alibaba.global.buy.api.facade.protocol.GlobalCheckoutFacadeImpl$1._execute@81\ncom.alibaba.global.buy.api.facade.protocol.GlobalCheckoutFacadeImpl$1._execute@69\ncom.alibaba.tmf.session.OnceInvoker.invoke@42\ncom.alibaba.global.buy.api.facade.protocol.GlobalCheckoutFacadeImpl.renderOrder@102\ncom.alibaba.tmf.scenario.ServiceEntranceAspect.doAround@39\ncom.alibaba.ae.trade.adaptor.checkout.AECheckoutMultiterminalFacadeImpl.renderOrder@65\n", "startTime": "26120001161774987", "subInvokeType": null, "throwable": null, "timeCost": "0.197ms", "type": "JAVA"}], "com.alibaba.trade.shared.utils.SequenceGenerator@next(String)": [{"afterExecuteParams": null, "endTime": "26120001205956698", "exceptionStackTrace": null, "extInvokeIdentity": null, "extraPrams": null, "fuzzyInvokeIdentity": null, "identity": "com.alibaba.trade.shared.utils.SequenceGenerator@next(String)", "index": 10, "isSerialized": null, "method": "next", "params": ["com.alibaba.trade.shared.order.OrderLine"], "paramsInstType": null, "paramsType": ["java.lang.String"], "relatedCollectIndex": 0, "response": 90, "rpcContext": null, "serviceName": "com.alibaba.trade.shared.utils.SequenceGenerator", "showIdentity": "com.alibaba.trade.shared.utils.SequenceGenerator@next(String)", "showInvokeIdentity": null, "stackTrace": "com.alibaba.trade.shared.utils.SequenceGenerator.next@-1\ncom.alibaba.trade.shared.order.OrderLine.<init>@199\ncom.alibaba.global.buy.request.builder.impl.OrderBuilder.buildTradeOrderLine@121\ncom.alibaba.global.buy.request.builder.impl.OrderBuilder.buildTradeOrderLines@356\ncom.alibaba.global.buy.request.builder.impl.OrderBuilder.buildTradeOrderLines@322\ncom.alibaba.global.buy.request.builder.impl.OrderBuilder.buildTradeOrder@275\ncom.alibaba.global.buy.request.builder.impl.OrderBuilder.lambda$build$0@70\ncom.alibaba.global.buy.request.builder.impl.OrderBuilder.build@72\ncom.alibaba.global.buy.request.builder.impl.OrderBuilder.build@57\ncom.alibaba.trade.scenario.build.RequestBuilderFactory.lambda$executeAll$1@56\ncom.alibaba.trade.scenario.build.RequestBuilderFactory.executeAll@56\ncom.alibaba.global.buy.request.ShoppingRequest.createShoppingRequest@108\ncom.alibaba.global.buy.request.ShoppingRequest.of@90\ncom.alibaba.global.buy.api.facade.protocol.GlobalCheckoutFacadeImpl$1._execute@81\ncom.alibaba.global.buy.api.facade.protocol.GlobalCheckoutFacadeImpl$1._execute@69\ncom.alibaba.tmf.session.OnceInvoker.invoke@42\ncom.alibaba.global.buy.api.facade.protocol.GlobalCheckoutFacadeImpl.renderOrder@102\ncom.alibaba.tmf.scenario.ServiceEntranceAspect.doAround@39\ncom.alibaba.ae.trade.adaptor.checkout.AECheckoutMultiterminalFacadeImpl.renderOrder@65\ncom.alibaba.global.satellite.proxy.hsf.utils.hook.SatelliteHSFProviderProxyHook.invoke@68\n", "startTime": "26120001205707857", "subInvokeType": null, "throwable": null, "timeCost": "0.249ms", "type": "JAVA"}, {"afterExecuteParams": null, "endTime": "26120001206226944", "exceptionStackTrace": null, "extInvokeIdentity": null, "extraPrams": null, "fuzzyInvokeIdentity": null, "identity": "com.alibaba.trade.shared.utils.SequenceGenerator@next(String)", "index": 11, "isSerialized": null, "method": "next", "params": ["com.alibaba.trade.shared.delivery.transport.TransportMethodId"], "paramsInstType": null, "paramsType": ["java.lang.String"], "relatedCollectIndex": 0, "response": 14615, "rpcContext": null, "serviceName": "com.alibaba.trade.shared.utils.SequenceGenerator", "showIdentity": "com.alibaba.trade.shared.utils.SequenceGenerator@next(String)", "showInvokeIdentity": null, "stackTrace": "com.alibaba.trade.shared.utils.SequenceGenerator.next@-1\ncom.alibaba.trade.shared.delivery.transport.TransportMethodId.generate@23\ncom.alibaba.global.buy.request.builder.impl.OrderBuilder.buildTransportMethod@179\ncom.alibaba.global.buy.request.builder.impl.OrderBuilder.buildTradeOrderLines@377\ncom.alibaba.global.buy.request.builder.impl.OrderBuilder.buildTradeOrderLines@322\ncom.alibaba.global.buy.request.builder.impl.OrderBuilder.buildTradeOrder@275\ncom.alibaba.global.buy.request.builder.impl.OrderBuilder.lambda$build$0@70\ncom.alibaba.global.buy.request.builder.impl.OrderBuilder.build@72\ncom.alibaba.global.buy.request.builder.impl.OrderBuilder.build@57\ncom.alibaba.trade.scenario.build.RequestBuilderFactory.lambda$executeAll$1@56\ncom.alibaba.trade.scenario.build.RequestBuilderFactory.executeAll@56\ncom.alibaba.global.buy.request.ShoppingRequest.createShoppingRequest@108\ncom.alibaba.global.buy.request.ShoppingRequest.of@90\ncom.alibaba.global.buy.api.facade.protocol.GlobalCheckoutFacadeImpl$1._execute@81\ncom.alibaba.global.buy.api.facade.protocol.GlobalCheckoutFacadeImpl$1._execute@69\ncom.alibaba.tmf.session.OnceInvoker.invoke@42\ncom.alibaba.global.buy.api.facade.protocol.GlobalCheckoutFacadeImpl.renderOrder@102\ncom.alibaba.tmf.scenario.ServiceEntranceAspect.doAround@39\ncom.alibaba.ae.trade.adaptor.checkout.AECheckoutMultiterminalFacadeImpl.renderOrder@65\ncom.alibaba.global.satellite.proxy.hsf.utils.hook.SatelliteHSFProviderProxyHook.invoke@68\n", "startTime": "26120001206018119", "subInvokeType": null, "throwable": null, "timeCost": "0.209ms", "type": "JAVA"}, {"afterExecuteParams": null, "endTime": "26120001206512421", "exceptionStackTrace": null, "extInvokeIdentity": null, "extraPrams": null, "fuzzyInvokeIdentity": null, "identity": "com.alibaba.trade.shared.utils.SequenceGenerator@next(String)", "index": 12, "isSerialized": null, "method": "next", "params": ["com.alibaba.trade.shared.order.Order"], "paramsInstType": null, "paramsType": ["java.lang.String"], "relatedCollectIndex": 0, "response": 812, "rpcContext": null, "serviceName": "com.alibaba.trade.shared.utils.SequenceGenerator", "showIdentity": "com.alibaba.trade.shared.utils.SequenceGenerator@next(String)", "showInvokeIdentity": null, "stackTrace": "com.alibaba.trade.shared.utils.SequenceGenerator.next@-1\ncom.alibaba.trade.shared.order.Order.<init>@32\ncom.alibaba.global.buy.request.builder.impl.OrderBuilder.buildTradeOrder@284\ncom.alibaba.global.buy.request.builder.impl.OrderBuilder.lambda$build$0@70\ncom.alibaba.global.buy.request.builder.impl.OrderBuilder.build@72\ncom.alibaba.global.buy.request.builder.impl.OrderBuilder.build@57\ncom.alibaba.trade.scenario.build.RequestBuilderFactory.lambda$executeAll$1@56\ncom.alibaba.trade.scenario.build.RequestBuilderFactory.executeAll@56\ncom.alibaba.global.buy.request.ShoppingRequest.createShoppingRequest@108\ncom.alibaba.global.buy.request.ShoppingRequest.of@90\ncom.alibaba.global.buy.api.facade.protocol.GlobalCheckoutFacadeImpl$1._execute@81\ncom.alibaba.global.buy.api.facade.protocol.GlobalCheckoutFacadeImpl$1._execute@69\ncom.alibaba.tmf.session.OnceInvoker.invoke@42\ncom.alibaba.global.buy.api.facade.protocol.GlobalCheckoutFacadeImpl.renderOrder@102\ncom.alibaba.tmf.scenario.ServiceEntranceAspect.doAround@39\ncom.alibaba.ae.trade.adaptor.checkout.AECheckoutMultiterminalFacadeImpl.renderOrder@65\ncom.alibaba.global.satellite.proxy.hsf.utils.hook.SatelliteHSFProviderProxyHook.invoke@68\ncom.alibaba.global.satellite.proxy.hsf.utils.hook.SatelliteHSFProviderProxyHook.invoke@30\ncom.alibaba.global.satellite.utils.SentinelUtils.invoke@37\ncom.alibaba.global.satellite.proxy.hsf.utils.hook.SatelliteHSFProviderProxyHook.proxy@56\n", "startTime": "26120001206313944", "subInvokeType": null, "throwable": null, "timeCost": "0.198ms", "type": "JAVA"}, {"afterExecuteParams": null, "endTime": "26120001943089062", "exceptionStackTrace": null, "extInvokeIdentity": null, "extraPrams": null, "fuzzyInvokeIdentity": null, "identity": "com.alibaba.trade.shared.utils.SequenceGenerator@next(String)", "index": 26, "isSerialized": null, "method": "next", "params": ["com.alibaba.trade.shared.delivery.transport.TransportMethodId"], "paramsInstType": null, "paramsType": ["java.lang.String"], "relatedCollectIndex": 0, "response": 14616, "rpcContext": null, "serviceName": "com.alibaba.trade.shared.utils.SequenceGenerator", "showIdentity": "com.alibaba.trade.shared.utils.SequenceGenerator@next(String)", "showInvokeIdentity": null, "stackTrace": "com.alibaba.trade.shared.utils.SequenceGenerator.next@-1\ncom.alibaba.trade.shared.delivery.transport.TransportMethodId.generate@23\ncom.alibaba.trade.function.delivery.service.TradeDeliveryTransportMethodDomainServiceImpl.toTransportMethod@380\ncom.alibaba.trade.function.delivery.service.TradeDeliveryTransportMethodDomainServiceImpl.buildResp@280\ncom.alibaba.trade.function.delivery.service.TradeDeliveryTransportMethodDomainServiceImpl.queryTransportMethod@102\ncom.alibaba.trade.scenario.activity.shopping.delivery.ShoppingDeliveryQueryTransportMethodActivity.callDomainService@55\ncom.alibaba.trade.scenario.activity.shopping.delivery.ShoppingDeliveryQueryTransportMethodActivity.callDomainService@29\ncom.alibaba.trade.scenario.activity.BaseTradeActivity.execute@37\ncom.alibaba.global.buy.activities.delivery.TransportMethodQueryActivity.execute@45\ncom.alibaba.smart.framework.engine.modules.smart.provider.performer.JavaPerformer.perform@56\ncom.alibaba.smart.framework.engine.provider.impl.ComboInvoker.invoke@27\ncom.alibaba.smart.framework.engine.pvm.impl.AbstractPvmElement.invoke@47\ncom.alibaba.smart.framework.engine.provider.impl.DefaultExecutePolicyBehavior.execute@63\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmActivity.execute@59\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmActivity.enter@53\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmTransition.execute@43\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmActivity.executeRecursively@103\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmActivity.execute@68\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmActivity.enter@53\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmTransition.execute@43\n", "startTime": "26120001942666723", "subInvokeType": null, "throwable": null, "timeCost": "0.422ms", "type": "JAVA"}, {"afterExecuteParams": null, "endTime": "26120001943427261", "exceptionStackTrace": null, "extInvokeIdentity": null, "extraPrams": null, "fuzzyInvokeIdentity": null, "identity": "com.alibaba.trade.shared.utils.SequenceGenerator@next(String)", "index": 27, "isSerialized": null, "method": "next", "params": ["com.alibaba.trade.shared.delivery.transport.TransportMethodId"], "paramsInstType": null, "paramsType": ["java.lang.String"], "relatedCollectIndex": 0, "response": 14617, "rpcContext": null, "serviceName": "com.alibaba.trade.shared.utils.SequenceGenerator", "showIdentity": "com.alibaba.trade.shared.utils.SequenceGenerator@next(String)", "showInvokeIdentity": null, "stackTrace": "com.alibaba.trade.shared.utils.SequenceGenerator.next@-1\ncom.alibaba.trade.shared.delivery.transport.TransportMethodId.generate@30\ncom.alibaba.trade.function.delivery.service.TradeDeliveryTransportMethodDomainServiceImpl.toTransportMethod@378\ncom.alibaba.trade.function.delivery.service.TradeDeliveryTransportMethodDomainServiceImpl.buildResp@323\ncom.alibaba.trade.function.delivery.service.TradeDeliveryTransportMethodDomainServiceImpl.queryTransportMethod@102\ncom.alibaba.trade.scenario.activity.shopping.delivery.ShoppingDeliveryQueryTransportMethodActivity.callDomainService@55\ncom.alibaba.trade.scenario.activity.shopping.delivery.ShoppingDeliveryQueryTransportMethodActivity.callDomainService@29\ncom.alibaba.trade.scenario.activity.BaseTradeActivity.execute@37\ncom.alibaba.global.buy.activities.delivery.TransportMethodQueryActivity.execute@45\ncom.alibaba.smart.framework.engine.modules.smart.provider.performer.JavaPerformer.perform@56\ncom.alibaba.smart.framework.engine.provider.impl.ComboInvoker.invoke@27\ncom.alibaba.smart.framework.engine.pvm.impl.AbstractPvmElement.invoke@47\ncom.alibaba.smart.framework.engine.provider.impl.DefaultExecutePolicyBehavior.execute@63\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmActivity.execute@59\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmActivity.enter@53\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmTransition.execute@43\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmActivity.executeRecursively@103\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmActivity.execute@68\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmActivity.enter@53\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmTransition.execute@43\n", "startTime": "26120001943150267", "subInvokeType": null, "throwable": null, "timeCost": "0.277ms", "type": "JAVA"}, {"afterExecuteParams": null, "endTime": "26120001943700079", "exceptionStackTrace": null, "extInvokeIdentity": null, "extraPrams": null, "fuzzyInvokeIdentity": null, "identity": "com.alibaba.trade.shared.utils.SequenceGenerator@next(String)", "index": 28, "isSerialized": null, "method": "next", "params": ["com.alibaba.trade.shared.delivery.transport.TransportMethodId"], "paramsInstType": null, "paramsType": ["java.lang.String"], "relatedCollectIndex": 0, "response": 14618, "rpcContext": null, "serviceName": "com.alibaba.trade.shared.utils.SequenceGenerator", "showIdentity": "com.alibaba.trade.shared.utils.SequenceGenerator@next(String)", "showInvokeIdentity": null, "stackTrace": "com.alibaba.trade.shared.utils.SequenceGenerator.next@-1\ncom.alibaba.trade.shared.delivery.transport.TransportMethodId.generate@30\ncom.alibaba.trade.function.delivery.service.TradeDeliveryTransportMethodDomainServiceImpl.toTransportMethod@378\ncom.alibaba.trade.function.delivery.service.TradeDeliveryTransportMethodDomainServiceImpl.buildResp@323\ncom.alibaba.trade.function.delivery.service.TradeDeliveryTransportMethodDomainServiceImpl.queryTransportMethod@102\ncom.alibaba.trade.scenario.activity.shopping.delivery.ShoppingDeliveryQueryTransportMethodActivity.callDomainService@55\ncom.alibaba.trade.scenario.activity.shopping.delivery.ShoppingDeliveryQueryTransportMethodActivity.callDomainService@29\ncom.alibaba.trade.scenario.activity.BaseTradeActivity.execute@37\ncom.alibaba.global.buy.activities.delivery.TransportMethodQueryActivity.execute@45\ncom.alibaba.smart.framework.engine.modules.smart.provider.performer.JavaPerformer.perform@56\ncom.alibaba.smart.framework.engine.provider.impl.ComboInvoker.invoke@27\ncom.alibaba.smart.framework.engine.pvm.impl.AbstractPvmElement.invoke@47\ncom.alibaba.smart.framework.engine.provider.impl.DefaultExecutePolicyBehavior.execute@63\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmActivity.execute@59\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmActivity.enter@53\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmTransition.execute@43\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmActivity.executeRecursively@103\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmActivity.execute@68\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmActivity.enter@53\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmTransition.execute@43\n", "startTime": "26120001943449104", "subInvokeType": null, "throwable": null, "timeCost": "0.251ms", "type": "JAVA"}], "com.alibaba.global.merchant.seller.api.facade.SellerReadFacadeForTrade@getBatchSellersInfoBySellerIds(List,List,List)": [{"afterExecuteParams": null, "endTime": "26120001186962926", "exceptionStackTrace": null, "extInvokeIdentity": null, "extraPrams": null, "fuzzyInvokeIdentity": null, "identity": "com.alibaba.global.merchant.seller.api.facade.SellerReadFacadeForTrade@getBatchSellersInfoBySellerIds(List,List,List)", "index": 4, "isSerialized": null, "method": "getBatchSellersInfoBySellerIds", "params": [[*********], ["RUSSIA_BOUTIQUE_SELLER", "SPANISH_PLAZA", "test", "48475", "RUSSIAN_OWN_RETAILER"], ["plaza_business_industry", "oversea_seller_location", "seller_quotation_currency", "seller_insurance_tag", "seller_fulfillment_type"]], "paramsInstType": null, "paramsType": ["java.util.List", "java.util.List", "java.util.List"], "relatedCollectIndex": 0, "response": {"success": true, "module": {"sellerResultMap": {"*********": {"country": "CN", "sellerMetrics": null, "gmtCreated": {"dateTime": {"date": {"month": 8, "year": 2019, "day": 1}, "time": {"hour": 1, "nano": 0, "minute": 14, "second": 54}}, "offset": {"seconds": -25200}, "zoneId": "America/Los_Angeles"}, "companyName": null, "sellerBank": null, "bizCodes": null, "sellerName": null, "sellerAddress": {"MapKey-hidden": []}, "merchantInfo": null, "sellerBizProperty": {"redMart": false, "sellerPatternDTO": {"option": 4}, "holidayMode": false, "verified": true, "active": true, "parentId": null, "local": false, "retail": false, "deleted": false, "tbc": false, "parentRetail": false, "vatRegistered": true, "crossBorder": true, "tp": false, "sellerType": null, "vatNumber": null}, "havanaId": *************, "accountId": *********, "companyId": null, "sellerId": *********, "phonePrefixCode": "", "phone": "", "shopInfoDTO": {"shopLogo": "https://ae01.alicdn.com/kf/HTB1Ih7oboLrK1Rjy1zb763enFXar.png", "shopName": "Shop5423335 Store", "shopId": 5423335}, "mainCategory": null, "paymentMethod": null, "parentSellerId": null, "email": "<EMAIL>", "shortCode": null}}, "booleanTagCodeResultMap": {"*********": {"RUSSIAN_OWN_RETAILER": false, "RUSSIA_BOUTIQUE_SELLER": false, "SPANISH_PLAZA": false}}, "stringTagCodeResultMap": {"*********": {"seller_insurance_tag": "1", "seller_fulfillment_type": null, "oversea_seller_location": "CN", "seller_quotation_currency": "USD", "sellerSignupTime": "*************", "plaza_business_industry": null}}}, "errorCode": null, "repeated": false, "retry": false}, "rpcContext": null, "serviceName": "com.alibaba.global.merchant.seller.api.facade.SellerReadFacadeForTrade", "showIdentity": "com.alibaba.global.merchant.seller.api.facade.SellerReadFacadeForTrade@getBatchSellersInfoBySellerIds(List,List,List)", "showInvokeIdentity": null, "stackTrace": "com.alibaba.global.satellite.proxy.hsf.utils.hook.SatelliteHSFConsumerProxyHook.invoke@92\ncom.alibaba.global.satellite.proxy.hsf.utils.hook.SatelliteHSFConsumerProxyHook.invoke@29\ncom.alibaba.global.satellite.utils.SentinelUtils.invoke@37\ncom.alibaba.global.satellite.proxy.hsf.utils.hook.SatelliteHSFConsumerProxyHook.proxy@58\ncom.alibaba.global.common.proxy.AbstractProxyHandler.invoke@63\ncom.alibaba.global.common.proxy.JdkProxy$Handler.invoke@66\ncom.alibaba.trade.function.actor.repository.Impl.ActorRepositoryImpl.querySellers@283\ncom.alibaba.trade.function.actor.repository.Impl.ActorRepositoryImpl.batchQuerySeller@276\ncom.alibaba.trade.function.actor.repository.Impl.ActorRepositoryImpl.batchQuerySeller@183\ncom.alibaba.global.buy.request.builder.impl.SellerBuilder.build@33\ncom.alibaba.global.buy.request.builder.impl.SellerBuilder.build@26\ncom.alibaba.trade.scenario.build.RequestBuilderFactory.lambda$executeAll$1@56\ncom.alibaba.trade.scenario.build.RequestBuilderFactory.executeAll@56\ncom.alibaba.global.buy.request.ShoppingRequest.createShoppingRequest@108\ncom.alibaba.global.buy.request.ShoppingRequest.of@90\ncom.alibaba.global.buy.api.facade.protocol.GlobalCheckoutFacadeImpl$1._execute@81\ncom.alibaba.global.buy.api.facade.protocol.GlobalCheckoutFacadeImpl$1._execute@69\ncom.alibaba.tmf.session.OnceInvoker.invoke@42\ncom.alibaba.global.buy.api.facade.protocol.GlobalCheckoutFacadeImpl.renderOrder@102\ncom.alibaba.tmf.scenario.ServiceEntranceAspect.doAround@39\n", "startTime": "26120001162167960", "subInvokeType": "rpc", "throwable": null, "timeCost": "24.795ms", "type": "JAVA"}], "com.alibaba.global.user.api.facade.UserBoolTagFacade@hasUserTag(Long,List)": [{"afterExecuteParams": null, "endTime": "*****************", "exceptionStackTrace": null, "extInvokeIdentity": null, "extraPrams": null, "fuzzyInvokeIdentity": null, "identity": "com.alibaba.global.user.api.facade.UserBoolTagFacade@hasUserTag(Long,List)", "index": 7, "isSerialized": null, "method": "hasUserTag", "params": [**********, ["FAST_PAYMENT", "GUEST_ACCOUNT", "ICS_TAG", "IS_B2B_BUYER", "is<PERSON><PERSON><PERSON><PERSON>er", "DROP_SHIPPER"]], "paramsInstType": null, "paramsType": ["java.lang.Long", "java.util.List"], "relatedCollectIndex": 0, "response": {"success": true, "module": {"FAST_PAYMENT": false, "DROP_SHIPPER": false, "ICS_TAG": false, "GUEST_ACCOUNT": false}, "errorCode": null, "repeated": false, "retry": false}, "rpcContext": null, "serviceName": "com.alibaba.global.user.api.facade.UserBoolTagFacade", "showIdentity": "com.alibaba.global.user.api.facade.UserBoolTagFacade@hasUserTag(Long,List)", "showInvokeIdentity": null, "stackTrace": "com.alibaba.global.satellite.proxy.hsf.utils.hook.SatelliteHSFConsumerProxyHook.invoke@92\ncom.alibaba.global.satellite.proxy.hsf.utils.hook.SatelliteHSFConsumerProxyHook.invoke@29\ncom.alibaba.global.satellite.utils.SentinelUtils.invoke@37\ncom.alibaba.global.satellite.proxy.hsf.utils.hook.SatelliteHSFConsumerProxyHook.proxy@58\ncom.alibaba.global.common.proxy.AbstractProxyHandler.invoke@63\ncom.alibaba.global.common.proxy.JdkProxy$Handler.invoke@66\ncom.alibaba.trade.function.actor.repository.Impl.ActorRepositoryImpl.hasUserTag@148\ncom.alibaba.trade.function.actor.repository.Impl.ActorRepositoryImpl.queryBuyer@118\ncom.alibaba.trade.scenario.build.common.BaseBuyerBuilder.build@30\ncom.alibaba.trade.scenario.build.RequestBuilderFactory.lambda$executeAll$1@56\ncom.alibaba.trade.scenario.build.RequestBuilderFactory.executeAll@56\ncom.alibaba.global.buy.request.ShoppingRequest.createShoppingRequest@108\ncom.alibaba.global.buy.request.ShoppingRequest.of@90\ncom.alibaba.global.buy.api.facade.protocol.GlobalCheckoutFacadeImpl$1._execute@81\ncom.alibaba.global.buy.api.facade.protocol.GlobalCheckoutFacadeImpl$1._execute@69\ncom.alibaba.tmf.session.OnceInvoker.invoke@42\ncom.alibaba.global.buy.api.facade.protocol.GlobalCheckoutFacadeImpl.renderOrder@102\ncom.alibaba.tmf.scenario.ServiceEntranceAspect.doAround@39\ncom.alibaba.ae.trade.adaptor.checkout.AECheckoutMultiterminalFacadeImpl.renderOrder@65\ncom.alibaba.global.satellite.proxy.hsf.utils.hook.SatelliteHSFProviderProxyHook.invoke@68\n", "startTime": "26120001199756987", "subInvokeType": "rpc", "throwable": null, "timeCost": "2.430ms", "type": "JAVA"}], "com.alibaba.global.expression.protocol.sdk.facade.FulfillmentServiceFacade@queryFulfillmentGroupService(QueryFulfillmentGroupServiceRequest)": [{"afterExecuteParams": null, "endTime": "26120001942515207", "exceptionStackTrace": null, "extInvokeIdentity": null, "extraPrams": null, "fuzzyInvokeIdentity": null, "identity": "com.alibaba.global.expression.protocol.sdk.facade.FulfillmentServiceFacade@queryFulfillmentGroupService(QueryFulfillmentGroupServiceRequest)", "index": 25, "isSerialized": null, "method": "queryFulfillmentGroupService", "params": [{"extendParam": {"extensionDTOMap": {"/fulfillmentGroups/fulfillmentLines/$90": {"shipToCatId": *********, "digital": false, "packAddWeight": null, "freightTemplateId": ***********, "skuFeature": {"$ref": "$.body.onlineContext.subInvokes.com\\.alibaba\\.global\\.ic\\.api\\.CustomerProductServiceFacade\\@queryProduct\\(ProductQueryRequest\\)[0].response.model[0].skuIdAndSkuMap.*****************.featureMap"}, "sendGoodsCountryCode": null, "freightTemplateSnapshotId": null, "itemFeature": {"$ref": "$.body.onlineContext.subInvokes.com\\.alibaba\\.global\\.ic\\.api\\.CustomerProductServiceFacade\\@queryProduct\\(ProductQueryRequest\\)[0].response.model[0].featureMap"}, "skuTags": ["82052", "110572"], "packBaseUnit": null, "tags": ["82052", "new_price_model"], "freightXddCb": null, "packSell": null, "taxRate": 0.2, "hiddenShippingCost": null, "packAddUnit": null, "uniqueId": "90"}, "/fulfillmentGroups/fulfillmentLines/buyerSelection/$90": {"originalService": null, "uniqueId": "90"}, "/invokeInfo": {"appVersion": "255", "invokeSource": "ios", "cookieCna": "", "appKey": "", "deviceId": "", "scene": "order"}}}, "routingInfo": {"sellerId": null, "buyerId": null}, "invokeInfo": {"appName": null}, "fulfillmentGroups": [{"buyerSelection": {"selectedFulfillmentServiceCode": null}, "fulfillmentGroupId": "8240949589426726998", "fulfillmentLines": [{"buyerSelection": {"selectedFulfillmentServiceCode": "CAINIAO_STANDARD"}, "seller": {"sellerId": *********}, "sku": {"itemId": ****************, "quantity": 1, "discountPrice": {"amount": 67089, "currencyCode": "USD"}, "leafCategoryId": 5090301, "dimension": {"length": 11, "width": 12, "weight": 0.1, "height": 13}, "skuId": "*****************"}, "inventory": null, "tradeOrderLineId": "90"}]}], "buyer": {"receiverAddress": {"addressType": "home", "locationTreeAddressId": "910100010000000000-910100010001000000", "countryISOCode": "IT", "latitude": null, "longitude": null}, "buyerId": **********}}], "paramsInstType": null, "paramsType": ["com.alibaba.global.expression.protocol.sdk.model.request.QueryFulfillmentGroupServiceRequest"], "relatedCollectIndex": 0, "response": {"result": {"fulfillmentGroupResults": [{"features": {"tradeOrderId": "8240949589426726998", "packageUniqueKey": "8240949589426726998"}, "preferFulfillmentGroupService": null, "fulfillmentGroupId": "8240949589426726998", "invalidTradeOrderLines": [], "fulfillmentGroupServices": [{"features": {}, "fulfillmentService": {"fulfillmentServiceTime": {"cutoffTimestamp": 1619613261917, "minTimeInSecond": 3024000, "maxTimeInSecond": 3024000}, "features": {"ITEM_ACTIVITY_SUBSCRIPTION_ID": null, "supportCod": null, "featuresForTrade": "", "calculation_SubscriptionId": null, "deliveryProviderName": "CAINIAO_STANDARD", "shippingFee": {"cent": 1074, "currencyCode": "USD"}, "shippingFeeDiscount": null, "savedShippingCost": {"cent": 0, "currencyCode": "USD"}, "deliveryProviderCode": "CAINIAO_STANDARD", "accurateFee": false}, "fulfillmentServiceFee": {"additionalFee": {"amount": 0, "currencyCode": "USD"}, "shippingFee": {"amount": 1074, "currencyCode": "USD"}, "fulfillmentServiceFee": {"amount": 1074, "currencyCode": "USD"}, "additionalFeeDetails": null}, "fulfillmentServiceDisplayModel": {"features": {}}, "fulfillmentServiceCode": "CAINIAO_STANDARD"}, "fulfillmentLineServices": [{"fulfillmentServices": [{"fulfillmentServiceTime": {"cutoffTimestamp": 1619613261911, "minTimeInSecond": 3024000, "maxTimeInSecond": 3024000}, "features": {"supportCod": false, "deliveryProviderName": "AliExpress Standard Shipping", "displayName": "AliExpress Standard Shipping", "sendGoodsCountryCode": "CN", "calculation_ShippingFeeUnitCount": "0.100", "freightAmountStr": "Shipping: {0}", "provider": "cainiao", "hbaService": false, "deliveryServicePudoDate": "2021-06-01", "deliveryDate": "2021-06-01", "hbaFullMailLine": false, "featuresForTrade": "", "guaranteedDeliveryTime": 75, "freightCommitDay": 75, "ITEM_ID": ****************, "savedShippingCost": {"cent": 0, "currencyCode": "USD"}, "deliveryDateStr": "Estimated delivery on <font color='#000000'>Jun 01</font>", "shippingToStr": "To <font color='#000000'>Italy</font> via AliExpress Standard Shipping", "rateAddressLevel": "COUNTRY", "isTracked": true, "shippingFee": {"cent": 1074, "currencyCode": "USD"}, "deliveryDateHelpStr": "If you finish the payment today, your order will arrive within the estimated delivery time.", "shippingFeeDiscount": 0, "deliveryProviderCode": "CAINIAO_STANDARD", "accurateFee": false}, "fulfillmentServiceFee": {"additionalFee": {"amount": 0, "currencyCode": "USD"}, "shippingFee": {"amount": 1074, "currencyCode": "USD"}, "fulfillmentServiceFee": {"amount": 1074, "currencyCode": "USD"}, "additionalFeeDetails": null}, "fulfillmentServiceDisplayModel": {"features": {"deliveryDateHelpStr": "If you finish the payment today, your order will arrive within the estimated delivery time.", "deliveryTextType": "deliveryDate", "estimateDeliveryDate": "Jun 01", "deliveryDateStr": "Estimated delivery on <font color='#000000'>Jun 01</font>", "freightAmountStr": "Shipping: {0}", "shippingToStr": "To <font color='#000000'>Italy</font> via AliExpress Standard Shipping"}}, "fulfillmentServiceCode": "CAINIAO_STANDARD"}, {"fulfillmentServiceTime": {"cutoffTimestamp": 1619613261914, "minTimeInSecond": 1209600, "maxTimeInSecond": 2851200}, "features": {"supportCod": false, "featuresForTrade": "", "deliveryProviderName": "EMS", "displayName": "EMS", "sendGoodsCountryCode": "CN", "guaranteedDeliveryTime": 75, "calculation_ShippingFeeUnitCount": "0.100", "freightCommitDay": 75, "ITEM_ID": ****************, "savedShippingCost": {"cent": 0, "currencyCode": "USD"}, "freightAmountStr": "Shipping: {0}", "shippingToStr": "To <font color='#000000'>Italy</font> via EMS", "search_calculate_delivery_days": 33, "rateAddressLevel": "COUNTRY", "isTracked": true, "shippingFee": {"cent": 9138, "currencyCode": "USD"}, "shippingFeeDiscount": 0, "provider": "ae_config", "search_calculate_strategy": "OTHER", "hbaService": false, "hbaFullMailLine": false, "deliveryProviderCode": "EMS", "accurateFee": false}, "fulfillmentServiceFee": {"additionalFee": {"amount": 0, "currencyCode": "USD"}, "shippingFee": {"amount": 9138, "currencyCode": "USD"}, "fulfillmentServiceFee": {"amount": 9138, "currencyCode": "USD"}, "additionalFeeDetails": null}, "fulfillmentServiceDisplayModel": {"features": {"deliveryTextType": "deliveryTime", "estimateDeliveryDate": "14-33", "freightAmountStr": "Shipping: {0}", "shippingToStr": "To <font color='#000000'>Italy</font> via EMS"}}, "fulfillmentServiceCode": "EMS"}], "features": {"LOGISTICS_ALL_TAGS": "{\"discountForCainiaoSettlement\":\"9\"}", "freightCommitDay": 75, "deliveryCanTracking": true}, "preferFulfillmentService": {"fulfillmentServiceTime": {"cutoffTimestamp": 1619613261915, "minTimeInSecond": 3024000, "maxTimeInSecond": 3024000}, "features": {"supportCod": false, "deliveryProviderName": "AliExpress Standard Shipping", "displayName": "AliExpress Standard Shipping", "sendGoodsCountryCode": "CN", "calculation_ShippingFeeUnitCount": "0.100", "freightAmountStr": "Shipping: {0}", "provider": "cainiao", "hbaService": false, "deliveryServicePudoDate": "2021-06-01", "deliveryDate": "2021-06-01", "hbaFullMailLine": false, "featuresForTrade": "", "guaranteedDeliveryTime": 75, "freightCommitDay": 75, "ITEM_ID": ****************, "savedShippingCost": {"$ref": "$.body.onlineContext.subInvokes.com\\.alibaba\\.global\\.expression\\.protocol\\.sdk\\.facade\\.FulfillmentServiceFacade\\@queryFulfillmentGroupService\\(QueryFulfillmentGroupServiceRequest\\)[0].response.result.fulfillmentGroupResults[0].fulfillmentGroupServices[0].fulfillmentLineServices[0].fulfillmentServices[0].features.savedShippingCost"}, "deliveryDateStr": "Estimated delivery on <font color='#000000'>Jun 01</font>", "shippingToStr": "To <font color='#000000'>Italy</font> via AliExpress Standard Shipping", "rateAddressLevel": "COUNTRY", "isTracked": true, "shippingFee": {"$ref": "$.body.onlineContext.subInvokes.com\\.alibaba\\.global\\.expression\\.protocol\\.sdk\\.facade\\.FulfillmentServiceFacade\\@queryFulfillmentGroupService\\(QueryFulfillmentGroupServiceRequest\\)[0].response.result.fulfillmentGroupResults[0].fulfillmentGroupServices[0].fulfillmentLineServices[0].fulfillmentServices[0].features.shippingFee"}, "deliveryDateHelpStr": "If you finish the payment today, your order will arrive within the estimated delivery time.", "shippingFeeDiscount": 0, "deliveryProviderCode": "CAINIAO_STANDARD", "accurateFee": false}, "fulfillmentServiceFee": {"additionalFee": {"amount": 0, "currencyCode": "USD"}, "shippingFee": {"amount": 1074, "currencyCode": "USD"}, "fulfillmentServiceFee": {"amount": 1074, "currencyCode": "USD"}, "additionalFeeDetails": null}, "fulfillmentServiceDisplayModel": {"features": {"deliveryDateHelpStr": "If you finish the payment today, your order will arrive within the estimated delivery time.", "deliveryTextType": "deliveryDate", "estimateDeliveryDate": "Jun 01", "deliveryDateStr": "Estimated delivery on <font color='#000000'>Jun 01</font>", "freightAmountStr": "Shipping: {0}", "shippingToStr": "To <font color='#000000'>Italy</font> via AliExpress Standard Shipping"}}, "fulfillmentServiceCode": "CAINIAO_STANDARD"}, "tradeOrderLineId": "90"}]}]}]}, "success": true, "errorCode": null}, "rpcContext": null, "serviceName": "com.alibaba.global.expression.protocol.sdk.facade.FulfillmentServiceFacade", "showIdentity": "com.alibaba.global.expression.protocol.sdk.facade.FulfillmentServiceFacade@queryFulfillmentGroupService(QueryFulfillmentGroupServiceRequest)", "showInvokeIdentity": null, "stackTrace": "com.alibaba.global.satellite.proxy.hsf.utils.hook.SatelliteHSFConsumerProxyHook.invoke@92\ncom.alibaba.global.satellite.proxy.hsf.utils.hook.SatelliteHSFConsumerProxyHook.invoke@29\ncom.alibaba.global.satellite.utils.SentinelUtils.invoke@37\ncom.alibaba.global.satellite.proxy.hsf.utils.hook.SatelliteHSFConsumerProxyHook.proxy@58\ncom.alibaba.global.common.proxy.AbstractProxyHandler.invoke@63\ncom.alibaba.global.common.proxy.JdkProxy$Handler.invoke@66\ncom.alibaba.trade.function.delivery.repository.protocol.GlobalFulfillmentServiceRepositoryImpl.queryDeliveryOptions@110\ncom.alibaba.trade.function.delivery.repository.DeliveryOptionRepositoryImpl.queryDeliveryOptions@147\ncom.alibaba.trade.function.delivery.service.TradeDeliveryTransportMethodDomainServiceImpl.queryTransportMethod@97\ncom.alibaba.trade.scenario.activity.shopping.delivery.ShoppingDeliveryQueryTransportMethodActivity.callDomainService@55\ncom.alibaba.trade.scenario.activity.shopping.delivery.ShoppingDeliveryQueryTransportMethodActivity.callDomainService@29\ncom.alibaba.trade.scenario.activity.BaseTradeActivity.execute@37\ncom.alibaba.global.buy.activities.delivery.TransportMethodQueryActivity.execute@45\ncom.alibaba.smart.framework.engine.modules.smart.provider.performer.JavaPerformer.perform@56\ncom.alibaba.smart.framework.engine.provider.impl.ComboInvoker.invoke@27\ncom.alibaba.smart.framework.engine.pvm.impl.AbstractPvmElement.invoke@47\ncom.alibaba.smart.framework.engine.provider.impl.DefaultExecutePolicyBehavior.execute@63\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmActivity.execute@59\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmActivity.enter@53\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmTransition.execute@43\n", "startTime": "26120001874131922", "subInvokeType": "rpc", "throwable": null, "timeCost": "68.383ms", "type": "JAVA"}], "com.alibaba.global.payment.api.facade.CashierFacade@calculatePayCurrency(CalculatePayCurrencyRequest)": [{"afterExecuteParams": null, "endTime": "26120001550132671", "exceptionStackTrace": null, "extInvokeIdentity": null, "extraPrams": null, "fuzzyInvokeIdentity": null, "identity": "com.alibaba.global.payment.api.facade.CashierFacade@calculatePayCurrency(CalculatePayCurrencyRequest)", "index": 18, "isSerialized": null, "method": "calculatePayCurrency", "params": [{"intentionSelectChannelDTO": {"attributes": {"cardBrandCurrency": null, "cardBinCountry": null, "cardBrand": null, "payChannelEchoExtAttribute": null}, "methodCode": null, "channelCode": null}, "calculatePayCurrencyItems": [{"outId": "90", "orderCcy": "USD"}], "intentionCurrency": "USD", "attributes": {"frontComponentDataJsonStr": null}, "chosenIntentionDTO": {"intentionLineId": null, "intentionContext": {}, "params": {"cardBrandCurrency": null, "cardBinCountry": null, "cardBrand": null, "payChannelEchoExtAttribute": null}}}], "paramsInstType": null, "paramsType": ["com.alibaba.global.payment.api.request.cashier.CalculatePayCurrencyRequest"], "relatedCollectIndex": 0, "response": {"success": true, "module": {"calculatePayCurrencyResults": [{"outId": "90", "orderCcy": "USD", "payCcy": "USD"}], "mergePayItemGroups": [{"calculatePayCurrencyItems": [{"$ref": "$.body.onlineContext.subInvokes.com\\.alibaba\\.global\\.payment\\.api\\.facade\\.CashierFacade\\@calculatePayCurrency\\(CalculatePayCurrencyRequest\\)[0].response.module.calculatePayCurrencyResults[0]"}], "payCcy": "USD"}], "attributes": {}}, "errorCode": null, "repeated": false, "retry": false}, "rpcContext": null, "serviceName": "com.alibaba.global.payment.api.facade.CashierFacade", "showIdentity": "com.alibaba.global.payment.api.facade.CashierFacade@calculatePayCurrency(CalculatePayCurrencyRequest)", "showInvokeIdentity": null, "stackTrace": "com.alibaba.global.satellite.proxy.hsf.utils.hook.SatelliteHSFConsumerProxyHook.invoke@92\ncom.alibaba.global.satellite.proxy.hsf.utils.hook.SatelliteHSFConsumerProxyHook.invoke@29\ncom.alibaba.global.satellite.utils.SentinelUtils.invoke@37\ncom.alibaba.global.satellite.proxy.hsf.utils.hook.SatelliteHSFConsumerProxyHook.proxy@58\ncom.alibaba.global.common.proxy.AbstractProxyHandler.invoke@63\ncom.alibaba.global.common.proxy.JdkProxy$Handler.invoke@66\ncom.alibaba.global.buy.activities.price.ExchangeInitActivity.execute@103\ncom.alibaba.smart.framework.engine.modules.smart.provider.performer.JavaPerformer.perform@56\ncom.alibaba.smart.framework.engine.provider.impl.ComboInvoker.invoke@27\ncom.alibaba.smart.framework.engine.pvm.impl.AbstractPvmElement.invoke@47\ncom.alibaba.smart.framework.engine.provider.impl.DefaultExecutePolicyBehavior.execute@63\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmActivity.execute@59\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmActivity.enter@53\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmTransition.execute@43\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmActivity.executeRecursively@103\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmActivity.execute@68\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmActivity.enter@53\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmTransition.execute@43\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmActivity.executeRecursively@103\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmActivity.execute@68\n", "startTime": "26120001257261517", "subInvokeType": "rpc", "throwable": null, "timeCost": "292.871ms", "type": "JAVA"}], "com.alibaba.global.address.api.facade.CollectionPointReadFacade@getRecommendCollectionPointForMultiOrder(CollectionPointMultiOrderQueryRequest)": [{"afterExecuteParams": null, "endTime": "26120001956299825", "exceptionStackTrace": null, "extInvokeIdentity": null, "extraPrams": null, "fuzzyInvokeIdentity": null, "identity": "com.alibaba.global.address.api.facade.CollectionPointReadFacade@getRecommendCollectionPointForMultiOrder(CollectionPointMultiOrderQueryRequest)", "index": 29, "isSerialized": null, "method": "getRecommendCollectionPointForMultiOrder", "params": [{"country": "US", "features": {"buyerCountry": "US", "protocol": "1", "appVersion": "255", "saasRegion": null, "detailAddressType": "residential", "clientType": "ios", "reStartTimes": "0", "intentional_currency": "USD", "isVirtualProduct": "false", "locale": "en_US", "BUCKET_NAME": "part", "shippingMethodType": "residential"}, "appVersion": null, "userAddressId": **********, "appName": null, "clientIp": null, "siteId": null, "bizScene": null, "orderList": [{"featureMap": null, "orderAmount": {"amount": {"number": 670.89}, "currencyCode": "USD"}, "userAddressId": null, "shippingFee": {"amount": {"number": 0.0}, "currencyCode": "USD"}, "orderId": "4433d4c6651e23cea9f67c92f2de6bf9", "userId": null, "items": [{"itemId": ****************, "featureMap": null, "sellerId": *********, "quantity": 1, "supportSelfPickProviderList": null, "defaultSelfPickup": false, "currencyCode": "USD", "deliveryProviderCode": "CAINIAO_STANDARD", "skuId": null, "supportAddressTypeList": null}]}], "locale": null, "userId": **********, "platform": "MOBILE"}], "paramsInstType": null, "paramsType": ["com.alibaba.global.address.api.request.CollectionPointMultiOrderQueryRequest"], "relatedCollectIndex": 0, "response": {"success": true, "module": [{"collectionPointList": [], "orderId": "4433d4c6651e23cea9f67c92f2de6bf9"}], "errorCode": null, "repeated": false, "retry": false}, "rpcContext": null, "serviceName": "com.alibaba.global.address.api.facade.CollectionPointReadFacade", "showIdentity": "com.alibaba.global.address.api.facade.CollectionPointReadFacade@getRecommendCollectionPointForMultiOrder(CollectionPointMultiOrderQueryRequest)", "showInvokeIdentity": null, "stackTrace": "com.alibaba.global.satellite.proxy.hsf.utils.hook.SatelliteHSFConsumerProxyHook.invoke@92\ncom.alibaba.global.satellite.proxy.hsf.utils.hook.SatelliteHSFConsumerProxyHook.invoke@29\ncom.alibaba.global.satellite.utils.SentinelUtils.invoke@37\ncom.alibaba.global.satellite.proxy.hsf.utils.hook.SatelliteHSFConsumerProxyHook.proxy@58\ncom.alibaba.global.common.proxy.AbstractProxyHandler.invoke@63\ncom.alibaba.global.common.proxy.JdkProxy$Handler.invoke@66\ncom.alibaba.trade.function.delivery.repository.AddressRepositoryImpl.queryServiceAddressByParcel@526\ncom.alibaba.trade.function.delivery.service.GlobalTradeDeliveryAddressDomainServiceImpl.queryServiceReceiveInfo@283\ncom.alibaba.trade.scenario.buy.global.ShoppingDeliveryQueryServiceAddressActivity.callDomainService@40\ncom.alibaba.trade.scenario.buy.global.ShoppingDeliveryQueryServiceAddressActivity.callDomainService@23\ncom.alibaba.trade.scenario.activity.BaseTradeActivity.execute@37\ncom.alibaba.global.buy.activities.address.ServiceAddressQueryActivity.execute@40\ncom.alibaba.smart.framework.engine.modules.smart.provider.performer.JavaPerformer.perform@56\ncom.alibaba.smart.framework.engine.provider.impl.ComboInvoker.invoke@27\ncom.alibaba.smart.framework.engine.pvm.impl.AbstractPvmElement.invoke@47\ncom.alibaba.smart.framework.engine.provider.impl.DefaultExecutePolicyBehavior.execute@63\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmActivity.execute@59\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmActivity.enter@53\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmTransition.execute@43\ncom.alibaba.smart.framework.engine.pvm.impl.DefaultPvmActivity.executeRecursively@103\n", "startTime": "26120001944293660", "subInvokeType": "rpc", "throwable": null, "timeCost": "12.006ms", "type": "JAVA"}], "com.alibaba.global.user.api.facade.UserReadFacade@getUserById(Long)": [{"afterExecuteParams": null, "endTime": "26120001199719235", "exceptionStackTrace": null, "extInvokeIdentity": null, "extraPrams": null, "fuzzyInvokeIdentity": null, "identity": "com.alibaba.global.user.api.facade.UserReadFacade@getUserById(Long)", "index": 6, "isSerialized": null, "method": "getUserById", "params": [**********], "paramsInstType": null, "paramsType": ["java.lang.Long"], "relatedCollectIndex": 0, "response": {"success": true, "module": {"birthday": null, "lastName": "shopper", "userFrom": null, "gmtModified": {"dateTime": {"date": {"month": 1, "year": 2020, "day": 2}, "time": {"hour": 20, "nano": 0, "minute": 0, "second": 12}}, "offset": {"seconds": -28800}, "zoneId": "America/Los_Angeles"}, "loginId": "cn262780764rdeae", "userStatus": 0, "gender": null, "language": null, "havanaId": 2207263590728, "countryCode": "CN", "liveUpUser": false, "userCertification": {"branchId": null, "taxId": null}, "email": "<EMAIL>", "adminUserId": null, "contactEmail": "<EMAIL>", "liveUserDTO": null, "fullName": "CN shopper", "avatar": null, "membershipStatus": 0, "gmtCreate": {"dateTime": {"date": {"month": 1, "year": 2020, "day": 2}, "time": {"hour": 20, "nano": 0, "minute": 0, "second": 12}}, "offset": {"seconds": -28800}, "zoneId": "America/Los_Angeles"}, "userId": **********, "firstName": "CN", "enableEwallet": false, "phonePrefixCode": "", "phone": "", "bizExt": {}, "siteId": "GLOBAL", "middleName": null, "userType": "BUYER", "isAdminUser": false, "contactPhone": null, "alipayUserId": "2188211633642564"}, "errorCode": null, "repeated": false, "retry": false}, "rpcContext": null, "serviceName": "com.alibaba.global.user.api.facade.UserReadFacade", "showIdentity": "com.alibaba.global.user.api.facade.UserReadFacade@getUserById(Long)", "showInvokeIdentity": null, "stackTrace": "com.alibaba.global.satellite.proxy.hsf.utils.hook.SatelliteHSFConsumerProxyHook.invoke@92\ncom.alibaba.global.satellite.proxy.hsf.utils.hook.SatelliteHSFConsumerProxyHook.invoke@29\ncom.alibaba.global.satellite.utils.SentinelUtils.invoke@37\ncom.alibaba.global.satellite.proxy.hsf.utils.hook.SatelliteHSFConsumerProxyHook.proxy@58\ncom.alibaba.global.common.proxy.AbstractProxyHandler.invoke@63\ncom.alibaba.global.common.proxy.JdkProxy$Handler.invoke@66\ncom.alibaba.trade.function.actor.repository.Impl.ActorRepositoryImpl.queryBuyer@111\ncom.alibaba.trade.scenario.build.common.BaseBuyerBuilder.build@30\ncom.alibaba.trade.scenario.build.RequestBuilderFactory.lambda$executeAll$1@56\ncom.alibaba.trade.scenario.build.RequestBuilderFactory.executeAll@56\ncom.alibaba.global.buy.request.ShoppingRequest.createShoppingRequest@108\ncom.alibaba.global.buy.request.ShoppingRequest.of@90\ncom.alibaba.global.buy.api.facade.protocol.GlobalCheckoutFacadeImpl$1._execute@81\ncom.alibaba.global.buy.api.facade.protocol.GlobalCheckoutFacadeImpl$1._execute@69\ncom.alibaba.tmf.session.OnceInvoker.invoke@42\ncom.alibaba.global.buy.api.facade.protocol.GlobalCheckoutFacadeImpl.renderOrder@102\ncom.alibaba.tmf.scenario.ServiceEntranceAspect.doAround@39\ncom.alibaba.ae.trade.adaptor.checkout.AECheckoutMultiterminalFacadeImpl.renderOrder@65\ncom.alibaba.global.satellite.proxy.hsf.utils.hook.SatelliteHSFProviderProxyHook.invoke@68\ncom.alibaba.global.satellite.proxy.hsf.utils.hook.SatelliteHSFProviderProxyHook.invoke@30\n", "startTime": "26120001197472998", "subInvokeType": "rpc", "throwable": null, "timeCost": "2.246ms", "type": "JAVA"}], "com.alibaba.global.shop.api.facade.ShopReadFacade@listShopBySellerIdList(List)": [{"afterExecuteParams": null, "endTime": "26120001205543116", "exceptionStackTrace": null, "extInvokeIdentity": null, "extraPrams": null, "fuzzyInvokeIdentity": null, "identity": "com.alibaba.global.shop.api.facade.ShopReadFacade@listShopBySellerIdList(List)", "index": 9, "isSerialized": null, "method": "listShopBySellerIdList", "params": [[*********]], "paramsInstType": null, "paramsType": ["java.util.List"], "relatedCollectIndex": 0, "response": {"success": true, "module": [{"gmtModified": null, "iconLinkLocal": null, "shopTags": null, "associatedBrands": null, "shopLogo": "https://ae01.alicdn.com/kf/HTB1Ih7oboLrK1Rjy1zb763enFXar.png", "nameEn": "Shop5423335 Store", "gmtCreate": 1574133147000, "url": "//www.aliexpress.com/store/5423335", "urlKey": null, "tags": null, "features": null, "shopStatus": 1, "iconLink": null, "name": "Shop5423335 Store", "bizId": *********, "subSellerId": null, "id": 5423335, "shopType": "other", "isLazMall": false, "sellerType": null}], "errorCode": null, "repeated": false, "retry": false}, "rpcContext": null, "serviceName": "com.alibaba.global.shop.api.facade.ShopReadFacade", "showIdentity": "com.alibaba.global.shop.api.facade.ShopReadFacade@listShopBySellerIdList(List)", "showInvokeIdentity": null, "stackTrace": "com.alibaba.global.satellite.proxy.hsf.utils.hook.SatelliteHSFConsumerProxyHook.invoke@92\ncom.alibaba.global.satellite.proxy.hsf.utils.hook.SatelliteHSFConsumerProxyHook.invoke@29\ncom.alibaba.global.satellite.utils.SentinelUtils.invoke@37\ncom.alibaba.global.satellite.proxy.hsf.utils.hook.SatelliteHSFConsumerProxyHook.proxy@58\ncom.alibaba.global.common.proxy.AbstractProxyHandler.invoke@63\ncom.alibaba.global.common.proxy.JdkProxy$Handler.invoke@66\ncom.alibaba.trade.function.common.domain.shop.repository.ShopRepositoryImpl.batchQueryBySellerIds@64\ncom.alibaba.trade.function.common.domain.shop.repository.ShopRepositoryImpl.batchQueryByProducts@123\ncom.alibaba.global.buy.request.builder.impl.ShopBuilder.build@79\ncom.alibaba.global.buy.request.builder.impl.BizCoderBuilder.build@32\ncom.alibaba.global.buy.request.builder.impl.BizCoderBuilder.build@26\ncom.alibaba.trade.scenario.build.RequestBuilderFactory.lambda$executeAll$1@56\ncom.alibaba.trade.scenario.build.RequestBuilderFactory.executeAll@56\ncom.alibaba.global.buy.request.ShoppingRequest.createShoppingRequest@108\ncom.alibaba.global.buy.request.ShoppingRequest.of@90\ncom.alibaba.global.buy.api.facade.protocol.GlobalCheckoutFacadeImpl$1._execute@81\ncom.alibaba.global.buy.api.facade.protocol.GlobalCheckoutFacadeImpl$1._execute@69\ncom.alibaba.tmf.session.OnceInvoker.invoke@42\ncom.alibaba.global.buy.api.facade.protocol.GlobalCheckoutFacadeImpl.renderOrder@102\ncom.alibaba.tmf.scenario.ServiceEntranceAspect.doAround@39\n", "startTime": "26120001204248039", "subInvokeType": "rpc", "throwable": null, "timeCost": "1.295ms", "type": "JAVA"}]}, "subInvokesCount": {}, "throwables": {"head": 0, "tail": 0, "list": [], "maxLength": 50}, "userDataMap": {"tmf_trace": {"bizCodes": ["ali.global.ae.trade.general"], "bundledServices": [], "products": ["ali.global.ae.address.restricted", "com.alibaba.business.trade.general.GeneralTradeAbility", "ali.global.ae.russain.selfpick"], "bizExtCodes": ["GENERAL#ORDER#ORDER_VALIDATE_EVENT", "GENERAL#ORDER#ORDER_VALIDATE_EVENT", "GENERAL#ORDER#ORDER_VALIDATE_EVENT", "GENERAL#COMMON#ORDER_ORDER_LINE_GET_CONTRACT_CONFIG", "GENERAL#COMMON#ORDER_ORDER_LINE_GET_CONTRACT_CONFIG", "GENERAL#COMMON#ORDER_ORDER_LINE_GET_CONTRACT_CONFIG", "GENERAL#COMMON#ORDER_ORDER_LINE_GET_CONTRACT_CONFIG", "GENERAL#ORDER#ORDER_BASE_INFO_ENRICH", "GENERAL#ORDER#ORDER_BASE_INFO_ENRICH", "GENERAL#ORDER#ORDER_BASE_INFO_ENRICH", "GENERAL#ORDER#ORDER_BASE_INFO_ENRICH", "GENERAL#BUYER_PLACE_ORDER#GET_GROUPING_INFO", "GENERAL#BUYER_PLACE_ORDER#SPLIT_GRUPING_TYPE", "GENERAL#ORDER#ORDER_BASE_INFO_ENRICH", "GENERAL#ORDER#ORDER_BASE_INFO_ENRICH", "GENERAL#ORDER#ORDER_BASE_INFO_ENRICH", "GENERAL#ORDER#ORDER_BASE_INFO_ENRICH", "GENERAL#COMMON#ORDER_ORDER_LINE_GET_CONTRACT_CONFIG", "GENERAL#BUYER_PLACE_ORDER#CONFIRM_OTHER_PARAMS", "GENERAL#BUYER_PLACE_ORDER#QUERY_PRICE_PARAMS", "GENERAL#COMMON#ORDER_ORDER_LINE_GET_CONTRACT_CONFIG", "GENERAL#COMMON#ORDER_ORDER_LINE_GET_CONTRACT_CONFIG", "GENERAL#BUYER_PLACE_ORDER_ENRICH_RENDER_CASHIER_PARAMS", "GENERAL#ORDER#ORDER_VALIDATE_EVENT", "GENERAL#ORDER#ORDER_VALIDATE_EVENT", "GENERAL#BUYER_PLACE_ORDER_ENRICH_RENDER_ORDER_RESULT"], "detail": [{"abilityCode": "com.alibaba.business.trade.general.ability.order.common.validate.GeneralTradeOrderValidateAbility", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "GENERAL#ORDER#ORDER_VALIDATE_EVENT", "instanceCode": "com.alibaba.business.trade.general.ability.order.common.validate.GeneralTradeOrderValidateAbility", "invokeInfo": {"deep": 1, "endSubIdx": 13, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 13}, "runner": [{"baseType": true, "runnerResult": [null], "templateCode": "ali.global.ae.trade.general", "templateType": "BUSINESS"}]}, {"abilityCode": "com.alibaba.trade.function.order.ability.order.OrderValidateAbility", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "OrderSpiCodes.EXT_INITIAL_VALIDATE_ORDER_BEFORE_RENDERING_CODE", "instanceCode": "com.alibaba.trade.function.order.ability.order.GlobalOrderValidateAbility", "invokeInfo": {"deep": 0, "endSubIdx": 13, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 13}, "runner": [{"baseType": true, "runnerResult": [null], "templateCode": "com.alibaba.business.trade.general.GeneralTradeAbility", "templateType": "PRODUCT"}]}, {"abilityCode": "com.alibaba.business.trade.general.ability.order.common.validate.GeneralTradeOrderValidateAbility", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "GENERAL#ORDER#ORDER_VALIDATE_EVENT", "instanceCode": "com.alibaba.business.trade.general.ability.order.common.validate.GeneralTradeOrderValidateAbility", "invokeInfo": {"deep": 1, "endSubIdx": 14, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 13}, "runner": [{"baseType": true, "runnerResult": [null], "templateCode": "ali.global.ae.trade.general", "templateType": "BUSINESS"}]}, {"abilityCode": "com.alibaba.trade.function.order.ability.orderline.OrderLineValidateAbility", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "GlobalOrderSpiCodes.EXT_ORDER_ORDER_LINE_INITIAL_VALIDATE_ORDER_LINE_BEFORE_RENDERING_CODE", "instanceCode": "com.alibaba.trade.function.order.ability.orderline.GlobalOrderLineValidateAbility", "invokeInfo": {"deep": 0, "endSubIdx": 14, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 13}, "runner": [{"baseType": true, "runnerResult": [null], "templateCode": "com.alibaba.business.trade.general.GeneralTradeAbility", "templateType": "PRODUCT"}]}, {"abilityCode": "com.alibaba.business.trade.general.ability.order.common.validate.GeneralTradeOrderValidateAbility", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "GENERAL#ORDER#ORDER_VALIDATE_EVENT", "instanceCode": "com.alibaba.business.trade.general.ability.order.common.validate.GeneralTradeOrderValidateAbility", "invokeInfo": {"deep": 1, "endSubIdx": 15, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 14}, "runner": [{"baseType": true, "runnerResult": [null], "templateCode": "ali.global.ae.trade.general", "templateType": "BUSINESS"}]}, {"abilityCode": "com.alibaba.trade.function.order.ability.orderline.OrderLineValidateAbility", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "GlobalOrderSpiCodes.EXT_ORDER_ORDER_LINE_INITIAL_VALIDATE_ORDER_LINE_BEFORE_RENDERING_CODE", "instanceCode": "com.alibaba.trade.function.order.ability.orderline.GlobalOrderLineValidateAbility", "invokeInfo": {"deep": 0, "endSubIdx": 15, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 14}, "runner": [{"baseType": true, "runnerResult": [null], "templateCode": "com.alibaba.business.trade.general.GeneralTradeAbility", "templateType": "PRODUCT"}]}, {"abilityCode": "com.alibaba.business.trade.general.ability.order.common.config.GeneralTradeOrderConfigAbility", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "GENERAL#COMMON#ORDER_ORDER_LINE_GET_CONTRACT_CONFIG", "instanceCode": "com.alibaba.business.trade.general.ability.order.common.config.GeneralTradeOrderConfigAbility", "invokeInfo": {"deep": 1, "endSubIdx": 15, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 15}, "runner": [{"baseType": true, "runnerResult": [{"type": "com.alibaba.business.ability.order.general.global.activity.common.order.config.output.OrderConfigOutput", "value": "COMPLEX_OBJECT"}], "templateCode": "ali.global.ae.trade.general", "templateType": "BUSINESS"}]}, {"abilityCode": "com.alibaba.trade.function.delivery.ability.address.GlobalAddressAbility", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "GlobalFulfillmentSpiCodes.EXT_FULFILLMENT_NEED_SHIPPING_ADDRESS_CODE", "instanceCode": "com.alibaba.trade.function.delivery.ability.address.GlobalDefaultAddressAbility", "invokeInfo": {"deep": 0, "endSubIdx": 15, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 15}, "runner": [{"baseType": true, "runnerResult": [{"type": "java.lang.Bo<PERSON>an", "value": true}], "templateCode": "com.alibaba.business.trade.general.GeneralTradeAbility", "templateType": "PRODUCT"}]}, {"abilityCode": "com.alibaba.trade.function.delivery.ability.address.GlobalAddressAbility", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "GlobalFulfillmentSpiCodes.EXT_FULFILLMENT_GET_FEATURES_CODE", "instanceCode": "com.alibaba.trade.function.delivery.ability.address.GlobalDefaultAddressAbility", "invokeInfo": {"deep": 0, "endSubIdx": 15, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 15}, "runner": [{"baseType": true, "runnerResult": [null], "templateCode": "com.alibaba.business.trade.general.GeneralTradeAbility", "templateType": "PRODUCT"}]}, {"abilityCode": "com.alibaba.business.trade.general.ability.order.common.enrich.GeneralTradeOrderReceiveInfoEnrichAbility", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "GENERAL#ORDER#ORDER_RECEIVE_INFO_ENRICH", "instanceCode": "com.alibaba.business.trade.general.ability.order.common.enrich.GeneralTradeOrderReceiveInfoEnrichAbility", "invokeInfo": {"deep": 1, "endSubIdx": 16, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 16}, "runner": [{"baseType": true, "runnerResult": [null], "templateCode": "ali.global.ae.russain.selfpick", "templateType": "PRODUCT"}]}, {"abilityCode": "com.alibaba.trade.function.delivery.ability.address.GlobalAddressAbility", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "GlobalFulfillmentSpiCodes.EXT_FULFILLMENT_ENRICH_SHIPPING_ADDRESS_CODE", "instanceCode": "com.alibaba.trade.function.delivery.ability.address.GlobalDefaultAddressAbility", "invokeInfo": {"deep": 0, "endSubIdx": 16, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 16}, "runner": [{"baseType": true, "runnerResult": [null], "templateCode": "com.alibaba.business.trade.general.GeneralTradeAbility", "templateType": "PRODUCT"}]}, {"abilityCode": "com.alibaba.business.trade.general.ability.delivery.GeneralTradeAddressAbility", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "GENERAL#BUYER_PLACE_ORDER_CHECK_RECEIVE_INFO", "instanceCode": "com.alibaba.business.trade.general.ability.delivery.GeneralTradeAddressAbility", "invokeInfo": {"deep": 1, "endSubIdx": 17, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 17}, "runner": [{"baseType": true, "runnerResult": [null], "templateCode": "ali.global.ae.address.restricted", "templateType": "PRODUCT"}]}, {"abilityCode": "com.alibaba.trade.function.delivery.ability.address.GlobalAddressAbility", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "GlobalFulfillmentSpiCodes.EXT_FULFILLMENT_CHECK_RECEIVE_INFO_CODE", "instanceCode": "com.alibaba.trade.function.delivery.ability.address.GlobalDefaultAddressAbility", "invokeInfo": {"deep": 0, "endSubIdx": 17, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 17}, "runner": [{"baseType": true, "runnerResult": [null], "templateCode": "com.alibaba.business.trade.general.GeneralTradeAbility", "templateType": "PRODUCT"}]}, {"abilityCode": "com.alibaba.business.trade.general.ability.order.common.config.GeneralTradeOrderConfigAbility", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "GENERAL#COMMON#ORDER_ORDER_LINE_GET_CONTRACT_CONFIG", "instanceCode": "com.alibaba.business.trade.general.ability.order.common.config.GeneralTradeOrderConfigAbility", "invokeInfo": {"deep": 1, "endSubIdx": 17, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 17}, "runner": [{"baseType": true, "runnerResult": [{"type": "com.alibaba.business.ability.order.general.global.activity.common.order.config.output.OrderConfigOutput", "value": "COMPLEX_OBJECT"}], "templateCode": "ali.global.ae.trade.general", "templateType": "BUSINESS"}]}, {"abilityCode": "com.alibaba.trade.functions.aftersale.ability.CustomerRightsAbility", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "EXT_IAFTERSALE_ORDERLINE_SKIP_PRODUCT_SERVICE_FEE ", "instanceCode": "com.alibaba.trade.function.aftersale.ability.DefaultCustomerRightsAbility", "invokeInfo": {"deep": 0, "endSubIdx": 17, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 17}, "runner": [{"baseType": true, "runnerResult": [{"type": "java.lang.Bo<PERSON>an", "value": false}], "templateCode": "com.alibaba.business.trade.general.GeneralTradeAbility", "templateType": "PRODUCT"}]}, {"abilityCode": "com.alibaba.business.trade.general.ability.order.common.config.GeneralTradeOrderConfigAbility", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "GENERAL#COMMON#ORDER_ORDER_LINE_GET_CONTRACT_CONFIG", "instanceCode": "com.alibaba.business.trade.general.ability.order.common.config.GeneralTradeOrderConfigAbility", "invokeInfo": {"deep": 1, "endSubIdx": 18, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 18}, "runner": [{"baseType": true, "runnerResult": [{"type": "com.alibaba.business.ability.order.general.global.activity.common.order.config.output.OrderConfigOutput", "value": "COMPLEX_OBJECT"}], "templateCode": "ali.global.ae.trade.general", "templateType": "BUSINESS"}]}, {"abilityCode": "com.alibaba.trade.functions.aftersale.ability.CustomerRightsAbility", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "EXT_IAFTERSALE_ORDERLINE_SKIP_RETURN_POLICY", "instanceCode": "com.alibaba.trade.function.aftersale.ability.DefaultCustomerRightsAbility", "invokeInfo": {"deep": 0, "endSubIdx": 18, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 18}, "runner": [{"baseType": true, "runnerResult": [{"type": "java.lang.Bo<PERSON>an", "value": true}], "templateCode": "com.alibaba.business.trade.general.GeneralTradeAbility", "templateType": "PRODUCT"}]}, {"abilityCode": "com.alibaba.business.trade.general.ability.order.common.config.GeneralTradeOrderConfigAbility", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "GENERAL#COMMON#ORDER_ORDER_LINE_GET_CONTRACT_CONFIG", "instanceCode": "com.alibaba.business.trade.general.ability.order.common.config.GeneralTradeOrderConfigAbility", "invokeInfo": {"deep": 1, "endSubIdx": 18, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 18}, "runner": [{"baseType": true, "runnerResult": [{"type": "com.alibaba.business.ability.order.general.global.activity.common.order.config.output.OrderConfigOutput", "value": "COMPLEX_OBJECT"}], "templateCode": "ali.global.ae.trade.general", "templateType": "BUSINESS"}]}, {"abilityCode": "com.alibaba.trade.functions.aftersale.ability.CustomerRightsAbility", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "EXT_IAFTERSALE_ORDERLINE_SKIP_RETURN_POLICY", "instanceCode": "com.alibaba.trade.function.aftersale.ability.DefaultCustomerRightsAbility", "invokeInfo": {"deep": 0, "endSubIdx": 18, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 18}, "runner": [{"baseType": true, "runnerResult": [{"type": "java.lang.Bo<PERSON>an", "value": true}], "templateCode": "com.alibaba.business.trade.general.GeneralTradeAbility", "templateType": "PRODUCT"}]}, {"abilityCode": "com.alibaba.business.trade.general.ability.order.common.enrich.GeneralTradeOrderBaseInfoEnrichAbility", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "GENERAL#ORDER#ORDER_BASE_INFO_ENRICH", "instanceCode": "com.alibaba.business.trade.general.ability.order.common.enrich.GeneralTradeOrderBaseInfoEnrichAbility", "invokeInfo": {"deep": 1, "endSubIdx": 21, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 20}, "runner": [{"baseType": true, "runnerResult": [{"type": "com.alibaba.business.ability.order.general.global.activity.common.order.enrich.output.EnrichOrderBaseInfoOutput", "value": "COMPLEX_OBJECT"}], "templateCode": "ali.global.ae.trade.general", "templateType": "BUSINESS"}]}, {"abilityCode": "com.alibaba.trade.function.order.ability.orderline.OrderLineBuildAbility", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "GlobalOrderSpiCodes.EXT_ORDER_ORDER_LINE_ENRICH_BUYER", "instanceCode": "com.alibaba.trade.function.order.ability.orderline.GlobalOrderLineBuildAbility", "invokeInfo": {"deep": 0, "endSubIdx": 21, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 20}, "runner": [{"baseType": true, "runnerResult": [null], "templateCode": "com.alibaba.business.trade.general.GeneralTradeAbility", "templateType": "PRODUCT"}]}, {"abilityCode": "com.alibaba.business.trade.general.ability.order.common.enrich.GeneralTradeOrderBaseInfoEnrichAbility", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "GENERAL#ORDER#ORDER_BASE_INFO_ENRICH", "instanceCode": "com.alibaba.business.trade.general.ability.order.common.enrich.GeneralTradeOrderBaseInfoEnrichAbility", "invokeInfo": {"deep": 1, "endSubIdx": 22, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 21}, "runner": [{"baseType": true, "runnerResult": [{"type": "com.alibaba.business.ability.order.general.global.activity.common.order.enrich.output.EnrichOrderBaseInfoOutput", "value": "COMPLEX_OBJECT"}], "templateCode": "ali.global.ae.trade.general", "templateType": "BUSINESS"}]}, {"abilityCode": "com.alibaba.trade.function.order.ability.orderline.OrderLineBuildAbility", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "GlobalOrderSpiCodes.EXT_ORDER_ORDER_LINE_ENRICH_SELLER", "instanceCode": "com.alibaba.trade.function.order.ability.orderline.GlobalOrderLineBuildAbility", "invokeInfo": {"deep": 0, "endSubIdx": 22, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 21}, "runner": [{"baseType": true, "runnerResult": [null], "templateCode": "com.alibaba.business.trade.general.GeneralTradeAbility", "templateType": "PRODUCT"}]}, {"abilityCode": "com.alibaba.business.trade.general.ability.order.common.enrich.GeneralTradeOrderBaseInfoEnrichAbility", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "GENERAL#ORDER#ORDER_BASE_INFO_ENRICH", "instanceCode": "com.alibaba.business.trade.general.ability.order.common.enrich.GeneralTradeOrderBaseInfoEnrichAbility", "invokeInfo": {"deep": 1, "endSubIdx": 23, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 22}, "runner": [{"baseType": true, "runnerResult": [{"type": "com.alibaba.business.ability.order.general.global.activity.common.order.enrich.output.EnrichOrderBaseInfoOutput", "value": "COMPLEX_OBJECT"}], "templateCode": "ali.global.ae.trade.general", "templateType": "BUSINESS"}]}, {"abilityCode": "com.alibaba.trade.function.order.ability.orderline.OrderLineBuildAbility", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "GlobalOrderSpiCodes.EXT_ORDER_ORDER_LINE_ENRICH_PRODUCT", "instanceCode": "com.alibaba.trade.function.order.ability.orderline.GlobalOrderLineBuildAbility", "invokeInfo": {"deep": 0, "endSubIdx": 23, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 22}, "runner": [{"baseType": true, "runnerResult": [{"type": "com.alibaba.business.global.shared.model.product.Product", "value": "COMPLEX_OBJECT"}], "templateCode": "com.alibaba.business.trade.general.GeneralTradeAbility", "templateType": "PRODUCT"}]}, {"abilityCode": "com.alibaba.business.trade.general.ability.order.common.enrich.GeneralTradeOrderBaseInfoEnrichAbility", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "GENERAL#ORDER#ORDER_BASE_INFO_ENRICH", "instanceCode": "com.alibaba.business.trade.general.ability.order.common.enrich.GeneralTradeOrderBaseInfoEnrichAbility", "invokeInfo": {"deep": 1, "endSubIdx": 23, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 23}, "runner": [{"baseType": true, "runnerResult": [{"type": "com.alibaba.business.ability.order.general.global.activity.common.order.enrich.output.EnrichOrderBaseInfoOutput", "value": "COMPLEX_OBJECT"}], "templateCode": "ali.global.ae.trade.general", "templateType": "BUSINESS"}]}, {"abilityCode": "com.alibaba.trade.function.order.ability.orderline.OrderLineBuildAbility", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "GlobalOrderSpiCodes.EXT_ORDER_ORDER_LINE_ENRICH_SHOP", "instanceCode": "com.alibaba.trade.function.order.ability.orderline.GlobalOrderLineBuildAbility", "invokeInfo": {"deep": 0, "endSubIdx": 23, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 23}, "runner": [{"baseType": true, "runnerResult": [null], "templateCode": "com.alibaba.business.trade.general.GeneralTradeAbility", "templateType": "PRODUCT"}]}, {"abilityCode": "com.alibaba.business.trade.general.ability.order.place.create.render.GeneralTradeOrderRenderAbility", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "GENERAL#BUYER_PLACE_ORDER#GET_GROUPING_INFO", "instanceCode": "com.alibaba.business.trade.general.ability.order.place.create.render.GeneralTradeOrderRenderAbility", "invokeInfo": {"deep": 1, "endSubIdx": 23, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 23}, "runner": [{"baseType": true, "runnerResult": [{"type": "java.util.HashSet", "value": 3}], "templateCode": "ali.global.ae.trade.general", "templateType": "BUSINESS"}]}, {"abilityCode": "com.alibaba.trade.function.order.ability.order.OrderBuildAbility", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "EXT_TRADE_ORDER_GROUPING_GET_GROUPING_INFO_KEY_CODE", "instanceCode": "com.alibaba.trade.function.order.ability.order.GlobalOrderBuildAbility", "invokeInfo": {"deep": 0, "endSubIdx": 23, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 23}, "runner": [{"baseType": true, "runnerResult": [{"type": "java.util.HashSet", "value": 3}], "templateCode": "com.alibaba.business.trade.general.GeneralTradeAbility", "templateType": "PRODUCT"}]}, {"abilityCode": "com.alibaba.business.trade.general.ability.order.place.create.render.GeneralTradeOrderRenderAbility", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "GENERAL#BUYER_PLACE_ORDER#SPLIT_GRUPING_TYPE", "instanceCode": "com.alibaba.business.trade.general.ability.order.place.create.render.GeneralTradeOrderRenderAbility", "invokeInfo": {"deep": 1, "endSubIdx": 24, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 24}, "runner": [{"baseType": true, "runnerResult": [{"type": "java.util.HashSet", "value": 3}], "templateCode": "ali.global.ae.trade.general", "templateType": "BUSINESS"}]}, {"abilityCode": "com.alibaba.trade.function.order.ability.order.OrderBuildAbility", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "EXT_TRADE_ORDER_GROUPING_GET_SPLIT_GROUPING_TYPE_CODE", "instanceCode": "com.alibaba.trade.function.order.ability.order.GlobalOrderBuildAbility", "invokeInfo": {"deep": 0, "endSubIdx": 24, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 24}, "runner": [{"baseType": true, "runnerResult": [{"type": "java.util.HashSet", "value": 3}], "templateCode": "com.alibaba.business.trade.general.GeneralTradeAbility", "templateType": "PRODUCT"}]}, {"abilityCode": "com.alibaba.business.trade.general.ability.order.common.enrich.GeneralTradeOrderBaseInfoEnrichAbility", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "GENERAL#ORDER#ORDER_BASE_INFO_ENRICH", "instanceCode": "com.alibaba.business.trade.general.ability.order.common.enrich.GeneralTradeOrderBaseInfoEnrichAbility", "invokeInfo": {"deep": 1, "endSubIdx": 25, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 25}, "runner": [{"baseType": true, "runnerResult": [{"type": "com.alibaba.business.ability.order.general.global.activity.common.order.enrich.output.EnrichOrderBaseInfoOutput", "value": "COMPLEX_OBJECT"}], "templateCode": "ali.global.ae.trade.general", "templateType": "BUSINESS"}]}, {"abilityCode": "com.alibaba.trade.function.order.ability.orderline.OrderLineBuildAbility", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "GlobalOrderSpiCodes.EXT_ORDER_ORDER_LINE_ENRICH_BUYER", "instanceCode": "com.alibaba.trade.function.order.ability.orderline.GlobalOrderLineBuildAbility", "invokeInfo": {"deep": 0, "endSubIdx": 25, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 25}, "runner": [{"baseType": true, "runnerResult": [null], "templateCode": "com.alibaba.business.trade.general.GeneralTradeAbility", "templateType": "PRODUCT"}]}, {"abilityCode": "com.alibaba.business.trade.general.ability.order.common.enrich.GeneralTradeOrderBaseInfoEnrichAbility", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "GENERAL#ORDER#ORDER_BASE_INFO_ENRICH", "instanceCode": "com.alibaba.business.trade.general.ability.order.common.enrich.GeneralTradeOrderBaseInfoEnrichAbility", "invokeInfo": {"deep": 1, "endSubIdx": 25, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 25}, "runner": [{"baseType": true, "runnerResult": [{"type": "com.alibaba.business.ability.order.general.global.activity.common.order.enrich.output.EnrichOrderBaseInfoOutput", "value": "COMPLEX_OBJECT"}], "templateCode": "ali.global.ae.trade.general", "templateType": "BUSINESS"}]}, {"abilityCode": "com.alibaba.trade.function.order.ability.orderline.OrderLineBuildAbility", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "GlobalOrderSpiCodes.EXT_ORDER_ORDER_LINE_ENRICH_SELLER", "instanceCode": "com.alibaba.trade.function.order.ability.orderline.GlobalOrderLineBuildAbility", "invokeInfo": {"deep": 0, "endSubIdx": 25, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 25}, "runner": [{"baseType": true, "runnerResult": [null], "templateCode": "com.alibaba.business.trade.general.GeneralTradeAbility", "templateType": "PRODUCT"}]}, {"abilityCode": "com.alibaba.business.trade.general.ability.order.common.enrich.GeneralTradeOrderBaseInfoEnrichAbility", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "GENERAL#ORDER#ORDER_BASE_INFO_ENRICH", "instanceCode": "com.alibaba.business.trade.general.ability.order.common.enrich.GeneralTradeOrderBaseInfoEnrichAbility", "invokeInfo": {"deep": 1, "endSubIdx": 25, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 25}, "runner": [{"baseType": true, "runnerResult": [{"type": "com.alibaba.business.ability.order.general.global.activity.common.order.enrich.output.EnrichOrderBaseInfoOutput", "value": "COMPLEX_OBJECT"}], "templateCode": "ali.global.ae.trade.general", "templateType": "BUSINESS"}]}, {"abilityCode": "com.alibaba.trade.function.order.ability.orderline.OrderLineBuildAbility", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "GlobalOrderSpiCodes.EXT_ORDER_ORDER_LINE_ENRICH_PRODUCT", "instanceCode": "com.alibaba.trade.function.order.ability.orderline.GlobalOrderLineBuildAbility", "invokeInfo": {"deep": 0, "endSubIdx": 25, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 25}, "runner": [{"baseType": true, "runnerResult": [{"type": "com.alibaba.business.global.shared.model.product.Product", "value": "COMPLEX_OBJECT"}], "templateCode": "com.alibaba.business.trade.general.GeneralTradeAbility", "templateType": "PRODUCT"}]}, {"abilityCode": "com.alibaba.business.trade.general.ability.order.common.enrich.GeneralTradeOrderBaseInfoEnrichAbility", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "GENERAL#ORDER#ORDER_BASE_INFO_ENRICH", "instanceCode": "com.alibaba.business.trade.general.ability.order.common.enrich.GeneralTradeOrderBaseInfoEnrichAbility", "invokeInfo": {"deep": 1, "endSubIdx": 25, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 25}, "runner": [{"baseType": true, "runnerResult": [{"type": "com.alibaba.business.ability.order.general.global.activity.common.order.enrich.output.EnrichOrderBaseInfoOutput", "value": "COMPLEX_OBJECT"}], "templateCode": "ali.global.ae.trade.general", "templateType": "BUSINESS"}]}, {"abilityCode": "com.alibaba.trade.function.order.ability.orderline.OrderLineBuildAbility", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "GlobalOrderSpiCodes.EXT_ORDER_ORDER_LINE_ENRICH_SHOP", "instanceCode": "com.alibaba.trade.function.order.ability.orderline.GlobalOrderLineBuildAbility", "invokeInfo": {"deep": 0, "endSubIdx": 25, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 25}, "runner": [{"baseType": true, "runnerResult": [null], "templateCode": "com.alibaba.business.trade.general.GeneralTradeAbility", "templateType": "PRODUCT"}]}, {"abilityCode": "com.alibaba.trade.function.price.ability.PriceItemBuildAbility", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "EXT_PRICE_ORDER_LINE_PRICE_GET_ADDITIONAL_FEE", "instanceCode": "com.alibaba.trade.function.price.ability.GlobalPriceItemBuildAbility", "invokeInfo": {"deep": 0, "endSubIdx": 25, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 25}, "runner": null}, {"abilityCode": "com.alibaba.trade.function.price.ability.PriceItemBuildAbility", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "EXT_PRICE_ORDER_LINE_PRICE_CUSTOM_ORDER_LINE_UNIT_FEE_CODE", "instanceCode": "com.alibaba.trade.function.price.ability.GlobalPriceItemBuildAbility", "invokeInfo": {"deep": 0, "endSubIdx": 25, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 25}, "runner": null}, {"abilityCode": "com.alibaba.business.trade.general.ability.order.common.config.GeneralTradeOrderConfigAbility", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "GENERAL#COMMON#ORDER_ORDER_LINE_GET_CONTRACT_CONFIG", "instanceCode": "com.alibaba.business.trade.general.ability.order.common.config.GeneralTradeOrderConfigAbility", "invokeInfo": {"deep": 1, "endSubIdx": 25, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 25}, "runner": [{"baseType": true, "runnerResult": [{"type": "com.alibaba.business.ability.order.general.global.activity.common.order.config.output.OrderConfigOutput", "value": "COMPLEX_OBJECT"}], "templateCode": "ali.global.ae.trade.general", "templateType": "BUSINESS"}]}, {"abilityCode": "com.alibaba.trade.function.delivery.ability.transportMethod.TransportMethodAbilityV2", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "GlobalFulfillmentSpiCodes.EXT_FULFILLMENT_NEED_TRANSPORT_METHOD_CODE", "instanceCode": "com.alibaba.trade.function.delivery.ability.transportmethod.GlobalTransportMethodAbility", "invokeInfo": {"deep": 0, "endSubIdx": 25, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 25}, "runner": [{"baseType": true, "runnerResult": [{"type": "java.lang.Bo<PERSON>an", "value": true}], "templateCode": "com.alibaba.business.trade.general.GeneralTradeAbility", "templateType": "PRODUCT"}]}, {"abilityCode": "com.alibaba.business.trade.general.ability.delivery.GeneralTradeDeliveryAbility", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "GENERAL#BUYER_PLACE_ORDER#CONFIRM_OTHER_PARAMS", "instanceCode": "com.alibaba.business.trade.general.ability.delivery.GeneralTradeDeliveryAbility", "invokeInfo": {"deep": 1, "endSubIdx": 25, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 25}, "runner": [{"baseType": true, "runnerResult": [{"type": "com.alibaba.business.ability.order.general.global.activity.place.delivery.transport.output.TransportExtraParamOutput", "value": "COMPLEX_OBJECT"}], "templateCode": "ali.global.ae.trade.general", "templateType": "BUSINESS"}]}, {"abilityCode": "com.alibaba.trade.function.delivery.ability.transportMethod.TransportMethodAbilityV2", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "EXT_FULFILLMENT_CUSTOM_QUERY_ENRICH_EXTRA_PARAMS_CODE", "instanceCode": "com.alibaba.trade.function.delivery.ability.transportmethod.GlobalTransportMethodAbility", "invokeInfo": {"deep": 0, "endSubIdx": 25, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 25}, "runner": [{"baseType": true, "runnerResult": [{"type": "com.alibaba.business.domain.global.fulfillment.spi.transport.query.reponse.TransportExtraParams", "value": "COMPLEX_OBJECT"}], "templateCode": "com.alibaba.business.trade.general.GeneralTradeAbility", "templateType": "PRODUCT"}]}, {"abilityCode": "com.alibaba.business.trade.general.ability.delivery.GeneralTradeDeliveryAbility", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "GENERAL#BUYER_PLACE_ORDER#QUERY_PRICE_PARAMS", "instanceCode": "com.alibaba.business.trade.general.ability.delivery.GeneralTradeDeliveryAbility", "invokeInfo": {"deep": 1, "endSubIdx": 25, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 25}, "runner": [{"baseType": true, "runnerResult": [{"type": "com.alibaba.business.ability.order.general.global.activity.place.delivery.transport.output.TransportPriceParamOutput", "value": "COMPLEX_OBJECT"}], "templateCode": "ali.global.ae.trade.general", "templateType": "BUSINESS"}]}, {"abilityCode": "com.alibaba.trade.function.delivery.ability.transportMethod.TransportMethodAbilityV2", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "EXT_FULFILLMENT_CUSTOM_QUERY_ENRICH_PRICE_PARAMS_CODE", "instanceCode": "com.alibaba.trade.function.delivery.ability.transportmethod.GlobalTransportMethodAbility", "invokeInfo": {"deep": 0, "endSubIdx": 25, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 25}, "runner": [{"baseType": true, "runnerResult": [{"type": "com.alibaba.business.domain.global.fulfillment.spi.transport.query.reponse.TransportPriceParams", "value": "COMPLEX_OBJECT"}], "templateCode": "com.alibaba.business.trade.general.GeneralTradeAbility", "templateType": "PRODUCT"}]}, {"abilityCode": "com.alibaba.business.trade.general.ability.delivery.GeneralTradeAddressAbility", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "GENERAL#BUYER_PLACE_ORDER_BUILD_ADDRESS_QUERY_EXTRA_PARAMS", "instanceCode": "com.alibaba.business.trade.general.ability.delivery.GeneralTradeAddressAbility", "invokeInfo": {"deep": 1, "endSubIdx": 29, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 29}, "runner": [{"baseType": true, "runnerResult": [{"type": "com.alibaba.business.ability.order.general.global.activity.place.delivery.receive.output.ReceiveMethodQueryExtraParamOutput", "value": "COMPLEX_OBJECT"}], "templateCode": "ali.global.ae.russain.selfpick", "templateType": "PRODUCT"}]}, {"abilityCode": "com.alibaba.trade.function.delivery.ability.address.GlobalAddressAbility", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "GlobalOrderSpiCodes.EXT_FULFILLMENT_BUILD_ADDRESS_QUERY_EXTRA_PARAMS_CODE", "instanceCode": "com.alibaba.trade.function.delivery.ability.address.GlobalDefaultAddressAbility", "invokeInfo": {"deep": 0, "endSubIdx": 29, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 29}, "runner": [{"baseType": true, "runnerResult": [{"type": "com.alibaba.business.domain.global.fulfillment.spi.receive.response.ReceiveMethodQueryExtraParams", "value": "COMPLEX_OBJECT"}], "templateCode": "com.alibaba.business.trade.general.GeneralTradeAbility", "templateType": "PRODUCT"}]}, {"abilityCode": "com.alibaba.business.trade.general.ability.order.common.config.GeneralTradeOrderConfigAbility", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "GENERAL#COMMON#ORDER_ORDER_LINE_GET_CONTRACT_CONFIG", "instanceCode": "com.alibaba.business.trade.general.ability.order.common.config.GeneralTradeOrderConfigAbility", "invokeInfo": {"deep": 1, "endSubIdx": 30, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 30}, "runner": [{"baseType": true, "runnerResult": [{"type": "com.alibaba.business.ability.order.general.global.activity.common.order.config.output.OrderConfigOutput", "value": "COMPLEX_OBJECT"}], "templateCode": "ali.global.ae.trade.general", "templateType": "BUSINESS"}]}, {"abilityCode": "com.alibaba.trade.function.delivery.ability.address.GlobalAddressAbility", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "GlobalFulfillmentSpiCodes.EXT_FULFILLMENT_NEED_SHIPPING_ADDRESS_CODE", "instanceCode": "com.alibaba.trade.function.delivery.ability.address.GlobalDefaultAddressAbility", "invokeInfo": {"deep": 0, "endSubIdx": 30, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 30}, "runner": [{"baseType": true, "runnerResult": [{"type": "java.lang.Bo<PERSON>an", "value": true}], "templateCode": "com.alibaba.business.trade.general.GeneralTradeAbility", "templateType": "PRODUCT"}]}, {"abilityCode": "com.alibaba.business.trade.general.ability.order.common.enrich.GeneralTradeOrderReceiveInfoEnrichAbility", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "GENERAL#ORDER#ORDER_RECEIVE_INFO_ENRICH", "instanceCode": "com.alibaba.business.trade.general.ability.order.common.enrich.GeneralTradeOrderReceiveInfoEnrichAbility", "invokeInfo": {"deep": 1, "endSubIdx": 30, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 30}, "runner": [{"baseType": true, "runnerResult": [{"type": "com.alibaba.business.ability.order.general.global.activity.common.order.enrich.output.EnrichOrderReceiveInfoOutput", "value": "COMPLEX_OBJECT"}], "templateCode": "ali.global.ae.russain.selfpick", "templateType": "PRODUCT"}]}, {"abilityCode": "com.alibaba.trade.function.delivery.ability.address.GlobalAddressAbility", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "GlobalFulfillmentSpiCodes.EXT_FULFILLMENT_ENRICH_SHIPPING_ADDRESS_CODE", "instanceCode": "com.alibaba.trade.function.delivery.ability.address.GlobalDefaultAddressAbility", "invokeInfo": {"deep": 0, "endSubIdx": 30, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 30}, "runner": [{"baseType": true, "runnerResult": [null], "templateCode": "com.alibaba.business.trade.general.GeneralTradeAbility", "templateType": "PRODUCT"}]}, {"abilityCode": "com.alibaba.trade.function.price.ability.PriceItemBuildAbility", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "EXT_PRICE_ORDER_LINE_PRICE_GET_ADDITIONAL_FEE", "instanceCode": "com.alibaba.trade.function.price.ability.GlobalPriceItemBuildAbility", "invokeInfo": {"deep": 0, "endSubIdx": 31, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 31}, "runner": null}, {"abilityCode": "com.alibaba.trade.function.price.ability.PriceItemBuildAbility", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "EXT_PRICE_ORDER_LINE_PRICE_CUSTOM_ORDER_LINE_UNIT_FEE_CODE", "instanceCode": "com.alibaba.trade.function.price.ability.GlobalPriceItemBuildAbility", "invokeInfo": {"deep": 0, "endSubIdx": 31, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 31}, "runner": null}, {"abilityCode": "com.alibaba.trade.function.price.ability.PriceItemBuildAbility", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "EXT_PRICE_ORDER_LINE_PRICE_GET_ADDITIONAL_FEE", "instanceCode": "com.alibaba.trade.function.price.ability.GlobalPriceItemBuildAbility", "invokeInfo": {"deep": 0, "endSubIdx": 31, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 31}, "runner": null}, {"abilityCode": "com.alibaba.trade.function.price.ability.PriceItemBuildAbility", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "EXT_PRICE_ORDER_LINE_PRICE_CUSTOM_ORDER_LINE_UNIT_FEE_CODE", "instanceCode": "com.alibaba.trade.function.price.ability.GlobalPriceItemBuildAbility", "invokeInfo": {"deep": 0, "endSubIdx": 31, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 31}, "runner": null}, {"abilityCode": "com.alibaba.business.trade.general.ability.order.common.config.GeneralTradeOrderConfigAbility", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "GENERAL#COMMON#ORDER_ORDER_LINE_GET_CONTRACT_CONFIG", "instanceCode": "com.alibaba.business.trade.general.ability.order.common.config.GeneralTradeOrderConfigAbility", "invokeInfo": {"deep": 1, "endSubIdx": 31, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 31}, "runner": [{"baseType": true, "runnerResult": [{"type": "com.alibaba.business.ability.order.general.global.activity.common.order.config.output.OrderConfigOutput", "value": "COMPLEX_OBJECT"}], "templateCode": "ali.global.ae.trade.general", "templateType": "BUSINESS"}]}, {"abilityCode": "com.alibaba.trade.functions.inventory.ability.query.InventoryQueryAbilityV2", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "EXT_INVENTORY_ORDERLINE_SKIP_QUERY_CODE", "instanceCode": "com.alibaba.trade.function.inventory.ability.query.GlobalInventoryQueryAbility", "invokeInfo": {"deep": 0, "endSubIdx": 31, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 31}, "runner": [{"baseType": true, "runnerResult": [{"type": "java.lang.Bo<PERSON>an", "value": false}], "templateCode": "com.alibaba.business.trade.general.GeneralTradeAbility", "templateType": "PRODUCT"}]}, {"abilityCode": "com.alibaba.trade.functions.inventory.ability.query.InventoryQueryAbilityV2", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "EXT_INVENTORY_ORDERLINE_ENRICH_QUERY_PARAM_CODE", "instanceCode": "com.alibaba.trade.function.inventory.ability.query.GlobalInventoryQueryAbility", "invokeInfo": {"deep": 0, "endSubIdx": 31, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 31}, "runner": null}, {"abilityCode": "com.alibaba.trade.function.price.ability.PriceItemBuildAbility", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "EXT_PRICE_ORDER_LINE_PRICE_GET_ADDITIONAL_FEE", "instanceCode": "com.alibaba.trade.function.price.ability.GlobalPriceItemBuildAbility", "invokeInfo": {"deep": 0, "endSubIdx": 32, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 32}, "runner": null}, {"abilityCode": "com.alibaba.trade.function.price.ability.PriceItemBuildAbility", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "EXT_PRICE_ORDER_LINE_PRICE_CUSTOM_ORDER_LINE_UNIT_FEE_CODE", "instanceCode": "com.alibaba.trade.function.price.ability.GlobalPriceItemBuildAbility", "invokeInfo": {"deep": 0, "endSubIdx": 32, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 32}, "runner": null}, {"abilityCode": "com.alibaba.trade.function.price.ability.PriceItemBuildAbility", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "EXT_PRICE_ORDER_LINE_PRICE_GET_ADDITIONAL_FEE", "instanceCode": "com.alibaba.trade.function.price.ability.GlobalPriceItemBuildAbility", "invokeInfo": {"deep": 0, "endSubIdx": 32, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 32}, "runner": null}, {"abilityCode": "com.alibaba.trade.function.price.ability.PriceItemBuildAbility", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "EXT_PRICE_ORDER_LINE_PRICE_CUSTOM_ORDER_LINE_UNIT_FEE_CODE", "instanceCode": "com.alibaba.trade.function.price.ability.GlobalPriceItemBuildAbility", "invokeInfo": {"deep": 0, "endSubIdx": 32, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 32}, "runner": null}, {"abilityCode": "com.alibaba.trade.function.payment.ability.PaymentCashierAbility", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "EXT_PAYMENT_CASHIER_QUERY_IS_RENDER_PRE_CASHIER", "instanceCode": "com.alibaba.trade.function.payment.ability.cashier.GlobalPaymentCashierAbility", "invokeInfo": {"deep": 0, "endSubIdx": 32, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 32}, "runner": null}, {"abilityCode": "com.alibaba.business.trade.general.ability.order.place.create.render.GeneralTradeRenderPaymentAbility", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "GENERAL#BUYER_PLACE_ORDER_ENRICH_RENDER_CASHIER_PARAMS", "instanceCode": "com.alibaba.business.trade.general.ability.order.place.create.render.GeneralTradeRenderPaymentAbility", "invokeInfo": {"deep": 1, "endSubIdx": 32, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 32}, "runner": [{"baseType": true, "runnerResult": [{"type": "com.alibaba.business.ability.order.general.global.activity.place.payment.output.PaymentCashierExtraParamOutput", "value": "COMPLEX_OBJECT"}], "templateCode": "ali.global.ae.trade.general", "templateType": "BUSINESS"}]}, {"abilityCode": "com.alibaba.trade.function.payment.ability.PaymentCashierAbility", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "EXT_PAYMENT_CASHIER_QUERY_BUILD_EXTRA_PARAMS_CODE", "instanceCode": "com.alibaba.trade.function.payment.ability.cashier.GlobalPaymentCashierAbility", "invokeInfo": {"deep": 0, "endSubIdx": 32, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 32}, "runner": [{"baseType": true, "runnerResult": [{"type": "com.alibaba.business.domain.global.payment.spi.cashier.query.req.param.GlobalCashierQueryExtraParam", "value": "COMPLEX_OBJECT"}], "templateCode": "com.alibaba.business.trade.general.GeneralTradeAbility", "templateType": "PRODUCT"}]}, {"abilityCode": "com.alibaba.business.trade.general.ability.order.common.validate.GeneralTradeOrderValidateAbility", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "GENERAL#ORDER#ORDER_VALIDATE_EVENT", "instanceCode": "com.alibaba.business.trade.general.ability.order.common.validate.GeneralTradeOrderValidateAbility", "invokeInfo": {"deep": 1, "endSubIdx": 34, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 34}, "runner": [{"baseType": true, "runnerResult": [null], "templateCode": "ali.global.ae.trade.general", "templateType": "BUSINESS"}]}, {"abilityCode": "com.alibaba.trade.function.order.ability.order.OrderValidateAbility", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "OrderSpiCodes.EXT_FINAL_VALIDATE_ORDER_BEFORE_RENDERING_CODE", "instanceCode": "com.alibaba.trade.function.order.ability.order.GlobalOrderValidateAbility", "invokeInfo": {"deep": 0, "endSubIdx": 34, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 34}, "runner": [{"baseType": true, "runnerResult": [null], "templateCode": "com.alibaba.business.trade.general.GeneralTradeAbility", "templateType": "PRODUCT"}]}, {"abilityCode": "com.alibaba.business.trade.general.ability.order.common.validate.GeneralTradeOrderValidateAbility", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "GENERAL#ORDER#ORDER_VALIDATE_EVENT", "instanceCode": "com.alibaba.business.trade.general.ability.order.common.validate.GeneralTradeOrderValidateAbility", "invokeInfo": {"deep": 1, "endSubIdx": 34, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 34}, "runner": [{"baseType": true, "runnerResult": [null], "templateCode": "ali.global.ae.trade.general", "templateType": "BUSINESS"}]}, {"abilityCode": "com.alibaba.trade.function.order.ability.orderline.OrderLineValidateAbility", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "OrderSpiCodes.EXT_FINAL_VALIDATE_ORDER_LINE_BEFORE_RENDERING_CODE", "instanceCode": "com.alibaba.trade.function.order.ability.orderline.GlobalOrderLineValidateAbility", "invokeInfo": {"deep": 0, "endSubIdx": 34, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 34}, "runner": [{"baseType": true, "runnerResult": [null], "templateCode": "com.alibaba.business.trade.general.GeneralTradeAbility", "templateType": "PRODUCT"}]}, {"abilityCode": "com.alibaba.business.trade.general.ability.order.place.page.GeneralCheckoutOrderLineAbility", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "GENERAL#BUYER_PLACE_ORDER_ENRICH_RENDER_ORDER_RESULT", "instanceCode": "com.alibaba.business.trade.general.ability.order.place.page.GeneralCheckoutOrderLineAbility", "invokeInfo": {"deep": 1, "endSubIdx": 34, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 34}, "runner": [{"baseType": true, "runnerResult": [null], "templateCode": "ali.global.ae.trade.general", "templateType": "BUSINESS"}]}, {"abilityCode": "com.alibaba.trade.function.order.ability.view.GlobalToRenderOrderResultPageAbility", "bizCode": "ali.global.ae.trade.general", "error": null, "extensionCode": "GlobalOrderSpiCodes.EXT_ORDER_ENRICH_RENDER_ORDER_RESULT_CODE", "instanceCode": "com.alibaba.trade.function.order.ability.orderline.GlobalOrderLineEnrichRenderOrderResultAbility", "invokeInfo": {"deep": 0, "endSubIdx": 34, "invokeContext": null, "needCollectTrace": true, "replay": false, "startSubIdx": 34}, "runner": [{"baseType": true, "runnerResult": [{"type": "com.alibaba.business.domain.global.order.spi.page.checkout.response.RenderOrderResultResp", "value": "COMPLEX_OBJECT"}], "templateCode": "com.alibaba.business.trade.general.GeneralTradeAbility", "templateType": "PRODUCT"}]}]}, "landlord_glkey": "AE_GLOBAL@US|en-US|USD|America/Los_Angeles#", "tmf_stack": {"type": "Entrance", "code": "com.alibaba.global.buy.api.facade.CheckoutMultiterminalFacade@renderOrder(RenderOrderRequest):1.0.0", "extInfo": {}, "subNodes": [{"type": "SubInvoke", "code": "com.alibaba.global.ic.api.CustomerProductServiceFacade@queryProduct(ProductQueryRequest)", "extInfo": {}, "subNodes": []}, {"type": "SubInvoke", "code": "com.alibaba.global.category.api.StdCategoryService@getStdCategory(Long,Locale)", "extInfo": {}, "subNodes": []}, {"type": "SubInvoke", "code": "com.alibaba.global.category.api.StdCategoryService@getStdCategory(Long,Locale)", "extInfo": {}, "subNodes": []}, {"type": "SubInvoke", "code": "com.alibaba.global.merchant.seller.api.facade.SellerReadFacadeForTrade@getBatchSellersInfoBySellerIds(List,List,List)", "extInfo": {}, "subNodes": []}, {"type": "SubInvoke", "code": "com.alibaba.global.merchant.seller.api.facade.SellerTagFacade@getSellerBizCodes(Long)", "extInfo": {}, "subNodes": []}, {"type": "SubInvoke", "code": "com.alibaba.global.user.api.facade.UserReadFacade@getUserById(Long)", "extInfo": {}, "subNodes": []}, {"type": "SubInvoke", "code": "com.alibaba.global.user.api.facade.UserBoolTagFacade@hasUserTag(Long,List)", "extInfo": {}, "subNodes": []}, {"type": "SubInvoke", "code": "com.alibaba.global.user.api.facade.UserDataTagFacade@getUserTag(Long,List)", "extInfo": {}, "subNodes": []}, {"type": "SubInvoke", "code": "com.alibaba.global.shop.api.facade.ShopReadFacade@listShopBySellerIdList(List)", "extInfo": {}, "subNodes": []}, {"type": "SubInvoke", "code": "com.alibaba.trade.shared.utils.SequenceGenerator@next(String)", "extInfo": {}, "subNodes": []}, {"type": "SubInvoke", "code": "com.alibaba.trade.shared.utils.SequenceGenerator@next(String)", "extInfo": {}, "subNodes": []}, {"type": "SubInvoke", "code": "com.alibaba.trade.shared.utils.SequenceGenerator@next(String)", "extInfo": {}, "subNodes": []}, {"type": "DomainSDK", "code": "OrderSpiCodes.EXT_INITIAL_VALIDATE_ORDER_BEFORE_RENDERING_CODE", "extInfo": {"runner": [{"templateCode": "com.alibaba.business.trade.general.GeneralTradeAbility", "templateType": "PRODUCT"}]}, "subNodes": [{"type": "BusinessSDK", "code": "GENERAL#ORDER#ORDER_VALIDATE_EVENT", "extInfo": {"runner": [{"templateCode": "ali.global.ae.trade.general", "templateType": "BUSINESS"}]}, "subNodes": []}]}, {"type": "DomainSDK", "code": "GlobalOrderSpiCodes.EXT_ORDER_ORDER_LINE_INITIAL_VALIDATE_ORDER_LINE_BEFORE_RENDERING_CODE", "extInfo": {"runner": [{"templateCode": "com.alibaba.business.trade.general.GeneralTradeAbility", "templateType": "PRODUCT"}]}, "subNodes": [{"type": "BusinessSDK", "code": "GENERAL#ORDER#ORDER_VALIDATE_EVENT", "extInfo": {"runner": [{"templateCode": "ali.global.ae.trade.general", "templateType": "BUSINESS"}]}, "subNodes": [{"type": "SubInvoke", "code": "com.taobao.tair.TairManager@mget(int,List)", "extInfo": {}, "subNodes": []}]}]}, {"type": "DomainSDK", "code": "GlobalOrderSpiCodes.EXT_ORDER_ORDER_LINE_INITIAL_VALIDATE_ORDER_LINE_BEFORE_RENDERING_CODE", "extInfo": {"runner": [{"templateCode": "com.alibaba.business.trade.general.GeneralTradeAbility", "templateType": "PRODUCT"}]}, "subNodes": [{"type": "BusinessSDK", "code": "GENERAL#ORDER#ORDER_VALIDATE_EVENT", "extInfo": {"runner": [{"templateCode": "ali.global.ae.trade.general", "templateType": "BUSINESS"}]}, "subNodes": [{"type": "SubInvoke", "code": "com.taobao.tair.TairManager@mget(int,List)", "extInfo": {}, "subNodes": []}]}]}, {"type": "DomainSDK", "code": "GlobalFulfillmentSpiCodes.EXT_FULFILLMENT_NEED_SHIPPING_ADDRESS_CODE", "extInfo": {"runner": [{"templateCode": "com.alibaba.business.trade.general.GeneralTradeAbility", "templateType": "PRODUCT"}]}, "subNodes": [{"type": "BusinessSDK", "code": "GENERAL#COMMON#ORDER_ORDER_LINE_GET_CONTRACT_CONFIG", "extInfo": {"runner": [{"templateCode": "ali.global.ae.trade.general", "templateType": "BUSINESS"}]}, "subNodes": []}]}, {"type": "DomainSDK", "code": "GlobalFulfillmentSpiCodes.EXT_FULFILLMENT_GET_FEATURES_CODE", "extInfo": {"runner": [{"templateCode": "com.alibaba.business.trade.general.GeneralTradeAbility", "templateType": "PRODUCT"}]}, "subNodes": []}, {"type": "SubInvoke", "code": "com.alibaba.global.address.api.facade.UserAddressReadFacade@listUserAddress(UserAddressQueryRequest)", "extInfo": {}, "subNodes": []}, {"type": "DomainSDK", "code": "GlobalFulfillmentSpiCodes.EXT_FULFILLMENT_ENRICH_SHIPPING_ADDRESS_CODE", "extInfo": {"runner": [{"templateCode": "com.alibaba.business.trade.general.GeneralTradeAbility", "templateType": "PRODUCT"}]}, "subNodes": [{"type": "BusinessSDK", "code": "GENERAL#ORDER#ORDER_RECEIVE_INFO_ENRICH", "extInfo": {"runner": [{"templateCode": "ali.global.ae.russain.selfpick", "templateType": "PRODUCT"}]}, "subNodes": []}]}, {"type": "SubInvoke", "code": "com.alibaba.global.address.api.facade.UserAddressReadFacade@getSelectedUserAddress(SelectedAddressRequest)", "extInfo": {}, "subNodes": []}, {"type": "DomainSDK", "code": "GlobalFulfillmentSpiCodes.EXT_FULFILLMENT_CHECK_RECEIVE_INFO_CODE", "extInfo": {"runner": [{"templateCode": "com.alibaba.business.trade.general.GeneralTradeAbility", "templateType": "PRODUCT"}]}, "subNodes": [{"type": "BusinessSDK", "code": "GENERAL#BUYER_PLACE_ORDER_CHECK_RECEIVE_INFO", "extInfo": {"runner": [{"templateCode": "ali.global.ae.address.restricted", "templateType": "PRODUCT"}]}, "subNodes": []}]}, {"type": "DomainSDK", "code": "EXT_IAFTERSALE_ORDERLINE_SKIP_PRODUCT_SERVICE_FEE ", "extInfo": {"runner": [{"templateCode": "com.alibaba.business.trade.general.GeneralTradeAbility", "templateType": "PRODUCT"}]}, "subNodes": [{"type": "BusinessSDK", "code": "GENERAL#COMMON#ORDER_ORDER_LINE_GET_CONTRACT_CONFIG", "extInfo": {"runner": [{"templateCode": "ali.global.ae.trade.general", "templateType": "BUSINESS"}]}, "subNodes": []}]}, {"type": "SubInvoke", "code": "com.alibaba.intl.ae.biz.promisetemplate.share.service.interfaces.PromiseTemplateService@calculatePromiseResultsByProductList(List)", "extInfo": {}, "subNodes": []}, {"type": "DomainSDK", "code": "EXT_IAFTERSALE_ORDERLINE_SKIP_RETURN_POLICY", "extInfo": {"runner": [{"templateCode": "com.alibaba.business.trade.general.GeneralTradeAbility", "templateType": "PRODUCT"}]}, "subNodes": [{"type": "BusinessSDK", "code": "GENERAL#COMMON#ORDER_ORDER_LINE_GET_CONTRACT_CONFIG", "extInfo": {"runner": [{"templateCode": "ali.global.ae.trade.general", "templateType": "BUSINESS"}]}, "subNodes": []}]}, {"type": "DomainSDK", "code": "EXT_IAFTERSALE_ORDERLINE_SKIP_RETURN_POLICY", "extInfo": {"runner": [{"templateCode": "com.alibaba.business.trade.general.GeneralTradeAbility", "templateType": "PRODUCT"}]}, "subNodes": [{"type": "BusinessSDK", "code": "GENERAL#COMMON#ORDER_ORDER_LINE_GET_CONTRACT_CONFIG", "extInfo": {"runner": [{"templateCode": "ali.global.ae.trade.general", "templateType": "BUSINESS"}]}, "subNodes": []}]}, {"type": "SubInvoke", "code": "com.alibaba.global.payment.api.facade.CashierFacade@calculatePayCurrency(CalculatePayCurrencyRequest)", "extInfo": {}, "subNodes": []}, {"type": "SubInvoke", "code": "com.taobao.payment.exchangeplatform.client.api.ExchangeQueryService@queryExchangeRateFromCache(List,String,String)", "extInfo": {}, "subNodes": []}, {"type": "DomainSDK", "code": "GlobalOrderSpiCodes.EXT_ORDER_ORDER_LINE_ENRICH_BUYER", "extInfo": {"runner": [{"templateCode": "com.alibaba.business.trade.general.GeneralTradeAbility", "templateType": "PRODUCT"}]}, "subNodes": [{"type": "BusinessSDK", "code": "GENERAL#ORDER#ORDER_BASE_INFO_ENRICH", "extInfo": {"runner": [{"templateCode": "ali.global.ae.trade.general", "templateType": "BUSINESS"}]}, "subNodes": [{"type": "SubInvoke", "code": "com.alibaba.aliexpress.price.api.common.client.product.ProductPriceQueryClient@queryBatchProductPrice(BatchProductPriceQueryParamDTO)", "extInfo": {}, "subNodes": []}]}]}, {"type": "DomainSDK", "code": "GlobalOrderSpiCodes.EXT_ORDER_ORDER_LINE_ENRICH_SELLER", "extInfo": {"runner": [{"templateCode": "com.alibaba.business.trade.general.GeneralTradeAbility", "templateType": "PRODUCT"}]}, "subNodes": [{"type": "BusinessSDK", "code": "GENERAL#ORDER#ORDER_BASE_INFO_ENRICH", "extInfo": {"runner": [{"templateCode": "ali.global.ae.trade.general", "templateType": "BUSINESS"}]}, "subNodes": [{"type": "SubInvoke", "code": "com.alibaba.aliexpress.price.api.common.client.product.ProductPriceQueryClient@queryBatchProductPrice(BatchProductPriceQueryParamDTO)", "extInfo": {}, "subNodes": []}]}]}, {"type": "DomainSDK", "code": "GlobalOrderSpiCodes.EXT_ORDER_ORDER_LINE_ENRICH_PRODUCT", "extInfo": {"runner": [{"templateCode": "com.alibaba.business.trade.general.GeneralTradeAbility", "templateType": "PRODUCT"}]}, "subNodes": [{"type": "BusinessSDK", "code": "GENERAL#ORDER#ORDER_BASE_INFO_ENRICH", "extInfo": {"runner": [{"templateCode": "ali.global.ae.trade.general", "templateType": "BUSINESS"}]}, "subNodes": [{"type": "SubInvoke", "code": "com.alibaba.aliexpress.price.api.common.client.product.ProductPriceQueryClient@queryBatchProductPrice(BatchProductPriceQueryParamDTO)", "extInfo": {}, "subNodes": []}]}]}, {"type": "DomainSDK", "code": "GlobalOrderSpiCodes.EXT_ORDER_ORDER_LINE_ENRICH_SHOP", "extInfo": {"runner": [{"templateCode": "com.alibaba.business.trade.general.GeneralTradeAbility", "templateType": "PRODUCT"}]}, "subNodes": [{"type": "BusinessSDK", "code": "GENERAL#ORDER#ORDER_BASE_INFO_ENRICH", "extInfo": {"runner": [{"templateCode": "ali.global.ae.trade.general", "templateType": "BUSINESS"}]}, "subNodes": []}]}, {"type": "DomainSDK", "code": "EXT_TRADE_ORDER_GROUPING_GET_GROUPING_INFO_KEY_CODE", "extInfo": {"runner": [{"templateCode": "com.alibaba.business.trade.general.GeneralTradeAbility", "templateType": "PRODUCT"}]}, "subNodes": [{"type": "BusinessSDK", "code": "GENERAL#BUYER_PLACE_ORDER#GET_GROUPING_INFO", "extInfo": {"runner": [{"templateCode": "ali.global.ae.trade.general", "templateType": "BUSINESS"}]}, "subNodes": []}]}, {"type": "SubInvoke", "code": "com.alibaba.global.expression.protocol.sdk.facade.FulfillmentServiceFacade@splitTradeOrder(SplitTradeOrderRequest)", "extInfo": {}, "subNodes": []}, {"type": "DomainSDK", "code": "EXT_TRADE_ORDER_GROUPING_GET_SPLIT_GROUPING_TYPE_CODE", "extInfo": {"runner": [{"templateCode": "com.alibaba.business.trade.general.GeneralTradeAbility", "templateType": "PRODUCT"}]}, "subNodes": [{"type": "BusinessSDK", "code": "GENERAL#BUYER_PLACE_ORDER#SPLIT_GRUPING_TYPE", "extInfo": {"runner": [{"templateCode": "ali.global.ae.trade.general", "templateType": "BUSINESS"}]}, "subNodes": []}]}, {"type": "SubInvoke", "code": "com.alibaba.global.ump.protocol.sdk.facade.GlobalTradePromotionMultiCalculateFacade@calculateItemPromotion4Checkout(GlobalTradeMultiCalculateRequest)", "extInfo": {}, "subNodes": []}, {"type": "DomainSDK", "code": "GlobalOrderSpiCodes.EXT_ORDER_ORDER_LINE_ENRICH_BUYER", "extInfo": {"runner": [{"templateCode": "com.alibaba.business.trade.general.GeneralTradeAbility", "templateType": "PRODUCT"}]}, "subNodes": [{"type": "BusinessSDK", "code": "GENERAL#ORDER#ORDER_BASE_INFO_ENRICH", "extInfo": {"runner": [{"templateCode": "ali.global.ae.trade.general", "templateType": "BUSINESS"}]}, "subNodes": []}]}, {"type": "DomainSDK", "code": "GlobalOrderSpiCodes.EXT_ORDER_ORDER_LINE_ENRICH_SELLER", "extInfo": {"runner": [{"templateCode": "com.alibaba.business.trade.general.GeneralTradeAbility", "templateType": "PRODUCT"}]}, "subNodes": [{"type": "BusinessSDK", "code": "GENERAL#ORDER#ORDER_BASE_INFO_ENRICH", "extInfo": {"runner": [{"templateCode": "ali.global.ae.trade.general", "templateType": "BUSINESS"}]}, "subNodes": []}]}, {"type": "DomainSDK", "code": "GlobalOrderSpiCodes.EXT_ORDER_ORDER_LINE_ENRICH_PRODUCT", "extInfo": {"runner": [{"templateCode": "com.alibaba.business.trade.general.GeneralTradeAbility", "templateType": "PRODUCT"}]}, "subNodes": [{"type": "BusinessSDK", "code": "GENERAL#ORDER#ORDER_BASE_INFO_ENRICH", "extInfo": {"runner": [{"templateCode": "ali.global.ae.trade.general", "templateType": "BUSINESS"}]}, "subNodes": []}]}, {"type": "DomainSDK", "code": "GlobalOrderSpiCodes.EXT_ORDER_ORDER_LINE_ENRICH_SHOP", "extInfo": {"runner": [{"templateCode": "com.alibaba.business.trade.general.GeneralTradeAbility", "templateType": "PRODUCT"}]}, "subNodes": [{"type": "BusinessSDK", "code": "GENERAL#ORDER#ORDER_BASE_INFO_ENRICH", "extInfo": {"runner": [{"templateCode": "ali.global.ae.trade.general", "templateType": "BUSINESS"}]}, "subNodes": []}]}, {"type": "DomainSDK", "code": "EXT_PRICE_ORDER_LINE_PRICE_GET_ADDITIONAL_FEE", "extInfo": {}, "subNodes": []}, {"type": "DomainSDK", "code": "EXT_PRICE_ORDER_LINE_PRICE_CUSTOM_ORDER_LINE_UNIT_FEE_CODE", "extInfo": {}, "subNodes": []}, {"type": "DomainSDK", "code": "GlobalFulfillmentSpiCodes.EXT_FULFILLMENT_NEED_TRANSPORT_METHOD_CODE", "extInfo": {"runner": [{"templateCode": "com.alibaba.business.trade.general.GeneralTradeAbility", "templateType": "PRODUCT"}]}, "subNodes": [{"type": "BusinessSDK", "code": "GENERAL#COMMON#ORDER_ORDER_LINE_GET_CONTRACT_CONFIG", "extInfo": {"runner": [{"templateCode": "ali.global.ae.trade.general", "templateType": "BUSINESS"}]}, "subNodes": []}]}, {"type": "DomainSDK", "code": "EXT_FULFILLMENT_CUSTOM_QUERY_ENRICH_EXTRA_PARAMS_CODE", "extInfo": {"runner": [{"templateCode": "com.alibaba.business.trade.general.GeneralTradeAbility", "templateType": "PRODUCT"}]}, "subNodes": [{"type": "BusinessSDK", "code": "GENERAL#BUYER_PLACE_ORDER#CONFIRM_OTHER_PARAMS", "extInfo": {"runner": [{"templateCode": "ali.global.ae.trade.general", "templateType": "BUSINESS"}]}, "subNodes": []}]}, {"type": "DomainSDK", "code": "EXT_FULFILLMENT_CUSTOM_QUERY_ENRICH_PRICE_PARAMS_CODE", "extInfo": {"runner": [{"templateCode": "com.alibaba.business.trade.general.GeneralTradeAbility", "templateType": "PRODUCT"}]}, "subNodes": [{"type": "BusinessSDK", "code": "GENERAL#BUYER_PLACE_ORDER#QUERY_PRICE_PARAMS", "extInfo": {"runner": [{"templateCode": "ali.global.ae.trade.general", "templateType": "BUSINESS"}]}, "subNodes": []}]}, {"type": "SubInvoke", "code": "com.alibaba.global.expression.protocol.sdk.facade.FulfillmentServiceFacade@queryFulfillmentGroupService(QueryFulfillmentGroupServiceRequest)", "extInfo": {}, "subNodes": []}, {"type": "SubInvoke", "code": "com.alibaba.trade.shared.utils.SequenceGenerator@next(String)", "extInfo": {}, "subNodes": []}, {"type": "SubInvoke", "code": "com.alibaba.trade.shared.utils.SequenceGenerator@next(String)", "extInfo": {}, "subNodes": []}, {"type": "SubInvoke", "code": "com.alibaba.trade.shared.utils.SequenceGenerator@next(String)", "extInfo": {}, "subNodes": []}, {"type": "DomainSDK", "code": "GlobalOrderSpiCodes.EXT_FULFILLMENT_BUILD_ADDRESS_QUERY_EXTRA_PARAMS_CODE", "extInfo": {"runner": [{"templateCode": "com.alibaba.business.trade.general.GeneralTradeAbility", "templateType": "PRODUCT"}]}, "subNodes": [{"type": "BusinessSDK", "code": "GENERAL#BUYER_PLACE_ORDER_BUILD_ADDRESS_QUERY_EXTRA_PARAMS", "extInfo": {"runner": [{"templateCode": "ali.global.ae.russain.selfpick", "templateType": "PRODUCT"}]}, "subNodes": []}]}, {"type": "SubInvoke", "code": "com.alibaba.global.address.api.facade.CollectionPointReadFacade@getRecommendCollectionPointForMultiOrder(CollectionPointMultiOrderQueryRequest)", "extInfo": {}, "subNodes": []}, {"type": "DomainSDK", "code": "GlobalFulfillmentSpiCodes.EXT_FULFILLMENT_NEED_SHIPPING_ADDRESS_CODE", "extInfo": {"runner": [{"templateCode": "com.alibaba.business.trade.general.GeneralTradeAbility", "templateType": "PRODUCT"}]}, "subNodes": [{"type": "BusinessSDK", "code": "GENERAL#COMMON#ORDER_ORDER_LINE_GET_CONTRACT_CONFIG", "extInfo": {"runner": [{"templateCode": "ali.global.ae.trade.general", "templateType": "BUSINESS"}]}, "subNodes": []}]}, {"type": "DomainSDK", "code": "GlobalFulfillmentSpiCodes.EXT_FULFILLMENT_ENRICH_SHIPPING_ADDRESS_CODE", "extInfo": {"runner": [{"templateCode": "com.alibaba.business.trade.general.GeneralTradeAbility", "templateType": "PRODUCT"}]}, "subNodes": [{"type": "BusinessSDK", "code": "GENERAL#ORDER#ORDER_RECEIVE_INFO_ENRICH", "extInfo": {"runner": [{"templateCode": "ali.global.ae.russain.selfpick", "templateType": "PRODUCT"}]}, "subNodes": []}]}, {"type": "SubInvoke", "code": "com.alibaba.global.ump.protocol.sdk.facade.GlobalTradePromotionMultiCalculateFacade@calculatePromotion4Checkout(GlobalTradeMultiCalculateRequest)", "extInfo": {}, "subNodes": []}, {"type": "DomainSDK", "code": "EXT_PRICE_ORDER_LINE_PRICE_GET_ADDITIONAL_FEE", "extInfo": {}, "subNodes": []}, {"type": "DomainSDK", "code": "EXT_PRICE_ORDER_LINE_PRICE_CUSTOM_ORDER_LINE_UNIT_FEE_CODE", "extInfo": {}, "subNodes": []}, {"type": "DomainSDK", "code": "EXT_PRICE_ORDER_LINE_PRICE_GET_ADDITIONAL_FEE", "extInfo": {}, "subNodes": []}, {"type": "DomainSDK", "code": "EXT_PRICE_ORDER_LINE_PRICE_CUSTOM_ORDER_LINE_UNIT_FEE_CODE", "extInfo": {}, "subNodes": []}, {"type": "DomainSDK", "code": "EXT_INVENTORY_ORDERLINE_SKIP_QUERY_CODE", "extInfo": {"runner": [{"templateCode": "com.alibaba.business.trade.general.GeneralTradeAbility", "templateType": "PRODUCT"}]}, "subNodes": [{"type": "BusinessSDK", "code": "GENERAL#COMMON#ORDER_ORDER_LINE_GET_CONTRACT_CONFIG", "extInfo": {"runner": [{"templateCode": "ali.global.ae.trade.general", "templateType": "BUSINESS"}]}, "subNodes": []}]}, {"type": "DomainSDK", "code": "EXT_INVENTORY_ORDERLINE_ENRICH_QUERY_PARAM_CODE", "extInfo": {}, "subNodes": []}, {"type": "SubInvoke", "code": "com.alibaba.global.inventory.protocol.sdk.facade.GlobalInventoryTradeFacade@sourcingInventory(SourcingInventoryRequest)", "extInfo": {}, "subNodes": []}, {"type": "DomainSDK", "code": "EXT_PRICE_ORDER_LINE_PRICE_GET_ADDITIONAL_FEE", "extInfo": {}, "subNodes": []}, {"type": "DomainSDK", "code": "EXT_PRICE_ORDER_LINE_PRICE_CUSTOM_ORDER_LINE_UNIT_FEE_CODE", "extInfo": {}, "subNodes": []}, {"type": "DomainSDK", "code": "EXT_PRICE_ORDER_LINE_PRICE_GET_ADDITIONAL_FEE", "extInfo": {}, "subNodes": []}, {"type": "DomainSDK", "code": "EXT_PRICE_ORDER_LINE_PRICE_CUSTOM_ORDER_LINE_UNIT_FEE_CODE", "extInfo": {}, "subNodes": []}, {"type": "DomainSDK", "code": "EXT_PAYMENT_CASHIER_QUERY_IS_RENDER_PRE_CASHIER", "extInfo": {}, "subNodes": []}, {"type": "DomainSDK", "code": "EXT_PAYMENT_CASHIER_QUERY_BUILD_EXTRA_PARAMS_CODE", "extInfo": {"runner": [{"templateCode": "com.alibaba.business.trade.general.GeneralTradeAbility", "templateType": "PRODUCT"}]}, "subNodes": [{"type": "BusinessSDK", "code": "GENERAL#BUYER_PLACE_ORDER_ENRICH_RENDER_CASHIER_PARAMS", "extInfo": {"runner": [{"templateCode": "ali.global.ae.trade.general", "templateType": "BUSINESS"}]}, "subNodes": []}]}, {"type": "SubInvoke", "code": "com.alibaba.global.payment.api.facade.GlobalPaymentCashierServiceFacade@renderIntention(RenderIntentionRequest)", "extInfo": {}, "subNodes": []}, {"type": "SubInvoke", "code": "com.alibaba.global.payment.api.facade.GlobalPaymentCashierServiceFacade@querySupportPaymentChannels(QuerySupportPaymentChannelRequest)", "extInfo": {}, "subNodes": []}, {"type": "DomainSDK", "code": "OrderSpiCodes.EXT_FINAL_VALIDATE_ORDER_BEFORE_RENDERING_CODE", "extInfo": {"runner": [{"templateCode": "com.alibaba.business.trade.general.GeneralTradeAbility", "templateType": "PRODUCT"}]}, "subNodes": [{"type": "BusinessSDK", "code": "GENERAL#ORDER#ORDER_VALIDATE_EVENT", "extInfo": {"runner": [{"templateCode": "ali.global.ae.trade.general", "templateType": "BUSINESS"}]}, "subNodes": []}]}, {"type": "DomainSDK", "code": "OrderSpiCodes.EXT_FINAL_VALIDATE_ORDER_LINE_BEFORE_RENDERING_CODE", "extInfo": {"runner": [{"templateCode": "com.alibaba.business.trade.general.GeneralTradeAbility", "templateType": "PRODUCT"}]}, "subNodes": [{"type": "BusinessSDK", "code": "GENERAL#ORDER#ORDER_VALIDATE_EVENT", "extInfo": {"runner": [{"templateCode": "ali.global.ae.trade.general", "templateType": "BUSINESS"}]}, "subNodes": []}]}, {"type": "DomainSDK", "code": "GlobalOrderSpiCodes.EXT_ORDER_ENRICH_RENDER_ORDER_RESULT_CODE", "extInfo": {"runner": [{"templateCode": "com.alibaba.business.trade.general.GeneralTradeAbility", "templateType": "PRODUCT"}]}, "subNodes": [{"type": "BusinessSDK", "code": "GENERAL#BUYER_PLACE_ORDER_ENRICH_RENDER_ORDER_RESULT", "extInfo": {"runner": [{"templateCode": "ali.global.ae.trade.general", "templateType": "BUSINESS"}]}, "subNodes": []}]}]}, "switch_key": {"com.alibaba.global.trade.resources.systemswitch.BusinessSwitch.QUERY_SELLER_OPTIMIZED_API": true, "com.alibaba.global.trade.resources.systemswitch.BusinessSwitch.BATCH_QUERY_SELLER_SIZE": 20, "com.alibaba.global.trade.resources.systemswitch.BusinessSwitch.SELLER_TAG_CODES": ["plaza_business_industry", "oversea_seller_location", "seller_quotation_currency", "seller_insurance_tag", "seller_fulfillment_type"], "com.alibaba.global.trade.resources.systemswitch.BusinessSwitch.NORMAL_BUY_LIMIT": 10000, "com.alibaba.global.trade.resources.systemswitch.BusinessSwitch.ENABLE_SELF_PICKUP": true, "com.alibaba.global.trade.resources.systemswitch.BusinessSwitch.SELLER_BOOL_TAG_CODES": ["RUSSIA_BOUTIQUE_SELLER", "SPANISH_PLAZA", "test", "48475", "RUSSIAN_OWN_RETAILER"], "com.alibaba.global.trade.resources.systemswitch.BusinessSwitch.IS_USING_GLOBAL_BUY": true, "com.alibaba.ae.trade.common.enrich.config.AeBusinessSwitch.LOG_PRICE_RESULT": true, "com.alibaba.global.trade.resources.systemswitch.BusinessSwitch.SELF_PICKUP_AND_DELIVERY": true, "com.alibaba.global.trade.resources.systemswitch.BusinessSwitch.DUP_CALCULATE_PROMOTION_AE": true}}}, "replayBytes": null, "taskInfo": {"features": {"msg_channel": "simulator-topic-1", "pojId": "123"}, "taskType": "real_time_collect", "mainInvokeIdentitiesRateMap": {"com.alibaba.global.order.management.api.facade.BizFeaturesQueryFacade:1.0.0@batchQueryTradeOrderLinesBizFeatures(QueryTradeOrderLinesBizFeaturesRequest)": 1000, "com.alibaba.global.order.management.facade.OrderQueryFacadeImpl@queryTradeOrderById(Long)": 1000, "com.alibaba.ae.trade.message.executor.payment.AeModifyPriceMsgExecutor@buildInputMsgRequest(String,Object)": 1000, "com.alibaba.global.buy.api.facade.wireless.WirelessCheckoutFacadeImpl@oneKeyCreateOrder(CreateQueryParams)": 1000, "com.alibaba.global.trade.open.define.facade.OrderQueryFacade:1.0.0@query(OrderQueryRequest,ClientInfo)": 1000, "com.taobao.order.query.seller.facade.impl.SellerOrderPrintWebFacadeFacadeImpl@printWayBill(SellerOrderListRequest)": 1000, "com.alibaba.taobao.trade.message.executor.timeout.TaobaoConfirmGoodsTimeoutReminderCallback@onTimeout(AlarmClockEvent)": 1000, "com.alibaba.daraz.trade.adaptor.message.executor.timeout.DarazPayTimeoutReminderCallback@onTimeout(AlarmClockEvent)": 1000, "com.alibaba.global.merchant.order.facade.OrderExportInitFacade:1.0.0@initExportPage(OrderExportInitRequest)": 1000, "com.alibaba.global.carts.api.facade.CartFacade:1.0.0@add(CartAddRequest)": 1000, "com.alibaba.global.merchant.common.batch.api.service.BackGroundTaskService:1.0.0@customizeExport(String,String,JSONObject)": 1000, "com.alibaba.lazada.trade.adaptor.message.executor.payment.LazadaPayFailedMsgExecutor@execute(InputTradeMsgRequest)": 1000, "com.alibaba.global.merchant.order.facade.OrderOperatingFacade:1.0.0@changeOrderAmount(ChangeOrderAmountRequest)": 1000, "com.alibaba.global.order.management.api.facade.OrderCallbackRegionFacade:1.0.0@asyncInventoryReduce(Long)": 1000, "com.alibaba.ae.trade.message.executor.payment.AePaymentBufferExecutor@filter(InputTradeMsgRequest)": 1000, "com.alibaba.global.order.management.api.facade.OrderManagementRegionFacade:1.0.0@deliveryGoods(DeliveryGoodsRequest)": 1000, "com.alibaba.global.order.management.api.facade.OrderGroupManagementFacade:1.0.0@queryOrderLinesByGroup(QueryTradeOrderGroupRequest)": 1000, "com.alibaba.global.order.management.api.facade.OrderViewFacade:1.0.0@renderBuyerOrderList(QueryBuyerOrderListRequest)": 1000, "com.alibaba.global.order.management.api.facade.OrderViewFacade:1.0.0@adjustDeliveryInfo(AdjustDeliveryInfoRequest)": 1000, "com.alibaba.global.order.management.api.facade.OrderManagementRegionFacade:1.0.0@extendTimeout(ExtendTimeoutRequest)": 1000, "com.alibaba.global.wishlist.api.facade.WishlistFacade:1.0.0@addItemsToWishlist(List)": 1000, "com.alibaba.lazada.trade.adaptor.message.executor.payment.LazadaPayIntentionMsgExecutor@execute(InputTradeMsgRequest)": 1000, "com.alibaba.global.order.management.api.facade.OrderManagementRegionFacade:1.0.0@unfreezeTradeOrder(UnfreezeTradeOrderRequest)": 1000, "com.alibaba.global.wishlist.api.facade.WishlistFacade:1.0.0@getPassPurchaseItems(WishlistQueryRequest)": 1000, "com.alibaba.global.order.management.api.facade.OrderManagementRegionFacade:1.0.0@unFreezeTimeout(UnFreezeTimeoutRequest)": 1000, "com.alibaba.global.carts.api.facade.CartDBFacade:1.0.0@queryCartItems(Long)": 1000, "com.alibaba.global.order.management.facade.OrderQueryForSellerFacadeImpl@querySearchValue(QueryESSearchValueRequest)": 1000, "com.alibaba.global.order.management.api.facade.OrderManagementRegionFacade:1.0.0@updateBatchReverseInfo(UpdateBatchReverseInfoRequest)": 1000, "com.alibaba.ae.trade.spi.save.AeCheckoutPageEnrich@generateErrorInfo(CheckoutErrorInfoInput)": 1000, "com.alibaba.ae.trade.message.executor.reverse.AeCreatedReturnExecutor@filter(InputTradeMsgRequest)": 1000, "com.alibaba.ae.trade.message.executor.notification.AeExtendShipTimeoutNotificationExecutor@execute(InputTradeMsgRequest)": 1000, "com.lazada.wishlist.api.facade.WishlistFacade:1.0.0@*(*)": 1000, "com.alibaba.global.wishlist.api.facade.WishlistFacade:1.0.0@findByItemId(String,Long)": 1000, "com.alibaba.global.buy.api.facade.CheckoutMultiterminalFacade:1.0.0@createOrder(CreateOrderRequest)": 1000, "com.alibaba.ae.trade.mock.mockFulfillmentOrderCreateFacade.MockCreateFulfillmentOrder@response(Object[],Map)": 1000, "com.lazada.buy.api.facade.wireless.WirelessCheckoutFacade:1.0.0@*(*)": 1000, "com.alibaba.global.order.management.api.facade.OrderViewFacade:1.0.0@recentOrders(Long)": 1000, "com.alibaba.global.buy.api.facade.CheckoutFacade:1.0.0@createOrder(CreateOrderRequest)": 1000, "com.alibaba.global.order.management.api.facade.OrderQueryForBuyerFacade:1.0.0@queryTradeOrderBySellerId(QueryOrderBySellerBuyerIdRequest)": 1000, "com.alibaba.ae.trade.message.executor.notification.AeModifyPriceNotificationExecutor@filter(InputTradeMsgRequest)": 1000, "com.alibaba.ae.trade.message.executor.payment.AePaymentBufferExecutor@execute(InputTradeMsgRequest)": 1000, "com.alibaba.global.wishlist.api.facade.WishlistFacade:1.0.0@addItemToWishlist2(WishlistItemDTO)": 1000, "com.alibaba.global.order.management.api.facade.OrderMatchFacade:1.0.0@convertTradeOrderLineIds(List<Long>)": 1000, "com.alibaba.global.order.management.api.facade.OrderCallbackRegionFacade:1.0.0@canPayCheck(CanPayCheckRequest)": 1000, "com.alibaba.global.buy.api.facade.wireless.WirelessCheckoutFacade:1.0.0@createOrder(CreateQueryParams)": 1000, "com.alibaba.global.buy.api.facade.wireless.WirelessCheckoutFacade:1.0.0@oneKeyCreateOrder(CreateQueryParams)": 1000, "com.alibaba.taobao.trade.message.executor.fulfillment.TaobaoCreatePaymentOrderWhenShippedMsgExecutor@execute(TaobaoInputTradeMsgRequest)": 1000, "com.alibaba.daraz.trade.adaptor.message.executor.timeout.DarazPayTimeoutCallback@onTimeout(AlarmClockEvent)": 1000, "com.alibaba.global.buy.api.facade.wireless.WirelessCheckoutFacadeImpl@createOrder(CreateQueryParams)": 1000, "com.alibaba.lazada.trade.adaptor.message.executor.oldpayment.LazadaOldPayFailedMsgExecutor@execute(InputTradeMsgRequest)": 1000, "com.alibaba.ae.trade.message.executor.payment.AeModifyPriceMsgExecutor@execute(InputTradeMsgRequest)": 1000, "com.alibaba.global.carts.api.facade.CartDBFacade:1.0.0@querySkuNum(Long)": 1000, "com.alibaba.global.order.management.api.facade.OrderManagementRegionFacade:1.0.0@createPaymentOrder(CreatePaymentOrderRequest)": 1000, "com.alibaba.ae.trade.message.executor.timeout.AeConfirmGoodsTimeoutCallback@onTimeout(AlarmClockEvent)": 1000, "com.alibaba.global.trade.open.define.facade.OrderCancelFacade:1.0.0@*(*)": 1000, "com.alibaba.ae.trade.message.executor.timeout.AePayTimeoutReminderCallback@onTimeout(AlarmClockEvent)": 1000, "com.alibaba.global.trade.open.define.facade.CheckoutFacade:1.0.0@preCreateOrderForMtop(PreCreateOrderRequestForMtop)": 1000, "com.alibaba.ae.trade.message.executor.uop.AeHBAMsgExecutor@execute(InputTradeMsgRequest)": 1000, "com.lazada.carts.api.facade.CartMtopFacade:1.0.0@*(*)": 1000, "com.alibaba.global.merchant.order.facade.OrderExportQueryFacade:1.0.0@getShippingListExportPage(OrderExportQueryRequest)": 1000, "com.alibaba.global.wishlist.api.facade.WishlistFacade:1.0.0@findWishlistItems(Long,QueryExtendParamDTO)": 1000, "com.alibaba.global.order.management.api.facade.OrderGroupManagementFacade:1.0.0@queryTradeOrderGroup(QueryTradeOrderGroupRequest)": 1000, "com.alibaba.daraz.trade.adaptor.message.executor.payment.DarazPayIntentionMsgExecutor@execute(InputTradeMsgRequest)": 1000, "com.alibaba.lazada.trade.adaptor.message.executor.adaptor.presale.DepositTimeOutNotificationOutPutMsgExecutor@execute(*)": 1000, "com.alibaba.global.carts.api.facade.CartMtopFacade:1.0.0@count(CartCountRequest)": 1000, "com.alibaba.global.buy.api.facade.wireless.WirelessCheckoutFacadeImpl@queryAmendPackage(QueryAmendPackageParams)": 1000, "com.lazada.buy.api.facade.CheckoutFacade:1.0.0@*(*)": 1000, "com.alibaba.ae.trade.message.executor.uop.AeDeliveredMsgExecutor@buildInputMsgRequest(String,FulfillmentOrderStatusUpdatedDTO)": 1000, "com.alibaba.global.merchant.order.facade.OrderQueryWebFacade:1.0.0@*(*)": 1000, "com.alibaba.global.order.query.seller.servcie.OrderQueryScenarioService:1.0.0@queryOrderStatistics(OrderCountQueryRequest)": 1000, "com.alibaba.ae.trade.message.handler.AeGroupBuyShareMsgHandler@receiveMessage(Message)": 1000, "com.alibaba.daraz.trade.adaptor.message.executor.ov.OrderRiskCheckMsgExecutor@execute(InputTradeMsgRequest)": 1000, "com.alibaba.ae.trade.message.executor.adapt.AeCODOrderDeliveredAdaptor@filter(InputTradeMsgRequest)": 1000, "com.alibaba.global.buy.api.facade.pressure.PressureFacade:1.0.0@shipping(RenderOrderRequest)": 1000, "com.alibaba.ae.trade.message.executor.notification.AeShippedNotificationExecutor@isTopUpOrder(TradeOrderLineDTO)": 1000, "com.alibaba.lazada.trade.adaptor.message.executor.payment.presale.LazadaDepositPayAcceptMsgExecutorImpl@execute(InputTradeMsgRequest)": 1000, "com.alibaba.global.wishlist.api.facade.WishlistFacade:1.0.0@addItem(WishlistItemDTO)": 1000, "com.alibaba.daraz.trade.adaptor.message.executor.payment.DarazPayFailedMsgExecutor@execute(InputTradeMsgRequest)": 1000, "com.alibaba.ae.trade.message.executor.reverse.AeCreatedReturnExecutor@execute(InputTradeMsgRequest)": 1000, "com.alibaba.ae.trade.spi.save.AeCheckoutPageEnrich@generateBusinessCheckoutPage(ICheckoutPage)": 1000, "com.alibaba.ae.trade.message.handler.AeReverseEndMsgHandler@receiveMessage(Message)": 1000, "com.alibaba.global.carts.api.facade.CartFacade:1.0.0@count(CartCountRequest)": 1000, "com.alibaba.lazada.trade.adaptor.message.executor.timeout.LazadaPayTimeoutReminderCallback@onTimeout(AlarmClockEvent)": 1000, "com.alibaba.global.carts.api.facade.CartDBFacade:1.0.0@updateCartItemFeature(Long,Long,String,String)": 1000, "com.alibaba.global.timeout.message.AlarmMessageListener@consume(List,ConsumeConcurrentlyContext)": 1000, "com.alibaba.ae.trade.message.executor.payment.AePaymentBufferExecutor@buildInputMsgRequest(String,PaymentCacheEvent)": 1000, "com.alibaba.global.wishlist.api.facade.WishlistFacade:1.0.0@addItem(WishlistAddTagForItemRequest)": 1000, "com.alibaba.global.order.management.api.facade.TestFacade:1.0.0@cache(String,String,Integer)": 1000, "com.alibaba.global.order.management.api.facade.OrderManagementRegionFacade:1.0.0@asyncEditPayPrice(EditPayPriceRequest)": 1000, "com.alibaba.global.merchant.common.batch.api.service.BackGroundTaskService:1.0.0@ximport(String,String,JSONObject)": 1000, "com.alibaba.global.order.management.api.facade.OrderCallbackFacade:1.0.0@*(*)": 1000, "com.alibaba.lazada.trade.adaptor.message.executor.groupbuy.LazadaGroupBuyAfterReduceMsgExecutor@execute(InputTradeMsgRequest)": 1000, "com.alibaba.global.order.management.facade.OrderQueryForSellerFacadeImpl@queryBatchTradeOrders(QueryForSellerRequest)": 1000, "com.alibaba.daraz.trade.adaptor.message.executor.uop.DarazUopDeliveredMsgExecutor@execute(InputTradeMsgRequest)": 1000, "com.alibaba.global.wishlist.api.facade.WishlistMtopFacade:1.0.0@getUserWishlistItems(WishlistBaseRequest)": 1000, "com.alibaba.ae.trade.spi.save.AeCheckoutPageEnrich@enrichCheckoutPage(CheckoutPageEnrichInput)": 1000, "com.alibaba.global.order.management.api.facade.FeaturesOperateFacade:1.0.0@updateWholeOrderFeatures(UpdateWholeOrderFeaturesRequest)": 1000, "com.alibaba.ae.trade.message.executor.uop.AeDeliveredMsgExecutor@filter(InputTradeMsgRequest)": 1000, "com.alibaba.lazada.trade.adaptor.message.handler.settlement.SettlementMsgHandler@receiveMessageFromSettleCallback(*)": 1000, "com.alibaba.lazada.trade.adaptor.message.executor.trade.LazadaDigitalShippedMsgExecutor@execute(InputTradeMsgRequest)": 1000, "com.alibaba.ae.trade.message.executor.payment.AePaySuccessMsgExecutor@buildInputMsgRequest(String,CheckoutEvent)": 1000, "com.alibaba.global.order.management.api.facade.OrderQueryFacade:1.0.0@queryTradeOrderById(Long)": 1000, "com.alibaba.global.buy.api.facade.pressure.wireless.WirelessPressureFacade:1.0.0@placeOrder(AdjustQueryParams)": 1000, "com.alibaba.global.merchant.order.facade.OrderOperatingFacade:1.0.0@extendsReceiveGoodsTime(ExtendsReceiveGoodsRequest)": 1000, "com.alibaba.lazada.trade.adaptor.message.executor.uop.LazadaUopStatusUpdateAssExecutor@execute(InputTradeMsgRequest)": 1000, "com.alibaba.global.order.query.seller.repository.TradeOrderSearchRepo:1.0.0@queryDistinctTradeOrderIdsByOS(OrderListQueryRequest)": 1000, "com.alibaba.global.merchant.toolbox.gateway.service.IMerchantGatewayService:1.0.0@getVersion()": 1000, "com.alibaba.global.merchant.order.facade.OrderOperatingFacade:1.0.0@changeOrderMemo(ChangeOrderMemoRequest)": 1000, "com.alibaba.ae.trade.message.executor.notification.AeExtendShipTimeoutNotificationExecutor@filter(InputTradeMsgRequest)": 1000, "com.alibaba.global.order.management.api.facade.OrderManagementFacade:1.0.0@editPayPrice(EditPayPriceRequest)": 1000, "com.alibaba.taobao.trade.message.executor.timeout.TaobaoPayTimeoutCallback@onTimeout(AlarmClockEvent)": 1000, "com.alibaba.global.wishlist.api.facade.WishlistFacade:1.0.0@removeUsrTagForItem(WishlistRemoveTagForItemRequest)": 1000, "com.alibaba.ae.trade.message.executor.payment.AePayAmountCheckExecutor@execute(InputTradeMsgRequest)": 1000, "com.lazada.order.management.api.facade.OrderQueryForBuyerFacade:1.0.0@*(*)": 1000, "com.alibaba.lazada.trade.adaptor.message.executor.oldpayment.LazadaOldPaySuccessMsgExecutor@execute(InputTradeMsgRequest)": 1000, "com.alibaba.global.order.management.api.facade.OrderViewFacade:1.0.0@getOrderDetailURL(PlatformType,Long)": 1000, "com.alibaba.global.order.management.api.facade.OrderCountFacade:1.0.0@countTradeOrderForNewCustomer(QueryTradeCountForNewCustomerRequest)": 1000, "com.alibaba.ae.trade.mock.inventorySourcingFacade.MockSourcing@response(Object[],Map)": 1000, "com.alibaba.global.order.management.api.facade.OrderCountFacade:1.0.0@countTradeOrderForWireless(QueryTradeCountForWirelessRequest)": 1000, "com.alibaba.global.wishlist.api.facade.WishlistMtopFacade:1.0.0@getPassPurchaseItems(WishlistMtopQueryRequst)": 1000, "com.alibaba.daraz.trade.adaptor.message.executor.trade.DarazPureZeroPaySuccessMsgExecutor@execute(InputTradeMsgRequest)": 1000, "com.alibaba.global.buy.api.facade.pc.PcCheckoutFacade:1.0.0@*(*)": 1000, "com.alibaba.global.order.management.api.facade.OrderRecoveryFacade:1.0.0@recoverOrder(RecoverTradeOrderRequest)": 1000, "com.alibaba.global.order.management.facade.OrderQueryForSellerFacadeImpl@queryTradeOrderLines(QueryESOrderLinesRequest)": 1000, "com.alibaba.global.order.management.api.facade.OrderManagementRegionFacade:1.0.0@checkPayAmount(CheckPayAmountRequest)": 1000, "com.alibaba.lazada.trade.adaptor.message.executor.payment.presale.LazadaFinalPayAcceptMsgExecutorImpl@execute(InputTradeMsgRequest)": 1000, "com.alibaba.global.order.management.api.facade.OrderManagementRegionFacade:1.0.0@securityManualCheck(SecurityManualCheckRequest)": 1000, "com.alibaba.global.order.management.api.facade.TestFacade:1.0.0@removeCache(String)": 1000, "com.alibaba.global.carts.api.facade.CartMtopFacade:1.0.0@alihot(CartAlihotRequest)": 1000, "com.alibaba.global.order.management.api.facade.BizFeaturesQueryFacade:1.0.0@queryBizFeatures(QueryTradeBizFeaturesRequest)": 1000, "com.alibaba.ae.trade.app.topup.utils.TopUpQueryFacade@queryRechargeLimit(Long,Long,Long,String)": 1000, "com.alibaba.global.merchant.order.facade.api.service.SellerOrderExportFacade:1.0.0@*(*)": 1000, "com.alibaba.global.carts.api.facade.CartFacade:1.0.0@*(*)": 1000, "com.alibaba.ae.trade.message.executor.reverse.AeReturnFinishedExecutor@execute(InputTradeMsgRequest)": 1000, "com.alibaba.global.order.management.api.facade.OrderViewFacade:1.0.0@recentChangeOrder(RecentChangeOrderRequest)": 1000, "com.alibaba.ae.trade.message.executor.notification.AeCODOrderCreatedNotificationExecutor@execute(InputTradeMsgRequest)": 1000, "com.alibaba.global.order.management.facade.OrderQueryForSellerFacadeImpl@queryCountTradeOrder(QueryESTradeOrderCountRequest)": 1000, "com.alibaba.global.wishlist.api.facade.WishlistFacade:1.0.0@findBySkuId(String,Long)": 1000, "com.alibaba.ae.trade.message.executor.payment.AeChargeBackFreezeExecutor@execute(InputTradeMsgRequest)": 1000, "com.alibaba.global.order.management.facade.OrderManageFacadeImpl@updateOrderAfterPaid(TradeOrderUpdateRequest)": 1000, "com.alibaba.lazada.trade.adaptor.message.executor.groupbuy.LazadaGroupBuyPushOrderMsgExecutor@execute(InputTradeMsgRequest)": 1000, "com.alibaba.daraz.trade.adaptor.message.executor.ov.DarazOVAsyncCloseOrderMsgExecutor@execute(InputTradeMsgRequest)": 1000, "com.alibaba.ae.trade.message.executor.trade.AeCODCreateFulfillmentOrderExecutor@buildInputMsgRequest(String,Object)": 1000, "com.alibaba.lazada.trade.adaptor.message.executor.trade.LazadaCreateFulfillmentOrderMsgExecutor@execute(InputTradeMsgRequest)": 1000, "com.alibaba.global.order.management.facade.OrderQueryForSellerFacadeImpl@limitSubTradeOrderId(Long)": 1000, "com.alibaba.ae.trade.message.executor.notification.AeExtendConfirmTimeoutNotificationExecutor@filter(InputTradeMsgRequest)": 1000, "com.alibaba.daraz.trade.adaptor.message.executor.oldpayment.DarazOldPayIntentionMsgExecutor@execute(InputTradeMsgRequest)": 1000, "com.alibaba.global.wishlist.api.facade.WishlistFacade:1.0.0@findWishlistItems(Long,QueryOptionDTO)": 1000, "com.alibaba.ae.trade.message.executor.notification.AeCODOrderCreatedNotificationExecutor@filter(InputTradeMsgRequest)": 1000, "com.alibaba.global.order.management.api.facade.OrderQueryForBuyerFacade:1.0.0@queryTradeOrderById(Long)": 1000, "com.alibaba.lazada.trade.adaptor.message.executor.adaptor.promotion.PromotionDiscardOutputMsgExecutor@execute(InputTradeMsgRequest)": 1000, "com.alibaba.tradebrp.client.service.BrpReplayService:1.0.0@doReplay(BrpReplayDO)": 1000, "com.alibaba.lazada.trade.adaptor.message.executor.oldpayment.presale.LazadaDepositOldPaySuccessMsgExecutorImpl@execute(InputTradeMsgRequest)": 1000, "com.alibaba.global.order.management.api.facade.OrderViewFacade:1.0.0@renderTrackingMyOrderForm(RenderTrackingMyOrderRequest)": 1000, "com.alibaba.global.order.management.api.facade.OrderManagementRegionFacade:1.0.0@freezeTradeOrder(FreezeTradeOrderRequest)": 1000, "com.alibaba.global.order.management.api.facade.OrderViewFacade:1.0.0@adjustBuyerOrderList(AdjustTradeOrdersRequest)": 1000, "com.alibaba.global.order.management.api.facade.OrderManagementRegionFacade:1.0.0@createReverse(CreateReverseRequest)": 1000, "com.alibaba.global.order.management.facade.OrderManageFacadeImpl@createTimeout(CreateTimeoutRequest)": 1000, "com.alibaba.lazada.trade.adaptor.message.executor.payment.LazadaPaySuccessMsgExecutor@execute(InputTradeMsgRequest)": 1000, "com.alibaba.global.carts.api.facade.CartFacade:1.0.0@update(CartUpdateRequest)": 1000, "com.alibaba.global.wishlist.api.facade.WishlistFacade:1.0.0@getItemCount(String)": 1000, "com.alibaba.global.wishlist.api.facade.WishlistFacade:1.0.0@addItemToWishlist(WishlistItemDTO)": 1000, " com.alibaba.global.order.management.api.facade.OrderQueryForBuyerFacade:1.0.0@*(*)": 1000, "com.alibaba.global.order.management.api.facade.OrderManagementRegionFacade:1.0.0@editPayPrice(EditPayPriceRequest)": 1000, "com.alibaba.global.order.management.api.facade.OrderViewFacade:1.0.0@validateTrackingMyOrder(String,Long)": 1000, "com.alibaba.global.order.management.api.facade.OrderMatchFacade:1.0.0@convertTradeOrderLineIds(List)": 1000, "com.alibaba.ae.trade.message.executor.notification.AeShippedNotificationExecutor@execute(InputTradeMsgRequest)": 1000, "com.alibaba.lazada.trade.adaptor.message.executor.uop.LazadaCreatePaymentOrderWhenShippedMsgExecutor@execute(InputTradeMsgRequest)": 1000, "com.alibaba.global.merchant.order.facade.api.service.web.SellerOrderPrintWebFacade:1.0.0@*(*)": 1000, "com.alibaba.ae.trade.message.executor.timeout.AeCreateSendGoodsTimeoutMsgExecutor@execute(InputTradeMsgRequest)": 1000, "com.alibaba.global.order.management.api.facade:1.0.0@queryBizFeatures(QueryTradeBizFeaturesRequest）": 1000, "com.alibaba.global.carts.api.facade.CartMtopFacade:1.0.0@*(*)": 1000, "com.alibaba.taobao.trade.message.executor.timeout.ConfirmGoodsTimeoutCallback@onTimeout(AlarmClockEvent)": 1000, "com.alibaba.global.order.management.api.facade.OrderNotificationFacade:1.0.0@sendImbaMsg(OrderNotificationRequest)": 1000, "com.alibaba.lazada.trade.adaptor.message.executor.payment.presale.LazadaFinalPaySuccessMsgExecutorImpl@execute(InputTradeMsgRequest)": 1000, "com.alibaba.global.order.management.facade.OrderQueryFacadeImpl@queryTradeOrderLineById(Long)": 1000, "com.lazada.carts.api.facade.CartFacade:1.0.0@*(*)": 1000, "com.alibaba.global.order.management.api.facade.BizFeaturesQueryFacade:1.0.0@queryOrderLineBizFeatures(QueryTradeOrderLineBizFeaturesRequest)": 1000, "com.alibaba.global.user.api.facade.UserReadFacade:1.0.0@*(*)": 1000, "com.alibaba.global.order.management.api.facade.FeaturesOperateFacade:1.0.0@updateOrderFeatures(UpdateOrderFeaturesRequest)": 1000, "com.alibaba.ae.trade.message.executor.payment.AeModifyPriceMsgExecutor@filter(InputTradeMsgRequest)": 1000, "com.alibaba.global.order.management.facade.OrderQueryForSellerFacadeImpl@queryBatchTradeOrders(QueryESTradeOrdersByShopOwnerIdRequest)": 1000, "com.alibaba.global.solution.trade.open.facade.OpenCheckoutFacadeImpl@createOrder(CreateOrderRequest,ClientInfo,CreateOption)": 1000, "com.alibaba.ae.trade.message.executor.payment.AePaymentBufferExecutor@buildInputMsgRequest(String,Object)": 1000, " com.alibaba.global.address.api.facade.LocationGstFreeFacade:1.0.0@*(*)": 1000, "com.alibaba.global.order.management.api.facade.OrderQueryForSellerFacade:1.0.0@regionalQueryBatchTradeOrderLines(QueryBatchTradeOrderLinesRequest)": 1000, "com.alibaba.global.carts.api.facade.CartMtopFacade:1.0.0@add(CartMtopAddRequest)": 1000, "com.alibaba.global.solution.trade.open.facade.OpenCheckoutFacadeImpl@renderOrder(RenderOrderRequest,ClientInfo,RenderOrderOption)": 1000, "com.alibaba.ae.trade.message.executor.notification.AeExtendConfirmTimeoutNotificationExecutor@execute(InputTradeMsgRequest)": 1000, "com.alibaba.global.wishlist.api.facade.WishlistFacade:1.0.0@getWishlist(WishlistQueryRequest)": 1000, "com.alibaba.global.trade.open.define.facade.CheckoutFacade:1.0.0@*(*)": 1000, "com.alibaba.global.buy.api.facade.wireless.WirelessCheckoutFacadeImpl@renderOrder(RenderQueryParams)": 1000, "com.alibaba.global.order.query.seller.servcie.OrderQueryScenarioService:1.0.0@queryOrderList(OrderListQueryRequest,String)": 1000, "com.alibaba.taobao.trade.message.executor.timeout.TaobaoSendGoodsTimeoutCallback@onTimeout(AlarmClockEvent)": 1000, "com.alibaba.global.carts.api.facade.CartDBFacade:1.0.0@getMultiBuy(long,long)": 1000, "com.alibaba.lazada.trade.adaptor.message.executor.trade.LazadaPureZeroPaySuccessMsgExecutor@execute(InputTradeMsgRequest)": 1000, "com.alibaba.ae.trade.message.executor.interactive.AeGroupBuyShareExecutor@execute(InputTradeMsgRequest)": 1000, "com.alibaba.global.buy.api.facade.pc.PcCheckoutFacade:1.0.0@adjustRender(AdjustQueryParams)": 1000, "com.alibaba.global.wishlist.api.facade.WishlistFacade:1.0.0@deleteItemById(Long,Long)": 1000, "com.alibaba.global.wishlist.api.facade.WishlistFacade:1.0.0@getWishlistStatisInfo(Long,Set)": 1000, "com.alibaba.ae.trade.message.executor.reverse.AeReturnFinishedExecutor@filter(InputTradeMsgRequest)": 1000, "com.alibaba.ae.trade.message.executor.timeout.AeCreateSendGoodsTimeoutMsgExecutor@filter(InputTradeMsgRequest)": 1000, "com.alibaba.global.order.management.api.facade.BizFeaturesQueryFacade:1.0.0@batchQueryOrderLineBizFeatures(BatchQueryTradeOrderLineBizFeaturesRequest)": 1000, "com.alibaba.global.order.management.api.facade.OrderViewFacade:1.0.0@adjustOrderDetail(AdjustBuyerOrderDetailRequest)": 1000, "com.alibaba.lazada.trade.adaptor.message.executor.oldpayment.LazadaOldPayIntentionMsgExecutor@execute(InputTradeMsgRequest)": 1000, "com.alibaba.global.order.management.api.facade.OrderRegulateFacade:1.0.0@backUp(String,String,String,String,String)": 1000, "com.lazada.wishlist.api.facade.WishlistMtopFacade:1.0.0@*(*)": 1000, "com.alibaba.global.carts.api.facade.CartMtopFacade:1.0.0@calculateProgressBar(CartProgressBarRequest)": 1000, "com.alibaba.global.order.management.api.facade.OrderMatchFacade:1.0.0@convertTradeOrderLineId(Long)": 1000, "com.alibaba.global.buy.api.facade.wireless.WirelessCheckoutFacadeImpl@adjustRender(AdjustQueryParams)": 1000, "com.alibaba.daraz.trade.adaptor.message.executor.oldpayment.DarazOldPayFailedMsgExecutor@execute(InputTradeMsgRequest)": 1000, "com.alibaba.global.wishlist.api.facade.WishlistFacade:1.0.0@addItems(List)": 1000, "com.alibaba.global.trade.open.define.facade.CheckoutFacade:1.0.0@preCreateOrderCheck(PreCreateOrderRequest,ClientInfo)": 1000, "com.alibaba.global.order.management.api.facade.OrderQueryFacade:1.0.0@queryTradeOrderLineById(Long)": 1000, "com.alibaba.global.order.management.api.facade.OrderViewFacade:1.0.0@renderDeliveryInfo(QueryDeliveryInfoRequest)": 1000, "com.alibaba.lazada.trade.adaptor.message.executor.oldpayment.presale.LazadaFinalOldPaySuccessMsgExecutorImpl@execute(InputTradeMsgRequest)": 1000, "com.alibaba.ae.trade.message.executor.timeout.AeCreatePayTimeoutMsgExecutor@execute(InputTradeMsgRequest)": 1000, "com.alibaba.global.carts.api.facade.CartFacade:1.0.0@query(CartQueryRequest)": 1000, "com.alibaba.ae.trade.message.executor.trade.AeCODCreateFulfillmentOrderExecutor@buildInputMsgRequest(String,TradeOrderMsgDTO)": 1000, "com.alibaba.global.order.management.facade.OrderQueryForSellerFacadeImpl@queryBatchTradeOrderLines(QueryESTradeOrderLinesRequest)": 1000, "com.alibaba.global.wishlist.api.facade.WishlistFacade:1.0.0@findByItemId(Long,Long)": 1000, "com.alibaba.global.buy.api.facade.wireless.WirelessCheckoutFacade:1.0.0@queryAmendPackage(QueryAmendPackageParams)": 1000, "com.alibaba.global.order.management.api.facade.OrderManagementFacade:1.0.0@createTimeout(CreateTimeoutRequest)": 1000, "com.alibaba.lazada.trade.adaptor.message.executor.uop.LazadaUopShippedMsgExecutor@execute(InputTradeMsgRequest)": 1000, "com.alibaba.global.merchant.common.batch.api.service.BackGroundTaskService:1.0.0@customizeImport(String,String,JSONObject)": 1000, "com.alibaba.global.order.management.api.facade.OrderManagementRegionFacade:1.0.0@paymentIntentionConfirmed(PaymentIntentionConfirmedRequest)": 1000, "com.alibaba.global.order.management.api.facade.OrderViewFacade:1.0.0@renderOrderDetail(QueryBuyerOrderDetailRequest)": 1000, "com.alibaba.global.order.management.api.facade.OrderManagementRegionFacade:1.0.0@closeOrder(CloseTradeRequest)": 1000, "com.alibaba.global.wishlist.api.facade.WishlistFacade:1.0.0@deleteItem(String,Long)": 1000, "com.alibaba.ae.trade.message.executor.timeout.AeCreatePayTimeoutMsgExecutor@filter(InputTradeMsgRequest)": 1000, "com.alibaba.global.wishlist.api.facade.WishlistFacade:1.0.0@addUsrTagForItem(WishlistAddTagForItemRequest)": 1000, "com.alibaba.global.order.management.api.facade.OrderQueryForSellerFacade:1.0.0@*(*)": 1000, "com.alibaba.global.order.management.api.facade.TestFacade:1.0.0@test(String)": 1000, "com.alibaba.global.order.management.api.facade.OrderManagementRegionFacade:1.0.0@pushOrder(PushOrderRequest)": 1000, "com.alibaba.global.order.management.api.facade.OrderManagementRegionFacade:1.0.0@receiveGoods(ReceiveGoodsRequest)": 1000, "com.alibaba.global.order.management.api.facade.OrderQueryForSellerFacade:1.0.0@querySearchValue(QueryESSearchValueRequest)": 1000, "com.alibaba.global.order.management.api.facade.OrderViewFacade:1.0.0@*(*)": 1000, "com.alibaba.daraz.trade.adaptor.message.executor.uop.DarazCreatePaymentOrderWhenShippedMsgExecutor@execute(InputTradeMsgRequest)": 1000, "com.alibaba.ae.trade.message.executor.notification.AeShippedNotificationExecutor@filter(InputTradeMsgRequest)": 1000, "com.lazada.order.management.api.facade.OrderQueryForSellerFacade:1.0.0@*(*)": 1000, "com.lazada.order.management.api.facade.OrderCallbackFacade:1.0.0@*(*)": 1000, "com.alibaba.global.trade.open.define.facade.OrderQueryFacade:1.0.0@bizCode()": 1000, "com.alibaba.global.merchant.order.facade.OrderOperatingFacade:1.0.0@confirmCancel(CancelOperateRequest)": 1000, "com.alibaba.lazada.trade.adaptor.message.executor.oldpayment.LazadaOldPayOtcCodeMsgExecutor@execute(InputTradeMsgRequest)": 1000, "com.alibaba.global.order.management.api.facade.OrderQueryForBuyerFacade:1.0.0@*(*)": 1000, "com.alibaba.global.carts.message.callback.OrderCreatedCallBackToCart@dealMessage(byte[])": 1000, "com.alibaba.ae.trade.message.executor.payment.AeModifyPriceMsgExecutor@getModifyPriceInfo(Long)": 1000, "com.alibaba.ae.trade.message.executor.payment.AePayAmountCheckExecutor@filter(InputTradeMsgRequest)": 1000, "com.alibaba.global.order.management.api.facade.OrderCountFacade:1.0.0@queryOrderLineCount(QueryTradeLineCountReq)": 1000, "com.alibaba.lazada.trade.adaptor.message.executor.oldpayment.presale.LazadaDepositOldPayAcceptMsgExecutorImpl@execute(InputTradeMsgRequest)": 1000, "com.alibaba.global.merchant.toolbox.gateway.service.IMerchantGatewayService:1.0.0@checkPreload()": 1000, "com.alibaba.global.merchant.order.facade.api.service.web.SellerOrderQueryWebFacade:1.0.0@*(*)": 1000, "com.alibaba.ae.trade.message.executor.interactive.AeGroupBuyShareExecutor@filter(InputTradeMsgRequest)": 1000, "com.alibaba.global.order.management.api.facade.OrderQueryForSellerFacade:1.0.0@queryBatchTradeOrderLines(QueryESTradeOrderLinesRequest)": 1000, "com.alibaba.global.order.management.api.facade.OrderCountFacade:1.0.0@tradeSummary(QueryTradeSummaryRequest)": 1000, "com.alibaba.ae.trade.mock.paymentFacadeV2.MockBatchCheckoutV2@response(Object[],Map)": 1000, "com.alibaba.lazada.trade.adaptor.message.executor.oldpayment.presale.LazadaFinalOldPayAcceptMsgExecutorImpl@execute(InputTradeMsgRequest)": 1000, "com.alibaba.global.order.management.api.facade.FeaturesOperateFacade:1.0.0@updateOrderLineFeatures(UpdateOrderLineFeaturesRequest)": 1000, "com.alibaba.global.trade.open.define.facade.CheckoutFacade:1.0.0@createOrderForMtop(CreateOrderRequestForMtop)": 1000, "com.alibaba.taobao.trade.message.executor.payment.TaobaoPaymentFailedMsgExecutor@execute(TaobaoInputTradeMsgRequest)": 1000, "com.alibaba.daraz.trade.adaptor.message.executor.oldpayment.DarazOldPaySuccessMsgExecutor@execute(InputTradeMsgRequest)": 1000, "com.alibaba.ae.trade.message.handler.AeUOPMsgHandler@receiveMessage(Message)": 1000, "com.alibaba.global.order.management.api.facade.OrderMatchFacade:1.0.0@convertTradeOrderIds(List<Long>)": 1000, "com.alibaba.global.satellite.api.SatelliteRouteFacade:1.0.0@invoke(HsfRequest)": 1000, "com.alibaba.global.buy.api.facade.wireless.WirelessCheckoutFacade:1.0.0@adjustRender(AdjustQueryParams)": 1000, "com.alibaba.global.order.management.api.facade.OrderManagementRegionFacade:1.0.0@paySuccess(PaySuccessRequest)": 1000, "com.alibaba.global.order.query.seller.servcie.OrderQueryScenarioService:1.0.0@queryOrderDetail(OrderDetailQueryRequest,String)": 1000, "com.alibaba.ae.trade.message.handler.AePaymentMsgHandler@receiveMessage(Message)": 1000, "com.alibaba.ae.trade.message.handler.AeReverseMsgHandler@receiveMessage(Message)": 1000, "com.alibaba.ae.trade.message.executor.notification.AePaySuccessNotificationExecutor@execute(InputTradeMsgRequest)": 1000, "com.alibaba.global.order.management.api.facade.OrderQueryForBuyerFacade:1.0.0@queryTradeOrderLineById(Long)": 1000, "com.alibaba.ae.trade.message.executor.uop.AeItemShippedMsgExecutor@filter(InputTradeMsgRequest)": 1000, "com.alibaba.ae.trade.message.executor.payment.AeChargeBackFreezeExecutor@filter(InputTradeMsgRequest)": 1000, "com.alibaba.global.order.management.api.facade.OrderViewFacade:1.0.0@queryOrderDeliveryStatus(OrderDeliveryStatusQueryRequest)": 1000, "com.alibaba.global.order.management.api.facade.OrderCountFacade:1.0.0@countTradeOrder(QueryTradeOrderCountRequest)": 1000, "com.alibaba.global.ump.client.service.GlobalPromotionCalculateService:1.0.0@*(*)": 1000, "com.taobao.order.query.seller.facade.SellerOrderQueryOpenApiFacade:1.0.0@*(*)": 1000, "com.lazada.order.management.api.facade.OrderViewFacade:1.0.0@*(*)": 1000, "com.alibaba.lazada.trade.adaptor.message.executor.timeout.presale.LazadaFinalPayNotificationCallBack@onTimeout(AlarmClockEvent)": 1000, "com.alibaba.global.trade.open.define.facade.CheckoutFacade:1.0.0@bizCode()": 1000, "com.alibaba.global.merchant.mtop.facade.SellerOrderQueryService:1.0.0@queryOrderBoard(OrderBoardRequest)": 1000, "com.alibaba.global.order.management.message.executor.pay.PaymentFailedMsgExecutor@execute(InputTradeMsgRequest)": 1000, "com.alibaba.global.order.management.api.facade.OrderTimeoutFacade:1.0.0@addTimeout(TimeoutAddRequest)": 1000, "com.alibaba.ae.trade.message.executor.timeout.AeConfirmGoodsTimeoutReminderCallback@onTimeout(AlarmClockEvent)": 1000, "com.alibaba.ae.trade.message.executor.notification.AeOrderConfirmedNotificationExecutor@filter(InputTradeMsgRequest)": 1000, "com.alibaba.lazada.trade.adaptor.message.executor.ov.LazadaOVAsyncCloseOrderMsgExecutor@execute(InputTradeMsgRequest)": 1000, "com.alibaba.ae.trade.message.executor.trade.AeOpLogExecutor@execute(InputTradeMsgRequest)": 1000, "com.taobao.order.query.seller.facade.impl.SellerOrderPrintWebFacadeFacadeImpl@validateCanPrint(BaseRequest)": 1000, "com.alibaba.lazada.trade.adaptor.message.executor.uop.UpdateO2OFeaturesExecutor@execute(InputTradeMsgRequest)": 1000, "com.alibaba.global.buy.api.facade.pressure.wireless.WirelessPressureFacade:1.0.0@shipping(RenderQueryParams)": 1000, "com.alibaba.global.merchant.common.batch.api.service.BackGroundTaskService:1.0.0@createTask(BatchTaskCreateRequest)": 1000, "com.alibaba.daraz.trade.adaptor.message.executor.timeout.DarazOtcPayTimeoutReminderCallback@onTimeout(AlarmClockEvent)": 1000, "com.taobao.order.query.seller.facade.impl.SellerOrderPrintWebFacadeFacadeImpl@printPickUpOrder(SellerOrderListRequest)": 1000, "com.alibaba.global.order.management.api.facade.OrderDeleteFacade:1.0.0@deleteTradeOrder(DeleteTradeOrderRequest)": 1000, "com.alibaba.ae.trade.message.executor.adapt.AeCODOrderDeliveredAdaptor@execute(InputTradeMsgRequest)": 1000, "com.alibaba.lazada.trade.adaptor.message.executor.uop.LazadaUopDeliveredMsgExecutor@execute(InputTradeMsgRequest)": 1000, "com.alibaba.ae.trade.message.executor.uop.AeHBAMsgExecutor@filter(InputTradeMsgRequest)": 1000, "com.alibaba.global.order.management.api.facade.OrderManagementRegionFacade:1.0.0@freezeTimeout(FreezeTimeoutRequest)": 1000, "com.alibaba.global.order.management.api.facade.OrderMatchFacade:1.0.0@convertTradeOrderId(Long)": 1000, "com.alibaba.taobao.trade.message.executor.payment.TaobaoPaymentIntentionExecutor@execute(TaobaoInputTradeMsgRequest)": 1000, "com.alibaba.global.order.management.api.facade:1.0.0@batchQueryTradeOrderLinesBizFeatures(QueryTradeOrderLinesBizFeaturesRequest）": 1000, "com.alibaba.ae.trade.message.executor.notification.AeModifyPriceNotificationExecutor@execute(InputTradeMsgRequest)": 1000, "com.alibaba.daraz.trade.adaptor.message.executor.trade.DarazCreateFulfillmentOrderMsgExecutor@execute(InputTradeMsgRequest)": 1000, "com.alibaba.global.wishlist.api.facade.WishlistFacade:1.0.0@findWishlistItems(String)": 1000, "com.alibaba.ae.trade.message.executor.uop.AeDeliveredMsgExecutor@buildInputMsgRequest(String,Object)": 1000, "com.alibaba.ae.trade.message.executor.timeout.AeCreateConfirmGoodsTimeoutMsgExecutor@execute(InputTradeMsgRequest)": 1000, "com.alibaba.global.order.management.api.facade.OrderManagementRegionFacade:1.0.0@createTimeout(CreateTimeoutRequest)": 1000, "com.alibaba.ae.trade.message.executor.trade.AePushCODOrderToConfirmedExecutor@execute(InputTradeMsgRequest)": 1000, "com.alibaba.global.order.management.api.facade.OrderManagementRegionFacade:1.0.0@createDeliveryOrder(CreateDeliveryOrderRequest)": 1000, "com.alibaba.ae.trade.message.executor.notification.AePaySuccessNotificationExecutor@filter(InputTradeMsgRequest)": 1000, "com.alibaba.global.order.management.api.facade.TestFacade:1.0.0@sendMsg(SendMessageRequest)": 1000, "com.alibaba.lazada.trade.adaptor.message.executor.payment.presale.LazadaDepositPaySuccessMsgExecutorImpl@execute(InputTradeMsgRequest)": 1000, "com.alibaba.global.buy.api.facade.CheckoutMultiterminalFacade:1.0.0@renderOrder(RenderOrderRequest)": 1000, "com.alibaba.ae.trade.message.handler.AeTradeMsgHandler@receiveMessage(Message)": 1000, "com.alibaba.ae.trade.message.executor.timeout.AePayTimeoutCallback@onTimeout(AlarmClockEvent)": 1000, "com.alibaba.taobao.trade.message.executor.timeout.TaobaoPayTimeoutReminderCallback@onTimeout(AlarmClockEvent)": 1000, "com.alibaba.global.solution.trade.open.facade.order.OpenOrderQueryFacadeImpl@query(OrderQueryRequest,ClientInfo)": 1000, "com.alibaba.taobao.trade.message.executor.fulfillment.TaobaoOFCTagMsgExecutor@execute(TaobaoInputTradeMsgRequest)": 1000, "com.alibaba.global.order.management.api.facade.OrderManagementRegionFacade:1.0.0@sendGoods(SellerSendGoodsRequest)": 1000, "com.alibaba.ae.trade.message.executor.payment.AePaySuccessMsgExecutor@buildInputMsgRequest(String,Object)": 1000, "com.alibaba.global.order.management.api.facade.OrderViewFacade:1.0.0@queryTradeResultReadyStatus(TradeResultReadyRequest)": 1000, "com.alibaba.global.buy.api.facade.wireless.WirelessCheckoutFacade:1.0.0@renderOrder(RenderQueryParams)": 1000, "com.alibaba.global.order.management.api.facade.OrderHistoryFacade:1.0.0@queryOrderLineHistory(OrderLineHistoryRequest)": 1000, "com.alibaba.global.carts.api.facade.CartMtopFacade:1.0.0@getPromotionDetail(CartPromotionDetailRequest)": 1000, "com.alibaba.global.carts.api.facade.CartDBFacade:1.0.0@*(*)": 1000, "com.alibaba.taobao.trade.message.executor.payment.TaobaoPaySuccessMsgExecutor@execute(TaobaoInputTradeMsgRequest)": 1000, "com.alibaba.global.order.management.facade.OrderQueryForSellerFacadeImpl@queryTradeOrder(QueryESTradeOrderRequest)": 1000, "com.alibaba.global.wishlist.api.facade.WishlistFacade:1.0.0@findWishlist(Long)": 1000, "com.alibaba.global.carts.api.facade.CartDBFacade:1.0.0@count(Long)": 1000, "com.alibaba.global.order.management.api.facade.OrderMatchFacade:1.0.0@convertTradeOrderIds(List)": 1000, "com.alibaba.ae.trade.message.executor.trade.AeCODCreateFulfillmentOrderExecutor@execute(InputTradeMsgRequest)": 1000, "com.alibaba.global.order.management.facade.OrderQueryForSellerFacadeImpl@batchCountTradeOrderForSeller(SellerBatchCountRequest)": 1000, "com.alibaba.global.order.management.api.facade.OrderManagementRegionFacade:1.0.0@buyerConfirmGoods(BuyerConfirmGoodsRequest)": 1000, "com.alibaba.ae.trade.message.executor.uop.AeDeliveredMsgExecutor@execute(InputTradeMsgRequest)": 1000, "com.alibaba.global.order.management.api.facade.OrderQueryForBuyerFacade:1.0.0@queryBatchTradeOrdersByIds(QueryESTradeOrdersByIdRequest)": 1000, "com.alibaba.daraz.trade.adaptor.message.executor.uop.DarazUopStatusUpdateMsgExecutor@execute(InputTradeMsgRequest)": 1000, "com.alibaba.lazada.trade.adaptor.message.executor.timeout.LazadaOtcPayTimeoutReminderCallback@onTimeout(AlarmClockEvent)": 1000, "com.alibaba.ae.trade.mock.inventoryReduceFacade.MockReduce@response(Object[],Map)": 1000, "com.alibaba.global.carts.api.facade.CartFacade:1.0.0@checkout(CartCheckOutRequest)": 1000, "com.alibaba.global.wishlist.api.facade.WishlistFacade:1.0.0@findWishlistItems(Long)": 1000, "com.alibaba.ae.trade.message.executor.trade.AeOpLogExecutor@filter(InputTradeMsgRequest)": 1000, "com.alibaba.ae.trade.message.executor.payment.AeModifyPriceMsgExecutor@buildInputMsgRequest(String,ModifyPriceEvent)": 1000, "com.alibaba.global.order.management.api.facade.OrderManagementRegionFacade:1.0.0@removeCache(CacheRemoveRequest)": 1000, "com.alibaba.global.order.management.api.facade.OrderManagementRegionFacade:1.0.0@updateSnapshot(SnapshotRequest)": 1000, "com.alibaba.global.order.management.api.facade.OrderCountFacade:1.0.0@purchaseFrequency(PurchaseFrequencyRequest)": 1000, "com.alibaba.global.wishlist.api.facade.WishlistFacade:1.0.0@updateWishlistLastSeen(Long)": 1000, "com.alibaba.global.order.management.api.facade.OrderSequenceFacade:1.0.0@queryOrderSequence(int,Long)": 1000, "com.alibaba.global.order.management.api.facade.OrderQueryForSellerFacade:1.0.0@queryBatchTradeOrders(QueryESTradeOrdersByShopOwnerIdRequest)": 1000, "com.lazada.order.management.api.facade.OrderManagementFacade:1.0.0@recalculateOrder(RecalculateOrderRequest)": 1000, "com.alibaba.global.trade.open.define.facade.CheckoutFacade:1.0.0@createOrder(CreateOrderRequest,ClientInfo,CreateOption)": 1000, "com.alibaba.global.carts.api.facade.CartFacade:1.0.0@delete(CartDeleteRequest)": 1000, "com.alibaba.ae.trade.message.executor.reverse.AeCancelReturnExecutor@execute(InputTradeMsgRequest)": 1000, "com.alibaba.global.order.query.seller.repository.TradeOrderSearchRepo:1.0.0@queryOrderLineIdsByOS(OrderListQueryRequest)": 1000, "com.alibaba.global.solution.trade.open.facade.OpenCheckoutFacadeImpl@preCreateOrder(PreCreateOrderRequest,ClientInfo,PreCreateOption)": 1000, "com.alibaba.ae.trade.message.executor.notification.AePartialShippedNotificationExecutor@filter(InputTradeMsgRequest)": 1000, "com.alibaba.global.order.management.facade.OrderQueryForSellerFacadeImpl@queryBatchTradeOrders(QueryESTradeOrdersRequest)": 1000, "com.alibaba.global.wishlist.api.facade.WishlistFacade:1.0.0@constructAggregate(WishlistUser,List)": 1000, "com.alibaba.ae.trade.message.executor.uop.AeItemShippedMsgExecutor@execute(InputTradeMsgRequest)": 1000, "com.alibaba.global.wishlist.api.facade.WishlistMtopFacade:1.0.0@getItemCount(WishlistBaseRequest)": 1000, "com.alibaba.global.order.management.api.facade.OrderDeleteFacade:1.0.0@permanentDeleteTradeOrder(DeleteTradeOrderRequest)": 1000, "com.alibaba.ae.trade.message.executor.timeout.AeCreateConfirmGoodsTimeoutMsgExecutor@filter(InputTradeMsgRequest)": 1000, "com.alibaba.global.wishlist.api.facade.WishlistMtopFacade:1.0.0@deleteWishlistItem(WishlistDeleteItemRequest)": 1000, "com.alibaba.global.order.management.api.facade.OrderQueryForSellerFacade:1.0.0@queryBatchTradeOrders(QueryESTradeOrdersRequest)": 1000, "com.alibaba.taobao.trade.message.executor.fulfillment.TaobaoDeliveredMsgExecutor@execute(TaobaoInputTradeMsgRequest)": 1000, "com.alibaba.global.buy.api.facade.wireless.WirelessCheckoutFacade:1.0.0@*(*)": 1000, "com.alibaba.lazada.trade.adaptor.message.executor.timeout.LazadaPayTimeoutCallback@onTimeout(AlarmClockEvent)": 1000, "com.alibaba.ae.trade.message.executor.notification.AeOrderConfirmedNotificationExecutor@execute(InputTradeMsgRequest)": 1000, "com.alibaba.lazada.trade.adaptor.message.executor.uop.LazadaUopStatusUpdateMsgExecutor@execute(InputTradeMsgRequest)": 1000, "com.alibaba.global.buy.api.facade.pc.PcCheckoutFacade:1.0.0@createOrder(CreateQueryParams)": 1000, "com.alibaba.global.satellite.api.SatelliteRouteFacade:1.0.0@invoke(MetaqRequest)": 1000, "com.alibaba.global.merchant.common.batch.api.facade.IBatchTaskFacade:1.0.0@createTask(BatchTaskCreateRequest)": 1000, "com.alibaba.global.order.management.api.facade.OrderRegulateFacade:1.0.0@delete(String,String,String)": 1000, "com.alibaba.ae.trade.message.handler.AeHBAHandler@receiveMessage(Message)": 1000, "com.alibaba.global.trade.open.define.facade.CheckoutFacade:1.0.0@renderOrder(RenderOrderRequest,ClientInfo,RenderOrderOption)": 1000, "com.alibaba.global.order.management.api.facade.OrderManagementRegionFacade:1.0.0@paymentBuffering(PaymentBufferRequest)": 1000, "com.alibaba.tradebrp.client.service.BrpReplayService:1.0.0@doBatchReplay(BrpReplayDO)": 1000, "com.alibaba.global.order.management.api.facade.OrderCountFacade:1.0.0@countTradeOrderLine(QueryTradeOrderLineCountRequest)": 1000, "com.alibaba.global.carts.api.facade.CartMtopFacade:1.0.0@ultron(CartMtopUltronRequest)": 1000, "com.alibaba.ae.trade.message.executor.timeout.AeSendGoodsTimeoutCallback@onTimeout(AlarmClockEvent)": 1000, "com.alibaba.daraz.trade.adaptor.message.executor.uop.DarazUopStatusUpdateAssExecutor@execute(InputTradeMsgRequest)": 1000, "com.alibaba.ae.trade.message.executor.payment.AePaySuccessMsgExecutor@filter(InputTradeMsgRequest)": 1000, "com.alibaba.global.order.management.api.facade.OrderManagementRegionFacade:1.0.0@cancelReverse(CancelReverseRequest)": 1000, "com.lazada.buy.api.facade.pc.PcCheckoutFacade:1.0.0@*(*)": 1000, "com.alibaba.global.merchant.order.facade.OrderQueryWebFacade:1.0.0@queryOrderList(OrderQueryRequest)": 1000, "com.alibaba.global.wishlist.api.facade.WishlistFacade:1.0.0@deleteItem(Long,Long)": 1000, "com.alibaba.daraz.trade.adaptor.message.executor.uop.DarazUopShippedMsgExecutor@execute(InputTradeMsgRequest)": 1000, "com.alibaba.global.wishlist.api.facade.WishlistFacade:1.0.0@deleteItemById(String,Long)": 1000, "com.alibaba.global.order.management.api.facade.TestFacade:1.0.0@getCache(String)": 1000, "com.alibaba.taobao.trade.message.executor.reverse.TaobaoReverseConfirmGoodsTimeoutMsgExecutor@execute(TaobaoInputTradeMsgRequest)": 1000, "com.alibaba.global.order.management.facade.OrderQueryForSellerFacadeImpl@queryBatchTradeOrdersByIds(QueryESTradeOrdersByIdRequest)": 1000, "com.alibaba.global.merchant.common.batch.api.service.BackGroundTaskService:1.0.0@export(String,String,JSONObject)": 1000, "com.alibaba.global.merchant.order.facade.OrderQueryWebFacade:1.0.0@queryOrderDetail(Long,OrderDetailQueryOption)": 1000, "com.alibaba.lazada.trade.adaptor.message.executor.payment.LazadaPayFailedMsgExecutor@execute(*)": 1000, "com.alibaba.global.order.management.api.facade.OrderRecoveryFacade:1.0.0@recoverOrder(RecoverTradeOrdersRequest)": 1000, "com.alibaba.global.trade.open.define.facade.CheckoutFacade:1.0.0@preCreateOrder(PreCreateOrderRequest,ClientInfo,PreCreateOption)": 1000, "com.alibaba.taobao.trade.message.executor.ov.TaobaoOVAsyncCloseOrderMsgExecutor@execute(TaobaoInputTradeMsgRequest)": 1000, "com.alibaba.global.order.query.seller.servcie.OrderQueryScenarioService:1.0.0@queryOrderCount(OrderListQueryRequest)": 1000, "com.alibaba.global.carts.api.facade.CartMtopFacade:1.0.0@appointCalculate(CartMtopAppointCalculateRequest)": 1000, "com.alibaba.tradebrp.client.service.BrpReplayService:1.0.0@closeMetaQReceiver()": 1000, "com.alibaba.daraz.trade.adaptor.message.executor.payment.DarazPaySuccessMsgExecutor@execute(InputTradeMsgRequest)": 1000, "com.alibaba.global.wishlist.api.facade.WishlistFacade:1.0.0@getItemCount(Long)": 1000, "com.alibaba.global.buy.api.facade.pc.PcCheckoutFacade:1.0.0@renderOrder(RenderQueryParams)": 1000, "com.alibaba.global.merchant.order.facade.api.service.SellerOrderQueryFacade:1.0.0@*(*)": 1000, "com.alibaba.global.buy.api.facade.ErrorCodeFacade:1.0.0@queryDisplayMessage(String)": 1000, "com.alibaba.ae.trade.message.executor.notification.AePartialShippedNotificationExecutor@execute(InputTradeMsgRequest)": 1000, "com.alibaba.global.order.management.api.facade.OrderHistoryFacade:1.0.0@createOrderLineHistory(CreateOrderLineHistoryRequest)": 1000, "com.alibaba.taobao.trade.message.executor.fulfillment.TaobaoShippedMsgExecutor@execute(TaobaoInputTradeMsgRequest)": 1000, "com.alibaba.global.order.management.api.facade.OrderManagementRegionFacade:1.0.0@payRiskPassed(PaySuccessRequest)": 1000, "com.alibaba.global.buy.api.facade.pressure.PressureFacade:1.0.0@placeOrder(CreateOrderRequest)": 1000, "com.alibaba.global.shop.api.facade.ShopReadFacade:1.0.0@*(*)": 1000, "com.alibaba.global.wishlist.api.facade.WishlistFacade:1.0.0@findBySkuId(Long,Long)": 1000, "com.alibaba.global.buy.api.facade.CheckoutFacade:1.0.0@renderOrder(RenderOrderRequest)": 1000, "com.alibaba.global.wishlist.api.facade.WishlistMtopFacade:1.0.0@addWishlistItem(WishlistAddItemRequest)": 1000, "com.alibaba.global.order.query.seller.servcie.OrderQueryScenarioService:1.0.0@queryOrderLineList(OrderListQueryRequest,String)": 1000, "com.alibaba.ae.trade.message.executor.trade.AePushCODOrderToConfirmedExecutor@filter(InputTradeMsgRequest)": 1000, "com.alibaba.ae.trade.message.executor.payment.AePaySuccessMsgExecutor@execute(InputTradeMsgRequest)": 1000, "com.alibaba.global.order.management.facade.OrderQueryForSellerFacadeImpl@regionalQueryBatchTradeOrderLines(QueryBatchTradeOrderLinesRequest)": 1000, "com.alibaba.global.buy.api.facade.pc.PcCheckoutFacade:1.0.0@oneKeyCreateOrder(CreateQueryParams)": 1000, "com.alibaba.global.order.management.api.facade.OrderViewFacade:1.0.0@validateTrackingMyOrder(TrackingMyOrderRequest)": 1000, "com.alibaba.global.order.management.api.facade.OrderCountFacade:1.0.0@tradeStatusBarCalculator(TradeStatusBarCalculatorRequest)": 1000, "com.alibaba.global.order.management.api.facade.OrderAddCartFacade:1.0.0@addOrderToCart(AddOrderToCartRequest)": 1000, "com.alibaba.global.order.management.api.facade.OrderDeleteFacade:1.0.0@permanentDeleteTradeOrder(DeleteTradeOrdersRequest)": 1000, "com.alibaba.ae.trade.message.executor.reverse.AeCancelReturnExecutor@filter(InputTradeMsgRequest)": 1000, "com.alibaba.global.wishlist.api.facade.WishlistFacade:1.0.0@getPassPurchaseItems(Long,Integer)": 1000, "com.alibaba.ae.trade.message.executor.trade.AeCODCreateFulfillmentOrderExecutor@filter(InputTradeMsgRequest)": 1000}, "mainInvokeIdentities": null, "taskId": 550}, "traceDataContext": {"traceId": null, "dataKey": null, "contextKey": "1446_0b8f77e416196132610802351e8282_0.1.2.5.8", "mainInvokeEntryFlag": false, "multiAppName": null, "isNeedReplay": true, "invokeFlag": null, "threadType": 1, "taskId": null}, "traceId": "0b8f77e416196132610802351e8282_0.1.2.5", "triggerContext": null}, "successful": true, "errorCode": null, "errorMessage": null, "additionalInfo": {"size": 411026, "sr": "33.4.8.152"}}