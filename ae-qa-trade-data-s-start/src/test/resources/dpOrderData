{"shippingDiscountFee": {"factory": null, "amount": 13.83, "cent": 1383, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 13.83}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 1383, "currencyCode": "USD"}, "reverseShippingFee": null, "bizCode": "ali.global.ae.general", "tradeOrderId": 1000177500140182, "amendableLogicDeliveryOrder": null, "driveInfoDTO": {"driveType": "pc", "wirelessType": null, "entrance": null, "class": "com.alibaba.global.order.management.api.model.DriveInfoDTO"}, "deliverStatus": 0, "trial": false, "features": {"featureMap": {"di": "{\"dt\":\"pc\"}", "buyerEmail": "<EMAIL>", "source": "{\"from\":\"BUY_NOW\",\"device\":{},\"network\":{\"clientIp\":\"*************\",\"sessionId\":\"1JSRP3AW7xgg4jtqi5uP94g1\",\"cookieId\":\"**********.1592043035544.224272.8\",\"userAgent\":\"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.141 Safari/537.36\"},\"system\":{\"platformType\":\"DESKTOP\"},\"site\":{\"locale\":\"es_ES\"},\"extraParams\":{\"MOBILE_ALIPAY_UMID_TOKEN\":\"\",\"MOBILE_API_NAME\":\"\",\"MOBILE_API_VERSION\":null,\"MOBILE_MINI_WUA\":\"140#rAMr1wIrzzZPbzo2woRz/3Sp/FLlvvhhzHz4sjcw5pNTi0qfrffVWm7ougvgisF6BSzlJcJArLgbZznt/hp//mxI9xokTNcv5a7UNgFVjbpybB1IwgDxUN+ybrZ/x6NJTS9Rdz5FxKaYwZQblp1zz/Bk9luKoQzxJKD+93vXzzrb22U3lp1xzEUAIUGoZFrrmDc3L6gqzzraKQ2cl3zo7kSVVSJoQqXx2aNzbyxZzOOi2SirUUMoxxrVIAVBlaznLBc+IA5txjfDCddwxrvZrI7ZbhGHm2wVfT5mfVTtrWD5JxjTcPrXmPkkdciGGrDo/hKki5T84QXuKgJFk61tlvr5Hxn2iFONvSFmSB1gp6Sm8gLMYOYjfq7YzaMNPNh0kISYLIzRKwottOa30JqxgewSel2K4beJ3DaZFbPVc4NJgWvvX92l52BxZcsS4kg5+7haoNw6Nz/e9gJnIJfCrxFI40GvcCbHVTbo+y4EXeCfqhxIZqP6mDNOOlq31mdMXsZ9paubv5dpyFIpHd/1EV+BiW9iSWqDVS7is5hU7jYojdJJKpOOnEXZNXDnfcweyDve4Pk038ZnIu2TfJRttG8Yir7zuf9gf6bBPabhhq+kpiQmR7aH3FRHm5J922nHjk9WQUar3XagWy46+SPt8anxVK5q98s3++cIIcbQ5/2mOD/zRejqcVmuSn62qH7aIXhKMlyObSfLWPEhtvfT9q9c/+bSF10NpndzAGTIdfOIYD6G4DOMEtTh+movpi0fOm/idYzSPU/y0O/EmswWOb8GJVFSclpjfVDtlqESBIJomhpFmoecefNvQ/BvfcSv+prAAgnVwV7lygfIxx/3E1bIpFAcK+5GuJAADVTaXLNRfhuPRy05/41188HMzDGrUW6PmxCsalKiBC8qpBobXtndNJNsQqfhKKl/mSCFaFmxqT+MFD9BpnRDFWJ8MOO1QGmv9f48HbMNJ/iUlj65PpZ7TFkpC55PVv7m0P6Ze3rVxntimN/QmiiqneApTcLNVeAfqvzpZpaAJG4ZVuUSmBr1JSRApMXkxtJyjsXeSHmgYsxmwV8E6rM7OTuQsX5ZT+ijRSPzm7RhuMci8LVWN6QVaUaspNbxN5lMidDFDv79Bc8N/VEALMgJ0AqXkFKndjXQPsgrQF/R+H1x/rxNf8gadXRpKDxXsNJMODHC8lrmMreYHKethPyC5mdCzb5TFeSUpvVC5bFKjcUsG4izByofMXb6lh/WXrIe3MOekaDBN08oIltWFq7MkE81LCtub4pMFuEnZIh6ZpLdFpU7NEda95GikA1JDoDJ7M3xZAyTwx1HcAZXv4ms+PTTvo3Oah7sOAlFGLrcOChRL4TV85ZYxIqaos4QIg+fSiZctlmENGoeru/fh8EkAeiD16O1Z0uEMSsDxAryvDuEfVNv/jWoOYQ1tLv7vsZpd6NXTawHHq7Zc95tkPGcGrkjuO0j+fcNCr68+Q6g8RiGZ9MxFh5TAeNti28rPHH4PnJVPqrXtJkV5ON4TwzfgtH3vOQ61APpFuKDu6iCUfUdxFuj6Fbsyz146f9u01/NSX9bWVa1hgIRJdrMJlFydV8MK/n4gKpyJUbvZPt+o/tTKz==\",\"MOBILE_SERVER_ENV_RAW\":\"\",\"MOBILE_UMID_TOKEN\":\"B894bde052d392a1ee13b420900d15938\",\"_adid\":null,\"_anony_id\":null,\"_cna\":\"+6Q/F+PAklACAXrr8mBec4Z+\",\"_utdid\":null,\"ali_apache_id\":\"**********.1592043035544.224272.8\",\"application\":\"CHECKOUT\",\"cna\":\"+6Q/F+PAklACAXrr8mBec4Z+\",\"cookieId\":\"**********.1592043035544.224272.8\",\"entrance\":null,\"ip\":\"*************\",\"page_id\":\"6qfpaklacaxrr8mb1777b3f904877589b7b1f3d280\",\"phase\":\"CREATE\",\"realtimeUmidToken\":\"B894bde052d392a1ee13b420900d15938\",\"realtimeUmidTokenType\":null,\"sessionId\":\"1JSRP3AW7xgg4jtqi5uP94g1\",\"source\":\"PC\",\"subPlatform\":null,\"tmpUmid\":\"T2gA8IhtA7SQeTZtjwQtwhUevGuRgx7tshnOv4zoouECLPbs65CcwGg4cs52qEIsLbs=\",\"umid\":\"CV13z5eb8b6ed258267450007efe427f7\",\"utdid\":null}}", "bp": "*********", "deliveryAddressSnapshotId": "117175200", "protocol": "1", "sellerId": "*********", "d_p": "1", "shippingAddressType": "DOMESTIC_USER_ADDRESS", "sellerSignupTime": "1291776387000", "promotionSnapshotId": "2000096120070182", "machineType": "OTHER.PC", "new": "1", "kernel": "1", "sri": "1000177500140182", "adp": "1", "toc_p": "1728000", "toc_ship": "1468800", "bppc": "33", "ck_s": "PASS", "ck_t": "1612680626.796000000", "afz": "false", "toc_all": "[{\"gmtStart\":1612680627197,\"type\":1,\"initSetSec\":1728000,\"remindSecs\":[259200,86400]},{\"type\":2,\"initSetSec\":1468800,\"remindSecs\":[]}]", "ctov": "1", "bul": "0", "plt": "3", "orderFrom": "BUY_NOW", "coi": "*************", "ro": "{\"br\":{\"st\":\"2019-03-11T23:26:44-07:00\",\"uc\":\"GLOBAL\",\"ut\":\"NORMAL\"}}"}, "notEmpty": true, "class": "com.alibaba.global.order.management.api.model.Features", "empty": false}, "freeze": 0, "deliveryAddress": {"city": "<PERSON><PERSON>", "locationTreeAddressId": "919965786577000000-919965786577067000", "latitude": null, "postCodeType": null, "companyName": null, "userAddressType": null, "countryId": 0, "thirdLevelAddressName": null, "addressId": 1300320008, "features": {"featureMap": {}, "notEmpty": false, "class": "com.alibaba.global.order.management.api.model.Features", "empty": true}, "countryCode": "ES", "state": "Almeria", "class": "com.alibaba.global.order.management.api.model.AddressInfoDTO", "longitude": null, "receiver": {"passportNo": null, "encryptCpf": null, "encryptPassportNo": null, "identifyNo": null, "fullName": "<PERSON> jianli", "mobileNo": "*********", "phoneCountry": "+90", "phonePrefixCode": null, "passportNoDate": null, "phone": null, "taxId": null, "mobilePrefixCode": "+90", "cpf": null, "phoneArea": null, "contractName": null, "class": "com.alibaba.global.order.management.api.model.ReceiveUserInfoDTO", "passportOrganization": null}, "address2": "Cat", "addressType": "HOME", "addressTag": "local", "languageCode": "es", "locationTreeAddressName": "Almeria,Adra", "deliveryTimeTag": "local", "fullAddress": "Adra, Almeria, Tom", "detailAddress": "<PERSON>", "postCode": "11111", "countryName": "Spain", "locationGSTFree": null}, "originalOrderNumber": 1000177500140182, "minDeliveryStatus": 0, "shippingActualFee": {"factory": null, "amount": 0, "cent": 0, "positive": false, "positiveOrZero": true, "zero": true, "number": {"number": 0}, "negative": false, "negativeOrZero": true, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 0, "currencyCode": "USD"}, "actualFee": {"factory": null, "amount": 46.99, "cent": 4699, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 46.99}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 4699, "currencyCode": "USD"}, "searchStatus": 1, "reverseFee": null, "endReason": null, "taxActualFee": {"factory": null, "amount": 0, "cent": 0, "positive": false, "positiveOrZero": true, "zero": true, "number": {"number": 0}, "negative": false, "negativeOrZero": true, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 0, "currencyCode": "USD"}, "groupBuy": false, "payOrderInfoDTO": {"paymentChannelParams": null, "productCode": null, "riskInfo": null, "cashierUrl": null, "coId": "20990591103210207792100880182", "extraParams": null, "pmntId": null, "class": "com.alibaba.global.order.management.api.model.PayOrderInfoDTO", "payplanId": null, "paymentChannel": null}, "minPaymentStatus": 1, "paidTime": null, "buyer": {"buyerPhone": "*********", "userLevel": 0, "buyerEmail": "<EMAIL>", "buyerPhonePrefixCode": "33", "buyerIcon": null, "buyerId": **********, "class": "com.alibaba.global.order.management.api.model.BuyerInfoDTO", "buyerFullName": "hz four"}, "stageOrderLines": null, "shippingFee": {"factory": null, "amount": 13.83, "cent": 1383, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 13.83}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 1383, "currencyCode": "USD"}, "actualFeeOfPurposeCurrency": {"factory": null, "amount": 40.2, "cent": 4020, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 40.2}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "EUR", "defaultFractionDigits": 2, "numericCode": 978}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 4020, "currencyCode": "EUR"}, "saleOriginalFee": {"factory": null, "amount": 49, "cent": 4900, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 49}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 4900, "currencyCode": "USD"}, "deliveryTime": null, "orderStatus": 0, "amendOriginalOrder": true, "orderAmount": {"factory": null, "amount": 47, "cent": 4700, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 47}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 4700, "currencyCode": "USD"}, "originalAddress": null, "orderLines": [{"originalSaleDiscountInfo": [{"promotionSponsorDTOS": null, "doSplit": true, "currentPromotionOriginalInfo": {"rebatesPercentage": null, "productSubCode": null, "priceTypes": [{"priceType": 0, "class": "com.alibaba.global.order.management.api.model.PromotionPriceTypeDTO", "monetaryAmount": {"factory": null, "amount": 0.01, "cent": 1, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 0.01}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 1, "currencyCode": "USD"}}], "spreadCode": null, "promotionSource": "CHECKOUT", "productCode": "platformCoupon", "promotionRole": "PLATFORM", "doesBundle": false, "doesMaster": null, "relationId": null, "class": "com.alibaba.global.order.management.api.model.PromotionOrderDTO", "promotionId": "5000008836917001"}, "nonGoldStandardDiscountFee": {"factory": null, "amount": 0, "cent": 0, "positive": false, "positiveOrZero": true, "zero": true, "number": {"number": 0}, "negative": false, "negativeOrZero": true, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 0, "currencyCode": "USD"}, "discountFee": {"factory": null, "amount": 0.01, "cent": 1, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 0.01}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 1, "currencyCode": "USD"}, "goldStandardDiscountFee": {"factory": null, "amount": 0.01, "cent": 1, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 0.01}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 1, "currencyCode": "USD"}, "promotionOriginalInfo": [{"rebatesPercentage": null, "productSubCode": null, "priceTypes": [{"priceType": 0, "class": "com.alibaba.global.order.management.api.model.PromotionPriceTypeDTO", "monetaryAmount": {"factory": null, "amount": 1, "cent": 100, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 1}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 100, "currencyCode": "USD"}}], "spreadCode": null, "promotionSource": "CHECKOUT", "productCode": "shopCoupon", "promotionRole": "SELLER", "doesBundle": false, "doesMaster": null, "relationId": null, "class": "com.alibaba.global.order.management.api.model.PromotionOrderDTO", "promotionId": "1000000047688388"}, {"rebatesPercentage": null, "productSubCode": null, "priceTypes": [{"priceType": 0, "class": "com.alibaba.global.order.management.api.model.PromotionPriceTypeDTO", "monetaryAmount": {"factory": null, "amount": 1, "cent": 100, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 1}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 100, "currencyCode": "USD"}}], "spreadCode": null, "promotionSource": "CHECKOUT", "productCode": "fixedDiscount", "promotionRole": "SELLER", "doesBundle": false, "doesMaster": null, "relationId": null, "class": "com.alibaba.global.order.management.api.model.PromotionOrderDTO", "promotionId": "5000004939102165"}, {"rebatesPercentage": null, "productSubCode": null, "priceTypes": [{"priceType": 0, "class": "com.alibaba.global.order.management.api.model.PromotionPriceTypeDTO", "monetaryAmount": {"factory": null, "amount": 13.83, "cent": 1383, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 13.83}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 1383, "currencyCode": "USD"}}], "spreadCode": null, "promotionSource": "CHECKOUT", "productCode": "freeShipping", "promotionRole": "SELLER", "doesBundle": false, "doesMaster": null, "relationId": null, "class": "com.alibaba.global.order.management.api.model.PromotionOrderDTO", "promotionId": "5000004940805715"}, {"rebatesPercentage": null, "productSubCode": null, "priceTypes": [{"priceType": 0, "class": "com.alibaba.global.order.management.api.model.PromotionPriceTypeDTO", "monetaryAmount": {"factory": null, "amount": 0.01, "cent": 1, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 0.01}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 1, "currencyCode": "USD"}}], "spreadCode": null, "promotionSource": "CHECKOUT", "productCode": "platformCoupon", "promotionRole": "PLATFORM", "doesBundle": false, "doesMaster": null, "relationId": null, "class": "com.alibaba.global.order.management.api.model.PromotionOrderDTO", "promotionId": "5000008836917001"}], "groupBy": null, "class": "com.alibaba.global.order.management.api.model.PromotionFeeInfoDTO", "promotionId": "5000008836917001"}, {"promotionSponsorDTOS": null, "doSplit": true, "currentPromotionOriginalInfo": {"rebatesPercentage": null, "productSubCode": null, "priceTypes": [{"priceType": 0, "class": "com.alibaba.global.order.management.api.model.PromotionPriceTypeDTO", "monetaryAmount": {"factory": null, "amount": 1, "cent": 100, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 1}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 100, "currencyCode": "USD"}}], "spreadCode": null, "promotionSource": "CHECKOUT", "productCode": "shopCoupon", "promotionRole": "SELLER", "doesBundle": false, "doesMaster": null, "relationId": null, "class": "com.alibaba.global.order.management.api.model.PromotionOrderDTO", "promotionId": "1000000047688388"}, "nonGoldStandardDiscountFee": {"factory": null, "amount": 1, "cent": 100, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 1}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 100, "currencyCode": "USD"}, "discountFee": {"factory": null, "amount": 1, "cent": 100, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 1}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 100, "currencyCode": "USD"}, "goldStandardDiscountFee": {"factory": null, "amount": 0, "cent": 0, "positive": false, "positiveOrZero": true, "zero": true, "number": {"number": 0}, "negative": false, "negativeOrZero": true, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 0, "currencyCode": "USD"}, "promotionOriginalInfo": [{"rebatesPercentage": null, "productSubCode": null, "priceTypes": [{"priceType": 0, "class": "com.alibaba.global.order.management.api.model.PromotionPriceTypeDTO", "monetaryAmount": {"factory": null, "amount": 1, "cent": 100, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 1}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 100, "currencyCode": "USD"}}], "spreadCode": null, "promotionSource": "CHECKOUT", "productCode": "shopCoupon", "promotionRole": "SELLER", "doesBundle": false, "doesMaster": null, "relationId": null, "class": "com.alibaba.global.order.management.api.model.PromotionOrderDTO", "promotionId": "1000000047688388"}, {"rebatesPercentage": null, "productSubCode": null, "priceTypes": [{"priceType": 0, "class": "com.alibaba.global.order.management.api.model.PromotionPriceTypeDTO", "monetaryAmount": {"factory": null, "amount": 1, "cent": 100, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 1}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 100, "currencyCode": "USD"}}], "spreadCode": null, "promotionSource": "CHECKOUT", "productCode": "fixedDiscount", "promotionRole": "SELLER", "doesBundle": false, "doesMaster": null, "relationId": null, "class": "com.alibaba.global.order.management.api.model.PromotionOrderDTO", "promotionId": "5000004939102165"}, {"rebatesPercentage": null, "productSubCode": null, "priceTypes": [{"priceType": 0, "class": "com.alibaba.global.order.management.api.model.PromotionPriceTypeDTO", "monetaryAmount": {"factory": null, "amount": 13.83, "cent": 1383, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 13.83}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 1383, "currencyCode": "USD"}}], "spreadCode": null, "promotionSource": "CHECKOUT", "productCode": "freeShipping", "promotionRole": "SELLER", "doesBundle": false, "doesMaster": null, "relationId": null, "class": "com.alibaba.global.order.management.api.model.PromotionOrderDTO", "promotionId": "5000004940805715"}, {"rebatesPercentage": null, "productSubCode": null, "priceTypes": [{"priceType": 0, "class": "com.alibaba.global.order.management.api.model.PromotionPriceTypeDTO", "monetaryAmount": {"factory": null, "amount": 0.01, "cent": 1, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 0.01}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 1, "currencyCode": "USD"}}], "spreadCode": null, "promotionSource": "CHECKOUT", "productCode": "platformCoupon", "promotionRole": "PLATFORM", "doesBundle": false, "doesMaster": null, "relationId": null, "class": "com.alibaba.global.order.management.api.model.PromotionOrderDTO", "promotionId": "5000008836917001"}], "groupBy": null, "class": "com.alibaba.global.order.management.api.model.PromotionFeeInfoDTO", "promotionId": "1000000047688388"}, {"promotionSponsorDTOS": null, "doSplit": true, "currentPromotionOriginalInfo": {"rebatesPercentage": null, "productSubCode": null, "priceTypes": [{"priceType": 0, "class": "com.alibaba.global.order.management.api.model.PromotionPriceTypeDTO", "monetaryAmount": {"factory": null, "amount": 1, "cent": 100, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 1}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 100, "currencyCode": "USD"}}], "spreadCode": null, "promotionSource": "CHECKOUT", "productCode": "fixedDiscount", "promotionRole": "SELLER", "doesBundle": false, "doesMaster": null, "relationId": null, "class": "com.alibaba.global.order.management.api.model.PromotionOrderDTO", "promotionId": "5000004939102165"}, "nonGoldStandardDiscountFee": {"factory": null, "amount": 1, "cent": 100, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 1}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 100, "currencyCode": "USD"}, "discountFee": {"factory": null, "amount": 1, "cent": 100, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 1}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 100, "currencyCode": "USD"}, "goldStandardDiscountFee": {"factory": null, "amount": 0, "cent": 0, "positive": false, "positiveOrZero": true, "zero": true, "number": {"number": 0}, "negative": false, "negativeOrZero": true, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 0, "currencyCode": "USD"}, "promotionOriginalInfo": [{"rebatesPercentage": null, "productSubCode": null, "priceTypes": [{"priceType": 0, "class": "com.alibaba.global.order.management.api.model.PromotionPriceTypeDTO", "monetaryAmount": {"factory": null, "amount": 1, "cent": 100, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 1}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 100, "currencyCode": "USD"}}], "spreadCode": null, "promotionSource": "CHECKOUT", "productCode": "shopCoupon", "promotionRole": "SELLER", "doesBundle": false, "doesMaster": null, "relationId": null, "class": "com.alibaba.global.order.management.api.model.PromotionOrderDTO", "promotionId": "1000000047688388"}, {"rebatesPercentage": null, "productSubCode": null, "priceTypes": [{"priceType": 0, "class": "com.alibaba.global.order.management.api.model.PromotionPriceTypeDTO", "monetaryAmount": {"factory": null, "amount": 1, "cent": 100, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 1}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 100, "currencyCode": "USD"}}], "spreadCode": null, "promotionSource": "CHECKOUT", "productCode": "fixedDiscount", "promotionRole": "SELLER", "doesBundle": false, "doesMaster": null, "relationId": null, "class": "com.alibaba.global.order.management.api.model.PromotionOrderDTO", "promotionId": "5000004939102165"}, {"rebatesPercentage": null, "productSubCode": null, "priceTypes": [{"priceType": 0, "class": "com.alibaba.global.order.management.api.model.PromotionPriceTypeDTO", "monetaryAmount": {"factory": null, "amount": 13.83, "cent": 1383, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 13.83}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 1383, "currencyCode": "USD"}}], "spreadCode": null, "promotionSource": "CHECKOUT", "productCode": "freeShipping", "promotionRole": "SELLER", "doesBundle": false, "doesMaster": null, "relationId": null, "class": "com.alibaba.global.order.management.api.model.PromotionOrderDTO", "promotionId": "5000004940805715"}, {"rebatesPercentage": null, "productSubCode": null, "priceTypes": [{"priceType": 0, "class": "com.alibaba.global.order.management.api.model.PromotionPriceTypeDTO", "monetaryAmount": {"factory": null, "amount": 0.01, "cent": 1, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 0.01}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 1, "currencyCode": "USD"}}], "spreadCode": null, "promotionSource": "CHECKOUT", "productCode": "platformCoupon", "promotionRole": "PLATFORM", "doesBundle": false, "doesMaster": null, "relationId": null, "class": "com.alibaba.global.order.management.api.model.PromotionOrderDTO", "promotionId": "5000008836917001"}], "groupBy": null, "class": "com.alibaba.global.order.management.api.model.PromotionFeeInfoDTO", "promotionId": "5000004939102165"}], "shippingDiscountFee": {"factory": null, "amount": 13.83, "cent": 1383, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 13.83}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 1383, "currencyCode": "USD"}, "seller": {"senderAddressDTO": {"locationTreeAddressId": "440305007", "countryCode": null, "fullAddress": "ke yuan nan lu 3398 hao", "detailAddress": "ke yuan nan lu 3398 hao", "class": "com.alibaba.global.order.management.api.model.SellerAddressInfoDTO", "addressId": null}, "sellerId": *********, "shopOwnerId": null, "sellerPhone": "81819494949", "flagship": false, "sellerFullName": "aatest Store", "subSellerId": "*********", "sellerShortCode": null, "storeId": null, "sellerType": 1, "class": "com.alibaba.global.order.management.api.model.SellerInfoDTO", "sellerEmail": "<EMAIL>"}, "taxRebateInfo": null, "saleDiscountInfo": [{"promotionSponsorDTOS": null, "doSplit": true, "currentPromotionOriginalInfo": {"rebatesPercentage": null, "productSubCode": null, "priceTypes": [{"priceType": 0, "class": "com.alibaba.global.order.management.api.model.PromotionPriceTypeDTO", "monetaryAmount": {"factory": null, "amount": 0.01, "cent": 1, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 0.01}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 1, "currencyCode": "USD"}}], "spreadCode": null, "promotionSource": "CHECKOUT", "productCode": "platformCoupon", "promotionRole": "PLATFORM", "doesBundle": false, "doesMaster": null, "relationId": null, "class": "com.alibaba.global.order.management.api.model.PromotionOrderDTO", "promotionId": "5000008836917001"}, "nonGoldStandardDiscountFee": {"factory": null, "amount": 0, "cent": 0, "positive": false, "positiveOrZero": true, "zero": true, "number": {"number": 0}, "negative": false, "negativeOrZero": true, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "EUR", "defaultFractionDigits": 2, "numericCode": 978}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 0, "currencyCode": "EUR"}, "discountFee": {"factory": null, "amount": 0.01, "cent": 1, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 0.01}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "EUR", "defaultFractionDigits": 2, "numericCode": 978}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 1, "currencyCode": "EUR"}, "goldStandardDiscountFee": {"factory": null, "amount": 0.01, "cent": 1, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 0.01}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "EUR", "defaultFractionDigits": 2, "numericCode": 978}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 1, "currencyCode": "EUR"}, "promotionOriginalInfo": [{"rebatesPercentage": null, "productSubCode": null, "priceTypes": [{"priceType": 0, "class": "com.alibaba.global.order.management.api.model.PromotionPriceTypeDTO", "monetaryAmount": {"factory": null, "amount": 1, "cent": 100, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 1}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 100, "currencyCode": "USD"}}], "spreadCode": null, "promotionSource": "CHECKOUT", "productCode": "shopCoupon", "promotionRole": "SELLER", "doesBundle": false, "doesMaster": null, "relationId": null, "class": "com.alibaba.global.order.management.api.model.PromotionOrderDTO", "promotionId": "1000000047688388"}, {"rebatesPercentage": null, "productSubCode": null, "priceTypes": [{"priceType": 0, "class": "com.alibaba.global.order.management.api.model.PromotionPriceTypeDTO", "monetaryAmount": {"factory": null, "amount": 1, "cent": 100, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 1}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 100, "currencyCode": "USD"}}], "spreadCode": null, "promotionSource": "CHECKOUT", "productCode": "fixedDiscount", "promotionRole": "SELLER", "doesBundle": false, "doesMaster": null, "relationId": null, "class": "com.alibaba.global.order.management.api.model.PromotionOrderDTO", "promotionId": "5000004939102165"}, {"rebatesPercentage": null, "productSubCode": null, "priceTypes": [{"priceType": 0, "class": "com.alibaba.global.order.management.api.model.PromotionPriceTypeDTO", "monetaryAmount": {"factory": null, "amount": 13.83, "cent": 1383, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 13.83}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 1383, "currencyCode": "USD"}}], "spreadCode": null, "promotionSource": "CHECKOUT", "productCode": "freeShipping", "promotionRole": "SELLER", "doesBundle": false, "doesMaster": null, "relationId": null, "class": "com.alibaba.global.order.management.api.model.PromotionOrderDTO", "promotionId": "5000004940805715"}, {"rebatesPercentage": null, "productSubCode": null, "priceTypes": [{"priceType": 0, "class": "com.alibaba.global.order.management.api.model.PromotionPriceTypeDTO", "monetaryAmount": {"factory": null, "amount": 0.01, "cent": 1, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 0.01}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 1, "currencyCode": "USD"}}], "spreadCode": null, "promotionSource": "CHECKOUT", "productCode": "platformCoupon", "promotionRole": "PLATFORM", "doesBundle": false, "doesMaster": null, "relationId": null, "class": "com.alibaba.global.order.management.api.model.PromotionOrderDTO", "promotionId": "5000008836917001"}], "groupBy": null, "class": "com.alibaba.global.order.management.api.model.PromotionFeeInfoDTO", "promotionId": "5000008836917001"}, {"promotionSponsorDTOS": null, "doSplit": true, "currentPromotionOriginalInfo": {"rebatesPercentage": null, "productSubCode": null, "priceTypes": [{"priceType": 0, "class": "com.alibaba.global.order.management.api.model.PromotionPriceTypeDTO", "monetaryAmount": {"factory": null, "amount": 1, "cent": 100, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 1}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 100, "currencyCode": "USD"}}], "spreadCode": null, "promotionSource": "CHECKOUT", "productCode": "shopCoupon", "promotionRole": "SELLER", "doesBundle": false, "doesMaster": null, "relationId": null, "class": "com.alibaba.global.order.management.api.model.PromotionOrderDTO", "promotionId": "1000000047688388"}, "nonGoldStandardDiscountFee": {"factory": null, "amount": 0.86, "cent": 86, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 0.86}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "EUR", "defaultFractionDigits": 2, "numericCode": 978}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 86, "currencyCode": "EUR"}, "discountFee": {"factory": null, "amount": 0.86, "cent": 86, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 0.86}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "EUR", "defaultFractionDigits": 2, "numericCode": 978}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 86, "currencyCode": "EUR"}, "goldStandardDiscountFee": {"factory": null, "amount": 0, "cent": 0, "positive": false, "positiveOrZero": true, "zero": true, "number": {"number": 0}, "negative": false, "negativeOrZero": true, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "EUR", "defaultFractionDigits": 2, "numericCode": 978}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 0, "currencyCode": "EUR"}, "promotionOriginalInfo": [{"rebatesPercentage": null, "productSubCode": null, "priceTypes": [{"priceType": 0, "class": "com.alibaba.global.order.management.api.model.PromotionPriceTypeDTO", "monetaryAmount": {"factory": null, "amount": 1, "cent": 100, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 1}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 100, "currencyCode": "USD"}}], "spreadCode": null, "promotionSource": "CHECKOUT", "productCode": "shopCoupon", "promotionRole": "SELLER", "doesBundle": false, "doesMaster": null, "relationId": null, "class": "com.alibaba.global.order.management.api.model.PromotionOrderDTO", "promotionId": "1000000047688388"}, {"rebatesPercentage": null, "productSubCode": null, "priceTypes": [{"priceType": 0, "class": "com.alibaba.global.order.management.api.model.PromotionPriceTypeDTO", "monetaryAmount": {"factory": null, "amount": 1, "cent": 100, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 1}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 100, "currencyCode": "USD"}}], "spreadCode": null, "promotionSource": "CHECKOUT", "productCode": "fixedDiscount", "promotionRole": "SELLER", "doesBundle": false, "doesMaster": null, "relationId": null, "class": "com.alibaba.global.order.management.api.model.PromotionOrderDTO", "promotionId": "5000004939102165"}, {"rebatesPercentage": null, "productSubCode": null, "priceTypes": [{"priceType": 0, "class": "com.alibaba.global.order.management.api.model.PromotionPriceTypeDTO", "monetaryAmount": {"factory": null, "amount": 13.83, "cent": 1383, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 13.83}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 1383, "currencyCode": "USD"}}], "spreadCode": null, "promotionSource": "CHECKOUT", "productCode": "freeShipping", "promotionRole": "SELLER", "doesBundle": false, "doesMaster": null, "relationId": null, "class": "com.alibaba.global.order.management.api.model.PromotionOrderDTO", "promotionId": "5000004940805715"}, {"rebatesPercentage": null, "productSubCode": null, "priceTypes": [{"priceType": 0, "class": "com.alibaba.global.order.management.api.model.PromotionPriceTypeDTO", "monetaryAmount": {"factory": null, "amount": 0.01, "cent": 1, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 0.01}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 1, "currencyCode": "USD"}}], "spreadCode": null, "promotionSource": "CHECKOUT", "productCode": "platformCoupon", "promotionRole": "PLATFORM", "doesBundle": false, "doesMaster": null, "relationId": null, "class": "com.alibaba.global.order.management.api.model.PromotionOrderDTO", "promotionId": "5000008836917001"}], "groupBy": null, "class": "com.alibaba.global.order.management.api.model.PromotionFeeInfoDTO", "promotionId": "1000000047688388"}, {"promotionSponsorDTOS": null, "doSplit": true, "currentPromotionOriginalInfo": {"rebatesPercentage": null, "productSubCode": null, "priceTypes": [{"priceType": 0, "class": "com.alibaba.global.order.management.api.model.PromotionPriceTypeDTO", "monetaryAmount": {"factory": null, "amount": 1, "cent": 100, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 1}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 100, "currencyCode": "USD"}}], "spreadCode": null, "promotionSource": "CHECKOUT", "productCode": "fixedDiscount", "promotionRole": "SELLER", "doesBundle": false, "doesMaster": null, "relationId": null, "class": "com.alibaba.global.order.management.api.model.PromotionOrderDTO", "promotionId": "5000004939102165"}, "nonGoldStandardDiscountFee": {"factory": null, "amount": 0.86, "cent": 86, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 0.86}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "EUR", "defaultFractionDigits": 2, "numericCode": 978}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 86, "currencyCode": "EUR"}, "discountFee": {"factory": null, "amount": 0.86, "cent": 86, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 0.86}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "EUR", "defaultFractionDigits": 2, "numericCode": 978}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 86, "currencyCode": "EUR"}, "goldStandardDiscountFee": {"factory": null, "amount": 0, "cent": 0, "positive": false, "positiveOrZero": true, "zero": true, "number": {"number": 0}, "negative": false, "negativeOrZero": true, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "EUR", "defaultFractionDigits": 2, "numericCode": 978}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 0, "currencyCode": "EUR"}, "promotionOriginalInfo": [{"rebatesPercentage": null, "productSubCode": null, "priceTypes": [{"priceType": 0, "class": "com.alibaba.global.order.management.api.model.PromotionPriceTypeDTO", "monetaryAmount": {"factory": null, "amount": 1, "cent": 100, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 1}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 100, "currencyCode": "USD"}}], "spreadCode": null, "promotionSource": "CHECKOUT", "productCode": "shopCoupon", "promotionRole": "SELLER", "doesBundle": false, "doesMaster": null, "relationId": null, "class": "com.alibaba.global.order.management.api.model.PromotionOrderDTO", "promotionId": "1000000047688388"}, {"rebatesPercentage": null, "productSubCode": null, "priceTypes": [{"priceType": 0, "class": "com.alibaba.global.order.management.api.model.PromotionPriceTypeDTO", "monetaryAmount": {"factory": null, "amount": 1, "cent": 100, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 1}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 100, "currencyCode": "USD"}}], "spreadCode": null, "promotionSource": "CHECKOUT", "productCode": "fixedDiscount", "promotionRole": "SELLER", "doesBundle": false, "doesMaster": null, "relationId": null, "class": "com.alibaba.global.order.management.api.model.PromotionOrderDTO", "promotionId": "5000004939102165"}, {"rebatesPercentage": null, "productSubCode": null, "priceTypes": [{"priceType": 0, "class": "com.alibaba.global.order.management.api.model.PromotionPriceTypeDTO", "monetaryAmount": {"factory": null, "amount": 13.83, "cent": 1383, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 13.83}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 1383, "currencyCode": "USD"}}], "spreadCode": null, "promotionSource": "CHECKOUT", "productCode": "freeShipping", "promotionRole": "SELLER", "doesBundle": false, "doesMaster": null, "relationId": null, "class": "com.alibaba.global.order.management.api.model.PromotionOrderDTO", "promotionId": "5000004940805715"}, {"rebatesPercentage": null, "productSubCode": null, "priceTypes": [{"priceType": 0, "class": "com.alibaba.global.order.management.api.model.PromotionPriceTypeDTO", "monetaryAmount": {"factory": null, "amount": 0.01, "cent": 1, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 0.01}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 1, "currencyCode": "USD"}}], "spreadCode": null, "promotionSource": "CHECKOUT", "productCode": "platformCoupon", "promotionRole": "PLATFORM", "doesBundle": false, "doesMaster": null, "relationId": null, "class": "com.alibaba.global.order.management.api.model.PromotionOrderDTO", "promotionId": "5000008836917001"}], "groupBy": null, "class": "com.alibaba.global.order.management.api.model.PromotionFeeInfoDTO", "promotionId": "5000004939102165"}], "reverseShippingFee": null, "leadingTimeDTO": {"shippingProviderCode": "CAINIAO_STANDARD", "fulfillmentFinishTime": null, "guaranteeTagIcon": null, "providerGps": null, "logisticCategory": null, "guaranteeCutoffTime": null, "shippingProviderName": "AliExpress Standard Shipping", "class": "com.alibaba.global.order.management.api.model.LeadingTimeDTO", "fulfillmentPriority": null, "buyerGps": null, "deliveryPriority": null}, "stepBarDTO": null, "logisticsOrderDTO": {"transportMethodDTO": {"supportCod": false, "deliverySlot": null, "deliveryType": "CAINIAO_STANDARD", "leadingTime": {"shippingProviderCode": "CAINIAO_STANDARD", "fulfillmentFinishTime": null, "guaranteeTagIcon": null, "providerGps": null, "logisticCategory": null, "guaranteeCutoffTime": null, "shippingProviderName": "AliExpress Standard Shipping", "class": "com.alibaba.global.order.management.api.model.LeadingTimeDTO", "fulfillmentPriority": null, "buyerGps": null, "deliveryPriority": null}, "class": "com.alibaba.global.order.management.api.model.TransportMethodDTO"}, "transportMethodLineDTO": {"supportCod": false, "deliveryType": "CAINIAO_STANDARD", "leadingTime": {"shippingProviderCode": "CAINIAO_STANDARD", "fulfillmentFinishTime": null, "guaranteeTagIcon": null, "providerGps": null, "logisticCategory": null, "guaranteeCutoffTime": null, "shippingProviderName": "AliExpress Standard Shipping", "class": "com.alibaba.global.order.management.api.model.LeadingTimeDTO", "fulfillmentPriority": null, "buyerGps": null, "deliveryPriority": null}, "class": "com.alibaba.global.order.management.api.model.TransportMethodLineDTO", "tradeOrderLineId": 1000177500150182}, "class": "com.alibaba.global.order.management.api.model.LogicDeliveryOrderDTO", "logicDeliveryId": "A9ef848ea-6cb3-43db-92ac-9649a15826dc1000177500140182", "amendable": null}, "bizCode": "ali.global.ae.general", "tradeOrderId": 1000177500140182, "unitFee": {"factory": null, "amount": 49, "cent": 4900, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 49}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 4900, "currencyCode": "USD"}, "memo": null, "reverseStatus": null, "features": {"featureMap": {"_dp_pcpdf": "[{\"promotionId\":\"5000008836917001\",\"discountFee\":{\"currencyCode\":\"EUR\",\"amount\":1},\"goldStandardDiscountFee\":{\"currencyCode\":\"EUR\",\"amount\":1}},{\"promotionId\":\"1000000047688388\",\"discountFee\":{\"currencyCode\":\"EUR\",\"amount\":86},\"goldStandardDiscountFee\":{\"currencyCode\":\"EUR\",\"amount\":0}},{\"promotionId\":\"5000004939102165\",\"discountFee\":{\"currencyCode\":\"EUR\",\"amount\":86},\"goldStandardDiscountFee\":{\"currencyCode\":\"EUR\",\"amount\":0}},{\"promotionId\":\"5000004940805715\",\"discountFee\":{\"currencyCode\":\"EUR\",\"amount\":1183},\"goldStandardDiscountFee\":{\"currencyCode\":\"EUR\",\"amount\":0}}]", "intent_pay_rate": "0.85560000", "sendGoodsCountryCode": "CN", "buyerEmail": "<EMAIL>", "gst": "true", "_sku_tag": "[70721,70722,82052,84809]", "productUnit": "piece", "cActivity": "WaitPaySuccessMsgWaitTask", "lipcc": "USD", "lip": "4900", "protocol": "1", "invt": "1000", "tml": "{\"tradeOrderLineId\":1000177500150182,\"leadingTime\":{\"shippingProviderCode\":\"CAINIAO_STANDARD\",\"shippingProviderName\":\"AliExpress Standard Shipping\",\"deliveryMinTime\":**********.872000000,\"deliveryMaxTime\":**********.872000000,\"deliveryDate\":\"2021-03-03\",\"freightCommitDay\":75,\"platformWarehousePromiseTime\":**********.872000000,\"featuresForTrade\":\"\"},\"deliveryType\":\"CAINIAO_STANDARD\",\"deliveryName\":\"CAINIAO_STANDARD\",\"solutionCode\":\"CAINIAO_STANDARD\",\"supportCod\":false}", "exchangeRate": "0.85560000", "dre": "CAINIAO_STANDARD", "goodsPrepareTime": "3", "sacc": "CN", "height": "11", "promiseTemplate": "[2,{\"id\":11,\"valueMaps\":{}}]", "new": "1", "workflow": "bpm.workflow.AE_GLOBAL___AEGeneralFulfillment", "sellerPhone": "81819494949", "sfts": "{\"inv_code\":\"5:4181;14:771\",\"sku_bulk_discount\":\"10\",\"sku_bulk_order\":\"5\"}", "guaranteedDeliveryTime": "75", "freightCommitDay": "75", "weight": "1.000", "rdgt": "1468800", "fullfillmentMode": "other", "sellerEmail": "<EMAIL>", "toc_p": "1728000", "adt": "HOME", "wtn": "ae_marketplace", "siteLanguage": "es_ES", "ads": "unpaid", "suaId": "109122473", "sda": "ke yuan nan lu 3398 hao", "intent_pay_cur": "EUR", "orderSearchStatus": "1", "pbi": "201512802", "isRetail": "false", "sbpm": "[]", "sellerType": "1", "saId": "440305007", "pft": "USD 0.00", "in_st": "0^1_1^58563290091_2^5000000000137141270", "deliveryTime": "14-18", "dop": "CAINIAO_STANDARD", "_dynamic_price": "{\"originalFee\":{\"amount\":50.00,\"currency\":\"USD\"},\"discountFee\":{\"currency\":\"USD\",\"itemSingleDiscountFee\":{\"currencyCode\":\"USD\",\"amount\":0},\"itemGoldStandardFee\":{\"currencyCode\":\"USD\",\"amount\":1},\"itemNonGoldStandardFee\":{\"currencyCode\":\"USD\",\"amount\":200},\"shippingGoldStandardFee\":{\"currencyCode\":\"USD\",\"amount\":0},\"shippingNonGoldStandardFee\":{\"currencyCode\":\"USD\",\"amount\":1383}},\"payCurrDiscountFee\":{\"currency\":\"EUR\",\"itemSingleDiscountFee\":{\"currencyCode\":\"EUR\",\"amount\":0},\"itemGoldStandardFee\":{\"currencyCode\":\"EUR\",\"amount\":1},\"itemNonGoldStandardFee\":{\"currencyCode\":\"EUR\",\"amount\":172},\"shippingGoldStandardFee\":{\"currencyCode\":\"EUR\",\"amount\":0},\"shippingNonGoldStandardFee\":{\"currencyCode\":\"EUR\",\"amount\":1183}},\"adjustFee\":{\"amount\":0.00,\"currency\":\"USD\"},\"shippingDiscountFee\":{\"amount\":13.83,\"currency\":\"USD\"},\"payableFee\":{\"amount\":47.99,\"currency\":\"USD\"},\"actualFee\":{\"amount\":47.99,\"currency\":\"USD\"},\"promotionFeeDetails\":[{\"promotionId\":\"5000008836917001\",\"promotionCode\":\"platformCoupon\",\"targetType\":2,\"type\":0,\"discountFee\":{\"currencyCode\":\"USD\",\"amount\":1},\"goldStandardDiscountFee\":{\"currencyCode\":\"USD\",\"amount\":1},\"nonGoldStandardDiscountFee\":{\"currencyCode\":\"USD\",\"amount\":0},\"source\":0},{\"promotionId\":\"1000000047688388\",\"promotionCode\":\"shopCoupon\",\"targetType\":1,\"type\":0,\"discountFee\":{\"currencyCode\":\"USD\",\"amount\":100},\"goldStandardDiscountFee\":{\"currencyCode\":\"USD\",\"amount\":0},\"nonGoldStandardDiscountFee\":{\"currencyCode\":\"USD\",\"amount\":100},\"source\":0},{\"promotionId\":\"5000004939102165\",\"promotionCode\":\"fixedDiscount\",\"targetType\":1,\"type\":0,\"discountFee\":{\"currencyCode\":\"USD\",\"amount\":100},\"goldStandardDiscountFee\":{\"currencyCode\":\"USD\",\"amount\":0},\"nonGoldStandardDiscountFee\":{\"currencyCode\":\"USD\",\"amount\":100},\"source\":0},{\"promotionId\":\"5000004940805715\",\"promotionCode\":\"freeShipping\",\"targetType\":1,\"type\":1,\"discountFee\":{\"currencyCode\":\"USD\",\"amount\":1383},\"goldStandardDiscountFee\":{\"currencyCode\":\"USD\",\"amount\":0},\"nonGoldStandardDiscountFee\":{\"currencyCode\":\"USD\",\"amount\":1383},\"source\":0}],\"extra\":{}}", "e_r_oid": "20210205C145006742", "_sku_feature": "{\"inv_code\":\"5:4181;14:771\",\"sku_bulk_discount\":\"10\",\"sku_bulk_order\":\"5\"}", "pcpdf": "[{\"promotionId\":\"5000008836917001\",\"discountFee\":{\"currencyCode\":\"EUR\",\"amount\":1},\"goldStandardDiscountFee\":{\"currencyCode\":\"EUR\",\"amount\":1}},{\"promotionId\":\"1000000047688388\",\"discountFee\":{\"currencyCode\":\"EUR\",\"amount\":86},\"goldStandardDiscountFee\":{\"currencyCode\":\"EUR\",\"amount\":0}},{\"promotionId\":\"5000004939102165\",\"discountFee\":{\"currencyCode\":\"EUR\",\"amount\":86},\"goldStandardDiscountFee\":{\"currencyCode\":\"EUR\",\"amount\":0}}]", "s_s_c": "32323883025_58563290091", "sku_custom_attr": "|||\u001f\b\u0000\u0000\u0000\u0000\u0000\u0000\u0000Ë+ÍÉ\u0001\u0000OüË%\u0004\u0000\u0000\u0000|||", "wc": "ae_marketplace", "bp": "*********", "assid": "*********", "invtn": "REAL", "d_p": "1", "proRetailPrice": "USD 49", "oip": "4900", "opicc": "USD", "t_c": "default", "tci": "*********", "deliveryDate": "2021-03-03", "checkOutBizCode": "ali.global.ae.trade.general", "wt": "ae_marketplace", "_product_feature": "{\"company_id\":\"*********\",\"creator_nick\":\"cn117358299\",\"creator_uid\":\"*********\",\"d_p\":\"1\",\"delivery_time\":\"3\",\"detail_type\":\"text\",\"down_shelf_time\":\"1604042544047\",\"fullfillmentMode\":\"other\",\"gpfSource\":\"common-market-100\",\"groupBuyId\":null,\"increment_migration\":\"1559763648870\",\"newSizeChart\":\"true\",\"off_time\":\"1505893856000\",\"owner_id\":\"*********\",\"owner_nick\":\"cn1517744312rpzt\",\"prd_type\":\"dynamic\",\"publish_biz_code\":\"ali.ae.general\",\"publish_by_new_service\":\"*************\",\"sku_custom_attr\":\"null\",\"source_app\":\"global-product-migration-d\",\"src\":\"post\",\"translate_flag\":\"00\",\"v_p\":\"USD 5E+1\"}", "s_sp_c": "32323883025_AE-58563290091", "in_rs": "place_order_withhold", "t_p": "0.0", "v_p": "USD 5E+1", "ppf": "{\"currencyCode\":\"USD\",\"amount\":0}", "lotNum": "1", "length": "11", "flagship": "false", "e_r_id": "3c0ffd60-9475-49d4-b1a0-f5d58a434aae", "gt": "USD 0.00", "svr": "true", "toc_ship": "1468800", "sfa": "ke yuan nan lu 3398 hao", "bppc": "33", "pdf": "{\"currency\":\"EUR\",\"itemSingleDiscountFee\":{\"currencyCode\":\"EUR\",\"amount\":0},\"itemGoldStandardFee\":{\"currencyCode\":\"EUR\",\"amount\":1},\"itemNonGoldStandardFee\":{\"currencyCode\":\"EUR\",\"amount\":172},\"shippingGoldStandardFee\":{\"currencyCode\":\"EUR\",\"amount\":0},\"shippingNonGoldStandardFee\":{\"currencyCode\":\"EUR\",\"amount\":1183}}", "toc_all": "[{\"type\":1,\"initSetSec\":1728000,\"remindSecs\":[259200,86400]},{\"type\":2,\"initSetSec\":1468800,\"remindSecs\":[]}]", "width": "11", "bul": "0", "cashierBefore": "true", "exchangeCurrency": "EUR", "adtg": "local", "bid": "201512802", "logisticsType": "CAINIAO_STANDARD", "in_i": "{}"}, "notEmpty": true, "class": "com.alibaba.global.order.management.api.model.Features", "empty": false}, "ascStatus": "unpaid", "originalOrderLineNumber": 1000177500150182, "reversePolicyId": null, "shippingActualFee": {"factory": null, "amount": 0, "cent": 0, "positive": false, "positiveOrZero": true, "zero": true, "number": {"number": 0}, "negative": false, "negativeOrZero": true, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 0, "currencyCode": "USD"}, "actualFee": {"factory": null, "amount": 46.99, "cent": 4699, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 46.99}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 4699, "currencyCode": "USD"}, "adjustFee": {"factory": null, "amount": 0, "cent": 0, "positive": false, "positiveOrZero": true, "zero": true, "number": {"number": 0}, "negative": false, "negativeOrZero": true, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 0, "currencyCode": "USD"}, "reverseFee": null, "marketplace": false, "endReason": null, "couponInfo": null, "deliveryEndTime": null, "orderClassify": null, "taxActualFee": {"factory": null, "amount": 0, "cent": 0, "positive": false, "positiveOrZero": true, "zero": true, "number": {"number": 0}, "negative": false, "negativeOrZero": true, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 0, "currencyCode": "USD"}, "outDeliveryId": "A9ef848ea-6cb3-43db-92ac-9649a15826dc1000177500140182", "paidTime": null, "taxCode": "default", "warrantyDTO": null, "buyer": {"buyerPhone": "*********", "userLevel": 0, "buyerEmail": "<EMAIL>", "buyerPhonePrefixCode": "33", "buyerIcon": null, "buyerId": **********, "class": "com.alibaba.global.order.management.api.model.BuyerInfoDTO", "buyerFullName": "hz four"}, "shippingDiscountInfo": [{"promotionSponsorDTOS": null, "doSplit": false, "currentPromotionOriginalInfo": {"rebatesPercentage": null, "productSubCode": null, "priceTypes": [{"priceType": 0, "class": "com.alibaba.global.order.management.api.model.PromotionPriceTypeDTO", "monetaryAmount": {"factory": null, "amount": 13.83, "cent": 1383, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 13.83}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 1383, "currencyCode": "USD"}}], "spreadCode": null, "promotionSource": "CHECKOUT", "productCode": "freeShipping", "promotionRole": "SELLER", "doesBundle": false, "doesMaster": null, "relationId": null, "class": "com.alibaba.global.order.management.api.model.PromotionOrderDTO", "promotionId": "5000004940805715"}, "nonGoldStandardDiscountFee": {"factory": null, "amount": 11.83, "cent": 1183, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 11.83}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "EUR", "defaultFractionDigits": 2, "numericCode": 978}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 1183, "currencyCode": "EUR"}, "discountFee": {"factory": null, "amount": 11.83, "cent": 1183, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 11.83}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "EUR", "defaultFractionDigits": 2, "numericCode": 978}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 1183, "currencyCode": "EUR"}, "goldStandardDiscountFee": {"factory": null, "amount": 0, "cent": 0, "positive": false, "positiveOrZero": true, "zero": true, "number": {"number": 0}, "negative": false, "negativeOrZero": true, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "EUR", "defaultFractionDigits": 2, "numericCode": 978}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 0, "currencyCode": "EUR"}, "promotionOriginalInfo": [{"rebatesPercentage": null, "productSubCode": null, "priceTypes": [{"priceType": 0, "class": "com.alibaba.global.order.management.api.model.PromotionPriceTypeDTO", "monetaryAmount": {"factory": null, "amount": 1, "cent": 100, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 1}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 100, "currencyCode": "USD"}}], "spreadCode": null, "promotionSource": "CHECKOUT", "productCode": "shopCoupon", "promotionRole": "SELLER", "doesBundle": false, "doesMaster": null, "relationId": null, "class": "com.alibaba.global.order.management.api.model.PromotionOrderDTO", "promotionId": "1000000047688388"}, {"rebatesPercentage": null, "productSubCode": null, "priceTypes": [{"priceType": 0, "class": "com.alibaba.global.order.management.api.model.PromotionPriceTypeDTO", "monetaryAmount": {"factory": null, "amount": 1, "cent": 100, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 1}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 100, "currencyCode": "USD"}}], "spreadCode": null, "promotionSource": "CHECKOUT", "productCode": "fixedDiscount", "promotionRole": "SELLER", "doesBundle": false, "doesMaster": null, "relationId": null, "class": "com.alibaba.global.order.management.api.model.PromotionOrderDTO", "promotionId": "5000004939102165"}, {"rebatesPercentage": null, "productSubCode": null, "priceTypes": [{"priceType": 0, "class": "com.alibaba.global.order.management.api.model.PromotionPriceTypeDTO", "monetaryAmount": {"factory": null, "amount": 13.83, "cent": 1383, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 13.83}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 1383, "currencyCode": "USD"}}], "spreadCode": null, "promotionSource": "CHECKOUT", "productCode": "freeShipping", "promotionRole": "SELLER", "doesBundle": false, "doesMaster": null, "relationId": null, "class": "com.alibaba.global.order.management.api.model.PromotionOrderDTO", "promotionId": "5000004940805715"}, {"rebatesPercentage": null, "productSubCode": null, "priceTypes": [{"priceType": 0, "class": "com.alibaba.global.order.management.api.model.PromotionPriceTypeDTO", "monetaryAmount": {"factory": null, "amount": 0.01, "cent": 1, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 0.01}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 1, "currencyCode": "USD"}}], "spreadCode": null, "promotionSource": "CHECKOUT", "productCode": "platformCoupon", "promotionRole": "PLATFORM", "doesBundle": false, "doesMaster": null, "relationId": null, "class": "com.alibaba.global.order.management.api.model.PromotionOrderDTO", "promotionId": "5000008836917001"}], "groupBy": null, "class": "com.alibaba.global.order.management.api.model.PromotionFeeInfoDTO", "promotionId": "5000004940805715"}], "stageApportionInfos": null, "payableFee": {"factory": null, "amount": 46.99, "cent": 4699, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 46.99}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 4699, "currencyCode": "USD"}, "ascExtendInfoDTO": {"hasPrinted": null, "ascStatus": "unpaid", "paidCommission": null, "shippingSLA": null, "enable": false, "rtsSLA": null, "invoiceNumber": null, "class": "com.alibaba.global.order.management.api.model.AscExtendInfoDTO", "doesRetail": false, "inTransit": null}, "shippingFee": {"factory": null, "amount": 13.83, "cent": 1383, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 13.83}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 1383, "currencyCode": "USD"}, "actualFeeOfPurposeCurrency": {"factory": null, "amount": 40.2, "cent": 4020, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 40.2}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "EUR", "defaultFractionDigits": 2, "numericCode": 978}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 4020, "currencyCode": "EUR"}, "saleOriginalFee": {"factory": null, "amount": 49, "cent": 4900, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 49}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 4900, "currencyCode": "USD"}, "deliveryStatus": 0, "tradeOrderLineId": 1000177500150182, "reversePolicyFreeDays": null, "succeedTimeout": null, "bizTenant": 1, "deliveryDetailStatus": null, "exchangeInfo": {"quoteCurrency": "EUR", "exchangeRate": 0.8556, "class": "com.alibaba.global.order.management.api.model.ExchangeInfoDTO", "baseCurrency": "USD"}, "volumePrice": {"features": {"featureMap": {"d_p_product_g_fee": "USD 0.01", "d_p_ori_fee_pay_df": "EUR 0.86", "d_p_shipping_g_fee": "USD 0.00", "d_p_adj_sp_fee_pay_df": null, "d_p_product_g_fee_pay": "EUR 0.01", "d_p_shipping_ng_fee_pay_df": null, "d_p_shipping_ng_fee_df": null, "d_p_adj_sp_fee_df": null, "d_p_product_ng_fee_df": null, "d_p_adj_gd_fee_df": null, "d_p_ori_fee_df": "USD 1.00", "d_p_product_ng_fee_pay_df": null, "d_p_shipping_g_fee_pay": "EUR 0.00", "d_p_adj_gd_fee_pay_df": null}, "notEmpty": true, "class": "com.alibaba.global.order.management.api.model.Features", "empty": false}, "unitFee": {"factory": null, "amount": 50, "cent": 5000, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 50}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 5000, "currencyCode": "USD"}, "class": "com.alibaba.global.order.management.api.model.VolumePriceDTO", "oriUnitFee": {"factory": null, "amount": 49, "cent": 4900, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 49}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 4900, "currencyCode": "USD"}}, "orderStatus": 0, "rebateUsed": null, "paymentDiscountFee": {"factory": null, "amount": 0, "cent": 0, "positive": false, "positiveOrZero": true, "zero": true, "number": {"number": 0}, "negative": false, "negativeOrZero": true, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 0, "currencyCode": "USD"}, "outPayId": null, "saleDiscountFee": {"factory": null, "amount": 2.01, "cent": 201, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 2.01}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 201, "currencyCode": "USD"}, "crossBorder": false, "slotIncentiveFee": null, "taxFee": {"factory": null, "amount": 0, "cent": 0, "positive": false, "positiveOrZero": true, "zero": true, "number": {"number": 0}, "negative": false, "negativeOrZero": true, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 0, "currencyCode": "USD"}, "frozenType": 0, "class": "com.alibaba.global.order.management.api.model.TradeOrderLineDTO", "payOrderDTO": {"paymentPromotionFee": null, "productCode": null, "riskInfo": null, "channelParams": null, "channelText": null, "paymentCheckoutId": "20990591103210207792100880182", "paymentCostFee": null, "channel": null, "actualPaidFee": null, "class": "com.alibaba.global.order.management.api.model.PayOrderDTO"}, "dutyFee": null, "ofcStatus": null, "product": {"itemId": "32323883025", "itemPicUrl": "HTB1tP.JKpXXXXaKXFXXq6xXFXXXr.jpg", "topCategoryId": "*********", "itemTitle": "23", "itemUnit": "piece", "siteItemTitle": "23", "sku": {"length": "11", "weight": "1.000", "barCode": null, "sellerSkuCode": "32323883025_58563290091", "features": {"inv_code": "5:4181;14:771", "sku_bulk_discount": "10", "sku_bulk_order": "5"}, "width": "11", "shopSkuCode": "32323883025_AE-58563290091", "class": "com.alibaba.global.order.management.api.model.SkuDTO", "skuInfo": "[{\"image\":{\"imageUrl\":\"HTB1tP.JKpXXXXaKXFXXq6xXFXXXr.jpg\",\"type\":1},\"valueId\":771,\"valueText\":\"Beige\",\"propertyName\":\"Color\",\"isCustom\":true,\"style\":\"colour_atla\",\"propertyId\":14,\"order\":2},{\"valueId\":4181,\"valueText\":\"XXS\",\"propertyName\":\"Size\",\"isCustom\":true,\"style\":\"size\",\"propertyId\":5,\"order\":1}]", "skuId": "58563290091", "properties": null, "channelCode": null, "height": "11"}, "class": "com.alibaba.global.order.management.api.model.ProductDTO", "itemUrl": "https://www.aliexpress.com/item/32323883025.html?mp=1", "categoryId": "201240202"}, "timeoutDTOS": [{"initSetSec": 1728000, "remindSecs": [259200, 86400], "totalExtendSec": null, "type": 1, "class": "com.alibaba.global.order.management.api.model.TimeoutDTO", "gmtStart": null}, {"initSetSec": 1468800, "remindSecs": [], "totalExtendSec": null, "type": 2, "class": "com.alibaba.global.order.management.api.model.TimeoutDTO", "gmtStart": null}], "quantity": 1, "buyerDeleteStatus": 0, "snapshotInfo": {"snapshotSitePath": null, "snapshotPath": "H67df19e02e30466dbab661b178924a5c9.xml", "snapshotId": null, "snapshotSmallPhotoPath": "Hb6a8814af8ab4d76b2454a40343b3763A.jpg", "class": "com.alibaba.global.order.management.api.model.SnapshotDTO"}, "taxPercentage": "0.0", "taxRebateFee": {"factory": null, "amount": 0, "cent": 0, "positive": false, "positiveOrZero": true, "zero": true, "number": {"number": 0}, "negative": false, "negativeOrZero": true, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 0, "currencyCode": "USD"}, "originalShippingDiscountInfo": [{"promotionSponsorDTOS": null, "doSplit": false, "currentPromotionOriginalInfo": {"rebatesPercentage": null, "productSubCode": null, "priceTypes": [{"priceType": 0, "class": "com.alibaba.global.order.management.api.model.PromotionPriceTypeDTO", "monetaryAmount": {"factory": null, "amount": 13.83, "cent": 1383, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 13.83}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 1383, "currencyCode": "USD"}}], "spreadCode": null, "promotionSource": "CHECKOUT", "productCode": "freeShipping", "promotionRole": "SELLER", "doesBundle": false, "doesMaster": null, "relationId": null, "class": "com.alibaba.global.order.management.api.model.PromotionOrderDTO", "promotionId": "5000004940805715"}, "nonGoldStandardDiscountFee": {"factory": null, "amount": 13.83, "cent": 1383, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 13.83}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 1383, "currencyCode": "USD"}, "discountFee": {"factory": null, "amount": 13.83, "cent": 1383, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 13.83}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 1383, "currencyCode": "USD"}, "goldStandardDiscountFee": {"factory": null, "amount": 0, "cent": 0, "positive": false, "positiveOrZero": true, "zero": true, "number": {"number": 0}, "negative": false, "negativeOrZero": true, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 0, "currencyCode": "USD"}, "promotionOriginalInfo": [{"rebatesPercentage": null, "productSubCode": null, "priceTypes": [{"priceType": 0, "class": "com.alibaba.global.order.management.api.model.PromotionPriceTypeDTO", "monetaryAmount": {"factory": null, "amount": 1, "cent": 100, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 1}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 100, "currencyCode": "USD"}}], "spreadCode": null, "promotionSource": "CHECKOUT", "productCode": "shopCoupon", "promotionRole": "SELLER", "doesBundle": false, "doesMaster": null, "relationId": null, "class": "com.alibaba.global.order.management.api.model.PromotionOrderDTO", "promotionId": "1000000047688388"}, {"rebatesPercentage": null, "productSubCode": null, "priceTypes": [{"priceType": 0, "class": "com.alibaba.global.order.management.api.model.PromotionPriceTypeDTO", "monetaryAmount": {"factory": null, "amount": 1, "cent": 100, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 1}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 100, "currencyCode": "USD"}}], "spreadCode": null, "promotionSource": "CHECKOUT", "productCode": "fixedDiscount", "promotionRole": "SELLER", "doesBundle": false, "doesMaster": null, "relationId": null, "class": "com.alibaba.global.order.management.api.model.PromotionOrderDTO", "promotionId": "5000004939102165"}, {"rebatesPercentage": null, "productSubCode": null, "priceTypes": [{"priceType": 0, "class": "com.alibaba.global.order.management.api.model.PromotionPriceTypeDTO", "monetaryAmount": {"factory": null, "amount": 13.83, "cent": 1383, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 13.83}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 1383, "currencyCode": "USD"}}], "spreadCode": null, "promotionSource": "CHECKOUT", "productCode": "freeShipping", "promotionRole": "SELLER", "doesBundle": false, "doesMaster": null, "relationId": null, "class": "com.alibaba.global.order.management.api.model.PromotionOrderDTO", "promotionId": "5000004940805715"}, {"rebatesPercentage": null, "productSubCode": null, "priceTypes": [{"priceType": 0, "class": "com.alibaba.global.order.management.api.model.PromotionPriceTypeDTO", "monetaryAmount": {"factory": null, "amount": 0.01, "cent": 1, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 0.01}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 1, "currencyCode": "USD"}}], "spreadCode": null, "promotionSource": "CHECKOUT", "productCode": "platformCoupon", "promotionRole": "PLATFORM", "doesBundle": false, "doesMaster": null, "relationId": null, "class": "com.alibaba.global.order.management.api.model.PromotionOrderDTO", "promotionId": "5000008836917001"}], "groupBy": null, "class": "com.alibaba.global.order.management.api.model.PromotionFeeInfoDTO", "promotionId": "5000004940805715"}], "promotionOrderDTO": [{"rebatesPercentage": null, "productSubCode": null, "priceTypes": [{"priceType": 0, "class": "com.alibaba.global.order.management.api.model.PromotionPriceTypeDTO", "monetaryAmount": {"factory": null, "amount": 1, "cent": 100, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 1}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 100, "currencyCode": "USD"}}], "spreadCode": null, "promotionSource": "CHECKOUT", "productCode": "shopCoupon", "promotionRole": "SELLER", "doesBundle": false, "doesMaster": null, "relationId": null, "class": "com.alibaba.global.order.management.api.model.PromotionOrderDTO", "promotionId": "1000000047688388"}, {"rebatesPercentage": null, "productSubCode": null, "priceTypes": [{"priceType": 0, "class": "com.alibaba.global.order.management.api.model.PromotionPriceTypeDTO", "monetaryAmount": {"factory": null, "amount": 1, "cent": 100, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 1}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 100, "currencyCode": "USD"}}], "spreadCode": null, "promotionSource": "CHECKOUT", "productCode": "fixedDiscount", "promotionRole": "SELLER", "doesBundle": false, "doesMaster": null, "relationId": null, "class": "com.alibaba.global.order.management.api.model.PromotionOrderDTO", "promotionId": "5000004939102165"}, {"rebatesPercentage": null, "productSubCode": null, "priceTypes": [{"priceType": 0, "class": "com.alibaba.global.order.management.api.model.PromotionPriceTypeDTO", "monetaryAmount": {"factory": null, "amount": 13.83, "cent": 1383, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 13.83}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 1383, "currencyCode": "USD"}}], "spreadCode": null, "promotionSource": "CHECKOUT", "productCode": "freeShipping", "promotionRole": "SELLER", "doesBundle": false, "doesMaster": null, "relationId": null, "class": "com.alibaba.global.order.management.api.model.PromotionOrderDTO", "promotionId": "5000004940805715"}, {"rebatesPercentage": null, "productSubCode": null, "priceTypes": [{"priceType": 0, "class": "com.alibaba.global.order.management.api.model.PromotionPriceTypeDTO", "monetaryAmount": {"factory": null, "amount": 0.01, "cent": 1, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 0.01}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 1, "currencyCode": "USD"}}], "spreadCode": null, "promotionSource": "CHECKOUT", "productCode": "platformCoupon", "promotionRole": "PLATFORM", "doesBundle": false, "doesMaster": null, "relationId": null, "class": "com.alibaba.global.order.management.api.model.PromotionOrderDTO", "promotionId": "5000008836917001"}], "sellerDeleteStatus": 0, "redmart": false, "productCode": null, "shippingSurchargeFee": null, "siteId": "GLOBAL", "endTime": null, "payStatus": 1, "wareHouseDTO": {"wareHouseCode": "ae_marketplace", "inventoryType": 1000, "estimateTime": null, "marketplace": false, "wareHouseType": "ae_marketplace", "locationTreeAddressId": null, "shippingType": "ae_marketplace", "fullAddress": null, "detailAddress": null, "class": "com.alibaba.global.order.management.api.model.WareHouseDTO"}}], "otherOrderInfo": ";dp_pminfo:[{\"promotionId\"#3A\"5000004939102165\",\"productCode\"#3A\"fixedDiscount\",\"promotionRole\"#3A0,\"promotionTargetType\"#3A0},{\"promotionId\"#3A\"5000004940805715\",\"productCode\"#3A\"freeShipping\",\"promotionRole\"#3A0,\"promotionTargetType\"#3A1},{\"promotionId\"#3A\"5000008836917001\",\"productCode\"#3A\"platformCoupon\",\"promotionRole\"#3A0,\"promotionTargetType\"#3A0},{\"promotionId\"#3A\"1000000047688388\",\"productCode\"#3A\"shopCoupon\",\"promotionRole\"#3A0,\"promotionTargetType\"#3A0}];pminfo:[{\"promotionId\"#3A\"5000004939102165\",\"productCode\"#3A\"fixedDiscount\",\"promotionPriceTypeSerializeDOs\"#3A[{\"priceType\"#3A0,\"amount\"#3A{\"currencyCode\"#3A\"USD\",\"amount\"#3A100}}],\"promotionRole\"#3A0,\"promotionTargetType\"#3A0,\"promotionSource\"#3A0},{\"promotionId\"#3A\"5000004940805715\",\"productCode\"#3A\"freeShipping\",\"promotionPriceTypeSerializeDOs\"#3A[{\"priceType\"#3A0,\"amount\"#3A{\"currencyCode\"#3A\"USD\",\"amount\"#3A1383}}],\"promotionRole\"#3A0,\"promotionTargetType\"#3A1,\"promotionSource\"#3A0},{\"promotionId\"#3A\"5000008836917001\",\"productCode\"#3A\"platformCoupon\",\"promotionPriceTypeSerializeDOs\"#3A[{\"priceType\"#3A0,\"amount\"#3A{\"currencyCode\"#3A\"USD\",\"amount\"#3A1}}],\"promotionRole\"#3A1,\"promotionTargetType\"#3A0,\"promotionSource\"#3A0},{\"promotionId\"#3A\"1000000047688388\",\"productCode\"#3A\"shopCoupon\",\"promotionPriceTypeSerializeDOs\"#3A[{\"priceType\"#3A0,\"amount\"#3A{\"currencyCode\"#3A\"USD\",\"amount\"#3A100}}],\"promotionRole\"#3A0,\"promotionTargetType\"#3A0,\"promotionSource\"#3A0}];", "saleDiscountFee": {"factory": null, "amount": 2.01, "cent": 201, "positive": true, "positiveOrZero": true, "zero": false, "number": {"number": 2.01}, "negative": false, "negativeOrZero": false, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 201, "currencyCode": "USD"}, "taxFee": {"factory": null, "amount": 0, "cent": 0, "positive": false, "positiveOrZero": true, "zero": true, "number": {"number": 0}, "negative": false, "negativeOrZero": true, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 0, "currencyCode": "USD"}, "promotionSnapshotId": "2000096120070182", "tradeOrderGroup": null, "class": "com.alibaba.global.order.management.api.model.TradeOrderDTO", "payOrderDTO": {"paymentPromotionFee": null, "productCode": "NORMAL_PAY", "riskInfo": null, "channelParams": null, "channelText": null, "paymentCheckoutId": "20990591103210207792100880182", "paymentCostFee": null, "channel": null, "actualPaidFee": null, "class": "com.alibaba.global.order.management.api.model.PayOrderDTO"}, "snapshotId": null, "internationalTaxCode": null, "taxRebateFee": {"factory": null, "amount": 0, "cent": 0, "positive": false, "positiveOrZero": true, "zero": true, "number": {"number": 0}, "negative": false, "negativeOrZero": true, "context": null, "currency": {"context": {"class": "javax.money.CurrencyContext", "providerName": "java.util.Currency", "empty": false}, "class": "org.javamoney.moneta.internal.JDKCurrencyAdapter", "currencyCode": "USD", "defaultFractionDigits": 2, "numericCode": 840}, "class": "com.alibaba.global.money.Money", "amountInMinorUnit": 0, "currencyCode": "USD"}, "groupMemberOrderIds": null, "promotionSpreadCode": null, "productCode": null, "isAmended": false, "localTaxCode": null, "fulfillTimeout": null, "shippingSurchargeFee": null, "cOD": false, "siteId": "GLOBAL", "billingAddress": null, "maxDeliveryStatus": 0, "endTime": null, "payStatus": 1}