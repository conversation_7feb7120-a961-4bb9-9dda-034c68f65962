package com.aliexpress.order.price;

import com.alibaba.ecommerce.module.Response;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.global.order.management.api.facade.OrderQueryForBuyerFacade;
import com.alibaba.global.order.management.api.model.TradeOrderDTO;
import com.alibaba.global.order.management.api.model.TradeOrderLineDTO;
import com.aliexpress.databank.price.TradeOrderAdjustViewService;
import com.aliexpress.databank.price.impl.TradeOrderAdjustViewServiceImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.javamoney.moneta.Money;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.*;
import java.math.BigDecimal;

import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
@SpringBootTest(classes = {AdjustPriceTest.class, TradeOrderAdjustViewServiceImpl.class,TradeOrderAdjustViewService.class})
public class AdjustPriceTest {

    @Mock
    private OrderQueryForBuyerFacade orderQueryForBuyerFacade;

    @Autowired
    @InjectMocks
    private TradeOrderAdjustViewServiceImpl tradeOrderAdjustViewServerImpl;

    @Before
    public void init(){
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void test() throws IOException {

        Response<TradeOrderDTO> response=buildTestData("{\"shippingDiscountFee\":{\"factory\":null,\"amount\":0,\"cent\":0,\"positive\":false,\"positiveOrZero\":true,\"zero\":true,\"number\":{\"number\":0},\"negative\":false,\"negativeOrZero\":true,\"context\":null,\"currency\":{\"context\":{\"class\":\"javax.money.CurrencyContext\",\"providerName\":\"java.util.Currency\",\"empty\":false},\"class\":\"org.javamoney.moneta.internal.JDKCurrencyAdapter\",\"currencyCode\":\"USD\",\"defaultFractionDigits\":2,\"numericCode\":840},\"class\":\"com.alibaba.global.money.Money\",\"amountInMinorUnit\":0,\"currencyCode\":\"USD\"},\"reverseShippingFee\":null,\"bizCode\":\"ali.global.ae.general\",\"tradeOrderId\":1000170750038218,\"amendableLogicDeliveryOrder\":null,\"driveInfoDTO\":{\"driveType\":\"wn\",\"wirelessType\":\"IOS\",\"entrance\":null,\"class\":\"com.alibaba.global.order.management.api.model.DriveInfoDTO\"},\"deliverStatus\":0,\"trial\":false,\"features\":{\"featureMap\":{\"di\":\"{\\\"dt\\\":\\\"wn\\\",\\\"wt\\\":\\\"IOS\\\"}\",\"buyerEmail\":\"<EMAIL>\",\"source\":\"{\\\"from\\\":\\\"BUY_NOW\\\",\\\"device\\\":{\\\"id\\\":\\\"x86_64\\\"},\\\"network\\\":{\\\"clientIp\\\":\\\"*************\\\"},\\\"system\\\":{\\\"ttid\\\":\\\"201200@Aliexpress_iphone_8.26.0\\\",\\\"operatorSystemType\\\":\\\"IOS\\\",\\\"platformType\\\":\\\"NATIVE\\\"},\\\"site\\\":{\\\"locale\\\":\\\"en_US\\\"},\\\"extraParams\\\":{\\\"AppKey\\\":\\\"21371581\\\",\\\"AppName\\\":null,\\\"AppVersion\\\":\\\"8.26.0\\\",\\\"DeviceBrand\\\":\\\"apple\\\",\\\"DeviceLanguage\\\":\\\"en_US\\\",\\\"DeviceModel\\\":\\\"iPhone\\\",\\\"DeviceTimeZone\\\":\\\"GMT+8\\\",\\\"DeviceType\\\":\\\"mobile\\\",\\\"Ip\\\":\\\"*************\\\",\\\"LoginTime\\\":\\\"**********423\\\",\\\"MOBILE_ALIPAY_UMID_TOKEN\\\":\\\"\\\",\\\"MOBILE_API_NAME\\\":\\\"\\\",\\\"MOBILE_API_VERSION\\\":null,\\\"MOBILE_MINI_WUA\\\":\\\"ARdT_GImBxuSEDnsCMGbAzV8hVOZDxL0wWv3fgzaOZme76Rz/EvC5/P1NIEH1BALFfCW0SLiVeqVrMH5EBR3Kgq+xoYS3P5sXOOyj2saZnllNcNUiGrLHGp9KEnoDfZMITbxECMqa5xkz1l7egf7Czg6L6VKJH2QA7EO4OsgMBA2tnwAI/rDKBp6CIPZSEKbBziDNgg2n6Ij3ncaAyCTUlJJ+2h8CB5fY0CIGPvA4xMUpIF7khRccMnqO/UcKcwOiQJvgXkFIpw9OCe+WYgPXiZKKCmmdO80TtMaEnlEgKYScDW1ukXi+fsfiASksMe8gxb73Wj924s9PtpzGOTVC5yQikCy9gL/CWmiWsrOQNPDs24tyU335Ue4qaH7KTJU2x+Po\\\",\\\"MOBILE_SERVER_ENV_RAW\\\":\\\"\\\",\\\"MOBILE_UMID_TOKEN\\\":\\\"27dLXnlLOl0FlzV3Zv0YuSXWuPneWezR\\\",\\\"Mac\\\":\\\"\\\",\\\"NetWorkType\\\":\\\"WIFI\\\",\\\"OsVersion\\\":\\\"14.2\\\",\\\"SessionToken\\\":\\\"a03bd93a-8990-45c4-ad71-198c827a0230_al_b_pre\\\",\\\"T_time\\\":\\\"**********326\\\",\\\"UmidToken\\\":\\\"27dLXnlLOl0FlzV3Zv0YuSXWuPneWezR\\\",\\\"UmidTokenType\\\":\\\"SECURITY_TOKEN\\\",\\\"Wua\\\":\\\"ARdT_MicRNoSjEFYDgXeTvsXu3J6rZTdiZLiwkr+/gXznvhcB3DGby6+ZSuZ2OxrFA4vNrMvx+PYm3CtyK1oWepbQ58NexkGtjM1fyYDq79icTeeDCbq2pNkMYsZ5xS00elxrbvy/+j0a9AjqJN8lqRzeUssPu90qqKbb3Dv3/B/4M1vgqU76thKzFNwFuFaAB/EEK0YnYYw2X/lJnDq/gyYLXee9N8q5eMXlx8Bq4pkApKalNhraFkCSJWsx0PTcycBtYwfbxiIX2gQUFZg4rtTjGpYandGq+1ZAFsgVZnUaus7N/neBjx94klGXv3YSo3SYun/D/INeIqjUUQxN3As4SaGsXM0JXnod0IrsRQzR1ZA1oZeBMrUk97sjHemystzN\\\",\\\"_adid\\\":null,\\\"_anony_id\\\":null,\\\"_cna\\\":null,\\\"_utdid\\\":\\\"X9BznTQh5FUDAFzcg8uGKiL/\\\",\\\"ali_apache_id\\\":null,\\\"application\\\":\\\"CHECKOUT\\\",\\\"cookieId\\\":null,\\\"deviceId\\\":\\\"X9BznTQh5FUDAFzcg8uGKiL/\\\",\\\"deviceScreenReso\\\":\\\"750x1334\\\",\\\"entrance\\\":null,\\\"ip\\\":\\\"*************\\\",\\\"page_id\\\":\\\"PlaceOrder_8448B331-D2A1-48CF-8908-D47A6A8A5C42\\\",\\\"phase\\\":\\\"CREATE\\\",\\\"realtimeUmidToken\\\":\\\"27dLXnlLOl0FlzV3Zv0YuSXWuPneWezR\\\",\\\"realtimeUmidTokenType\\\":null,\\\"sessionId\\\":null,\\\"source\\\":\\\"iPhone_8.26.0\\\",\\\"subPlatform\\\":null,\\\"umid\\\":null,\\\"utdid\\\":\\\"X9BznTQh5FUDAFzcg8uGKiL/\\\"}}\",\"deliveryAddressSnapshotId\":\"116520007\",\"sellerId\":\"*********\",\"shippingAddressType\":\"DOMESTIC_USER_ADDRESS\",\"sellerSignupTime\":\"1200617747000\",\"promotionSnapshotId\":\"2000094280808218\",\"machineType\":\"iOS.iPhone.App\",\"new\":\"1\",\"polaris\":\"1\",\"sri\":\"1000170750038218\",\"adp\":\"1\",\"toc_p\":\"1728000\",\"toc_ship\":\"2332800\",\"ck_s\":\"PASS\",\"ck_t\":\"1612338250.460000000\",\"afz\":\"false\",\"toc_all\":\"[{\\\"gmtStart\\\":1612338250873,\\\"type\\\":1,\\\"initSetSec\\\":1728000,\\\"remindSecs\\\":[259200,86400]},{\\\"type\\\":2,\\\"initSetSec\\\":2332800,\\\"remindSecs\\\":[]}]\",\"ctov\":\"1\",\"bul\":\"0\",\"plt\":\"1\",\"orderFrom\":\"BUY_NOW\",\"coi\":\"**********\",\"ro\":\"{\\\"br\\\":{\\\"st\\\":\\\"2020-11-17T18:12:25-08:00\\\",\\\"uc\\\":\\\"GLOBAL\\\",\\\"ut\\\":\\\"NORMAL\\\"}}\"},\"notEmpty\":true,\"class\":\"com.alibaba.global.order.management.api.model.Features\",\"empty\":false},\"freeze\":0,\"deliveryAddress\":{\"city\":\"Dortmund\",\"locationTreeAddressId\":\"907700900001000000-907700900001002000\",\"postCodeType\":null,\"latitude\":null,\"companyName\":null,\"userAddressType\":null,\"countryId\":0,\"thirdLevelAddressName\":null,\"addressId\":1300460004,\"features\":{\"featureMap\":{},\"notEmpty\":false,\"class\":\"com.alibaba.global.order.management.api.model.Features\",\"empty\":true},\"countryCode\":\"DE\",\"state\":\"Arnsberg\",\"class\":\"com.alibaba.global.order.management.api.model.AddressInfoDTO\",\"longitude\":null,\"receiver\":{\"passportNo\":null,\"encryptCpf\":null,\"encryptPassportNo\":null,\"identifyNo\":null,\"fullName\":\"jehrjerr\",\"mobileNo\":\"01238484\",\"phoneCountry\":\"+49\",\"phonePrefixCode\":null,\"passportNoDate\":null,\"phone\":null,\"taxId\":null,\"mobilePrefixCode\":\"+49\",\"cpf\":null,\"phoneArea\":null,\"contractName\":null,\"class\":\"com.alibaba.global.order.management.api.model.ReceiveUserInfoDTO\",\"passportOrganization\":null},\"address2\":\"OTP\",\"addressType\":\"HOME\",\"addressTag\":\"local\",\"languageCode\":\"en\",\"deliveryTimeTag\":\"local\",\"locationTreeAddressName\":\"Arnsberg,Dortmund\",\"fullAddress\":\"Dortmund, Arnsberg, TEST OTP\",\"detailAddress\":\"TEST OTP\",\"postCode\":\"12345\",\"countryName\":\"Germany\",\"locationGSTFree\":null},\"originalOrderNumber\":1000170750038218,\"minDeliveryStatus\":0,\"shippingActualFee\":{\"factory\":null,\"amount\":0,\"cent\":0,\"positive\":false,\"positiveOrZero\":true,\"zero\":true,\"number\":{\"number\":0},\"negative\":false,\"negativeOrZero\":true,\"context\":null,\"currency\":{\"context\":{\"class\":\"javax.money.CurrencyContext\",\"providerName\":\"java.util.Currency\",\"empty\":false},\"class\":\"org.javamoney.moneta.internal.JDKCurrencyAdapter\",\"currencyCode\":\"USD\",\"defaultFractionDigits\":2,\"numericCode\":840},\"class\":\"com.alibaba.global.money.Money\",\"amountInMinorUnit\":0,\"currencyCode\":\"USD\"},\"actualFee\":{\"factory\":null,\"amount\":79.2,\"cent\":7920,\"positive\":true,\"positiveOrZero\":true,\"zero\":false,\"number\":{\"number\":79.2},\"negative\":false,\"negativeOrZero\":false,\"context\":null,\"currency\":{\"context\":{\"class\":\"javax.money.CurrencyContext\",\"providerName\":\"java.util.Currency\",\"empty\":false},\"class\":\"org.javamoney.moneta.internal.JDKCurrencyAdapter\",\"currencyCode\":\"USD\",\"defaultFractionDigits\":2,\"numericCode\":840},\"class\":\"com.alibaba.global.money.Money\",\"amountInMinorUnit\":7920,\"currencyCode\":\"USD\"},\"searchStatus\":1,\"reverseFee\":null,\"endReason\":null,\"taxActualFee\":{\"factory\":null,\"amount\":0,\"cent\":0,\"positive\":false,\"positiveOrZero\":true,\"zero\":true,\"number\":{\"number\":0},\"negative\":false,\"negativeOrZero\":true,\"context\":null,\"currency\":{\"context\":{\"class\":\"javax.money.CurrencyContext\",\"providerName\":\"java.util.Currency\",\"empty\":false},\"class\":\"org.javamoney.moneta.internal.JDKCurrencyAdapter\",\"currencyCode\":\"USD\",\"defaultFractionDigits\":2,\"numericCode\":840},\"class\":\"com.alibaba.global.money.Money\",\"amountInMinorUnit\":0,\"currencyCode\":\"USD\"},\"groupBuy\":false,\"payOrderInfoDTO\":{\"paymentChannelParams\":null,\"productCode\":null,\"riskInfo\":null,\"cashierUrl\":null,\"coId\":\"20990591103210203799000038218\",\"extraParams\":null,\"pmntId\":null,\"class\":\"com.alibaba.global.order.management.api.model.PayOrderInfoDTO\",\"payplanId\":null,\"paymentChannel\":null},\"minPaymentStatus\":1,\"paidTime\":null,\"buyer\":{\"buyerPhone\":null,\"userLevel\":0,\"buyerEmail\":\"<EMAIL>\",\"buyerPhonePrefixCode\":null,\"buyerIcon\":null,\"buyerId\":**********,\"class\":\"com.alibaba.global.order.management.api.model.BuyerInfoDTO\",\"buyerFullName\":\"xiongxintest05 user\"},\"stageOrderLines\":null,\"shippingFee\":{\"factory\":null,\"amount\":0,\"cent\":0,\"positive\":false,\"positiveOrZero\":true,\"zero\":true,\"number\":{\"number\":0},\"negative\":false,\"negativeOrZero\":true,\"context\":null,\"currency\":{\"context\":{\"class\":\"javax.money.CurrencyContext\",\"providerName\":\"java.util.Currency\",\"empty\":false},\"class\":\"org.javamoney.moneta.internal.JDKCurrencyAdapter\",\"currencyCode\":\"USD\",\"defaultFractionDigits\":2,\"numericCode\":840},\"class\":\"com.alibaba.global.money.Money\",\"amountInMinorUnit\":0,\"currencyCode\":\"USD\"},\"actualFeeOfPurposeCurrency\":{\"factory\":null,\"amount\":79.2,\"cent\":7920,\"positive\":true,\"positiveOrZero\":true,\"zero\":false,\"number\":{\"number\":79.2},\"negative\":false,\"negativeOrZero\":false,\"context\":null,\"currency\":{\"context\":{\"class\":\"javax.money.CurrencyContext\",\"providerName\":\"java.util.Currency\",\"empty\":false},\"class\":\"org.javamoney.moneta.internal.JDKCurrencyAdapter\",\"currencyCode\":\"USD\",\"defaultFractionDigits\":2,\"numericCode\":840},\"class\":\"com.alibaba.global.money.Money\",\"amountInMinorUnit\":7920,\"currencyCode\":\"USD\"},\"saleOriginalFee\":{\"factory\":null,\"amount\":198,\"cent\":19800,\"positive\":true,\"positiveOrZero\":true,\"zero\":false,\"number\":{\"number\":198},\"negative\":false,\"negativeOrZero\":false,\"context\":null,\"currency\":{\"context\":{\"class\":\"javax.money.CurrencyContext\",\"providerName\":\"java.util.Currency\",\"empty\":false},\"class\":\"org.javamoney.moneta.internal.JDKCurrencyAdapter\",\"currencyCode\":\"USD\",\"defaultFractionDigits\":2,\"numericCode\":840},\"class\":\"com.alibaba.global.money.Money\",\"amountInMinorUnit\":19800,\"currencyCode\":\"USD\"},\"deliveryTime\":null,\"orderStatus\":0,\"amendOriginalOrder\":true,\"orderAmount\":{\"factory\":null,\"amount\":79.2,\"cent\":7920,\"positive\":true,\"positiveOrZero\":true,\"zero\":false,\"number\":{\"number\":79.2},\"negative\":false,\"negativeOrZero\":false,\"context\":null,\"currency\":{\"context\":{\"class\":\"javax.money.CurrencyContext\",\"providerName\":\"java.util.Currency\",\"empty\":false},\"class\":\"org.javamoney.moneta.internal.JDKCurrencyAdapter\",\"currencyCode\":\"USD\",\"defaultFractionDigits\":2,\"numericCode\":840},\"class\":\"com.alibaba.global.money.Money\",\"amountInMinorUnit\":7920,\"currencyCode\":\"USD\"},\"originalAddress\":null,\"orderLines\":[{\"originalSaleDiscountInfo\":[{\"promotionSponsorDTOS\":null,\"doSplit\":true,\"currentPromotionOriginalInfo\":{\"productSubCode\":null,\"rebatesPercentage\":null,\"priceTypes\":[{\"priceType\":0,\"class\":\"com.alibaba.global.order.management.api.model.PromotionPriceTypeDTO\",\"monetaryAmount\":{\"factory\":null,\"amount\":118.8,\"cent\":11880,\"positive\":true,\"positiveOrZero\":true,\"zero\":false,\"number\":{\"number\":118.8},\"negative\":false,\"negativeOrZero\":false,\"context\":null,\"currency\":{\"context\":{\"class\":\"javax.money.CurrencyContext\",\"providerName\":\"java.util.Currency\",\"empty\":false},\"class\":\"org.javamoney.moneta.internal.JDKCurrencyAdapter\",\"currencyCode\":\"USD\",\"defaultFractionDigits\":2,\"numericCode\":840},\"class\":\"com.alibaba.global.money.Money\",\"amountInMinorUnit\":11880,\"currencyCode\":\"USD\"}}],\"spreadCode\":null,\"promotionSource\":\"CHECKOUT\",\"productCode\":\"fullPiece\",\"promotionRole\":\"SELLER\",\"doesBundle\":false,\"doesMaster\":null,\"relationId\":null,\"class\":\"com.alibaba.global.order.management.api.model.PromotionOrderDTO\",\"promotionId\":\"5000004719668158\"},\"nonGoldStandardDiscountFee\":{\"factory\":null,\"amount\":118.8,\"cent\":11880,\"positive\":true,\"positiveOrZero\":true,\"zero\":false,\"number\":{\"number\":118.8},\"negative\":false,\"negativeOrZero\":false,\"context\":null,\"currency\":{\"context\":{\"class\":\"javax.money.CurrencyContext\",\"providerName\":\"java.util.Currency\",\"empty\":false},\"class\":\"org.javamoney.moneta.internal.JDKCurrencyAdapter\",\"currencyCode\":\"USD\",\"defaultFractionDigits\":2,\"numericCode\":840},\"class\":\"com.alibaba.global.money.Money\",\"amountInMinorUnit\":11880,\"currencyCode\":\"USD\"},\"discountFee\":{\"factory\":null,\"amount\":118.8,\"cent\":11880,\"positive\":true,\"positiveOrZero\":true,\"zero\":false,\"number\":{\"number\":118.8},\"negative\":false,\"negativeOrZero\":false,\"context\":null,\"currency\":{\"context\":{\"class\":\"javax.money.CurrencyContext\",\"providerName\":\"java.util.Currency\",\"empty\":false},\"class\":\"org.javamoney.moneta.internal.JDKCurrencyAdapter\",\"currencyCode\":\"USD\",\"defaultFractionDigits\":2,\"numericCode\":840},\"class\":\"com.alibaba.global.money.Money\",\"amountInMinorUnit\":11880,\"currencyCode\":\"USD\"},\"goldStandardDiscountFee\":{\"factory\":null,\"amount\":0,\"cent\":0,\"positive\":false,\"positiveOrZero\":true,\"zero\":true,\"number\":{\"number\":0},\"negative\":false,\"negativeOrZero\":true,\"context\":null,\"currency\":{\"context\":{\"class\":\"javax.money.CurrencyContext\",\"providerName\":\"java.util.Currency\",\"empty\":false},\"class\":\"org.javamoney.moneta.internal.JDKCurrencyAdapter\",\"currencyCode\":\"USD\",\"defaultFractionDigits\":2,\"numericCode\":840},\"class\":\"com.alibaba.global.money.Money\",\"amountInMinorUnit\":0,\"currencyCode\":\"USD\"},\"promotionOriginalInfo\":[{\"productSubCode\":null,\"rebatesPercentage\":null,\"priceTypes\":[{\"priceType\":0,\"class\":\"com.alibaba.global.order.management.api.model.PromotionPriceTypeDTO\",\"monetaryAmount\":{\"factory\":null,\"amount\":118.8,\"cent\":11880,\"positive\":true,\"positiveOrZero\":true,\"zero\":false,\"number\":{\"number\":118.8},\"negative\":false,\"negativeOrZero\":false,\"context\":null,\"currency\":{\"context\":{\"class\":\"javax.money.CurrencyContext\",\"providerName\":\"java.util.Currency\",\"empty\":false},\"class\":\"org.javamoney.moneta.internal.JDKCurrencyAdapter\",\"currencyCode\":\"USD\",\"defaultFractionDigits\":2,\"numericCode\":840},\"class\":\"com.alibaba.global.money.Money\",\"amountInMinorUnit\":11880,\"currencyCode\":\"USD\"}}],\"spreadCode\":null,\"promotionSource\":\"CHECKOUT\",\"productCode\":\"fullPiece\",\"promotionRole\":\"SELLER\",\"doesBundle\":false,\"doesMaster\":null,\"relationId\":null,\"class\":\"com.alibaba.global.order.management.api.model.PromotionOrderDTO\",\"promotionId\":\"5000004719668158\"}],\"groupBy\":null,\"class\":\"com.alibaba.global.order.management.api.model.PromotionFeeInfoDTO\",\"promotionId\":\"5000004719668158\"}],\"shippingDiscountFee\":{\"factory\":null,\"amount\":0,\"cent\":0,\"positive\":false,\"positiveOrZero\":true,\"zero\":true,\"number\":{\"number\":0},\"negative\":false,\"negativeOrZero\":true,\"context\":null,\"currency\":{\"context\":{\"class\":\"javax.money.CurrencyContext\",\"providerName\":\"java.util.Currency\",\"empty\":false},\"class\":\"org.javamoney.moneta.internal.JDKCurrencyAdapter\",\"currencyCode\":\"USD\",\"defaultFractionDigits\":2,\"numericCode\":840},\"class\":\"com.alibaba.global.money.Money\",\"amountInMinorUnit\":0,\"currencyCode\":\"USD\"},\"seller\":{\"senderAddressDTO\":{\"locationTreeAddressId\":\"*********\",\"countryCode\":null,\"fullAddress\":\"sdsd\",\"detailAddress\":\"sdsd\",\"class\":\"com.alibaba.global.order.management.api.model.SellerAddressInfoDTO\",\"addressId\":null},\"sellerId\":*********,\"shopOwnerId\":null,\"sellerPhone\":null,\"flagship\":false,\"sellerFullName\":\"Shop402172 Store\",\"subSellerId\":\"*********\",\"sellerShortCode\":null,\"storeId\":null,\"sellerType\":1,\"class\":\"com.alibaba.global.order.management.api.model.SellerInfoDTO\",\"sellerEmail\":\"<EMAIL>\"},\"taxRebateInfo\":null,\"reverseShippingFee\":null,\"saleDiscountInfo\":[{\"promotionSponsorDTOS\":null,\"doSplit\":true,\"currentPromotionOriginalInfo\":{\"productSubCode\":null,\"rebatesPercentage\":null,\"priceTypes\":[{\"priceType\":0,\"class\":\"com.alibaba.global.order.management.api.model.PromotionPriceTypeDTO\",\"monetaryAmount\":{\"factory\":null,\"amount\":118.8,\"cent\":11880,\"positive\":true,\"positiveOrZero\":true,\"zero\":false,\"number\":{\"number\":118.8},\"negative\":false,\"negativeOrZero\":false,\"context\":null,\"currency\":{\"context\":{\"class\":\"javax.money.CurrencyContext\",\"providerName\":\"java.util.Currency\",\"empty\":false},\"class\":\"org.javamoney.moneta.internal.JDKCurrencyAdapter\",\"currencyCode\":\"USD\",\"defaultFractionDigits\":2,\"numericCode\":840},\"class\":\"com.alibaba.global.money.Money\",\"amountInMinorUnit\":11880,\"currencyCode\":\"USD\"}}],\"spreadCode\":null,\"promotionSource\":\"CHECKOUT\",\"productCode\":\"fullPiece\",\"promotionRole\":\"SELLER\",\"doesBundle\":false,\"doesMaster\":null,\"relationId\":null,\"class\":\"com.alibaba.global.order.management.api.model.PromotionOrderDTO\",\"promotionId\":\"5000004719668158\"},\"nonGoldStandardDiscountFee\":{\"factory\":null,\"amount\":118.8,\"cent\":11880,\"positive\":true,\"positiveOrZero\":true,\"zero\":false,\"number\":{\"number\":118.8},\"negative\":false,\"negativeOrZero\":false,\"context\":null,\"currency\":{\"context\":{\"class\":\"javax.money.CurrencyContext\",\"providerName\":\"java.util.Currency\",\"empty\":false},\"class\":\"org.javamoney.moneta.internal.JDKCurrencyAdapter\",\"currencyCode\":\"USD\",\"defaultFractionDigits\":2,\"numericCode\":840},\"class\":\"com.alibaba.global.money.Money\",\"amountInMinorUnit\":11880,\"currencyCode\":\"USD\"},\"discountFee\":{\"factory\":null,\"amount\":118.8,\"cent\":11880,\"positive\":true,\"positiveOrZero\":true,\"zero\":false,\"number\":{\"number\":118.8},\"negative\":false,\"negativeOrZero\":false,\"context\":null,\"currency\":{\"context\":{\"class\":\"javax.money.CurrencyContext\",\"providerName\":\"java.util.Currency\",\"empty\":false},\"class\":\"org.javamoney.moneta.internal.JDKCurrencyAdapter\",\"currencyCode\":\"USD\",\"defaultFractionDigits\":2,\"numericCode\":840},\"class\":\"com.alibaba.global.money.Money\",\"amountInMinorUnit\":11880,\"currencyCode\":\"USD\"},\"goldStandardDiscountFee\":{\"factory\":null,\"amount\":0,\"cent\":0,\"positive\":false,\"positiveOrZero\":true,\"zero\":true,\"number\":{\"number\":0},\"negative\":false,\"negativeOrZero\":true,\"context\":null,\"currency\":{\"context\":{\"class\":\"javax.money.CurrencyContext\",\"providerName\":\"java.util.Currency\",\"empty\":false},\"class\":\"org.javamoney.moneta.internal.JDKCurrencyAdapter\",\"currencyCode\":\"USD\",\"defaultFractionDigits\":2,\"numericCode\":840},\"class\":\"com.alibaba.global.money.Money\",\"amountInMinorUnit\":0,\"currencyCode\":\"USD\"},\"promotionOriginalInfo\":[{\"productSubCode\":null,\"rebatesPercentage\":null,\"priceTypes\":[{\"priceType\":0,\"class\":\"com.alibaba.global.order.management.api.model.PromotionPriceTypeDTO\",\"monetaryAmount\":{\"factory\":null,\"amount\":118.8,\"cent\":11880,\"positive\":true,\"positiveOrZero\":true,\"zero\":false,\"number\":{\"number\":118.8},\"negative\":false,\"negativeOrZero\":false,\"context\":null,\"currency\":{\"context\":{\"class\":\"javax.money.CurrencyContext\",\"providerName\":\"java.util.Currency\",\"empty\":false},\"class\":\"org.javamoney.moneta.internal.JDKCurrencyAdapter\",\"currencyCode\":\"USD\",\"defaultFractionDigits\":2,\"numericCode\":840},\"class\":\"com.alibaba.global.money.Money\",\"amountInMinorUnit\":11880,\"currencyCode\":\"USD\"}}],\"spreadCode\":null,\"promotionSource\":\"CHECKOUT\",\"productCode\":\"fullPiece\",\"promotionRole\":\"SELLER\",\"doesBundle\":false,\"doesMaster\":null,\"relationId\":null,\"class\":\"com.alibaba.global.order.management.api.model.PromotionOrderDTO\",\"promotionId\":\"5000004719668158\"}],\"groupBy\":null,\"class\":\"com.alibaba.global.order.management.api.model.PromotionFeeInfoDTO\",\"promotionId\":\"5000004719668158\"}],\"leadingTimeDTO\":{\"shippingProviderCode\":\"CAINIAO_PREMIUM\",\"fulfillmentFinishTime\":null,\"guaranteeTagIcon\":null,\"providerGps\":null,\"logisticCategory\":null,\"guaranteeCutoffTime\":null,\"shippingProviderName\":\"AliExpress Premium Shipping\",\"class\":\"com.alibaba.global.order.management.api.model.LeadingTimeDTO\",\"buyerGps\":null,\"fulfillmentPriority\":null,\"deliveryPriority\":null},\"stepBarDTO\":null,\"logisticsOrderDTO\":{\"transportMethodDTO\":{\"supportCod\":false,\"deliverySlot\":null,\"deliveryType\":\"CAINIAO_PREMIUM\",\"leadingTime\":{\"shippingProviderCode\":\"CAINIAO_PREMIUM\",\"fulfillmentFinishTime\":null,\"guaranteeTagIcon\":null,\"providerGps\":null,\"logisticCategory\":null,\"guaranteeCutoffTime\":null,\"shippingProviderName\":\"AliExpress Premium Shipping\",\"class\":\"com.alibaba.global.order.management.api.model.LeadingTimeDTO\",\"buyerGps\":null,\"fulfillmentPriority\":null,\"deliveryPriority\":null},\"class\":\"com.alibaba.global.order.management.api.model.TransportMethodDTO\"},\"transportMethodLineDTO\":{\"supportCod\":false,\"deliveryType\":\"CAINIAO_PREMIUM\",\"leadingTime\":{\"shippingProviderCode\":\"CAINIAO_PREMIUM\",\"fulfillmentFinishTime\":null,\"guaranteeTagIcon\":null,\"providerGps\":null,\"logisticCategory\":null,\"guaranteeCutoffTime\":null,\"shippingProviderName\":\"AliExpress Premium Shipping\",\"class\":\"com.alibaba.global.order.management.api.model.LeadingTimeDTO\",\"buyerGps\":null,\"fulfillmentPriority\":null,\"deliveryPriority\":null},\"class\":\"com.alibaba.global.order.management.api.model.TransportMethodLineDTO\",\"tradeOrderLineId\":1000170750048218},\"class\":\"com.alibaba.global.order.management.api.model.LogicDeliveryOrderDTO\",\"logicDeliveryId\":\"A3355aea6-4bfb-45de-9c41-f8fe15ebee581000170750038218\",\"amendable\":null},\"bizCode\":\"ali.global.ae.general\",\"tradeOrderId\":1000170750038218,\"unitFee\":{\"factory\":null,\"amount\":99,\"cent\":9900,\"positive\":true,\"positiveOrZero\":true,\"zero\":false,\"number\":{\"number\":99},\"negative\":false,\"negativeOrZero\":false,\"context\":null,\"currency\":{\"context\":{\"class\":\"javax.money.CurrencyContext\",\"providerName\":\"java.util.Currency\",\"empty\":false},\"class\":\"org.javamoney.moneta.internal.JDKCurrencyAdapter\",\"currencyCode\":\"USD\",\"defaultFractionDigits\":2,\"numericCode\":840},\"class\":\"com.alibaba.global.money.Money\",\"amountInMinorUnit\":9900,\"currencyCode\":\"USD\"},\"memo\":null,\"reverseStatus\":null,\"features\":{\"featureMap\":{\"intent_pay_rate\":\"1\",\"sendGoodsCountryCode\":\"CN\",\"buyerEmail\":\"<EMAIL>\",\"gst\":\"true\",\"_sku_tag\":\"[82052,92886]\",\"productUnit\":\"pack\",\"cActivity\":\"WaitPaySuccessMsgWaitTask\",\"lipcc\":\"USD\",\"lip\":\"5000\",\"invt\":\"1000\",\"tml\":\"{\\\"tradeOrderLineId\\\":1000170750048218,\\\"leadingTime\\\":{\\\"shippingProviderCode\\\":\\\"CAINIAO_PREMIUM\\\",\\\"shippingProviderName\\\":\\\"AliExpress Premium Shipping\\\",\\\"deliveryMinTime\\\":**********.651000000,\\\"deliveryMaxTime\\\":**********.651000000,\\\"freightCommitDay\\\":75,\\\"platformWarehousePromiseTime\\\":**********.651000000,\\\"featuresForTrade\\\":\\\"\\\"},\\\"deliveryType\\\":\\\"CAINIAO_PREMIUM\\\",\\\"deliveryName\\\":\\\"CAINIAO_PREMIUM\\\",\\\"solutionCode\\\":\\\"CAINIAO_PREMIUM\\\",\\\"supportCod\\\":false}\",\"exchangeRate\":\"1\",\"dre\":\"CAINIAO_PREMIUM\",\"goodsPrepareTime\":\"7\",\"sacc\":\"CN\",\"height\":\"2\",\"promiseTemplate\":\"[2,{\\\"id\\\":17,\\\"valueMaps\\\":{}},{\\\"id\\\":11,\\\"valueMaps\\\":{}}]\",\"new\":\"1\",\"workflow\":\"bpm.workflow.AE_GLOBAL___AEGeneralFulfillment\",\"sfts\":\"{\\\"inv_code\\\":\\\"14:350852\\\"}\",\"guaranteedDeliveryTime\":\"75\",\"freightCommitDay\":\"75\",\"weight\":\"0.600\",\"rdgt\":\"2332800\",\"fullfillmentMode\":\"other\",\"sellerEmail\":\"<EMAIL>\",\"toc_p\":\"1728000\",\"adt\":\"HOME\",\"siteLanguage\":\"en_US\",\"ads\":\"unpaid\",\"suaId\":\"35400001\",\"sda\":\"sdsd\",\"intent_pay_cur\":\"USD\",\"orderSearchStatus\":\"1\",\"pbi\":\"201512802\",\"isRetail\":\"false\",\"sbpm\":\"[\\\"pmnt.paypal\\\"]\",\"sellerType\":\"1\",\"saId\":\"*********\",\"sellerRegion\":\"CN\",\"pft\":\"USD 0.00\",\"in_st\":\"0^2_1^12000016667659209_2^6000000000935225700_3^6000000000128249158\",\"deliveryTime\":\"7-15\",\"dop\":\"CAINIAO_PREMIUM\",\"_sku_feature\":\"{\\\"inv_code\\\":\\\"14:350852\\\"}\",\"pcpdf\":\"[{\\\"promotionId\\\":\\\"5000004719668158\\\",\\\"discountFee\\\":{\\\"currencyCode\\\":\\\"USD\\\",\\\"amount\\\":11880},\\\"goldStandardDiscountFee\\\":{\\\"currencyCode\\\":\\\"USD\\\",\\\"amount\\\":0}}]\",\"s_s_c\":\"1005001578993694_12000016667659209\",\"wc\":\"ae_marketplace\",\"assid\":\"*********\",\"proRetailPrice\":\"USD 1E+2\",\"oip\":\"10000\",\"opicc\":\"USD\",\"t_c\":\"default\",\"tci\":\"*********\",\"checkOutBizCode\":\"ali.global.ae.trade.general\",\"wt\":\"ae_marketplace\",\"_product_feature\":\"{\\\"company_id\\\":\\\"*********\\\",\\\"creator_nick\\\":\\\"aliqatest01\\\",\\\"creator_uid\\\":\\\"*********\\\",\\\"delivery_time\\\":\\\"7\\\",\\\"detail_type\\\":\\\"std\\\",\\\"down_shelf_time\\\":\\\"1611904969886\\\",\\\"fullfillmentMode\\\":\\\"other\\\",\\\"gpfSource\\\":\\\"common-market-100\\\",\\\"groupBuyId\\\":null,\\\"newSizeChart\\\":\\\"true\\\",\\\"owner_id\\\":\\\"*********\\\",\\\"owner_nick\\\":\\\"aliqatest01\\\",\\\"publish_biz_code\\\":\\\"ali.ae.general\\\",\\\"publish_by_new_service\\\":\\\"***********\\\",\\\"source_app\\\":\\\"gpf-i18n\\\",\\\"src\\\":\\\"post\\\",\\\"write_trunk_date\\\":\\\"1602470601716\\\"}\",\"s_sp_c\":\"1005001578993694_AE-12000016667659209\",\"in_rs\":\"payment_success_deduct\",\"t_p\":\"0.0\",\"ppf\":\"{\\\"currencyCode\\\":\\\"USD\\\",\\\"amount\\\":0}\",\"crtid\":\"10000021225002\",\"lotNum\":\"4\",\"length\":\"1\",\"flagship\":\"false\",\"gt\":\"USD 0.00\",\"svr\":\"true\",\"toc_ship\":\"2332800\",\"sfa\":\"sdsd\",\"pdf\":\"{\\\"currency\\\":\\\"USD\\\",\\\"itemSingleDiscountFee\\\":{\\\"currencyCode\\\":\\\"USD\\\",\\\"amount\\\":100},\\\"itemGoldStandardFee\\\":{\\\"currencyCode\\\":\\\"USD\\\",\\\"amount\\\":0},\\\"itemNonGoldStandardFee\\\":{\\\"currencyCode\\\":\\\"USD\\\",\\\"amount\\\":11880},\\\"shippingGoldStandardFee\\\":{\\\"currencyCode\\\":\\\"USD\\\",\\\"amount\\\":0},\\\"shippingNonGoldStandardFee\\\":{\\\"currencyCode\\\":\\\"USD\\\",\\\"amount\\\":0}}\",\"ical\":\"990000\",\"toc_all\":\"[{\\\"type\\\":1,\\\"initSetSec\\\":1728000,\\\"remindSecs\\\":[259200,86400]},{\\\"type\\\":2,\\\"initSetSec\\\":2332800,\\\"remindSecs\\\":[]}]\",\"width\":\"1\",\"bul\":\"0\",\"exchangeCurrency\":\"USD\",\"adtg\":\"local\",\"bid\":\"201512802\",\"logisticsType\":\"CAINIAO_PREMIUM\",\"in_i\":\"{}\"},\"notEmpty\":true,\"class\":\"com.alibaba.global.order.management.api.model.Features\",\"empty\":false},\"ascStatus\":\"unpaid\",\"originalOrderLineNumber\":1000170750048218,\"reversePolicyId\":null,\"shippingActualFee\":{\"factory\":null,\"amount\":0,\"cent\":0,\"positive\":false,\"positiveOrZero\":true,\"zero\":true,\"number\":{\"number\":0},\"negative\":false,\"negativeOrZero\":true,\"context\":null,\"currency\":{\"context\":{\"class\":\"javax.money.CurrencyContext\",\"providerName\":\"java.util.Currency\",\"empty\":false},\"class\":\"org.javamoney.moneta.internal.JDKCurrencyAdapter\",\"currencyCode\":\"USD\",\"defaultFractionDigits\":2,\"numericCode\":840},\"class\":\"com.alibaba.global.money.Money\",\"amountInMinorUnit\":0,\"currencyCode\":\"USD\"},\"actualFee\":{\"factory\":null,\"amount\":79.2,\"cent\":7920,\"positive\":true,\"positiveOrZero\":true,\"zero\":false,\"number\":{\"number\":79.2},\"negative\":false,\"negativeOrZero\":false,\"context\":null,\"currency\":{\"context\":{\"class\":\"javax.money.CurrencyContext\",\"providerName\":\"java.util.Currency\",\"empty\":false},\"class\":\"org.javamoney.moneta.internal.JDKCurrencyAdapter\",\"currencyCode\":\"USD\",\"defaultFractionDigits\":2,\"numericCode\":840},\"class\":\"com.alibaba.global.money.Money\",\"amountInMinorUnit\":7920,\"currencyCode\":\"USD\"},\"adjustFee\":{\"factory\":null,\"amount\":0,\"cent\":0,\"positive\":false,\"positiveOrZero\":true,\"zero\":true,\"number\":{\"number\":0},\"negative\":false,\"negativeOrZero\":true,\"context\":null,\"currency\":{\"context\":{\"class\":\"javax.money.CurrencyContext\",\"providerName\":\"java.util.Currency\",\"empty\":false},\"class\":\"org.javamoney.moneta.internal.JDKCurrencyAdapter\",\"currencyCode\":\"USD\",\"defaultFractionDigits\":2,\"numericCode\":840},\"class\":\"com.alibaba.global.money.Money\",\"amountInMinorUnit\":0,\"currencyCode\":\"USD\"},\"reverseFee\":null,\"marketplace\":false,\"endReason\":null,\"couponInfo\":null,\"deliveryEndTime\":null,\"orderClassify\":null,\"taxActualFee\":{\"factory\":null,\"amount\":0,\"cent\":0,\"positive\":false,\"positiveOrZero\":true,\"zero\":true,\"number\":{\"number\":0},\"negative\":false,\"negativeOrZero\":true,\"context\":null,\"currency\":{\"context\":{\"class\":\"javax.money.CurrencyContext\",\"providerName\":\"java.util.Currency\",\"empty\":false},\"class\":\"org.javamoney.moneta.internal.JDKCurrencyAdapter\",\"currencyCode\":\"USD\",\"defaultFractionDigits\":2,\"numericCode\":840},\"class\":\"com.alibaba.global.money.Money\",\"amountInMinorUnit\":0,\"currencyCode\":\"USD\"},\"outDeliveryId\":\"A3355aea6-4bfb-45de-9c41-f8fe15ebee581000170750038218\",\"paidTime\":null,\"taxCode\":\"default\",\"warrantyDTO\":null,\"buyer\":{\"buyerPhone\":null,\"userLevel\":0,\"buyerEmail\":\"<EMAIL>\",\"buyerPhonePrefixCode\":null,\"buyerIcon\":null,\"buyerId\":**********,\"class\":\"com.alibaba.global.order.management.api.model.BuyerInfoDTO\",\"buyerFullName\":\"xiongxintest05 user\"},\"shippingDiscountInfo\":null,\"stageApportionInfos\":null,\"payableFee\":{\"factory\":null,\"amount\":79.2,\"cent\":7920,\"positive\":true,\"positiveOrZero\":true,\"zero\":false,\"number\":{\"number\":79.2},\"negative\":false,\"negativeOrZero\":false,\"context\":null,\"currency\":{\"context\":{\"class\":\"javax.money.CurrencyContext\",\"providerName\":\"java.util.Currency\",\"empty\":false},\"class\":\"org.javamoney.moneta.internal.JDKCurrencyAdapter\",\"currencyCode\":\"USD\",\"defaultFractionDigits\":2,\"numericCode\":840},\"class\":\"com.alibaba.global.money.Money\",\"amountInMinorUnit\":7920,\"currencyCode\":\"USD\"},\"ascExtendInfoDTO\":{\"hasPrinted\":null,\"ascStatus\":\"unpaid\",\"paidCommission\":null,\"shippingSLA\":null,\"enable\":false,\"rtsSLA\":null,\"invoiceNumber\":null,\"class\":\"com.alibaba.global.order.management.api.model.AscExtendInfoDTO\",\"doesRetail\":false,\"inTransit\":null},\"shippingFee\":{\"factory\":null,\"amount\":0,\"cent\":0,\"positive\":false,\"positiveOrZero\":true,\"zero\":true,\"number\":{\"number\":0},\"negative\":false,\"negativeOrZero\":true,\"context\":null,\"currency\":{\"context\":{\"class\":\"javax.money.CurrencyContext\",\"providerName\":\"java.util.Currency\",\"empty\":false},\"class\":\"org.javamoney.moneta.internal.JDKCurrencyAdapter\",\"currencyCode\":\"USD\",\"defaultFractionDigits\":2,\"numericCode\":840},\"class\":\"com.alibaba.global.money.Money\",\"amountInMinorUnit\":0,\"currencyCode\":\"USD\"},\"actualFeeOfPurposeCurrency\":{\"factory\":null,\"amount\":79.2,\"cent\":7920,\"positive\":true,\"positiveOrZero\":true,\"zero\":false,\"number\":{\"number\":79.2},\"negative\":false,\"negativeOrZero\":false,\"context\":null,\"currency\":{\"context\":{\"class\":\"javax.money.CurrencyContext\",\"providerName\":\"java.util.Currency\",\"empty\":false},\"class\":\"org.javamoney.moneta.internal.JDKCurrencyAdapter\",\"currencyCode\":\"USD\",\"defaultFractionDigits\":2,\"numericCode\":840},\"class\":\"com.alibaba.global.money.Money\",\"amountInMinorUnit\":7920,\"currencyCode\":\"USD\"},\"saleOriginalFee\":{\"factory\":null,\"amount\":198,\"cent\":19800,\"positive\":true,\"positiveOrZero\":true,\"zero\":false,\"number\":{\"number\":198},\"negative\":false,\"negativeOrZero\":false,\"context\":null,\"currency\":{\"context\":{\"class\":\"javax.money.CurrencyContext\",\"providerName\":\"java.util.Currency\",\"empty\":false},\"class\":\"org.javamoney.moneta.internal.JDKCurrencyAdapter\",\"currencyCode\":\"USD\",\"defaultFractionDigits\":2,\"numericCode\":840},\"class\":\"com.alibaba.global.money.Money\",\"amountInMinorUnit\":19800,\"currencyCode\":\"USD\"},\"deliveryStatus\":0,\"reversePolicyFreeDays\":null,\"tradeOrderLineId\":1000170750048218,\"succeedTimeout\":null,\"bizTenant\":1,\"deliveryDetailStatus\":null,\"exchangeInfo\":{\"quoteCurrency\":\"USD\",\"exchangeRate\":1,\"class\":\"com.alibaba.global.order.management.api.model.ExchangeInfoDTO\",\"baseCurrency\":\"USD\"},\"volumePrice\":null,\"orderStatus\":0,\"rebateUsed\":null,\"paymentDiscountFee\":{\"factory\":null,\"amount\":0,\"cent\":0,\"positive\":false,\"positiveOrZero\":true,\"zero\":true,\"number\":{\"number\":0},\"negative\":false,\"negativeOrZero\":true,\"context\":null,\"currency\":{\"context\":{\"class\":\"javax.money.CurrencyContext\",\"providerName\":\"java.util.Currency\",\"empty\":false},\"class\":\"org.javamoney.moneta.internal.JDKCurrencyAdapter\",\"currencyCode\":\"USD\",\"defaultFractionDigits\":2,\"numericCode\":840},\"class\":\"com.alibaba.global.money.Money\",\"amountInMinorUnit\":0,\"currencyCode\":\"USD\"},\"outPayId\":null,\"saleDiscountFee\":{\"factory\":null,\"amount\":118.8,\"cent\":11880,\"positive\":true,\"positiveOrZero\":true,\"zero\":false,\"number\":{\"number\":118.8},\"negative\":false,\"negativeOrZero\":false,\"context\":null,\"currency\":{\"context\":{\"class\":\"javax.money.CurrencyContext\",\"providerName\":\"java.util.Currency\",\"empty\":false},\"class\":\"org.javamoney.moneta.internal.JDKCurrencyAdapter\",\"currencyCode\":\"USD\",\"defaultFractionDigits\":2,\"numericCode\":840},\"class\":\"com.alibaba.global.money.Money\",\"amountInMinorUnit\":11880,\"currencyCode\":\"USD\"},\"crossBorder\":false,\"slotIncentiveFee\":null,\"taxFee\":{\"factory\":null,\"amount\":0,\"cent\":0,\"positive\":false,\"positiveOrZero\":true,\"zero\":true,\"number\":{\"number\":0},\"negative\":false,\"negativeOrZero\":true,\"context\":null,\"currency\":{\"context\":{\"class\":\"javax.money.CurrencyContext\",\"providerName\":\"java.util.Currency\",\"empty\":false},\"class\":\"org.javamoney.moneta.internal.JDKCurrencyAdapter\",\"currencyCode\":\"USD\",\"defaultFractionDigits\":2,\"numericCode\":840},\"class\":\"com.alibaba.global.money.Money\",\"amountInMinorUnit\":0,\"currencyCode\":\"USD\"},\"frozenType\":0,\"class\":\"com.alibaba.global.order.management.api.model.TradeOrderLineDTO\",\"payOrderDTO\":{\"paymentPromotionFee\":null,\"productCode\":null,\"riskInfo\":null,\"channelParams\":null,\"channelText\":null,\"paymentCheckoutId\":\"20990591103210203799000038218\",\"paymentCostFee\":null,\"channel\":null,\"actualPaidFee\":null,\"class\":\"com.alibaba.global.order.management.api.model.PayOrderDTO\"},\"dutyFee\":null,\"ofcStatus\":null,\"product\":{\"itemId\":\"1005001578993694\",\"itemPicUrl\":\"Hf234792d6e1f4e95bf5dc6e345f2c45ch.png\",\"topCategoryId\":\"*********\",\"itemTitle\":\"[for Bizum]- testDo not modify the price editsa\",\"itemUnit\":\"pack\",\"siteItemTitle\":\"[for Bizum]- testDo not modify the price editsa\",\"sku\":{\"length\":\"1\",\"weight\":\"0.600\",\"barCode\":null,\"sellerSkuCode\":\"1005001578993694_12000016667659209\",\"features\":{\"inv_code\":\"14:350852\"},\"width\":\"1\",\"shopSkuCode\":\"1005001578993694_AE-12000016667659209\",\"class\":\"com.alibaba.global.order.management.api.model.SkuDTO\",\"skuInfo\":\"[{\\\"image\\\":{\\\"imageUrl\\\":\\\"Hf234792d6e1f4e95bf5dc6e345f2c45ch.png\\\",\\\"type\\\":1},\\\"valueId\\\":350852,\\\"valueText\\\":\\\"Orange\\\",\\\"propertyName\\\":\\\"Color\\\",\\\"isCustom\\\":true,\\\"style\\\":\\\"colour_atla\\\",\\\"propertyId\\\":14,\\\"order\\\":1}]\",\"skuId\":\"12000016667659209\",\"properties\":null,\"channelCode\":null,\"height\":\"2\"},\"class\":\"com.alibaba.global.order.management.api.model.ProductDTO\",\"itemUrl\":\"https://www.aliexpress.com/item/1005001578993694.html?mp=1\",\"categoryId\":\"200001916\"},\"timeoutDTOS\":[{\"initSetSec\":1728000,\"remindSecs\":[259200,86400],\"totalExtendSec\":null,\"type\":1,\"class\":\"com.alibaba.global.order.management.api.model.TimeoutDTO\",\"gmtStart\":null},{\"initSetSec\":2332800,\"remindSecs\":[],\"totalExtendSec\":null,\"type\":2,\"class\":\"com.alibaba.global.order.management.api.model.TimeoutDTO\",\"gmtStart\":null}],\"quantity\":2,\"buyerDeleteStatus\":0,\"snapshotInfo\":{\"snapshotSitePath\":null,\"snapshotPath\":\"H9f727df1dc3241e0a7ecdf612c1b3dfce.xml\",\"snapshotId\":null,\"snapshotSmallPhotoPath\":\"H16cf6aa1d281465e8a0c70c4da770d55U.png\",\"class\":\"com.alibaba.global.order.management.api.model.SnapshotDTO\"},\"taxPercentage\":\"0.0\",\"taxRebateFee\":{\"factory\":null,\"amount\":0,\"cent\":0,\"positive\":false,\"positiveOrZero\":true,\"zero\":true,\"number\":{\"number\":0},\"negative\":false,\"negativeOrZero\":true,\"context\":null,\"currency\":{\"context\":{\"class\":\"javax.money.CurrencyContext\",\"providerName\":\"java.util.Currency\",\"empty\":false},\"class\":\"org.javamoney.moneta.internal.JDKCurrencyAdapter\",\"currencyCode\":\"USD\",\"defaultFractionDigits\":2,\"numericCode\":840},\"class\":\"com.alibaba.global.money.Money\",\"amountInMinorUnit\":0,\"currencyCode\":\"USD\"},\"originalShippingDiscountInfo\":null,\"sellerDeleteStatus\":0,\"promotionOrderDTO\":[{\"productSubCode\":null,\"rebatesPercentage\":null,\"priceTypes\":[{\"priceType\":0,\"class\":\"com.alibaba.global.order.management.api.model.PromotionPriceTypeDTO\",\"monetaryAmount\":{\"factory\":null,\"amount\":118.8,\"cent\":11880,\"positive\":true,\"positiveOrZero\":true,\"zero\":false,\"number\":{\"number\":118.8},\"negative\":false,\"negativeOrZero\":false,\"context\":null,\"currency\":{\"context\":{\"class\":\"javax.money.CurrencyContext\",\"providerName\":\"java.util.Currency\",\"empty\":false},\"class\":\"org.javamoney.moneta.internal.JDKCurrencyAdapter\",\"currencyCode\":\"USD\",\"defaultFractionDigits\":2,\"numericCode\":840},\"class\":\"com.alibaba.global.money.Money\",\"amountInMinorUnit\":11880,\"currencyCode\":\"USD\"}}],\"spreadCode\":null,\"promotionSource\":\"CHECKOUT\",\"productCode\":\"fullPiece\",\"promotionRole\":\"SELLER\",\"doesBundle\":false,\"doesMaster\":null,\"relationId\":null,\"class\":\"com.alibaba.global.order.management.api.model.PromotionOrderDTO\",\"promotionId\":\"5000004719668158\"}],\"redmart\":false,\"productCode\":null,\"shippingSurchargeFee\":null,\"siteId\":\"GLOBAL\",\"endTime\":null,\"payStatus\":1,\"wareHouseDTO\":{\"wareHouseCode\":\"ae_marketplace\",\"inventoryType\":1000,\"estimateTime\":null,\"marketplace\":false,\"wareHouseType\":\"ae_marketplace\",\"locationTreeAddressId\":null,\"shippingType\":\"ae_marketplace\",\"fullAddress\":null,\"detailAddress\":null,\"class\":\"com.alibaba.global.order.management.api.model.WareHouseDTO\"}}],\"otherOrderInfo\":\";pminfo:[{\\\"promotionId\\\"#3A\\\"5000004719668158\\\",\\\"productCode\\\"#3A\\\"fullPiece\\\",\\\"promotionPriceTypeSerializeDOs\\\"#3A[{\\\"priceType\\\"#3A0,\\\"amount\\\"#3A{\\\"currencyCode\\\"#3A\\\"USD\\\",\\\"amount\\\"#3A11880}}],\\\"promotionRole\\\"#3A0,\\\"promotionTargetType\\\"#3A0,\\\"promotionSource\\\"#3A0}];\",\"saleDiscountFee\":{\"factory\":null,\"amount\":118.8,\"cent\":11880,\"positive\":true,\"positiveOrZero\":true,\"zero\":false,\"number\":{\"number\":118.8},\"negative\":false,\"negativeOrZero\":false,\"context\":null,\"currency\":{\"context\":{\"class\":\"javax.money.CurrencyContext\",\"providerName\":\"java.util.Currency\",\"empty\":false},\"class\":\"org.javamoney.moneta.internal.JDKCurrencyAdapter\",\"currencyCode\":\"USD\",\"defaultFractionDigits\":2,\"numericCode\":840},\"class\":\"com.alibaba.global.money.Money\",\"amountInMinorUnit\":11880,\"currencyCode\":\"USD\"},\"taxFee\":{\"factory\":null,\"amount\":0,\"cent\":0,\"positive\":false,\"positiveOrZero\":true,\"zero\":true,\"number\":{\"number\":0},\"negative\":false,\"negativeOrZero\":true,\"context\":null,\"currency\":{\"context\":{\"class\":\"javax.money.CurrencyContext\",\"providerName\":\"java.util.Currency\",\"empty\":false},\"class\":\"org.javamoney.moneta.internal.JDKCurrencyAdapter\",\"currencyCode\":\"USD\",\"defaultFractionDigits\":2,\"numericCode\":840},\"class\":\"com.alibaba.global.money.Money\",\"amountInMinorUnit\":0,\"currencyCode\":\"USD\"},\"promotionSnapshotId\":\"2000094280808218\",\"tradeOrderGroup\":null,\"class\":\"com.alibaba.global.order.management.api.model.TradeOrderDTO\",\"payOrderDTO\":{\"paymentPromotionFee\":null,\"productCode\":\"NORMAL_PAY\",\"riskInfo\":null,\"channelParams\":null,\"channelText\":null,\"paymentCheckoutId\":\"20990591103210203799000038218\",\"paymentCostFee\":null,\"channel\":null,\"actualPaidFee\":null,\"class\":\"com.alibaba.global.order.management.api.model.PayOrderDTO\"},\"snapshotId\":null,\"internationalTaxCode\":null,\"taxRebateFee\":{\"factory\":null,\"amount\":0,\"cent\":0,\"positive\":false,\"positiveOrZero\":true,\"zero\":true,\"number\":{\"number\":0},\"negative\":false,\"negativeOrZero\":true,\"context\":null,\"currency\":{\"context\":{\"class\":\"javax.money.CurrencyContext\",\"providerName\":\"java.util.Currency\",\"empty\":false},\"class\":\"org.javamoney.moneta.internal.JDKCurrencyAdapter\",\"currencyCode\":\"USD\",\"defaultFractionDigits\":2,\"numericCode\":840},\"class\":\"com.alibaba.global.money.Money\",\"amountInMinorUnit\":0,\"currencyCode\":\"USD\"},\"groupMemberOrderIds\":null,\"promotionSpreadCode\":null,\"productCode\":null,\"isAmended\":false,\"localTaxCode\":null,\"fulfillTimeout\":null,\"shippingSurchargeFee\":null,\"cOD\":false,\"siteId\":\"GLOBAL\",\"maxDeliveryStatus\":0,\"billingAddress\":null,\"endTime\":null,\"payStatus\":1}");
        when(orderQueryForBuyerFacade.queryTradeOrderById(1000170750038218L)).thenReturn(response);

        tradeOrderAdjustViewServerImpl.getTradeOrderAdjustDetail(1000170750038218L,new BigDecimal(50));


        BufferedReader bufferedReader=new BufferedReader(new FileReader(new File(AdjustPriceTest.class.getResource("/").getPath()+"dpOrderData")));
        response=buildTestData(bufferedReader.readLine());
        when(orderQueryForBuyerFacade.queryTradeOrderById(1000170750038218L)).thenReturn(response);

        tradeOrderAdjustViewServerImpl.getTradeOrderAdjustDetail(1000170750038218L,new BigDecimal(50));


    }


    private Response<TradeOrderDTO> buildTestData(String json){

        TradeOrderDTO tradeOrderDTO=JSONObject.parseObject(json,TradeOrderDTO.class);

        JSONObject jsonObject=JSONObject.parseObject(json);
        tradeOrderDTO.setActualFee(convertMoney(jsonObject,"actualFee"));
        tradeOrderDTO.setOrderAmount(convertMoney(jsonObject,"orderAmount"));
        tradeOrderDTO.setSaleDiscountFee(convertMoney(jsonObject,"saleDiscountFee"));
        tradeOrderDTO.setShippingActualFee(convertMoney(jsonObject,"shippingActualFee"));
        tradeOrderDTO.setSaleOriginalFee(convertMoney(jsonObject,"saleOriginalFee"));
        tradeOrderDTO.setShippingDiscountFee(convertMoney(jsonObject,"shippingDiscountFee"));
        tradeOrderDTO.setShippingFee(convertMoney(jsonObject,"shippingFee"));
        tradeOrderDTO.setTaxActualFee(convertMoney(jsonObject,"taxActualFee"));
        tradeOrderDTO.setTaxFee(convertMoney(jsonObject,"taxFee"));
        tradeOrderDTO.setTaxRebateFee(convertMoney(jsonObject,"taxRebateFee"));

        tradeOrderDTO.getOrderLines().stream().forEach(tradeOrderLineDTO -> {
            JSONArray jsonArray=jsonObject.getJSONArray("orderLines");
            JSONObject orderLinObject=null;
            for(int i=0;i<jsonArray.size();i++){
                if(jsonArray.getJSONObject(i).getLong("tradeOrderLineId").equals(tradeOrderLineDTO.getTradeOrderLineId())){
                    orderLinObject=jsonArray.getJSONObject(i);
                }
            }
            if(orderLinObject==null){
                return;
            }
            tradeOrderLineDTO.setActualFee(convertMoney(orderLinObject,"actualFee"));
            tradeOrderLineDTO.setAdjustFee(convertMoney(orderLinObject,"adjustFee"));
            tradeOrderLineDTO.setTaxActualFee(convertMoney(orderLinObject,"taxActualFee"));
            tradeOrderLineDTO.setShippingDiscountFee(convertMoney(orderLinObject,"shippingDiscountFee"));
            tradeOrderLineDTO.setUnitFee(convertMoney(orderLinObject,"unitFee"));
            tradeOrderLineDTO.setShippingActualFee(convertMoney(orderLinObject,"shippingActualFee"));
            tradeOrderLineDTO.setSaleDiscountFee(convertMoney(orderLinObject,"saleDiscountFee"));
            tradeOrderLineDTO.setShippingFee(convertMoney(orderLinObject,"shippingFee"));
            tradeOrderLineDTO.setPayableFee(convertMoney(orderLinObject,"payableFee"));
            tradeOrderLineDTO.setTaxFee(convertMoney(orderLinObject,"taxFee"));
            handleSaleDiscountInfo(orderLinObject,tradeOrderLineDTO);
            handleOriginSaleDiscountInfo(orderLinObject,tradeOrderLineDTO);
            handleOriginShippingDiscountInfo(orderLinObject,tradeOrderLineDTO);
            handleShippingDiscountInfo(orderLinObject,tradeOrderLineDTO);
        });

        return Response.success(tradeOrderDTO);
    }

    public static void main(String[] args) {
        convertMoney(null,null);
    }
    private static Money convertMoney(JSONObject jsonObject,String key){

       JSONObject jsonObject1=jsonObject.getJSONObject(key);
        //jsonObject=        JSONObject.parseObject("{\"factory\":null,\"amount\":78.9,\"cent\":100,\"positive\":false,\"positiveOrZero\":true,\"zero\":true,\"number\":{\"number\":0},\"negative\":false,\"negativeOrZero\":true,\"context\":null,\"currency\":{\"context\":{\"class\":\"javax.money.CurrencyContext\",\"providerName\":\"java.util.Currency\",\"empty\":false},\"class\":\"org.javamoney.moneta.internal.JDKCurrencyAdapter\",\"currencyCode\":\"USD\",\"defaultFractionDigits\":2,\"numericCode\":840},\"class\":\"com.alibaba.global.money.Money\",\"amountInMinorUnit\":0,\"currencyCode\":\"USD\"}");
        Money money=Money.of(jsonObject1.getBigDecimal("amount"),jsonObject1.getString("currencyCode"));
        return money;
    }

    private static void handleSaleDiscountInfo(JSONObject jsonObject,TradeOrderLineDTO tradeOrderLineDTO){
        JSONArray jsonArray=jsonObject.getJSONArray("saleDiscountInfo");
        if(CollectionUtils.isEmpty(tradeOrderLineDTO.getSaleDiscountInfo())){
            return;
        }
        tradeOrderLineDTO.getSaleDiscountInfo().stream().forEach(promotionFeeInfoDTO -> {
            for (int i=0;i<jsonArray.size();i++){
                if(jsonArray.getJSONObject(i).getString("promotionId").equalsIgnoreCase(promotionFeeInfoDTO.getPromotionId())){
                   JSONObject temp= jsonArray.getJSONObject(i);
                    promotionFeeInfoDTO.setDiscountFee(convertMoney(temp,"discountFee"));
                    promotionFeeInfoDTO.setGoldStandardDiscountFee(convertMoney(temp,"goldStandardDiscountFee"));
                    promotionFeeInfoDTO.setNonGoldStandardDiscountFee(convertMoney(temp,"nonGoldStandardDiscountFee"));
                }
            }
        });

    }

    private static void handleOriginSaleDiscountInfo(JSONObject jsonObject,TradeOrderLineDTO tradeOrderLineDTO){
        JSONArray jsonArray=jsonObject.getJSONArray("originalSaleDiscountInfo");
        if(CollectionUtils.isEmpty(tradeOrderLineDTO.getOriginalSaleDiscountInfo())){
            return;
        }
        tradeOrderLineDTO.getOriginalSaleDiscountInfo().stream().forEach(promotionFeeInfoDTO -> {
            for (int i=0;i<jsonArray.size();i++){
                if(jsonArray.getJSONObject(i).getString("promotionId").equalsIgnoreCase(promotionFeeInfoDTO.getPromotionId())){
                    JSONObject temp= jsonArray.getJSONObject(i);
                    promotionFeeInfoDTO.setDiscountFee(convertMoney(temp,"discountFee"));
                    promotionFeeInfoDTO.setGoldStandardDiscountFee(convertMoney(temp,"goldStandardDiscountFee"));
                    promotionFeeInfoDTO.setNonGoldStandardDiscountFee(convertMoney(temp,"nonGoldStandardDiscountFee"));
                }
            }
        });

    }



    private static void handleShippingDiscountInfo(JSONObject jsonObject,TradeOrderLineDTO tradeOrderLineDTO){
        JSONArray jsonArray=jsonObject.getJSONArray("shippingDiscountInfo");
        if(CollectionUtils.isEmpty(tradeOrderLineDTO.getShippingDiscountInfo())){
            return;
        }
        tradeOrderLineDTO.getSaleDiscountInfo().stream().forEach(promotionFeeInfoDTO -> {
            for (int i=0;i<jsonArray.size();i++){
                if(jsonArray.getJSONObject(i).getString("promotionId").equalsIgnoreCase(promotionFeeInfoDTO.getPromotionId())){
                    JSONObject temp= jsonArray.getJSONObject(i);
                    promotionFeeInfoDTO.setDiscountFee(convertMoney(temp,"discountFee"));
                    promotionFeeInfoDTO.setGoldStandardDiscountFee(convertMoney(temp,"goldStandardDiscountFee"));
                    promotionFeeInfoDTO.setNonGoldStandardDiscountFee(convertMoney(temp,"nonGoldStandardDiscountFee"));
                }
            }
        });

    }

    private static void handleOriginShippingDiscountInfo(JSONObject jsonObject,TradeOrderLineDTO tradeOrderLineDTO){
        JSONArray jsonArray=jsonObject.getJSONArray("originalShippingDiscountInfo");
        if(CollectionUtils.isEmpty(tradeOrderLineDTO.getOriginalShippingDiscountInfo())){
            return;
        }
        tradeOrderLineDTO.getOriginalShippingDiscountInfo().stream().forEach(promotionFeeInfoDTO -> {
            for (int i=0;i<jsonArray.size();i++){
                if(jsonArray.getJSONObject(i).getString("promotionId").equalsIgnoreCase(promotionFeeInfoDTO.getPromotionId())){
                    JSONObject temp= jsonArray.getJSONObject(i);
                    promotionFeeInfoDTO.setDiscountFee(convertMoney(temp,"discountFee"));
                    promotionFeeInfoDTO.setGoldStandardDiscountFee(convertMoney(temp,"goldStandardDiscountFee"));
                    promotionFeeInfoDTO.setNonGoldStandardDiscountFee(convertMoney(temp,"nonGoldStandardDiscountFee"));
                }
            }
        });

    }

}
