package com.aliexpress.order.price;

import com.aliexpress.databank.price.TradeOrderPriceRenderService;
import com.aliexpress.databank.price.TradeSubinvokesCheckService;
import com.aliexpress.databank.price.dto.common.render.common.BaseResult;
import com.aliexpress.databank.price.factory.TradePriceJsonPathFactory;
import com.aliexpress.databank.price.factory.impl.jsonpath.*;
import com.aliexpress.databank.price.helper.TradePriceJsonPathHelper;
import com.aliexpress.databank.price.impl.TradeOrderPriceRenderServiceImpl;
import com.aliexpress.databank.price.impl.TradeSubinvokesCheckServiceImpl;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = {TradePriceRenderSrviceTest.class,
        TradeOrderPriceRenderServiceImpl.class, TradeOrderPriceRenderService.class,TradeSubinvokesCheckService.class,TradeSubinvokesCheckServiceImpl.class,
        org.noear.snack.ONode.class, TradePriceJsonPathHelper.class, TradePriceJsonPathFactory.class,
        ICPriceRenderFactory.class, SimpleJsonPathFactory.class, PromiseResultsJsonPathFactory.class,
        ExchangeRateFactory.class, FulfillmentServiceFactory.class, PromotionInfoFactory.class, CalculatePayCurrencyFactory.class
        ,PriceCenterRenderFactory.class,TaxCalculateFactory.class})

public class TradePriceRenderSrviceTest {

    public static String COOKIE = "ALIPAYCHAIRBUCJSESSIONID=7100d9a3-106e-4593-a917-0823595fcddf;c_token=59f7592a69bd55a749530a451a704334;ck2=5439164ad03e46b340fd30913f3c9069;an=lijuan.qilj;lg=true;sg=j05;SSO_EMPID_HASH_V2=1605b460c7964ca85f7333be9a6c67b8;_m_h5_tk=fc81e64585591fd7590160d7b6c84149_1619424584531;_m_h5_tk_enc=2c364639717f570cb20fce6c022a6808;SSO_BU_HASH_V2=4e8d59ba0e6e3962f75465fbe4286df2;UM_distinctid=1791724d9805cd-0fcb71fb34f87-1635685c-1aeaa0-1791724d981af6;SSO_LANG_V2=ZH-CN;SSO_REFRESH_TOKEN=b618eb1055db46ce803049bd0bd65400039b8e00;ae-qa-datacenter-b_USER_COOKIE=78B361086E585B2E2C1276C32EF0ECAD4AC1CABB07B65CE294BB39CBAD7226D354FCA9F4C3C27ECD05603A90486419B7C9CE3BDD09B497A0475DD94B13501F7A3AA0B0771FCCCB924177618FCFB9E5C07FF7FA685AF89E72A0665A0AFF81E28A24484EE620ED602B58C172F24A7C82DABE3DE885065D760FEA5A41E6A96D01AE42221EC6868516806F86647E555B4E3D10093FE70719D0F2DC75E825FA8B1D6C7D44CDF2999614786853B7B4DCFAA294CBEEA5A5C5E92297D24067F9E05C161569A10B6AD4893B884D34D0BA155D0E2A23B2825B74F802AAF102B5903D3CFAD79797FBE57E443CF917B178FE19322B6F75E21796742387C257EF416D5DFC9585CD03D78E30F523D1050B127B547F15E6E9BD4559B315D01B4D78E5618D6B0323482AE139714CCC3599D72FD7DB490AA5E60F2BF2EBB202AAADEDD7AF61CE90F9567FBC124AB2190514CBF1537F8378111B3449522B5B584ED032D92031A2B95D4D75EFD7F6B4F427246F9440D22CA2921C6C35515E336655F093E183317B934583D6E8D9795E537CB27DCDBD08DE7FD472A68E9D2D1FBFB08CC6800699B4386108BA3D67DEF8718FADF3811B96E6F055CB08AEDDB0F326184A044746EA991BCDE44B8D7B03B9537A014823B6AB6858A92EFFE271C26A81B2A52B0AB7044CA273;JSESSIONID=FBBE24AE7F2EBA8A6BA2E0D987FAA482;cna=2RMRGUwD+QkCASp4Smtj0rLU;xlly_s=1;ae-qa-datacenter-b_SSO_TOKEN_V2=B7377A371E8C24C67AD02A4B81328CD31945DD062D68388D8BF6F7A7BF6CAFD5FE8A72AFC0D745005FC96F2917DE76C2A9FE61C86D1751D2749B0869DF0F187E;tfstk=c50hB0avjR6jLyriceaI9kgbyAuhaPB8n0oi74zRUo8xRUDgTs01birsed2IK4I5.;isg=BNDQiEVQhNm2K1jSTogn_jfAoRgimbTjrm5dCMqjaCv6BXqveal-c-Fz3c3l1Wy7;l=eBa2RE4VjKH87brNBO5Zhurza77tXQRflsPzaNbMiInca15f_F_vuNCC_NRM8dtjgtfYzU-rNYrrMRhWJmaLSxt1nAnA6eRKmJ96-;";

    @Autowired
    private TradeOrderPriceRenderServiceImpl tradeOrderPriceRenderServiceImpl;

    @Autowired
    private TradeSubinvokesCheckServiceImpl tradeSubinvokesCheckServiceImpl;

    @Test
    public void testHttpRequest() throws Exception {
        BaseResult o = tradeOrderPriceRenderServiceImpl.getOrderRenderResult( "0b16f48616459755120814895e2245", COOKIE, "RENDER",null);

        System.out.println(o);
    }


//    @Test
//    public void testGetSubinvokesResult() throws Exception {
//        BaseResult o = tradeSubinvokesCheckServiceImpl.getSubinvokesResult( "210388be16426173406571095de24f","RENDER",null);
//
//        System.out.println(o);
//    }


}
