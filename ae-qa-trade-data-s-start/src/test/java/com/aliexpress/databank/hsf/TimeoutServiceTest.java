package com.aliexpress.databank.hsf;


import com.alibaba.global.qa.dc.model.result.ResultDTO;
import com.aliexpress.databank.hsf.provider.TimeoutServiceImpl;
import com.aliexpress.databank.price.dto.common.render.common.BaseResult;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import java.io.IOException;

@RunWith(MockitoJUnitRunner.class)
@SpringBootTest(classes = {TimeoutServiceTest.class, TimeoutServiceImpl.class})
public class TimeoutServiceTest {

    @Autowired
    @InjectMocks
    private TimeoutServiceImpl timeoutServiceImpl;

    @Before
    public void init(){
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void test() throws Exception {
        String param = "{\"orderIdStr\": ****************,\"buyerId\":**********, \"taskNameStr\": \"确认收货超时\"}";
        ResultDTO o = timeoutServiceImpl.updateTimeout(param,null);

        System.out.println(o);

    }

}
