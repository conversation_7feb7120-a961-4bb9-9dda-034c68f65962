package com.aliexpress.databank.hsf;


import com.alibaba.fastjson.JSON;
import com.alibaba.global.qa.dc.model.result.ResultDTO;
import com.aliexpress.databank.hsf.provider.OrderPriceServiceImpl;
import com.aliexpress.databank.hsf.provider.TimeoutServiceImpl;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@RunWith(MockitoJUnitRunner.class)
@SpringBootTest(classes = {OrderQueryTest.class, OrderPriceServiceImpl.class})
public class OrderQueryTest {

    @Autowired
    @InjectMocks
    private OrderPriceServiceImpl orderPriceService;
    @Before
    public void init(){
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void test() throws Exception {

        String param = "{\"orderIdStr\": ****************,\"staging\":null}";
        ResultDTO resultDTO = orderPriceService.getOrderDpPriceById(param, null);
        System.out.println(JSON.toJSONString(resultDTO));


    }

}
