package com.aliexpress.databank.hsf;

import com.alibaba.fastjson.JSON;
import com.alibaba.global.qa.dc.model.result.ResultDTO;
import com.aliexpress.databank.Application;
import com.aliexpress.databank.hsf.provider.OrderPriceServiceImpl;
import com.taobao.hsf.junit.DelegateTo;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.runners.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.IOException;


/**
 * 将HSFConfig中配置的hsf服务注入并测试
 * http://gitlab.alibaba-inc.com/middleware-container/pandora-boot/wikis/spring-boot-hsf
 * <AUTHOR>
 */
@RunWith(SpringRunner.class)
@DelegateTo(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = { OrderPriceServiceImpl.class ,Application.class,HSFTest.class })
public class HSFTest {

    @Autowired
    private OrderPriceServiceImpl orderPriceService;

    @Test
    public void testOrderPrice()  throws Exception {
        String param = "{\"orderIdStr\": 1005001524096296}";
        ResultDTO resultDTO = orderPriceService.getOrderDpPriceById(param, null);
        System.out.println(JSON.toJSONString(resultDTO));
    }
}