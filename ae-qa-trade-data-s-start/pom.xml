<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

    <parent>
        <groupId>com.aliexpress.databank</groupId>
        <artifactId>ae-qa-trade-data-s</artifactId>
        <version>1.0.2-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <artifactId>ae-qa-trade-data-s-start</artifactId>
    <packaging>jar</packaging>
    <name>ae-qa-trade-data-s-start</name>

    <dependencies>
        <dependency>
            <groupId>com.aliexpress.databank</groupId>
            <artifactId>ae-qa-trade-data-s-service</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>tmf2-core</artifactId>
                    <groupId>com.alibaba.tmf</groupId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-bom</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.aliexpress.databank</groupId>
            <artifactId>testcase</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>tmf2-core</artifactId>
                    <groupId>com.alibaba.tmf</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.taobao.pandora</groupId>
            <artifactId>taobao-hsf.sar</artifactId>
        </dependency>
        <dependency>
            <groupId>org.noear</groupId>
            <artifactId>snack3</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.middleware</groupId>
            <artifactId>sdk</artifactId>
            <version>999-not-exist-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.jayway.jsonpath</groupId>
            <artifactId>json-path</artifactId>
        </dependency>
        <dependency>
            <groupId>com.taobao.pandora</groupId>
            <artifactId>pandora-boot-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-all</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>jcl-over-slf4j</artifactId>
        </dependency>

        <!--        <dependency>-->
        <!--            <groupId>com.alibaba.global</groupId>-->
        <!--            <artifactId>global-satellite-sdk</artifactId>-->
        <!--            <version>3.0.8</version>-->
        <!--        </dependency>-->

        <dependency>
            <groupId>com.taobao.pandora</groupId>
            <artifactId>pandora-boot-starter-bom</artifactId>
            <version>${pandora-boot.version}</version>
            <type>pom</type>
            <scope>import</scope>
        </dependency>
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>pandora-apphealth-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloudnative</groupId>
            <artifactId>cloudn-application-lifecycle</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>tddl-spring-boot-starter</artifactId>
            <version>5.2.6</version>
        </dependency>

        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
            <version>1.3.5</version>
        </dependency>

        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-api</artifactId>
            <version>2.14.1</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba.saiga</groupId>
            <artifactId>saiga-link-case-api</artifactId>
            <version>1.0.1-SNAPSHOT</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>com.taobao.pandora</groupId>
                <artifactId>pandora-boot-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <!-- 解压fat jar到target/${project-name}目录 -->
            <plugin>
                <artifactId>maven-antrun-plugin</artifactId>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <configuration>
                            <tasks>
                                <unzip
                                        src="${project.build.directory}/${project.build.finalName}.${project.packaging}"
                                        dest="${project.build.directory}/ae-qa-trade-data-s"/>
                            </tasks>
                        </configuration>
                        <goals>
                            <goal>run</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
