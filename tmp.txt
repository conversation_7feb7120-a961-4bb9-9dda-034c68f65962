[INFO] Scanning for projects...
[WARNING] 
[WARNING] Some problems were encountered while building the effective model for com.aliexpress.databank:ae-qa-trade-data-s-service:jar:1.0.0-SNAPSHOT
[WARNING] 'dependencies.dependency.(groupId:artifactId:type:classifier)' must be unique: com.aliyun.openservices:aliyun-log:jar -> version [0.6.56,0.6.61] vs 0.6.33 @ com.aliexpress.databank:ae-qa-trade-data-s-service:[unknown-version], /Users/<USER>/ae-project/ae-qa-trade-data-s/ae-qa-trade-data-s-service/pom.xml, line 616, column 21
[WARNING] 
[WARNING] Some problems were encountered while building the effective model for com.aliexpress.databank:ae-qa-trade-data-s-start:jar:1.0.0-SNAPSHOT
[WARNING] 'dependencies.dependency.scope' for com.taobao.pandora:pandora-boot-starter-bom:pom must be one of [provided, compile, runtime, test, system] but is 'import'. @ com.aliexpress.databank:ae-qa-trade-data-s-start:[unknown-version], /Users/<USER>/ae-project/ae-qa-trade-data-s/ae-qa-trade-data-s-start/pom.xml, line 99, column 20
[WARNING] 
[WARNING] It is highly recommended to fix these problems because they threaten the stability of your build.
[WARNING] 
[WARNING] For this reason, future Maven versions might no longer support building such malformed projects.
[WARNING] 
[INFO] ------------------------------------------------------------------------
[INFO] Reactor Build Order:
[INFO] 
[INFO] ae-qa-trade-data-s                                                 [pom]
[INFO] ae-qa-trade-data-s-service                                         [jar]
[INFO] test-case-generate                                                 [jar]
[INFO] testcase                                                           [jar]
[INFO] ae-qa-trade-data-s-start                                           [jar]
[INFO] 
[INFO] -------------< com.aliexpress.databank:ae-qa-trade-data-s >-------------
[INFO] Building ae-qa-trade-data-s 1.0.0-SNAPSHOT                         [1/5]
[INFO] --------------------------------[ pom ]---------------------------------
[INFO] 
[INFO] --- maven-dependency-plugin:2.8:analyze-only (default-cli) @ ae-qa-trade-data-s ---
[INFO] Skipping pom project
[INFO] 
[INFO] ---------< com.aliexpress.databank:ae-qa-trade-data-s-service >---------
[INFO] Building ae-qa-trade-data-s-service 1.0.0-SNAPSHOT                 [2/5]
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- maven-dependency-plugin:2.8:analyze-only (default-cli) @ ae-qa-trade-data-s-service ---
Unable to process: com.aliexpress.databank.processor.PlaceOrderByReverseProcessor
Unable to process: com.aliexpress.databank.utils.ConvertParam
Unable to process: com.aliexpress.databank.reverse.WarrantyService
Unable to process: com.aliexpress.databank.price.impl.TradeOrderAdjustViewServiceImpl
Unable to process: com.aliexpress.databank.price.utils.ApportionPriceUtils
Unable to process: com.aliexpress.databank.price.factory.impl.calculate.OrdersSummaryPlatformCouponFeeFactory
Unable to process: com.aliexpress.databank.price.factory.impl.calculate.ShopSummaryShopCouponFeeFactory
Unable to process: com.aliexpress.databank.price.factory.impl.calculate.OrdersSummaryPlatformCodeFeeFactory
Unable to process: com.aliexpress.databank.price.factory.impl.calculate.OrdersSummaryCoinFeeFactory
Unable to process: com.aliexpress.databank.service.impl.PaymentBizServiceImpl
Unable to process: com.aliexpress.databank.service.impl.ReverseServiceImpl
Unable to process: com.aliexpress.databank.service.impl.TradeServiceImpl
Unable to process: com.aliexpress.databank.hsf.provider.TimeoutServiceImpl
Unable to process: com.aliexpress.databank.hsf.provider.OrderServiceImpl
Unable to process: com.aliexpress.databank.hsf.provider.SellerServiceImpl
Unable to process: com.aliexpress.databank.hsf.provider.CartServiceImpl
Unable to process: com.aliexpress.databank.hsf.provider.ReverseOrderServiceImpl
Unable to process: com.aliexpress.databank.hsf.provider.AddressServiceImpl
Unable to process: com.aliexpress.databank.hsf.provider.RobotServiceImpl
[WARNING] Used undeclared dependencies found:
[WARNING]    org.apache.commons:commons-lang3:jar:3.4:compile
[WARNING]    org.springframework:spring-beans:jar:4.3.25.RELEASE:compile
[WARNING]    com.alibaba.middleware:eagleeye-core-sdk:jar:*******--2020-11-release-mesh-0429:compile
[WARNING]    javax.money:money-api:jar:1.0.1:compile
[WARNING]    com.alibaba.middleware:hsf-sdk:jar:********-MESH-SNAPSHOT--2020-11-release-mesh-0429:compile
[WARNING]    com.alibaba.boot:pandora-hsf-spring-boot-autoconfigure:jar:2020-11-release-mesh-0429:compile
[WARNING]    com.aliexpress.issue:ae-issue-api-common:jar:1.0.0:compile
[WARNING]    org.slf4j:slf4j-api:jar:1.7.26:compile
[WARNING]    com.alibaba.global:global-basic-boot-money:jar:1.0.0-SNAPSHOT:compile
[WARNING]    org.apache.poi:poi:jar:5.1.0:compile
[WARNING]    com.google.code.gson:gson:jar:2.8.5:compile
[WARNING]    com.alibaba.middleware:mtop-uncenter-sdk:jar:1.0.5.3--2019-04-stable:compile
[WARNING]    com.alibaba.global:global-basic-boot-exchange:jar:3.0.8:compile
[WARNING]    com.alibaba.fastvalidator:fastvalidator-constraints:jar:2.6.2.0:compile
[WARNING]    com.google.guava:guava:jar:19.0:compile
[WARNING]    com.alibaba.toolkit.common:toolkit-common-lang:jar:1.1.4:compile
[WARNING]    org.springframework.boot:spring-boot:jar:1.5.22.RELEASE:compile
[WARNING]    com.alibaba.middleware:metaq-client-sdk:jar:4.3.6.Final--2020-11-release-mesh-0429:compile
[WARNING]    commons-lang:commons-lang:jar:2.6:compile
[WARNING]    com.alibaba.middleware:rocketmq-client-sdk:jar:4.4.1.3--2020-11-release-mesh-0429:compile
[WARNING]    org.springframework:spring-core:jar:4.3.25.RELEASE:compile
[WARNING]    com.aliexpress.uic:uic-tag-api:jar:3.1.12:compile
[WARNING]    apache-httpclient:commons-httpclient:jar:3.1:compile
[WARNING]    commons-codec:commons-codec:jar:1.10:compile
[WARNING]    org.mybatis:mybatis:jar:3.4.6:compile
[WARNING]    javax.annotation:javax.annotation-api:jar:1.2:compile
[WARNING]    org.apache.httpcomponents:httpcore:jar:4.4.11:compile
[WARNING]    org.apache.commons:commons-collections4:jar:4.4:compile
[WARNING]    com.alibaba.global:global-basic-boot-common:jar:1.2.1:compile
[WARNING]    com.alibaba.schedulerx:schedulerx-worker:jar:1.3.0.1:compile
[WARNING]    com.alibaba.global:global-g11n-sdk:jar:2.0.0-stable:compile
[WARNING]    commons-collections:commons-collections:jar:3.2.2:compile
[WARNING]    com.alibaba.global:global-reverse-api:jar:1.0.1-ae-reverse-SNAPSHOT:compile
[WARNING]    com.taobao.payment.boot:payment-boot-techsdk:jar:1.2.4:compile
[WARNING]    org.springframework:spring-context:jar:4.3.25.RELEASE:compile
[WARNING]    com.aliexpress.money.open:money:jar:2.0.3:compile
[WARNING] Unused declared dependencies found:
[WARNING]    com.aliexpress.boot:region-spring-boot-starter:jar:3.2.1-sirius:compile
[WARNING]    com.alibaba.boot:pandora-hsf-spring-boot-starter:jar:2020-11-release-mesh-0429:compile
[WARNING]    com.alibaba.platform.shared:fastjson:jar:999-not-exist-v3:compile
[WARNING]    com.taobao.payment.boot:payment-boot-core:jar:1.2.4:compile
[WARNING]    com.alibaba.global:global-basic-boot-exchange-starter:jar:3.0.8:compile
[WARNING]    com.alibaba.ae.group:ae-trade-reverse-api:jar:1.0.3-USEAST-SNAPSHOT:compile
[WARNING]    org.mybatis.spring.boot:mybatis-spring-boot-starter:jar:1.3.5:compile
[WARNING]    com.taobao.hsf:hsf-plugin-scm-22:jar:1.0.8-SNAPSHOT:compile
[WARNING]    com.alibaba.business.qa:biz-simulator-common-base:jar:1.1.20:compile
[WARNING]    com.alibaba.schedulerx:schedulerx2-spring-boot-starter:jar:1.3.0.1:compile
[WARNING]    com.aliyun:aliyun-security-client-common:jar:1.2.10:compile
[WARNING]    com.aliyun:aliyun-security-client-acsclient:jar:1.0.1:compile
[WARNING]    com.aliyun.oss:aliyun-sdk-oss:jar:3.10.2:compile
[WARNING]    com.aliyun:aliyun-security-client-oss:jar:1.0.1:compile
[WARNING]    com.aliyun:aliyun-security-client-sls-producer:jar:1.0.2:compile
[WARNING]    com.aliyun.openservices:aliyun-log-producer:jar:0.3.9:compile
[WARNING]    com.aliexpress.uic:uic-tag-client:jar:3.1.12:compile
[WARNING]    commons-io:commons-io:jar:2.9.0:compile
[WARNING]    commons-compress:commons-compress:jar:********:compile
[WARNING]    org.apache.logging.log4j:log4j-api:jar:2.14.1:compile
[INFO] 
[INFO] -------------< com.aliexpress.databank:test-case-generate >-------------
[INFO] Building test-case-generate 1.0.0-SNAPSHOT                         [3/5]
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- maven-dependency-plugin:2.8:analyze-only (default-cli) @ test-case-generate ---
Unable to process: com.aliexpress.testcase.gen.service.impl.TestCaseServiceImpl
[WARNING] Used undeclared dependencies found:
[WARNING]    commons-io:commons-io:jar:2.9.0:compile
[WARNING]    org.springframework:spring-core:jar:4.3.25.RELEASE:compile
[WARNING] Unused declared dependencies found:
[WARNING]    com.aliexpress.databank:ae-qa-trade-data-s-service:jar:1.0.0-SNAPSHOT:compile
[INFO] 
[INFO] ------------------< com.aliexpress.databank:testcase >------------------
[INFO] Building testcase 1.0.0-SNAPSHOT                                   [4/5]
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- maven-dependency-plugin:2.8:analyze-only (default-cli) @ testcase ---
Unable to process: com.aliexpress.testcase.dispute.impl.DisputeListServiceImpl
Unable to process: com.aliexpress.testcase.dispute.impl.SellerDisputeListServiceImpl
Unable to process: com.aliexpress.testcase.service.sp.impl.GuaranteeNegotiationServiceImpl
[WARNING] Used undeclared dependencies found:
[WARNING]    com.alibaba.global:global-buy-api:jar:1.1.11-UNIQUE-AE:compile
[WARNING]    org.springframework:spring-beans:jar:4.3.25.RELEASE:compile
[WARNING]    com.alibaba.global:global-basic-boot-common:jar:1.2.1:compile
[WARNING]    commons-collections:commons-collections:jar:3.2.2:compile
[WARNING]    com.alibaba.toolkit.common:toolkit-common-lang:jar:1.1.4:compile
[WARNING]    com.alibaba.middleware:mtop-uncenter-sdk:jar:1.0.5.3--2019-04-stable:compile
[WARNING]    com.alibaba.global:global-order-management-api:jar:1.1.19-AE-DP-SNAPSHOT:compile
[WARNING]    com.alibaba:fastjson:jar:1.2.60:compile
[WARNING]    org.springframework:spring-context:jar:4.3.25.RELEASE:compile
[WARNING]    com.alibaba.global.payment:global-payment-api:jar:2.1.9-API-GLOBAL-ISOLATED:compile
[WARNING]    com.aliexpress.sellerpromise:aesp-spserver-api:jar:1.0.26:compile
[WARNING]    com.alibaba.middleware:hsf-sdk:jar:********-MESH-SNAPSHOT--2020-11-release-mesh-0429:compile
[WARNING]    org.slf4j:slf4j-api:jar:1.7.26:compile
[INFO] 
[INFO] ----------< com.aliexpress.databank:ae-qa-trade-data-s-start >----------
[INFO] Building ae-qa-trade-data-s-start 1.0.0-SNAPSHOT                   [5/5]
[INFO] --------------------------------[ jar ]---------------------------------
[INFO] 
[INFO] --- maven-dependency-plugin:2.8:analyze-only (default-cli) @ ae-qa-trade-data-s-start ---
Unable to process: com.aliexpress.databank.category.EasyReturnCategory
Unable to process: com.aliexpress.databank.pressure.test.SellerOmGen
Unable to process: com.aliexpress.databank.easyreturn.warehouseid.WarehouseDiff
Unable to process: com.aliexpress.order.price.AdjustPriceTest
[WARNING] Used undeclared dependencies found:
[WARNING]    org.mybatis:mybatis-spring:jar:1.3.3:compile
[WARNING]    org.springframework:spring-beans:jar:4.3.25.RELEASE:compile
[WARNING]    javax.servlet:javax.servlet-api:jar:3.1.0:compile
[WARNING]    com.google.guava:guava:jar:19.0:compile
[WARNING]    org.springframework:spring-web:jar:4.3.25.RELEASE:compile
[WARNING]    org.springframework.boot:spring-boot:jar:1.5.22.RELEASE:compile
[WARNING]    com.alibaba:fastjson:jar:1.2.60:compile
[WARNING]    com.alibaba.toolkit.common.lang:lang:jar:1.0:compile
[WARNING]    org.springframework:spring-context:jar:4.3.25.RELEASE:compile
[WARNING]    com.taobao.pandora:pandora-boot-bootstrap:jar:2.1.15-mesh:compile
[WARNING]    org.springframework.boot:spring-boot-autoconfigure:jar:1.5.22.RELEASE:compile
[WARNING]    org.slf4j:slf4j-api:jar:1.7.26:compile
[WARNING] Unused declared dependencies found:
[WARNING]    com.aliexpress.databank:testcase:jar:1.0.0-SNAPSHOT:compile
[WARNING]    com.taobao.pandora:taobao-hsf.sar:jar:2020-11-release-mesh-0429:compile
[WARNING]    com.alibaba.middleware:sdk:jar:999-not-exist-SNAPSHOT:compile
[WARNING]    com.jayway.jsonpath:json-path:jar:2.5.0:compile
[WARNING]    com.taobao.pandora:pandora-boot-test:jar:2.1.15-mesh:test
[WARNING]    org.mockito:mockito-all:jar:1.10.19:test
[WARNING]    org.springframework.boot:spring-boot-starter:jar:1.5.22.RELEASE:compile
[WARNING]    org.slf4j:jcl-over-slf4j:jar:1.7.26:compile
[WARNING]    com.taobao.pandora:pandora-boot-starter-bom:pom:2020-11-release-mesh-0429:import
[WARNING]    com.alibaba.boot:pandora-apphealth-spring-boot-starter:jar:2020-11-release-mesh-0429:compile
[WARNING]    com.alibaba.cloudnative:cloudn-application-lifecycle:jar:1.1.0:compile
[WARNING]    org.springframework.boot:spring-boot-starter-web:jar:1.5.22.RELEASE:compile
[WARNING]    com.alibaba.boot:tddl-spring-boot-starter:jar:5.2.6:compile
[WARNING]    org.mybatis.spring.boot:mybatis-spring-boot-starter:jar:1.3.5:compile
[INFO] ------------------------------------------------------------------------
[INFO] Reactor Summary:
[INFO] 
[INFO] ae-qa-trade-data-s 1.0.0-SNAPSHOT .................. SUCCESS [  0.805 s]
[INFO] ae-qa-trade-data-s-service ......................... SUCCESS [  7.553 s]
[INFO] test-case-generate ................................. SUCCESS [  4.036 s]
[INFO] testcase ........................................... SUCCESS [  3.776 s]
[INFO] ae-qa-trade-data-s-start 1.0.0-SNAPSHOT ............ SUCCESS [  4.780 s]
[INFO] ------------------------------------------------------------------------
[INFO] BUILD SUCCESS
[INFO] ------------------------------------------------------------------------
[INFO] Total time: 21.591 s
[INFO] Finished at: 2022-01-29T15:53:53+08:00
[INFO] ------------------------------------------------------------------------
