package com.aliexpress.testcase.module;

import lombok.Data;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

@Data
public class AutoTestResponse {

    private boolean pass;
    private String scenario;
    private Long reverseOrderLineId;
    private String description;
    private List<String> errorMessages;


    public List<String> appendErrorMessage(String errorMessage) {
        if (!CollectionUtils.isNotEmpty(errorMessages)) {
        errorMessages = new ArrayList<>();
    }
        errorMessages.add(errorMessage);
        return errorMessages;
}


    public List<String> appendErrorMessages(List<String> errorMessage) {
        if (!CollectionUtils.isNotEmpty(errorMessages)) {
            errorMessages = new ArrayList<>();
        }
        errorMessages.addAll(errorMessage);
        return errorMessages;
    }
}
