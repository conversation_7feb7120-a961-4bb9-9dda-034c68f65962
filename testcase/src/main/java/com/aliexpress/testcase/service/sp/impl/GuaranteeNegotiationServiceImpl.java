package com.aliexpress.testcase.service.sp.impl;

import com.alibaba.common.lang.StringUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.intl.ae.biz.promisetemplate.share.service.interfaces.PromiseServiceForMtop;
import com.aliexpress.testcase.constant.Constant;
import com.aliexpress.testcase.gen.model.CheckPoint;
import com.aliexpress.testcase.gen.model.Module;
import com.aliexpress.testcase.gen.model.SubModule;
import com.aliexpress.testcase.gen.model.TestCase;
import com.aliexpress.testcase.gen.service.TestCaseService;
import com.aliexpress.testcase.service.sp.CommonService;
import com.aliexpress.testcase.service.sp.GuaranteeNegotiationService;
import com.aliexpress.testcase.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
public class GuaranteeNegotiationServiceImpl implements GuaranteeNegotiationService {

    @Autowired
    private TestCaseService testCaseService;

    @Autowired
    private CommonService commonService;

    @Override
    public SubModule testSign(Module module) throws IOException {
        final String moduleName = "签约功能";
        Optional<SubModule> subModule = testCaseService.getSubModule(module, "签约功能");
        SubModule sign = testCaseService.validSubModule(subModule, moduleName);
        if (!sign.isSuccess()) {
            return sign;
        }
        boolean isSigned = commonService.checkSellerPromiseStatus(Constant.GUARANTEE_PROMISE_ID, Constant.SELLER_ID_1);
        if (isSigned) {
            // current status check - joined
            TestCase alreadySigned = testCaseService.getTestCase(sign, 2, "卖家已加入售后宝场景");
            if (!alreadySigned.isSuccess()) {
                sign.setSuccess(false);
                sign.setComment(StringUtils.appendStr(sign.getComment(), "Fail to get testcase about 卖家已加入售后宝场景"));
            } else {
                // detail
                List<JSONObject> promiseDetails = commonService.getPromiseDetail(Constant.GUARANTEE_PROMISE_ID, Constant.SELLER_ID_1);
                if (promiseDetails == null) {
                    sign.setSuccess(false);
                    sign.setComment(StringUtils.appendStr(sign.getComment(),
                            "Fail to get promiseDetails. PromiseID: 21. Error Info: " + promiseDetails.get(0).toJSONString()));
                } else {
                    promiseDetails.forEach(promiseDetail -> {
//                        CheckPoint checkPoint = c
                    });
                }


            }
        } else {
            TestCase unsigned = testCaseService.getTestCase(sign, 1, "卖家未加入售后宝场景");
        }
        return null;
    }

    @Override
    public SubModule testAcl(Module module) throws IOException {
        Optional<SubModule> subModule = testCaseService.getSubModule(module, "子账号权限校验功能");
        return null;
    }

    public static void main(String[] args) {
        String str = "{\n" +
                "  \"data\": [\n" +
                "    {\n" +
                "      \"name\": \"promiseId\",\n" +
                "      \"content\": 21\n" +
                "    },\n" +
                "    {\n" +
                "      \"name\": \"title\",\n" +
                "      \"content\": \"After-sales treasure dispute order negotiation function\"\n" +
                "    },\n" +
                "    {\n" +
                "      \"visible\": true,\n" +
                "      \"name\": \"description\",\n" +
                "      \"content\": \"<p>A. After-sales treasure dispute orders that are agreed upon will not be included in the initiation rate of product non-version disputes. Based on the buyer's portrait and background, you can actively negotiate with the buyer through your professionalism to better resolve the buyer Problems, reduce the dispute rate. If there is a consensus and a refund is involved, the merchant needs to bear it;</p>\\n<p><br>B. If the negotiation is inconsistent, the dispute will automatically go to arbitration after 3 days, and the platform will still handle the business on behalf of the merchant and assume the corresponding refund. At this time, the merchant will not respond to the entrance.<br>Note: If you (store dimension) accumulate 2 negotiated orders within 1 mon</p>\\n<p>th and the response timeout occurs (that is, after the buyer initiates a dispute, the merchant does not respond within 3 days, resulting in an increase in dispute timeout arbitration), the platform will immediately cancel the merchant Participating in the after-sales Baokai negotiation project qualification, you can reapply to join after 90 days, but if the merchant is disqualified twice in the history, the merchant will permanently lose the negotiation opportunity, can no longer apply to join the negotiation, nor can it reduce the dispute rate of goods against the board through negotiation. Please note.<br><br><br>Intro video: https://university.aliexpress.com/course/learn?spm=ae-cn-pc.ae-university-cn-pc-list.courserlist_courses.1.6fca4151dH17gn&amp;id=1365&amp;type=video</p>\"\n" +
                "    },\n" +
                "    {\n" +
                "      \"visible\": true,\n" +
                "      \"name\": \"serviceStatus\"\n" +
                "    },\n" +
                "    {\n" +
                "      \"visible\": true,\n" +
                "      \"name\": \"serviceExit\"\n" +
                "    },\n" +
                "    {\n" +
                "      \"visible\": true,\n" +
                "      \"name\": \"customTipsForQuitService\",\n" +
                "      \"content\": \"After exiting the negotiation function, the newly generated after-sales treasure dispute orders will be completely handled by the platform, and the dispute rate will no longer be exempt. Are you sure to close it? Please note: After exiting the negotiation function, you need to wait 7 days before joining again.\"\n" +
                "    },\n" +
                "    {\n" +
                "      \"visible\": false,\n" +
                "      \"name\": \"btmJoinAction\"\n" +
                "    },\n" +
                "    {\n" +
                "      \"visible\": false,\n" +
                "      \"name\": \"topJoinAction\"\n" +
                "    },\n" +
                "    {\n" +
                "      \"visible\": false,\n" +
                "      \"name\": \"serviceCountries\",\n" +
                "      \"content\": null\n" +
                "    },\n" +
                "    {\n" +
                "      \"visible\": false,\n" +
                "      \"name\": \"serviceCategories\",\n" +
                "      \"content\": null\n" +
                "    },\n" +
                "    {\n" +
                "      \"visible\": true,\n" +
                "      \"name\": \"serviceTime\",\n" +
                "      \"content\": \"2021-01-07 05:28:22\"\n" +
                "    },\n" +
                "    {\n" +
                "      \"visible\": false,\n" +
                "      \"name\": \"otherService\"\n" +
                "    }\n" +
                "  ],\n" +
                "  \"success\": true,\n" +
                "  \"error\": null,\n" +
                "  \"type\": null,\n" +
                "  \"class\": \"com.alibaba.intl.ae.biz.promisetemplate.share.dto.DadaResultVO\"\n" +
                "}";
//        JSON
    }

}
