package com.aliexpress.testcase.service.sp.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.intl.ae.biz.promisetemplate.share.dto.DadaResultVO;
import com.alibaba.intl.ae.biz.promisetemplate.share.dto.result.SpResult;
import com.alibaba.intl.ae.biz.promisetemplate.share.service.interfaces.PromiseServiceForMtop;
import com.aliexpress.testcase.service.sp.CommonService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@Slf4j
public class CommonServiceImpl implements CommonService {

    @Autowired
    private PromiseServiceForMtop promiseServiceForMtop;

    @Override
    public boolean checkSellerPromiseStatus(Long promiseId, Long sellerId) {
        SpResult<Boolean> isSigned = promiseServiceForMtop.isSigned(sellerId, promiseId);
        return isSigned.getData();
    }

    @Override
    public List<JSONObject> getPromiseDetail(Long promiseId, Long sellerId) {
        DadaResultVO<List<JSONObject>> promiseDetailResult = promiseServiceForMtop.getPromiseDetail(sellerId, promiseId);
        if (promiseDetailResult.getSuccess() && !promiseDetailResult.getData().isEmpty()) {
            return promiseDetailResult.getData();
        }
        List<JSONObject> jsonObjects = new ArrayList<>();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("error", promiseDetailResult.getError());
        jsonObjects.add(jsonObject);
        return jsonObjects;

    }
}
