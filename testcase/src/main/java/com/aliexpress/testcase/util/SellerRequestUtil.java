package com.aliexpress.testcase.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.taobao.mtop.api.util.StringUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class SellerRequestUtil {
//    创建方法：卖家纠纷总数的请求
    public static String getSellerDisputeCountRequest(Long sellerId){
//创建集合放请求的类型，往里面新增请求参数的类型，在hsf里面可查到
        List<String> argsTypes = new ArrayList<>();
        argsTypes.add("com.alibaba.ae.reverse.model.request.AeSellerStatusScreenItemsQueryRequest");
//创建集合放请求体，根据开发提供的接口往里面新增key和value
        List<Object> argBody = new ArrayList<>();
        JSONObject requestJson = new JSONObject();
        requestJson.put("userId",sellerId);
        argBody.add(requestJson.toJSONString());
//创建map放请求参数，里面放请求的类型和请求体
        Map<String,Object> requestParams = new ConcurrentHashMap<>();
        requestJson.put("argsTypes",argsTypes);
        requestJson.put("argsObjs",argBody);
        return JSON.toJSONString(requestParams);
    }

//    创建方法：卖家纠纷列表的请求
    public static String getSellerDisputeListRequest(Long sellerId,String tradeOrderId,Integer reverseBizType,String buyerName,
                                                     Integer reverseStatus,String itemId,Integer current,
                                                     Integer size,Integer pageNo,
                                                     String sortOrder,Integer screenItemsStatus,String sortField
                                                     ){
//创建集合放请求的类型，往里面新增请求参数的类型，在hsf里面可查到
        List<String> argsTypes = new ArrayList<>();
        argsTypes.add("com.alibaba.ae.reverse.model.request.AeSellerPageQueryRequest");

//创建集合放请求体，根据开发提供的接口往里面新增key和value
        List<Object> argBody = new ArrayList<>();
        JSONObject requestJson = new JSONObject();
        if (tradeOrderId != null && tradeOrderId != "0"){
            requestJson.put("tradeOrderId",tradeOrderId);
        }
        requestJson.put("reverseBizType",reverseBizType);
        if (StringUtil.isNotBlank(buyerName)){
            requestJson.put("buyerName",buyerName);
        }
        requestJson.put("reverseStatus",reverseStatus);
        requestJson.put("itemId",itemId);

        requestJson.put("current",current == 0 ? 1 : current);
        requestJson.put("sellerId",sellerId);
        requestJson.put("size",size == 0 ? 20 : size );
        requestJson.put("pageNo",pageNo == 0 ? 1 : pageNo );
        requestJson.put("sortOrder",StringUtil.isNotBlank(sortOrder) ? sortOrder : "DESC");//如果为空，取sort的值；如果不为空，取DESC
        requestJson.put("sellerId",sellerId);
        requestJson.put("screenItemsStatus",screenItemsStatus);
        requestJson.put("sortField",StringUtil.isNotBlank(sortField) ? sortOrder : "gmt");

        argBody.add(requestJson.toJSONString());

//创建map放请求参数，里面放请求的类型和请求体
        Map<String,Object> requestParams = new ConcurrentHashMap<>();
        requestParams.put("argsTypes",argsTypes);
        requestParams.put("argsObjs",argBody);
        return JSON.toJSONString(requestParams);
    }

}
