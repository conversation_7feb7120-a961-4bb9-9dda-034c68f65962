package com.aliexpress.testcase.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.taobao.mtop.api.util.StringUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class RequestUtil {

    public static String getBuyerDisputeCountRequest(Long buyerId) {
        List<String> argsTypes = new ArrayList<>();
        argsTypes.add("com.alibaba.ae.reverse.model.request.AeBuyerStatusScreenItemsQueryRequest");

        List<Object> argBody = new ArrayList<>();
        JSONObject requestJson = new JSONObject();
        requestJson.put("userId", buyerId);
        argBody.add(requestJson.toJSONString());

        Map<String, Object> requestParams = new ConcurrentHashMap<>();
        requestParams.put("argsTypes", argsTypes);
        requestParams.put("argsObjs", argBody);
        return JSON.toJSONString(requestParams);
    }

    public static String getBuyerDisputeListRequest(Long buyerId, Long orderId, String supplierName, int reverseStatus,
                                                    int pageNo, int pageSize, String sort) {
        List<String> argsTypes = new ArrayList<>();
        argsTypes.add("com.alibaba.ae.reverse.model.request.AeBuyerPageQueryRequest");

        List<Object> argBody = new ArrayList<>();
        JSONObject requestJson = new JSONObject();
        if (StringUtil.isNotBlank(supplierName)) {
            requestJson.put("supplierName", supplierName);
        }
        if (orderId != null && orderId != 0L) {
            requestJson.put("tradeOrderId", orderId);
        }
        requestJson.put("reverseStatus", reverseStatus);
        requestJson.put("buyerId", buyerId);
        requestJson.put("size", pageSize == 0 ? 20 : pageSize);
        requestJson.put("pageNo", pageNo == 0 ? 20 : 1);
        requestJson.put("sortOrder", StringUtil.isNotBlank(sort) ? sort : "DESC");
        argBody.add(requestJson.toJSONString());

        Map<String, Object> requestParams = new ConcurrentHashMap<>();
        requestParams.put("argsTypes", argsTypes);
        requestParams.put("argsObjs", argBody);
        return JSON.toJSONString(requestParams);
    }

}
