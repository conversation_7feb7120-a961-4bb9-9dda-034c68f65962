package com.aliexpress.testcase.util;

import com.alibaba.common.lang.StringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.taobao.hsf.app.api.util.HSFApiConsumerBean;
import com.taobao.hsf.remoting.service.GenericService;

import java.util.ArrayList;
import java.util.List;

public class HsfUtil {

    public static JSONObject genericServiceInvoke(
            String serviceName, String serviceVersion, String methodName, String groupId,
            String[] parameterType, Object[] parameterValue) throws Exception {
        HSFApiConsumerBean consumerBean = createConsumerBean(serviceName, serviceVersion, groupId);
        GenericService genericService = (GenericService) consumerBean.getObject();
        Object result = genericService.$invoke(methodName, parameterType, parameterValue);
        return JSONObject.parseObject(JSON.toJSONString(result));
    }

    public static HSFApiConsumerBean createConsumerBean(String serviceName, String serviceVersion, String groupId) throws Exception {
        HSFApiConsumerBean consumerBean = new HSFApiConsumerBean();
        consumerBean.setInterfaceName(serviceName);
        consumerBean.setGeneric("true");
        consumerBean.setVersion(serviceVersion);
        consumerBean.setGroup(groupId);
        consumerBean.setClientTimeout(30000);
        consumerBean.init();
        return consumerBean;
    }

    public static JSONObject getDisputeListCountRes(Long buyerId) throws Exception {
        String serviceName = "com.alibaba.ae.tradeReverse.api.QueryReverseOrderFacade";
        String serviceVersion = "1.0.0";
        String methodName = "queryReverseOrderStatusScreenItemsForBuyer";
        String groupId = "HSF";
        String[] parameterTypes = {"com.alibaba.ae.tradeReverse.model.request.BuyerStatusScreenItemsQueryRequest"};
        List<Object> paramValues = new ArrayList<>();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("buyerId", buyerId);
        paramValues.add(jsonObject.toJavaObject(Object.class));
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, parameterTypes, paramValues.toArray());
    }

    /**
     * 履约推单-废弃
     * https://yuque.antfin.com/wml01028991/ph8tnu/szu1ve?#rhxak
     * 文档 履约退款
     * @param buyerId
     * @param foId
     * @return
     * @throws Exception
     */
    public static JSONObject acceptOrder(Long buyerId, String foId) throws Exception {
        String serviceName = "com.ascp.uop.kernel.share.service.UopFulfillmentAcceptService";
        String serviceVersion = "1.0.0";
        String methodName = "acceptOrder";
        String groupId = "HSF";
        String[] parameterTypes = {"com.ascp.uop.kernel.share.common.model.acceptorder.req.AcceptOrderReqDTO", "com.ascp.uop.kernel.common.model.InvokeInfoDTO"};
        List<Object> paramValues = new ArrayList<>();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("buyerId", buyerId);
        jsonObject.put("foId", foId);
        paramValues.add(jsonObject.toJavaObject(Object.class));
        paramValues.add(new JSONObject().toJavaObject(Object.class));
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, parameterTypes, paramValues.toArray());
    }

    public static JSONObject getBuyerDisputeListRequest(Long buyerId, int reverseStatus, int pageNo, int pageSize,
                                                        String sort, String tradeOrderId, String shopName) throws Exception {
        String serviceName = "com.alibaba.ae.tradeReverse.api.QueryReverseOrderFacade";
        String serviceVersion = "1.0.0";
        String methodName = "queryReverseOrderPageListForBuyer";
        String groupId = "HSF";
        String[] parameterTypes = {"com.alibaba.ae.tradeReverse.model.request.BuyerPageQueryRequest"};
        List<Object> paramValues = new ArrayList<>();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("buyerId", buyerId);
        jsonObject.put("reverseStatus", reverseStatus);
        jsonObject.put("size", pageSize);
        jsonObject.put("pageNo", pageNo);
        jsonObject.put("sortOrder", StringUtil.isNotBlank(sort) ? sort : "DESC");
        if (StringUtil.isNotBlank(tradeOrderId)) {
            jsonObject.put("tradeOrderId", tradeOrderId);
        }
        if (StringUtil.isNotBlank(shopName)) {
            jsonObject.put("shopName", shopName);
        }
        paramValues.add(jsonObject.toJavaObject(Object.class));
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, parameterTypes, paramValues.toArray());
    }

    public static JSONObject getSellerDisputeListCountRes(Long sellerId) throws Exception {
        String serviceName = "com.alibaba.ae.tradeReverse.api.QueryReverseOrderFacade";
        String serviceVersion = "1.0.0";
        String methodName = "queryReverseOrderStatusScreenItemsForSeller";
        String groupId = "HSF";
        String[] parameterTypes = {"com.alibaba.ae.tradeReverse.model.request.SellerStatusScreenItemsQueryRequest"};
        List<Object> paramValues = new ArrayList<>();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("sellerId", sellerId);
        paramValues.add(jsonObject.toJavaObject(Object.class));
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, parameterTypes, paramValues.toArray());
    }

    public static JSONObject getSellerDisputeListRequest(String tradeOrderId, int pageSize, int reverseBizType, String language,
                                                         String buyerName, int reverseStatus, String itemId, int current,
                                                         Long sellerId, int size, int pageNo, int screenItemsStatus,
                                                         String sortOrder, String sortField
    ) throws Exception {
        String serviceName = "com.alibaba.ae.tradeReverse.api.QueryReverseOrderFacade";
        String serviceVersion = "1.0.0";
        String methodName = "queryReverseOrderPageListForSeller";
        String groupId = "HSF";
        String[] parameterTypes = {"com.alibaba.ae.tradeReverse.model.request.SellerPageQueryRequest"};
        List<Object> paramValues = new ArrayList<>();
        JSONObject jsonObject = new JSONObject();

        if (StringUtil.isNotBlank(tradeOrderId)) {
            jsonObject.put("tradeOrderId", tradeOrderId);
        }
        jsonObject.put("pageSize", pageSize);
        jsonObject.put("reverseBizType", reverseBizType);
        jsonObject.put("language", language);
        if (StringUtil.isNotBlank(buyerName)) {
            jsonObject.put("buyerName", buyerName);
        }
        jsonObject.put("reverseStatus", reverseStatus);
        if (StringUtil.isNotBlank(itemId)) {
            jsonObject.put("itemId", itemId);
        }
        jsonObject.put("current", current);
        jsonObject.put("sellerId", sellerId);
        jsonObject.put("size", size);
        jsonObject.put("pageNo", pageNo);
        jsonObject.put("screenItemsStatus", screenItemsStatus);
        jsonObject.put("sortOrder", StringUtil.isNotBlank(sortOrder) ? sortOrder : "DESC");
        if (StringUtil.isNotBlank(sortField)) {
            jsonObject.put("sortField", sortField);
        }

        paramValues.add(jsonObject.toJavaObject(Object.class));
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, parameterTypes, paramValues.toArray());
    }

    public static JSONObject getReverseOrderLineRes(Long buyerId, Long reverseOrderLineId) throws Exception {
        String serviceName = "com.alibaba.global.reverse.api.facade.GlobalReverseOrderQueryForBuyerFacade";
        String serviceVersion = "1.0.0.global";
        String methodName = "queryReverseOrderLineById";
        String groupId = "HSF";
        String[] parameterTypes = {"java.lang.Long", "java.lang.Long"};
        List<Object> paramValues = new ArrayList<>();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("buyerId", buyerId);
        jsonObject.put("reverseOrderLineId", reverseOrderLineId);
        paramValues.add(jsonObject.toJavaObject(Object.class));
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, parameterTypes, paramValues.toArray());
    }

}
