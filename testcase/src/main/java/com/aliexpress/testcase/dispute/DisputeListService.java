package com.aliexpress.testcase.dispute;

import com.aliexpress.testcase.module.AutoTestResponse;

import java.util.List;

/**
 *
 *     All(1,"all","All"),
 *     IN_NEGOTIATION(2,"in_negotiation", "In negotiation"),
 *     IN_ARBITRATION(3,"in_arbitration", "In arbitration"),
 *     WAIT_SELLER_SET_RETURN_ADDRESS(4, "wait_seller_set_return_address","Wait seller set return address"),
 *     WAIT_BUYER_RETURN_GOODS(5,"wait_buyer_return_goods", "Wait buyer return goods"),
 *     WAIT_SELLER_RECEIVE_GOODS(6,"wait_seller_receive_goods", "Wait seller receive goods"),
 *     WAIT_WAREHOUSE_RECEIVE_GOODS(7,"wait_warehouse_receive_goods", "Wait warehouse receive goods"),
 *     FINISH(8,"finish", "finish"),
 *     CANCEL(9,"cancel", "cancel"),
 *     WAIT_FOR_YOUR_RESPONSE(10,"wait_for_your_response", "Wait for your response"),
 */
public interface DisputeListService {

    List<AutoTestResponse> buyerDisputeListAutoTest(Long buyerId) throws Exception;

}
