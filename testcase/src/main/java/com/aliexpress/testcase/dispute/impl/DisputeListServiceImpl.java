package com.aliexpress.testcase.dispute.impl;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.common.lang.StringUtil;
import com.alibaba.ecommerce.module.Response;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.global.order.management.api.facade.OrderQueryForBuyerFacade;
import com.alibaba.global.order.management.api.model.*;
import com.alibaba.global.order.management.api.request.QueryESTradeOrdersByIdRequest;
import com.aliexpress.testcase.constant.DisputeError;
import com.aliexpress.testcase.dispute.DisputeListService;
import com.aliexpress.testcase.module.AutoTestResponse;
import com.aliexpress.testcase.util.HsfUtil;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.money.MonetaryAmount;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * ReverseStatusEnum
 * REQUEST_INITIATE(1, "request initiate"),
 * REQUEST_REJECT(2, "request reject"),
 * REQUEST_CANCEL(3, "request cancel"),
 * CANCEL_SUCCESS(4, "cancel success"),
 * REPLACE_PENDING(5, "replace pending"),
 * REPLACE_WAIT_AUDIT(6, "replace wait audit"),
 * REPLACE_SUCCESS(7, "replace success"),
 * REFUND_PENDING(8, "refund pending"),
 * REFUND_AUTHORIZED(9, "refund authorized"),
 * REFUND_SUCCESS(10, "refund success"),
 * REFUND_REJECT(11, "refund reject"),
 * REQUEST_COMPLETE(12, "request complete"),
 * SELLER_AGREE_RETURN(13, "seller agree return"),
 * SELLER_REJECT_RETURN(17, "seller reject return"),
 * BUYER_RETURN_ITEM(14, "buyer return item"),
 * SELLER_AGREE_REFUND(15, "seller agree refund"),
 * SELLER_REJECT_REFUND(18, "seller reject refund"),
 * CS_APPROVING(19, "customer service approving");
 */
@HSFProvider(serviceInterface = DisputeListService.class, serviceGroup = "HSF", serviceVersion = "1.0.0", clientTimeout = 20000)
public class DisputeListServiceImpl implements DisputeListService {

    @Autowired
    private OrderQueryForBuyerFacade orderQueryForBuyerFacade;


    /**
     * BuyerSearchStatus
     * All(1,"All"),
     * DISPUTE_IN_PROCESS(2,"Dispute in process"),
     * AWAITING_RETURN_GOODS(3,"Awaiting return goods"),
     * FINISHED(4, "finished"),
     * CANCELED(5,"canceled");
     */
    @Override
    public List<AutoTestResponse> buyerDisputeListAutoTest(Long buyerId) throws Exception {
        List<AutoTestResponse> autoTestResponses = new ArrayList<>();
        JSONObject countRes = HsfUtil.getDisputeListCountRes(buyerId);
        List<AutoTestResponse> inProgressAutoTests = buyerInProgressListAutoTest(getCount(countRes, 2), buyerId);
        autoTestResponses.addAll(inProgressAutoTests);
        List<AutoTestResponse> awaitingShipmentAutoTests = buyerAwaitingShipmentReturnAutoTest(getCount(countRes, 3), buyerId);
        autoTestResponses.addAll(awaitingShipmentAutoTests);
        return autoTestResponses;
    }

    private int getCount(JSONObject countRes, int disputeStatus) {
        return Integer.parseInt(countRes.getJSONObject("module").getJSONObject("reverseOrderStatusScreenItems").get(String.valueOf(disputeStatus)).toString());
    }


    private List<AutoTestResponse> buyerInProgressListAutoTest(int count, Long buyerId) throws Exception {
        List<AutoTestResponse> autoTestResponses = new ArrayList<>();

        List<JSONObject> listResponses = getListResByReverseStatus(2, count, 10, buyerId);

        // check count match
        AutoTestResponse countMatchAutoTestResponse = checkBuyerDisputeCount(count, listResponses);
        autoTestResponses.add(countMatchAutoTestResponse);

        // check list info
        List<AutoTestResponse> listAutoTestResponse = checkBuyerInProgressDisputeList(listResponses);
        autoTestResponses.addAll(listAutoTestResponse);

        // check search
        List<AutoTestResponse> inProgressSearchAutoTestResponses = checkSearch(buyerId, 2, listResponses);
        autoTestResponses.addAll(inProgressSearchAutoTestResponses);
        // check search
        List<AutoTestResponse> awaitingShipmentSearchAutoTestResponses = checkSearch(buyerId, 3, listResponses);
        autoTestResponses.addAll(awaitingShipmentSearchAutoTestResponses);

        return autoTestResponses;
    }

    private List<JSONObject> getListResByReverseStatus(int reverseStatus, int count, int pageSize, Long buyerId) throws Exception {
        List<JSONObject> listResponses = new ArrayList<>();
        int totalPage = count / pageSize + 1;
        for (int i = 1; i <= totalPage; i++) {
            JSONObject listRes = HsfUtil.getBuyerDisputeListRequest(buyerId, reverseStatus, i, pageSize, "", "", "");
            listResponses.add(listRes);
        }
        return listResponses;
    }

    private AutoTestResponse checkBuyerDisputeCount(int count, List<JSONObject> listResponses) {
        AutoTestResponse autoTestResponse = new AutoTestResponse();
        autoTestResponse.setPass(true);
        autoTestResponse.setDescription("买家侧纠纷list：接口返回总数量和列表返回的总数量对比场景");
        List<String> errorMessages = new ArrayList<>();
        int listTotal = getCountByDisputeList(listResponses);
        if (count != listTotal) {
            autoTestResponse.setPass(false);
            errorMessages.add(
                    "Buyer Dispute List - In Progress : Count Not Match! Count info: " + count + ". Dispute List Count Info: " + listTotal);
        }
        autoTestResponse.appendErrorMessages(errorMessages);
        return autoTestResponse;
    }

    private int getCountByDisputeList(List<JSONObject> listResponses) {
        int totalSize = 0;
        for (JSONObject listResponse : listResponses) {
            totalSize += listResponse.getJSONObject("module").getJSONArray("items").size();
        }
        return totalSize;
    }


    private List<AutoTestResponse> checkBuyerInProgressDisputeList(List<JSONObject> listResponses) {
        List<AutoTestResponse> autoTestResponses = new ArrayList<>();
        listResponses.forEach(response -> {
            AutoTestResponse autoTestResponse = new AutoTestResponse();
            List<String> errorMessages = new ArrayList<>();
            autoTestResponse.setDescription("Buyer In progress: 买家侧纠纷list元素校验");
            if ("true".equals(response.getString("success"))) {
                JSONObject module = response.getJSONObject("module");
                if (module != null) {
                    JSONArray items = module.getJSONArray("items");
                    for (int i = 0; i < items.size(); i++) {
                        JSONObject item = items.getJSONObject(i);
                        AutoTestResponse fieldsAutoTestResponses = checkInProgressFields(item);
                        autoTestResponses.add(fieldsAutoTestResponses);
                    }
                }
            } else {
                errorMessages.add(DisputeError.ERROR_RES_DISPUTE + ". Res: " + JSON.toJSONString(response));
                autoTestResponse.setErrorMessages(errorMessages);
                autoTestResponse.setPass(false);
            }

        });
        return autoTestResponses;
    }

    private AutoTestResponse checkInProgressFields(JSONObject item) {
        AutoTestResponse autoTestResponse;
        List<String> errorMessages = new ArrayList<>();
        JSONArray reverseOrderLines = item.getJSONArray("reverseOrderLines");
        // check reverseOrderLine is legal
        autoTestResponse = checkReverseOrderLines(reverseOrderLines);
        if (!autoTestResponse.isPass()) {
            return autoTestResponse;
        }
        JSONObject reverseOrderLine = reverseOrderLines.getJSONObject(0);
        Long tradeOrderLineId = reverseOrderLine.getLong("tradeOrderLineId");
        Long reverseOrderLineId = reverseOrderLine.getLong("reverseOrderLineId");
        // set scenarios
        autoTestResponse.setScenario(getDisputeScenarios(reverseOrderLine.getString("reverseStatus"),
                reverseOrderLine.getString("reverseBizType"), reverseOrderLine.getString("timeoutType"),
                reverseOrderLine.getString("reverseType")));
        autoTestResponse.setReverseOrderLineId(reverseOrderLineId);

        List<String> commonFieldsErrorMsg = checkCommonFields(item, reverseOrderLine, reverseOrderLineId);
        if (CollectionUtils.isNotEmpty(commonFieldsErrorMsg)) {
            errorMessages.addAll(commonFieldsErrorMsg);
        }
        // check button
        List<String> buttonErrorMsgs = checkButton(reverseOrderLine.getJSONArray("buttonDTOList"),
                reverseOrderLine.getString("timeoutType"), tradeOrderLineId, reverseOrderLineId);
        if (CollectionUtils.isNotEmpty(buttonErrorMsgs)) {
            errorMessages.addAll(buttonErrorMsgs);
        }
        // check reverseStatus
        List<String> reverseStatusErrorMsg = checkReverseStatus(reverseOrderLine, 2, tradeOrderLineId, reverseOrderLineId);
        if (CollectionUtils.isNotEmpty(reverseStatusErrorMsg)) {
            errorMessages.addAll(reverseStatusErrorMsg);
        }

        if (CollectionUtils.isNotEmpty(errorMessages)) {
            autoTestResponse.setErrorMessages(errorMessages);
            autoTestResponse.setPass(false);
        } else {
            autoTestResponse.setPass(true);
        }
        return autoTestResponse;

    }

    private List<String> checkButton(JSONArray buttonDTOs, String timeoutType, Long tradeOrderLineId, Long reverseOrderLineId) {
        List<String> errorMessages = new ArrayList<>();
        if (buttonDTOs == null || buttonDTOs.size() < 1) {
            errorMessages.add(DisputeError.ILLEGAL_REVERSE_BUTTON + ". Button of ReverseOrderLine is illegal. Res: " + JSON.toJSONString(buttonDTOs)
                    + ". TradeOrderLineId: " + tradeOrderLineId + ". ReverseOrderLineId: " + reverseOrderLineId);
        } else {
            Set<String> buttonTypes = getButtonTypes(buttonDTOs);
            if (!buttonTypes.contains("view_details")) {
                errorMessages.add(DisputeError.ILLEGAL_REVERSE_BUTTON + ". Missing button view_details. Res: "
                        + JSON.toJSONString(buttonDTOs)
                        + ". TradeOrderLineId: " + tradeOrderLineId + ". ReverseOrderLineId: " + reverseOrderLineId);
            }
            if (StringUtil.isNotBlank(timeoutType) && (timeoutType.equals("seller_confirm_goods_timeout_job")
                    || timeoutType.equals("lrd_warehouse_confirm_goods") || timeoutType.equals("seller_return_address_timeout_job"))) {
                if (buttonDTOs.size() != 1 || !buttonDTOs.getJSONObject(0).getString("type").equals("view_details")) {
                    errorMessages.add(DisputeError.ILLEGAL_REVERSE_BUTTON + ". Button size of ReverseOrderLine should be 1 and type should be view_details. Res: "
                            + JSON.toJSONString(buttonDTOs)
                            + ". TradeOrderLineId: " + tradeOrderLineId + ". ReverseOrderLineId: " + reverseOrderLineId);
                }
            } else {
                if (buttonDTOs.size() != 2) {
                    errorMessages.add(DisputeError.ILLEGAL_REVERSE_BUTTON + ". Button size of ReverseOrderLine should be 2. Res: "
                            + JSON.toJSONString(buttonDTOs)
                            + ". TradeOrderLineId: " + tradeOrderLineId + ". ReverseOrderLineId: " + reverseOrderLineId);
                }
                if (!buttonTypes.contains("immediately_deal_with")) {
                    errorMessages.add(DisputeError.ILLEGAL_REVERSE_BUTTON + ". Missing button immediately_deal_with. Res: "
                            + JSON.toJSONString(buttonDTOs)
                            + ". TradeOrderLineId: " + tradeOrderLineId + ". ReverseOrderLineId: " + reverseOrderLineId);
                }
            }
        }
        return errorMessages;
    }

    private Set<String> getButtonUrl(JSONArray buttonDTOs) {
        Set<String> urls = new HashSet<>();
        for (int i = 0; i < buttonDTOs.size(); i++) {
            urls.add(buttonDTOs.getJSONObject(i).getString("type"));
        }
        return urls;
    }

    private Set<String> getButtonTypes(JSONArray buttonDTOs) {
        Set<String> types = new HashSet<>();
        for (int i = 0; i < buttonDTOs.size(); i++) {
            types.add(buttonDTOs.getJSONObject(i).getString("type"));
        }
        return types;
    }

    private String getDisputeScenarios(String reverseStatus, String reverseBizType, String timeoutType, String reverseType) {
        StringBuilder sb = new StringBuilder();
        sb.append(reverseStatus)
                .append("-")
                .append(reverseBizType)
                .append("-")
                .append(timeoutType)
                .append("-")
                .append(reverseType);
        return sb.toString();
    }

    private List<String> checkItem(TradeOrderLineDTO tradeOrderLineDTO, ProductDTO product, JSONObject reverseOrderLine, Integer quantity, Long tradeOrderLineId, Long reverseOrderLineId) {
        List<String> errorMessages = new ArrayList<>();
        JSONObject item = reverseOrderLine.getJSONObject("aeItemDTO");
        if (item == null) {
            errorMessages.add(DisputeError.ILLEGAL_ITEM + ". Item of ReverseOrderLine is null. Res: " + JSONObject.toJSONString(reverseOrderLine));
            return errorMessages;
        }
        if (!product.getItemId().equals(item.getString("itemId"))) {
            errorMessages.add(DisputeError.ILLEGAL_ITEM_ID + ". ItemId of ReverseOrderLine is: " + item.getString("itemId")
                    + ". ItemId of Trade is: " + product.getItemId()
                    + ". TradeOrderLineId: " + tradeOrderLineId + ". ReverseOrderLineId: " + reverseOrderLineId);
        }
        if (!product.getItemUrl().equals(item.getString("itemPicUrl"))) {
            errorMessages.add(DisputeError.ILLEGAL_ITEM_PIC_URL + ". ItemPicUrl of ReverseOrderLine is: " + item.getString("itemPicUrl")
                    + ". ItemPicUrl of Trade is: " + product.getItemUrl()
                    + ". TradeOrderLineId: " + tradeOrderLineId + ". ReverseOrderLineId: " + reverseOrderLineId);
        }
        JSONObject unitPriceOfReverse = item.getJSONObject("itemUnitPrice");
        if (unitPriceOfReverse == null) {
            errorMessages.add(DisputeError.ILLEGAL_ITEM_PRICE + ". ItemPrice of ReverseOrderLine is null. Res: " + JSON.toJSONString(item)
                    + ". TradeOrderLineId: " + tradeOrderLineId + ". ReverseOrderLineId: " + reverseOrderLineId);
        } else {
            // actualFee * 汇率 + 银行家算法
            MonetaryAmount actualFee = tradeOrderLineDTO.getActualFee();
            if (actualFee == null) {
                errorMessages.add(DisputeError.ILLEGAL_ITEM_PRICE_CURRENCY + ". ItemPriceCurrency of ReverseOrderLine is: " + unitPriceOfReverse.getString("currency")
                        + ". ItemPriceCurrency of Trade is null. "
                        + ". TradeOrderLineId: " + tradeOrderLineId + ". ReverseOrderLineId: " + reverseOrderLineId);
                errorMessages.add(DisputeError.ILLEGAL_ITEM_PRICE_AMOUNT + ". ItemPriceAmount of ReverseOrderLine is: " + unitPriceOfReverse.getIntValue("cent")
                        + ". ItemPriceAmount of Trade is null"
                        + ". TradeOrderLineId: " + tradeOrderLineId + ". ReverseOrderLineId: " + reverseOrderLineId);
            } else {
                BigDecimal exchangeRate = tradeOrderLineDTO.getExchangeInfo().getExchangeRate();
                BigDecimal tradeProductAmount = exchangeRate.multiply(new BigDecimal(actualFee.getNumber().intValue())).setScale(2, RoundingMode.HALF_EVEN).multiply(new BigDecimal(100));
                if (!actualFee.getCurrency().getCurrencyCode().equals(unitPriceOfReverse.getString("currency"))) {
                    errorMessages.add(DisputeError.ILLEGAL_ITEM_PRICE_CURRENCY + ". ItemPriceCurrency of ReverseOrderLine is: " + unitPriceOfReverse.getString("currency")
                            + ". ItemPriceCurrency of Trade is: " + actualFee.getCurrency()
                            + ". TradeOrderLineId: " + tradeOrderLineId + ". ReverseOrderLineId: " + reverseOrderLineId);
                }
                if (tradeProductAmount.intValue() != unitPriceOfReverse.getIntValue("cent")) {
                    errorMessages.add(DisputeError.ILLEGAL_ITEM_PRICE_AMOUNT + ". ItemPriceAmount of ReverseOrderLine is: " + unitPriceOfReverse.getIntValue("cent")
                            + ". ItemPriceAmount of Trade is: " + tradeProductAmount.intValue()
                            + ". TradeOrderLineId: " + tradeOrderLineId + ". ReverseOrderLineId: " + reverseOrderLineId);
                }
            }
        }
        if (!product.getItemTitle().equals(item.getString("itemTitle"))) {
            errorMessages.add(DisputeError.ILLEGAL_ITEM_TITLE + ". ItemTitle of ReverseOrderLine is: " + item.getString("itemTitle")
                    + ". ItemTitle of Trade is: " + product.getItemTitle()
                    + ". TradeOrderLineId: " + tradeOrderLineId + ". ReverseOrderLineId: " + reverseOrderLineId);
        }
        // check sku
        Map<Integer, String> skusOfReverse = itemSkusOfReverse(item.getJSONArray("skuDTOList"));
        Map<Integer, String> skusOfTrade = itemSkusOfTrade(JSONArray.parseArray(product.getSku().getSkuInfo()));
        if (skusOfReverse.size() != skusOfTrade.size()) {
            errorMessages.add(DisputeError.ILLEGAL_ITEM_SKU + ". ItemSku of ReverseOrderLine is: " + JSON.toJSONString(skusOfReverse)
                    + ". ItemSku of Trade is: " + JSON.toJSONString(skusOfTrade)
                    + ". TradeOrderLineId: " + tradeOrderLineId + ". ReverseOrderLineId: " + reverseOrderLineId);
        }
        if (skusOfReverse.size() > 0) {
            skusOfReverse.keySet().forEach(skuId -> {
                if (!skusOfReverse.get(skuId).equals(skusOfTrade.get(skuId))) {
                    errorMessages.add(DisputeError.ILLEGAL_ITEM_SKU
                            + ". SkuId is: " + skuId
                            + ". ItemSku of ReverseOrderLine is: " + skusOfReverse.get(skuId)
                            + ". ItemSku of Trade is: " + skusOfTrade.get(skuId)
                            + ". TradeOrderLineId: " + tradeOrderLineId + ". ReverseOrderLineId: " + reverseOrderLineId);
                }
            });
        }
        // check quantity
        if (!quantity.equals(item.getInteger("" +
                "" +
                "" +
                "itemCount"))) {
            errorMessages.add(DisputeError.ILLEGAL_ITEM_QUANTITY + ". ItemQuantity of ReverseOrderLine is: " + item.getLong("itemCount")
                    + ". ItemQuantity of Trade is: " + quantity
                    + ". TradeOrderLineId: " + tradeOrderLineId + ". ReverseOrderLineId: " + reverseOrderLineId);
        }
        return errorMessages;
    }

    private Map<Integer, String> itemSkusOfReverse(JSONArray skuDTOList) {
        Map<Integer, String> map = new ConcurrentHashMap<>();
        if (skuDTOList != null) {
            for (int i = 0; i < skuDTOList.size(); i++) {
                JSONObject sku = skuDTOList.getJSONObject(i);
                map.put(sku.getIntValue("skuId"), sku.getString("skuValue"));
            }
        }
        return map;
    }

    private Map<Integer, String> itemSkusOfTrade(JSONArray skuDTOList) {
        Map<Integer, String> map = new ConcurrentHashMap<>();
        if (skuDTOList != null) {
            for (int i = 0; i < skuDTOList.size(); i++) {
                JSONObject sku = skuDTOList.getJSONObject(i);
                map.put(sku.getIntValue("valueId"), sku.getString("valueText"));
            }
        }
        return map;
    }

    private List<String> checkReverseStatus(JSONObject reverseOrderLine, int aimReverseStatus, Long tradeOrderLineId, Long reverseOrderLineId) {
        List<String> errorMessages = new ArrayList<>();
        if (reverseOrderLine.getIntValue("reverseStatus") != aimReverseStatus) {
            errorMessages.add(DisputeError.ILLEGAL_REVERSE_STATUS + ". ReverseStatus of ReverseOrderLine is: " + reverseOrderLine.getIntValue("reverseStatus")
                    + ". Aim ReverseStatus is: " + aimReverseStatus
                    + ". TradeOrderLineId: " + tradeOrderLineId + ". ReverseOrderLineId: " + reverseOrderLineId);
        }
        return errorMessages;
    }

    private List<String> checkBuyer(BuyerInfoDTO buyer, JSONObject reverseOrderLine, Long tradeOrderLineId, Long reverseOrderLineId) {
        List<String> errorMessages = new ArrayList<>();
        if (reverseOrderLine.getJSONObject("buyer") == null) {
            errorMessages.add(DisputeError.ILLEGAL_BUYER + ". Buyer of ReverseOrderLine is null. Res: " + JSON.toJSONString(reverseOrderLine));
            return errorMessages;
        }
        if (!buyer.getBuyerId().equals(reverseOrderLine.getJSONObject("buyer").getLong("userId"))) {
            errorMessages.add(DisputeError.ILLEGAL_BUYER_ID + "Trade Buyer Id: " + buyer.getBuyerId()
                    + ". Reverse Buyer Id: " + reverseOrderLine.getJSONObject("buyer").getLong("userId")
                    + ". TradeOrderLineId: " + tradeOrderLineId + ". ReverseOrderLineId: " + reverseOrderLineId);
        }
        return errorMessages;
    }

    private List<String> checkSeller(SellerInfoDTO sellerOfTrade, JSONObject reverseOrderLine, JSONObject item, Long tradeOrderLineId, Long reverseOrderLineId) {
        List<String> errorMessages = new ArrayList<>();
        if (!sellerOfTrade.getSellerFullName().equals(item.getString("shopName"))) {
            errorMessages.add(DisputeError.ILLEGAL_SELLER_FULL_NAME + ". Trade Seller FullName: " + sellerOfTrade.getSellerFullName() +
                    ". Dispute Seller FullName: " + item.getString("shopName")
                    + ". TradeOrderLineId: " + tradeOrderLineId + ". ReverseOrderLineId: " + reverseOrderLineId);
        }
        JSONObject sellerOfReverse = reverseOrderLine.getJSONObject("seller");
        if (sellerOfReverse == null) {
            errorMessages.add(DisputeError.ILLEGAL_SELLER + ". Seller of ReverseOrderLine is null. ReverseOrderLine: " + JSONObject.toJSONString(reverseOrderLine));
        } else {
            if (!sellerOfReverse.getLong("userId").equals(sellerOfTrade.getSellerId())) {
                errorMessages.add(DisputeError.ILLEGAL_SELLER_ID + ". Trade Seller Id: " + sellerOfTrade.getSellerId() +
                        ". Dispute Seller Id: " + sellerOfReverse.getLong("userId")
                        + ". TradeOrderLineId: " + tradeOrderLineId + ". ReverseOrderLineId: " + reverseOrderLineId);
            }
//            交易子单未存储storeId
//            if (!sellerOfReverse.getLong("storeId").equals(sellerOfTrade.getStoreId())) {
//                errorMessages.add(DisputeError.ILLEGAL_SELLER_STORE_ID + ". Trade Seller Store Id: " + sellerOfTrade.getStoreId() +
//                        ". Dispute Seller Store Id: " + sellerOfReverse.getLong("storeId")
//                        + ". TradeOrderLineId: " + tradeOrderLineId + ". ReverseOrderLineId: " + reverseOrderLineId);
//            }
        }
        return errorMessages;
    }

    private AutoTestResponse checkTradeOrderLineDTO(Response<TradeOrderLineDTO> tradeOrderLineDTO, Long tradeOrderLineId) {
        AutoTestResponse autoTestResponse = new AutoTestResponse();
        if (!tradeOrderLineDTO.isSuccess() || tradeOrderLineDTO.getModule() == null) {
            List<String> errorMessages = new ArrayList<>();
            errorMessages.add(DisputeError.ERROR_RES_TRADE + ". Trade Order Line Id: " + tradeOrderLineId + ". Res: " + JSON.toJSONString(tradeOrderLineDTO));
            autoTestResponse.setErrorMessages(errorMessages);
            autoTestResponse.setPass(false);
            return autoTestResponse;
        }
        autoTestResponse.setPass(true);
        return autoTestResponse;
    }

    private AutoTestResponse checkReverseOrderLines(JSONArray reverseOrderLines) {
        AutoTestResponse autoTestResponse = new AutoTestResponse();
        if (reverseOrderLines == null || reverseOrderLines.size() != 1) {
            List<String> errorMessages = new ArrayList<>();
            String errorMessage = DisputeError.ERROR_RES_DISPUTE + ". ReverseOrderLines is null or the size of ReverseOrderLines is not 1. Res" + JSON.toJSONString(reverseOrderLines);
            errorMessages.add(errorMessage);
            autoTestResponse.setErrorMessages(errorMessages);
            autoTestResponse.setPass(false);
            return autoTestResponse;
        }
        autoTestResponse.setPass(true);
        return autoTestResponse;
    }

    private List<AutoTestResponse> checkSearch(Long buyerId, int reverseStatus, List<JSONObject> listResponses) throws Exception {
        List<AutoTestResponse> autoTestResponses = new ArrayList<>();
        AutoTestResponse searchAutoTestResponse = new AutoTestResponse();
        searchAutoTestResponse.setDescription("买家侧纠纷list: in progress 搜索");
        List<String> errorMsgs = new ArrayList<>();
        if (listResponses.size() > 0 && listResponses.get(0).getJSONObject("module").getJSONArray("items").size() > 0) {
            JSONObject first = listResponses.get(0).getJSONObject("module").getJSONArray("items").getJSONObject(0);
            JSONObject last = listResponses.size() <= 1 ? first :
                    listResponses.get(listResponses.size() - 1).getJSONObject("module").getJSONArray("items")
                            .getJSONObject(listResponses.get(listResponses.size() - 1)
                                    .getJSONObject("module").getJSONArray("items").size() - 1);
            JSONObject middle = listResponses.size() <= 2 ? first
                    : listResponses.get((listResponses.size() - 1) / 2).getJSONObject("module").getJSONArray("items")
                    .getJSONObject(listResponses.get((listResponses.size() - 1) / 2).getJSONObject("module").getJSONArray("items").size() / 2);
            errorMsgs.addAll(checkBuyerListOrderIdAndStoreName(buyerId, reverseStatus, first));
            if (first != last) {
                errorMsgs.addAll(checkBuyerListOrderIdAndStoreName(buyerId, reverseStatus, last));
            }
            if (first != middle) {
                errorMsgs.addAll(checkBuyerListOrderIdAndStoreName(buyerId, reverseStatus, middle));
            }
        } else {
            errorMsgs.add(DisputeError.ILLEGAL_SEARCH
                    + "Fail to get list response");
        }

        // reverseStatus
        List<JSONObject> all = getAllDisputeListByReverseStatus(buyerId, 1);
        int allCount = all.get(0).getJSONObject("module").getIntValue("total");
        List<JSONObject> inProgress = getAllDisputeListByReverseStatus(buyerId, 2);
        int inProgressCount = inProgress.get(0).getJSONObject("module").getIntValue("total");
        List<JSONObject> awaitingItemsReturn = getAllDisputeListByReverseStatus(buyerId, 3);
        List<JSONObject> complete = getAllDisputeListByReverseStatus(buyerId, 4);
        int completeCount = complete.get(0).getJSONObject("module").getIntValue("total");
        if (allCount != inProgressCount + completeCount) {
            errorMsgs.add(DisputeError.ILLEGAL_SEARCH
                    + "Count Not Match. All : " + allCount + ". In Progress: " + inProgressCount + ". Complete: " + completeCount);
        }
        searchAutoTestResponse.setErrorMessages(errorMsgs);
        autoTestResponses.add(searchAutoTestResponse);
        List<AutoTestResponse> disputeInProgressAutoTestRes = checkBuyerInProgressDisputeList(inProgress);
        List<AutoTestResponse> disputeAwaitingReturnAutoTestRes = checkBuyerAwaitingShipmentReturnDisputeList(awaitingItemsReturn);
        List<AutoTestResponse> disputeCompleteAutoTestRes = checkBuyerCompleteDisputeList(complete);
        autoTestResponses.addAll(disputeInProgressAutoTestRes);
        autoTestResponses.addAll(disputeAwaitingReturnAutoTestRes);
        autoTestResponses.addAll(disputeCompleteAutoTestRes);
        return autoTestResponses;
    }

    private List<AutoTestResponse> checkBuyerCompleteDisputeList(List<JSONObject> listResponses) {
        List<AutoTestResponse> autoTestResponses = new ArrayList<>();
        listResponses.forEach(response -> {
            AutoTestResponse autoTestResponse = new AutoTestResponse();
            List<String> errorMessages = new ArrayList<>();
            autoTestResponse.setDescription("Buyer Complete: 买家侧纠纷list元素校验");
            if ("true".equals(response.getString("success"))) {
                JSONObject module = response.getJSONObject("module");
                if (module != null) {
                    JSONArray items = module.getJSONArray("items");
                    for (int i = 0; i < items.size(); i++) {
                        JSONObject item = items.getJSONObject(i);
                        AutoTestResponse fieldsAutoTestResponses = checkCompleteFields(item);
                        autoTestResponses.add(fieldsAutoTestResponses);
                    }
                }
            } else {
                errorMessages.add(DisputeError.ERROR_RES_DISPUTE + ". Res: " + JSON.toJSONString(response));
                autoTestResponse.setErrorMessages(errorMessages);
                autoTestResponse.setPass(false);
            }

        });
        return autoTestResponses;
    }

    private AutoTestResponse checkCompleteFields(JSONObject item) {
        AutoTestResponse autoTestResponse = new AutoTestResponse();
        List<String> errorMessages = new ArrayList<>();
        Long tradeOrderLineId = item.getLong("tradeOrderLineId");
        JSONArray reverseOrderLines = item.getJSONArray("reverseOrderLines");
        // check reverseOrderLine is legal
        autoTestResponse = checkReverseOrderLines(reverseOrderLines);
        if (!autoTestResponse.isPass()) {
            return autoTestResponse;
        }
        JSONObject reverseOrderLine = reverseOrderLines.getJSONObject(0);
        Long reverseOrderLineId = reverseOrderLine.getLong("reverseOrderLineId");
        // set scenarios
        autoTestResponse.setScenario(getDisputeScenarios(reverseOrderLine.getString("reverseStatus"),
                reverseOrderLine.getString("reverseBizType"), reverseOrderLine.getString("timeoutType"),
                reverseOrderLine.getString("reverseType")));
        autoTestResponse.setReverseOrderLineId(reverseOrderLineId);

        List<String> commonFieldsErrorMsg = checkCommonFields(item, reverseOrderLine, reverseOrderLineId);
        if (CollectionUtils.isNotEmpty(commonFieldsErrorMsg)) {
            errorMessages.addAll(commonFieldsErrorMsg);
        }
        // check button
        List<String> buttonErrorMsgs = checkButton(reverseOrderLine.getJSONArray("buttonDTOList"),
                reverseOrderLine.getString("timeoutType"), tradeOrderLineId, reverseOrderLineId);
        if (CollectionUtils.isNotEmpty(buttonErrorMsgs)) {
            errorMessages.addAll(buttonErrorMsgs);
        }
        // check reverseStatus
        List<String> reverseStatusErrorMsg = checkReverseStatus(reverseOrderLine, 4, tradeOrderLineId, reverseOrderLineId);
        if (CollectionUtils.isNotEmpty(reverseStatusErrorMsg)) {
            errorMessages.addAll(reverseStatusErrorMsg);
        }

        if (CollectionUtils.isNotEmpty(errorMessages)) {
            autoTestResponse.setErrorMessages(errorMessages);
            autoTestResponse.setPass(false);
        } else {
            autoTestResponse.setPass(true);
        }
        return autoTestResponse;
    }

    private List<JSONObject> getAllDisputeListByReverseStatus(Long buyerId, int reverseStatus) throws Exception {
        JSONObject first = HsfUtil.getBuyerDisputeListRequest(buyerId, reverseStatus, 1, 10, "", "", "");
        int count = first.getJSONObject("module").getIntValue("total");
        List<JSONObject> listResponses = getListResByReverseStatus(reverseStatus, count, 10, buyerId);
        return listResponses;
    }

    private List<String> checkBuyerListOrderIdAndStoreName(Long buyerId, int reverseStatus, JSONObject item) throws Exception {
        List<String> errorMessages = new ArrayList<>();
        String orderId = item.getJSONArray("reverseOrderLines").getJSONObject(0).getString("tradeOrderId");
        String shopName = item.getString("shopName");
        JSONObject orderIdSearch = HsfUtil.getBuyerDisputeListRequest(buyerId, reverseStatus, 1, 50, "", orderId, "");
        errorMessages.addAll(checkBuyerListOrderIdAndStoreName(orderId, "", orderIdSearch));
        JSONObject shopNameSearch = HsfUtil.getBuyerDisputeListRequest(buyerId, reverseStatus, 1, 50, "", "", shopName);
        errorMessages.addAll(checkBuyerListOrderIdAndStoreName("", shopName, shopNameSearch));
        JSONObject orderIdAndShopNameSearch = HsfUtil.getBuyerDisputeListRequest(buyerId, reverseStatus, 1, 50, "", orderId, shopName);
        errorMessages.addAll(checkBuyerListOrderIdAndStoreName(orderId, shopName, orderIdAndShopNameSearch));
        return errorMessages;
    }

    private List<String> checkBuyerListOrderIdAndStoreName(String orderId, String shopName, JSONObject searchResult) {
        List<String> errorMessages = new ArrayList<>();
        JSONArray itemsOfSearch = searchResult.getJSONObject("module").getJSONArray("items");
        for (int i = 0; i < itemsOfSearch.size(); i++) {
            JSONObject itemOfSearch = itemsOfSearch.getJSONObject(i);
            if (StringUtil.isEmpty(orderId)) {
                if (!itemOfSearch.getString("shopName").equals(shopName)) {
                    errorMessages.add(DisputeError.ILLEGAL_SEARCH
                            + ". Search Shop Name: " + itemOfSearch.getString("shopName") + ". Original Shop Name: " + shopName
                            + ". Trade Order Id: " + orderId
                            + ". Search Result: " + itemsOfSearch.toJSONString());
                }
            } else if (StringUtil.isEmpty(shopName)) {
                if (!itemOfSearch.getJSONArray("reverseOrderLines").getJSONObject(0).getString("tradeOrderId").equals(orderId)) {
                    errorMessages.add(DisputeError.ILLEGAL_SEARCH
                            + ". Search Trade Order Id: " + itemOfSearch.getJSONArray("reverseOrderLines").getJSONObject(0).getString("tradeOrderId")
                            + ". Original Trade Order Id: " + orderId
                            + ". Search Result: " + itemsOfSearch.toJSONString());
                }
            } else {
                if (!itemOfSearch.getString("shopName").equals(shopName)) {
                    errorMessages.add(DisputeError.ILLEGAL_SEARCH
                            + ". Search Shop Name: " + itemOfSearch.getString("shopName") + ". Original Shop Name: " + shopName
                            + ". Trade Order Id: " + orderId
                            + ". Search Result: " + itemsOfSearch.toJSONString());
                }
                if (!itemOfSearch.getJSONArray("reverseOrderLines").getJSONObject(0).getString("tradeOrderId").equals(orderId)) {
                    errorMessages.add(DisputeError.ILLEGAL_SEARCH
                            + ". Search Trade Order Id: " + itemOfSearch.getJSONArray("reverseOrderLines").getJSONObject(0).getString("tradeOrderId")
                            + ". Original Trade Order Id: " + orderId
                            + ". Search Result: " + itemsOfSearch.toJSONString());
                }
            }
        }
        return errorMessages;
    }

    private List<AutoTestResponse> buyerAwaitingShipmentReturnAutoTest(int count, Long buyerId) throws Exception {
        List<AutoTestResponse> autoTestResponses = new ArrayList<>();
        List<JSONObject> listResponses = getListResByReverseStatus(3, count, 10, buyerId);
        // check count match
        AutoTestResponse countMatchAutoTestResponse = checkBuyerDisputeCount(count, listResponses);
        autoTestResponses.add(countMatchAutoTestResponse);

        // check list info
        List<AutoTestResponse> listAutoTestResponse = checkBuyerAwaitingShipmentReturnDisputeList(listResponses);
        autoTestResponses.addAll(listAutoTestResponse);

        // check search
        List<AutoTestResponse> searchAutoTestResponses = checkSearch(buyerId, 3, listResponses);
        autoTestResponses.addAll(searchAutoTestResponses);
        return autoTestResponses;
    }

    private List<AutoTestResponse> checkBuyerAwaitingShipmentReturnDisputeList(List<JSONObject> listResponses) {
        List<AutoTestResponse> autoTestResponses = new ArrayList<>();
        listResponses.forEach(response -> {
            AutoTestResponse autoTestResponse = new AutoTestResponse();
            List<String> errorMessages = new ArrayList<>();
            autoTestResponse.setDescription("Buyer Awaiting Shipment Return: 买家侧纠纷list元素校验");
            if ("true".equals(response.getString("success"))) {
                JSONObject module = response.getJSONObject("module");
                if (module != null) {
                    JSONArray items = module.getJSONArray("items");
                    for (int i = 0; i < items.size(); i++) {
                        JSONObject item = items.getJSONObject(i);
                        AutoTestResponse fieldsAutoTestResponses = checkWaitingShipmentReturnFields(item);
                        autoTestResponses.add(fieldsAutoTestResponses);
                    }
                }
            } else {
                errorMessages.add(DisputeError.ERROR_RES_DISPUTE + ". Res: " + JSON.toJSONString(response));
                autoTestResponse.setErrorMessages(errorMessages);
                autoTestResponse.setPass(false);
            }

        });
        return autoTestResponses;
    }

    private AutoTestResponse checkWaitingShipmentReturnFields(JSONObject item) {
        AutoTestResponse autoTestResponse = new AutoTestResponse();
        List<String> errorMessages = new ArrayList<>();
        Long tradeOrderLineId = item.getLong("tradeOrderLineId");
        JSONArray reverseOrderLines = item.getJSONArray("reverseOrderLines");
        // check reverseOrderLine is legal
        autoTestResponse = checkReverseOrderLines(reverseOrderLines);
        if (!autoTestResponse.isPass()) {
            return autoTestResponse;
        }
        JSONObject reverseOrderLine = reverseOrderLines.getJSONObject(0);
        Long reverseOrderLineId = reverseOrderLine.getLong("reverseOrderLineId");
        // set scenarios
        autoTestResponse.setScenario(getDisputeScenarios(reverseOrderLine.getString("reverseStatus"),
                reverseOrderLine.getString("reverseBizType"), reverseOrderLine.getString("timeoutType"),
                reverseOrderLine.getString("reverseType")));
        autoTestResponse.setReverseOrderLineId(reverseOrderLineId);

        List<String> commonFieldsErrorMsg = checkCommonFields(item, reverseOrderLine, reverseOrderLineId);
        if (CollectionUtils.isNotEmpty(commonFieldsErrorMsg)) {
            errorMessages.addAll(commonFieldsErrorMsg);
        }
        // check button
        List<String> buttonErrorMsgs = checkButton(reverseOrderLine.getJSONArray("buttonDTOList"),
                reverseOrderLine.getString("timeoutType"), tradeOrderLineId, reverseOrderLineId);
        if (CollectionUtils.isNotEmpty(buttonErrorMsgs)) {
            errorMessages.addAll(buttonErrorMsgs);
        }
        // check reverseStatus
        List<String> reverseStatusErrorMsg = checkReverseStatus(reverseOrderLine, 3, tradeOrderLineId, reverseOrderLineId);
        if (CollectionUtils.isNotEmpty(reverseStatusErrorMsg)) {
            errorMessages.addAll(reverseStatusErrorMsg);
        }

        if (CollectionUtils.isNotEmpty(errorMessages)) {
            autoTestResponse.setErrorMessages(errorMessages);
            autoTestResponse.setPass(false);
        } else {
            autoTestResponse.setPass(true);
        }
        return autoTestResponse;
    }

    private List<String> checkCommonFields(JSONObject item, JSONObject reverseOrderLine, Long reverseOrderLineId) {
        List<String> errorMessages = new ArrayList<>();
        Long tradeOrderLineId = reverseOrderLine.getLong("tradeOrderLineId");
        // check tradeOrder is legal
        Response<TradeOrderLineDTO> tradeOrderLineDTO = orderQueryForBuyerFacade.queryTradeOrderLineById(tradeOrderLineId);



        AutoTestResponse autoTestResponse = checkTradeOrderLineDTO(tradeOrderLineDTO, tradeOrderLineId);
        if (!autoTestResponse.isPass()) {
            return autoTestResponse.getErrorMessages();
        }
        // check seller
        List<String> sellerErrorMsgs = checkSeller(tradeOrderLineDTO.getModule().getSeller(), reverseOrderLine, item, tradeOrderLineId, reverseOrderLineId);
        if (CollectionUtils.isNotEmpty(sellerErrorMsgs)) {
            errorMessages.addAll(sellerErrorMsgs);
        }
        // check buyer
        List<String> buyerErrorMsgs = checkBuyer(tradeOrderLineDTO.getModule().getBuyer(), reverseOrderLine, tradeOrderLineId, reverseOrderLineId);
        if (CollectionUtils.isNotEmpty(buyerErrorMsgs)) {
            errorMessages.addAll(sellerErrorMsgs);
        }
        // check item
        List<String> itemErrorMsgs = checkItem(tradeOrderLineDTO.getModule(), tradeOrderLineDTO.getModule().getProduct(), reverseOrderLine, tradeOrderLineDTO.getModule().getQuantity(), tradeOrderLineId, reverseOrderLineId);
        if (CollectionUtils.isNotEmpty(itemErrorMsgs)) {
            errorMessages.addAll(sellerErrorMsgs);
        }
        return errorMessages;
    }

}
