package com.aliexpress.testcase.dispute.impl;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.common.lang.StringUtil;
import com.alibaba.ecommerce.module.Response;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.global.landlord.LandlordContext;
import com.alibaba.global.order.management.api.facade.OrderQueryForBuyerFacade;
//import com.alibaba.global.order.management.api.facade.OrderQueryForSellerFacade;

import com.alibaba.global.order.management.api.model.BuyerInfoDTO;
import com.alibaba.global.order.management.api.model.ProductDTO;
import com.alibaba.global.order.management.api.model.TradeOrderDTO;
import com.alibaba.global.order.management.api.model.TradeOrderLineDTO;
import com.alibaba.global.order.management.api.request.QueryESTradeOrdersByIdRequest;
import com.aliexpress.testcase.constant.DisputeError;
import com.aliexpress.testcase.dispute.SellerDisputeListService;
import com.aliexpress.testcase.module.AutoTestResponse;
import com.aliexpress.testcase.util.HsfUtil;
import com.taobao.payment.boot.sdk.pay.sdo.IReturnable;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import javax.money.MonetaryAmount;
import javax.money.NumberValue;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

@HSFProvider(serviceInterface = SellerDisputeListService.class, serviceGroup = "HSF", serviceVersion = "1.0.0", clientTimeout = 20000)
public class SellerDisputeListServiceImpl implements SellerDisputeListService {


    @Autowired
    private OrderQueryForBuyerFacade orderQueryForBuyerFacade;
//    @Autowired
//    private OrderQueryForSellerFacade orderQueryForSellerFacade;


    /**
     * SellerSearchStatus
     * All(1,"all","All"),
     * IN_NEGOTIATION(2,"in_negotiation", "In negotiation"),
     * IN_ARBITRATION(3,"in_arbitration", "In arbitration"),
     * WAIT_SELLER_SET_RETURN_ADDRESS(4, "wait_seller_set_return_address","Wait seller set return address"),
     * WAIT_BUYER_RETURN_GOODS(5,"wait_buyer_return_goods", "Wait buyer return goods"),
     * WAIT_SELLER_RECEIVE_GOODS(6,"wait_seller_receive_goods", "Wait seller receive goods"),
     * WAIT_WAREHOUSE_RECEIVE_GOODS(7,"wait_warehouse_receive_goods", "Wait warehouse receive goods"),
     * FINISH(8,"finish", "finish"),
     * <p>
     * WAIT_FOR_YOUR_RESPONSE(10,"wait_for_your_response", "Wait for your response")
     */

    @Override
    public List<AutoTestResponse> sellerDisputeListAutoTest(Long sellerId) throws Exception {
        List<AutoTestResponse> autoTestResponses = new ArrayList<>();
        JSONObject countRes = HsfUtil.getSellerDisputeListCountRes(sellerId);

//        协商中空指针异常
//        List<AutoTestResponse> inNegotiationListAutoTests = reverseStatusListAutoTest(getListCountByRes(10, 1, 2, sellerId), sellerId, 2);
//        autoTestResponses.addAll(inNegotiationListAutoTests);

        List<AutoTestResponse> inArbitrationListAutoTests = reverseStatusListAutoTest(getListCountByRes(10, 1, 3, sellerId), sellerId, 3);
        autoTestResponses.addAll(inArbitrationListAutoTests);

        List<AutoTestResponse> waitSetAddressListAutoTests = reverseStatusListAutoTest(getListCountByRes(10, 1, 4, sellerId), sellerId, 4);
        autoTestResponses.addAll(waitSetAddressListAutoTests);


        List<AutoTestResponse> waitReturnGoodsAutoTests = reverseStatusListAutoTest(getCount(countRes, 5), sellerId, 5);
//        List<AutoTestResponse> waitReturnGoodsAutoTests = waitReturnGoodsListAutoTest(getCount(countRes, 5), sellerId, 5);
        autoTestResponses.addAll(waitReturnGoodsAutoTests);

        List<AutoTestResponse> awaitSellerReceiptListAutoTests = reverseStatusListAutoTest(getListCountByRes(10, 1, 6, sellerId), sellerId, 6);
        autoTestResponses.addAll(awaitSellerReceiptListAutoTests);

        List<AutoTestResponse> awaitWareHouseReceiptListAutoTests = reverseStatusListAutoTest(getListCountByRes(10, 1, 7, sellerId), sellerId, 7);
        autoTestResponses.addAll(awaitWareHouseReceiptListAutoTests);

        List<AutoTestResponse> finishListAutoTests = reverseStatusListAutoTest(getListCountByRes(10, 1, 8, sellerId), sellerId, 8);
        autoTestResponses.addAll(finishListAutoTests);

//        WAIT_FOR_YOUR_RESPONSE
        List<AutoTestResponse> waitForResListAutoTests = reverseStatusListAutoTest(getCount(countRes, 10), sellerId, 10);
        autoTestResponses.addAll(waitForResListAutoTests);

        return autoTestResponses;
    }


    private List<AutoTestResponse> reverseStatusListAutoTest(int count, Long sellerId, int reverseStatus) throws Exception {
        List<AutoTestResponse> autoTestResponses = new ArrayList<>();
        List<JSONObject> listResponses = getListResByReverseStatus(count, 10, 1, reverseStatus, sellerId);


        // check count match
        AutoTestResponse countMatchAutoTestResponse = checkSellerDisputeCount(count, listResponses, reverseStatus);
        autoTestResponses.add(countMatchAutoTestResponse);

        // check list info
        List<AutoTestResponse> listAutoTestResponse = checkReverseStatusDisputeList(listResponses, reverseStatus);
        autoTestResponses.addAll(listAutoTestResponse);

//        // check search
//        List<AutoTestResponse> searchAutoTestResponse = checkSearch(listResponses, reverseStatus);
//        autoTestResponses.addAll(searchAutoTestResponse);

        return autoTestResponses;

    }

    private List<AutoTestResponse> checkReverseStatusDisputeList(List<JSONObject> listResponses, int reverseStatus) {
        List<AutoTestResponse> autoTestResponses = new ArrayList<>();
        listResponses.forEach(response -> {
            AutoTestResponse autoTestResponse = new AutoTestResponse();
            List<String> errorMessages = new ArrayList<>();
            autoTestResponse.setDescription(getStringStatusCode(reverseStatus) + ": 卖家侧纠纷list元素校验");
            if ("true".equals(response.getString("success"))) {
                JSONObject data = response.getJSONObject("data");
                if (data != null) {
                    JSONArray dataSource = data.getJSONArray("dataSource");
                    // check dataSource里面的内容
                    for (int i = 0; i < dataSource.size(); i++) {
                        JSONObject sellerReverseInfo = dataSource.getJSONObject(i);
                        AutoTestResponse fieldsAutoTestResponses = null;//sellerReverseInfo是查list返回的
                        try {
                            fieldsAutoTestResponses = checkReverseStatusFields(sellerReverseInfo, reverseStatus);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        autoTestResponses.add(fieldsAutoTestResponses);
                    }
                }
            } else {
                errorMessages.add(DisputeError.ERROR_RES_DISPUTE + ". Res: " + JSON.toJSONString(response));
                autoTestResponse.setErrorMessages(errorMessages);
                autoTestResponse.setPass(false);
            }

        });
        return autoTestResponses;

    }

    private List<AutoTestResponse> checkAwaitSellerReceiptDisputeList(List<JSONObject> listResponses) {
        List<AutoTestResponse> autoTestResponses = new ArrayList<>();
        listResponses.forEach(response -> {
            AutoTestResponse autoTestResponse = new AutoTestResponse();
            List<String> errorMessages = new ArrayList<>();
            autoTestResponse.setDescription("Await Seller Receipt: 卖家侧纠纷list元素校验");
            if ("true".equals(response.getString("success"))) {
                JSONObject data = response.getJSONObject("data");
                if (data != null) {
                    JSONArray dataSource = data.getJSONArray("dataSource");
                    // check dataSource里面的内容
                    for (int i = 0; i < dataSource.size(); i++) {
                        JSONObject sellerReverseInfo = dataSource.getJSONObject(i);
                        AutoTestResponse fieldsAutoTestResponses = checkAwaitSellerReceiptFields(sellerReverseInfo);//sellerReverseInfo是查list返回的
                        autoTestResponses.add(fieldsAutoTestResponses);
                    }
                }
            } else {
                errorMessages.add(DisputeError.ERROR_RES_DISPUTE + ". Res: " + JSON.toJSONString(response));
                autoTestResponse.setErrorMessages(errorMessages);
                autoTestResponse.setPass(false);
            }

        });
        return autoTestResponses;

    }

    private AutoTestResponse checkReverseStatusFields(JSONObject sellerReverseInfo, int reverseStatus) throws Exception {
        AutoTestResponse autoTestResponse = new AutoTestResponse();
        List<String> errorMessages = new ArrayList<>();
        JSONArray sellerReverseOrderLines = sellerReverseInfo.getJSONArray("sellerReverseOrderLines");
        // check reverseOrderLine is legal
        autoTestResponse = checkReverseOrderLines(sellerReverseOrderLines);
        if (!autoTestResponse.isPass()) {
            return autoTestResponse;
        }
        JSONObject sellerReverseOrderLine = sellerReverseOrderLines.getJSONObject(0);
        Long reverseOrderLineId = sellerReverseOrderLine.getLong("reverseOrderLineId");
        Long tradeOrderLineId = sellerReverseOrderLine.getLong("tradeOrderLineId");
        String reverseBizType = sellerReverseOrderLine.getString("reverseBizType");

        // set scenarios
        //组装各种入参
        autoTestResponse.setScenario(getDisputeScenarios(sellerReverseOrderLine.getString("reverseStatus"),
                sellerReverseOrderLine.getString("reverseBizType"), sellerReverseOrderLine.getString("timeoutType"),
                sellerReverseOrderLine.getString("reverseType")));
        autoTestResponse.setReverseOrderLineId(reverseOrderLineId);
        // check common
        List<String> commonFieldsErrorMsg = checkCommonFields(sellerReverseOrderLine, reverseOrderLineId);
        if (CollectionUtils.isNotEmpty(commonFieldsErrorMsg)) {
            errorMessages.addAll(commonFieldsErrorMsg);
        }
        // check button
        List<String> buttonErrorMsgs = checkButton(reverseStatus, sellerReverseOrderLine.getJSONArray("buttonDTOList")
                , tradeOrderLineId, reverseOrderLineId, reverseBizType);
        if (CollectionUtils.isNotEmpty(buttonErrorMsgs)) {
            errorMessages.addAll(buttonErrorMsgs);
        }

        //check bizType
        List<String> bizTypeErrorMsg = checkBizType(sellerReverseOrderLine, reverseOrderLineId, tradeOrderLineId, reverseBizType);
        if (CollectionUtils.isNotEmpty(bizTypeErrorMsg)) {
            errorMessages.addAll(bizTypeErrorMsg);
        }

        // check reverseStatus
        List<String> reverseStatusErrorMsg = checkReverseStatus(sellerReverseOrderLine, reverseStatus, tradeOrderLineId, reverseOrderLineId);
        if (CollectionUtils.isNotEmpty(reverseStatusErrorMsg)) {
            errorMessages.addAll(reverseStatusErrorMsg);
        }
        if (CollectionUtils.isNotEmpty(errorMessages)) {
            autoTestResponse.setErrorMessages(errorMessages);
            autoTestResponse.setPass(false);
        } else {
            autoTestResponse.setPass(true);
        }
        return autoTestResponse;

    }

    private List<String> checkBizType(JSONObject sellerReverseOrderLine, Long reverseOrderLineId, Long tradeOrderLineId, String reverseBizType) throws Exception {
        List<String> errorMessages = new ArrayList<>();
        JSONObject buyerJson = sellerReverseOrderLine.getJSONObject("buyer");
        Long buyerId = buyerJson.getLong("userId");
        JSONObject reverseOrderLineRes = HsfUtil.getReverseOrderLineRes(buyerId, reverseOrderLineId);
        String bizTypeFromAPI = reverseOrderLineRes.getJSONObject("module").getJSONObject("features").getString("ae_business_type");
        String reverseBizTypeFromAPI = getReverseBizType(bizTypeFromAPI);
        if (reverseBizTypeFromAPI != reverseBizType) {
            errorMessages.add(DisputeError.ILLEGAL_REVERSE_BIZTYPE + ". ReverseBizType of ReverseOrderLine is: " + reverseBizType
                    + ". ReverseBizType of queryReverseOrderLineByIdAPI is: " + reverseBizTypeFromAPI
                    + ". TradeOrderLineId: " + tradeOrderLineId + ". ReverseOrderLineId: " + reverseOrderLineId);
        }
        return errorMessages;
    }


//    private List<AutoTestResponse> awaitSellerReceiptListAutoTest(int count, Long sellerId, int reverseStatus) throws Exception {
//
//        List<AutoTestResponse> autoTestResponses = new ArrayList<>();
//        List<JSONObject> listResponses = getListResByReverseStatus(count, 10, 1, 6, sellerId);
//
//        // check count match
//        AutoTestResponse countMatchAutoTestResponse = checkSellerDisputeCount(count, listResponses, reverseStatus);
//        autoTestResponses.add(countMatchAutoTestResponse);
//
//        // check list info
////        List<AutoTestResponse> listAutoTestResponse = checkAwaitSellerReceiptDisputeList(listResponses);
////        autoTestResponses.addAll(listAutoTestResponse);
//
////        // check search
////        List<AutoTestResponse> searchAutoTestResponse = checkSearch(listResponses);
////        autoTestResponses.addAll(searchAutoTestResponse);
//
//        return autoTestResponses;
//    }


    private AutoTestResponse checkAwaitSellerReceiptFields(JSONObject sellerReverseInfo) {
        AutoTestResponse autoTestResponse = new AutoTestResponse();
        List<String> errorMessages = new ArrayList<>();
        JSONArray sellerReverseOrderLines = sellerReverseInfo.getJSONArray("sellerReverseOrderLines");
        // check reverseOrderLine is legal
        autoTestResponse = checkReverseOrderLines(sellerReverseOrderLines);
        if (!autoTestResponse.isPass()) {
            return autoTestResponse;
        }
        JSONObject sellerReverseOrderLine = sellerReverseOrderLines.getJSONObject(0);
        Long reverseOrderLineId = sellerReverseOrderLine.getLong("reverseOrderLineId");
        Long tradeOrderLineId = sellerReverseOrderLine.getLong("tradeOrderLineId");
        String reverseBizType = sellerReverseOrderLine.getString("reverseBizType");

        // set scenarios
        //组装各种入参
        autoTestResponse.setScenario(getDisputeScenarios(sellerReverseOrderLine.getString("reverseStatus"),
                sellerReverseOrderLine.getString("reverseBizType"), sellerReverseOrderLine.getString("timeoutType"),
                sellerReverseOrderLine.getString("reverseType")));
        autoTestResponse.setReverseOrderLineId(reverseOrderLineId);
        // check common
        List<String> commonFieldsErrorMsg = checkCommonFields(sellerReverseOrderLine, reverseOrderLineId);
        if (CollectionUtils.isNotEmpty(commonFieldsErrorMsg)) {
            errorMessages.addAll(commonFieldsErrorMsg);
        }
        // check button
        List<String> buttonErrorMsgs = checkButton(6, sellerReverseOrderLine.getJSONArray("buttonDTOList"),
                tradeOrderLineId, reverseOrderLineId, reverseBizType);
        if (CollectionUtils.isNotEmpty(buttonErrorMsgs)) {
            errorMessages.addAll(buttonErrorMsgs);
        }
        // check reverseStatus
        List<String> reverseStatusErrorMsg = checkReverseStatus(sellerReverseOrderLine, 6, tradeOrderLineId, reverseOrderLineId);
        if (CollectionUtils.isNotEmpty(reverseStatusErrorMsg)) {
            errorMessages.addAll(reverseStatusErrorMsg);
        }
        if (CollectionUtils.isNotEmpty(errorMessages)) {
            autoTestResponse.setErrorMessages(errorMessages);
            autoTestResponse.setPass(false);
        } else {
            autoTestResponse.setPass(true);
        }
        return autoTestResponse;

    }

    private int getCount(JSONObject countRes, int disputeStatus) {
        return Integer.parseInt(countRes.getJSONObject("data").get(String.valueOf(disputeStatus)).toString());
    }

    private int getListCountByRes(int pageSize, int reverseBizType, int reverseStatus, Long sellerId) throws Exception {
        JSONObject listResponse = HsfUtil.getSellerDisputeListRequest(null, pageSize, reverseBizType, "en_US", null, reverseStatus, null,
                1, sellerId, pageSize, 1, reverseStatus, "DESC", null);
        JSONObject data = listResponse.getJSONObject("data");
        JSONObject pageInfo = data.getJSONObject("pageInfo");
        Object total = pageInfo.get("total");
        int count = Integer.parseInt(total.toString());
        return count;
    }


    private List<AutoTestResponse> waitReturnGoodsListAutoTest(int count, Long sellerId, int reverseStatus) throws Exception {
        List<AutoTestResponse> autoTestResponses = new ArrayList<>();
        List<JSONObject> listResponses = getListResByReverseStatus(count, 10, 1, reverseStatus, sellerId);

        // check count match
        AutoTestResponse countMatchAutoTestResponse = checkSellerDisputeCount(count, listResponses, reverseStatus);
        autoTestResponses.add(countMatchAutoTestResponse);

        // check list info
        List<AutoTestResponse> listAutoTestResponse = checkWaitReturnGoodsDisputeList(sellerId, listResponses, reverseStatus);
        autoTestResponses.addAll(listAutoTestResponse);

//         check search
        List<AutoTestResponse> searchAutoTestResponse = checkSearch(listResponses, reverseStatus, sellerId);
        autoTestResponses.addAll(searchAutoTestResponse);

        return autoTestResponses;
    }

    //将每页的返回放在一起
    private List<JSONObject> getListResByReverseStatus(int count, int pageSize, int reverseBizType, int reverseStatus, Long sellerId) throws Exception {
        List<JSONObject> listResponses = new ArrayList<>();
        int totalPage = count / pageSize + 1;
        for (int i = 1; i <= totalPage; i++) {
            JSONObject listRes = HsfUtil.getSellerDisputeListRequest(null, pageSize, reverseBizType, "en_US", null, reverseStatus, null,
                    i, sellerId, pageSize, i, reverseStatus, "DESC", null);
//           返回值里面的结果待解析，只放页码返回的?

            listResponses.add(listRes);//返回值是list里面有多次response 。因为每页限制10个的话，就只会展示10个，所以需要换页才能展示所有的数据
        }
        return listResponses;
    }


    private AutoTestResponse checkSellerDisputeCount(int count, List<JSONObject> listResponses, int reverseStatus) {
        AutoTestResponse autoTestResponse = new AutoTestResponse();
        autoTestResponse.setPass(true);
        autoTestResponse.setDescription("卖家侧纠纷list-" + getStringStatusCode(reverseStatus) + ":接口返回总数量和列表返回的总数量对比场景");
        List<String> errorMessages = new ArrayList<>();
        //        listTotal是通过一个个计算查到的total
        int listTotal = getCountByDisputeList(listResponses);
//        入参的count是通过count接口获取的结果
        if (count != listTotal) {
            autoTestResponse.setPass(false);
            errorMessages.add(
                    "Seller Dispute List - " + getStringStatusCode(reverseStatus) + " : Count Not Match! Count info of Response: " + count + ". Actually Dispute List Count Info: " + listTotal);
        }
        autoTestResponse.appendErrorMessages(errorMessages);
        return autoTestResponse;
    }

    private int getCountByDisputeList(List<JSONObject> listResponses) {
        int totalSize = 0;
        for (JSONObject listResponse : listResponses) {
            totalSize += listResponse.getJSONObject("data").getJSONArray("dataSource").size();
        }
        return totalSize;
    }


    private List<AutoTestResponse> checkWaitReturnGoodsDisputeList(Long sellerId, List<JSONObject> listResponses, int reverseStatus) {
        List<AutoTestResponse> autoTestResponses = new ArrayList<>();
        listResponses.forEach(response -> {
            AutoTestResponse autoTestResponse = new AutoTestResponse();
            List<String> errorMessages = new ArrayList<>();
            autoTestResponse.setDescription("Wait Return Goods: 卖家侧纠纷list元素校验");
            if ("true".equals(response.getString("success"))) {
                JSONObject data = response.getJSONObject("data");
                if (data != null) {
                    JSONArray dataSource = data.getJSONArray("dataSource");
                    // check dataSource里面的内容
                    for (int i = 0; i < dataSource.size(); i++) {
                        JSONObject sellerReverseInfo = dataSource.getJSONObject(i);
                        AutoTestResponse fieldsAutoTestResponses = checkWaitReturnGoodsFields(sellerId, sellerReverseInfo, reverseStatus);//sellerReverseInfo是查list返回的
                        autoTestResponses.add(fieldsAutoTestResponses);
                    }
                }
            } else {
                errorMessages.add(DisputeError.ERROR_RES_DISPUTE + ". Res: " + JSON.toJSONString(response));
                autoTestResponse.setErrorMessages(errorMessages);
                autoTestResponse.setPass(false);
            }

        });
        return autoTestResponses;
    }

    //入参 sellerReverseInfo是查list返回的
    private AutoTestResponse checkWaitReturnGoodsFields(Long sellerId, JSONObject sellerReverseInfo, int reverseStatus) {
        AutoTestResponse autoTestResponse = new AutoTestResponse();
        List<String> errorMessages = new ArrayList<>();
        JSONArray sellerReverseOrderLines = sellerReverseInfo.getJSONArray("sellerReverseOrderLines");
        // check reverseOrderLine is legal
        autoTestResponse = checkReverseOrderLines(sellerReverseOrderLines);//checkReverseOrderLines?
        if (!autoTestResponse.isPass()) {
            return autoTestResponse;
        }
        JSONObject sellerReverseOrderLine = sellerReverseOrderLines.getJSONObject(0);
        Long reverseOrderLineId = sellerReverseOrderLine.getLong("reverseOrderLineId");
        Long tradeOrderLineId = sellerReverseOrderLine.getLong("tradeOrderLineId");
        String reverseBizType = sellerReverseOrderLine.getString("reverseBizType");

        // set scenarios
        //组装各种入参
        autoTestResponse.setScenario(getDisputeScenarios(sellerReverseOrderLine.getString("reverseStatus"),
                sellerReverseOrderLine.getString("reverseBizType"), sellerReverseOrderLine.getString("timeoutType"),
                sellerReverseOrderLine.getString("reverseType")));
        autoTestResponse.setReverseOrderLineId(reverseOrderLineId);
        // check common
        List<String> commonFieldsErrorMsg = checkCommonFields(sellerReverseOrderLine, reverseOrderLineId);
        if (CollectionUtils.isNotEmpty(commonFieldsErrorMsg)) {
            errorMessages.addAll(commonFieldsErrorMsg);
        }
        // check button
        List<String> buttonErrorMsgs = checkButton(reverseStatus, sellerReverseOrderLine.getJSONArray("buttonDTOList"),
                tradeOrderLineId, reverseOrderLineId, reverseBizType);
        if (CollectionUtils.isNotEmpty(buttonErrorMsgs)) {
            errorMessages.addAll(buttonErrorMsgs);
        }
        // check reverseStatus
        List<String> reverseStatusErrorMsg = checkReverseStatus(sellerReverseOrderLine, reverseStatus, tradeOrderLineId, reverseOrderLineId);
        if (CollectionUtils.isNotEmpty(reverseStatusErrorMsg)) {
            errorMessages.addAll(reverseStatusErrorMsg);
        }


        if (CollectionUtils.isNotEmpty(errorMessages)) {
            autoTestResponse.setErrorMessages(errorMessages);
            autoTestResponse.setPass(false);
        } else {
            autoTestResponse.setPass(true);
        }
        return autoTestResponse;

    }


    //seller接口timeoutType为空
    private List<String> checkButton(int reverseStatus, JSONArray buttonDTOs, Long tradeOrderLineId, Long reverseOrderLineId, String reverseBizType) {
        List<String> errorMessages = new ArrayList<>();
        Set<String> buttonTypes = getButtonTypes(buttonDTOs);
        //通用的查看详情未记录在buttonDTOs中
        //协商状态 根据类型和状态展示立即处理
        if (reverseStatus == 2) {
            if (reverseBizType != null) {
                //有纠纷类型标但是不是售后宝协商的，不展示按钮
                if (reverseBizType != "AliExpress investigation with negotiation") {
                    if (!buttonDTOs.isEmpty() || buttonDTOs.size() > 0) {
                        errorMessages.add(DisputeError.ILLEGAL_REVERSE_BUTTON + ".Button of ReverseOrderLine is illegal. Res: " + JSON.toJSONString(buttonDTOs)
                                + ". TradeOrderLineId: " + tradeOrderLineId + ". ReverseOrderLineId: " + reverseOrderLineId);
                    }
                    //有纠纷类型标且是售后宝协商的，展示立即处理
                } else if (reverseBizType == "AliExpress investigation with negotiation") {
                    if (!buttonTypes.contains("deal_with") && buttonDTOs.size() != 1) {
                        errorMessages.add(DisputeError.ILLEGAL_REVERSE_BUTTON + ".Button of ReverseOrderLine is illegal. Res: " + JSON.toJSONString(buttonDTOs)
                                + ". TradeOrderLineId: " + tradeOrderLineId + ". ReverseOrderLineId: " + reverseOrderLineId);
                    }
                }

            }
        }

        //平台介入状态 根据类型和状态展示立即处理
        if (reverseStatus == 3) {
            if (reverseBizType != "AliExpress investigation with negotiation") {
                if (reverseBizType != null) {
                    //有纠纷类型标的且非售后宝协商的，不展示按钮
                    if (!buttonDTOs.isEmpty() || buttonDTOs.size() > 0) {
                        errorMessages.add(DisputeError.ILLEGAL_REVERSE_BUTTON + ".Button of ReverseOrderLine is illegal. Res: " + JSON.toJSONString(buttonDTOs)
                                + ". TradeOrderLineId: " + tradeOrderLineId + ". ReverseOrderLineId: " + reverseOrderLineId);
                    }
                }
            }
        }


        //待卖家补充地址状态仅展示新增地址按钮
        if (reverseStatus == 4) {
            if (!buttonTypes.contains("set_return_address")) {
                errorMessages.add(DisputeError.ILLEGAL_REVERSE_BUTTON + ". Button of ReverseOrderLine is illegal. Res: " + JSON.toJSONString(buttonDTOs)
                        + ". TradeOrderLineId: " + tradeOrderLineId + ". ReverseOrderLineId: " + reverseOrderLineId);
            }
        }

        //待买家退货状态返回不展示按钮
        //待仓库收货状态不展示按钮
        //结束状态不展示按钮
        if (reverseStatus == 5 || reverseStatus == 7 || reverseStatus == 8) {
            if (!buttonDTOs.isEmpty() || buttonDTOs.size() > 0) {
                errorMessages.add(DisputeError.ILLEGAL_REVERSE_BUTTON + ".Button of ReverseOrderLine is illegal. Res: " + JSON.toJSONString(buttonDTOs)
                        + ". TradeOrderLineId: " + tradeOrderLineId + ". ReverseOrderLineId: " + reverseOrderLineId);
            }
        }
        //待卖家收货状态展示确认收货按钮
        if (reverseStatus == 6) {
            if (!buttonTypes.contains("confirm_goods") && buttonDTOs.size() != 1) {
                errorMessages.add(DisputeError.ILLEGAL_REVERSE_BUTTON + ". Button of ReverseOrderLine is illegal. Res: " + JSON.toJSONString(buttonDTOs)
                        + ". TradeOrderLineId: " + tradeOrderLineId + ". ReverseOrderLineId: " + reverseOrderLineId);
            }
        }
        //待卖家处理状态展示立即处理按钮
        if (reverseStatus == 10) {
            if (!buttonTypes.contains("view_details") && buttonDTOs.size() != 1) {
                errorMessages.add(DisputeError.ILLEGAL_REVERSE_BUTTON + ". Button of ReverseOrderLine is illegal. Res: " + JSON.toJSONString(buttonDTOs)
                        + ". TradeOrderLineId: " + tradeOrderLineId + ". ReverseOrderLineId: " + reverseOrderLineId);
            }
        }
        return errorMessages;
    }


    private Set<String> getButtonUrl(JSONArray buttonDTOs) {
        Set<String> urls = new HashSet<>();
        for (int i = 0; i < buttonDTOs.size(); i++) {
            urls.add(buttonDTOs.getJSONObject(i).getString("type"));
        }
        return urls;
    }

    private Set<String> getButtonTypes(JSONArray buttonDTOs) {
        Set<String> types = new HashSet<>();
        for (int i = 0; i < buttonDTOs.size(); i++) {
            types.add(buttonDTOs.getJSONObject(i).getString("detailUrl"));
        }
        return types;
    }

    private String getDisputeScenarios(String reverseStatus, String reverseBizType, String timeoutType, String reverseType) {
        StringBuilder sb = new StringBuilder();
        sb.append(reverseStatus)
                .append("-")
                .append(reverseBizType)
                .append("-")
                .append(timeoutType)
                .append("-")
                .append(reverseType);
        return sb.toString();
    }

    private List<String> checkItem(TradeOrderLineDTO tradeOrderLineDTO, ProductDTO product, JSONObject reverseOrderLine, Integer quantity, Long tradeOrderLineId, Long reverseOrderLineId) {
        List<String> errorMessages = new ArrayList<>();
        JSONObject item = reverseOrderLine.getJSONObject("item");
        if (item == null) {
            errorMessages.add(DisputeError.ILLEGAL_ITEM + ". Item of ReverseOrderLine is null. Res: " + JSONObject.toJSONString(reverseOrderLine));
            return errorMessages;
        }
        if (!product.getItemId().equals(item.getString("itemId"))) {
            errorMessages.add(DisputeError.ILLEGAL_ITEM_ID + ". ItemId of ReverseOrderLine is: " + item.getString("itemId")
                    + ". ItemId of Trade is: " + product.getItemId()
                    + ". TradeOrderLineId: " + tradeOrderLineId + ". ReverseOrderLineId: " + reverseOrderLineId);
        }
        int i = item.getString("itemPicUrl").indexOf(product.getItemPicUrl());
        if (i != 27) {
            errorMessages.add(DisputeError.ILLEGAL_ITEM_PIC_URL + ". ItemPicUrl of ReverseOrderLine is: " + item.getString("itemPicUrl")
                    + ". ItemPicUrl of Trade is: " + product.getItemPicUrl()
                    + ". TradeOrderLineId: " + tradeOrderLineId + ". ReverseOrderLineId: " + reverseOrderLineId);
        }
        JSONObject unitPriceOfReverse = item.getJSONObject("itemUnitPrice");
        if (unitPriceOfReverse == null) {
            errorMessages.add(DisputeError.ILLEGAL_ITEM_PRICE + ". ItemPrice of ReverseOrderLine is null. Res: " + JSON.toJSONString(item)
                    + ". TradeOrderLineId: " + tradeOrderLineId + ". ReverseOrderLineId: " + reverseOrderLineId);
        } else {
            MonetaryAmount unitFee = tradeOrderLineDTO.getUnitFee();
            if (!unitFee.getCurrency().getCurrencyCode().equals(unitPriceOfReverse.getString("currency"))) {
                errorMessages.add(DisputeError.ILLEGAL_ITEM_PRICE_CURRENCY + ". ItemPriceCurrency of ReverseOrderLine is: " + unitPriceOfReverse.getString("currency")
                        + ". ItemPriceCurrency of Trade is: " + unitFee.getCurrency()
                        + ". TradeOrderLineId: " + tradeOrderLineId + ". ReverseOrderLineId: " + reverseOrderLineId);
            }
            //交易商品金额取值异常，待处理
            int productPriceOfTrade;
            double number = unitFee.getNumber().doubleValue() * 100;
            productPriceOfTrade = (int) number;
            if (productPriceOfTrade != unitPriceOfReverse.getIntValue("cent")) {
                errorMessages.add(DisputeError.ILLEGAL_ITEM_PRICE_AMOUNT + ". ItemPriceAmount of ReverseOrderLine is: " + unitPriceOfReverse.getIntValue("cent")
                        + ". ItemPriceAmount of Trade is: " + productPriceOfTrade
                        + ". TradeOrderLineId: " + tradeOrderLineId + ". ReverseOrderLineId: " + reverseOrderLineId);
            }
        }
        if (!product.getItemTitle().equals(item.getString("itemTitle"))) {
            errorMessages.add(DisputeError.ILLEGAL_ITEM_TITLE + ". ItemTitle of ReverseOrderLine is: " + item.getString("itemTitle")
                    + ". ItemTitle of Trade is: " + product.getItemTitle()
                    + ". TradeOrderLineId: " + tradeOrderLineId + ". ReverseOrderLineId: " + reverseOrderLineId);
        }
        // check sku
        Map<Integer, String> skusOfReverse = itemSkusOfReverse(item.getJSONArray("skuDTOList"));
        Map<Integer, String> skusOfTrade = itemSkusOfTrade(JSONArray.parseArray(product.getSku().getSkuInfo()));
        if (skusOfReverse.size() != skusOfTrade.size()) {
            errorMessages.add(DisputeError.ILLEGAL_ITEM_SKU + ". ItemSku of ReverseOrderLine is: " + JSON.toJSONString(skusOfReverse)
                    + ". ItemSku of Trade is: " + JSON.toJSONString(skusOfTrade)
                    + ". TradeOrderLineId: " + tradeOrderLineId + ". ReverseOrderLineId: " + reverseOrderLineId);
        }
        if (skusOfReverse.size() > 0) {
            skusOfReverse.keySet().forEach(skuId -> {
                if (!skusOfReverse.get(skuId).equals(skusOfTrade.get(skuId))) {
                    errorMessages.add(DisputeError.ILLEGAL_ITEM_SKU
                            + ". SkuId is: " + skuId
                            + ". ItemSku of ReverseOrderLine is: " + skusOfReverse.get(skuId)
                            + ". ItemSku of Trade is: " + skusOfTrade.get(skuId)
                            + ". TradeOrderLineId: " + tradeOrderLineId + ". ReverseOrderLineId: " + reverseOrderLineId);
                }
            });
        }


        // check quantity
        if (!quantity.equals(item.getInteger("itemCount"))) {
            errorMessages.add(DisputeError.ILLEGAL_ITEM_QUANTITY + ". ItemQuantity of ReverseOrderLine is: " + item.getLong("itemCount")
                    + ". ItemQuantity of Trade is: " + quantity
                    + ". TradeOrderLineId: " + tradeOrderLineId + ". ReverseOrderLineId: " + reverseOrderLineId);
        }
        return errorMessages;
    }

    private Map<Integer, String> itemSkusOfReverse(JSONArray skuDTOList) {
        Map<Integer, String> map = new ConcurrentHashMap<>();
        if (skuDTOList != null) {
            for (int i = 0; i < skuDTOList.size(); i++) {
                JSONObject sku = skuDTOList.getJSONObject(i);
                map.put(sku.getIntValue("skuId"), sku.getString("skuValue"));
            }
        }
        return map;
    }

    private Map<Integer, String> itemSkusOfTrade(JSONArray skuDTOList) {
        Map<Integer, String> map = new ConcurrentHashMap<>();
        if (skuDTOList != null) {
            for (int i = 0; i < skuDTOList.size(); i++) {
                JSONObject sku = skuDTOList.getJSONObject(i);
                map.put(sku.getIntValue("valueId"), sku.getString("valueText"));
            }
        }
        return map;
    }

    private List<String> checkReverseStatus(JSONObject reverseOrderLine, int reverseStatus, Long tradeOrderLineId, Long reverseOrderLineId) {
        List<String> errorMessages = new ArrayList<>();
        String StringReverseStatus = reverseOrderLine.getString("reverseStatus");
        int intStatusCode = getIntStatusCode(StringReverseStatus);
        if (intStatusCode != reverseStatus) {
            errorMessages.add(DisputeError.ILLEGAL_REVERSE_STATUS + ". ReverseStatus of ReverseOrderLine is: " + intStatusCode
                    + ". Aim ReverseStatus is: " + reverseStatus
                    + ". TradeOrderLineId: " + tradeOrderLineId + ". ReverseOrderLineId: " + reverseOrderLineId);
        }
        return errorMessages;
    }


    private List<String> checkBuyer(BuyerInfoDTO buyer, JSONObject reverseOrderLine, Long tradeOrderLineId, Long reverseOrderLineId) {
        List<String> errorMessages = new ArrayList<>();
        if (reverseOrderLine.getJSONObject("buyer") == null) {
            errorMessages.add(DisputeError.ILLEGAL_BUYER + ". Buyer of ReverseOrderLine is null. Res: " + JSON.toJSONString(reverseOrderLine));
            return errorMessages;
        }
        if (!buyer.getBuyerId().equals(reverseOrderLine.getJSONObject("buyer").getLong("userId"))) {
            errorMessages.add(DisputeError.ILLEGAL_BUYER_ID + "Trade Buyer Id: " + buyer.getBuyerId()
                    + ". Reverse Buyer Id: " + reverseOrderLine.getJSONObject("buyer").getLong("userId")
                    + ". TradeOrderLineId: " + tradeOrderLineId + ". ReverseOrderLineId: " + reverseOrderLineId);
        }
        return errorMessages;
    }


    private AutoTestResponse checkTradeOrderLineDTO(Response<TradeOrderLineDTO> tradeOrderLineDTO, Long tradeOrderLineId) {
        AutoTestResponse autoTestResponse = new AutoTestResponse();
        if (!tradeOrderLineDTO.isSuccess() || tradeOrderLineDTO.getModule() == null) {
            List<String> errorMessages = new ArrayList<>();
            errorMessages.add(DisputeError.ERROR_RES_TRADE + ". Trade Order Line Id: " + tradeOrderLineId + ". Res: " + JSON.toJSONString(tradeOrderLineDTO));
            autoTestResponse.setErrorMessages(errorMessages);
            autoTestResponse.setPass(false);
            return autoTestResponse;
        }
        autoTestResponse.setPass(true);
        return autoTestResponse;
    }

    private AutoTestResponse checkReverseOrderLines(JSONArray sellerReverseOrderLines) {
        AutoTestResponse autoTestResponse = new AutoTestResponse();
        //check sellerReverseOrderLines值里面的东西只有1个
        if (sellerReverseOrderLines == null || sellerReverseOrderLines.size() != 1) {
            List<String> errorMessages = new ArrayList<>();
            String errorMessage = DisputeError.ERROR_RES_DISPUTE + ". sellerReverseOrderLines is null or the size of sellerReverseOrderLines is not 1. Res" + JSON.toJSONString(sellerReverseOrderLines);
            errorMessages.add(errorMessage);
            autoTestResponse.setErrorMessages(errorMessages);
            autoTestResponse.setPass(false);
            return autoTestResponse;
        }
        autoTestResponse.setPass(true);
        return autoTestResponse;
    }

    private List<AutoTestResponse> checkSearch(List<JSONObject> listResponses, int reverseStatus, Long sellerId) throws Exception {
        List<AutoTestResponse> autoTestResponses = new ArrayList<>();
        AutoTestResponse searchAutoTestResponse = new AutoTestResponse();
        searchAutoTestResponse.setDescription("卖家侧纠纷list-" + getStringStatusCode(reverseStatus) + ": 搜索");
        List<String> errorMsgs = new ArrayList<>();
        if (listResponses.size() > 0 && listResponses.get(0).getJSONObject("data").getJSONArray("dataSource").size() > 0) {
            JSONObject first = listResponses.get(0).getJSONObject("data").getJSONArray("dataSource").getJSONObject(0);
            JSONObject last = listResponses.size() <= 1 ? first :
                    listResponses.get(listResponses.size() - 1).getJSONObject("data").getJSONArray("dataSource")
                            .getJSONObject(listResponses.get(listResponses.size() - 1)
                                    .getJSONObject("data").getJSONArray("dataSource").size() - 1);
            JSONObject middle = listResponses.size() <= 2 ? first
                    : listResponses.get((listResponses.size() - 1) / 2).getJSONObject("data").getJSONArray("dataSource")
                    .getJSONObject(listResponses.get((listResponses.size() - 1) / 2).getJSONObject("data").getJSONArray("dataSource").size() / 2);
            errorMsgs.addAll(checkSearchChoice(sellerId, reverseStatus, first));
            if (first != last) {
                errorMsgs.addAll(checkSearchChoice(sellerId, reverseStatus, last));
            }
            if (first != middle) {
                errorMsgs.addAll(checkSearchChoice(sellerId, reverseStatus, middle));
            }
        } else {
            errorMsgs.add(DisputeError.ILLEGAL_SEARCH
                    + "Fail to get list response");
        }

//        // reverseStatus
//        List<JSONObject> all = getAllDisputeListByReverseStatus(buyerId, 1);
//        int allCount = all.get(0).getJSONObject("module").getIntValue("total");
//        List<JSONObject> inProgress = getAllDisputeListByReverseStatus(buyerId, 2);
//        int inProgressCount = inProgress.get(0).getJSONObject("module").getIntValue("total");
//        List<JSONObject> awaitingItemsReturn = getAllDisputeListByReverseStatus(buyerId, 3);
//        List<JSONObject> complete = getAllDisputeListByReverseStatus(buyerId, 4);
//        int completeCount = complete.get(0).getJSONObject("module").getIntValue("total");
//        if (allCount != inProgressCount + completeCount) {
//            errorMsgs.add(DisputeError.ILLEGAL_SEARCH
//                    + "Count Not Match. All : " + allCount + ". In Progress: " + inProgressCount + ". Complete: " + completeCount);
//        }
//        searchAutoTestResponse.setErrorMessages(errorMsgs);
//        autoTestResponses.add(searchAutoTestResponse);
//        List<AutoTestResponse> disputeInProgressAutoTestRes = checkBuyerInProgressDisputeList(inProgress);
//        List<AutoTestResponse> disputeAwaitingReturnAutoTestRes = checkBuyerAwaitingShipmentReturnDisputeList(awaitingItemsReturn);
//        List<AutoTestResponse> disputeCompleteAutoTestRes = checkBuyerCompleteDisputeList(complete);
//        autoTestResponses.addAll(disputeInProgressAutoTestRes);
//        autoTestResponses.addAll(disputeAwaitingReturnAutoTestRes);
//        autoTestResponses.addAll(disputeCompleteAutoTestRes);
        return autoTestResponses;
    }


    private List<String> checkSearchChoice(Long sellerId, int reverseStatus, JSONObject sellerReverseInfo) throws Exception {
        List<String> errorMessages = new ArrayList<>();
        JSONObject sellerReverseOrderLine = sellerReverseInfo.getJSONArray("sellerReverseOrderLines").getJSONObject(0);

        String orderId = sellerReverseOrderLine.getString("tradeOrderId");
        String buyerName = sellerReverseOrderLine.getJSONObject("buyer").getString("fullName");
        String productId = sellerReverseOrderLine.getJSONObject("aeItemDTO").getString("itemId");
//        String stringReverseBizType = sellerReverseOrderLine.getString("reverseBizType");
//        int reverseBizType = getIntReverseBizType(stringReverseBizType);

        JSONObject orderIdSearch = HsfUtil.getSellerDisputeListRequest(orderId, 10, 1, "en_US", "", reverseStatus, "", 1, sellerId, 10, 1, reverseStatus, "DESC", "");
        errorMessages.addAll(checkSearchChoice(orderId, "", "", "", orderIdSearch));
        JSONObject buyerNameSearch = HsfUtil.getSellerDisputeListRequest("", 10, 1, "en_US", buyerName, reverseStatus, "", 1, sellerId, 10, 1, reverseStatus, "DESC", "");
        errorMessages.addAll(checkSearchChoice("", buyerName, "", "", buyerNameSearch));
        JSONObject productIdSearch = HsfUtil.getSellerDisputeListRequest("", 10, 1, "en_US", "", reverseStatus, productId, 1, sellerId, 10, 1, reverseStatus, "DESC", "");
        errorMessages.addAll(checkSearchChoice("", "", productId, "", productIdSearch));
//        JSONObject reverseBizTypeSearch = HsfUtil.getSellerDisputeListRequest("",10,reverseBizType, "en_US", "", reverseStatus,"", 1,sellerId,10 ,1, reverseStatus,"DESC","");
//        errorMessages.addAll(checkSearchChoice("", "", "",stringReverseBizType,reverseBizTypeSearch));
        return errorMessages;
    }


    private List<String> checkSearchChoice(String orderId, String buyerName, String productId, String stringReverseBizType, JSONObject searchResult) {
        List<String> errorMessages = new ArrayList<>();
        JSONArray dataSourcesOfSearch = searchResult.getJSONObject("data").getJSONArray("dataSource");
        for (int i = 0; i < dataSourcesOfSearch.size(); i++) {
            JSONObject dataSourceOfSearch = dataSourcesOfSearch.getJSONObject(i);
            JSONObject sellerReverseOrderLine = dataSourceOfSearch.getJSONArray("sellerReverseOrderLines").getJSONObject(0);

            if (StringUtil.isEmpty(orderId)) {
                if (!sellerReverseOrderLine.getString("tradeOrderId").equals(orderId)) {
                    errorMessages.add(DisputeError.ILLEGAL_SEARCH
                            + ". Search OrderId: " + sellerReverseOrderLine.getString("tradeOrderId") + ". Original OrderId: " + orderId
                            + ". Trade Order Id: " + orderId
                            + ". Search Result: " + sellerReverseOrderLine.toJSONString());
                }
            } else if (StringUtil.isEmpty(buyerName)) {
                if (!sellerReverseOrderLine.getJSONObject("buyer").getString("fullName").equals(buyerName)) {
                    errorMessages.add(DisputeError.ILLEGAL_SEARCH
                            + ". Search BuyerName: " + sellerReverseOrderLine.getJSONObject("buyer").getString("fullName")
                            + ". Original BuyerName: " + buyerName
                            + ". Search Result: " + sellerReverseOrderLine.toJSONString());
                }
            } else if (StringUtil.isEmpty(productId)) {
                if (!sellerReverseOrderLine.getJSONObject("aeItemDTO").getString("itemId").equals(productId)) {
                    errorMessages.add(DisputeError.ILLEGAL_SEARCH
                            + ". Search ProductId: " + sellerReverseOrderLine.getJSONObject("aeItemDTO").getString("itemId")
                            + ". Original ProductId: " + productId
                            + ". Search Result: " + sellerReverseOrderLine.toJSONString());
                }
            } else if (StringUtil.isEmpty(stringReverseBizType)) {
                if (!sellerReverseOrderLine.getString("reverseBizType").equals(stringReverseBizType)) {
                    errorMessages.add(DisputeError.ILLEGAL_SEARCH
                            + ". Search ReverseBizType: " + sellerReverseOrderLine.getString("reverseBizType")
                            + ". Original ReverseBizType: " + stringReverseBizType
                            + ". Search Result: " + sellerReverseOrderLine.toJSONString());
                }
            }

        }
        return errorMessages;
    }


    private List<String> checkCommonFields(JSONObject sellerReverseOrderLine, Long reverseOrderLineId) {
        List<String> errorMessages = new ArrayList<>();
        Long tradeOrderLineId = sellerReverseOrderLine.getLong("tradeOrderLineId");
        // check tradeOrder is legal
        Response<TradeOrderLineDTO> tradeOrderLineDTO = orderQueryForBuyerFacade.queryTradeOrderLineById(tradeOrderLineId);
        Long buyerId = tradeOrderLineDTO.getModule().getBuyer().getBuyerId();

        AutoTestResponse autoTestResponse = checkTradeOrderLineDTO(tradeOrderLineDTO, tradeOrderLineId);
        if (!autoTestResponse.isPass()) {
            return autoTestResponse.getErrorMessages();
        }
        // seller返回空，不需要check

        // check buyer
        List<String> buyerErrorMsgs = checkBuyer(tradeOrderLineDTO.getModule().getBuyer(), sellerReverseOrderLine, tradeOrderLineId, reverseOrderLineId);
        if (CollectionUtils.isNotEmpty(buyerErrorMsgs)) {
            errorMessages.addAll(buyerErrorMsgs);
        }
        // check item
        List<String> itemErrorMsgs = checkItem(tradeOrderLineDTO.getModule(), tradeOrderLineDTO.getModule().getProduct(), sellerReverseOrderLine, tradeOrderLineDTO.getModule().getQuantity(), tradeOrderLineId, reverseOrderLineId);
        if (CollectionUtils.isNotEmpty(itemErrorMsgs)) {
            errorMessages.addAll(itemErrorMsgs);
        }

        return errorMessages;
    }


    private int getIntStatusCode(String reverseStatus) {
        switch (reverseStatus) {
            case "Currently ongoing":
                return 2;
            case "AliExpress is handling":
                return 3;
            case "Awaiting seller return address":
                return 4;
            case "Awaiting buyer return":
                return 5;
            case "Awaiting receipt":
                return 6;
            case "Awaiting warehouse  receipt":
                return 7;
            case "Complete":
                return 8;
            default:
                return 1;
        }
    }

    private String getStringStatusCode(int reverseStatus) {
        switch (reverseStatus) {
            case 2:
                return "Currently ongoing";
            case 3:
                return "AliExpress is handling";
            case 4:
                return "Awaiting seller return address";
            case 5:
                return "Awaiting buyer return";
            case 6:
                return "Awaiting receipt";
            case 7:
                return "Awaiting warehouse  receipt";
            case 8:
                return "Complete";
            default:
                return "";
        }
    }

    private String getReverseBizType(String businessTypeFromAPI) {
        switch (businessTypeFromAPI) {
            case "guarantee":
                return "AliExpress investigation";
            case "4pl":
                return "Cainiao 4PL dispute order";
            case "easy_return_insurance":
                return "Free return";
            case "guarantee_negotiation":
                return "AliExpress investigation with negotiation";
            case "italy_local_issue":
                return null;
            case "normal":
                return null;
            case "ae_plus_easy_return":
                return null;
            default:
                return null;
        }
    }

    private int getIntReverseBizType(String reverseBizType) {
        switch (reverseBizType) {
            case "AliExpress investigation with negotiation":
                return 2;
            case "Cainiao 4PL dispute order":
                return 3;
            case "Free return":
                return 5;
            default:
                return 1;
        }
    }

}
