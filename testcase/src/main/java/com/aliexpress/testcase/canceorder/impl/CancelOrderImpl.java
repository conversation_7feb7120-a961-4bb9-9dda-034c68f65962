package com.aliexpress.testcase.canceorder.impl;

import com.alibaba.ecommerce.module.Response;
import com.alibaba.global.buy.api.response.CreateOrderResult;
import com.alibaba.global.order.management.api.model.TradeOrderDTO;
import com.alibaba.global.payment.api.dto.payment.ChosenIntentionDTO;
import com.alibaba.global.payment.api.response.RenderIntentionResponse;
import com.aliexpress.databank.service.PaymentBizService;
import com.aliexpress.databank.service.ReverseService;
import com.aliexpress.databank.service.TradeService;
import com.aliexpress.issue.common.result.PlainResult;
import com.aliexpress.issue.dispute.pojo.common.IssueCancelOrderOperatorResult;
import com.aliexpress.testcase.canceorder.CancelOrder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class CancelOrderImpl implements CancelOrder {

    private static Long VIRTUAL_ITEM_ID = 4000687717802L;

    private static Long BUYER_ID = 1861263106L;

    @Autowired
    private TradeService tradeService;

    @Autowired
    private ReverseService reverseService;

    @Autowired
    private PaymentBizService paymentBizService;

    @Override
    public void virtualOrderWithoutPayment() throws Exception {
        Response<CreateOrderResult> createOrderResponse = tradeService.getPlaceOrderResponse(BUYER_ID, VIRTUAL_ITEM_ID, "", "", "");
        if (createOrderResponse.isSuccess() && createOrderResponse.getModule().isSuccess()) {
            List<String> orderIds = createOrderResponse.getModule().getOrderIds();
            Long orderId = Long.parseLong(orderIds.get(0));
            Response<TradeOrderDTO> tradeOrderDTOResponse = tradeService.queryTradeOrderById(BUYER_ID, orderId);
            ChosenIntentionDTO chosenIntentionDTO = paymentBizService.getWalletPay();
            Response<RenderIntentionResponse> renderIntentionResponse = paymentBizService.confirmIntention(tradeOrderDTOResponse.getModule(), chosenIntentionDTO);
            if (renderIntentionResponse.isSuccess()) {
                PlainResult<IssueCancelOrderOperatorResult> cancelOrderResult = reverseService.cancelOrder(BUYER_ID, orderId, "", tradeOrderDTOResponse.getModule().getOrderLines(), "BALANCE");
            }
        }
    }

    @Override
    public void virtualOrderWithPayment() {

    }

}
