//package com.aliexpress.testcase.sp.impl;
//
//import com.alibaba.intl.ae.biz.promisetemplate.share.dto.result.SpResult;
//import com.alibaba.intl.ae.biz.promisetemplate.share.service.interfaces.PromiseServiceForMtop;
//import com.aliexpress.testcase.gen.model.Module;
//import com.aliexpress.testcase.gen.model.SubModule;
//import com.aliexpress.testcase.gen.service.TestCaseService;
//import com.aliexpress.testcase.service.sp.GuaranteeNegotiationService;
//import com.aliexpress.testcase.sp.PromiseService;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
//import java.io.IOException;
//
//@Slf4j
//@Service
//public class PromiseServiceImpl implements PromiseService {
//
//    @Autowired
//    private TestCaseService testCaseService;
//
//    @Autowired
//    private GuaranteeNegotiationService guaranteeNegotiationService;
//
//    // 售后宝开放协商签约
//    @Override
//    public Module guarantee() throws IOException {
//        Module module = testCaseService.getModule("tc/spserver/guarantee.json");
//        SubModule sign = guaranteeNegotiationService.testSign(module);
//        SubModule acl = guaranteeNegotiationService.testAcl(module);
//        return module;
//    }
//
//
//}
