package serviceMetrics

import (
	"gitlab.alibaba-inc.com/global-cloud-native/serverless-iac/serverless"         // 服务
	"gitlab.alibaba-inc.com/global-cloud-native/serverless-iac/container"          // 容器
	res "gitlab.alibaba-inc.com/global-cloud-native/serverless-iac/resource"       // 资源
	rs "gitlab.alibaba-inc.com/global-cloud-native/serverless-iac/releasestrategy" // 发布策略
	"gitlab.alibaba-inc.com/global-cloud-native/serverless-iac/logging"            //日志
  "gitlab.alibaba-inc.com/global-cloud-native/serverless-iac/container/probe" //生命周期
  "gitlab.alibaba-inc.com/global-cloud-native/serverless-iac/sidecar"            //sidecar
  "gitlab.alibaba-inc.com/global-cloud-native/serverless-iac/volume" 						//volume
)

//日常环境变量  若无，则无需配置
dailyEnv: [
	{name: "ARTHAS_LIB_DIR", value: "/tmp"}
]
//预发环境变量 若无，则无需配置
//开启预发debug
preEnv: [
	{name: "ARTHAS_LIB_DIR", value: "/tmp"},
	{name: "JPDA_ENABLE", value: "1"}
]
//生产环境变量 若无，则无需配置
prodEnv: [
	{name: "ARTHAS_LIB_DIR", value: "/tmp"}
]

//生命周期探针 注：springboot1.4版本以下的 不需要接入生命周期
Probe: {
    livenessProbe:   probe.#DefaultLivenessHttpProbe
    readinessProbe: probe.#DefaultReadinessHttpProbe
}

TengineSideCarData: sidecar.#Tengine &{
  image: "reg.docker.alibaba-inc.com/aone-base-global/global-base-tengine-7u2:2.0.0" // 镜像
}




MyApp: serverless.#Service & {
    rasp:true

	releaseStrategy: rs.#Normal//rs.#Normal、rs.#Core
    //testing-ncloud表示日常环境的环境等级
	if APPSTACK_ENV_LEVEL == "testing-ncloud" {
		resource: res.#_4C8G //res.#_2C4G(2C4G)、res.#_4C8G(4C8G)、res.#_8C16G(8C16G)，按需选择，有需要的可以联系 @行禅
		replica:  1          //固定副本数
        mainContainer: container.#Main & {
			env: dailyEnv //环境变量
            //生命周期配置
			Probe
		}
	}
    //staging-ncloud表示预发环境的环境等级
	if APPSTACK_ENV_LEVEL == "staging-ncloud" {
		resource: res.#_4C8G //res.#_2C4G(2C4G)、res.#_4C8G(4C8G)、res.#_8C16G(8C16G)，按需选择，有需要的可以联系 @行禅
		replica:  2   //固定副本数
        mainContainer: container.#Main & {
			env: preEnv //环境变量
            //生命周期配置
			Probe
		}
	}
    //production-ncloud表示线上环境的环境等级
  if APPSTACK_ENV_LEVEL == "production-ncloud" {
		resource: res.#_4C8G //res.#_2C4G(2C4G)、res.#_4C8G(4C8G)、res.#_8C16G(8C16G)，按需选择，有需要的可以联系 @行禅
		replica:  2    //固定副本数
        mainContainer: container.#Main & {
			env: prodEnv //环境变量
            //生命周期配置
			Probe
		}
	}
	timezone:"Asia/Shanghai"//时区 "America/Los_Angeles" | "Asia/Shanghai" | "Asia/Bangkok" | "Asia/Karachi" | "Asia/Yangon" | "Asia/Dhaka" | "Asia/Colombo" | "Asia/Kathmandu"
	//如果没有通过SLS日志采集，则必须配置下面的内容
	logCollecting: logging.#Schema
	//将IaC中的Tengine配置文件带入Tengine容器中
  staticConfig: [
		{
			sourceFrom:   "./tengine/"           //tengine目录下的所有文件
			absolutePath: "/home/<USER>/cai/conf" //容器中Tengine的配置目录，此目录固定，不要更改
		}
	]
	sidecar:[TengineSideCarData] //声明使用Tengine容器作为SideCar
	volumes: [volume.#TengineLogsVolume]//声明Tengine的日志持久化，但Pod置换会导致丢失,如果对日志的要求很高请使用SLS采集。

}