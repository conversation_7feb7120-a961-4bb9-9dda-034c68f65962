<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

    <parent>
        <groupId>com.aliexpress.databank</groupId>
        <artifactId>ae-qa-trade-data-s</artifactId>
        <version>1.0.2-SNAPSHOT</version>
        <relativePath>../pom.xml</relativePath>
    </parent>

    <modelVersion>4.0.0</modelVersion>
    <artifactId>ae-qa-trade-data-s-service</artifactId>
    <packaging>jar</packaging>
    <name>ae-qa-trade-data-s-service</name>
    <properties>
        <bings.framework.version>1.3.0-SNAPSHOT</bings.framework.version>
    </properties>
    <dependencies>

        <dependency>
            <groupId>com.alibaba.fpark</groupId>
            <artifactId>bings-framework-et</artifactId>
            <version>${bings.framework.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba.fpark</groupId>
            <artifactId>bings-framework-base</artifactId>
            <version>${bings.framework.version}</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.fpark</groupId>
            <artifactId>bings-framework-executor</artifactId>
            <version>${bings.framework.version}</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.ultron</groupId>
            <artifactId>ultron-protocol-engine</artifactId>
            <version>1.0.4</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba.astore</groupId>
            <artifactId>astore-protocol-ultronage</artifactId>
            <version>1.5.6</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.taobao.wireless</groupId>
            <artifactId>mtop-common-service</artifactId>
            <version>1.0.3-SNAPSHOT</version>
        </dependency>


        <dependency>
            <groupId>com.aliexpress.boot</groupId>
            <artifactId>region-spring-boot-starter</artifactId>
            <version>3.2.1-sirius</version>

        </dependency>
        <dependency>
            <groupId>com.taobao.top</groupId>
            <artifactId>messaging-client</artifactId>
            <version>1.3.6</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.ae.group</groupId>
            <artifactId>ae-trade-api</artifactId>
            <version>2.0.24-GOLD-PROMOTION-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba.astore</groupId>
            <artifactId>astore-protocol-spi-common</artifactId>
            <version>1.4.2</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.8</version>
        </dependency>
        <dependency>
            <groupId>com.taobao.diamond</groupId>
            <artifactId>diamond-client</artifactId>
            <version>3.8.18</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.noear/snack3 -->
        <dependency>
            <groupId>org.noear</groupId>
            <artifactId>snack3</artifactId>
        </dependency>
        <dependency>
            <groupId>com.jayway.jsonpath</groupId>
            <artifactId>json-path</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alibaba.global.payment</groupId>
            <artifactId>payment-calculator</artifactId>
            <version>1.0.2-SIRIUS</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>pandora-hsf-spring-boot-starter</artifactId>
        </dependency>
        <!-- fastjson -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.70</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba.platform.shared</groupId>
            <artifactId>fastjson</artifactId>
            <version>999-not-exist-v3</version>
        </dependency>
        <!-- dingding -->

        <dependency>
            <groupId>com.dingtalk.api</groupId>
            <artifactId>taobao-sdk-java-auto</artifactId>
            <version>1479188381469-20180817</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.platform.shared</groupId>
            <artifactId>xxpt.gateway.shared.client</artifactId>
            <version>1.0.4</version>
        </dependency>
        <!-- trade -->
        <dependency>
            <groupId>com.alibaba.global</groupId>
            <artifactId>global-order-management-api</artifactId>
            <version>1.1.19-AE-DP-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>global-landlord-sdk</artifactId>
                    <groupId>com.alibaba.global</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>es-boot-international</artifactId>
                    <groupId>com.alibaba.ecommerce</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jackson-databind</artifactId>
                    <groupId>com.fasterxml.jackson.core</groupId>
                </exclusion>
            </exclusions>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.alibaba.ae.group</groupId>-->
<!--            <artifactId>ae-trade-api</artifactId>-->
<!--            <version>0.0.24-DOUBLR-COUNTING-SNAPSHOT</version>-->
<!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    <groupId>*</groupId>-->
<!--                    <artifactId>*</artifactId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.alibaba.global</groupId>
            <artifactId>global-landlord-sdk</artifactId>
            <version>2.0.0-stable</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.global.ic</groupId>
            <artifactId>global-ic-common</artifactId>
            <version>4.0.2-AE</version>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>com.aliexpress.boot</groupId>-->
        <!--            <artifactId>region-spring-boot-starter</artifactId>-->
        <!--            <version>3.2.1</version>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>com.alibaba.global</groupId>
            <artifactId>global-carts-api</artifactId>
            <version>1.1.2-UNIQUE-AE-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>servlet-api</artifactId>
                    <groupId>javax.servlet</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>es-boot-common</artifactId>
                    <groupId>com.alibaba.ecommerce</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>pandora-mtop-spring-boot-starter</artifactId>
                    <groupId>com.alibaba.boot</groupId>
                </exclusion>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.alibaba.global</groupId>
            <artifactId>global-buy-api</artifactId>
            <version>2.0.3-FOR-AE-INVENTORY</version>
            <exclusions>
                <exclusion>
                    <groupId>javax.servlet</groupId>
                    <artifactId>servlet-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.lazada</groupId>
                    <artifactId>*</artifactId>
                </exclusion>

                <exclusion>
                    <groupId>com.alibaba.middleware</groupId>
                    <artifactId>sdk</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba.global</groupId>
                    <artifactId>global-landlord-sdk</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba.global</groupId>
                    <artifactId>global-satellite-collect-plugin</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba.global</groupId>
                    <artifactId>global-satellite-trace-plugin</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.alibaba.global.payment</groupId>
            <artifactId>global-payment-api</artifactId>
            <version>1.0.24-RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba.global</groupId>
                    <artifactId>global-satellite-collect-plugin</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba.global</groupId>
                    <artifactId>global-satellite-trace-plugin</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.aliexpress.boot</groupId>
                    <artifactId>region-spring-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.aliexpress.share.art</groupId>
                    <artifactId>region-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.aliexpress.share.art</groupId>
                    <artifactId>routingtable-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.aliexpress.share.art</groupId>
                    <artifactId>middleware-hsf</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.aliexpress.share.art</groupId>
                    <artifactId>routingtable-micro</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.aliexpress.share.art</groupId>
                    <artifactId>art-route-service-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.aliexpress.share.art</groupId>
                    <artifactId>middleware-tddl</artifactId>
                </exclusion>

                <exclusion>
                    <groupId>com.lazada</groupId>
                    <artifactId>lazada-payment-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba.astore</groupId>
                    <artifactId>astore-protocol-ultronage</artifactId>
                </exclusion>
                <!--exclusion>
                    <groupId>com.alibaba.ecommerce</groupId>
                    <artifactId>es-boot-error</artifactId>
                </exclusion-->
                <exclusion>
                    <groupId>com.alibaba.ecommerce</groupId>
                    <artifactId>es-boot-error-autoconfigure</artifactId>
                </exclusion>

                <exclusion>
                    <groupId>com.alibaba.ecommerce</groupId>
                    <artifactId>es-boot-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba.ecommerce</groupId>
                    <artifactId>es-boot-serialize</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba.middleware</groupId>
                    <artifactId>mtop-uncenter-sdk</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.alibaba.global.payment</groupId>
            <artifactId>global-payment-api-simple</artifactId>
            <version>2.1.20-API-GLOBAL-AE-PAD-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba.global</groupId>
                    <artifactId>global-satellite-collect-plugin</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba.global</groupId>
                    <artifactId>global-satellite-trace-plugin</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.aliexpress.boot</groupId>
                    <artifactId>region-spring-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.aliexpress.share.art</groupId>
                    <artifactId>region-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.aliexpress.share.art</groupId>
                    <artifactId>routingtable-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.aliexpress.share.art</groupId>
                    <artifactId>middleware-hsf</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.aliexpress.share.art</groupId>
                    <artifactId>routingtable-micro</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.aliexpress.share.art</groupId>
                    <artifactId>art-route-service-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.aliexpress.share.art</groupId>
                    <artifactId>middleware-tddl</artifactId>
                </exclusion>

                <exclusion>
                    <groupId>com.lazada</groupId>
                    <artifactId>lazada-payment-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba.astore</groupId>
                    <artifactId>astore-protocol-ultronage</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba.ecommerce</groupId>
                    <artifactId>es-boot-error-autoconfigure</artifactId>
                </exclusion>

                <exclusion>
                    <groupId>com.alibaba.ecommerce</groupId>
                    <artifactId>es-boot-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba.ecommerce</groupId>
                    <artifactId>es-boot-serialize</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba.middleware</groupId>
                    <artifactId>mtop-uncenter-sdk</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.taobao.payment.boot</groupId>
            <artifactId>payment-boot-core</artifactId>
            <version>1.2.4</version>
            <exclusions>
                <exclusion>
                    <groupId>com.taobao.payment.boot</groupId>
                    <artifactId>payment-boot-common</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba.logback</groupId>
                    <artifactId>logback-classic</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>tmf2-core</artifactId>
                    <groupId>com.alibaba.tmf</groupId>
                </exclusion>
            </exclusions>

        </dependency>

        <dependency>
            <groupId>com.alibaba.global</groupId>
            <artifactId>global-basic-boot-exchange-starter</artifactId>
            <version>4.0.1</version>
            <exclusions>
                <exclusion>
                    <groupId>javax.servlet</groupId>
                    <artifactId>servlet-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.aliexpress</groupId>
            <artifactId>ae-wallet-api-member</artifactId>
            <version>1.3.19</version>
        </dependency>

        <dependency>
            <groupId>com.aliexpress</groupId>
            <artifactId>ae-wallet-api</artifactId>
            <version>1.3.13-MOCK-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.aliexpress</groupId>
            <artifactId>ae-wallet-api-asset</artifactId>
            <version>1.3.13-MOCK-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.aliexpress</groupId>
            <artifactId>ae-wallet-api-payment</artifactId>
            <version>1.3.13-MOCK-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.global</groupId>
            <artifactId>global-timeout-center-api</artifactId>
            <version>1.0.0</version>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba.global</groupId>
                    <artifactId>global-landlord-diamond-starter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba.global</groupId>
                    <artifactId>global-basic-boot-logging-starter</artifactId>
                </exclusion>

                <exclusion>
                    <groupId>com.alibaba.global</groupId>
                    <artifactId>global-basic-boot-pandora-starter</artifactId>
                </exclusion>

                <exclusion>
                    <groupId>com.alibaba.global</groupId>
                    <artifactId>global-basic-boot-serialize-starter</artifactId>
                </exclusion>

                <exclusion>
                    <groupId>com.alibaba.global</groupId>
                    <artifactId>global-basic-boot-serialize-autoconfigure</artifactId>
                </exclusion>

                <exclusion>
                    <groupId>com.alibaba.global</groupId>
                    <artifactId>global-landlord-metaq-boot-starter</artifactId>
                </exclusion>

                <exclusion>
                    <groupId>com.alibaba.middleware</groupId>
                    <artifactId>sdk</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>me.ele.uni</groupId>
            <artifactId>common-mq</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.taobao.metaq.final</groupId>
            <artifactId>metaq-client</artifactId>
            <version>4.2.6.Final</version>
        </dependency>
        <dependency>
            <groupId>com.aliexpress.issue.open</groupId>
            <artifactId>issue.open.share</artifactId>
            <version>1.3.5-fast-refund-SNAPSHOT</version>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.alibaba.global.payment</groupId>-->
<!--            <artifactId>global-payment-api</artifactId>-->
<!--            <version>2.1.9-API-GLOBAL-ISOLATED</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.aliexpress.issue</groupId>
            <artifactId>ae-issue-facade-s-api</artifactId>
            <version>1.0.21</version>
            <exclusions>
                <exclusion>
                    <groupId>com.thoughtworks.xstream</groupId>
                    <artifactId>xstream</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.aliexpress.issue</groupId>
            <artifactId>ae-issue-dispute-s-api</artifactId>
            <version>1.1.34-cancel-order-lines-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>tp.utils</artifactId>
                    <groupId>com.aliexpress.trade.open</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>art-route-service-client</artifactId>
                    <groupId>com.aliexpress.shart.art</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.alibaba.ae.group</groupId>
            <artifactId>ae-trade-reverse-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.global</groupId>
            <artifactId>global-reverse-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.global</groupId>
            <artifactId>reverse-platform-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.rdc.xcommerce</groupId>
            <artifactId>xcommerce-dispute-api</artifactId>
            <version>21.0518.icbu.v2</version>
            <exclusions>
                <exclusion>
                    <groupId>com.alibaba.middleware</groupId>
                    <artifactId>mtop-uncenter-sdk</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
       <!-- <dependency>
            <groupId>com.aliexpress.qa.accurate</groupId>
            <artifactId>ae-qa-accurate-s-service</artifactId>
            <version>2.0.2-SNAPSHOT</version>
        </dependency>-->
        <dependency>
            <groupId>com.alibaba.ae.service</groupId>
            <artifactId>ae-service-platform-api</artifactId>
            <version>2.3.12-********-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.ae.service</groupId>
            <artifactId>ae-service-platform-api</artifactId>
            <version>2.3.12-ON-TIME-GUARANTEE-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.aliexpress.databank</groupId>
            <artifactId>ae-qa-trade-data-api</artifactId>
            <version>1.0.3-SNAPSHOT</version>
        </dependency>
        <!--qa dc-->
        <dependency>
            <groupId>com.alibaba.global.qa</groupId>
            <artifactId>global-dc-common</artifactId>
            <version>1.0.3-cookie-SNAPSHOT</version>
        </dependency>
        <!--qa dc-->
        <dependency>
            <groupId>org.javers</groupId>
            <artifactId>javers-core</artifactId>
            <version>3.9.6</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba.global.uop</groupId>
            <artifactId>global-uop-api</artifactId>
            <version>2.1.8-AE</version>
            <exclusions>
                <exclusion>
                    <groupId>javax.servlet</groupId>
                    <artifactId>servlet-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.lazada</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba.middleware</groupId>
                    <artifactId>sdk</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba.global</groupId>
                    <artifactId>global-landlord-sdk</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba.global</groupId>
                    <artifactId>global-satellite-collect-plugin</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba.global</groupId>
                    <artifactId>global-satellite-trace-plugin</artifactId>
                </exclusion>
            </exclusions>
        </dependency>


        <!--<dependency>-->
        <!--<groupId>com.alibaba.price.center</groupId>-->
        <!--<artifactId>test</artifactId>-->
        <!--<version>0.0.3-SNAPSHOT</version>-->
        <!--</dependency>-->

        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
            <version>1.3.5</version>
        </dependency>

        <dependency>
            <groupId>com.taobao.hsf</groupId>
            <artifactId>hsf-plugin-scm-22</artifactId>
            <version>1.0.8-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.aliexpress.sellerpromise</groupId>
            <artifactId>aesp-spserver-api</artifactId>
            <version>1.0.26</version>
            <exclusions>
                <exclusion>
                    <groupId>com.thoughtworks.xstream</groupId>
                    <artifactId>xstream</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.alibaba.intl.ae.shared</groupId>
            <artifactId>trade.open.bops.share</artifactId>
            <version>2.1.3</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.business.qa</groupId>
            <artifactId>biz-simulator-common-base</artifactId>
            <version>1.1.20</version>
        </dependency>
        <dependency>
            <groupId>com.aliexpress.qa.data.open</groupId>
            <artifactId>shadowdata.share</artifactId>
            <version>1.0.9-trade-SNAPSHOT</version>
        </dependency>

        <!-- 地址老应用 -->
        <dependency>
            <groupId>com.alibaba.global.uic</groupId>
            <artifactId>global-address-api</artifactId>
            <version>1.1.45-MARS</version>
            <exclusions>
                <exclusion>
                    <groupId>javax.servlet</groupId>
                    <artifactId>servlet-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.lazada</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba.middleware</groupId>
                    <artifactId>sdk</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba.global</groupId>
                    <artifactId>global-landlord-sdk</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba.global</groupId>
                    <artifactId>global-satellite-collect-plugin</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba.global</groupId>
                    <artifactId>global-satellite-trace-plugin</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.aliexpress.logistics</groupId>
            <artifactId>logistics.open.address.share</artifactId>
            <version>3.1.55-MARS</version>
            <exclusions>
                <exclusion>
                    <groupId>javax.servlet</groupId>
                    <artifactId>servlet-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.lazada</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba.middleware</groupId>
                    <artifactId>sdk</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba.global</groupId>
                    <artifactId>global-landlord-sdk</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba.global</groupId>
                    <artifactId>global-satellite-collect-plugin</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba.global</groupId>
                    <artifactId>global-satellite-trace-plugin</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>com.alibaba.schedulerx</groupId>-->
<!--            <artifactId>schedulerx-client</artifactId>-->
<!--            <version>2.1.1</version>-->
<!--        </dependency>-->

<!--        <dependency>-->
<!--            <groupId>com.alibaba.schedulerx</groupId>-->
<!--            <artifactId>schedulerx-worker</artifactId>-->
<!--            <version>1.3.0.1</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.alibaba.schedulerx</groupId>
            <artifactId>schedulerx2-spring-boot-starter</artifactId>
            <version>1.3.0.1</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.logging.log4j</groupId>
                    <artifactId>log4j-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--  aliyun  sdk   api  -->
<!--        <dependency>-->
<!--            <groupId>com.aliyun</groupId>-->
<!--            <artifactId>aliyun-security-client-common</artifactId>-->
<!--            <version>1.2.10</version>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-security-client-acsclient</artifactId>
            <version>1.0.1</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
            <version>3.10.2</version>
            <exclusions>
                <exclusion>
                    <groupId>com.aliyun</groupId>
                    <artifactId>aliyun-java-sdk-kms</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-security-client-oss</artifactId>
            <version>1.0.1</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-security-client-sls-producer</artifactId>
            <version>1.0.2</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun.openservices</groupId>
            <artifactId>aliyun-log-producer</artifactId>
            <version>[0.3.2,0.3.9]</version>
        </dependency>
        <dependency>
            <groupId>com.aliyun.openservices</groupId>
            <artifactId>aliyun-log</artifactId>
            <version>0.6.61</version>
            <exclusions>
                <exclusion>
                    <groupId>com.google.protobuf</groupId>
                    <artifactId>protobuf-java</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-security-client-common</artifactId>
            <version>1.2.13</version>
            <exclusions>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-codec</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-java-sdk-core</artifactId>
            <version>4.5.17</version>
            <exclusions>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-codec</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.aliyun</groupId>
            <artifactId>aliyun-security-client-sls</artifactId>
            <version>1.0.2</version>
            <exclusions>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-codec</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.lazada</groupId>
            <artifactId>i-mp-test-api</artifactId>
            <version>1.3.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>es-boot-common</artifactId>
                    <groupId>com.alibaba.ecommerce</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>global-dc-common</artifactId>
                    <groupId>com.alibaba.global.qa</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.aliexpress.uic</groupId>
            <artifactId>uic-tag-client</artifactId>
            <version>3.1.12</version>
        </dependency>

        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>5.1.0</version>
            <exclusions>
                <exclusion>
                    <artifactId>log4j-api</artifactId>
                    <groupId>org.apache.logging.log4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>org.apache.poi</groupId>-->
<!--            <artifactId>poi-ooxml</artifactId>-->
<!--            <version>4.1.0</version>-->
<!--            <type>pom</type>-->
<!--        </dependency>-->
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.9.0</version>
        </dependency>
        <dependency>
            <groupId>commons-compress</groupId>
            <artifactId>commons-compress</artifactId>
            <version>20050911</version>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-api</artifactId>
            <version>2.14.1</version>
        </dependency>

        <!--request like python-->
        <dependency>
            <groupId>net.dongliu</groupId>
            <artifactId>requests</artifactId>
            <version>5.0.7</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.ae.qa</groupId>
            <artifactId>ae-qa-fulfillment-dchain-api</artifactId>
            <version>1.0.5-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
        </dependency>
        <!-- 打点 -->
        <dependency>
            <groupId>com.aliexpress.qa</groupId>
            <artifactId>platform-api</artifactId>
            <version>1.0.1</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.payment</groupId>
            <artifactId>payment-gateway-facade</artifactId>
            <version>1.0.41-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
<!--        <dependency>-->
<!--            <groupId>com.aliexpress</groupId>-->
<!--            <artifactId>ae-wallet-api-asset</artifactId>-->
<!--            <version>9.9.9-3S-SNAPSHOT</version>-->
<!--        </dependency>-->
        <!--        <dependency>-->
<!--            <groupId>org.apache.logging.log4j</groupId>-->
<!--            <artifactId>log4j-core</artifactId>-->
<!--            <version>2.9.0-sec</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.alibaba.bcp</groupId>-->
<!--            <artifactId>bcp-sdk</artifactId>-->
<!--            <version>1.0-SNAPSHOT</version>-->
<!--        </dependency>-->

        <dependency>
            <groupId>com.alibaba.saiga</groupId>
            <artifactId>saiga-link-case-api</artifactId>
            <version>1.0.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba.saiga</groupId>
            <artifactId>saiga-replay-api</artifactId>
            <version>1.0.37-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.alibaba.global.gcd</groupId>
                <artifactId>routingtable-dependencies</artifactId>
                <version>1.0.0-sirius</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <artifactId>maven-antrun-plugin</artifactId>
                    <version>${maven-antrun.version}</version>
                </plugin>
                <plugin>
                    <groupId>com.taobao.pandora</groupId>
                    <artifactId>pandora-boot-maven-plugin</artifactId>
                    <version>${pandora-boot-maven-plugin.version}</version>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>
</project>
