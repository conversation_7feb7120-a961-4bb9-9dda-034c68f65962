package com.aliexpress.databank.constant;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;

@Slf4j
public enum ReverseWorryFreeV2Status {

    REPORT("报案结果消息（面单返回）", "report", "ae_insurance_service",""),
    CLAIM_LOGISTICS_COLLECT("已揽件消息（ascan）", "claim_logistics","ae_insurance_service","SHIPPING"),
    CLAIM_LOGISTICS_RECEIPT("已签收消息（dscan）", "claim_logistics","ae_insurance_service","SIGNED"),
    CLAIM_CHECK("勘查结果消息（质检）", "claim_check", "ae_insurance_service",""),
    CLAIM_LOGISTICS_ANOMALY("物流异常消息（二揽）", "claim_logistics_anomaly", "ae_insurance_service",""),
    ;


    ReverseWorryFreeV2Status(String scenario, String tag, String topic,String currentLogisticsStatus) {
        this.scenario = scenario;
        this.tag = tag;
        this.topic = topic;
        this.currentLogisticsStatus = currentLogisticsStatus;
    }

    @Getter
    private String scenario;

    @Getter
    private String tag;

    @Getter
    private String topic;

    @Getter
    private String currentLogisticsStatus;


    public static ReverseWorryFreeV2Status getReverseWorryFreeV2StatusByScenario(String scenario) {
        return Arrays.stream(ReverseWorryFreeV2Status.values()).filter(it -> it.getScenario().equalsIgnoreCase(scenario)).findFirst().get();
    }

}

