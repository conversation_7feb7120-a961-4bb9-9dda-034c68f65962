package com.aliexpress.databank.constant;


import com.alibaba.common.lang.StringUtil;

public enum DataDisableScenario {
	/**
	 * 不可用原因
	 */
	USER_NO_EXIST("用户不存在",1),
	USER_RISK_CONTROL("用户风控中",2),
	PRODUCT_NO_EXIST("商品不存在",3),
	PRODUCT_STATUS_ERROR("商品不可用状态",4),
	PRODUCT_NOT_ENOUGH("商品已售罄",5),
	PRODUCT_STOCK_WARN("⚠️商品库存不足10个，请提前加库存",6),
	ADDRESS_NOT_MATCH("用户地址不匹配",7),
	NEED_CONFIRM("需手动email或短信验证",8);
	/**
	 * 成员变量
	 */
	private String name;
	private int index;
	//

	/**
	 * 构造方法
	 * @param name
	 * @param index
	 */
	private DataDisableScenario(String name, int index) {
		this.name = name;
		this.index = index;
	}

	/**
	 * 普通方法
	 * @param index
	 * @return
	 */
	public static String getName(int index) {
		for (DataDisableScenario c : DataDisableScenario.values()) {
			if (c.getIndex() == index) {
				return c.name;
			}
		}
		return null;
	}
	/**
	 * 普通方法
	 * @param name
	 * @return
	 */
	public static DataDisableScenario getTypeByName(String name) {
    	if (StringUtil.isEmpty(name)) {
			return null;
		}
		for (DataDisableScenario enums : DataDisableScenario.values()) {
			if (enums.getName().equals(name)) {
				return enums;
			}
		}
		return null;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public int getIndex() {
		return index;
	}
	public void setIndex(int index) {
		this.index = index;
	}
}
