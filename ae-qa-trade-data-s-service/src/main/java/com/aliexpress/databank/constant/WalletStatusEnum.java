package com.aliexpress.databank.constant;

import lombok.Getter;
import lombok.Setter;

public enum WalletStatusEnum {
	/**
	 * 钱包状态
	 */
	ACTIVATED("已开通",1),
	WAITING_ACTIVATE("未开通",2),
	NOT_ALLOW("不可开通",3);

	/**
	 * 成员变量
	 */
	@Getter
	@Setter
	private String name;

	@Getter
	@Setter
	private int num;

	WalletStatusEnum(String name, int num) {
		this.name = name;
		this.num = num;
	}

	public static Integer getNumByName(String name){
		for(WalletStatusEnum c: WalletStatusEnum.values()){
			if (name.equals(c.getName())){
				return c.getNum();
			}
		}
		return null;
	}

	public static String getNameByNum(int num){
		for(WalletStatusEnum c: WalletStatusEnum.values()){
			if (num == c.getNum()){
				return c.getName();
			}
		}
		return null;
	}

}
