package com.aliexpress.databank.constant;

import android.taobao.util.StringUtils;
import lombok.Getter;

public enum SellerStrategyAIEnum {

    PARTIAL_REFUND("部分退"),

    NEGOTIATE("协商"),

    AUTO_REFUND("低金额"),

    NO_QC("免质检"),

    RETURN_SOLUTION("退货策略"),

    ITEM_RETURN_SOLUTION("特殊商品");


    @Getter
    private String description;

    SellerStrategyAIEnum(String description) {
        this.description = description;
    }


    public static String getSellerStrategyByDescription(String description) {
        if (StringUtils.isEmpty(description)) {
            return null;
        }
        for (SellerStrategyAIEnum sellerStrategyEnum : SellerStrategyAIEnum.values()) {
            if (sellerStrategyEnum.description.equals(description)) {
                return sellerStrategyEnum.name();
            }
        }
        return null;
    }
}
