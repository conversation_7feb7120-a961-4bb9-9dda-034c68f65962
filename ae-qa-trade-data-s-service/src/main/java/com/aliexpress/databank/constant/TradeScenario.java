package com.aliexpress.databank.constant;


public class TradeScenario {

    /**
     * 父子同体
     */
    public static final String ONE_SON = "oneson";
    /**
     * 多子
     */
    public static final String SEVERAL_SON = "severalson";
    /**
     * 3C订单
     */
    public static final String THREE_C = "3c";
    /**
     * 美国征税订单
     */
    public static final String US_TAX = "ustax";
    /**
     * 澳洲征税订单
     */
    public static final String AU_TAX = "autax";
    /**
     * DDP订单
     */
    public static final String DDP = "ddp";
    /**
     * 自提订单
     */
    public static final String SELF_PICKUP = "selfpickup";
    /**
     * 店铺满减订单
     */

    public static final String SELLER_DISCOUNT = "sellerdiscount";
    /**
     * USD报价币种订单
     */
    public static final String US_POST = "uspost";
    /**
     * EUR报价币种订单
     */
    public static final String EUR_POST = "eurpost";
    /**
     * RUB报价币种订单
     */
    public static final String RU_POST = "rupost";
    /**
     * 满包邮
     */
    public static final String FREE_SHIPPING = "freeshipping";
    /**
     * 满件折
     */
    public static final String FULL_PIECE = "fullpiece";
    /**
     * 单品折扣
     */
    public static final String PE_MEMBER_DISCOUNT = "peMemberDiscount";



    }
