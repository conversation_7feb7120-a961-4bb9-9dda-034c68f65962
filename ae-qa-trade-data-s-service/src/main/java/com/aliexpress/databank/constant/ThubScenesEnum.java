package com.aliexpress.databank.constant;

import com.alibaba.fastvalidator.constraints.utils.StringUtils;
import lombok.Getter;

public enum ThubScenesEnum {
    THUB_OPEN_V3_DISPUTE("发起纠纷", "task_fQpDaA", "2000100", "fr", "thubOpenV3Dispute"),

    //面单
    THUB_MOCK_FR_MAILNO("面单-面单返回", "task_rjEHp6", "2000340", "local_free_return", "thubMockFRMailNo"),
    THUB_MOCK_FR_ASCAN("面单-ascan消息", "task_QnFff3", "2045100", "local_free_return", "thubMockFRASCAN"),
    THUB_MOCK_FR_DSCAN("面单-平台收货", "task_a7YKbj", "2050100", "local_free_return", "thubMockFRDSCAN"),
    THUB_MOCK_FR_QC("面单-质检消息", "task_H7JXxY", "2070050", "local_free_return", "thubMockFRQC"),

    //自寄
    THUB_MOCK_FR_SELF_DROP_OFF("买家自寄退货", "task_3ShPCY", "2045100", "self_drop_off", "thubSelfDropOff"),

    //上门揽件
    THUB_MOCK_VC_ACCEPT_ORDER("上门揽件-服务商接单成功", "task_bHRZey", "2000340", "visit_collect", "thubMockVCAcceptOrder"),
    THUB_MOCK_VC_BUYER_INFO_ERROR("买家信息错误拒单", "task_fEX2H4", "-2000340", "visit_collect", "thubMockVCBuyerInfoError"),
    THUB_MOCK_VC_COLLECT_AGAIN("确认二揽", "task_Q6wewf", "2000341", "visit_collect", "thubMockCollectAgain"),
    THUB_MOCK_VC_ASCAN("上门揽件-揽件消息", "task_abMDiY", "2045100", "visit_collect", "thubMockVCASCAN"),
    THUB_MOCK_VC_DSCAN("上门揽件-平台收货", "task_HemdCK", "2050100", "visit_collect", "thubMockVCDSCAN"),
    THUB_MOCK_VC_QC("上门揽件-质检消息", "task_tKknEp", "2070050", "visit_collect", "thubMockVCQC"),

    THUB_MOCK_FR_SELLER_CONFIRM_GOODS("商家确认收货", "task_Dn5rkM", "", "", "thubSellerConfirmGoods"),

    // 整合通用
    THUB_MOCK_ACCEPT_SUCCESS("履约单接单成功", "task_znt3MF", "2000340", "fr", "thubMockAcceptSuccess"),
    THUB_MOCK_ACCEPT_FAILED("收到二揽消息", "task_NbaEBJ", "-2000340", "fr", "thubMockAcceptFailed"),
    THUB_MOCK_RECEIVED_ASCAN("收到ASCAN消息", "task_4YsEh6", "2045100", "fr", "thubMockReceivedAscan"),
    THUB_MOCK_RECEIVED_DSCAN("收到DSCAN消息", "task_GB2ptP", "2050100", "fr", "thubMockReceivedDscan"),
    THUB_MOCK_QUALITY_CHECKED("收到质检完成消息", "task_kCMrFA", "2070050", "fr", "thubMockQualityChecked"),
    THUB_MOCK_CCO_ONLY_REFUND("mockCCO仅退款", "task_XyYKyN", "2070050", "fr", "thubMockQualityChecked"),
    THUB_MOCK_CCO_NOT_REFUND("mockCCO零退款", "task_3PSDxc", "2070050", "fr", "thubMockQualityChecked"),
    ;

    @Getter
    private final String scenes;

    @Getter
    private final String thubServiceId;

    @Getter
    private final String fulfillmentStatus;

    @Getter
    private final String returnWayType;

    @Getter
    private final String preFulfillmentStatus;

    ThubScenesEnum(String scenes, String thubServiceId, String fulfillmentStatus, String returnWayType, String preFulfillmentStatus) {
        this.scenes = scenes;
        this.thubServiceId = thubServiceId;
        this.fulfillmentStatus = fulfillmentStatus;
        this.returnWayType = returnWayType;
        this.preFulfillmentStatus = preFulfillmentStatus;
    }

    public static String getThubServiceIdByScenes(String scenes) {
        for (ThubScenesEnum c : ThubScenesEnum.values()) {
            if (scenes.equals(c.getScenes())) {
                return c.getThubServiceId();
            }
        }
        return null;
    }

    public static String getFulfillmentStatusByScenes(String scenes) {
        for (ThubScenesEnum c : ThubScenesEnum.values()) {
            if (scenes.equals(c.getScenes())) {
                return c.getFulfillmentStatus();
            }
        }
        return null;
    }

    public static String getThubServiceIdByFulfillmentStatus(String fulfillmentStatus, String returnWayType) {
        for (ThubScenesEnum c : ThubScenesEnum.values()) {
            if (StringUtils.isEmpty(returnWayType)) {
                returnWayType = "fr";
            }

            if (fulfillmentStatus.equals(c.getFulfillmentStatus()) && returnWayType.equals(c.getReturnWayType())) {
                return c.getThubServiceId();
            }
        }
        return null;
    }

}
