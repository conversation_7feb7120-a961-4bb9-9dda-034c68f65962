package com.aliexpress.databank.constant;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;

@Slf4j
public enum ReverseFulfillmentStatus {

    REJECT_ORDER("物流商拒单", "2010", "2020", "2010100", "", "P-2-1", "地址解析问题拒单"),
    ACCEPT_ORDER("物流商接单", "2010", "2015", "2010100", "", "", ""),
    PARCEL_REMOTE("偏远件接单", "2010", "2015", "2010100", "", "", ""),
    PARCEL_OVERSEA("海岛件接单", "2010", "2015", "2010100", "", "", ""),
    PARCEL_REMOTE_OVERSEA("偏远件海岛件接单", "2010", "2015", "2010100", "", "", ""),

    BUYER_INFO_ERROR("买家信息错误拒单", "2015", "2030", "2015100", "", "BUYER_INFO_ERROR", "用户地址错误"),
    PARCEL_OVERSIZE("海岛大件拒单", "2015", "2030", "2015100", "", "PARCEL_OVERSIZE", "海岛大件拒单"),
    PARCEL_RELE("货品带磁带电拒单", "2010", "2020", "2010100", "", "RELE", "货品带磁带电拒单"),
    PARCEL_RBAT("货品带电拒单", "2010", "2020", "2010100", "", "RBAT", "货品带电拒单"),
    PARCEL_RSPE("特货(粉膏液)拒单", "2010", "2020", "2010100", "", "RSPE", "特货(粉膏液)拒单"),

    ASCAN("上门揽收成功(ASCAN)", "2015", "2045", "2015100", "", "", ""),

    DSCAN("仓库已收到货(DSCAN)", "2045", "2050", "", "", "", ""),

    QC("质检结束", "2050", "2070", "2050100", "2070050", "", ""),

    OPEARION("实操","2050","2070","2015100","2070120","",""),

    ;


    ReverseFulfillmentStatus(String scenario, String srcStatus, String targetStatus, String srcOrderBizStatus,
                             String targetOrderBizStatus, String errorCode, String errorMsg) {
        this.scenario = scenario;
        this.srcStatus = srcStatus;
        this.targetStatus = targetStatus;
        this.srcOrderBizStatus = srcOrderBizStatus;
        this.targetOrderBizStatus = targetOrderBizStatus;
        this.errorCode = errorCode;
        this.errorMsg = errorMsg;
    }

    @Getter
    private String scenario;

    @Getter
    private String srcStatus;

    @Getter
    private String targetStatus;

    @Getter
    private String srcOrderBizStatus;

    @Getter
    private String targetOrderBizStatus;

    @Getter
    private String errorCode;

    @Getter
    private String errorMsg;


    public static ReverseFulfillmentStatus getReverseFulfillmentConstantByScenario(String scenario) {
        return Arrays.stream(ReverseFulfillmentStatus.values()).filter(it -> it.getScenario().equalsIgnoreCase(scenario)).findFirst().get();
    }

}

