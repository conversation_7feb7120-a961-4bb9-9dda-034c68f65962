package com.aliexpress.databank.constant;

import lombok.Getter;

public enum AepayRouteFailReasonEum {
    CardAssetRuleProcessor("CardAssetRuleProcessor", "支付信用卡非aepay绑定信用卡，不符合aepay路由规则，请前往diamond查看路由配置规则：https://mse.alibaba-inc.com/rg-us-east-pre/diamond/configlist/configdetail?dataId=aepay.gray.route.config&group=AE_GLOBAL_ae-payment-s&namespaceId=ae-payment-ns-s&tab=content"),
    CardBinCountryRuleProcessor("CardBinCountryRuleProcessor", "支付信用卡的卡country不符合aepay路由规则，请前往diamond查看路由配置规则：https://mse.alibaba-inc.com/rg-us-east-pre/diamond/configlist/configdetail?dataId=aepay.gray.route.config&group=AE_GLOBAL_ae-payment-s&namespaceId=ae-payment-ns-s&tab=content"),
    CardBrandRuleProcessor("CardBrandRuleProcessor", "支付信用卡的卡品牌不符合aepay路由规则，请前往diamond查看路由配置规则：https://mse.alibaba-inc.com/rg-us-east-pre/diamond/configlist/configdetail?dataId=aepay.gray.route.config&group=AE_GLOBAL_ae-payment-s&namespaceId=ae-payment-ns-s&tab=content"),
    PayerGrayPrecentRuleProcessor("PayerGrayPrecentRuleProcessor", "用户userId未满足aepay切流百分比，请前往diamond查看路由配置规则：https://mse.alibaba-inc.com/rg-us-east-pre/diamond/configlist/configdetail?dataId=aepay.gray.route.config&group=AE_GLOBAL_ae-payment-s&namespaceId=ae-payment-ns-s&tab=content"),
    PayAmountLimitRuleProcessor("PayAmountLimitRuleProcessor", "支付金额未满足aepay限额规则，请前往diamond查看路由配置规则：https://mse.alibaba-inc.com/rg-us-east-pre/diamond/configlist/configdetail?dataId=aepay.gray.route.config&group=AE_GLOBAL_ae-payment-s&namespaceId=ae-payment-ns-s&tab=content"),
    PayCurrencyRuleProcessor("PayCurrencyRuleProcessor", "支付币种未满足aepay路由规则，请前往diamond查看路由配置规则：https://mse.alibaba-inc.com/rg-us-east-pre/diamond/configlist/configdetail?dataId=aepay.gray.route.config&group=AE_GLOBAL_ae-payment-s&namespaceId=ae-payment-ns-s&tab=content！"),
    PlatformRuleProcessor("PlatformRuleProcessor", "支付platform未满足aepay路由规则，请前往diamond查看路由配置规则：https://mse.alibaba-inc.com/rg-us-east-pre/diamond/configlist/configdetail?dataId=aepay.gray.route.config&group=AE_GLOBAL_ae-payment-s&namespaceId=ae-payment-ns-s&tab=content"),
    RouteChannelCodeRuleProcessor("RouteChannelCodeRuleProcessor", "支付渠道未满足aepay路由规则，请前往diamond查看路由配置规则：https://mse.alibaba-inc.com/rg-us-east-pre/diamond/configlist/configdetail?dataId=aepay.gray.route.config&group=AE_GLOBAL_ae-payment-s&namespaceId=ae-payment-ns-s&tab=content"),
    SellerCountryRuleProcessor("SellerCountryRuleProcessor", "支付渠道未满足aepay路由规则，请前往diamond查看路由配置规则：https://mse.alibaba-inc.com/rg-us-east-pre/diamond/configlist/configdetail?dataId=aepay.gray.abliity.config&group=AE_GLOBAL_ae-payment-s&namespaceId=ae-payment-ns-s&tab=content"),
    SellerOrderCurrencyRuleProcessor("SellerOrderCurrencyRuleProcessor", "卖家报价币种未满足aepay路由规则，请前往diamond查看路由配置规则：https://mse.alibaba-inc.com/rg-us-east-pre/diamond/configlist/configdetail?dataId=aepay.gray.abliity.config&group=AE_GLOBAL_ae-payment-s&namespaceId=ae-payment-ns-s&tab=content"),
    ShipToCountryRuleProcessor("ShipToCountryRuleProcessor", "shipping adress的ship to国家未满足aepay路由规则，请前往diamond查看路由配置规则：https://mse.alibaba-inc.com/rg-us-east-pre/diamond/configlist/configdetail?dataId=aepay.gray.route.config&group=AE_GLOBAL_ae-payment-s&namespaceId=ae-payment-ns-s&tab=content"),
    SupportMixedOrderCurrencyRuleProcessor("SupportMixedOrderCurrencyRuleProcessor", "合并支付的报价币种未同时满足aepay路由规则，请前往diamond查看路由配置规则：https://mse.alibaba-inc.com/rg-us-east-pre/diamond/configlist/configdetail?dataId=aepay.gray.abliity.config&group=AE_GLOBAL_ae-payment-s&namespaceId=ae-payment-ns-s&tab=content");

    @Getter
    private String reason;

    @Getter
    private String msg;

    AepayRouteFailReasonEum(String reason, String msg) {
        this.reason = reason;
        this.msg = msg;
    }

    public static String getRealMsg(String reason) {
        AepayRouteFailReasonEum[] eums = AepayRouteFailReasonEum.values();
        for (AepayRouteFailReasonEum eum : eums) {
            if (reason.equals(eum.getReason())) {
                return eum.getMsg();
            }
        }
        return null;
    }
}
