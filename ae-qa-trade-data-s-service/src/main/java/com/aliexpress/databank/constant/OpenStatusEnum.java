package com.aliexpress.databank.constant;

import lombok.Getter;
import lombok.Setter;

public enum OpenStatusEnum {
	/**
	 * 钱包状态
	 */
	OPENED("已开通",true),
	NOT_OPENED("未开通",false);

	/**
	 * 成员变量
	 */
	@Getter
	@Setter
	private String name;

	@Getter
	@Setter
	private Boolean type;

	OpenStatusEnum(String name, Boolean type) {
		this.name = name;
		this.type = type;
	}

	public static Boolean getNumByName(String name){
		for(OpenStatusEnum c: OpenStatusEnum.values()){
			if (name.equals(c.getName())){
				return c.getType();
			}
		}
		return null;
	}

	public static String getNameByType(Boolean type){
		for(OpenStatusEnum c: OpenStatusEnum.values()){
			if (type.equals(c.getType())){
				return c.getName();
			}
		}
		return null;
	}

}
