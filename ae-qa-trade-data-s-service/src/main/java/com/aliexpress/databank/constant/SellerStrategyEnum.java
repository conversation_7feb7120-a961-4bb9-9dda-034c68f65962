package com.aliexpress.databank.constant;

import android.taobao.util.StringUtils;
import lombok.Getter;

public enum SellerStrategyEnum {

    LOCAL_SERVICE_AUTO_REFUND("海外托管小金额LOCAL_SERVICE_AUTO_REFUND"),

    LOCAL_SERVICE_NEGOTIATE("海外托管协商LOCAL_SERVICE_NEGOTIATE"),

    LOCAL_SERVICE_STORE_OWNED_LOGISTIC("海外托管退货策略LOCAL_SERVICE_STORE_OWNED_LOGISTIC"),

    LOCAL_POP_NEGOTIATE("本地pop协商LOCAL_POP_NEGOTIATE"),

    LOCAL_POP_MULTI_AUTO_REFUND("本地pop小金额LOCAL_POP_MULTI_AUTO_REFUND"),

    LOCAL_POP_MULTI_LOGISTIC("本地pop退货策略LOCAL_POP_MULTI_LOGISTIC"),

    PARTIAL_REFUND("部分退PARTIAL_REFUND"),

    NEGOTIATE("协商NEGOTIATE"),

    AUTO_REFUND("低金额AUTO_REFUND"),

    NO_QC("免质检NO_QC"),

    RETURN_SOLUTION("退货策略RETURN_SOLUTION"),

    ITEM_RETURN_SOLUTION("特殊商品ITEM_RETURN_SOLUTION");


    @Getter
    private String description;

    SellerStrategyEnum(String description) {
        this.description = description;
    }


    public static String getSellerStrategyByDescription(String description) {
        if (StringUtils.isEmpty(description)) {
            return null;
        }
        for (SellerStrategyEnum sellerStrategyEnum : SellerStrategyEnum.values()) {
            if (sellerStrategyEnum.description.equals(description)) {
                return sellerStrategyEnum.name();
            }
        }
        return null;
    }
}
