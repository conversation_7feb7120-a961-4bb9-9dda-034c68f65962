package com.aliexpress.databank.constant;

public enum CheckCaseEnum {
    /**
     * 场景期望case
     */
    CREATE_AND_PAY_GOODS_NAME("验证goodsName长度",1),
    CREATE_AND_PAY_GOODS_URL("验证是否带goodsUrl",2),
    CREATE_AND_PAY_MONEY("校验金额",3),
    CREATE_AND_PAY_META_DATA("验证paymentMethodMetaData请求参数",4),
    CREATE_AND_PAY_SPLIT_ORDER("验证金额是否拆分到子订单且金额正确",5),
    PAY_SUCCESS_MESSAGE_METHOD("验证支付渠道是否匹配",6),
    PAY_SUCCESS_MESSAGE_PAID_AMOUNT("验证订单优惠后金额是否正确",7),
    PAY_SUCCESS_MESSAGE_PAID_CASH("验证实际支付金额是否正确",8),
    PAY_SUCCESS_MESSAGE_CASHBACK_AMOUNT("验证钱包优惠金额是否正确",9),
    PAY_SUCCESS_MESSAGE_PAYMENT_PROMOTION_AMOUNT("验证支付优惠总金额是否正确",10),
    PROMOTION_REVERSE_MESSAGE_RETURN_PAYABLE_AMOUNT("验证可退金额是否正确",11),
    PROMOTION_REVERSE_MESSAGE_RETURN_PROMOTION_AMOUNT("验证可退支付优惠金额是否正确",12),

    //线下码消息内容
    PAYMENT_CODE_MESSAGE_HIDEOFFLINECODETIME("验证消息体是否包含hideOfflineCodeTime",13),
    PAYMENT_CODE_MESSAGE_APPPAYRESULTURL("验证消息体是否包含appPayResultUrl",14),
    PAYMENT_CODE_MESSAGE_OFFLINEPAYMENT("验证消息体是否包含offlinePayment",15),
    PAYMENT_CODE_MESSAGE_PAYMENTMETHODTYPE("验证消息体是否包含paymentMethodType",16),
    PAYMENT_CODE_MESSAGE_PCPAYRESULTURL("验证消息体是否包含pcPayResultUrl",17),
    PAYMENT_CODE_MESSAGE_PAYRESULTURL("验证消息体是否包含payResultUrl",18),
    PAYMENT_CODE_MESSAGE_MSITEPAYRESULTURL("验证消息体是否包含msitePayResultUrl",19),
    PAYMENT_CODE_MESSAGE_ATTRIBUTR("验证消息体是否包含attributes",20),
    PAYMENT_CODE_MESSAGE_BIZORDERID("验证消息体是否包含bizOrderId",21),
    PAYMENT_CODE_MESSAGE_CHECKOUTORDERID("验证消息体是否包含checkoutOrderId",22),
    PAYMENT_CODE_MESSAGE_PAYPLANID("验证消息体是否包含payPlanId",23);

    /**
     * 成员变量
     */
    private String name;
    private int index;
    //

    /**
     * 构造方法
     * @param name
     * @param index
     */
    private CheckCaseEnum(String name, int index) {
        this.name = name;
        this.index = index;
    }

    /**
     * 普通方法
     * @param index
     * @return
     */
    public static String getName(int index) {
        for (CheckCaseEnum c : CheckCaseEnum.values()) {
            if (c.getIndex() == index) {
                return c.name;
            }
        }
        return null;
    }
    public String getName() {
        return name;
    }
    public void setName(String name) {
        this.name = name;
    }
    public int getIndex() {
        return index;
    }
    public void setIndex(int index) {
        this.index = index;
    }

}
