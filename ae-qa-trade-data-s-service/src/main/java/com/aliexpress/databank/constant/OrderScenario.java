package com.aliexpress.databank.constant;

import lombok.Getter;

public enum OrderScenario {

    //  for reverse
    NORMAL_NEGOTIATION("NORMAL_NEGOTIATION"), // 普通协商的
    GLOBAL_GUARANTEE("GLOBAL_GUARANTEE"), // 售后宝
    BR_GUARANTEE("BR_GUARANTEE"), // 巴西售后宝
    FR_GUARANTEE("FR_GUARANTEE"), // 法国售后宝
    ES_GUARANTEE("ES_GUARANTEE"), // 西班牙售后宝
    FOUR_P_L("FOUR_P_L"), // 4PL
    LOCAL_2_LOCAL("LOCAL_2_LOCAL"), // 本对本
    CJ_FREE_RETURN("CJ_FREE_RETURN"), // 无忧退
    CN_FREE_RETURN("CN_FREE_RETURN"), // 海外仓退货
    DEFAULT("DEFAULT"); // 默认，无实际意义
    ;

    @Getter
    private String name;

    OrderScenario() {
    }

    OrderScenario(String name) {
        this.name = name;
    }
}
