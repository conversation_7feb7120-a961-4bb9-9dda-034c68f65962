package com.aliexpress.databank.constant;

import lombok.Getter;

import java.util.Arrays;

public enum LogisticsServiceProviderEnum {
    YIBANG_3PL("3PL-亦邦", "3PL", "yibang_cs"),
    USPEED_3PL("3PL-Uspeed", "3PL", "uspeed"),
    CAINIAO_4PL("4PL-菜鸟", "4PL", "CAINIAO"),
    UNKNOWN("未知服务商", "unknown", "unknown");


    LogisticsServiceProviderEnum(String providerName, String LogisticsServiceProvider, String LogisticsServiceProviderCode) {
        this.providerName = providerName;
        this.LogisticsServiceProvider = LogisticsServiceProvider;
        this.LogisticsServiceProviderCode = LogisticsServiceProviderCode;
    }

    @Getter
    private String providerName;

    @Getter
    private String LogisticsServiceProvider;

    @Getter
    private String LogisticsServiceProviderCode;


    public static LogisticsServiceProviderEnum getLogisticsServiceProviderByName(String providerName) {
        return Arrays.stream(LogisticsServiceProviderEnum.values()).filter(it -> it.getProviderName().equalsIgnoreCase(providerName)).findFirst().get();
    }

}


