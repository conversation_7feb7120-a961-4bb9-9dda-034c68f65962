package com.aliexpress.databank.constant;

import android.taobao.util.StringUtils;
import lombok.Getter;

public enum CountryEnum {
    DE("德国"),
    IL("以色列"),
    AE("阿联酋"),
    JP("日本"),
    BH("巴林"),
    KR("韩国"),
    MX("墨西哥"),
    IT("意大利"),
    KW("科威特"),
    FR("法国"),
    SA("沙特"),
    ES("西班牙"),
    QA("卡塔尔"),
    AU("澳大利亚"),
    GB("英国"),
    PL("波兰"),
    OM("阿曼"),
    US("美国"),
    NL("荷兰");
    @Getter
    private String countryName;

    CountryEnum(String countryName) {
        this.countryName = countryName;
    }

    public static String getCountryByCountryName(String countryName) {
        if (StringUtils.isEmpty(countryName)) {
            return null;
        }
        for (CountryEnum countryEnum : CountryEnum.values()) {
            if (countryEnum.countryName.equals(countryName)) {
                return countryEnum.name();
            }
        }
        return null;
    }

}
