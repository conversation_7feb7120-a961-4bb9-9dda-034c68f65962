package com.aliexpress.databank.constant;

import java.util.Arrays;

public enum CompensateAuditReason {
    FULL_REFUND("全部差额已赔付", "Y","全部差额已赔付"),
    PARTIAL_AMOUNT_EXCEEDED_LIMIT("部分金额超过上限（整月累计未超上线，单笔订单超上限）", "N","部分金额超过上限"),
    PARTIAL_AMOUNT_SUPPORTED("审核后仅支持部分金额", "Y","审核后仅支持部分金额"),
    INSUFFICIENT_INFORMATION("提交信息不足够，不符合要求", "N","提交信息不足够不符合要求"),
    NO_PRICE_DIFFERENCE("价格不存在价差", "N","价格不存在价差"),
    RISK_USER("风险用户", "N","风险用户"),
    MONTHLY_LIMIT_EXCEEDED("整月累计超过上限，全部拒赔", "N","整月累计超过上限全部拒赔"),
    MONTHLY_LIMIT_PARTIAL_AMOUNT("整月累计超上限，仅赔付部分金额", "N","整月累计超上限仅赔付部分金额"),
    OTHER("其他", "N","其他");

    private final String auditReasonDescription;
    private final String auditStatus;
    private final String expression;

    CompensateAuditReason(String auditReasonDescription, String auditStatus,String expression) {
        this.auditReasonDescription = auditReasonDescription;
        this.auditStatus = auditStatus;
        this.expression = expression;
    }

    public String getAuditReasonDescription() {
        return auditReasonDescription;
    }

    public String getAuditStatus() {
        return auditStatus;
    }

    public String getExpression() {
        return expression;
    }

    public static CompensateAuditReason getCompensateAuditReason(String expression) {
        return Arrays.stream(CompensateAuditReason.values()).filter(it -> it.getExpression().equalsIgnoreCase(expression)).findFirst().get();
    }

}
