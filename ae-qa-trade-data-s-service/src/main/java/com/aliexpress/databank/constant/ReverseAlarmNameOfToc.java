package com.aliexpress.databank.constant;

import lombok.Getter;

public enum ReverseAlarmNameOfToc {

    WAIT_SOLUTION_TIMEOUT("WAIT_SOLUTION_TIMEOUT", "协商超时任务"),
    WAIT_SELLER_FILL_ADDRESS_TIMEOUT("WAIT_SELLER_FILL_ADDRESS_TIMEOUT", "卖家补充退货地址超时任务"),
    WAIT_BUYER_RETURN_ITEM_WILL_TIMEOUT("WAIT_BUYER_RETURN_ITEM_WILL_TIMEOUT", "买家退货超时提醒"),
    WAIT_BUYER_RETURN_ITEM_TIMEOUT("WAIT_BUYER_RETURN_ITEM_TIMEOUT", "买家退货超时任务"),
    WAIT_RECEIVE_ITEM_WILL_TIMEOUT("WAIT_RECEIVE_ITEM_WILL_TIMEOUT", "卖家收货超时提醒-3天"),
    WAIT_RECEIVE_ITEM_TIMEOUT("WAIT_RECEIVE_ITEM_TIMEOUT", "卖家收货超时任务"),
    WAIT_CREATE_CLAIMS_TIMEOUT("WAIT_CREATE_CLAIMS_TIMEOUT", "理赔报案重试");

    @Getter
    private String value;

    @Getter
    private String name;

    ReverseAlarmNameOfToc(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public static ReverseAlarmNameOfToc getReverseAlarmNameOfToc(String alarmName) {
        ReverseAlarmNameOfToc[] alarmNames = ReverseAlarmNameOfToc.values();
        for (ReverseAlarmNameOfToc value : alarmNames) {
            if (value.getValue().equals(alarmName)) {
                return value;
            }
        }
        return null;
    }

}
