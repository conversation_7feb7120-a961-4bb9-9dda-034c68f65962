package com.aliexpress.databank.constant;

import lombok.Getter;

public enum MonetizationPenalties {

    NOT_SOLD_1("成交不卖", "timeoutCancel", "seller_send_goods_timeout","pop"),
    NOT_SOLD_2("成交不卖", "timeoutCancel", "Unable_to_ship", "自营"),
    FAKE_SHIPMENT_1("虚假发货", "LogisticCloseCancel", "logistics_oversea_close", "海外发货"),
    FAKE_SHIPMENT_2("虚假发货", "LogisticCloseCancel", "logistics_close", "跨境"),



    ;
    public String getType() {
        return type;
    }

    @Getter
    private String type;

    @Getter
    private String cancelEvent;

    @Getter
    private String cancelReason;

    @Getter
    private String extraType;

    MonetizationPenalties(String type, String cancelEvent, String cancelReason,String extraType) {
        this.type = type;
        this.cancelEvent = cancelEvent;
        this.cancelReason = cancelReason;
        this.extraType = extraType;
    }
    }
