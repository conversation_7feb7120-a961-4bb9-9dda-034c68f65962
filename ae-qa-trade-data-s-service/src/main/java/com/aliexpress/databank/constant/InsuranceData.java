package com.aliexpress.databank.constant;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSON;

import java.math.BigInteger;
import java.time.LocalDateTime;
import java.time.ZoneOffset;

/**
 * <AUTHOR>
 * @date 2024/7/18
 */
public class InsuranceData {
    public static void main(String[] args) {
        System.out.println(getMsgBody().toJSONString());
    }

    public static JSONObject getMsgBody() {
        String insuranceNo = "01960100001180724071800000017001";
        long timestamp = System.currentTimeMillis();
        // 获取当前时间
        LocalDateTime now = LocalDateTime.now();
        // 当前时间加上90天
        LocalDateTime futureDate = now.plusDays(90);
        // 转换为Unix时间戳（毫秒）
        long futureTimestampMillis = futureDate.toInstant(ZoneOffset.UTC).toEpochMilli();
        BigInteger insuranceId = new BigInteger("***********");

        //投保成功消息
        String jsonString = "{\n" +
                "    \"tenant\": \"AE\",\n" +
                "    \"institution\": \"GUO_TAI\",\n" +
                "    \"product\": \"AE_FREE_RETURN_LOCAL\",\n" +
                "    \"identifier\": \"8191310050174400\",\n" +
                "    \"insuranceNo\": \"" + insuranceNo + "\",\n" +
                "    \"applicantId\": \"200042360\",\n" +
                "    \"applicantType\": \"ALI\",\n" +
                "    \"policyNo\": \"" + timestamp + "\",\n" +
                "    \"insureAmount\": 0.02,\n" +
                "    \"insureAmountCurrency\": \"USD\",\n" +
                "    \"premium\": 0.02,\n" +
                "    \"premiumCurrency\": \"CNY\",\n" +
                "    \"insureTime\": " + timestamp + ",\n" +
                "    \"effectTime\": " + timestamp + ",\n" +
                "    \"expireTime\": " + futureTimestampMillis + ",\n" +
                "    \"eventType\": \"INSURE_SUCCEED\",\n" +
                "    \"attributes\": {\n" +
                "        \"echo\": \"{\\\"insuranceId\\\":" + insuranceId + "}\"\n" +
                "    }\n" +
                "}";

        JSONObject msgBody = JSON.parseObject(jsonString);
        return msgBody;
    }
}
