package com.aliexpress.databank.constant;

import lombok.Getter;

import java.util.Arrays;

public enum CancelOrderReasonEnum {
    bops_Cancel("bops","bopsCancel", "bops_manual_cancel"),
    buyer_Cancel("买家","buyerCancel", "buyerDonotwantOrder"),
    Logistic_Close_Cancel("物流", "LogisticCloseCancel","logistics_close"),
    mtee_Cancel("cro", "mteeCancel","security_close6"),
    delivery_mtee_Cancel("发货后风控关单", "mteeCancel","security_close4"),
    payment_Risk_Cancel("支付风控", "paymentRiskCancel","security_close"),
    payment_Cancel("支付", "paymentCancel","risk_reject_closed"),
    promotion_Cancel("营销", "promotionCancel","group_failure"),
    seller_Cancel("卖家", "sellerCancel","freightCommitDayNotMatch"),
    timeout_Cancel("发货超时", "timeoutCancel","seller_send_goods_timeout"),
    partial_timeout_Cancel("部分发货超时", "timeoutCancel","partial_shipping_overtime_closed"),
    supply_Chain_Cancel("JIT不可达关单", "supplyChainCancel","warehouse_unable_sendout");

    @Getter
    private String scenario;

    @Getter
    private String cancelEvent;

    @Getter
    private String cancelReason;


    CancelOrderReasonEnum(String scenario, String cancelEvent,String cancelReason) {
        this.scenario = scenario;
        this.cancelEvent = cancelEvent;
        this.cancelReason = cancelReason;
    }

    public static CancelOrderReasonEnum getCancelOrderReasonEnum(String scenario) {
        return Arrays.stream(CancelOrderReasonEnum.values()).filter(it -> it.getScenario().equalsIgnoreCase(scenario)).findFirst().get();
    }

}
