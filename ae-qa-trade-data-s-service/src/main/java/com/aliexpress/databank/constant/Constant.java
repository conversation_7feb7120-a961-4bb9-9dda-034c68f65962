package com.aliexpress.databank.constant;

import com.alibaba.fastjson.JSONObject;
import com.aliexpress.databank.dataobject.OrderScenarioModule;
import com.aliexpress.databank.dataobject.UITask;
import org.apache.commons.compress.utils.Lists;

import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

public class Constant {

    public static final String ORDER_ID = "orderIdStr";
    public static final String SUB_ORDER_ID = "subOrderId";
    public static final String TRADE_ORDER_LINE_ID = "tradeOrderLineId";
    public static final String TIMEOUT_ALARM_NAME = "taskNameStr";
    public static final String REVERSE_NEW_TIMEOUT_ALARM_NAME = "reverseNewTaskNameStr";

    public static final String TIMEOUT_ALARM_ID = "alarmId";
    public static final String VERSION = "version";
    public static final String ISSUE_ID = "issueId";
    public static final String CLAIM_ID = "claimId";
    public static final String CLAIM_REPORT_NO = "claimReportNo";
    public static final String CLAIM_RESULT = "claimResult";
    public static final String APPLY_RESULT = "applyResult";
    public static final String ADD_CART_QUANTITIES = "addCartQuantityList";
    public static final String BUSINESS = "business";
    public static final String CONSIGN_ORDER_NO = "consignorderNo";
    public static final String INDEX = "index";

    public static final String BIZ_PARAMS = "bizParams";

    public static final String BUYER_ID = "buyerId";
    public static final String FULFILLMENT_TYPE = "fulfillmentType";
    public static final String ORDER_TYPE = "orderType";
    public static final String DS = "ds";
    public static final String TRACKING_NUMBER = "trackingNumber";
    public static final String SELLER_ID = "sellerId";

    public static final String ORDER_INFO = "orderInfo";
    public static final String SUB_ORDER_INFO = "subOrderInfo";
    public static final String PRODUCT_INFO = "productInfo";
    public static final String ITEM_IDS = "productidlist";
    public static final String SKU_ID = "skuId";
    public static final String QUANTITY = "quantity";
    public static final String SKU_IDS = "skuList";
    public static final String PRODUCT_IDS = "productIdList";
    public static final String UNIT = "unit";

    // 拼团相关字段
    public static final String  SHARE_GROUP = "shareGroup";
    public static final String SHARE_GROUP_CODE = "shareGroupCode";

    // 拒付featureKey
    public static final String RESOLVE_DUE_TIME = "resolveDueTime";
    public static final String CUSTOMER_MSGS = "customerMsgs";
    public static final String RETURN_SHIPPING_INFOS = "returnShippingInfos";
    public static final String SHIPPING_CARRIER = "shippingCarrier";
    public static final String SHIPPING_NUMBER = "shippingNumber";
    public static final String JUDGEMENT_MSG = "judgementMsg";

    public static final String CURRENCY = "currency";
    public static final String PRICE = "price";
    public static final String SHIP_TO_COUNTRY = "country";

    public static final String SHIPPING_METHOD = "shippingMethod";
    public static final String COUNTRY_CODE = "countryCode";

    public static final String ADDRESS_TYPE = "addressType";
    public static final String CPF_STATUS = "cpfStatus";


    public static final String CANCEL_EVENT = "cancelEvent";
    public static final String REFUND_CHANNEL = "refundChannel";
    public static final String REVERSE_TYPE = "reverseType";
    public static final String ALIPAY_TOKEN = "alipayToken";

    public static final String ISSUE_INFO = "issueInfo";
    public static final String REVERSE_ORDER_LINE_ID = "reverseOrderLineId";

    //miniapp
    public static final String OUTID = "outid";
    public static final String ADDRESS_ID = "addressId";

    //address validate rule
    public static final String REQUIRED = "required";
    public static final String REGEXP = "regexp";
    public static final String LENGTH = "length";

    public static final String REPAID_PERIOD = "repaidPeriod";

    public static final String REPAID_REFUND_COMPLETE = "repaidRefundComplete";
    public static List<String> LOGIN_ORDER_HEADS = Arrays.asList("交易主单", "卖家loginId", "卖家id", "买家loginId", "买家id",
            "订单状态", "逆向单状态", "支付金额", "结算金额", "订单创建时间",
            "订单支付时间", "最新更新时间", "物流状态", "是否冻结");

    public static String EXCHANGE_RATE = "";

    //cod cco审核类型
    public static final String COD_AUDIT_TYPE = "codAuditType";
    public static final String COD_AUDIT_RESULT = "codAuditResult";

    public static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * pay time out toc type
     */
    public static final String PAY_TIMEOUT_TYPE = "trade-pay-time-out";

    /**
     * ship time out toc type
     */
    public static final String SHIP_TIMEOUT_TYPE = "trade-ship-time-out";

    /*** succeed time out toc type*/
    public static final String SUCCEED_TIMEOUT_TYPE = "trade-succeed-time-out";

    /**
     * reminder ship toc type
     **/
    public static final String SHIP_REMINDER_TIMEOUT_TYPE = "trade-ship-remind-time-out";


    public static final String SUCCEED_REMINDER_TIMEOUT_TYPE = "trade-succeed-remind-time-out";


    public static final String PAY_REMINDER_TIMEOUT_TYPE = "trade-pay-remind-time-out";

    public static final String WAIT_SELLER_HANDLE_CANCEL_TIMEOUT = "WAIT_SELLER_HANDLE_CANCEL_TIMEOUT";

    /**
     * alarm name of reverse toc
     */
    public static final String ALARM_NAME_OF_REVERSE_TIMEOUT = "CATHEDRAL_GLOBAL_REVERSE_TIMEOUT";

    public static final String X_DAY_ON_TIME_GUARANTEE_TIMEOUT_TYPE = "ETA_DELAY_COMPENSATE_TIMEOUT";

    public static final String DELIVERY_ON_TIME_GUARANTEE_TIMEOUT_TYPE = "ON_TIME_GUARANTEE_SERVICE_TIMEOUT";

    public static final String SELLER_SUBMIT_EVIDENCE_TIMEOUT_TYPE = "SELLER_SUBMIT_EVIDENCE_TIMEOUT";


    /**
     * reminder seller cancel order toc type
     **/
    public static final String SELLER_CANCEL_ORDER_TIMEOUT_TYPE = "WAIT_SELLER_HANDLE_CANCEL_TIMEOUT";

    public static final String INTENTIONAL_CURRENCY = "intentional_currency";

    public static final String MQ_PRODUCER = "ae-qa-trade-data-s";

    public static final String SATELLITE_MQ_PRODUCER = "ae-qa-trade-data-s-landlord";

    public static final String ORDER_SCENARIO = "scenario";
    public static final String ORDER_LINE_SCENARIO = "orderLineScenario";

    public static final String PARAM_ITEM_ID = "itemId";

    public static final String PARAM_SKU_ID = "skuId";
    public static final String PARAM_SHIP_TO_COUNTRY = "shipToCountry";
    public static final String STAGING = "staging";
    public static final String IS_FREE_GIFT = "isFreeGift";
    public static final String FULFILLMENT_SERVICE = "fulfillmentService";

    public static final String NO_NEED_AUTH = "noNeedAuth";
    public static final String ADMIN_AUTH = "adminAuth";
    public static final String ADMIN = "admin";
    public static final String BUYER_AUTH = "buyerAuth";
    public static final String SELLER_AUTH = "sellerAuth";
    public static final String TEST_USER_FLAG = "AE_TEST_ACCOUNT";

    public static String ADDRESS_DATA = null;
    public static String SUGGEST_ADDRESS = null;
    public static String SUGGEST_ADDRESS_TYPE = "suggestAddressType";

    public static Map<String, List<Long>> ORDER_SCENARIO_ITEMS = new ConcurrentHashMap<>();
    public static Map<String, List<OrderScenarioModule>> ORDER_SCENARIOS = new ConcurrentHashMap<>();
    public static Map<String, List<String>> ORDER_DELIVERY_OPTION = new ConcurrentHashMap<>();
    public static Set<Long> USER_WHITE_LIST = new HashSet<>();
    public static Set<String> DIFF_HSF = new HashSet<>();
    public static boolean IS_TEST_FLAG = true;

    public static final String FREEZE_TYPE = "freezeType";

    public static final String ACTIVITY_ID = "activityId";

    public static final String PAYMENT_TOPIC = "global_payment_event_topic";
    public static final String REVERSE_FULFILLMENT_TOPIC = "GLOBAL-UOP-RETURN-ORDER-STATUS-UPDATE";
    public static final String CB_TAG = "chargeback";
    public static final String REVERSE_FULFILLMENT_TAG = "returnOrderStatusChanged";
    public static final String IS_NEW_CB = "isNewCb";
    public static final String CB_ORDER_ID = "bizOrderId";
    public static final String CB_RAW_CODE = "rawCode";
    public static final String CB_DISPUTE_RAW_CODE = "disputeRawCode";
    public static final String CB_CHARGE_BACK_AMT = "chargebackAmount";
    public static final String CHARGE_BACK_ID = "chargebackId";
    public static final String IS_MERGE = "isMerge";

    public static final String CB_CHARGE_BACK_CNY = "currencyCode";
    public static final String TASK_ID = "taskId";
    public static final String SHIP_TYPE = "shipType";
    public static final String DPATH_ENV = "dpath";

    public static final String PAYMENT_RISK_TAG = "checkout_reverse";

    public static final String ANT_INSURANCE_TOPIC = "tmc_aliexpress_insurance";
    public static final String ANT_INSURANCE_TAG = "aliexpress_insurance_MsgFromAnt";


    public static final String PRE_IS_THE_SAME_2_ONLINE = "isSame";
    public static final String PRE_IP = "preIp";
    public static final String ONLINE_IP = "onlineIp";
    public static final String PRE_HSF = "preHsfRef";
    public static final String PRE_HSF_VERSION = "preHsfVersion";
    public static final String PRE_HSF_METHOD = "preHsfMethod";
    public static final String PRE_REQ_BODY = "preReqBody";
    public static final String PRE_REQ_TYPE = "preReqType";

    public static final String ONLINE_HSF = "onlineHsfRef";
    public static final String ONLINE_HSF_VERSION = "onlineHsfVersion";
    public static final String ONLINE_HSF_METHOD = "onlineHsfMethod";
    public static final String ONLINE_REQ_BODY = "onlineReqBody";
    public static final String ONLINE_REQ_TYPE = "onlineReqType";

    //拒付二期真实消息体
    public static final String CB_MSG_BODY = "{\"bizOrderId\":3027421070072202,\"cbType\":\"CHARGEBACK\",\"checkoutOrderId\":\"12990501103210716320800043002\",\"attributes\":{\"chargebackEntity\":{\"chargebackId\":\"2023112715533027421070072202\",\"chargebackReasonCode\":\"FRAUD\",\"chargebackType\":\"OPEN\",\"chargebackAmount\":{\"zero\":false,\"amount\":0.01,\"centFactor\":100,\"cent\":1,\"currency\":\"USD\",\"currencyCode\":\"USD\"},\"payInstructionNo\":\"12990501203210716238100043002\",\"disputeReasonInfo\":{\"disputeRawCode\":\"PP-30\",\"disputeReasonCode\":\"FRAUD\",\"disputeReasonMsg\":\"Thecustomerdice\",\"disputeSource\":\"paypal\"},\"processName\":\"bpm.CommonChargeBackProcessV1\",\"chargebackTime\":1626431330000,\"chargebackReason\":\"Thecustomerdidnotauthorizepurchaseofthemerchandiseorservice\",\"additionalInfo\":{\"customerMsgs\":[\"defaultmsg\",\"defaultcustomer\",\"https://www.aliexpress.com/p/reverse-pages/detail.html?_forceUseSystemWebView=true&tradeOrderLineId=8188675070022202&reverseOrderLineId=14235106269752202&from=1&_mtopPrev_=use-pre-acshttps://www.aliexpress.com/p/reverse-pages/detail.html?_forceUseSystemWebView=true&tradeOrderLineId=8188675070022202&reverseOrderLineId=14235106269752202&from=1&_mtopPrev_=use-pre-acs\"],\"returnShippingInfos\":[{\"shippingCarrier\":\"DEFAULT\",\"shippingNumber\":\"111\"}]},\"resolveDueTime\":\"2023-11-29T01:11:11-08:00\",\"extraInfo\":{\"REFERENCE_SUB_ORDER_ID_LIST\":[\"8128703832254731\"],\"rawReason\":{\"source\":\"paypal\",\"rawCode\":\"PP-30\"},\"CB_TYPE\":\"CHARGEBACK\"},\"flowType\":\"COMMON_CHARGEBACK_FLOW\"},\"chargebackId\":\"2023112715533027421070072202\",\"isNewCb\":\"true\",\"mergePayedTradeOrderIds\":[1],\"payOptionCode\":\"DIRECTDEBIT_PAYPAL\"},\"buyerId\":**********}";
    public static final String CB_MSG_HAPPEN_BODY = "{ \"bizOrderId\":\"8155987130011090\", \"cbType\":\"CHARGEBACK\", \"checkoutOrderId\":\"12990501103210716320800043002\", \"attributes\":{ \"chargebackEntity\":\"{ \\\"chargebackId\\\":\\\"2022100225013101969101837830\\\", \\\"chargebackReasonCode\\\":\\\"FRAUD\\\", \\\"chargebackType\\\":\\\"OPEN\\\", \\\"chargebackAmount\\\":{ \\\"zero\\\":false, \\\"amount\\\":0.01, \\\"centFactor\\\":100, \\\"cent\\\":1, \\\"currency\\\":\\\"USD\\\", \\\"currencyCode\\\":\\\"USD\\\" }, \\\"payInstructionNo\\\":\\\"12990501203210716238100043002\\\", \\\"disputeReasonInfo\\\":{ \\\"disputeRawCode\\\":\\\"PP-53\\\", \\\"disputeReasonCode\\\":\\\"FRAUD\\\", \\\"disputeReasonMsg\\\":\\\"The customer did not authorize purchase of the merchandise or service\\\", \\\"disputeSource\\\":\\\"paypal\\\" }, \\\"processName\\\":\\\"bpm.CommonChargeBackProcessV1\\\", \\\"chargebackTime\\\":1626431330000, \\\"chargebackReason\\\":\\\"The customer did not authorize purchase of the merchandise or service\\\", \\\"resolveDueTime\\\": \\\"2022-07-06T01:43:48-07:00\\\", \\\"additionalInfo\\\": { \\\"customerMsgs\\\": [ \\\"aaaaaa\\\", \\\"bbbbbbb\\\", \\\"ccccccccc\\\" ], \\\"returnShippingInfos\\\": [ { \\\"shippingCarrier\\\": \\\"FEX\\\", \\\"shippingNumber\\\": \\\"79695132\\\" } ] }, \\\"extraInfo\\\":{ \\\"REFERENCE_SUB_ORDER_ID_LIST\\\":[ \\\"8128703832254731\\\" ], \\\"rawReason\\\":{ \\\"source\\\":\\\"paypal\\\", \\\"rawCode\\\":\\\"PP-53\\\" }, \\\"CB_TYPE\\\":\\\"CHARGEBACK\\\" }, \\\"flowType\\\":\\\"COMMON_CHARGEBACK_FLOW\\\" }\", \"chargebackId\":\"2021071633013101729618403028\", \"isNewCb\":\"true\", \"mergePayedTradeOrderIds\":\"[\\\"387426801153002\\\"]\", \"payOptionCode\":\"DIRECTDEBIT_PAYPAL\" }, \"buyerId\":1861541090 }";
    public static final String CB_MSG_RETRY_BODY = "{ \"bizOrderId\":\"3018084586218898\", \"cbType\":\"CHARGEBACK_RETRY\", \"checkoutSource\":\"global.trade\", \"checkoutOrderId\":\"20990501103220913049483048898\", \"attributes\":{ \"chargebackEntity\":\"{ \\\"resolveDueTime\\\": \\\"2022-07-06T01:43:48-07:00\\\", \\\"additionalInfo\\\": { \\\"customerMsgs\\\": [ \\\"aaaaaa\\\", \\\"bbbbbbb\\\", \\\"ccccccccc\\\" ], \\\"returnShippingInfos\\\": [ { \\\"shippingCarrier\\\": \\\"FEX\\\", \\\"shippingNumber\\\": \\\"79695132\\\" } ] }, \\\"payInstructionNo\\\":\\\"20990501203220913968995798898\\\", \\\"chargebackId\\\":\\\"2022100325013101345701842528\\\", \\\"processName\\\":\\\"bpm.CommonChargeBackProcessV1\\\", \\\"chargebackType\\\":\\\"RETRY_OPEN\\\", \\\"extraInfo\\\":\\\"{\\\\\\\"notifyId\\\\\\\":\\\\\\\"202210030491539865651250IjJZJSLE\\\\\\\"}\\\", \\\"flowType\\\":\\\"COMMON_CHARGEBACK_FLOW\\\" }\", \"chargebackId\":\"2022100325013101345701842528\", \"isNewCb\":\"true\", \"mergePayedTradeOrderIds\":\"[\\\"3018084586218898\\\"]\", \"payPlanStatus\":\"PAID\", \"payOptionCode\":\"DIRECTDEBIT_PAYPAL\" }, \"buyerId\":128908898 }";
    public static final String CB_MSG_APPEAL_BODY = "{ \"bizOrderId\":\"3018084586218898\", \"cbType\":\"CHARGEBACK_APPEAL\", \"checkoutSource\":\"global.trade\", \"checkoutOrderId\":\"20990501103220913049483048898\", \"attributes\":{ \"chargebackEntity\":\"{ \\\"resolveDueTime\\\": \\\"2022-07-06T01:43:48-07:00\\\", \\\"additionalInfo\\\": { \\\"customerMsgs\\\": [ \\\"aaaaaa\\\", \\\"bbbbbbb\\\", \\\"ccccccccc\\\" ], \\\"returnShippingInfos\\\": [ { \\\"shippingCarrier\\\": \\\"FEX\\\", \\\"shippingNumber\\\": \\\"79695132\\\" } ] }, \\\"payInstructionNo\\\":\\\"20990501203220913968995798898\\\", \\\"chargebackId\\\":\\\"2022100325013101345701842528\\\", \\\"processName\\\":\\\"bpm.CommonChargeBackProcessV1\\\", \\\"chargebackType\\\":\\\"RETRY_OPEN\\\", \\\"extraInfo\\\":\\\"{\\\\\\\"notifyId\\\\\\\":\\\\\\\"202210030491539865651250IjJZJSLE\\\\\\\"}\\\", \\\"flowType\\\":\\\"COMMON_CHARGEBACK_FLOW\\\" }\", \"chargebackId\":\"2022100325013101345701842528\", \"isNewCb\":\"true\", \"mergePayedTradeOrderIds\":\"[\\\"3018084586218898\\\"]\", \"payPlanStatus\":\"PAID\", \"payOptionCode\":\"DIRECTDEBIT_PAYPAL\" }, \"buyerId\":128908898 }";
    public static final String CB_MSG_RESULT_BODY = "{ \"bizOrderId\":\"3018056795905395\", \"cbType\":\"CHARGEBACK_JUDGE\", \"checkoutSource\":\"global.trade\", \"checkoutOrderId\":\"20990501103220825781643205395\", \"attributes\":{ \"chargebackEntity\":{ \"additionalInfo\":{ \"customerMsgs\":[ \"aaaaaa\", \"bbbbbbb\" ], \"returnShippingInfos\":[ { \"shippingCarrier\":\"FEX\", \"shippingNumber\":\"79695132\" } ], \"judgementMsg\":\"INELIGIBLE_SELLER_PRORTECTION_POLICY\" }, \"payInstructionNo\":\"20990501203220825782344675395\", \"chargebackId\":\"2022101325013101810401877720\", \"chargebackReasonCode\":\"MERCHANDISE\", \"disputeReasonInfo\":{ \"disputeReasonCode\":\"MERCHANDISE\", \"disputeReasonMsg\":\"The customer did not receive the merchandise or service\", \"disputeSource\":\"paypal\" }, \"processName\":\"bpm.CommonChargeBackProcessV1\", \"chargebackTime\":1667216181000, \"chargebackReason\":\"The customer did not receive the merchandise or service\", \"chargebackType\":\"RESOLVED\", \"chargebackAmount\":{ \"zero\":true, \"amount\":0, \"centFactor\":100, \"cent\":0, \"currency\":\"USD\", \"currencyCode\":\"USD\" }, \"flowType\":\"COMMON_CHARGEBACK_FLOW\" }, \"chargebackId\":\"2022101325013101810401877720\", \"isNewCb\":\"true\", \"mergePayedTradeOrderIds\":\"[\\\"3018056795905395\\\"]\", \"payPlanStatus\":\"PAID\", \"payOptionCode\":\"DIRECTDEBIT_PAYPAL\" }, \"buyerId\":********* }";

    // 履约单创建消息
    public static final String FULFILLMENT_MSG_MAIL_BODY = "{ \"dateTimeHappened\":1668159962000, \"fulfillmentOrderId\":\"FO2580410626214791\", \"tradeOrderId\":\"8157920950151090\", \"reverseTradeOrderId\":\"6263274901141090\", \"buyerId\":\"1861541090\", \"sellerId\":\"*********\", \"siteId\":\"GLOBAL\", \"srcOrderStatus\":\"2010\", \"srcOrderBizStatus\":\"2010100\", \"targetOrderStatus\":\"2015\", \"extendMap\":{ \"carriedCompanyName\":\"SPAIN RETURN SERVICE\", \"carriedWaybillUrl\":\"http://fpx-bss-oss-eu.oss-eu-central-1.aliyuncs.com/channel-service-25703a1c-e45f-41c6-8008-ffaf123a544a.pdf\", \"trackingNumber\":\"PQ56GB0762593130128806S\" }, \"fulfillmentOrderItemStatusUpdatedList\":[ { \"fulfillmentOrderItemId\":\"FO2580410778401015\", \"tradeOrderItemId\":\"8157920950171090\", \"reverseTradeOrderItemId\":\"6263274901241090\", \"srcStatus\":\"2010\", \"targetStatus\":\"2015\" } ] }";
    public static final String FULFILLMENT_MSG_ASCAN_BODY = "{\"buyerId\":\"1861541090\",\"dateTimeHappened\":1668595849000,\"extendMap\":{},\"fulfillmentOrderId\":\"FO3298511556128004\",\"fulfillmentOrderItemStatusUpdatedList\":[{\"fulfillmentOrderItemId\":\"FO3298511994460007\",\"reverseTradeOrderItemId\":\"6263939900841090\",\"srcStatus\":\"2015\",\"targetStatus\":\"2045\",\"tradeOrderItemId\":\"8157832087961090\"}],\"fulfillmentOrderLineIdList\":[\"FO3298511994460007\"],\"fulfillmentOrderLineIdMap\":{\"FO3298511994460007\":{\"$ref\":\"$.fulfillmentOrderItemStatusUpdatedList[0]\"}},\"reverseTradeOrderId\":\"6263939900741090\",\"sellerId\":\"*********\",\"siteId\":\"GLOBAL\",\"srcOrderBizStatus\":\"2015100\",\"srcOrderStatus\":\"2015\",\"targetOrderStatus\":\"2045\",\"tradeOrderId\":\"8157832087951090\"}";
    public static final String FULFILLMENT_MSG_DSCAN_BODY = "{\"buyerId\":\"1861541090\",\"dateTimeHappened\":1668595908875,\"extendMap\":{},\"fulfillmentOrderId\":\"FO3298511556128004\",\"fulfillmentOrderItemStatusUpdatedList\":[{\"fulfillmentOrderItemId\":\"FO3298511994460007\",\"reverseTradeOrderItemId\":\"6263939900841090\",\"srcStatus\":\"2045\",\"targetStatus\":\"2050\",\"tradeOrderItemId\":\"8157832087961090\"}],\"fulfillmentOrderLineIdList\":[\"FO3298511994460007\"],\"fulfillmentOrderLineIdMap\":{\"FO3298511994460007\":{\"$ref\":\"$.fulfillmentOrderItemStatusUpdatedList[0]\"}},\"reverseTradeOrderId\":\"6263939900741090\",\"sellerId\":\"*********\",\"siteId\":\"GLOBAL\",\"srcOrderBizStatus\":\"2045100\",\"srcOrderStatus\":\"2045\",\"targetOrderStatus\":\"2050\",\"tradeOrderId\":\"8157832087951090\"}";
    public static final String FULFILLMENT_MSG_QC_BODY = "{\"buyerId\":\"1861541090\",\"dateTimeHappened\":1668596586360,\"extendMap\":{\"qcResultStatus\":\"1\"},\"fulfillmentOrderId\":\"FO3298511556128004\",\"fulfillmentOrderItemStatusUpdatedList\":[{\"fulfillmentOrderItemId\":\"FO3298511994460007\",\"reverseTradeOrderItemId\":\"6263939900841090\",\"srcStatus\":\"2050\",\"targetStatus\":\"2070\",\"tradeOrderItemId\":\"8157832087961090\"}],\"fulfillmentOrderLineIdList\":[\"FO3298511994460007\"],\"fulfillmentOrderLineIdMap\":{\"FO3298511994460007\":{\"$ref\":\"$.fulfillmentOrderItemStatusUpdatedList[0]\"}},\"reverseTradeOrderId\":\"6263939900741090\",\"sellerId\":\"*********\",\"siteId\":\"GLOBAL\",\"srcOrderBizStatus\":\"2050100\",\"srcOrderStatus\":\"2050\",\"targetOrderStatus\":\"2070\",\"tradeOrderId\":\"8157832087951090\"}";

    public static Boolean isNeedMock = true;
    public static final String TRACE_ID = "traceId";
    public static final String SIMULATOR_KEY = "simulatorKey";

    public static final String LOGIN_ID = "loginId";


    public static final String IS_RECEIVED = "isReceived";
    public static final String RETURN_TYPE = "return";
    public static final String FEATURE = "feature";
    public static final String RETURN_OR_REFUND = "returnOrRefund";
    public static final String RETURN_AMT = "returnAmt";
    public static final String RETURN_REASON = "returnReason";
    public static final String RETURN_ADDRESS = "returnAddress";
    public static final String SOLUTION_OWNER = "solutionOwner";

    public static final String LOGISTIC_NUM = "logisticNum";
    public static final String LOGISTIC_COMPANY = "logisticCompany";
    public static final String LOGISTIC_CODE = "logisticCode";

    public static final String PROMISE_TEMPLATE_ID = "promiseTemplateId";

    public static final String FREE_RETURN_COUNTRY = "RU,US,ES,FR,BR,NL,IL,PL,UK,KR,DE,IT,CL,CA,UA,JP,AU,MX,BE,CZ,SA,CH,PT,AE";

    public static final String HAVANA_ID = "havanaId";

    public static final String IS_OVERSIZE = "isOverSize";

    public static final String ORDER_PRODUCT_ID = "itemId";
    public static final String ORDER_LINES = "orderLines";
    public static final String SOLUTION_CODE = "solutionCode";
    public static final String MERGE_PAYED_TRADE_ORDER_IDS = "mergePayedTradeOrderIds";
    public static final String ONLINE_SHIP_PARAM = "{    \"bizSource\": \"ALIEXPRESS\",    \"orderParam\": {        \"tradeOrderParam\": {            \"tradeOrderId\": \"1000193050030182\"        },        \"feature\": {            \"tradeOrderFrom\": \"ESCROW\",            \"entryCode\": \"AE_SINGLE\",            \"class\": \"java.util.HashMap\"        },        \"receiverParam\": {            \"mobilePhone\": \"18200000000\",            \"name\": \"kr name\",            \"telephone\": \"18200000000\",            \"addressParam\": {                \"zipCode\": \"01001\",                \"province\": \"Maryland\",                \"city\": \"Aber prov grd\",                \"street\": \"test address\",                \"turnType\": \"UP\",                \"detailAddress\": \"test address2025001\",                \"userSpecified\": false            }        },        \"sellerInfoParam\": {            \"globalUserId\": \"*********\"        },        \"logisticPackageParams\": [            {                \"itemParams\": [                                    ],                \"categoryFeature\": [                                    ]            }        ],        \"returnerParam\": {            \"useUserSpecifiedAddress\": false,            \"mobilePhone\": \"1800000000\",            \"name\": \"cainiao test\",            \"telephone\": \"315040\",            \"addressParam\": {                \"zipCode\": \"315040\",                \"province\": \"zhe jiang\",                \"city\": \"ning bo shi\",                \"countryCode\": \"CN\",                \"street\": \"dong sheng jie dao\",                \"district\": \"jiang dong qu\",                \"turnType\": \"UP\",                \"detailAddress\": \"测试test address\",                \"divisionId\": 330204,                \"userSpecified\": false            }        },        \"senderParam\": {            \"useUserSpecifiedAddress\": false,            \"mobilePhone\": \"18200000000\",            \"name\": \"cainiao test\",            \"telephone\": \"18200000000\",            \"addressParam\": {                \"zipCode\": \"315040\",                \"province\": \"zhe jiang\",                \"city\": \"ning bo shi\",                \"countryCode\": \"CN\",                \"street\": \"dong sheng jie dao\",                \"district\": \"jiang dong qu\",                \"turnType\": \"UP\",                \"detailAddress\": \"test address\",                \"divisionId\": 330204,                \"userSpecified\": false            }        },        \"solutionParam\": {            \"serviceParams\": [                {                    \"code\": \"DOOR_PICKUP\",                    \"useRecommendedPickupWarehouse\": true,                    \"class\": \"com.cainiao.lsp.client.request.DoorPickupServiceParam\",                    \"pickupUserInfoParam\": {                        \"mobilePhone\": \"18200000000\",                        \"name\": \"测试\",                        \"telephone\": \"18200000000\",                        \"addressParam\": {                            \"zipCode\": \"315040\",                            \"province\": \"浙江省\",                            \"city\": \"宁波市\",                            \"countryCode\": \"CN\",                            \"street\": \"东胜街道\",                            \"district\": \"江东区\",                            \"turnType\": \"UP\",                            \"detailAddress\": \"测试地址\",                            \"divisionId\": \"330204\",                            \"userSpecified\": false                        }                    },                    \"warehouseCode\": \"Tran_Store_1731793\"                }            ],            \"solutionCode\": \"CAINIAO_STANDARD\"        }    },    \"class\": \"com.cainiao.lsp.order.request.TakingOrderRequest\"}\n";
    public static final String ONLINE_SHIP_ITEM_PARAM = "{                        \"englishName\": \"Skirts\",                        \"itemId\": \"1005002259775696\",                        \"itemFeatures\": [                                                    ],                        \"hscode\": \"\",                        \"quantity\": 2,                        \"totalPrice\": {                            \"amount\": 0.2,                            \"amountString\": \"0.20\",                            \"cent\": 20,                            \"currency\": {                                \"currencyCode\": \"USD\",                                \"class\": \"java.util.Currency\"                            },                            \"positive\": true                        },                        \"chineseName\": \"半身裙\",                        \"weight\": {                            \"value\": 200                        }                    }\n";

    public static final String MAIL_NUM_PARAM = "{\t\"buyerId\": \"1859103223\",\t\"dateTimeHappened\": 1631867637756,\t\"extendMap\": {\t\t\"carriedCompanyName\": \"POLAND_DHL_PARCEL_RETURN_SERVICE\",\t\t\"carriedWaybillUrl\": \"http://fpx-bss-oss-eu.oss-eu-central-1.aliyuncs.com/fpx-print-label-5970e380-7fba-43d6-ad65-4aded6689a07.pdf\",\t\t\"trackingNumber\": \"MN1631867637637\"\t},\t\"fulfillmentOrderId\": \"FO2551110003335014\",\t\"fulfillmentOrderItemStatusUpdatedList\": [{\t\t\"fulfillmentOrderItemId\": \"FO2551110003913017\",\t\t\"reverseTradeOrderItemId\": \"2302218800503223\",\t\t\"srcStatus\": \"2010\",\t\t\"targetStatus\": \"2015\",\t\t\"tradeOrderItemId\": \"1000199700893223\"\t}],\t\"fulfillmentOrderLineIdList\": [\"FO2551110003913017\"],\t\"fulfillmentOrderLineIdMap\": {\t\t\"FO2551110003913017\": {\t\t\t\"$ref\": \"$.fulfillmentOrderItemStatusUpdatedList[0]\"\t\t}\t},\t\"reverseTradeOrderId\": \"2302218800403223\",\t\"sellerId\": \"*********\",\t\"siteId\": \"GLOBAL\",\t\"srcOrderBizStatus\": \"2010100\",\t\"srcOrderStatus\": \"2010\",\t\"targetOrderStatus\": \"2015\",\t\"tradeOrderId\": \"1000199700883223\"}\n";
    public static final String A_SCAN_PARAM = "{\t\"buyerId\": \"1859103223\",\t\"dateTimeHappened\": 1631868009425,\t\"extendMap\": {},\t\"fulfillmentOrderId\": \"FO2551110003335014\",\t\"fulfillmentOrderItemStatusUpdatedList\": [{\t\t\"fulfillmentOrderItemId\": \"FO2551110003913017\",\t\t\"reverseTradeOrderItemId\": \"2302218800503223\",\t\t\"srcStatus\": \"2015\",\t\t\"targetStatus\": \"2045\",\t\t\"tradeOrderItemId\": \"1000199700893223\"\t}],\t\"fulfillmentOrderLineIdList\": [\"FO2551110003512032\"],\t\"fulfillmentOrderLineIdMap\": {\t\t\"FO2551110003913017\": {\t\t\t\"$ref\": \"$.fulfillmentOrderItemStatusUpdatedList[0]\"\t\t}\t},\t\"reverseTradeOrderId\": \"2300032322903223\",\t\"sellerId\": \"*********\",\t\"siteId\": \"GLOBAL\",\t\"srcOrderBizStatus\": \"2015100\",\t\"srcOrderStatus\": \"2015\",\t\"targetOrderStatus\": \"2045\",\t\"tradeOrderId\": \"1000199700883223\"}\n";
    public static final String D_SCAN_PARAM = "{\t\"buyerId\": \"1859103223\",\t\"dateTimeHappened\": 1631801339000,\t\"extendMap\": {},\t\"fulfillmentOrderId\": \"FO2551110003664031\",\t\"fulfillmentOrderItemStatusUpdatedList\": [{\t\t\"fulfillmentOrderItemId\": \"FO2551110003512032\",\t\t\"reverseTradeOrderItemId\": \"2300032323003223\",\t\t\"srcStatus\": \"2045\",\t\t\"targetStatus\": \"2050\",\t\t\"tradeOrderItemId\": \"1005003302283993\"\t}],\t\"fulfillmentOrderLineIdList\": [\"FO2551110003512032\"],\t\"fulfillmentOrderLineIdMap\": {\t\t\"FO2551110003512032\": {\t\t\t\"$ref\": \"$.fulfillmentOrderItemStatusUpdatedList[0]\"\t\t}\t},\t\"reverseTradeOrderId\": \"2300032322903223\",\t\"sellerId\": \"*********\",\t\"siteId\": \"GLOBAL\",\t\"srcOrderBizStatus\": \"2045100\",\t\"srcOrderStatus\": \"2045\",\t\"targetOrderStatus\": \"2050\",\t\"tradeOrderId\": \"1000198670213223\"}\n";
    public static final String DELIVERY_OPTION = "deliveryOption";

    public static final String CJJR_TOPIC = "FIN_INSURANCE-AE_FREE_RETURN";
    public static final String CLAIM_APPLY_ACCEPT_TAG = "CLAIM_REPORT_ACCEPTED";
    public static final String CLAIM_APPLY_REJECT_TAG = "CLAIM_REPORT_REJECTED";
    public static final String LOGISTICS_RETURN_TAG = "LOGISTICS_ADDED";
    public static final String CLAIM_APPLY_ACCEPT_MSG_BODY = "{\"claimReportNo\":\"01960100011160221082400000078001\",\"echo\":\"100000900183223\",\"eventTime\":1632987749000,\"eventType\": \"CLAIM_REPORT_ACCEPTED\",\"expressInfo\": {\"expressUrl\":\"http://shipment.anhelogistics.com/static/smt_file/CORREOS_PQ5J0A0711517800128947B.pdf\",\"logisticsCompanyCode\": \"SPAIN POST\",\"waybillNo\": \"PQ5J0A0711517800128947B\"},\"identifier\": \"100001067823223_17_5\",\"productCode\": \"AE_FREE_RETURN\",\"successTime\": 1632987749000,\"warehouseInfo\": {\"address\":\"Calle San Lorenzo 5. Fuenlabrada. \",\"city\":\"Madrid\",\"contactName\":\"ES-RTN-AE\",\"country\":\"ES\",\"province\":\"Madrid\",\"telephone\":\"*********\",\"warehouseCode\":\"CKESMD0001\",\"zipcode\":\"28947\"}}";
    public static final String CLAIM_APPLY_REJECT_MSG_BODY = "{\"claimReportNo\":\"01960100011040021110400000503296\",\"echo\":\"400462907016794\",\"eventTime\":1636019523881,\"eventType\":\"CLAIM_REPORT_REJECTED\",\"identifier\":\"8139637368286794_17_1\",\"productCode\":\"AE_FREE_RETURN\",\"rejectCode\":\"拒绝报案\",\"rejectTime\":1636019523881}";
    public static final String CLAIM_APPLY_ONLY_REFUND = "{\"attrs\" : {\"onlyRefund\" : true}, \"claimReportNo\" : \"01960100011160222030300001157003\",\"echo\" : \"200000160023223\", \"eventTime\" : 1646135906000, \"eventType\" : \"CLAIM_REPORT_ACCEPTED\", \"identifier\" :\"8147035580443223_17_2\", \"productCode\" : \"AE_FREE_RETURN\", \"successTime\" : 1646135906000}";
    public static final String CLAIM_APPLY_RESULT_TAG = "CLAIM_REPORT_INVESTIGATED";
    public static final String CLAIM_RESULT_ACCEPT_MSG_BODY = "{\"claimReportNo\": \"01960100011160221102900000471907\",\"echo\": \"100001942860181\",\"eventTime\": 1635921911852,\"eventType\": \"CLAIM_REPORT_INVESTIGATED\",\"goodsAttachments\": [\"http://cosfilesouth-1252602947.cn-north.myqcloud.com/01125/01125-ac9673c53fa34763893a6c0fde4ae4a9.jpg\", \"http://cosfilesouth-1252602947.cn-north.myqcloud.com/01125/01125-5f6373fd69504f8cbaf77e293b58f2cb.jpg\"],\"itemDepreciation\": 289,\"needClaimAmount\": 0.02,\"productCode\": \"AE_FREE_RETURN\",\"result\": \"PASS\",\"surveyConclusion\": \"ACCEPTED\",\"surveyDescription\": \"货物相符\"}";
    public static final String CLAIM_RESULT_REJECT_MSG_BODY = "{\"claimReportNo\":\"01960100011108121102600000369361\",\"echo\":\"500163207506297\",\"eventTime\":1635931693337,\"eventType\":\"CLAIM_REPORT_INVESTIGATED\",\"goodsAttachments\":[\"http://cosfilesouth-1252602947.cn-north.myqcloud.com/01125/01125-fc636db119a54089baebaa967d042c9d.jpg\",\"http://cosfilesouth-1252602947.cn-north.myqcloud.com/01125/01125-b2a73680bf1141e9a791fbe84a3dfe15.jpg\",\"http://cosfilesouth-1252602947.cn-north.myqcloud.com/01125/01125-1839f7dda4fa4d98a225e71dfedc870a.jpg\"],\"itemDepreciation\":861,\"needClaimAmount\":0.01,\"productCode\":\"AE_FREE_RETURN\",\"result\":\"REJECT\",\"surveyConclusion\":\"REJECTED_MISSING_QUANTITY\",\"surveyDescription\":\"数量缺失\"}";
    public static final String LOGISTICS_RETURN_MSG_BODY = "{\"claimReportNo\":\"MOCK_1320010182\",\"currentStatus\":\"SHIPPING\",\"echo\":\"1320010182\",\"institution\":\"CPIC\",\"logisticsTime\":1625068800000,\"product\":\"AE_FREE_RETURN\",\"tenant\":\"AE\",\"wayBillNo\":\"PQ5J0A0711517800128947B\"}";


    public static Map<String, Object> PAYMENT_METHOD_META_DAYA = new ConcurrentHashMap<>();

    public static List<String> PAYMENT_METHOD_CHECK_GOODNAME = Arrays.asList("DIRECTDEBIT_PAYPAL", "WALLET_PAYPAL_CHECKOUT", "WALLET_PAYPAL_PAYLATER");

    public static List<String> PAYMENT_METHOD_CHECK_GOODURL = Arrays.asList("DIRECTDEBIT_PAYPAL", "WALLET_PAYPAL_CHECKOUT", "WALLET_PAYPAL_PAYLATER");

    public static List<String> PAYMENT_METHOD_SPLIT_ORDER = Arrays.asList("WALLET_PAYPAL", "" +
            "CREDITPAY_KLARNA_SE", "DIRECTDEBIT_PAYPAL", "WALLET_PAYPAL_CHECKOUT", "WALLET_PAYPAL_PAYLATER");

    public static Integer MAX_TIMES = 10;

    public static List<String> UserTagList = Arrays.asList("AE_TEST_ACCOUNT");

    public static final String WORK_ORDER_ID = "wordOrderId";

    public static final String DIAMOND_GROUP_ID = "ae-qa-databank";

    public static final String JUDGE_RESULT = "judgeResult";

    public static List<UITask> IosTaskConfig = new ArrayList<>();

    public static List<UITask> AndroidTaskConfig = new ArrayList<>();

    public static String iosPackageUrl;

    public static String androidPackageUrl;
    public static final String PRE_ENV = "PRE_ENV";

    public static final String ONLINE_ENV = "ONLINE_ENV";

    public static final String EVN_TYPE = "envType";

    public static final String THUB_PROCESS_CODE = "processCode";

    public static final String THUB_PARAM = "thubParam";

    public static final String THUB_ROW_KEY = "rowKey";

    public static JSONObject THUB_INFO = new JSONObject();

    public static JSONObject AK_INFO = new JSONObject();

    public static JSONObject REVERSE_DIAMOND = new JSONObject();


    public static String REFUND_PROCESS = "REFUND_PROCESS";

    public static String REFUND_PROCESS_EXCHANGECODE = "ipayagh.gn.notification.refund.process";

    public static String PAYMENT_RESULT = "PAYMENT_RESULT";

    public static String PARAMS = "params";

    public static String TARGET_IP = "targetIp";

    // 取消订单子单列表
    public static String NEED_CANCEL_ORDER_LINE_DATA = "subOrderIds";

    public static String PROMISE_ID = "promiseId";
    public static String ACTION_ID = "actionId";
    public static String RULE_DETAIL = "ruleDetail";

    public static List<String> COUNTRY_CODES = Lists.newArrayList();

    public static List<String> INTENTION_CURRENCY = Lists.newArrayList();

    public static final String DYNAMIC_PRICE = "dynamicPrice";

    public static final String BREACH_SCENARIO = "breachScenario";

    public static final String SHIP_METHOD = "shipMethod";

    public static final String CO_STATUS = "coStatus";

    public static final String TAGS = "tags";

    public static final String EXPENSIVE_COUPONS_TOPIC = "AE-SERVICE-PLATFORM-TOPIC";
    public static final String EXPENSIVE_COUPONS_TAG = "FinalJudge";

    public static final String BREACH_FTP_JIT_TIMEOUT_TOPIC = "JIT_PURCHASE_PUNISH_TOPIC";
    public static final String BREACH_STP_JIT_TIMEOUT_TOPIC = "SEMI_CHOICE_JIT_PURCHASE_PUNISH_TOPIC";
    public static final String JITOrder_Type = "jitOrderType";


    public static final String BREACH_WAREHOUSE_TOPIC = "AE-SERVICE-PLATFORM-DATA-SYNC-TOPIC";
    public static final String BREACH_WAREHOUSE_TAG = "REPLENISHMENT_ORDER";
    public static final String BREACH_WAREHOUSE_TIMEOUT_WITHOUT_CO_MSG_BODY = "{\"acceptTime\":1690122037000,\"sellerId\":2671514005,\"quotationCurrency\":\"CNY\",\"consignOrderList\":[{\"sc_item_id\":712408549244,\"sku_id\":12000033125076189,\t\"sku_attr\":\"Color:Yellow\",\"sku_image\":\"S6e552b9d9f3342068bb9bb193d183890k.jpg\",\"subject\":\"MiniBuildingBlocksBouquet3DModelToysDIYBricksPlantPottedFlowerAssemblyToysforGirlsKidsGiftsHomeDecorationMoc\"}],\"inboundOrderId\":\"PON230614417994202\",\"itemQuantity\":91,\"punishType\":\"1\",\"replenishmentOrderId\":\"BH23071700540128477511395\",\"scItemId\":695541438464,\"status\":4020}";
    public static final String BREACH_WAREHOUSE_TIMEOUT_CO_INVALID_MSG_BODY = "{\"acceptTime\":1690122037000,\"sellerId\":2671514005,\"quotationCurrency\":\"USD\",\"consignOrderList\":[{\"actualPickupTime\":0,\"actual_pickup_time\":\"\",\"ae_shipping_mode\":-1,\"co_status\":-99,\"consign_order_no\":\"IOCN230731143334407398318\",\"inboundArriveTime\":0,\"inbound_arrive_time\":\"\",\"item_id\":1005005069039131,\"quantity\":125,\"sc_item_id\":695541438464,\"sku_attr\":\"Color:Blue\",\"sku_id\":12000031515619239,\"sku_image\":\"S9dc84d3ec582415c857a021281d7ee10N.jpg\",\"subject\":\"UniversalCarPolishPad3/4inchForM10/M14SoftWoolMachineWaxingPolisherCarBodyPolishingDiscsCleaningAccessories\",\"up_shelf_defective_qty\":0,\"up_shelf_normal_qty\":0}],\"inboundOrderId\":\"PON230614417994202\",\"itemQuantity\":125,\"punishType\":\"1\",\"replenishmentOrderId\":\"BH230717005401284775112219\",\"scItemId\":695541438464,\"status\":4020}";
    public static final String BREACH_WAREHOUSE_TIMEOUT_CO_NOT_ENOUGH_MSG_BODY = "{\"acceptTime\":1690122037000,\"sellerId\":2671514005,\"quotationCurrency\":\"USD\",\"consignOrderList\":[{\"actualPickupTime\":0,\"actual_pickup_time\":\"\",\"ae_shipping_mode\":1,\"co_status\":20,\"consign_order_no\":\"IOCN230731143334407398318\",\"inboundArriveTime\":0,\"inbound_arrive_time\":\"\",\"item_id\":1005005069039131,\"quantity\":25,\"sc_item_id\":695541438464,\"sku_attr\":\"Color:Blue\",\"sku_id\":12000031515619239,\"sku_image\":\"S9dc84d3ec582415c857a021281d7ee10N.jpg\",\"subject\":\"UniversalCarPolishPad3/4inchForM10/M14SoftWoolMachineWaxingPolisherCarBodyPolishingDiscsCleaningAccessories\",\"up_shelf_defective_qty\":0,\"up_shelf_normal_qty\":0}],\"inboundOrderId\":\"PON230614417994202\",\"itemQuantity\":125,\"punishType\":\"1\",\"replenishmentOrderId\":\"BH2307170054012847751124\",\"scItemId\":695541438464,\"status\":4020}";
    public static final String BREACH_WAREHOUSE_LESS_QUANTITY_MSG_BODY = "{\"acceptTime\":1681973536000,\"consignOrderList\":[{\"actualPickupTime\":0,\"actual_pickup_time\":\"\",\"ae_shipping_mode\":-1,\"co_status\":30,\"consign_order_no\":\"IOCN230420150051383368778\",\"inboundArriveTime\":1682048251000,\"inbound_arrive_time\":\"2023-04-21 11:37:31\",\"item_id\":1005005448013490,\"quantity\":10,\"sc_item_id\":712408549244,\"seller_admin_id\":\"cn1073929856nbyae\",\"seller_admin_seq\":\"2671514005\",\"sku_attr\":\"Color:Yellow\",\"sku_id\":12000033125076189,\"sku_image\":\"S6e552b9d9f3342068bb9bb193d183890k.jpg\",\"subject\":\"MiniBuildingBlocksBouquet3DModelToysDIYBricksPlantPottedFlowerAssemblyToysforGirlsKidsGiftsHomeDecorationMoc\",\"up_shelf_defective_qty\":0,\"up_shelf_normal_qty\":6}],\"inboundOrderId\":\"PON230420406188830\",\"itemQuantity\":100,\"punishType\":\"2\",\"quotationCurrency\":\"CNY\",\"replenishmentOrderId\":\"BH2304180049991872291127\",\"scItemId\":712408549244,\"sellerId\":2673387856,\"status\":4020}";

    public static final String PO_ORDER = "po";
    public static final String WH_ORDER = "wh";
    public static final String BH_ORDER = "bh";
    public static final String CO_ORDERS = "cos";
    public static final String AE_INBOUND_TOPIC = "ae-inbound";
    public static final String AE_INBOUND_TAG = "ae-jit-order-status-update";
    public static final String AE_INBOUND_MSG = "{\"gmtModified\":1692021095000,\"purchaseOrderNo\":\"PON230814436552011\",\"purchaseRequirementOrderNo\":\"WH3938610013990018\",\"status\":17}";

    public static final String AE_COD_ORDERLIST = "codOrderList";
    public static final String AE_COD_SHIP_type = "codShipType";
//    public static final String ORDER_LIST = "OrderList";

    public static final String AE_COD_SHIPPED_TOPIC = "GLOBAL-UOP-ORDER-STATUS-UPDATE";
    public static final String AE_COD_SHIPPED_TAG = "batchOrderStatusUpdate";
    public static final String AE_COD_SHIPPED_MSG = "{\"batchTradeGroup\":\"8189239410073223\",\"logisticsPartnerOrderNo\":\"LP00653412107281\",\"fulfillmentOrderList\":[{\"dateTimeHappened\":1716972753685,\"fulfillmentOrderId\":\"FO2551512661824002\",\"tradeOrderId\":\"8189239410073223\",\"buyerId\":\"1859103223\",\"sellerId\":\"2671514005\",\"siteId\":\"GLOBAL\",\"srcOrderStatus\":\"510\",\"targetOrderStatus\":\"510\",\"fulfillmentOrderItemStatusUpdatedList\":[{\"fulfillmentOrderItemId\":\"FO2551513544499002\",\"tradeOrderItemId\":\"8189239410083223\",\"sellerId\":\"2671514005\",\"srcStatus\":\"510100\",\"targetStatus\":\"700\",\"outSubBizId\":\"8189239410083223\"}],\"outBizId\":\"Af956766e-5c31-474a-b38a-1844bf9fcba0818856434051900800001\"}]}";

    public static final String AE_COD_DELIVERED_TOPIC = "GLOBAL-UOP-ORDER-STATUS-UPDATE";
    public static final String AE_COD_DELIVERED_TAG = "orderStatusUpdate";
    public static final String AE_COD_DELIVERED_MSG = "{\"dateTimeHappened\":1716433451000,\"fulfillmentOrderId\":\"FO2065512657473005\",\"tradeOrderId\":\"8188351200113697\",\"buyerId\":\"1861703697\",\"sellerId\":\"2671514005\",\"srcOrderStatus\":\"900\",\"targetOrderStatus\":\"900\",\"fulfillmentOrderItemStatusUpdatedList\":[{\"fulfillmentOrderItemId\":\"FO2065513522209005\",\"tradeOrderItemId\":\"8188351200123697\",\"srcStatus\":\"900\",\"targetStatus\":\"900\",\"reason\":\"repush latest status\"}]}";


    public static final String SC_ITEM_ID = "scItemId";
    public static final String SC_ITEM_NAME = "scItemName";
    public static final String LBX_NO = "lbxNo";
    public static final String LATEST_CERTIFICATION_TIME = "latestCertificationTime";
    public static final String LAST_CERTIFICATION_TIME = "lastCertificationTime";
    public static final String LATEST_WEIGHT = "latestWeight";
    public static final String LAST_ROUND_WEIGHT = "lastRoundWeight";
    public static final String ITEM_COUNT = "itemCount";
    public static final String WEIGHT_INTERVAL = "weightInterval";

    public static final String WEIGHT_RECOVER_STANDARD = "weightRecoverStandard";
    public static final String WEIGHT_RECOVER_STANDARD_ENUM = "weightRecoverStandardEnum";

    public static final String OVERLOAD_WEIGHT_RATIO = "overloadWeightRatio";
    public static final String OVERLOAD_WEIGHT = "overloadWeight";


    public static final String BREACH_ID = "breachId";

    public static final String P_P_O_C = "{\"productSupplyCost\":{\"currencyCode\":\"CNY\",\"amount\":25},\"productLogisticsCost\":{\"currencyCode\":\"CNY\",\"amount\":1},\"productOperatingCost\":{\"currencyCode\":\"CNY\",\"amount\":0},\"productMarketingCost\":{\"currencyCode\":\"CNY\",\"amount\":0},\"productMarkUpCost\":{\"currencyCode\":\"CNY\",\"amount\":1},\"productDdpCost\":{\"currencyCode\":\"CNY\",\"amount\":0},\"invitationProcurementPrice\":{\"currencyCode\":\"CNY\",\"amount\":22},\"productCostSnapshotId\":\"123\"}";

    public static final String REVERSE_FO_ID = "reverseFoId";
    public static final String LOGISTICS_SERVICE_PROVIDER = "logisticsServiceProvider";
    public static final String LOGISTICS_SERVICE_NAME = "logisticsServiceName";

    public static final String TAG_REVERSE_FO_ID = "package com.alibaba.reverse.platform.plus.test\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.repository.ReverseOrderRepository\n" +
            "import com.alibaba.reverse.platform.kernel.server.activity.scenario.model.aggregation.GlobalReverseOrderLine\n" +
            "import com.alibaba.reverse.platform.plus.domain.timeout.AeTimeoutCallbackCheckClaimsStatusAndTakeAction\n" +
            "import com.taobao.eagleeye.EagleEye\n" +
            "import org.slf4j.Logger\n" +
            "import org.slf4j.LoggerFactory\n" +
            "import org.springframework.context.ApplicationContext\n" +
            "ApplicationContext ac = context.getContext();\n" +
            "ReverseOrderRepository reverseOrderRepository = ac.getBean(ReverseOrderRepository.class)\n" +
            "Logger logger = LoggerFactory.getLogger(AeTimeoutCallbackCheckClaimsStatusAndTakeAction.class);\n" +
            "List<Long> noSuitAeReverse = new ArrayList<>();\n" +
            "List<Long> finishReverse = new ArrayList<>();\n" +
            "List<Long> reverseOrderLineIds = Arrays.asList(8212951500772884)\n" +
            "reverseOrderLineIds.stream().forEach({ reverseOrderLineId ->\n" +
            "    try{\n" +
            "        GlobalReverseOrderLine globalReverseOrderLine = reverseOrderRepository.getReverseOrderLine(reverseOrderLineId);\n" +
            "        globalReverseOrderLine.getReverseOrderLine().addFeature(\"OuterId\", \"FO0810512173388002\")\n" +
            "        reverseOrderRepository.updateGlobalReverseOrderLine(globalReverseOrderLine)\n" +
            "    }catch(Exception e){\n" +
            "        // 报错\n" +
            "        logger.warn(\"AeTimeoutCallbackCheckClaimsStatusAndTakeAction error! reverseOrderLineId: {}, trace: {}, exception: \", reverseOrderLineId, EagleEye.getTraceId(), e);\n" +
            "        noSuitAeReverse.add(reverseOrderLineId)\n" +
            "        return\n" +
            "    }\n" +
            "})\n" +
            "logger.warn(\"AeTimeoutCallbackCheckClaimsStatusAndTakeAction error! noSuitAeReverse: {}\", noSuitAeReverse);";

    public static final String LOGISTICS_SERVICE_PROVIDER_SCRIPT = "package com.alibaba.reverse.platform.plus.test\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.repository.ReverseOrderRepository\n" +
            "import com.alibaba.reverse.platform.kernel.server.activity.scenario.model.aggregation.GlobalReverseOrderLine\n" +
            "import com.alibaba.reverse.platform.plus.domain.timeout.AeTimeoutCallbackCheckClaimsStatusAndTakeAction\n" +
            "import com.taobao.eagleeye.EagleEye\n" +
            "import org.slf4j.Logger\n" +
            "import org.slf4j.LoggerFactory\n" +
            "import org.springframework.context.ApplicationContext\n" +
            "ApplicationContext ac = context.getContext();\n" +
            "ReverseOrderRepository reverseOrderRepository = ac.getBean(ReverseOrderRepository.class)\n" +
            "Logger logger = LoggerFactory.getLogger(AeTimeoutCallbackCheckClaimsStatusAndTakeAction.class);\n" +
            "List<Long> noSuitAeReverse = new ArrayList<>();\n" +
            "List<Long> finishReverse = new ArrayList<>();\n" +
            "List<Long> reverseOrderLineIds = Arrays.asList(8212951500772884)\n" +
            "reverseOrderLineIds.stream().forEach({ reverseOrderLineId ->\n" +
            "    try{\n" +
            "        GlobalReverseOrderLine globalReverseOrderLine = reverseOrderRepository.getReverseOrderLine(reverseOrderLineId);\n" +
            "        globalReverseOrderLine.getReverseOrderLine().addFeature(\"f_l_p_c\", \"CAINIAO\")\n" +
            "        globalReverseOrderLine.getReverseOrderLine().addFeature(\"f_l_p_t\", \"4PL\")\n" +
            "        reverseOrderRepository.updateGlobalReverseOrderLine(globalReverseOrderLine)\n" +
            "    }catch(Exception e){\n" +
            "        // 报错\n" +
            "        logger.warn(\"AeTimeoutCallbackCheckClaimsStatusAndTakeAction error! reverseOrderLineId: {}, trace: {}, exception: \", reverseOrderLineId, EagleEye.getTraceId(), e);\n" +
            "        noSuitAeReverse.add(reverseOrderLineId)\n" +
            "        return\n" +
            "    }\n" +
            "})\n" +
            "logger.warn(\"AeTimeoutCallbackCheckClaimsStatusAndTakeAction error! noSuitAeReverse: {}\", noSuitAeReverse);";


    public static final String AGREE_REFUND_CHECK_POINT_REQUEST = "{\"extendParam\":{\"extensionDTOMap\":{}},\"routingInfo\":{\"buyerId\":**********},\"invokeInfo\":{\"appName\":\"ae-reverse-ns-s\"},\"operator\":{\"operatorType\":\"CUSTOMER_SERVICE\",\"operatorId\":0},\"reverseOrderLines\":[{\"reverseOrderLineId\":****************}]}";

    public static final String WAIT_DSCAN_RESULT_TIMEOUT = "WAeTimeoutSubmitReturnItemCallbackAIT_DSCAN_RESULT_TIMEOUT";

    public static final String RESPONSIBLE_PARTY = "responsibleParty";

    public static final String PO_STATUS = "poStatus";

    public static final String BREACH_TYPE = "breachType";

    public static final String FAKE_ITEM = "fakeItem";

    public static final String ARBITRATION_TO_SELFDROP = "arbitrationToSelfDrop";
    public static final String FAST_ARBITRATION_RESULT = "fastArbitrationResult";
    public static final String IS_FAST_ARBITRATION = "isFastArbitration";
    public static final String PROCESS_TYPE = "processType";
    public static final String SELLER_WEIGHT = "sellerWeight";

    public static final String DIFF_RATIO = "diffRadio";

    public static final String WEIGHT_DIFF_TYPE = "weightDiffType";

    public static final String LBX = "lbx";

    public static final String LOGISTICS_ONLINE_ORDER = "LOGISTICS_ONLINE_ORDER";

    public static final String TRIGGER_TYPE = "triggerType";

    public static final String CANCEL_QUANTITY = "unreachableQty";

    public static final String UNREACHABLE_FULFILLMENT_MSG = "{\"dateTimeHappened\":1698292761000,\"fulfillmentOrderId\":\"FO0810410940376701\",\"tradeOrderId\":\"3027796752342202\",\"buyerId\":\"**********\",\"sellerId\":\"2671514005\",\"siteId\":\"GLOBAL\",\"srcOrderStatus\":\"700\",\"srcOrderBizStatus\":\"700100\",\"targetOrderStatus\":\"920\",\"extendMap\":{\"eventCode\":\"unReachable\",\"idCode\":\"global-uop-app-ae-choice-warehouse\",\"packageId\":\"*********226081002\",\"payMethod\":\"MIXEDCARD\",\"polarisFlag\":\"1\"},\"fulfillmentOrderItemStatusUpdatedList\":[{\"fulfillmentOrderItemId\":\"FO0810411217529541\",\"tradeOrderItemId\":\"3027796752352202\",\"sellerId\":\"2671514005\",\"srcStatus\":\"700\",\"targetStatus\":\"920\",\"extendMap\":{\"cancelQty\":\"0\",\"deliveryQty\":\"5\",\"itemList\":\"[{\\\"unReachableReason\\\":\\\"出入库失败\\\",\\\"packageId\\\":\\\"*********226081002\\\",\\\"unReachableQty\\\":3,\\\"status\\\":\\\"unReachable\\\"}]\"},\"originalDataMap\":{\"TRACKING_NUMBER\":\"4208500192612927005455000503168318\",\"buyerId\":**********,\"eventCode\":\"unReachable\",\"unReachableReason\":\"出入库失败\"},\"outSubBizId\":\"3027796752352202\"}],\"fulfillmentOrderItemStatusUpdatedIncreaseList\":[{\"fulfillmentOrderItemId\":\"FO0810411217529541\",\"tradeOrderItemId\":\"3027796752352202\",\"sellerId\":\"2671514005\",\"srcStatus\":\"700\",\"targetStatus\":\"920\",\"extendMap\":{\"cancelQty\":\"0\",\"deliveryQty\":\"5\",\"itemList\":\"[{\\\"unReachableReason\\\":\\\"出入库失败\\\",\\\"packageId\\\":\\\"*********226081002\\\",\\\"unReachableQty\\\":3,\\\"status\\\":\\\"unReachable\\\"}]\"},\"originalDataMap\":{\"TRACKING_NUMBER\":\"4208500192612927005455000503168318\",\"buyerId\":**********,\"eventCode\":\"unReachable\",\"unReachableReason\":\"出入库失败\"},\"outSubBizId\":\"3027796752352202\"}],\"outBizId\":\"Addeb9752-5c68-47b0-b7f6-142f22dba1383027796752342202\"}";

    public static final String FULFILLMENT_TOPIC = "GLOBAL-UOP-ORDER-STATUS-UPDATE";

    public static final String FULFILLMENT_TAG = "orderStatusUpdate";

    public static final String FULFILLMENT_UNREACHABLE_TAG = "unreachableByLogistics";

    public static final String PAYMENT_CHANNEL = "paymentChannel";

    public static final String DURATION = "duration";

    public static final DateTimeFormatter FORMATTER_WITH_T = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss");

    public static final String MSG_TOPIC = "topic";

    public static final String MSG_TAG = "tag";

    public static final String MSG_BODY = "body";

    public static final String REFUND_FAIL_TAG = "refund_order_failed";
    public static final String REFUND_PROCESS_TAG = "refundProcess";


    public static final String PACKAGE_ID = "packageId";

    public static final String MOCK_DELIVERY_FAILED_REQ = "{\"logisticsOrderId\":\"LP00629959280956\",\"logisticsUnitId\":\"LP00629959280956\",\"trackingPrimaryCode\":\"AE_RT_EX_PICKUP\",\"trackingSecondCode\":\"AE_RT_EX_PICKUP\",\"operationTime\":1706854414000,\"mailNo\":\"4204978192612903396184000011398447\",\"tags\":\"rPkgReturn;loacl;cb;exception\",\"features\":{\"featureMap\":{\"actionCode\":\"RT_EX_PICKUP\",\"actionDescription\":\"Logistics contract\",\"cpCode\":\"Tran_Store_13423797\",\"currentCountryCode\":\"\",\"currentCountryName\":\"\",\"dstCountryCode\":\"US\",\"dstCountryName\":\"United States\",\"event_source\":\"GSDP_GFC\",\"exceptionCode\":\"\",\"lgOrderCode\":\"LP00629959280956\",\"logistic_system\":\"CAINIAO\",\"opCode\":\"\",\"opRemark\":\"\",\"sellerId\":\"17379911544\",\"sendMsgTime\":\"1706854414000\",\"serviceItemId\":\"5000000011398\",\"solutionCode\":\"CAINIAO_STANDARD\",\"tracking_event_type\":\"STATUS_UPDATE\",\"tradeOrderId\":\"3030251313652202\"}},\"trackingUpdatedLines\":[{\"tradeOrderId\":\"3030251313652202\"}]}";

    public static final String PAYOUT_REASON = "payoutReason";

    public static final String UNIQUE_ID = "uniqueId";

    public static final String LOGISTIC_ONLINE_TIME = "logisticOnlineTime";
    public static final String PAYMENT_AUTH_TIME = "paymentAuthorizedTime";
    public static final String PRODUCT_PUBLISH_TIME = "productPunishTime";
    public static final String FULFILLMENT_CREATE_TIME = "fulfillmentCreateTime";
    public static final String CAINIAO_LOGISTIC = "caiNiaoLogistic";


    public static final String TIMESTAMP = "timestamp";

    public static final String TYPE = "type";

    public static final String USER_RATE = "userRate";

    public static final String SYSTEM_RATE = "systemRate";

    public static final String SERVICE_PLATFORM_UNFINISHED_CONTRACT_TOPIC = "SERVICE_PLATFORM_UNFINISHED_CONTRACT_TOPIC";

    public static final String SELLER_PERFORMANCE_EXEMPTION = "qualifiedExempt";

    public static final String SYSTEM_EXEMPTION = "systemExempt";

    public static final String ORDERS = "orders";

    public static final String PRE_PAY = "pre_pay";

    public static final String PRE_PAY_MSG = "{\"batchPayRelationNo\":\"12990591211240529365700033697\",\"bizOrderNo\":[\"8189035580673697\"],\"buyerId\":\"1861703697\",\"checkoutResponses\":[{\"actualOrderAmount\":{\"cent\":16428,\"currencyCode\":\"CNY\"},\"actualPayOrderAmount\":{\"cent\":8708,\"currencyCode\":\"SAR\"},\"bizOrderNo\":\"8189035580673697\",\"checkoutOrderNo\":\"12990591103240529936800053697\",\"codCostOrderAmount\":{\"cent\":2830,\"currencyCode\":\"CNY\"},\"codCostPayAmount\":{\"cent\":1500,\"currencyCode\":\"SAR\"},\"comissionDists\":[{\"code\":\"COD_COST_FEE\",\"comissionCostOrderAmount\":{\"cent\":2830,\"currencyCode\":\"CNY\"},\"comissionPayAmout\":{\"cent\":1500,\"currencyCode\":\"SAR\"}}],\"fxRate\":{\"fxRate\":0.5301000000,\"id\":\"007ea03b-916c-44ce-896e-70d842418b58\",\"outId\":\"20240529C585395152\"},\"orderAmount\":{\"cent\":20738,\"currencyCode\":\"CNY\"},\"payOrderAmount\":{\"cent\":10993,\"currencyCode\":\"SAR\"},\"payPromotionOrderAmount\":{\"cent\":0,\"currencyCode\":\"CNY\"},\"payPromotionPayAmount\":{\"cent\":0,\"currencyCode\":\"SAR\"},\"succeeded\":true}],\"checkoutSource\":\"global.trade\",\"payOptionCode\":\"COD_V2\",\"prePayFinishTimeStamp\":\"1716968760801\",\"responseCode\":\"F\"}";


    public static Map<String, String> EXTRA_PARAMS;

    public static String PROPERTIES = "properties";

    public static final String REFUND_RATE = "refundRate";
    public static final String REFUND_RATE_STANDARD = "refundRateStandard";
    public static final String TEST_PATH = "test_path";
    public static final String REFUND_RATE_MESSAGE_TOPIC = "CHOICE_SNAD_REFUND_RATE_TOPIC";
    public static final String OVERSEA_CONSIGN_PUNISH_TOPIC = "OVERSEA_CONSIGN_PUNISH_TOPIC";
    public static final String PUNISH_TIMEOUT_TYPE = "PUNISH_TIMEOUT_TYPE";
    public static final String AE_SERVICE_PLATFORM_WEIGHT_SCALE_RECOVER_TOPIC = "AE_SERVICE_PLATFORM_WEIGHT_SCALE_RECOVER_TOPIC";
    public static final String AE_SERVICE_PLATFORM_WEIGHT_SCALE_RECOVER_TAG = "breachContractInit";

    public static final String ORDER_LINE_IDS = "orderlineIds";

    public static String SELLER_UPLOAD_REQUEST_BODY = "{\"addressId\":\"110000-110100-110101\",\"city\":\"Beijing\",\"country\":\"China\",\"countryCode\":\"CN\",\"detailAddress\":\"百货商场\",\"district\":\"Dongcheng District\",\"labelUrl\":\"https://ae01.alicdn.com/kf/S495ba8fc259d4f029c2a0c0468f05c83q.png\",\"logisticsCompany\":\"测试承运商6\",\"logisticsCompanyCode\":\"test_carrier_6\",\"logisticsTrackNum\":\"********\",\"operatorId\":*********,\"province\":\"Beijing\",\"receiveName\":\"杨先生\",\"receivePhone\":\"***********\",\"reverseOrderLineId\":****************,\"reverseOrderLineIdList\":\"****************,****************\",\"routingId\":**********,\"zipCode\":\"100010\"}";

    public static final String WAREHOUSE = "WAREHOUSE";

    public static final String PICK_UP_ORDER = "puo";

    public static final String FUND_TRANSFER_CENTER_TOPIC = "ae_fund_transfer_center_topic";

    public static final String PARTIAL_TRANSFER_TAG = "ae_fund_transfer_center_partial_transfer_success_tag";

    public static final String TOTAL_TRANSFER_TAG = "ae_fund_transfer_center_complete_tag";

    public static final String SOURCE = "source";

    public static final String REASON = "reason";

    public static final String AIM_TIMESTAMP = "aimTimestamp";

    public static final String STATUS = "status";

    public static final String XP_COMPLAINT = "aidc_new_complaint";

    public static final String MODIFIED_DATA_STR= "[{\"currency\":\"USD\",\"level\":1,\"lowerLimit\":0.2,\"maxAmount\":\"20\",\"minAmount\":\"minAmountReplace\",\"ratio\":\"0.00\",\"recommendRatio\":0.45,\"recommendRecoveryRatio\":0.32,\"stageRecoveryList\":[{\"maxRatio\":0.2,\"minRatio\":0,\"recoveryRatio\":0.21},{\"maxRatio\":0.3,\"minRatio\":0.2,\"recoveryRatio\":0.26},{\"maxRatio\":0.4,\"minRatio\":0.3,\"recoveryRatio\":0.29},{\"maxRatio\":0.5,\"minRatio\":0.4,\"recoveryRatio\":0.32},{\"maxRatio\":1,\"minRatio\":0.5,\"recoveryRatio\":0.4}],\"upperLimit\":1},{\"currency\":\"USD\",\"level\":2,\"lowerLimit\":0.1,\"maxAmount\":\"50\",\"minAmount\":\"20\",\"ratio\":\"0.00\",\"recommendRatio\":0.25,\"recommendRecoveryRatio\":0.26,\"stageRecoveryList\":[{\"maxRatio\":0.1,\"minRatio\":0,\"recoveryRatio\":0.2},{\"maxRatio\":0.2,\"minRatio\":0.1,\"recoveryRatio\":0.21},{\"maxRatio\":0.3,\"minRatio\":0.2,\"recoveryRatio\":0.26},{\"maxRatio\":0.4,\"minRatio\":0.3,\"recoveryRatio\":0.29},{\"maxRatio\":0.5,\"minRatio\":0.4,\"recoveryRatio\":0.33},{\"maxRatio\":1,\"minRatio\":0.5,\"recoveryRatio\":0.41}],\"upperLimit\":1},{\"currency\":\"USD\",\"level\":3,\"lowerLimit\":0.1,\"maxAmount\":\"100\",\"minAmount\":\"50\",\"ratio\":\"0.00\",\"recommendRatio\":0.25,\"recommendRecoveryRatio\":0.26,\"stageRecoveryList\":[{\"maxRatio\":0.1,\"minRatio\":0,\"recoveryRatio\":0.2},{\"maxRatio\":0.2,\"minRatio\":0.1,\"recoveryRatio\":0.21},{\"maxRatio\":0.3,\"minRatio\":0.2,\"recoveryRatio\":0.26},{\"maxRatio\":0.4,\"minRatio\":0.3,\"recoveryRatio\":0.29},{\"maxRatio\":0.5,\"minRatio\":0.4,\"recoveryRatio\":0.32},{\"maxRatio\":1,\"minRatio\":0.5,\"recoveryRatio\":0.39}],\"upperLimit\":1},{\"currency\":\"USD\",\"level\":4,\"lowerLimit\":0.1,\"maxAmount\":\"200\",\"minAmount\":\"100\",\"ratio\":\"0.00\",\"recommendRatio\":0.25,\"recommendRecoveryRatio\":0.25,\"stageRecoveryList\":[{\"maxRatio\":0.1,\"minRatio\":0,\"recoveryRatio\":0.19},{\"maxRatio\":0.2,\"minRatio\":0.1,\"recoveryRatio\":0.2},{\"maxRatio\":0.3,\"minRatio\":0.2,\"recoveryRatio\":0.25},{\"maxRatio\":0.4,\"minRatio\":0.3,\"recoveryRatio\":0.29},{\"maxRatio\":0.5,\"minRatio\":0.4,\"recoveryRatio\":0.32},{\"maxRatio\":1,\"minRatio\":0.5,\"recoveryRatio\":0.4}],\"upperLimit\":1},{\"currency\":\"USD\",\"level\":5,\"lowerLimit\":0.1,\"minAmount\":\"200\",\"ratio\":\"0.00\",\"recommendRatio\":0.25,\"recommendRecoveryRatio\":0.26,\"stageRecoveryList\":[{\"maxRatio\":0.1,\"minRatio\":0,\"recoveryRatio\":0.2},{\"maxRatio\":0.2,\"minRatio\":0.1,\"recoveryRatio\":0.22},{\"maxRatio\":0.3,\"minRatio\":0.2,\"recoveryRatio\":0.26},{\"maxRatio\":0.4,\"minRatio\":0.3,\"recoveryRatio\":0.3},{\"maxRatio\":0.5,\"minRatio\":0.4,\"recoveryRatio\":0.33},{\"maxRatio\":1,\"minRatio\":0.5,\"recoveryRatio\":0.41}],\"upperLimit\":1}]";

    public static final String AI_RESULT="{\"code\":200,\"data\":{\"taskId\":\"83b3f5eb-86d3-47a5-b5d8-5ca0f1187b01\",\"taskResult\":[{\"taskResult\":\"{\\\"result\\\":\\\"{\\\\\\\"success\\\\\\\":true,\\\\\\\"data\\\\\\\":{\\\\\\\"answer\\\\\\\":\\\\\\\"{\\\\\\\\\\\\\\\"reverseOrderLineId\\\\\\\\\\\\\\\":6351621025600453,\\\\\\\\\\\\\\\"icCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"-1\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"discount\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"100\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"eCode\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"2\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"status\\\\\\\\\\\\\\\":6}\\\\\\\",\\\\\\\"structData\\\\\\\":{\\\\\\\"reverseOrderLineId\\\\\\\":6351621025600453,\\\\\\\"icCode\\\\\\\":\\\\\\\"-1\\\\\\\",\\\\\\\"discount\\\\\\\":\\\\\\\"100\\\\\\\",\\\\\\\"eCode\\\\\\\":\\\\\\\"2\\\\\\\",\\\\\\\"status\\\\\\\":6}}}\\\",\\\"usage\\\":1}\",\"taskStatus\":\"finished\",\"usage\":1}],\"taskStatus\":\"finished\"},\"message\":\"success\",\"requestId\":\"212e5fb117358896701371673d11f6\",\"success\":true}";

    public static final String AI_TOPIC = "AI_GENERATION_RESULT";
    public static final String AI_TAG = "agent_invoke_batch_ae_aiRefund";

    public static final String JIT_PURCHASE_PUNISH_TOPIC = "JIT_PURCHASE_PUNISH_TOPIC";
    public static final String JIT_PUNISH_TAG = "JIT_PUNISH";
    public static final String JIT_PUNISH_TEST_PATH = "JIT_PUNISH_TEST_PATH";
    public static final String ITEM_CREATE = "ITEM_CREATE";

    public static final String BID_QTY = "bidQty";
    public static final String LOSS_QTY = "lossQty";
    public static final String STARLINK_BREACH_TOPIC = "AE-SERVICE-STARLINK-TOPIC";

}


