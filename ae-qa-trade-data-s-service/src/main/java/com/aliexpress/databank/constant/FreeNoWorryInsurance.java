package com.aliexpress.databank.constant;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;

@Slf4j
public enum FreeNoWorryInsurance {

    REPORT("投保成功", "INSURE_SUCCEED", "FIN_INSURANCE_GLOBAL-AE_FREE_RETURN_LOCAL", ""),
    ;


    FreeNoWorryInsurance(String scenario, String tag, String topic, String currentLogisticsStatus) {
        this.scenario = scenario;
        this.tag = tag;
        this.topic = topic;
        this.currentLogisticsStatus = currentLogisticsStatus;
    }

    @Getter
    private String scenario;

    @Getter
    private String tag;

    @Getter
    private String topic;

    @Getter
    private String currentLogisticsStatus;


    public static FreeNoWorryInsurance getReverseWorryFreeV2StatusByScenario(String scenario) {
        return Arrays.stream(FreeNoWorryInsurance.values()).filter(it -> it.getScenario().equalsIgnoreCase(scenario)).findFirst().get();
    }

}

