package com.aliexpress.databank.constant;

import android.taobao.util.StringUtils;
import lombok.Getter;

public enum ReturnSolutionEnum {

    SELF_PICKUP(AfterSaleSolution.RETURN_TO_PLATFORM, "Return_SellerPickup", "退平台仓商家自提"),
    DESTROY(AfterSaleSolution.RETURN_TO_PLATFORM, "Return_AbandonAfterQC", "退平台仓销毁"),
    RETURN_TO_HONGKONG(AfterSaleSolution.RETURN_TO_PLATFORM, "Return_HK_WH", "退香港仓"),
    RETURN_TO_CHINA(AfterSaleSolution.RETURN_TO_PLATFORM, "Return_MainlandSellerWH", "退平台仓后退到商家国内"),
    NO_QC_DESTROY(AfterSaleSolution.RETURN_TO_PLATFORM, "Return_DirectAbandon", "免质检销毁"),

    RETURN_TO_SELLER_BY_PLATFORM(AfterSaleSolution.RETURN_TO_SELLER, "Return_SellerWH", "退商家仓平台配送"),
    RETURN_TO_SELLER_BY_SELLER_MAIL(AfterSaleSolution.RETURN_TO_SELLER, "", "退商家仓商配面单"),


    /**
     * 仅退款没有解决方案code，无需映射
     */
    ONLY_REFUND(AfterSaleSolution.ONLY_REFUND, "", "退款不退货"),
    ;


    /**
     * 逆向自定义type，和前端交互使用，当前有三个：不退货仅退款(only_refund)、退平台仓(return_to_platform)、退商家仓(return_to_seller)
     */
    @Getter
    private AfterSaleSolution type;

    /**
     * 组网code，和逆向自定义type进行映射
     */
    @Getter
    private String code;

    /**
     * 描述信息
     */
    @Getter
    private String description;

    private ReturnSolutionEnum(AfterSaleSolution type, String code, String description) {
        this.type = type;
        this.code = code;
        this.description = description;
    }

    public static String getReturnSolutionByDescription(String description) {
        if (StringUtils.isEmpty(description)) {
            return null;
        }
        for (ReturnSolutionEnum solutionType : ReturnSolutionEnum.values()) {
            if (solutionType.description.equals(description)) {
                return solutionType.name();
            }
        }
        return null;
    }
}
