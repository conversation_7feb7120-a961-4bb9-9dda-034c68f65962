package com.aliexpress.databank.constant;

import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public enum TimeoutEnum {

    WAIT_SOLUTION_TIMEOUT("WAIT_SOLUTION_TIMEOUT", "协商超时任务"),
    WAIT_SELLER_FILL_ADDRESS_TIMEOUT("WAIT_SELLER_FILL_ADDRESS_TIMEOUT", "卖家补充退货地址超时任务"),
    WAIT_BUYER_RETURN_ITEM_WILL_TIMEOUT("WAIT_BUYER_RETURN_ITEM_WILL_TIMEOUT", "买家退货超时提醒"),
    WAIT_BUYER_RETURN_ITEM_TIMEOUT("WAIT_BUYER_RETURN_ITEM_TIMEOUT", "买家退货超时任务"),
    WAIT_RECEIVE_ITEM_WILL_TIMEOUT("WAIT_RECEIVE_ITEM_WILL_TIMEOUT", "卖家收货超时提醒-3天"),
    WAIT_RECEIVE_ITEM_TIMEOUT("WAIT_RECEIVE_ITEM_TIMEOUT", "卖家收货超时任务"),
    WAIT_CREATE_CLAIMS_TIMEOUT("WAIT_CREATE_CLAIMS_TIMEOUT", "理赔报案重试"),
    INSURANCE_ACCEPT_TIMEOUT("INSURANCE_ACCEPT_TIMEOUT", "报案接单超时"),
    WAIT_BUYER_RECOLLECT_CONFIRM("WAIT_BUYER_RECOLLECT_CONFIRM", "用户确认二揽超时"),
    WAIT_QUALITY_RESULT_TIMEOUT("WAIT_QUALITY_RESULT_TIMEOUT", "质检超时"),
    WAIT_AFTER_WAREHOUSE_ARBITRATION_TIMEOUT("WAIT_AFTER_WAREHOUSE_ARBITRATION_TIMEOUT", "仓后仲裁超时"),
    WAIT_SOLUTION_TIMEOUT_SNAD_SELLER("WAIT_SOLUTION_TIMEOUT_SNAD_SELLER", "商家响应超时"),
    WAIT_FULFILLMENT_ACCEPT_TIMEOUT("WAIT_FULFILLMENT_ACCEPT_TIMEOUT", "履约接单超时"),
    WAIT_SELLER_RESPONSE_TIMEOUT("WAIT_SELLER_RESPONSE_TIMEOUT", "取消订单商家响应超时")
    ;

    @Getter
    private final String value;

    @Getter
    private final String name;


    TimeoutEnum(String value, String name) {
        this.value = value;
        this.name = name;
    }

    public static List<String> getNames() {
        return Arrays.stream(TimeoutEnum.values()).map(TimeoutEnum::getName).collect(Collectors.toList());
    }

    public static String getValue(String scenario) {
        for (TimeoutEnum c : TimeoutEnum.values()) {
            if (Objects.equals(scenario, c.getName())) {
                return c.getValue();
            }
        }
        return null;
    }

}
