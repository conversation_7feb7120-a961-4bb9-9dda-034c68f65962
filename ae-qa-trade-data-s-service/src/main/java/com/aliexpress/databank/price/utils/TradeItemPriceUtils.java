package com.aliexpress.databank.price.utils;

import com.aliexpress.databank.price.dto.common.simulatordata.data.TradePriceItemDto;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 金额处理工具类
 */
public class TradeItemPriceUtils {
    /**
     * 根据订单按照店铺维度的拆分
     *
     * @param priceItems
     * @return
     */
    public static Map<String, List<TradePriceItemDto>> groupBySellerId(Map<String, TradePriceItemDto> priceItems) {
        Map<String, List<TradePriceItemDto>> groupBySellerId = new HashMap<>();
        for (TradePriceItemDto tradePriceItemDto : priceItems.values()) {
            String sellerId = tradePriceItemDto.getSellerId();
            List<TradePriceItemDto> list = groupBySellerId.get(sellerId);
            if (CollectionUtils.isEmpty(list)) {
                list = new ArrayList<>();
            }
            list.add(tradePriceItemDto);
            groupBySellerId.put(sellerId, list);
        }
        return groupBySellerId;
    }


    /**
     * 根据订单按照店铺维度的拆分
     *
     * @param priceItems
     * @return
     */
    public static Map<String, List<TradePriceItemDto>> groupBySellerId(List<TradePriceItemDto> priceItems) {
        Map<String, List<TradePriceItemDto>> groupBySellerId = new HashMap<>();
        for (TradePriceItemDto tradePriceItemDto : priceItems) {
            String sellerId = tradePriceItemDto.getSellerId();
            List<TradePriceItemDto> list = groupBySellerId.get(sellerId);
            if (CollectionUtils.isEmpty(list)) {
                list = new ArrayList<>();
            }
            list.add(tradePriceItemDto);
            groupBySellerId.put(sellerId, list);
        }
        return groupBySellerId;
    }


    /**
     * 获取订单全部的子订单信息
     *
     * @param priceItems
     * @return
     */
    public static List<TradePriceItemDto> getAllItem(Map<String, TradePriceItemDto> priceItems) {
        List<TradePriceItemDto> list = new ArrayList<>();
        for (TradePriceItemDto tradePriceItemDto : priceItems.values()) {
            list.add(tradePriceItemDto);
        }
        return list;
    }
}
