package com.aliexpress.databank.price.dto.common.render.checkout.detail;

import com.aliexpress.databank.price.dto.common.render.common.PriceCalculateDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 金额渲染，商品模块
 */

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class RenderItemDetailDto {

    /**
     * 商品标题
     */
    private String title;

    /**
     * 商品链接
     */
    private String url;

    /**
     * 商品对应的sku
     */
    private String sku;

    /**
     * 图片
     */
    private String pic;
    /**
     * 商品id
     */
    private String itemId;

    /**
     * skuId
     */
    private String skuId;

    /**
     * 数量
     */
    private Integer quality;

    /**
     * 单价
     */
    private PriceCalculateDto uniteFee;

    /**
     * 运费金额
     */
    private PriceCalculateDto shippingFee;


}
