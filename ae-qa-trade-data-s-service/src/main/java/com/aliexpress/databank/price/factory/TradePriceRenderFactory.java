package com.aliexpress.databank.price.factory;

import com.aliexpress.databank.price.dto.common.TradePriceJsonPathDto;
import com.aliexpress.databank.price.dto.common.render.common.PriceRenderResponse;
import com.aliexpress.databank.price.dto.common.simulatordata.SimulatorJsonPathResponseDto;
import org.noear.snack.ONode;

/**
 * 价格渲染工厂类，处理价格公式
 */
public interface TradePriceRenderFactory {

    PriceRenderResponse invoke(SimulatorJsonPathResponseDto result) throws Exception;


}
