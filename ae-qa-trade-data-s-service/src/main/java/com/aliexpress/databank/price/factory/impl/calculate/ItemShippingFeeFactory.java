package com.aliexpress.databank.price.factory.impl.calculate;

import com.alibaba.fastjson.JSONObject;
import com.aliexpress.databank.price.dto.common.render.common.PriceCalculateDto;
import com.aliexpress.databank.price.dto.common.simulatordata.data.PromotionInfoDto;
import com.aliexpress.databank.price.dto.common.simulatordata.data.TradePriceItemDto;
import com.aliexpress.databank.price.factory.TradePriceSingleCalculateFactory;
import com.aliexpress.databank.utils.FundCalculator;
import org.apache.commons.collections4.CollectionUtils;

import javax.money.MonetaryAmount;
import java.util.List;

public class ItemShippingFeeFactory implements TradePriceSingleCalculateFactory {
    @Override
    public PriceCalculateDto calculate(List<TradePriceItemDto> tradePriceItemDtos) throws Exception {
        TradePriceItemDto tradePriceItemDto = tradePriceItemDtos.get(0);

        PriceCalculateDto priceCalculateDto = new PriceCalculateDto();
        //金额不能为null
        MonetaryAmount price = tradePriceItemDto.getShippingFeeDto().getShippingBaseFee();
        if (price == null) {
            throw new Exception();
        }
        //(baseShippingFee-shippingDiscountFee)*rate
        priceCalculateDto.setFormulaOfCalculate("(baseShippingFee+shippingSavedFee)*rate");
        JSONObject detail = new JSONObject();
        priceCalculateDto.setDetail(detail);
        detail.put("baseShippingFee", tradePriceItemDto.getShippingFeeDto().getShippingBaseFee());
        detail.put("shippingSavedFee", tradePriceItemDto.getShippingFeeDto().getShippingSavedFee());
        detail.put("rate", tradePriceItemDto.getExchangeInfoDto().getRate());

        if (tradePriceItemDto.getShippingFeeDto().getShippingSavedFee() != null) {
            price = price.add(tradePriceItemDto.getShippingFeeDto().getShippingSavedFee());
        }
//        List<PromotionInfoDto> list = tradePriceItemDto.getShippingPromotions();
//        if (CollectionUtils.isNotEmpty(list)) {
//            for (PromotionInfoDto promotionInfoDto : list) {
//                //单品优惠营销返回是全部金额，所以需要单独除数量
//                price = price.subtract(promotionInfoDto.getDiscountFeeForBase());
//            }
//            detail.put("shippingDiscountFee", list);
//        }

        price = FundCalculator.calculateAmountByExchange(tradePriceItemDto.getExchangeInfoDto().getRate(), price, tradePriceItemDto.getExchangeInfoDto().getQutoCurrency(), Boolean.TRUE);
        priceCalculateDto.setPrice(price.toString());
        return priceCalculateDto;
    }
}
