package com.aliexpress.databank.price.factory.impl.calculate;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.global.money.Money;
import com.aliexpress.databank.price.dto.common.render.common.PriceCalculateDto;
import com.aliexpress.databank.price.dto.common.simulatordata.data.PromotionInfoDto;
import com.aliexpress.databank.price.dto.common.simulatordata.data.TradePriceItemDto;
import com.aliexpress.databank.price.factory.TradePriceSingleCalculateFactory;
import com.aliexpress.databank.utils.FundCalculator;
import org.apache.commons.collections4.CollectionUtils;

import javax.money.MonetaryAmount;
import java.util.ArrayList;
import java.util.List;

public class ShopSummary4SubtotalFactory implements TradePriceSingleCalculateFactory {


    @Override
    public PriceCalculateDto calculate(List<TradePriceItemDto> tradePriceItemDtos) throws Exception {
        PriceCalculateDto priceCalculateDto = new PriceCalculateDto();
        priceCalculateDto.setFormulaOfCalculate("sum((icPrice*(1+3cRate)-itemSingleDiscountFee/quality)*quality)*exRate");

        List<JSONObject> details = new ArrayList<>();

        JSONObject detailTemp = new JSONObject();
        TradePriceItemDto temp = tradePriceItemDtos.get(0);
        MonetaryAmount money = Money.zero(temp.getBaseCurrency());
        for (TradePriceItemDto tradePriceItemDto : tradePriceItemDtos) {
            JSONObject detail = new JSONObject();
            details.add(detail);
            detail.put("icPrice", tradePriceItemDto.getPrice().toString());
            detail.put("3cRate", tradePriceItemDto.getRate());
            detail.put("quality", tradePriceItemDto.getQuality());
            MonetaryAmount price = tradePriceItemDto.getPrice().multiply(1 + (tradePriceItemDto.getRate() == null ? 0 : tradePriceItemDto.getRate().doubleValue()));
            List<PromotionInfoDto> list = tradePriceItemDto.getItemSinglePromotions();
            if (CollectionUtils.isNotEmpty(list)) {
                for (PromotionInfoDto promotionInfoDto : list) {
                    //单品优惠营销返回是全部金额，所以需要单独除数量
                    price = price.subtract(promotionInfoDto.getDiscountFeeForBase().divide(tradePriceItemDto.getQuality()));
                }
                detail.put("itemSingleDiscountFee", list);
            }
            money = money.add(price.multiply(tradePriceItemDto.getQuality()));

        }

        money = FundCalculator.calculateAmountByExchange(temp.getExchangeInfoDto().getRate(), money, temp.getExchangeInfoDto().getQutoCurrency(), Boolean.TRUE);
        detailTemp.put("exRate", temp.getExchangeInfoDto().getRate());
        detailTemp.put("list", details);
        priceCalculateDto.setPrice(money.toString());
        priceCalculateDto.setDetail(detailTemp);
        return priceCalculateDto;


    }



}
