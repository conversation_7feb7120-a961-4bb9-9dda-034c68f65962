package com.aliexpress.databank.price.impl;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.global.qa.dc.model.builder.QueryResultBuilder;
import com.alibaba.global.qa.dc.model.param.SystemDTO;
import com.alibaba.global.qa.dc.model.result.QueryResultUnit;
import com.alibaba.global.qa.dc.model.result.ResultDTO;
import com.aliexpress.databank.constant.Constant;
import com.aliexpress.databank.diamond.TradeSubinvokesConfigDiamondRunner;
import com.aliexpress.databank.price.TradeSubinvokesCheckService;
import com.aliexpress.databank.price.constant.TradePriceConfigTypeEnum;
import com.aliexpress.databank.price.context.TradePriceRenderContext;
import com.aliexpress.databank.price.dto.common.CheckFiledDTO;
import com.aliexpress.databank.price.dto.common.TradePriceConfigDto;
import com.aliexpress.databank.price.dto.common.render.checkout.CheckSubInvokeResponse;
import com.aliexpress.databank.price.dto.common.render.common.BaseResult;
import com.aliexpress.databank.price.dto.common.render.common.PriceRenderResponse;
import com.aliexpress.databank.price.dto.common.simulatordata.data.TradePriceItemDto;
import com.aliexpress.databank.price.factory.impl.adapt.CheckSubInvokeFactory;
import com.aliexpress.databank.price.helper.TradePriceJsonPathHelper;
import com.aliexpress.databank.price.utils.SimulatorDataUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.noear.snack.ONode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Service
@HSFProvider(serviceInterface = TradeSubinvokesCheckService.class, serviceVersion = "1.0.0", serviceGroup = "HSF",clientTimeout = 20000)
@Slf4j
public class TradeSubinvokesCheckServiceImpl implements TradeSubinvokesCheckService {
    @Autowired
    private TradePriceJsonPathHelper tradePriceJsonPathHelper;

    @Override
    public ResultDTO getSubinvokesCheckResult(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        Map<String, QueryResultUnit> data = new LinkedHashMap<>();
        JSONObject jsonObject = JSONObject.parseObject(params);
        String traceId = jsonObject.getString(Constant.TRACE_ID);
        String simulatorKey = jsonObject.getString(Constant.SIMULATOR_KEY);

        //这边是jsonArray的格式
        BaseResult renderResult = getSubinvokesResult(traceId, TradePriceConfigTypeEnum.RENDER.toString(),simulatorKey, "ae-trade-ns-s");

        if (StringUtils.isNotEmpty(renderResult.getErrorMessage())) {
            result.setSuccess(false);
            result.setMessage(renderResult.getErrorMessage());
            result.setData("getSubinvokesResult failed");
            return result;
        }

        //目前仅支持渲染和下单
        CheckSubInvokeResponse checkSubInvokeResponse = (CheckSubInvokeResponse) renderResult.getResult();
        Map<String, TradePriceItemDto> priceItems = checkSubInvokeResponse.getBody().getPriceItems();

        // 根据trace获取基础价格

        for(String key:priceItems.keySet()){
            TradePriceItemDto value = priceItems.get(key);

            Map<String, QueryResultUnit> basePrice = QueryResultBuilder.buildQueryResult("skuId:"+key+"子调用参数", null, getTradePriceItemDtoKey(), value);
            data.putAll(basePrice);
        }


        //check结果
        Map<String, QueryResultUnit> checkResult = QueryResultBuilder.buildQueryResult("诊断结果", null, null, checkSubInvokeResponse.getCheckFiledDTO());
        data.putAll(checkResult);

        result.setData(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat,SerializerFeature.DisableCircularReferenceDetect));
        result.setSuccess(true);
        return result;
    }

    @Override
    public BaseResult<PriceRenderResponse> getSubinvokesResult(String trace, String type,String simulatorId, String appName) {

        BaseResult<PriceRenderResponse> baseResult = new BaseResult<>();

        //通过天启获取到上下游到依赖
        JSONObject jsonObject = null;
        try {
            jsonObject = SimulatorDataUtils.getSimulatorResult(null, trace, type, simulatorId, appName);
        } catch (Exception e) {
            baseResult.setErrorMessage(e.getMessage());
            return baseResult;
        }


        if (jsonObject == null) {
            baseResult.setErrorMessage("天启数据获取为空，请检测天启是否有采集到");
            return baseResult;
        }

        ONode o = ONode.load(jsonObject.toJSONString());

        if (o == null) {
            return null;
        }

        TradePriceConfigDto tradePriceConfigDto = TradeSubinvokesConfigDiamondRunner.getConfig();
        if (tradePriceConfigDto == null) {
            baseResult.setErrorMessage("数据配置异常，请联系@磅蟹检测配置");
            return baseResult;
        }
        //构建上下文
        TradePriceConfigTypeEnum tradePriceConfigTypeEnum = TradePriceConfigTypeEnum.valueOf(type);
        TradePriceRenderContext tradePriceRenderContext = TradePriceRenderContext.builder()
                .priceJsonPathDtos(tradePriceConfigDto.getTradePriceForJsonPathConfigDto().getConfigByEnum(tradePriceConfigTypeEnum))
                .tradePriceConfigTypeEnum(tradePriceConfigTypeEnum)
                .simulatorNode(o)
                .build();
        //理论上使用责任链来支持，但是这边就直接循环调用吧
        try {
            tradePriceJsonPathHelper.handleJsonPathParse(tradePriceRenderContext);
        } catch (Exception e) {
            log.error("error",e);
            baseResult.setErrorMessage("下游返回值解析异常,请联系@磅蟹检测配置TradeInvokesConfigDto");
            return baseResult;
        }

        //获取到了原始数据，需要根据数据来还原页面

        try {
          CheckSubInvokeResponse checkSubInvokeResponse = new CheckSubInvokeResponse();
          checkSubInvokeResponse.setBody(tradePriceRenderContext.getBody());
          List<CheckFiledDTO> checkFiledDTO = CheckSubInvokeFactory.checkSubInvoke(tradePriceRenderContext.getBody());
          checkSubInvokeResponse.setCheckFiledDTO(checkFiledDTO);

          baseResult.setResult(checkSubInvokeResponse);
        } catch (Exception e) {
            e.printStackTrace();
            baseResult.setErrorMessage("子调用检查异常");
            return baseResult;
        }

        return baseResult;
    }


    public List<String> getTradePriceItemDtoKey(){
        List<String> tradePriceItemDtoKey = new ArrayList<>();
        tradePriceItemDtoKey.add("productId");
        tradePriceItemDtoKey.add("skuId");
        tradePriceItemDtoKey.add("price");
        tradePriceItemDtoKey.add("quality");
        tradePriceItemDtoKey.add("exchangeInfoDto");
        tradePriceItemDtoKey.add("priceCenterInfoDto");
        tradePriceItemDtoKey.add("shippingFeeDto");
        tradePriceItemDtoKey.add("dp");
        tradePriceItemDtoKey.add("calDiscountPrice");
        tradePriceItemDtoKey.add("promotionDetailDtos");
        tradePriceItemDtoKey.add("promotionInfoDtos");//以下是下单命中的优惠，有量价是量价，无量价是原价
        tradePriceItemDtoKey.add("itemSinglePromotions");
        tradePriceItemDtoKey.add("itemGoldPromotions");
        tradePriceItemDtoKey.add("itemPromotions");//非金本位优惠
        tradePriceItemDtoKey.add("goldPromotions");
        tradePriceItemDtoKey.add("shippingPromotions");

        return tradePriceItemDtoKey;

    }


}
