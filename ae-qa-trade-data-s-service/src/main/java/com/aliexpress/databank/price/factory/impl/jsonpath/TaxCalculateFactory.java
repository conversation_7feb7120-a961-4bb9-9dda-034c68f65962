package com.aliexpress.databank.price.factory.impl.jsonpath;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.global.money.Money;
import com.aliexpress.databank.price.annotation.TradePriceJsonPathFactoryAnnotation;
import com.aliexpress.databank.price.dto.common.TradePriceJsonPathDto;
import com.aliexpress.databank.price.dto.common.simulatordata.SimulatorJsonPathResponseDto;
import com.aliexpress.databank.price.dto.common.simulatordata.data.TaxDetailDto;
import com.aliexpress.databank.price.dto.common.simulatordata.data.TradePriceItemDto;
import org.apache.commons.collections4.CollectionUtils;
import org.noear.snack.ONode;
import org.springframework.stereotype.Component;

import javax.money.MonetaryAmount;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

@Component
@TradePriceJsonPathFactoryAnnotation(value = "taxCalculateFactory")
public class TaxCalculateFactory extends AbstractOrderPriceRenderFactory {

    @Override
    public void handleResult(SimulatorJsonPathResponseDto result, TradePriceJsonPathDto tradePriceJsonPathDto, ONode target, ONode selectResult) {
        //转化成jsonObject
        if (selectResult.isNull()) {
            return;
        }
        String json = selectResult.toJson();
        JSONArray jsonArray = JSONArray.parseArray(json);

        Map<String, TradePriceItemDto> idMaps = new HashMap<>();
        for (TradePriceItemDto tradePriceItemDto : result.getPriceItems().values()) {
            idMaps.put(tradePriceItemDto.getId(), tradePriceItemDto);
        }
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject taxDetail = jsonArray.getJSONObject(i);
            JSONArray taxCalculateItemResultDTOList = taxDetail.getJSONArray("taxCalculateItemResultDTOList");
            if (CollectionUtils.isEmpty(taxCalculateItemResultDTOList)) {
                continue;
            }
            for (int j = 0; j < taxCalculateItemResultDTOList.size(); j++) {
                JSONObject taxCalculateItemResult = taxCalculateItemResultDTOList.getJSONObject(j);
                String id = taxCalculateItemResult.getString("orderUniqueKey");
                TaxDetailDto taxDetailDto = buildTaxDetail(taxCalculateItemResult);
                TradePriceItemDto tradePriceItemDto = idMaps.get(id);
                if (CollectionUtils.isEmpty(tradePriceItemDto.getTaxDetails())) {
                    tradePriceItemDto.setTaxDetails(new ArrayList<>());
                }
                tradePriceItemDto.getTaxDetails().add(taxDetailDto);
            }
        }


    }

    /**
     * 构建税明细
     *
     * @param taxCalculateItemResult
     * @return
     */
    private TaxDetailDto buildTaxDetail(JSONObject taxCalculateItemResult) {
        String type = taxCalculateItemResult.getString("itemType");
        JSONObject tax = taxCalculateItemResult.getJSONObject("itemTaxAmount");
        MonetaryAmount monetaryAmount = Money.of(tax.getBigDecimal("cent").divide(new BigDecimal(100)), tax.getString("currencyCode"));

        return TaxDetailDto.builder().taxFee(monetaryAmount).type(type).build();
    }
}
