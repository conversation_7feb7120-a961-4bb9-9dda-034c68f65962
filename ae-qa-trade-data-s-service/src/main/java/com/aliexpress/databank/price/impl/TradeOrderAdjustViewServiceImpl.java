package com.aliexpress.databank.price.impl;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.common.lang.StringUtil;
import com.alibaba.ecommerce.module.Response;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.global.landlord.LandlordContext;
import com.alibaba.global.money.Money;
import com.alibaba.global.order.management.api.facade.OrderQueryForBuyerFacade;
import com.alibaba.global.order.management.api.model.Features;
import com.alibaba.global.order.management.api.model.TradeOrderDTO;
import com.alibaba.global.order.management.api.model.TradeOrderLineDTO;
import com.alibaba.global.qa.dc.model.param.SystemDTO;
import com.alibaba.global.qa.dc.model.result.ResultDTO;
import com.aliexpress.databank.constant.Constant;
import com.aliexpress.databank.price.TradeOrderAdjustViewService;
import com.aliexpress.databank.price.dto.response.TradeItemFee;
import com.aliexpress.databank.price.utils.ApportionPriceUtils;
import com.aliexpress.databank.price.utils.DpOrderUtils;
import com.aliexpress.databank.price.utils.PriceUtils;
import com.taobao.eagleeye.EagleEye;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.money.MonetaryAmount;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@HSFProvider(serviceInterface = TradeOrderAdjustViewService.class)

public class TradeOrderAdjustViewServiceImpl implements TradeOrderAdjustViewService {

    @Autowired
    private OrderQueryForBuyerFacade orderQueryForBuyerFacade;


    @Override
    public ResultDTO adjustPriceByOrderId(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long orderId = jsonObject.getLong(Constant.ORDER_ID);
        String staging = jsonObject.getString(Constant.STAGING);
        BigDecimal afterPrice = new BigDecimal(jsonObject.getString("afterPrice"));
        //查询区域化接口
        LandlordContext.setTenantSpec("AE_GLOBAL", -1);

        if (StringUtil.isNotBlank(staging)) {
            EagleEye.putUserData("scm_project", staging);//临时处理

        }

        Object data = getTradeOrderAdjustDetail(orderId, afterPrice);

        result.setData(data.toString());
        result.setSuccess(true);
        return result;
    }



    public Object getTradeOrderAdjustDetail(Long tradeOrder, BigDecimal adjustPrice) {



        //首先获取订单明细
        Response<TradeOrderDTO> response= orderQueryForBuyerFacade.queryTradeOrderById(tradeOrder);

        //获取订单金额
        if(response==null||!response.isSuccess()||response.getModule()==null){
            return null;
        }
        TradeOrderDTO tradeOrderDTO=response.getModule();
        //
        List<TradeOrderLineDTO> lineDTOS=tradeOrderDTO.getOrderLines();

        List<TradeItemFee> originOrderLineFeeList=lineDTOS.stream().map(tradeOrderLineDTO ->  convertItemFee(tradeOrderLineDTO)
        ).collect(Collectors.toList());
        //构建原始金额

        List<TradeItemFee> originDpOrderLineFeeList=new ArrayList<>();
        //先处理原价
        //根据原价计算出金额
        //获取订单总额
        MonetaryAmount orderMount= PriceUtils.getOrderAmount(tradeOrderDTO);

        if(orderMount==null){
            return null;
        }
        //获取新调整的金额
        //这边的币种同意为报价币种
        MonetaryAmount orderAdjustMount= PriceUtils.getOrderAdjustAmount(tradeOrderDTO);

        if(orderAdjustMount!=null){
            orderMount=orderMount.subtract(orderAdjustMount);
        }
        //目标金额
        String currency=orderMount.getCurrency().getCurrencyCode();
        MonetaryAmount newOrderMount=Money.of(adjustPrice,orderMount.getCurrency());
        MonetaryAmount adjustFee = newOrderMount.subtract(orderMount);
        //把金额分摊一下
        ApportionPriceUtils.apportionOrderPrice(tradeOrderDTO,adjustFee);
        //这个时候子订单维度的分摊已经结束，构建返回结果
        //还差一步重新计算税

        //调价后的结果
        List<TradeItemFee> adjustOrderLineFeeList=lineDTOS.stream().map(tradeOrderLineDTO ->  convertItemFee(tradeOrderLineDTO)
        ).collect(Collectors.toList());


        boolean isDp=false;
        //首先构建调价前的金额
        for(TradeOrderLineDTO tradeOrderLineDTO:lineDTOS){
            //判断是否有量价
            if("1".equalsIgnoreCase(tradeOrderLineDTO.getFeatures().getFeature("d_p"))){
                isDp=true;
            }

            if(isDp){
                JSONObject jsonObject=JSONObject.parseObject(tradeOrderLineDTO.getFeatures().getFeature("_dynamic_price"));
                convertDpPrice(jsonObject,tradeOrderLineDTO);
                MonetaryAmount oip=Money.of(BigDecimal.valueOf(Long.valueOf(tradeOrderLineDTO.getFeatures().getFeature("oip"))).divide(BigDecimal.valueOf(100)),tradeOrderLineDTO.getFeatures().getFeature("opicc"));
                MonetaryAmount dpoip=PriceUtils.convertMoney(jsonObject,"originalFee",false);
                BigDecimal rate=getVolumeRate(oip,dpoip);
                MonetaryAmount dpadjustFee=Money.of(Money.of(tradeOrderLineDTO.getAdjustFee()).getAmount().multiply(rate), adjustFee.getCurrency());
                tradeOrderLineDTO.setAdjustFee(dpadjustFee);
                ApportionPriceUtils.apportionOrderLinePrice(tradeOrderLineDTO);
            }
        }
        if(isDp){
            List<TradeItemFee> dpadjustOrderLineFeeList=lineDTOS.stream().map(tradeOrderLineDTO ->  convertItemFee(tradeOrderLineDTO)
            ).collect(Collectors.toList());
            System.out.println(dpadjustOrderLineFeeList.size());
        }



        //计算出差值，得到需要调价的金额


        return null;
    }



    private final static double MAX_USD_AMOUNT = 99999999d;
    private final static double MIN_USD_AMOUNT = 0.01d;

    private final static double MAX_RUB_AMOUNT = 99999999d;
    private final static double MIN_RUB_AMOUNT = 1d;

    private final static double MAX_EUR_AMOUNT = 99999999d;
    private final static double MIN_EUR_AMOUNT = 0.01d;

    private boolean valiteParam(Money newOrderAmount,TradeOrderLineDTO firstOrderLine){


        if ("USD".equals(newOrderAmount.getCurrency().getCurrencyCode())) {
            if (newOrderAmount.getAmount().doubleValue() > MAX_USD_AMOUNT
                    || newOrderAmount.getAmount().doubleValue() < MIN_USD_AMOUNT) {
               return false;
            }
        }
        if ("RUB".equals(newOrderAmount.getCurrency().getCurrencyCode())) {
            if (newOrderAmount.getAmount().doubleValue() > MAX_RUB_AMOUNT
                    || newOrderAmount.getAmount().doubleValue() < MIN_RUB_AMOUNT) {
                return false;
            }
        }
        if ("EUR".equals(newOrderAmount.getCurrency().getCurrencyCode())) {
            if (newOrderAmount.getAmount().doubleValue() > MAX_EUR_AMOUNT
                    || newOrderAmount.getAmount().doubleValue() < MIN_EUR_AMOUNT) {
                return false;
            }
        }

        //if (input.getOrder().getAttribute(AttributeConstants.ATTR_IS_GROUP_ORDER).equals("1")) {
        //    return ErrorMessage.of("B-TRADE-GROUP-BUY-NOT-MODIFYPRICE", "B-TRADE-GROUP-BUY-NOT-MODIFYPRICE",
        // "B-TRADE-GROUP-BUY-NOT-MODIFYPRICE");
        //}



        if (!isOverTime(firstOrderLine.getCreateTime())) {
            return false;
        }
        return true;
    }


    private boolean isOverTime(ZonedDateTime orderCreateTime) {

        ZonedDateTime now = ZonedDateTime.now(orderCreateTime.getZone());
        Date createDate = Date.from(orderCreateTime.toInstant());
        Date nowDate = Date.from(now.toInstant());

        if ((nowDate.getTime() - createDate.getTime()) / 50000 > 15) {
            return true;
        }

        return false;

    }


    /**
     * 构建返回结果页
     * @param tradeOrderLineDTO
     * @return
     */
    private static TradeItemFee convertItemFee(TradeOrderLineDTO tradeOrderLineDTO){
        String currency=tradeOrderLineDTO.getExchangeInfo().getBaseCurrency();
        TradeItemFee tradeItemFee =TradeItemFee.builder()
                .itemGoldDiscountFee(Money.of(PriceUtils.getGoldStandardFee(tradeOrderLineDTO,currency)))
                .itemNoGoldDiscountFee(Money.of(PriceUtils.getNoGoldStandardFee(tradeOrderLineDTO,currency)))
                .adjustFee(Money.of(tradeOrderLineDTO.getAdjustFee()))
                .actualFee(Money.of(PriceUtils.getOrderLineActualFee(tradeOrderLineDTO,currency,false)))
                .shippingFee(Money.of(tradeOrderLineDTO.getShippingActualFee()))
                .taxFee(Money.of(tradeOrderLineDTO.getTaxActualFee()))
                .uniteFee(Money.of(tradeOrderLineDTO.getUnitFee()))
                .shippingDiscountFee(Money.of(PriceUtils.getShippingNoGoldStandardFee(tradeOrderLineDTO,currency)))
                .tradeOrderId(tradeOrderLineDTO.getTradeOrderId())
                .tradeOrderLineId(tradeOrderLineDTO.getTradeOrderLineId())
                .orderMount(Money.of(PriceUtils.getOrderLineOrderAmount(tradeOrderLineDTO)))
                .originalActualFee(Money.of(PriceUtils.getOrderLineActualFee(tradeOrderLineDTO,currency,true)))
                .build();
        Features features=tradeOrderLineDTO.getFeatures();
        if(StringUtils.isNotEmpty(features.getFeature("paf"))){
            tradeItemFee.setGaf(Money.of(features.getFeature("paf")));
        }
        if(StringUtils.isNotEmpty(features.getFeature("gaf"))){
            tradeItemFee.setGaf(Money.of(features.getFeature("gaf")));
        }
        return tradeItemFee;

    }


    /**
     * 转化量价金额
     * @param jsonObject
     * @param tradeOrderLineDTO
     */
    private void convertDpPrice(JSONObject jsonObject,TradeOrderLineDTO tradeOrderLineDTO){

        tradeOrderLineDTO.setShippingDiscountFee(PriceUtils.convertMoney(jsonObject,"shippingDiscountFee",false));
        tradeOrderLineDTO.setUnitFee(DpOrderUtils.getOrderLineUnitFee(tradeOrderLineDTO));
        //不需要覆盖调价，调价会基于原价重新计算
       // tradeOrderLineDTO.setAdjustFee(PriceUtils.convertMoney(jsonObject,"adjustFee",false));
        tradeOrderLineDTO.setActualFee(PriceUtils.convertMoney(jsonObject,"actualFee",false));
        DpOrderUtils.getOrderLineSaleDiscountFee(tradeOrderLineDTO);
        //paf和gaf要用量价的做覆盖
        tradeOrderLineDTO.getFeatures().addFeature("paf",jsonObject.getJSONObject("extra").getString("paf"));
        tradeOrderLineDTO.getFeatures().addFeature("gaf",jsonObject.getJSONObject("extra").getString("gaf"));

    }


    //获取计算比例
    private BigDecimal getVolumeRate(MonetaryAmount originalFees,MonetaryAmount dpOriginalFee) {

        if(originalFees!=null && dpOriginalFee!=null){
            BigDecimal b1 = new BigDecimal(Money.of(dpOriginalFee).getCent());
            BigDecimal b2 = new BigDecimal(Money.of(originalFees).getCent());
            return b1.divide(b2,3, RoundingMode.UP);
        }
        return new BigDecimal(1);
    }



}
