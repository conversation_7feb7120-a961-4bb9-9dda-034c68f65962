package com.aliexpress.databank.price.dto.common.render.checkout;

import com.aliexpress.databank.price.dto.common.render.checkout.detail.RenderItemDetailDto;
import com.aliexpress.databank.price.dto.common.render.checkout.detail.RenderOrderSummaryDetailDto;
import com.aliexpress.databank.price.dto.common.render.checkout.detail.RenderShopDetailDto;
import com.aliexpress.databank.price.dto.common.render.common.PriceRenderResponse;
import com.aliexpress.databank.price.dto.common.simulatordata.SimulatorJsonPathResponseDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 订单金额渲染返回结果
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CheckOutPriceRenderResponse implements PriceRenderResponse {

    /**
     * 店铺维度的模块
     */
    private List<RenderShopDetailDto> shopModel;

    /**
     * 总订单维度
     */
    private RenderOrderSummaryDetailDto orderSummary;

    /**
     * 原始数据
     */
    private SimulatorJsonPathResponseDto body;



}
