package com.aliexpress.databank.price.dto.common.render.checkout.detail;

import com.aliexpress.databank.price.dto.common.render.common.PriceCalculateDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 订单渲染Summary模块
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class RenderSummaryDetailDto {

    /**
     * 子订单商品总金额
     */
    private PriceCalculateDto subTotalFee;


    /**
     * 子订单运费总金额
     */
    private PriceCalculateDto shippingFee;

    /**
     * 税费
     */
    private PriceCalculateDto taxFee;

    /**
     * 卖家优惠明细
     */
    private PriceCalculateDto sellerDiscount;

    /**
     * 店铺卷优惠
     */
    private PriceCalculateDto storeCouponFee;

    /**
     * 总金额
     */
    private PriceCalculateDto totalFee;


}
