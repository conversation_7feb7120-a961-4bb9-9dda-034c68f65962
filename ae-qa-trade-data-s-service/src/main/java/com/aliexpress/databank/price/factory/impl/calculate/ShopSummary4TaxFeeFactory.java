package com.aliexpress.databank.price.factory.impl.calculate;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.global.money.Money;
import com.aliexpress.databank.price.dto.common.render.common.PriceCalculateDto;
import com.aliexpress.databank.price.dto.common.simulatordata.data.PromotionInfoDto;
import com.aliexpress.databank.price.dto.common.simulatordata.data.TaxDetailDto;
import com.aliexpress.databank.price.dto.common.simulatordata.data.TradePriceItemDto;
import com.aliexpress.databank.price.factory.TradePriceSingleCalculateFactory;
import com.aliexpress.databank.utils.FundCalculator;
import org.apache.commons.collections4.CollectionUtils;

import javax.money.MonetaryAmount;
import java.util.ArrayList;
import java.util.List;

public class ShopSummary4TaxFeeFactory implements TradePriceSingleCalculateFactory {
    @Override
    public PriceCalculateDto calculate(List<TradePriceItemDto> tradePriceItemDtos) throws Exception {


        PriceCalculateDto priceCalculateDto = new PriceCalculateDto();
        priceCalculateDto.setFormulaOfCalculate("sum(sum(taxDetail)*rate)");
        TradePriceItemDto temp = tradePriceItemDtos.get(0);
        MonetaryAmount money = Money.zero(temp.getQuoteCurrency());

        JSONObject result = new JSONObject();
        List<JSONObject> details = new ArrayList<>();
        for (TradePriceItemDto tradePriceItemDto : tradePriceItemDtos) {
            JSONObject detail = new JSONObject();
            List<TaxDetailDto> list = tradePriceItemDto.getTaxDetails();
            if (CollectionUtils.isEmpty(list)) {
                continue;
            }
            MonetaryAmount tax = Money.zero(temp.getBaseCurrency());
            for (TaxDetailDto taxDetailDto : list) {
                tax = tax.add(taxDetailDto.getTaxFee());
            }
            detail.put("taxDetail", list);
            detail.put("rate", tradePriceItemDto.getExchangeInfoDto().getRate());
            details.add(detail);
            tax = FundCalculator.calculateAmountByExchange(tradePriceItemDto.getExchangeInfoDto().getRate(), tax, tradePriceItemDto.getQuoteCurrency(), true);
            money = money.add(tax);
        }
        if (money.isZero()) {
            return null;
        }
        priceCalculateDto.setPrice(money.toString());
        result.put("list", details);
        priceCalculateDto.setDetail(result);
        return priceCalculateDto;
    }
}
