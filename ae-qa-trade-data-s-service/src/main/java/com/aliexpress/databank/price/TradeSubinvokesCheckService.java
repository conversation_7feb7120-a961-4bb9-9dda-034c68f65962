package com.aliexpress.databank.price;

import com.alibaba.global.qa.dc.model.param.SystemDTO;
import com.alibaba.global.qa.dc.model.result.ResultDTO;
import com.aliexpress.databank.price.dto.common.render.common.BaseResult;
import com.aliexpress.databank.price.dto.common.render.common.PriceRenderResponse;

public interface TradeSubinvokesCheckService {


    ResultDTO getSubinvokesCheckResult(String params, SystemDTO systemDTO) throws Exception;


    BaseResult<PriceRenderResponse> getSubinvokesResult(String trace, String type, String simulatorId, String appName) throws Exception;


    }
