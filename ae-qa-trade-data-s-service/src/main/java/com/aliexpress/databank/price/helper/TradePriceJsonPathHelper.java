package com.aliexpress.databank.price.helper;

import com.alibaba.fastjson.JSONObject;
import com.aliexpress.databank.price.annotation.TradePriceJsonPathFactoryAnnotation;
import com.aliexpress.databank.price.context.TradePriceRenderContext;
import com.aliexpress.databank.price.dto.common.TradePriceJsonPathDto;
import com.aliexpress.databank.price.dto.common.simulatordata.SimulatorJsonPathResponseDto;
import com.aliexpress.databank.price.factory.TradePriceJsonPathFactory;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
public class TradePriceJsonPathHelper implements ApplicationListener<ContextRefreshedEvent> {

    private volatile Map<String, TradePriceJsonPathFactory> map=new HashMap<>();


    @Override
    public void onApplicationEvent(ContextRefreshedEvent contextRefreshedEvent) {
        ApplicationContext applicationContext = contextRefreshedEvent.getApplicationContext();
        // 通过注解获取相关的类
        Map<String, Object> beanMaps = applicationContext.getBeansWithAnnotation(TradePriceJsonPathFactoryAnnotation.class);
        if (MapUtils.isEmpty(beanMaps)) {
            return;
        }
        for (Map.Entry<String, Object> entrymap : beanMaps.entrySet()) {
            try {
                // 通过反射获取相关的实现类的Object
                TradePriceJsonPathFactory tradePriceJsonPathFactory = (TradePriceJsonPathFactory) entrymap.getValue();
                if (tradePriceJsonPathFactory == null) {
                    continue;
                }
                TradePriceJsonPathFactoryAnnotation tradePriceJsonPathFactoryAnnotation = tradePriceJsonPathFactory.getClass().getAnnotation(TradePriceJsonPathFactoryAnnotation.class);
                if (tradePriceJsonPathFactoryAnnotation == null) {
                    continue;
                }
                String name = tradePriceJsonPathFactoryAnnotation.value();
                map.put(name, tradePriceJsonPathFactory);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }


    }


    public void handleJsonPathParse(TradePriceRenderContext tradePriceRenderContext) {
        List<TradePriceJsonPathDto> list = tradePriceRenderContext.getPriceJsonPathDtos();
        if (tradePriceRenderContext.getBody() == null) {
            tradePriceRenderContext.setBody(new SimulatorJsonPathResponseDto());
        }
        SimulatorJsonPathResponseDto body = tradePriceRenderContext.getBody();
        for (TradePriceJsonPathDto tradePriceJsonPathDto : list) {
            if (StringUtils.isEmpty(tradePriceJsonPathDto.getFactory())) {
                //打一行日志
                continue;
            }
            TradePriceJsonPathFactory tradePriceJsonPathFactory = getFactoryByName(tradePriceJsonPathDto.getFactory());
            tradePriceJsonPathFactory.invoke(body,tradePriceJsonPathDto,tradePriceRenderContext.getSimulatorNode());
        }
//        ONode node=  ONode.load(JSONObject.toJSONString(body));
//        tradePriceRenderContext.setBodyNode(node);

    }


    /**
     * 通过配置名称获取到这个创建工厂
     *
     * @param name
     * @return
     */
    public TradePriceJsonPathFactory getFactoryByName(String name) {
        if (MapUtils.isEmpty(map)) {
            return null;
        }
        return map.get(name);
    }
}
