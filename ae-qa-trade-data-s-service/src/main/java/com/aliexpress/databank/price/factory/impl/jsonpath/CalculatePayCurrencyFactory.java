package com.aliexpress.databank.price.factory.impl.jsonpath;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.global.money.Money;
import com.aliexpress.databank.price.annotation.TradePriceJsonPathFactoryAnnotation;
import com.aliexpress.databank.price.dto.common.TradePriceJsonPathDto;
import com.aliexpress.databank.price.dto.common.simulatordata.SimulatorJsonPathResponseDto;
import com.aliexpress.databank.price.dto.common.simulatordata.data.TradePriceItemDto;
import org.noear.snack.ONode;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

@Component
@TradePriceJsonPathFactoryAnnotation(value = "calculatePayCurrencyFactory")
public class CalculatePayCurrencyFactory extends AbstractOrderPriceRenderFactory {

    @Override
    public void handleResult(SimulatorJsonPathResponseDto result, TradePriceJsonPathDto tradePriceJsonPathDto, ONode target, ONode selectResult) {

        //因为ic无法获取币种，报价币种在请求币种强切的时候，会出现，所以这个节点，用来回刷报价币种
        JSONArray response = JSON.parseArray(selectResult.toJson());
        String paramsPath = tradePriceJsonPathDto.getExt().get("params");
        ONode params = target.select(paramsPath);
        if (params.isNull()) {
            return;
        }
        String quoteCurrency = response.getString(0);
        String paramString = params.toJson();
        JSONArray paramArrays = JSON.parseArray(paramString);
        Map<String, TradePriceItemDto> map = result.getPriceItems();
        Map<String, TradePriceItemDto> idMaps = new HashMap<>();
        for (TradePriceItemDto tradePriceItemDto : map.values()) {
            idMaps.put(tradePriceItemDto.getId(), tradePriceItemDto);
        }
        handleCurrency(idMaps, quoteCurrency, paramArrays);


    }

    /**
     * 处理币种，币种回刷
     * @param idMaps
     * @param quoteCurrency
     * @param paramArrays
     */
    private void handleCurrency(Map<String, TradePriceItemDto> idMaps, String quoteCurrency, JSONArray paramArrays) {
        for (int i = 0; i < paramArrays.size(); i++) {
            JSONObject temp = paramArrays.getJSONObject(i);
            String outId = temp.getString("outId");
            String orderCcy = temp.getString("orderCcy");
            TradePriceItemDto priceItemDto = idMaps.get(outId);
            priceItemDto.setPrice(Money.of(priceItemDto.getPrice().toString().replace("USD", orderCcy)));
            priceItemDto.setBaseCurrency(orderCcy);
            priceItemDto.setQuoteCurrency(quoteCurrency);
        }
    }
}
