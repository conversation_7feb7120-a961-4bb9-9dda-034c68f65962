package com.aliexpress.databank.price.context;

import com.alibaba.fastjson.JSONObject;
import com.aliexpress.databank.price.constant.TradePriceConfigTypeEnum;
import com.aliexpress.databank.price.dto.common.TradePriceJsonPathDto;
import com.aliexpress.databank.price.dto.common.simulatordata.SimulatorJsonPathResponseDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.noear.snack.ONode;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TradePriceRenderContext {

    /**
     * 存储jsonPath后的结果
     */
    private SimulatorJsonPathResponseDto body;

    /**
     * 天启原始数据
     */
    private ONode simulatorNode;

    /**
     * body的json格式
     */
    private ONode bodyNode;

    /**
     * 暂时用json
     */
    private JSONObject result;

    /**
     * jsonpath的配置
     */
    private List<TradePriceJsonPathDto> priceJsonPathDtos;

    /**
     * 类型枚举
     */
    private TradePriceConfigTypeEnum tradePriceConfigTypeEnum;


}
