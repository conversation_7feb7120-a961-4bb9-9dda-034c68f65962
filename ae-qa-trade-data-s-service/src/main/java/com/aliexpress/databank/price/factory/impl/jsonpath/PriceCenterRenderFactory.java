package com.aliexpress.databank.price.factory.impl.jsonpath;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.global.money.Money;
import com.aliexpress.databank.price.annotation.TradePriceJsonPathFactoryAnnotation;
import com.aliexpress.databank.price.dto.common.TradePriceJsonPathDto;
import com.aliexpress.databank.price.dto.common.simulatordata.SimulatorJsonPathResponseDto;
import com.aliexpress.databank.price.dto.common.simulatordata.data.PriceCenterInfoDto;
import com.aliexpress.databank.price.dto.common.simulatordata.data.ProductPriceDto;
import com.aliexpress.databank.price.dto.common.simulatordata.data.TradePriceItemDto;
import org.apache.commons.collections4.CollectionUtils;
import org.noear.snack.ONode;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.money.MonetaryAmount;
import java.math.BigDecimal;


@Component
@TradePriceJsonPathFactoryAnnotation(value = "priceCenterRenderFactory")
public class PriceCenterRenderFactory extends AbstractOrderPriceRenderFactory {
    @Override
    public void handleResult(SimulatorJsonPathResponseDto result, TradePriceJsonPathDto tradePriceJsonPathDto, ONode target, ONode selectResult) {
        //转化成jsonObject
        if (selectResult.isNull()) {
            return;
        }
        String json = selectResult.toJson();
        JSONArray jsonArray = JSONArray.parseArray(json);

        if (CollectionUtils.isEmpty(jsonArray)) {
            return;
        }
        //解析参数，获取金额
        boolean isDp = false;
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject sku = jsonArray.getJSONObject(i);

            for (String productId : sku.keySet()) {
                JSONObject productObject = sku.getJSONObject(productId);
                JSONObject skuIdAndSkuPriceQueryResultMap = productObject.getJSONObject("skuIdAndSkuPriceQueryResultMap");
                for (String key : skuIdAndSkuPriceQueryResultMap.keySet()) {
                    String skuId = key;
                    TradePriceItemDto tradePriceItemDto = result.getPriceItems().get(skuId);

                    JSONObject skuIdAndSkuPriceQueryResult = skuIdAndSkuPriceQueryResultMap.getJSONObject(key);
                    boolean isNewPrice = checkNewPrice(skuIdAndSkuPriceQueryResult.getJSONObject("featureMap"));
                    String isIncTax = checkIncTax(skuIdAndSkuPriceQueryResult.getJSONObject("featureMap"));

                    PriceCenterInfoDto priceCenterInfoDto = new PriceCenterInfoDto();
                    priceCenterInfoDto.setNewPrice(isNewPrice);
                    priceCenterInfoDto.setIsIncTax(isIncTax);

                    //解析原价
                    JSONObject productSkuRegionPrice = skuIdAndSkuPriceQueryResult.getJSONObject("productSkuRegionPrice");
                    boolean dpResult = handlePriceCenterResponse(productSkuRegionPrice, priceCenterInfoDto, tradePriceItemDto);
                    isDp = isDp || dpResult;
                    tradePriceItemDto.setPriceCenterInfoDto(priceCenterInfoDto);

                }
            }
            //根据sku获取商品结构体

        }
        //需要做一次数据更新，只要其中一个是量价，则全部更新为量价数据,并且回刷一下价格明细中的原价
        for (TradePriceItemDto tradePriceItemDto : result.getPriceItems().values()) {
            PriceCenterInfoDto priceCenterInfoDto = tradePriceItemDto.getPriceCenterInfoDto();
            if (isDp && !priceCenterInfoDto.isDp()) {
                //回刷数据
                BeanUtils.copyProperties(priceCenterInfoDto.getOriginPrice(), priceCenterInfoDto.getDynamicPrice());
            }
            if (priceCenterInfoDto != null) {
                tradePriceItemDto.setPrice(priceCenterInfoDto.getPrice());
            }

        }

    }


    /**
     * 校验是否是量价
     *
     * @return
     */
    private boolean checkDp(JSONObject priceSemanticTags) {
        if (priceSemanticTags == null || !priceSemanticTags.containsKey("dp")) {
            return false;
        }
        return priceSemanticTags.getBoolean("dp");

    }

    /**
     * 校验是否是新价格体系
     *
     * @return
     */
    private boolean checkNewPrice(JSONObject priceSemanticTags) {

        if (priceSemanticTags == null || !priceSemanticTags.containsKey("npp")) {
            return false;
        }
        return priceSemanticTags.getBoolean("npp");

    }

    /**
     * 校验是否含税价
     *
     * @return
     */
    private String checkIncTax(JSONObject priceSemanticTags) {

        if (priceSemanticTags == null || !priceSemanticTags.containsKey("inc_tax")) {
            return null;
        }
        return priceSemanticTags.getString("inc_tax");

    }


    /**
     * 返回是否量价结果
     *
     * @param productSkuRegionPrice
     * @param priceCenterInfoDto
     * @return
     */
    private boolean handlePriceCenterResponse(JSONObject productSkuRegionPrice, PriceCenterInfoDto priceCenterInfoDto, TradePriceItemDto tradePriceItemDto) {

        String baseCurrency = tradePriceItemDto.getBaseCurrency();
        //原价
        JSONObject originPrice = productSkuRegionPrice.getJSONObject("originalSuppliedPriceSummary");

        //售价
        JSONObject salePrice = productSkuRegionPrice.getJSONObject("promotionSuppliedPriceSummary");
        JSONObject featureMap = productSkuRegionPrice.getJSONObject("featureMap");
        boolean isDp = checkDp(featureMap);
        priceCenterInfoDto.setDp(isDp);
        //物流成本

        //量价物流成本

        //平台支持价，如果是量价场景，则为量价，否则和原价一样，所以先解析这个，然后在解析原价
        ProductPriceDto origin = new ProductPriceDto();
        ProductPriceDto dynamic = new ProductPriceDto();
        if (isDp) {
            //解析量价数据
            JSONObject jsonObject = originPrice.getJSONObject("platformSuppliedPrice");
            //原始金额
            //正常量价数据不可能没有的
            //如果没有，则不行
            //
            dynamic = handlePriceCenter(jsonObject, tradePriceItemDto, false, baseCurrency);
            if (priceCenterInfoDto.isNewPrice()) {
                ProductPriceDto salePriceTemp = handlePriceCenter(salePrice.getJSONObject("platformSuppliedPrice"), tradePriceItemDto, true, baseCurrency);
                dynamic.setSalePrice(salePriceTemp.getSalePrice());
                dynamic.setSalePriceTax(salePriceTemp.getSalePriceTax());
            }

        } else {

            JSONObject jsonObject = originPrice.getJSONObject("merchantSuppliedPrice");
            origin = handlePriceCenter(jsonObject, tradePriceItemDto, false, baseCurrency);
            if (priceCenterInfoDto.isNewPrice()) {
                ProductPriceDto salePriceTemp = handlePriceCenter(salePrice.getJSONObject("merchantSuppliedPrice"), tradePriceItemDto, true, baseCurrency);
                origin.setSalePrice(salePriceTemp.getSalePrice());
                origin.setSalePriceTax(salePriceTemp.getSalePriceTax());
            }
        }
        priceCenterInfoDto.setDynamicPrice(dynamic);
        priceCenterInfoDto.setOriginPrice(origin);


        //如果是新价格体系，解析售价相关数据
        return isDp;

    }


    private MonetaryAmount parseOriginPrice(JSONObject productPrice, TradePriceItemDto tradePriceItemDto, String baseCurrency) {
        //这个获取的是ic的原价，有可能是空的，如果是空的,按照ic获取的金额为准
        JSONObject jsonObject = productPrice.getJSONObject("quotePrice");
        if (jsonObject.containsKey("amount") && jsonObject.get("amount") != null) {
            return Money.of(jsonObject.getJSONObject("amount").getBigDecimal("number"), baseCurrency);
        }
        return tradePriceItemDto.getPrice();

    }

    private MonetaryAmount parseTaxPrice(JSONObject productPrice, TradePriceItemDto tradePriceItemDto, String baseCurrency) {
        //这个获取的是汇率，一般不会为空，为空，不含税
        if (productPrice == null) {
            return null;
        }
        JSONObject jsonObject = productPrice.getJSONObject("taxFeeQuotePrice");
        if (jsonObject==null||!jsonObject.containsKey("amount") || jsonObject.get("amount") == null) {
            return null;
        }
        //获取税率
        tradePriceItemDto.setTaxRate(jsonObject.getBigDecimal("rate"));
        return Money.of(jsonObject.getJSONObject("amount").getBigDecimal("number"), baseCurrency);

    }


    private MonetaryAmount parsePostCostPrice(JSONObject postCostPrice, TradePriceItemDto tradePriceItemDto, String baseCurrency) {
        //这个获取的是ic的原价，有可能是空的，如果是空的,按照ic获取的金额为准
        if (postCostPrice == null) {
            return null;
        }
        JSONObject jsonObject = postCostPrice.getJSONObject("quotePrice");
        if (jsonObject.containsKey("amount") && jsonObject.get("amount") != null) {
            return Money.of(jsonObject.getJSONObject("amount").getBigDecimal("number"), baseCurrency);
        }
        return null;

    }

    /**
     * 处理价格中心数据
     *
     * @param jsonObject
     * @param tradePriceItemDto
     * @return
     */
    private ProductPriceDto handlePriceCenter(JSONObject jsonObject, TradePriceItemDto tradePriceItemDto, boolean isSalePrice, String baseCurrency) {
        ProductPriceDto productPriceDto = new ProductPriceDto();
        //原始金额
        //正常量价数据不可能没有的
        //如果没有，则不行
        JSONObject productPrice = jsonObject.getJSONObject("productPrice");
        //解析原价
        MonetaryAmount originPriceFee = parseOriginPrice(productPrice, tradePriceItemDto, baseCurrency);
        productPriceDto.setIcPrice(originPriceFee);
        //解析税费
        MonetaryAmount taxFee = parseTaxPrice(productPrice, tradePriceItemDto, baseCurrency);
        //解析物流成本

        JSONObject postageCostPrice = jsonObject.getJSONObject("postageCostPrice");

        MonetaryAmount postCost = parsePostCostPrice(postageCostPrice, tradePriceItemDto, baseCurrency);
        //解析物流成本税
        MonetaryAmount postCostTaxFee = parseTaxPrice(postageCostPrice, tradePriceItemDto, baseCurrency);
        //获取真实原价
        if (isSalePrice) {
            //处理的就是售价数据
            handleSalePrice(taxFee, originPriceFee, productPriceDto);
            return productPriceDto;
        }
        handleOriginPrice(taxFee, postCost, postCostTaxFee, originPriceFee, productPriceDto);
        return productPriceDto;
    }

    /**
     * 售价赋值
     *
     * @param taxFee
     * @param originPriceFee
     * @param productPriceDto
     */
    private void handleSalePrice(MonetaryAmount taxFee, MonetaryAmount originPriceFee, ProductPriceDto productPriceDto) {
        if (taxFee != null) {
            productPriceDto.setSalePriceTax(taxFee);
            originPriceFee = originPriceFee.add(taxFee);
        }

        productPriceDto.setSalePrice(originPriceFee);
    }


    /**
     * 原价赋值
     *
     * @param taxFee
     * @param postCost
     * @param postCostTaxFee
     * @param originPriceFee
     * @param productPriceDto
     */
    private void handleOriginPrice(MonetaryAmount taxFee, MonetaryAmount postCost, MonetaryAmount postCostTaxFee, MonetaryAmount originPriceFee, ProductPriceDto productPriceDto) {
        if (taxFee != null) {
            productPriceDto.setOriginPriceTax(taxFee);
            originPriceFee = originPriceFee.add(taxFee);
        }
        if (postCost != null) {
            productPriceDto.setPostCost(postCost);
            originPriceFee = originPriceFee.add(postCost);
        }
        if (postCostTaxFee != null) {
            productPriceDto.setPostCostTax(postCostTaxFee);
            originPriceFee = originPriceFee.add(postCostTaxFee);
        }
        productPriceDto.setOriginPrice(originPriceFee);
    }
}
