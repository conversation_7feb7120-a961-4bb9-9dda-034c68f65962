package com.aliexpress.databank.price.dto.common.simulatordata.data;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.money.MonetaryAmount;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ProductPriceDto {
    /**
     * 原价
     */
    private MonetaryAmount originPrice;

    /**
     * 售价
     */
    private MonetaryAmount salePrice;

    /**
     * 物流成本
     */
    private MonetaryAmount postCost;

    /**
     * 原价税金
     */
    private MonetaryAmount originPriceTax;

    /**
     * 售价税金
     */
    private MonetaryAmount salePriceTax;

    /**
     * 物流成本税
     */
    private MonetaryAmount postCostTax;

    /**
     * 记录ic原始金额
     */
    private MonetaryAmount icPrice;
}
