package com.aliexpress.databank.price.dto.common;

import com.aliexpress.databank.price.constant.TradePriceConfigTypeEnum;
import lombok.Setter;
import org.apache.commons.collections4.MapUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class TradePriceForJsonPathConfigDto {

    @Setter
    private Map<String, List<TradePriceJsonPathDto>> tradePriceJsonPathDtoList;

    public List<TradePriceJsonPathDto> getConfigByEnum(TradePriceConfigTypeEnum tradePriceConfigTypeEnum) {
        if (MapUtils.isEmpty(tradePriceJsonPathDtoList)) {
            return new ArrayList<>();
        }
        return tradePriceJsonPathDtoList.get(tradePriceConfigTypeEnum.name());
    }
}
