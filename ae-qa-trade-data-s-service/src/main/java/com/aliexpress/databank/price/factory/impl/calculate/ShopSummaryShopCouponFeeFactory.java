package com.aliexpress.databank.price.factory.impl.calculate;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.global.money.Money;
import com.aliexpress.databank.price.dto.common.render.common.PriceCalculateDto;
import com.aliexpress.databank.price.dto.common.simulatordata.data.PromotionInfoDto;
import com.aliexpress.databank.price.dto.common.simulatordata.data.TradePriceItemDto;
import com.aliexpress.databank.price.factory.TradePriceSingleCalculateFactory;
import org.apache.commons.collections4.CollectionUtils;

import javax.money.MonetaryAmount;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class ShopSummaryShopCouponFeeFactory implements TradePriceSingleCalculateFactory {

    @Override
    public PriceCalculateDto calculate(List<TradePriceItemDto> tradePriceItemDtos) throws Exception {
        PriceCalculateDto priceCalculateDto = new PriceCalculateDto();
        priceCalculateDto.setFormulaOfCalculate("sum(shopCouponFee)");

        TradePriceItemDto temp = tradePriceItemDtos.get(0);
        MonetaryAmount money = Money.zero(temp.getQuoteCurrency());

        List<List<PromotionInfoDto>> details = new ArrayList<>();
        for (TradePriceItemDto tradePriceItemDto : tradePriceItemDtos) {
            List<PromotionInfoDto> list = tradePriceItemDto.getItemPromotions().stream().filter(promotionInfoDto -> "shopCoupon".equalsIgnoreCase(promotionInfoDto.getToolCode())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(list)) {
                continue;
            }
            for (PromotionInfoDto promotionInfoDto : list) {
                money = money.add(promotionInfoDto.getDiscountFeeForQuote());
            }
            details.add(list);
        }
        if (money.isZero()) {
            return null;
        }

        JSONObject result = new JSONObject();
        result.put("shopCouponFee", details);

        priceCalculateDto.setPrice(money.toString());
        priceCalculateDto.setDetail(result);
        return priceCalculateDto;


    }
}
