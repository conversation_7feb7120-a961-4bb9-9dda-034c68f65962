package com.aliexpress.databank.price;

import com.alibaba.global.qa.dc.model.param.SystemDTO;
import com.alibaba.global.qa.dc.model.result.ResultDTO;

import java.math.BigDecimal;

public interface TradeOrderAdjustViewService {

    /**
     * 获取调价明细
     * @params tradeOrderid
     * @params adjustPrice --调整后的价格
     * @return
     */
    Object getTradeOrderAdjustDetail(Long tradeOrder, BigDecimal adjustPrice);

    ResultDTO adjustPriceByOrderId (String params, SystemDTO systemDTO) throws Exception;


}
