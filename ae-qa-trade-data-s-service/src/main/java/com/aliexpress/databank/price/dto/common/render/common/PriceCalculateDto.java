package com.aliexpress.databank.price.dto.common.render.common;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.money.MonetaryAmount;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PriceCalculateDto {
    /**
     * 公式计算的金额
     */
    private String price;
    
    /**
     * 金额计算公式
     */
    private String formulaOfCalculate;

    /**
     * 金额计算详情（参与计算的明细)
     */
    @JSONField(serialize = false)
    private JSONObject detail;
}
