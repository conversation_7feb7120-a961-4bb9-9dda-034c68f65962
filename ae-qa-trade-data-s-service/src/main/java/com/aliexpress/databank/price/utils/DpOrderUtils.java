package com.aliexpress.databank.price.utils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.global.money.Money;
import com.alibaba.global.order.management.api.model.PromotionFeeInfoDTO;
import com.alibaba.global.order.management.api.model.TradeOrderLineDTO;

import javax.money.MonetaryAmount;
import java.util.ArrayList;
import java.util.List;

public class DpOrderUtils {


    /**
     * 计算量价订单 的单价
     * @param tradeOrderLineDTO
     * @return
     */
    public static MonetaryAmount getOrderLineUnitFee(TradeOrderLineDTO tradeOrderLineDTO) {

        JSONObject jsonObject=getDpPriceObject(tradeOrderLineDTO);
        if(jsonObject==null){
            return null;
        }
        JSONObject discountFee = jsonObject.getJSONObject("discountFee");
        Money originalFee = PriceUtils.convertMoney(jsonObject, "originalFee",false);
        return originalFee.subtract(PriceUtils.convertMoney(discountFee, "itemSingleDiscountFee",false));
    }

    /**
     * 获取量价订单优惠信息  --使用前确保量价订单
     * @param tradeOrderLineDTO
     */
    public static void getOrderLineSaleDiscountFee(TradeOrderLineDTO tradeOrderLineDTO) {
        JSONObject jsonObject=getDpPriceObject(tradeOrderLineDTO);
        if(jsonObject==null){
            return;
        }
        JSONArray promotionFeeDetails = jsonObject.getJSONArray("promotionFeeDetails");
        if (promotionFeeDetails == null || promotionFeeDetails.size() <= 0) {
            //必须清空
            tradeOrderLineDTO.setOriginalSaleDiscountInfo(new ArrayList<>());
            tradeOrderLineDTO.setSaleDiscountInfo(new ArrayList<>());
            tradeOrderLineDTO.setOriginalShippingDiscountInfo(new ArrayList<>());
            tradeOrderLineDTO.setShippingDiscountInfo(new ArrayList<>());
            return;
        }
        tradeOrderLineDTO.setOriginalSaleDiscountInfo(getOriginPromotionFeeInfoDTOForSale(jsonObject));
        tradeOrderLineDTO.setOriginalShippingDiscountInfo(getOriginPromotionFeeInfoDTOForShipping(jsonObject));
        //tradeOrderLineDTO.setSaleDiscountInfo(getPayPromotionFeeInfoDTOForSale(jsonObject));
        //tradeOrderLineDTO.setOriginalSaleDiscountInfo(getPayPromotionFeeInfoDTOForShipping(jsonObject));

    }

    /**
     * 获取报价币种的商品优惠
     * @param jsonObject
     * @return
     */
    public static List<PromotionFeeInfoDTO> getOriginPromotionFeeInfoDTOForSale(JSONObject jsonObject){
        JSONArray promotionFeeDetails = jsonObject.getJSONArray("promotionFeeDetails");
        if(promotionFeeDetails==null||promotionFeeDetails.size()<=0){
            return new ArrayList<>();
        }
        List<PromotionFeeInfoDTO> originalPromotionFeeInfoDTOs=new ArrayList<>();
        for (int i = 0; i < promotionFeeDetails.size(); i++) {
            JSONObject promotionFeeDetail=promotionFeeDetails.getJSONObject(i);
            PromotionFeeInfoDTO promotionFeeInfoDTO=convertPromotionFee(promotionFeeDetail);
            int type=promotionFeeDetail.getInteger("type");
            if(type!=1){
                originalPromotionFeeInfoDTOs.add(promotionFeeInfoDTO);
            }
        }
        return originalPromotionFeeInfoDTOs;
    }


    /**
     * 获取报价币种的运费优惠
     * @param jsonObject
     * @return
     */
    public static List<PromotionFeeInfoDTO> getOriginPromotionFeeInfoDTOForShipping(JSONObject jsonObject){
        JSONArray promotionFeeDetails = jsonObject.getJSONArray("promotionFeeDetails");
        if(promotionFeeDetails==null||promotionFeeDetails.size()<=0){
            return new ArrayList<>();
        }
        List<PromotionFeeInfoDTO> originalPromotionFeeInfoDTOs=new ArrayList<>();
        for (int i = 0; i < promotionFeeDetails.size(); i++) {
            JSONObject promotionFeeDetail=promotionFeeDetails.getJSONObject(i);
            PromotionFeeInfoDTO promotionFeeInfoDTO=convertPromotionFee(promotionFeeDetail);
            int type=promotionFeeDetail.getInteger("type");
            if(type==1){
                originalPromotionFeeInfoDTOs.add(promotionFeeInfoDTO);
            }
        }
        return originalPromotionFeeInfoDTOs;
    }


    /**
     * 获取支付币种的商品优惠
     * @param jsonObject
     * @return
     */
    public static List<PromotionFeeInfoDTO> getPayPromotionFeeInfoDTOForSale(JSONObject jsonObject){
        JSONArray promotionFeeDetails = jsonObject.getJSONArray("payCurrDiscountFee");
        if(promotionFeeDetails==null||promotionFeeDetails.size()<=0){
            return new ArrayList<>();
        }
        List<PromotionFeeInfoDTO> originalPromotionFeeInfoDTOs=new ArrayList<>();
        for (int i = 0; i < promotionFeeDetails.size(); i++) {
            JSONObject promotionFeeDetail=promotionFeeDetails.getJSONObject(i);
            PromotionFeeInfoDTO promotionFeeInfoDTO=convertPromotionFee(promotionFeeDetail);
            int type=promotionFeeDetail.getInteger("type");
            if(type!=1){
                originalPromotionFeeInfoDTOs.add(promotionFeeInfoDTO);
            }
        }
        return originalPromotionFeeInfoDTOs;
    }


    /**
     * 获取支付币种的运费优惠
     * @param jsonObject
     * @return
     */
    public static List<PromotionFeeInfoDTO> getPayPromotionFeeInfoDTOForShipping(JSONObject jsonObject){
        JSONArray promotionFeeDetails = jsonObject.getJSONArray("payCurrDiscountFee");
        if(promotionFeeDetails==null||promotionFeeDetails.size()<=0){
            return new ArrayList<>();
        }
        List<PromotionFeeInfoDTO> originalPromotionFeeInfoDTOs=new ArrayList<>();
        for (int i = 0; i < promotionFeeDetails.size(); i++) {
            JSONObject promotionFeeDetail=promotionFeeDetails.getJSONObject(i);
            PromotionFeeInfoDTO promotionFeeInfoDTO=convertPromotionFee(promotionFeeDetail);
            int type=promotionFeeDetail.getInteger("type");
            if(type==1){
                originalPromotionFeeInfoDTOs.add(promotionFeeInfoDTO);
            }
        }
        return originalPromotionFeeInfoDTOs;
    }

    private static PromotionFeeInfoDTO convertPromotionFee(JSONObject promotionFeeDetail){
        PromotionFeeInfoDTO promotionFeeInfoDTO=new PromotionFeeInfoDTO();

        promotionFeeInfoDTO.setNonGoldStandardDiscountFee(PriceUtils.convertMoney(promotionFeeDetail,"nonGoldStandardDiscountFee",true));
        promotionFeeInfoDTO.setGoldStandardDiscountFee(PriceUtils.convertMoney(promotionFeeDetail,"goldStandardDiscountFee",true));
        promotionFeeInfoDTO.setDiscountFee(PriceUtils.convertMoney(promotionFeeDetail,"discountFee",true));
        promotionFeeInfoDTO.setPromotionId(promotionFeeDetail.getString("promotionId"));
        promotionFeeInfoDTO.setDoSplit(true);
        return promotionFeeInfoDTO;
    }

    /**
     * 获取量价金额相关
     * @param tradeOrderLineDTO
     * @return
     */
    private static JSONObject getDpPriceObject(TradeOrderLineDTO tradeOrderLineDTO){
        //判断是否是量价
        if(!"1".equalsIgnoreCase(tradeOrderLineDTO.getFeatures().getFeature("d_p"))){
            return null;
        }
        JSONObject jsonObject=JSONObject.parseObject(tradeOrderLineDTO.getFeatures().getFeature("_dynamic_price"));

        return jsonObject;
    }
}
