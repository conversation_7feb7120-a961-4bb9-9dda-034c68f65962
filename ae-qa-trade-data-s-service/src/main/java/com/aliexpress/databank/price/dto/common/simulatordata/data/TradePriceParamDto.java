package com.aliexpress.databank.price.dto.common.simulatordata.data;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TradePriceParamDto {

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 商品id
     */
    private String itemId;

    /**
     * 购物车id
     */
    private String cartId;

    /**
     * skuId
     */
    private String skuId;
}
