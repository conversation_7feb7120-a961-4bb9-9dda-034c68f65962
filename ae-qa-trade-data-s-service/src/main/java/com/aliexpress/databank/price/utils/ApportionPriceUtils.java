package com.aliexpress.databank.price.utils;

import com.alibaba.global.money.Money;
import com.alibaba.global.order.management.api.model.TradeOrderDTO;
import com.alibaba.global.order.management.api.model.TradeOrderLineDTO;
import com.alibaba.global.order.management.constants.AttributeConstants;
import com.alibaba.global.payment.calculator.domain.FundCalculateService;
import com.alibaba.global.payment.calculator.domain.request.CalculateRequest;
import com.alibaba.global.payment.calculator.domain.response.CalculateResponse;
import com.alibaba.global.payment.calculator.domain.vo.CalculateRequestMainItem;
import com.alibaba.global.payment.calculator.domain.vo.CalculateRequestSplitItem;
import com.aliexpress.databank.price.constant.PriceApportionStrategy;
import com.aliexpress.databank.price.dto.common.PriceShareWeight;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;

import javax.money.MonetaryAmount;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

public class ApportionPriceUtils {


    /**
     * 主订单维度的分摊金额
     * @return
     */
    public static void apportionOrderPrice(TradeOrderDTO tradeOrderDTO, MonetaryAmount adjustFee){
        List<PriceShareWeight> factors=tradeOrderDTO.getOrderLines().stream()
                .map(orderLine -> PriceShareWeight.builder()
                        .identity(orderLine.getTradeOrderLineId().toString())
                        .fee(apportionFeeCalculate(orderLine,orderLine.getExchangeInfo().getBaseCurrency()))
                        .build())
                .collect(Collectors.toList());
        convertCalculateResponse(tradeOrderDTO,adjustPrice(factors,PriceApportionStrategy.WEIGHT,adjustFee));
    }


    /**
     * 子订单维度的分摊金额
     * @return
     */
    public static void apportionOrderLinePrice(TradeOrderLineDTO tradeOrderLine){
        MonetaryAmount adjustFee=tradeOrderLine.getAdjustFee() == null ? Money
                .zero(tradeOrderLine.getExchangeInfo().getBaseCurrency())
                : tradeOrderLine.getAdjustFee();

        List<PriceShareWeight> factors = Lists.newArrayList();
        String baseCurrency=tradeOrderLine.getExchangeInfo().getBaseCurrency();
        MonetaryAmount itemAmount = tradeOrderLine.getUnitFee().multiply(tradeOrderLine.getQuantity());
        if (PriceUtils.getNoGoldStandardFee(tradeOrderLine,tradeOrderLine.getExchangeInfo().getBaseCurrency())!=null) {
            itemAmount = itemAmount.subtract(
                    PriceUtils.getNoGoldStandardFee(tradeOrderLine,baseCurrency));
        }
        MonetaryAmount min_item_share = itemAmount.isZero() ? Money.zero(itemAmount.getCurrency())
                : tradeOrderLine.getActualFee().add(tradeOrderLine.getAdjustFee())
                .isZero() ? Money.zero(itemAmount.getCurrency()).subtract(itemAmount) : Money.of(0.01,
                itemAmount.getCurrency()).subtract(itemAmount);
        PriceShareWeight factor_item = PriceShareWeight.builder().identity("item").fee(itemAmount).minShare(
                min_item_share).priority(0).build();
        factors.add(factor_item);

        MonetaryAmount shippingAmount = tradeOrderLine.getShippingFee();
        if (shippingAmount == null) {
            shippingAmount = Money.zero(baseCurrency);
        }
        if (PriceUtils.getShippingNoGoldStandardFee(tradeOrderLine,baseCurrency)!=null) {
            shippingAmount = shippingAmount.subtract(
                    PriceUtils.getShippingNoGoldStandardFee(tradeOrderLine,baseCurrency));
        }
        MonetaryAmount min_shipping_share = Money.zero(itemAmount.getCurrency()).subtract(shippingAmount);
        PriceShareWeight factor_shipping = PriceShareWeight.builder().identity("shipping").fee(shippingAmount).minShare(
                min_shipping_share).priority(1).build();
        factors.add(factor_shipping);

        adjustApportionFeeShare(factors,adjustFee);
        CalculateResponse calculateResponse= adjustPrice(factors,PriceApportionStrategy.WEIGHT,adjustFee);

        calculateResponse.getCalculateResponseList().forEach(p->{
            p.getMainCalculateResult().forEach(q->{
                if(p.getMainNo().equalsIgnoreCase("item")){
                    tradeOrderLine.getFeatures().addFeature("gaf",q.getAmount().toString());
                }
                if(p.getMainNo().equalsIgnoreCase("shipping")){
                    tradeOrderLine.getFeatures().addFeature("paf",q.getAmount().toString());
                }
            });
        });
    }




    private static CalculateResponse adjustPrice(List<PriceShareWeight> factors, PriceApportionStrategy priceApportionStrategy, MonetaryAmount adjustFee){


        CalculateRequest calculateRequest = new CalculateRequest();

        //子订单维度的拆封
        List<CalculateRequestSplitItem> requestSplitItemList = new ArrayList<>();
        CalculateRequestSplitItem calculateRequestSplitItem = new CalculateRequestSplitItem(priceApportionStrategy.name(), Money.of(adjustFee));
        requestSplitItemList.add(calculateRequestSplitItem);

        List<CalculateRequestMainItem> mainItemList = factors.stream().map(p->{
            CalculateRequestMainItem calculateRequestMainItem = new CalculateRequestMainItem();
            calculateRequestMainItem.setMainNo(p.getIdentity());
            calculateRequestMainItem.setMainAmount(Money.of(p.getFee()));
            calculateRequestMainItem.setMaxAdjustAmount(Money.of(p.getMinShare()));
            calculateRequestMainItem.setPriority(p.getPriority());
            return calculateRequestMainItem;
        }).collect(Collectors.toList());

        calculateRequest.setMainItemList(mainItemList);
        calculateRequest.setRequestSplitItemList(requestSplitItemList);

        boolean adjustLimitSwitch = mainItemList.stream().anyMatch(p->p.getPriority()!=null);
        calculateRequest.setAdjustLimitSwitch(adjustLimitSwitch);

        CalculateResponse calculateResponse = FundCalculateService.calculateAmount(calculateRequest);
        return calculateResponse;
    }


    /**
     * 子订单的调价金额计算
     * @param orderLine
     * @param currcyCode
     * @return
     */
    private static MonetaryAmount apportionFeeCalculate(TradeOrderLineDTO orderLine, String currcyCode) {
        MonetaryAmount apportionFee = orderLine.getActualFee();
        if (orderLine.getAdjustFee() != null) {
            apportionFee = apportionFee.subtract(orderLine.getAdjustFee());
        }
        if (orderLine.getTaxActualFee() != null) {
            apportionFee = apportionFee.subtract(orderLine.getTaxActualFee());
        }

        String ddpFeature = orderLine.getFeatures().getFeature(AttributeConstants.ATTR_CB_IMPORT_TAX);
        if(StringUtils.isNotBlank(ddpFeature)){
            Money ddpAmount = Money.of(ddpFeature);
            if(ddpAmount != null){
                apportionFee = apportionFee.subtract(ddpAmount);
            }
        }

        return apportionFee;
    }


    /**
     * 子订单各项分摊
     * @param factors
     * @param apportionedFee
     */
    private static void adjustApportionFeeShare(List<PriceShareWeight> factors,MonetaryAmount apportionedFee) {
        Optional<MonetaryAmount> apportionedFeeShare = factors.stream().map(p -> p.getMinShare())
                .reduce(
                        MonetaryAmount::add);
        if (!apportionedFee.equals(apportionedFeeShare.get())) {
            MonetaryAmount restShareFee = apportionedFee.subtract(apportionedFeeShare.get());
            if (restShareFee.isNegative()) {
                for (PriceShareWeight factor : factors) {
                    if (!factor.getFee().isZero()) {
                        MonetaryAmount minRestShareFee = factor.getMinShare().add(factor.getFee());
                        factor.setMinShare(factor.getMinShare().subtract(minRestShareFee));
                        restShareFee = restShareFee.add(minRestShareFee);
                        if (restShareFee.isZero()) {
                            break;
                        }
                    }
                }
            }
        }
    }

    /**
     * 主订单维度的分摊
     * @param tradeOrder
     * @param calculateResponse
     */
    private static void convertCalculateResponse(TradeOrderDTO tradeOrder,CalculateResponse calculateResponse){

        if(!calculateResponse.isSucceed()) {
            return;
        }
        calculateResponse.getCalculateResponseList().forEach(p->{

            p.getMainCalculateResult().forEach(q->{
                //找到主订单对应的子订单
                Optional<TradeOrderLineDTO> optional= tradeOrder.getOrderLines().stream().filter(orderLineDTO-> p.getMainNo().equals(orderLineDTO.getTradeOrderLineId().toString())).findFirst();
                if(optional.isPresent()){
                    optional.get().setAdjustFee(q.getAmount());
                    apportionOrderLinePrice(optional.get());
                }
            });
        });
    }
}
