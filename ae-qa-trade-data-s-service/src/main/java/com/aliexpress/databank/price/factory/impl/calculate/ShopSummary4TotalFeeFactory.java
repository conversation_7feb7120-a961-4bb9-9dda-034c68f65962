package com.aliexpress.databank.price.factory.impl.calculate;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.global.money.Money;
import com.aliexpress.databank.price.dto.common.render.common.PriceCalculateDto;
import com.aliexpress.databank.price.dto.common.simulatordata.data.PromotionInfoDto;
import com.aliexpress.databank.price.dto.common.simulatordata.data.TaxDetailDto;
import com.aliexpress.databank.price.dto.common.simulatordata.data.TradePriceItemDto;
import com.aliexpress.databank.price.factory.TradePriceSingleCalculateFactory;
import com.aliexpress.databank.utils.FundCalculator;
import org.apache.commons.collections4.CollectionUtils;

import javax.money.MonetaryAmount;
import java.util.ArrayList;
import java.util.List;

public class ShopSummary4TotalFeeFactory implements TradePriceSingleCalculateFactory {
    @Override
    public PriceCalculateDto calculate(List<TradePriceItemDto> tradePriceItemDtos) throws Exception {

        PriceCalculateDto priceCalculateDto = new PriceCalculateDto();
        priceCalculateDto.setFormulaOfCalculate("sum((icPrice*(1+3cRate)-itemSingleDiscountFee/quality)*quality+taxFee+baseShippingFee-shippingDiscountFee-itemDiscountFee+goldDiscountFees)*rate-goldDiscountFees*rate+itemGoldDiscountFees*rate+coinDiscountFee");

        List<JSONObject> details = new ArrayList<>();

        JSONObject detailTemp = new JSONObject();
        TradePriceItemDto temp = tradePriceItemDtos.get(0);
        MonetaryAmount money = Money.zero(temp.getBaseCurrency());
        for (TradePriceItemDto tradePriceItemDto : tradePriceItemDtos) {
            JSONObject detail = new JSONObject();
            details.add(detail);
            detail.put("icPrice", tradePriceItemDto.getPrice().toString());
            detail.put("3cRate", tradePriceItemDto.getRate());
            detail.put("quality", tradePriceItemDto.getQuality());
            MonetaryAmount price = tradePriceItemDto.getPrice().multiply(1 + (tradePriceItemDto.getRate() == null ? 0 : tradePriceItemDto.getRate().doubleValue()));
            List<PromotionInfoDto> list = tradePriceItemDto.getItemSinglePromotions();
            if (CollectionUtils.isNotEmpty(list)) {
                for (PromotionInfoDto promotionInfoDto : list) {
                    //单品优惠营销返回是全部金额，所以需要单独除数量
                    price = price.subtract(promotionInfoDto.getDiscountFeeForBase().divide(tradePriceItemDto.getQuality()));
                }
                detail.put("itemSingleDiscountFee", list);
            }
            money = money.add(price.multiply(tradePriceItemDto.getQuality()));
            money = money.add(tradePriceItemDto.getShippingFeeDto().getShippingBaseFee());
            List<PromotionInfoDto> shippingDiscountFee = tradePriceItemDto.getShippingPromotions();
            if (CollectionUtils.isNotEmpty(shippingDiscountFee)) {
                for (PromotionInfoDto promotionInfoDto : shippingDiscountFee) {
                    //单品优惠营销返回是全部金额，所以需要单独除数量
                    money = money.subtract(promotionInfoDto.getDiscountFeeForBase());
                }
                detail.put("shippingDiscountFee", shippingDiscountFee);
            }

            List<PromotionInfoDto> itemDiscountFees = tradePriceItemDto.getItemPromotions();
            if (CollectionUtils.isNotEmpty(itemDiscountFees)) {
                for (PromotionInfoDto promotionInfoDto : itemDiscountFees) {
                    money = money.subtract(promotionInfoDto.getDiscountFeeForBase());
                }
                detail.put("itemDiscountFees", itemDiscountFees);
            }

            List<PromotionInfoDto> itemGoldDiscountFees = tradePriceItemDto.getGoldPromotions();
            if (CollectionUtils.isNotEmpty(itemGoldDiscountFees)) {
                for (PromotionInfoDto promotionInfoDto : itemGoldDiscountFees) {
                    money = money.add(promotionInfoDto.getDiscountFeeForBase());
                }
                detail.put("goldDiscountFees", itemGoldDiscountFees);
            }
            //税费计算
            List<TaxDetailDto> taxDetails = tradePriceItemDto.getTaxDetails();
            if (CollectionUtils.isEmpty(taxDetails)) {
                continue;
            }
            MonetaryAmount tax = Money.zero(temp.getBaseCurrency());
            for (TaxDetailDto taxDetailDto : taxDetails) {
                tax = tax.add(taxDetailDto.getTaxFee());
            }
            detail.put("taxDetail", taxDetails);
            money = money.add(tax);

        }

        money = FundCalculator.calculateAmountByExchange(temp.getExchangeInfoDto().getRate(), money, temp.getExchangeInfoDto().getQutoCurrency(), Boolean.TRUE);

        //重新获取金本位优惠
        List<PromotionInfoDto> goldDiscountFeesAll = new ArrayList<>();
        for (TradePriceItemDto tradePriceItemDto : tradePriceItemDtos) {
            JSONObject detail = new JSONObject();
            details.add(detail);
            List<PromotionInfoDto> itemGoldDiscountFees = tradePriceItemDto.getGoldPromotions();
            if (CollectionUtils.isNotEmpty(itemGoldDiscountFees)) {
                for (PromotionInfoDto promotionInfoDto : itemGoldDiscountFees) {
                    money = money.subtract(promotionInfoDto.getDiscountFeeForQuote());
                    goldDiscountFeesAll.add(promotionInfoDto);
                }
            }
        }
        detailTemp.put("goldDiscountFeesAllForQuote", goldDiscountFeesAll);

        //重新获取商品金本位优惠
        List<PromotionInfoDto> itemGoldDiscountFeesAll = new ArrayList<>();
        for (TradePriceItemDto tradePriceItemDto : tradePriceItemDtos) {
            JSONObject detail = new JSONObject();
            details.add(detail);
            List<PromotionInfoDto> itemGoldDiscountFees = tradePriceItemDto.getItemGoldPromotions();
            if (CollectionUtils.isNotEmpty(itemGoldDiscountFees)) {
                for (PromotionInfoDto promotionInfoDto : itemGoldDiscountFees) {
                    money = money.add(promotionInfoDto.getDiscountFeeForQuote());
                    itemGoldDiscountFeesAll.add(promotionInfoDto);
                }
            }
        }
        detailTemp.put("itemGoldDiscountFeesForQuote", itemGoldDiscountFeesAll);


        //重新获取金币优惠
        Money coinFee = Money.zero(temp.getQuoteCurrency());
        List<PromotionInfoDto> coin = new ArrayList<>();
        for (TradePriceItemDto tradePriceItemDto : tradePriceItemDtos) {
            List<PromotionInfoDto> itemDiscountFees = tradePriceItemDto.getItemPromotions();
            for (PromotionInfoDto promotionInfoDto : itemDiscountFees) {
                if ("flexiCoin".equalsIgnoreCase(promotionInfoDto.getToolCode())) {
                    coinFee = coinFee.add(promotionInfoDto.getDiscountFeeForQuote());
                    coin.add(promotionInfoDto);
                }
            }

        }
        detailTemp.put("exRate", temp.getExchangeInfoDto().getRate());
        detailTemp.put("list", details);
        detailTemp.put("coinDiscountFee", coin);
        priceCalculateDto.setPrice(money.add(coinFee).toString());
        priceCalculateDto.setDetail(detailTemp);
        return priceCalculateDto;
    }


}
