package com.aliexpress.databank.price.dto.common.simulatordata;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.aliexpress.databank.price.dto.common.simulatordata.data.TradePriceItemDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class SimulatorJsonPathResponseDto {

    /**
     * 商品相关解析
     */
    @JSONField(serialize = false)
    private Map<String, TradePriceItemDto> priceItems;


    /**
     * 扩展属性
     */
    private JSONObject extend = new JSONObject();



    /**
     * 是否是量价
     */
    private boolean isDp = false;


}
