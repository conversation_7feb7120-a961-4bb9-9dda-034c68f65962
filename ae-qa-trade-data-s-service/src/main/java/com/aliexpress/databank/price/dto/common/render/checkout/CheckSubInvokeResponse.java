package com.aliexpress.databank.price.dto.common.render.checkout;


import com.aliexpress.databank.price.dto.common.CheckFiledDTO;
import com.aliexpress.databank.price.dto.common.render.common.PriceRenderResponse;
import com.aliexpress.databank.price.dto.common.simulatordata.SimulatorJsonPathResponseDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 子调用check规则和结果
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class CheckSubInvokeResponse implements PriceRenderResponse {

    /**
     * 原始数据
     */
    private SimulatorJsonPathResponseDto body;

    /** 诊断规则和字段
     */

    private List<CheckFiledDTO> checkFiledDTO;



}
