package com.aliexpress.databank.price.dto.common;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import javax.money.MonetaryAmount;

/**
 * Created with IntelliJ IDEA.
 * User: wuqi.zlx
 * Date: 2018/5/30
 * Time: 5:32
 */
@Builder
@Getter
public class PriceShareWeight {

    private String identity;

    private MonetaryAmount fee;

    @Setter
    private MonetaryAmount minShare;

    private Integer priority;
}
