package com.aliexpress.databank.price.dto.common;

import lombok.Data;

import java.util.HashMap;
import java.util.Map;

@Data
public class TradePriceJsonPathDto {

    /**
     * 解析字段对应的工厂类
     */
    private String factory;

    /**
     * jsonpath，json解析专用
     */
    private String jsonPath;

    /**
     * 字段名称，当factory为simpleFactory 的时候生效。
     */
    private String name;

    /**
     * oNode是否支持，如果不支持则会使用jsonpath包支持jsonpath功能
     */
    private boolean notUseONode = false;

    /**
     * 扩展用，可能一个节点不止一个jsonpath
     */
    private Map<String, String> ext = new HashMap<>();

}
