package com.aliexpress.databank.price.utils;

import com.alibaba.ecommerce.internal.dto.MonetaryDTO;
import com.alibaba.global.g11n.model.DisplayStyle;
import com.alibaba.global.g11n.utils.MonetaryUtils;
import com.alibaba.global.money.Money;
import com.alibaba.global.payment.calculator.domain.FundCalculateService;
import com.alibaba.global.payment.calculator.domain.response.CalResponse;

import javax.money.MonetaryAmount;
import java.math.BigDecimal;
import java.util.Currency;
import java.util.Optional;

/**
 * Created by limeng on 2021/11/22.
 */
public class MoneyUtils {

    public static Money divide(Money a, Integer b) {
        return a.divide(b);
    }

    public static Money getPriceByMultiply(Money postAmount, String payCur, BigDecimal fxRate) {
        CalResponse calResponse = FundCalculateService.transferMoneyFromPostCurToPayCur(postAmount, payCur, fxRate);
        if (calResponse.isSucceed()) {
            Money result = calResponse.getTargetAmount();
            return result;
        } else {
            throw new RuntimeException("base:" + postAmount.getCurrencyCode() + "/quote:" + payCur);
        }
    }



//    public static ILocalizationService getILocalizationService() {
//        return GPBeanHelper.getBean(ILocalizationService.class);
//    }

//    public static MonetaryAmount buildMonetaryAmount(Money money) {
//        if (money == null) return null;
//        return getILocalizationService().getCurrentMonetaryService().getMonetaryAmount(money.getNumber(), money.getCurrency());
//    }

    public static Money convert2Money(MonetaryAmount amount) {
        if (null == amount) {
            return null;
        }
        int fractionDigits = Currency.getInstance(amount.getCurrency().getCurrencyCode()).getDefaultFractionDigits();
        if (fractionDigits == 0) {
            return Money.of(String.valueOf(amount.getNumber().longValue()), amount.getCurrency().getCurrencyCode());
        }
        return Money.of(new BigDecimal(String.valueOf(amount.getNumber().doubleValue())).toPlainString(),
                amount.getCurrency().getCurrencyCode());
    }

    public static Money min(Money a, Money b) {
        if (null == a || null == b) {
            return null;
        }
        if (a.compareTo(b) <= 0) {
            return a;
        }
        return b;
    }

    public static Money max(Money a, Money b) {
        if (null == a || null == b) {
            return null;
        }
        if (a.compareTo(b) <= 0) {
            return b;
        }
        return a;
    }


    public static Money add(Money a, Money b) {
        if (a == null) {
            return b;
        }
        if (b == null) {
            return a;
        }
        return a.add(b);
    }


    public static boolean isZero(Money money) {
        if (null == money) {
            return false;
        }
        return money.isZero();
    }

    public static Money zero(String ccy) {
        return Money.of("0.0", ccy);
    }

    /**
     * >0 : true
     *
     * @param money
     * @return
     */
    public static boolean biggerThanZero(Money money) {
        if (null == money) {
            return false;
        }
        return money.isGreaterThan(zero(money.getCurrencyCode()));
    }

    public static String format(Money amt) {
        return MonetaryUtils.format(amt, "en", DisplayStyle.GLOBAL);
    }

}
