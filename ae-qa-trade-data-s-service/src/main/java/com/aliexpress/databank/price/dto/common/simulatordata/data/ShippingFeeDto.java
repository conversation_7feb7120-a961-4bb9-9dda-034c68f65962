package com.aliexpress.databank.price.dto.common.simulatordata.data;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.money.MonetaryAmount;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ShippingFeeDto {


    /**
     * 合包后运费
     */
    private MonetaryAmount shippingBaseFee;

    /**
     * 合包优惠的费用
     */
    private MonetaryAmount shippingSavedFee;
}
