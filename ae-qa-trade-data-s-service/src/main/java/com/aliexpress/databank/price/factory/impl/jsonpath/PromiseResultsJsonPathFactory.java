package com.aliexpress.databank.price.factory.impl.jsonpath;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliexpress.databank.price.annotation.TradePriceJsonPathFactoryAnnotation;
import com.aliexpress.databank.price.dto.common.TradePriceJsonPathDto;
import com.aliexpress.databank.price.dto.common.simulatordata.SimulatorJsonPathResponseDto;
import com.aliexpress.databank.price.dto.common.simulatordata.data.TradePriceItemDto;
import com.aliexpress.databank.price.factory.TradePriceJsonPathFactory;
import org.apache.commons.lang3.StringUtils;
import org.noear.snack.ONode;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

@Component
@TradePriceJsonPathFactoryAnnotation(value = "promiseResultsJsonPathFactory")
public class PromiseResultsJsonPathFactory extends AbstractOrderPriceRenderFactory {


    @Override
    public void handleResult(SimulatorJsonPathResponseDto result, TradePriceJsonPathDto tradePriceJsonPathDto, ONode target, ONode selectResult) {
        JSONArray response = JSON.parseArray(selectResult.toJson());
        //这边是基于cartId来获取的
        Map<String, TradePriceItemDto> map = result.getPriceItems();
        Map<String, JSONObject> promiseResultTransform = transformSimulatorForCartId(response);

        for (String key : map.keySet()) {
            TradePriceItemDto tradePriceItemDto = map.get(key);
            JSONObject promiseResult = promiseResultTransform.get(tradePriceItemDto.getCartId());
            if (promiseResult == null) {
                promiseResult = promiseResultTransform.get(tradePriceItemDto.getProductId());
            }
            if (promiseResult == null) {
                continue;
            }
            String warrantyExt = getItemExtraParams(tradePriceItemDto.getSkuId(), result.getExtend().getJSONArray("itemParam"));

            BigDecimal rate = get3CPromiseRate(promiseResult, warrantyExt);
            tradePriceItemDto.setRate(rate);
        }
    }

    /**
     * 转化天启的返回值，变成cartId对应的结构
     *
     * @param response
     * @return
     */
    private Map<String, JSONObject> transformSimulatorForCartId(JSONArray response) {
        Map<String, JSONObject> result = new HashMap<>();
        for (int i = 0; i < response.size(); i++) {
            JSONObject jsonObject = response.getJSONObject(i);
            for (String key : jsonObject.keySet()) {
                //key就是cartId
                result.put(key, jsonObject.getJSONObject(key));

            }
        }
        return result;
    }


    /**
     * 获取3c保修的汇率
     *
     * @param promiseResult
     * @return
     */
    private BigDecimal get3CPromiseRate(JSONObject promiseResult, String warrantyExt) {
        JSONArray valuablePromiseDetailList = promiseResult.getJSONArray("valuablePromiseDetailList");
        //这边分两种，一种是强制命中，一种是用户选中
        //先过滤3c的服务类型，id是10的那种
        for (int i = 0; i < valuablePromiseDetailList.size(); i++) {
            JSONObject valuablePromiseDetail = valuablePromiseDetailList.getJSONObject(i);
            if (10 != valuablePromiseDetail.getInteger("id")) {
                continue;
            }

            //根据valueMap获取
            JSONObject valueMap = valuablePromiseDetail.getJSONObject("valueMap");
            if (valueMap == null) {
                continue;
            }
            //
            int type = valueMap.getInteger("k6");
            if (type == 1) {
                return valueMap.getBigDecimal("k1");
            }
            //如果不是强制，则匹配对应的id
            if (StringUtils.isEmpty(warrantyExt)) {
                return null;
            }
            String xxId = StringUtils.contains(warrantyExt, ":") ? warrantyExt.split(":")[1] : warrantyExt;
            if (xxId.equalsIgnoreCase(valuablePromiseDetail.getString("promiseInstanceId"))) {
                return valueMap.getBigDecimal("k1");
            }
        }
        return null;

    }

    private String getItemExtraParams(String skuId, JSONArray itemParam) {
        for (int i = 0; i < itemParam.size(); i++) {
            JSONObject item = itemParam.getJSONObject(i);
            if (skuId.equalsIgnoreCase(item.getString("skuId"))) {
                JSONObject extraParams = item.getJSONObject("extraParams");
                if (extraParams != null) {
                    return extraParams.getString("warranty_ext");
                }
            }
        }
        return null;
    }
}
