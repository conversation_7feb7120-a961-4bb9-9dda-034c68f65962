package com.aliexpress.databank.price.factory.impl.jsonpath;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.global.money.Money;
import com.aliexpress.databank.price.annotation.TradePriceJsonPathFactoryAnnotation;
import com.aliexpress.databank.price.constant.Promotion4TradeTypeEnum;
import com.aliexpress.databank.price.constant.PromotionMoldEnum;
import com.aliexpress.databank.price.constant.PromotionTypeEnum;
import com.aliexpress.databank.price.dto.common.TradePriceJsonPathDto;
import com.aliexpress.databank.price.dto.common.simulatordata.SimulatorJsonPathResponseDto;
import com.aliexpress.databank.price.dto.common.simulatordata.data.PromotionDetailDto;
import com.aliexpress.databank.price.dto.common.simulatordata.data.PromotionInfoDto;
import com.aliexpress.databank.price.dto.common.simulatordata.data.TradePriceItemDto;
import com.aliexpress.databank.utils.FundCalculator;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.noear.snack.ONode;
import org.springframework.stereotype.Component;

import javax.money.MonetaryAmount;
import java.math.BigDecimal;
import java.util.*;

@Component
@TradePriceJsonPathFactoryAnnotation(value = "promotionInfoFactory")
public class PromotionInfoFactory extends AbstractOrderPriceRenderFactory {


    @Override
    public void handleResult(SimulatorJsonPathResponseDto result, TradePriceJsonPathDto tradePriceJsonPathDto, ONode target, ONode selectResult) {
        JSONObject selectResultJSON = JSON.parseObject(selectResult.toJson());
        //优惠根据key来获取，所以要选则出对应skuid和key的关系，在params里面
        String paramsPath = tradePriceJsonPathDto.getExt().get("params");
        String type = tradePriceJsonPathDto.getExt().get("type");
        ONode paramsSelectResult = target.select(paramsPath);
        if (paramsSelectResult == null || paramsSelectResult.isNull()) {
            return;
        }
        JSONArray paramsJSON = JSON.parseArray(paramsSelectResult.toJson());
        //处理营销结果 作用到优惠上面
        Promotion4TradeTypeEnum promotion4TradeTypeEnum = Promotion4TradeTypeEnum.origin;

        if (StringUtils.isNotEmpty(type)) {
            promotion4TradeTypeEnum = Promotion4TradeTypeEnum.valueOf(type);
        }

        //处理入参
        Map<String, TradePriceItemDto> relations = getRelationForTrade(paramsJSON, result.getPriceItems(),promotion4TradeTypeEnum);

        //处理出参
        getPromotionInfo(selectResultJSON, relations, promotion4TradeTypeEnum);
        //处理优惠明细
//        handlePromotionDetail(relations);
    }

    /**
     * 处理优惠明细
     */
    private void handlePromotionDetail(Map<String, TradePriceItemDto> relations) {
        for (String key : relations.keySet()) {
            TradePriceItemDto tradePriceItemDto = relations.get(key);
//            tradePriceItemDto.setItemSinglePromotions(tradePriceItemDto.getItemSinglePromotions());
//            tradePriceItemDto.setItemPromotions(tradePriceItemDto.getItemPromotions());
//            tradePriceItemDto.setItemGoldPromotions(tradePriceItemDto.getItemGoldPromotions());
//            tradePriceItemDto.setGoldPromotions(tradePriceItemDto.getGoldPromotions());
//            tradePriceItemDto.setShippingPromotions(tradePriceItemDto.getShippingPromotions());

        }

    }

    /**
     * 构建优惠关系
     *
     * @param params
     * @param skuMaps
     * @return
     */
    private Map<String, TradePriceItemDto> getRelationForTrade(JSONArray params, Map<String, TradePriceItemDto> skuMaps,Promotion4TradeTypeEnum type) {
        Map<String, TradePriceItemDto> result = new HashMap<>();
        for (int i = 0; i < params.size(); i++) {
            JSONObject item = params.getJSONObject(i);
            JSONArray skus = item.getJSONArray("skus");
            for (int j = 0; j < skus.size(); j++) {
                JSONObject sku = skus.getJSONObject(j);
                String skuId = sku.getString("skuId");
                JSONArray targetIds = sku.getJSONArray("targetIds");
                if (CollectionUtils.isEmpty(targetIds)) {
                    continue;
                }
                String targetId = targetIds.getString(0);
                JSONObject prices = sku.getJSONObject("prices");
                if (prices == null) {
                    continue;
                }



                TradePriceItemDto tradePriceItemDto = skuMaps.get(skuId);

                //处理交易给营入参
                List<PromotionDetailDto> promotionDetailDtos = tradePriceItemDto.getPromotionDetailDtos();

                if(promotionDetailDtos==null){
                    promotionDetailDtos= new ArrayList<>();
                }

                MonetaryAmount retailPrice = Money.of(prices.getJSONObject("retailPrice").getBigDecimal("amount").divide(new BigDecimal(100)), prices.getJSONObject("retailPrice").getString("currencyCode"));
                MonetaryAmount salePrice = Money.of(prices.getJSONObject("salePrice").getBigDecimal("amount").divide(new BigDecimal(100)), prices.getJSONObject("salePrice").getString("currencyCode"));

                PromotionDetailDto temp = new PromotionDetailDto();

                temp.setCalRetailPrice(retailPrice);
                temp.setCalSalePrice(salePrice);
                temp.setType(type);

                promotionDetailDtos.add(temp);

                tradePriceItemDto.setPromotionDetailDtos(promotionDetailDtos);

                result.put(targetId, tradePriceItemDto);
            }
        }
        return result;
    }


    /**
     * 获取优惠信息
     *
     * @param respons
     * @param relations
     * @return
     */
    private void getPromotionInfo(JSONObject respons, Map<String, TradePriceItemDto> relations, Promotion4TradeTypeEnum type) {
        JSONObject promotion = respons;
        //正常情况下，优惠只有一次调用，量价是两次调用，但是都用最新的去覆盖,如果是dp，存储原价内容

        //优惠分三类，商品优惠，店铺优惠，跨店优惠
        //处理商品优惠
        JSONArray itemPromotions = promotion.getJSONArray("itemPromotions");
        JSONArray shopPromotions = promotion.getJSONArray("shopPromotions");
        JSONArray acrossPromotions = promotion.getJSONArray("acrossPromotions");

        handlePromotions(itemPromotions, relations, PromotionMoldEnum.ITEM, type);

        if (CollectionUtils.isNotEmpty(shopPromotions)) {
            handlePromotions(shopPromotions, relations, PromotionMoldEnum.SHOP,type);

        }

        if (CollectionUtils.isNotEmpty(acrossPromotions)) {
            handlePromotions(acrossPromotions, relations, PromotionMoldEnum.CROSS,type);

        }

        return;
    }

    private void handlePromotions(JSONArray itemPromotions, Map<String, TradePriceItemDto> relations, PromotionMoldEnum promotionMoldEnum, Promotion4TradeTypeEnum isDp) {
        for (int i = 0; i < itemPromotions.size(); i++) {
            JSONObject promotion = itemPromotions.getJSONObject(i);
            JSONArray promotionResults = promotion.getJSONArray("promotionResults");
            for (int j = 0; j < promotionResults.size(); j++) {
                JSONObject promotionResult = promotionResults.getJSONObject(j);
                boolean selected = promotionResult.getBoolean("selected");
                if (!selected) {
                    continue;
                }
                JSONArray promotionResultDetails = promotionResult.getJSONArray("promotionResultDetails");
                for (int m = 0; m < promotionResultDetails.size(); m++) {
                    JSONObject promotionResultDetail = promotionResultDetails.getJSONObject(m);
                    //获取channelcode
                    JSONObject inventoryChannelInfo = promotionResultDetail.getJSONObject("inventoryChannelInfo");
                    String channelCode = null;
                    if (inventoryChannelInfo != null) {
                        channelCode = inventoryChannelInfo.getString("channelCode");
                    }
                    //获取优惠信息
                    JSONObject promotionInfo = promotionResultDetail.getJSONObject("promotion");
                    String toolcode = promotionInfo.getString("toolCode");
                    String type = promotionInfo.getString("promotionItemType");
                    String promotionType = promotionInfo.getString("promotionType");

                    //金额分摊
                    JSONObject priceResult = promotionResultDetail.getJSONObject("priceResult");
                    PromotionTypeEnum promotionTypeEnum = getPromotionTypeEnumByPromotionItemType(type);
                    //合单优惠比较特殊
                    JSONObject discount = priceResult.getJSONObject("discount");
                    boolean isGold = isGold(discount);
                    JSONArray sharedAmounts = getSharedMounts(discount, toolcode, promotionTypeEnum, isGold);
                    JSONArray intentionDiscount = getSharedMounts(priceResult.getJSONObject("intentionDiscount"), toolcode, promotionTypeEnum, isGold);

                    boolean isIntention = sharedAmounts.size() < intentionDiscount.size();
                    JSONArray target = sharedAmounts.size() < intentionDiscount.size() ? intentionDiscount : sharedAmounts;
                    JSONArray source = sharedAmounts.size() < intentionDiscount.size() ? sharedAmounts : intentionDiscount;
                    for (int n = 0; n < target.size(); n++) {
                        JSONObject sharedAmount = target.getJSONObject(n);
                        String targetId = sharedAmount.getString("targetId");
                        TradePriceItemDto tradePriceItemDto = relations.get(targetId);
                        MonetaryAmount discountFee = Money.of(sharedAmount.getJSONObject("amount").getBigDecimal("amount").divide(new BigDecimal(100)), sharedAmount.getJSONObject("amount").getString("currencyCode"));
                        //获取另外一个币种金额
                        MonetaryAmount otherDiscountFee = getFee(isIntention, source, targetId, discountFee, tradePriceItemDto);
                        PromotionInfoDto promotionInfoDto = PromotionInfoDto.builder()
                                .discountFeeForBase(isIntention ? otherDiscountFee : discountFee)
                                .discountFeeForQuote(isIntention ? discountFee : otherDiscountFee)
                                .isPlatform(isGold)
                                .isSingle(promotionType.equalsIgnoreCase("ITEM"))
                                .toolCode(toolcode)
                                .promotionMold(promotionMoldEnum)
                                .type(promotionTypeEnum)
                                .channelCode(channelCode).build();

                        if (Promotion4TradeTypeEnum.dp.equals(isDp)) {
                            if (CollectionUtils.isEmpty(tradePriceItemDto.getPromotionInfoDtos())) {
                                tradePriceItemDto.setPromotionInfoDtos(new ArrayList<>());
                            }
                            tradePriceItemDto.getPromotionInfoDtos().add(promotionInfoDto);
                        }


                        PromotionDetailDto promotionDetailDto= tradePriceItemDto.getPromotionDetail4Enum(isDp);
                        if(promotionDetailDto==null){
                            promotionDetailDto=new PromotionDetailDto();
                            tradePriceItemDto.getPromotionDetailDtos().add(promotionDetailDto);
                        }
                        promotionDetailDto.setType(isDp);
                        if (CollectionUtils.isEmpty(promotionDetailDto.getPromotionInfoDtos())) {
                            promotionDetailDto.setPromotionInfoDtos(new ArrayList<>());
                        }
                        promotionDetailDto.getPromotionInfoDtos().add(promotionInfoDto);


                    }
                }
            }
        }

    }

    private MonetaryAmount getFee(boolean isIntention, JSONArray source, String targetId, MonetaryAmount discountFee, TradePriceItemDto tradePriceItemDto) {

        for (int i = 0; i < source.size(); i++) {
            JSONObject sharedAmount = source.getJSONObject(i);
            String targetIdTemp = sharedAmount.getString("targetId");
            if (targetId.equalsIgnoreCase(targetIdTemp)) {
                if (sharedAmount.getJSONObject("amount") != null) {
                    return Money.of(sharedAmount.getJSONObject("amount").getBigDecimal("amount").divide(new BigDecimal(100)), sharedAmount.getJSONObject("amount").getString("currencyCode"));
                } else {
                    return FundCalculator.calculateAmountByExchange(tradePriceItemDto.getExchangeInfoDto().getRate(), discountFee, isIntention ? tradePriceItemDto.getBaseCurrency() : tradePriceItemDto.getQuoteCurrency(), !isIntention);
                }
            }
        }
        //不然就要用意向币种进行转化
        return Money.zero(isIntention ? tradePriceItemDto.getBaseCurrency() : tradePriceItemDto.getQuoteCurrency());

    }


    /**
     * 合单特殊处理
     *
     * @param priceResult
     * @param toolCode
     * @return
     */
    private JSONArray getSharedMounts(JSONObject priceResult, String toolCode, PromotionTypeEnum promotionTypeEnum, boolean isgold) {
        JSONArray jsonArray = new JSONArray();
        //运费优惠比较特殊
        if (promotionTypeEnum.equals(PromotionTypeEnum.SHIPPING)) {

            JSONArray shippingServiceDiscounts = priceResult.getJSONArray("shippingServiceDiscounts");

            for (int i = 0; i < shippingServiceDiscounts.size(); i++) {
                JSONObject discount = shippingServiceDiscounts.getJSONObject(i);
                JSONObject temp = discount.getJSONObject("discount");
                jsonArray.addAll(temp.getJSONArray("sharedAmounts"));
            }

            return jsonArray;
        }

        return isgold ? priceResult.getJSONArray("platformSponsoredSharedAmounts") : priceResult.getJSONArray("sharedAmounts");
    }


    private PromotionTypeEnum getPromotionTypeEnumByPromotionItemType(String promotionItemType) {
        switch (promotionItemType) {
            case "TRADE_ITEM":
                return PromotionTypeEnum.ITEM;
            case "FULFILLMENT_ITEM":
                return PromotionTypeEnum.SHIPPING;
            default:
                return PromotionTypeEnum.ITEM;
        }
    }


    private boolean isGold(JSONObject discount) {

        return discount != null && discount.getJSONArray("platformSponsoredSharedAmounts") != null && CollectionUtils.isNotEmpty(discount.getJSONArray("platformSponsoredSharedAmounts"));
    }
}
