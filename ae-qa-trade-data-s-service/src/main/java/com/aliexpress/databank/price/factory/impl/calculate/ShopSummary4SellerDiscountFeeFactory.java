package com.aliexpress.databank.price.factory.impl.calculate;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.global.money.Money;
import com.aliexpress.databank.price.dto.common.render.common.PriceCalculateDto;
import com.aliexpress.databank.price.dto.common.simulatordata.data.PromotionInfoDto;
import com.aliexpress.databank.price.dto.common.simulatordata.data.TradePriceItemDto;
import com.aliexpress.databank.price.factory.TradePriceSingleCalculateFactory;
import com.aliexpress.databank.utils.FundCalculator;
import org.apache.commons.collections4.CollectionUtils;

import javax.money.MonetaryAmount;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class ShopSummary4SellerDiscountFeeFactory implements TradePriceSingleCalculateFactory {

    @Override
    public PriceCalculateDto calculate(List<TradePriceItemDto> tradePriceItemDtos) throws Exception {

        PriceCalculateDto priceCalculateDto = new PriceCalculateDto();
        priceCalculateDto.setFormulaOfCalculate("fixedDiscount+fullPrice+freeShipping");
        List<JSONObject> details = new ArrayList<>();

        List<String> toolcodes = Arrays.asList("fixedDiscount", "fullPiece", "freeShipping");
        JSONObject detailTemp = new JSONObject();
        TradePriceItemDto temp = tradePriceItemDtos.get(0);
        MonetaryAmount money = Money.zero(temp.getQuoteCurrency());
        for (TradePriceItemDto tradePriceItemDto : tradePriceItemDtos) {
            JSONObject detail = new JSONObject();
            details.add(detail);
            List<PromotionInfoDto> list = tradePriceItemDto.getPromotionInfoDtos();
            List<PromotionInfoDto> sellerDiscounts = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(list)) {
                for (PromotionInfoDto promotionInfoDto : list) {
                    if (toolcodes.contains(promotionInfoDto.getToolCode())) {
                        money = money.add(promotionInfoDto.getDiscountFeeForQuote());
                        sellerDiscounts.add(promotionInfoDto);
                    }
                }
                detail.put("sellerDiscountFee", sellerDiscounts);
            }
        }
        if (money.isZero()) {
            return null;
        }

        detailTemp.put("list", details);
        priceCalculateDto.setPrice(money.toString());
        priceCalculateDto.setDetail(detailTemp);
        return priceCalculateDto;
    }

}
