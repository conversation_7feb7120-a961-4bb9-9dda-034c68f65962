package com.aliexpress.databank.price.factory.impl.jsonpath;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.global.money.Money;
import com.aliexpress.databank.price.annotation.TradePriceJsonPathFactoryAnnotation;
import com.aliexpress.databank.price.dto.common.TradePriceJsonPathDto;
import com.aliexpress.databank.price.dto.common.simulatordata.SimulatorJsonPathResponseDto;
import com.aliexpress.databank.price.dto.common.simulatordata.data.ShippingFeeDto;
import com.aliexpress.databank.price.dto.common.simulatordata.data.TradePriceItemDto;
import com.aliexpress.databank.price.factory.TradePriceJsonPathFactory;
import com.jayway.jsonpath.JsonPath;
import org.apache.commons.lang3.StringUtils;
import org.noear.snack.ONode;
import org.springframework.stereotype.Component;

import javax.money.MonetaryAmount;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

@Component
@TradePriceJsonPathFactoryAnnotation(value = "fulfillmentServiceFactory")
public class FulfillmentServiceFactory extends AbstractOrderPriceRenderFactory {

    @Override
    public void handleResult(SimulatorJsonPathResponseDto result, TradePriceJsonPathDto tradePriceJsonPathDto, ONode target, ONode selectResult) {
        JSONArray response = JSON.parseArray(selectResult.toJson());
        //运费只有itemId，没有skuId，所以需要在param里面获取到关系，所以也要获取一下params到返回
        String paramsPath = tradePriceJsonPathDto.getExt().get("params");
        ONode params = target.select(paramsPath);
        if (params.isNull()) {
            return;
        }
        String paramString = params.toJson();
        JSONArray paramArrays = JSON.parseArray(paramString);
        Map<String, TradePriceItemDto> relationForTrade = getRelationForTrade(paramArrays, result.getPriceItems());
        //获取一下tradeLineid和商品sku组合对应的问题
        getShippingFee(response, relationForTrade);
    }

    /**
     * 根据入参获取id和skuid的关系
     *
     * @param requestParam
     * @param map
     * @return
     */
    private Map<String, TradePriceItemDto> getRelationForTrade(JSONArray requestParam, Map<String, TradePriceItemDto> map) {
        Map<String, TradePriceItemDto> result = new HashMap<>();
        for (int i = 0; i < requestParam.size(); i++) {
            JSONObject request = requestParam.getJSONObject(i);
            JSONArray fulfillmentLines = request.getJSONArray("fulfillmentLines");
            for (int j = 0; j < fulfillmentLines.size(); j++) {
                JSONObject fulfillmentLine = fulfillmentLines.getJSONObject(j);
                JSONObject sku = fulfillmentLine.getJSONObject("sku");
                String tradeOrderLineId = fulfillmentLine.getString("tradeOrderLineId");
                TradePriceItemDto tradePriceItemDto = map.get(sku.getString("skuId"));
                tradePriceItemDto.setId(tradeOrderLineId);
                MonetaryAmount discountPrice = Money.of(sku.getJSONObject("discountPrice").getBigDecimal("amount").divide(new BigDecimal(100)), sku.getJSONObject("discountPrice").getString("currencyCode"));
                tradePriceItemDto.setCalDiscountPrice(discountPrice);
                result.put(tradeOrderLineId, tradePriceItemDto);
            }

        }

        return result;
    }


    /**
     * 根据返回结果获取运费
     *
     * @param response
     * @param relationForTrade
     */
    private void getShippingFee(JSONArray response, Map<String, TradePriceItemDto> relationForTrade) {

        for (int i = 0; i < response.size(); i++) {
            JSONObject request = response.getJSONObject(i);
            JSONArray fulfillmentLines = request.getJSONArray("fulfillmentGroupServices");
            for (int j = 0; j < fulfillmentLines.size(); j++) {
                JSONObject fulfillmentLine = fulfillmentLines.getJSONObject(j);
                JSONArray fulfillmentLineServices = fulfillmentLine.getJSONArray("fulfillmentLineServices");
                for (int k = 0; k < fulfillmentLineServices.size(); k++) {
                    JSONObject fulfillmentLineService = fulfillmentLineServices.getJSONObject(k);
                    String tradeOrderLineId = fulfillmentLineService.getString("tradeOrderLineId");
                    TradePriceItemDto tradePriceItemDto = relationForTrade.get(tradeOrderLineId);
                    //找到命中的物流方式，获取code
                    String code = fulfillmentLineService.getJSONObject("preferFulfillmentService").getString("fulfillmentServiceCode");
                    //因为天启的问题，运费信息基本会被refer，所以需要遍历服务列表，去获取运费信息
                    JSONArray fulfillmentServices = fulfillmentLineService.getJSONArray("fulfillmentServices");
                    //五层循环是坑人的
                    for (int m = 0; m < fulfillmentServices.size(); m++) {
                        JSONObject fulfillmentService = fulfillmentServices.getJSONObject(m);
                        String fulfillmentServiceCode = fulfillmentService.getString("fulfillmentServiceCode");
                        if (!code.equalsIgnoreCase(fulfillmentServiceCode)) {
                            continue;
                        }
                        ShippingFeeDto shippingFeeDto = new ShippingFeeDto();
                        JSONObject shippingFeeJSON = fulfillmentService.getJSONObject("fulfillmentServiceFee").getJSONObject("shippingFee");
                        if (shippingFeeJSON != null) {
                            MonetaryAmount shippingFee = Money.of(shippingFeeJSON.getBigDecimal("amount").divide(new BigDecimal(100)), shippingFeeJSON.getString("currencyCode"));
                            shippingFeeDto.setShippingBaseFee(shippingFee);
                        }
                        JSONObject shippingSavedFeeJSON = fulfillmentService.getJSONObject("features").getJSONObject("savedShippingCost");
                        if (shippingSavedFeeJSON != null) {
                            MonetaryAmount shippingSavedFee = Money.of(shippingSavedFeeJSON.getBigDecimal("cent").divide(new BigDecimal(100)), shippingSavedFeeJSON.getString("currencyCode"));
                            shippingFeeDto.setShippingSavedFee(shippingSavedFee);
                        }

                        tradePriceItemDto.setShippingFeeDto(shippingFeeDto);
                        break;

                    }

                }

            }

        }

        return;
    }
}
