package com.aliexpress.databank.price.helper;

import com.aliexpress.databank.price.constant.CalculateDetailEnum;
import com.aliexpress.databank.price.constant.TradePriceConfigTypeEnum;
import com.aliexpress.databank.price.factory.TradePriceRenderFactory;
import com.aliexpress.databank.price.factory.TradePriceSingleCalculateFactory;
import com.aliexpress.databank.price.factory.impl.adapt.TradePriceRender4CheckoutFactory;
import com.aliexpress.databank.price.factory.impl.calculate.ItemUnitFeeFactory;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 渲染类工厂工具类
 */
public class TradePriceRenderFactoryHelper {

    private static Map<TradePriceConfigTypeEnum, TradePriceRenderFactory> maps = new HashMap<>();

    static {
        maps.put(TradePriceConfigTypeEnum.RENDER, new TradePriceRender4CheckoutFactory());

    }


    /**
     * 获取工具类
     * @param tradePriceConfigTypeEnum
     * @return
     */
    public static TradePriceRenderFactory getTradePriceRenderFactoryByEnum(TradePriceConfigTypeEnum tradePriceConfigTypeEnum) {
        return maps.get(tradePriceConfigTypeEnum);
    }

}
