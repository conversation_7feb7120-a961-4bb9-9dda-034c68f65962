package com.aliexpress.databank.price.factory.impl.jsonpath;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.global.money.Money;
import com.aliexpress.databank.price.annotation.TradePriceJsonPathFactoryAnnotation;
import com.aliexpress.databank.price.dto.common.TradePriceJsonPathDto;
import com.aliexpress.databank.price.dto.common.simulatordata.SimulatorJsonPathResponseDto;
import com.aliexpress.databank.price.dto.common.simulatordata.data.TradePriceItemDto;
import com.aliexpress.databank.price.factory.TradePriceJsonPathFactory;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.noear.snack.ONode;
import org.springframework.stereotype.Component;

import javax.money.MonetaryAmount;
import java.util.HashMap;
import java.util.Map;

@Component
@TradePriceJsonPathFactoryAnnotation(value = "iCPriceRenderFactory")
public class ICPriceRenderFactory extends AbstractOrderPriceRenderFactory {

    @Override
    public void handleResult(SimulatorJsonPathResponseDto result, TradePriceJsonPathDto tradePriceJsonPathDto, ONode target, ONode selectResult) {

        JSONArray itemParam = result.getExtend().getJSONArray("itemParam");
        Map<String, TradePriceItemDto> oip = new HashMap<>();
        //这边得在查一个卖家id
        ONode shipId = target.select(tradePriceJsonPathDto.getExt().get("params"));
        Map<String, JSONObject> shopInfp = getSellerId(shipId.toJson());
        //转化成jsonObject
        if (selectResult.isNull()) {
            return;
        }
        String json = selectResult.toJson();
        JSONArray jsonArray = JSONArray.parseArray(json);

        JSONObject sku = new JSONObject();
        for (int i = 0; i < jsonArray.size(); i++) {
            sku.putAll(jsonArray.getJSONObject(i));
        }

        for (int i = 0; i < itemParam.size(); i++) {
            JSONObject jsonObject = itemParam.getJSONObject(i);
            String skuId = jsonObject.getString("skuId");
            JSONObject price = sku.getJSONObject(skuId);
            MonetaryAmount priceResult = getPrice(price.getJSONObject("priceDTO"), jsonObject.getInteger("quantity"), price,result.getExtend().getString("shipTo"));
            TradePriceItemDto oipDto = TradePriceItemDto.builder()
                    .cartId(jsonObject.getString("cartId"))
                    .price(priceResult)
                    .productId(jsonObject.getString("itemId"))
                    .skuId(skuId)
                    .quality(jsonObject.getInteger("quantity"))
                    .sellerId(shopInfp.get(jsonObject.getString("itemId")).getString("shipId"))
                    .baseCurrency("USD")
                    .title(shopInfp.get(jsonObject.getString("itemId")).getString("title"))
                    .url(price.getString("detailPageUrl"))
                    .pic(price.getJSONObject("majorImageDTO").getString("imageUrl"))
                    .sku(getSkuAttr(price))
                    .build();

            oip.put(skuId, oipDto);
        }

        result.setPriceItems(oip);
    }

    /**
     * 价格获取
     *
     * @param price
     * @param quality
     * @param featureMap
     * @return
     */
    private MonetaryAmount getPrice(JSONObject price, Integer quality, JSONObject featureMap,String country) {
        //fixedPrice是基础价格
        JSONObject base = price.getJSONObject("fixedPrice");
        MonetaryAmount monetaryAmount = Money.of(base.getBigDecimal("number"), "USD");
        //判断区域价是否生效

        if (price.containsKey("countryPriceList") && price.get("countryPriceList") != null && CollectionUtils.isNotEmpty(price.getJSONArray("countryPriceList"))) {

            JSONArray countryPriceList = price.getJSONArray("countryPriceList");
            for (int i = 0; i < countryPriceList.size(); i++) {
                JSONObject countryPrice = countryPriceList.getJSONObject(i);
                if(!country.equalsIgnoreCase(countryPrice.getString("countryCode"))){
                    continue;
                }
                if(countryPrice.getJSONObject("price")!=null){
                    monetaryAmount=Money.of(countryPrice.getJSONObject("price").getBigDecimal("number"), "USD");
                }
                //判断批发价
                if(countryPrice.getJSONObject("bulkPrice")!=null){
                    Integer integer = featureMap.getInteger("bulkMinQuantity");
                    if (integer != null && quality > integer) {
                        monetaryAmount = Money.of(price.getJSONObject("bulkPrice").getBigDecimal("number"), "USD");
                    }
                }

            }


        }

        //判断批发价是否生效
        if (!price.containsKey("bulkOriginalPrice") && price.get("bulkOriginalPrice") == null) {
            return monetaryAmount;
        }

        Integer integer = featureMap.getInteger("sku_bulk_order");
        if (integer != null && quality > integer) {
            monetaryAmount = Money.of(price.getJSONObject("bulkOriginalPrice").getBigDecimal("number"), "USD");
        }
        return monetaryAmount;
    }


    /**
     * 解析出卖家id
     *
     * @param json
     * @return
     */
    private Map<String, JSONObject> getSellerId(String json) {
        Map<String, JSONObject> result = new HashMap<>();
        JSONArray jsonArray = JSONArray.parseArray(json);
        //有两层
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONArray jsonArray1 = jsonArray.getJSONArray(i);
            for (int j = 0; j < jsonArray1.size(); j++) {
                JSONObject jsonObject = jsonArray1.getJSONObject(j);
                String productId = jsonObject.getString("productId");
                String shopId = jsonObject.getJSONObject("sellerQueryResultDTO").getString("id");
                JSONObject temp = new JSONObject();
                temp.put("shipId", shopId);
                temp.put("title", jsonObject.getJSONObject("title").getJSONObject("langAndValueMap").getJSONObject("MapKey-hidden").getString("value"));
                temp.put("url", jsonObject.getString("detailPageUrl"));
                result.put(productId, temp);
            }
        }
        return result;
    }

    private String getSkuAttr(JSONObject price) {
        JSONArray salePropertyPairList = price.getJSONArray("salePropertyPairList");
        if (CollectionUtils.isEmpty(salePropertyPairList)) {
            return null;
        }
        StringBuilder result = new StringBuilder();
        for (int i = 0; i < salePropertyPairList.size(); i++) {
            JSONObject temp = salePropertyPairList.getJSONObject(i);
            String value = temp.getJSONObject("valueText").getJSONObject("langAndValueMap").getJSONObject("MapKey-hidden").getString("value");
            result.append(value).append(";");
        }
        return result.toString();
    }


}
