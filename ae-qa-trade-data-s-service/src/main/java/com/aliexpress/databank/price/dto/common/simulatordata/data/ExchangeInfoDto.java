package com.aliexpress.databank.price.dto.common.simulatordata.data;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ExchangeInfoDto {

    /**
     * 汇率
     */
    private BigDecimal rate;

    /**
     * 意向币种
     */
    private String qutoCurrency;

    /**
     * 报价币种
     */
    private String baseCurrency;
}
