package com.aliexpress.databank.price.helper;

import com.aliexpress.databank.price.constant.CalculateDetailEnum;
import com.aliexpress.databank.price.dto.common.render.common.PriceCalculateDto;
import com.aliexpress.databank.price.dto.common.simulatordata.data.TradePriceItemDto;
import com.aliexpress.databank.price.factory.TradePriceSingleCalculateFactory;
import com.aliexpress.databank.price.factory.impl.calculate.*;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class CalculateDetailHelper {

    private static Map<CalculateDetailEnum, TradePriceSingleCalculateFactory> maps = new HashMap<>();

    static {
        maps.put(CalculateDetailEnum.PRODUCT_ITEM_UNITE_FEE, new ItemUnitFeeFactory());
        maps.put(CalculateDetailEnum.PRODUCT_ITEM_SHIPPING_FEE, new ItemShippingFeeFactory());
        maps.put(CalculateDetailEnum.SHOP_SUMMARY_SELLER_DISCOUNT_FEE, new ShopSummary4SellerDiscountFeeFactory());
        maps.put(CalculateDetailEnum.SHOP_SUMMARY_SHIPPING_FEE, new ShopSummary4ShippingFeeFactory());
        maps.put(CalculateDetailEnum.SHOP_SUMMARY_SUBTOTAL_FEE, new ShopSummary4SubtotalFactory());
        maps.put(CalculateDetailEnum.SHOP_SUMMARY_TAX_FEE, new ShopSummary4TaxFeeFactory());
        maps.put(CalculateDetailEnum.SHOP_SUMMARY_TOTAL_FEE, new ShopSummary4TotalFeeFactory());
        maps.put(CalculateDetailEnum.SHOP_SUMMARY_STORE_COUPON_FEE, new ShopSummaryShopCouponFeeFactory());

        maps.put(CalculateDetailEnum.ORDER_SUMMARY_COIN_FEE, new OrdersSummaryCoinFeeFactory());
        maps.put(CalculateDetailEnum.ORDER_SUMMARY_PLATFORM_COUPON_CODE_FEE, new OrdersSummaryPlatformCodeFeeFactory());
        maps.put(CalculateDetailEnum.ORDER_SUMMARY_PLATFORM_COUPON_FEE, new OrdersSummaryPlatformCouponFeeFactory());
        maps.put(CalculateDetailEnum.ORDER_SUMMARY_STORE_FEE, new OrdersSummary4StoretotalFactory());
        maps.put(CalculateDetailEnum.ORDER_SUMMARY_TOTAL_FEE, new OrdersSummaryTotalFeeFactory());
    }


    /**
     * 获取工具类
     *
     * @param calculateDetailEnum
     * @return
     */
    public static TradePriceSingleCalculateFactory getTradePriceSingleCalculateFactoryByEnum(CalculateDetailEnum calculateDetailEnum) {
        return maps.get(calculateDetailEnum);
    }

    /**
     * 获取工具类
     *
     * @param calculateDetailEnum
     * @return
     */
    public static PriceCalculateDto invokeByEnmum(CalculateDetailEnum calculateDetailEnum, List<TradePriceItemDto> tradePriceItemDtos) throws Exception {
        return maps.get(calculateDetailEnum).calculate(tradePriceItemDtos);
    }

}
