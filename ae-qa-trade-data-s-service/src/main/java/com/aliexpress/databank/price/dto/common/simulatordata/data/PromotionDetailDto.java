package com.aliexpress.databank.price.dto.common.simulatordata.data;

import com.aliexpress.databank.price.constant.Promotion4TradeTypeEnum;
import lombok.Data;

import javax.money.MonetaryAmount;
import java.util.List;

@Data
public class PromotionDetailDto {

    /**
     * 类型（区分量价/原价/单品)
     */
    private Promotion4TradeTypeEnum type;

    /**
     * 优惠明细
     */
    private List<PromotionInfoDto> promotionInfoDtos;


    /**
     * 交易计算后的原价
     */
    private MonetaryAmount calRetailPrice;

    /**
     * 交易计算后的售价
     */
    private MonetaryAmount calSalePrice;
}
