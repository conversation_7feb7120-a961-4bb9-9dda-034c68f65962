package com.aliexpress.databank.price.factory;

import com.alibaba.fastjson.JSONObject;
import com.aliexpress.databank.price.context.TradePriceRenderContext;
import com.aliexpress.databank.price.dto.common.TradePriceJsonPathDto;
import com.aliexpress.databank.price.dto.common.simulatordata.SimulatorJsonPathResponseDto;
import org.noear.snack.ONode;

/**
 * 价格jsonpath解析方法
 */
public interface TradePriceJsonPathFactory {

     void invoke(SimulatorJsonPathResponseDto result , TradePriceJsonPathDto tradePriceJsonPathDto, ONode target);

     /**
      * 结果处理方法
      *
      * @param result
      * @param tradePriceJsonPathDto
      * @param target
      * @param selectResult
      */
      void afterResult(SimulatorJsonPathResponseDto result, TradePriceJsonPathDto tradePriceJsonPathDto, ONode target, ONode selectResult);


}
