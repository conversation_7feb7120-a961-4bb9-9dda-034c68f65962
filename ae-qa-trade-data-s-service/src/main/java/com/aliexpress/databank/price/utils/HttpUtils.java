package com.aliexpress.databank.price.utils;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;

import java.nio.charset.Charset;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

/**
 * @Description:[ HttpUtils ]
 */
@Slf4j
public class HttpUtils {

    /**
     * 方法描述: 发送get请求
     *
     * @param url
     * @param params
     * @param header
     * @throws
     * @Return {@link String}
     */
    public static String sendGet(String url, Map<String, String> params, Map<String, String> header) throws Exception {
        HttpGet httpGet = null;
        String body = "";
        try {
            CloseableHttpClient httpClient = HttpClients.createDefault();
            List<String> mapList = new ArrayList<>();
            if (params != null) {
                for (Entry<String, String> entry : params.entrySet()) {
                    mapList.add(entry.getKey() + "=" + entry.getValue());
                }
            }
            if (CollectionUtils.isNotEmpty(mapList)) {
                url = url + "?";
                String paramsStr = StringUtils.join(mapList, "&");
                url = url + paramsStr;
            }
            httpGet = new HttpGet(url);
            httpGet.setHeader("Content-type", "application/json; charset=utf-8");
            httpGet.setHeader("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
            if (header != null) {
                for (Entry<String, String> entry : header.entrySet()) {
                    httpGet.setHeader(entry.getKey(), entry.getValue());
                }
            }
            HttpResponse response = httpClient.execute(httpGet);

            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode != HttpStatus.SC_OK) {
                throw new RuntimeException("请求失败");
            } else {
                body = EntityUtils.toString(response.getEntity(), "UTF-8");
            }
        } catch (Exception e) {
            throw e;
        } finally {
            if (httpGet != null) {
                httpGet.releaseConnection();
            }
        }
        return body;
    }

    /**
     * 方法描述: 发送post请求-json数据
     *
     * @param url
     * @param json
     * @param header
     * @throws
     * @Return {@link String}
     */
    public static String sendPostJson(String url, String json, Map<String, String> header) throws Exception {
        HttpPost httpPost = null;
        String body = "";
        try {
            log.info("sendPostJson(): entity: " + json);
            CloseableHttpClient httpClient = HttpClients.createDefault();
            httpPost = new HttpPost(url);
            httpPost.setHeader("Content-type", "application/json; charset=utf-8");
            httpPost.setHeader("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
            if (header != null) {
                for (Entry<String, String> entry : header.entrySet()) {
                    httpPost.setHeader(entry.getKey(), entry.getValue());
                }
            }
            StringEntity entity = new StringEntity(json, Charset.forName("UTF-8"));
            entity.setContentEncoding("UTF-8");
            entity.setContentType("application/json");
            httpPost.setEntity(entity);
            log.info("sendPostJson(): req: " + JSONObject.toJSONString(httpPost.getEntity()));
            HttpResponse response = httpClient.execute(httpPost);
            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode != HttpStatus.SC_OK) {
                throw new RuntimeException("请求失败");
            } else {
                body = EntityUtils.toString(response.getEntity(), "UTF-8");
            }
        } catch (Exception e) {
            throw e;
        } finally {
            if (httpPost != null) {
                httpPost.releaseConnection();
            }
        }
        return body;
    }

    /**
     * 方法描述: 发送post请求-form表单数据
     *
     * @param url
     * @param header
     * @throws
     * @Return {@link String}
     */
    public static String sendPostForm(String url, Map<String, String> params, Map<String, String> header) throws Exception {
        HttpPost httpPost = null;
        String body = "";
        try {
            CloseableHttpClient httpClient = HttpClients.createDefault();
            httpPost = new HttpPost(url);
            httpPost.setHeader("Content-type", "application/x-www-form-urlencoded");
            httpPost.setHeader("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
            if (header != null) {
                for (Entry<String, String> entry : header.entrySet()) {
                    httpPost.setHeader(entry.getKey(), entry.getValue());
                }
            }
            List<NameValuePair> nvps = new ArrayList<>();
            if (params != null) {
                for (Entry<String, String> entry : params.entrySet()) {
                    nvps.add(new BasicNameValuePair(entry.getKey(), entry.getValue()));
                }
            }
            //设置参数到请求对象中
            httpPost.setEntity(new UrlEncodedFormEntity(nvps, "UTF-8"));
            HttpResponse response = httpClient.execute(httpPost);

            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode != HttpStatus.SC_OK) {
                throw new RuntimeException("请求失败");
            } else {
                body = EntityUtils.toString(response.getEntity(), "UTF-8");
            }
        } catch (Exception e) {
            throw e;
        } finally {
            if (httpPost != null) {
                httpPost.releaseConnection();
            }
        }
        return body;
    }


    public static void main(String[] args) throws Exception {
        Map<String, String> header = new HashMap<>();
        header.put("cookie", "ck2=0647c6dead91a37d01f61466310ec707; an=jiqi.zjq; lg=true; sg=q25; cna=+6Q/F+PAklACAXrr8mBec4Z+; c_token=31c53a5e874f0ccca1a3c8ca2f6f323d; ALIPAYCHAIRBUCJSESSIONID=03d50e2e-3144-4fd4-a51a-dc89589a932b; SSO_BU_HASH_V2=4e8d59ba0e6e3962f75465fbe4286df2; xlly_s=1; SSO_EMPID_HASH_V2=b1009f394ed3448bf755d69e0ded4031; UM_distinctid=1788106fd80324-0bb31d636bd1ed-1730685d-1ea000-1788106fd81d8a; arms_uid=f3a966c8-5610-4270-8fed-11e26155d9f2; business-simulator_USER_COOKIE=69004A752F2B4DCE748962902EF3496BCE43A73F4C8B23A8B992B634309C13D90B8D8CB2EB89C6FF698A382FECE1FAA189A1E5AA18A722E14F8C990DBAFD29BD4248E3FFBFAB6C49A4ED3AEFE20914B3C26607CF733B0B59B14F741C61F5697919CBD8310966DF22494EE1E0F31B6AC0; SSO_LANG_V2=ZH-CN; SSO_REFRESH_TOKEN=5186447bc29040d1bc977ef082f3c9001b13ed00; business-simulator_SSO_TOKEN_V2=9C3D5816CF4517D00D5647B99F3235E5CC2152D5ACDD9A7EE6B2516BEF699015A7A8749E8BEDDBC76179B0326D4498FA893DCD79F6C33C37AACFC37A88FAD813; l=eBNaMDucOHpSgTk_BO5aPurza77TZIRb41VzaNbMiInca6MlNFtnGNCQ0Q-eldtxgtCXfetriwFgJRLHRntpvxDDBDfUBgEI3xvO.; tfstk=cXyABgcz1gKYYEi8YSCkfAorZ_UOanxxPngwBipSpvSvLN5fMsczKJnNpsi9kYIR.; JSESSIONID=50252F64C3FFC0875611B990867B4A5D; isg=BBMTRbtvd1j16Ts2NsHUblv4opE9yKeKH-PD5cUwbzJpRDPmTZg32nGSf7QqPP-C");
        String result = sendGet("https://simulator.alibaba-inc.com/api/case/querySavedDataDoomCtx?_input_charset=utf-8&dataId=2021-03-30%2FONLINE%2Fae-trade-ns-s%2F0b14c60c16170735347258290e34a8_0.*******%2F1617073534764", null, header);
        LocalDateTime now = LocalDateTime.now();
        System.out.println(now.with(LocalTime.MAX).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        System.out.println(now.plusDays(-15).with(LocalTime.MIN).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        System.out.println(result);
    }

}