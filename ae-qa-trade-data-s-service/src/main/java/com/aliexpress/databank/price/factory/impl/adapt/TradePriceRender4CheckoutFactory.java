package com.aliexpress.databank.price.factory.impl.adapt;

import com.aliexpress.databank.price.constant.CalculateDetailEnum;
import com.aliexpress.databank.price.dto.common.render.checkout.CheckOutPriceRenderResponse;
import com.aliexpress.databank.price.dto.common.render.checkout.detail.RenderItemDetailDto;
import com.aliexpress.databank.price.dto.common.render.checkout.detail.RenderOrderSummaryDetailDto;
import com.aliexpress.databank.price.dto.common.render.checkout.detail.RenderShopDetailDto;
import com.aliexpress.databank.price.dto.common.render.checkout.detail.RenderSummaryDetailDto;
import com.aliexpress.databank.price.dto.common.render.common.PriceRenderResponse;
import com.aliexpress.databank.price.dto.common.simulatordata.SimulatorJsonPathResponseDto;
import com.aliexpress.databank.price.dto.common.simulatordata.data.TradePriceItemDto;
import com.aliexpress.databank.price.factory.TradePriceRenderFactory;
import com.aliexpress.databank.price.helper.CalculateDetailHelper;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 下单渲染公式转化逻辑
 */

public class TradePriceRender4CheckoutFactory implements TradePriceRenderFactory {


    @Override
    public PriceRenderResponse invoke(SimulatorJsonPathResponseDto result) throws Exception {

        //数据适配，把订单按照店铺维度拆分
        CheckOutPriceRenderResponse checkOutPriceRenderResponse = new CheckOutPriceRenderResponse();
        Map<String, List<TradePriceItemDto>> groupBySellerId = groupBySellerId(result.getPriceItems());

        List<TradePriceItemDto> list = getAllItem(result.getPriceItems());
        List<RenderShopDetailDto> renderShopDetailDtos = new ArrayList<>();
        for (String key : groupBySellerId.keySet()) {

            RenderShopDetailDto renderShopDetailDto = new RenderShopDetailDto();
            renderShopDetailDto.setSellerId(key);
            renderShopDetailDto.setItems(getRenderItem(groupBySellerId.get(key)));

            renderShopDetailDto.setRenderSummaryDetailDto(getSummaryForShop(groupBySellerId.get(key)));
            renderShopDetailDtos.add(renderShopDetailDto);
        }
        checkOutPriceRenderResponse.setShopModel(renderShopDetailDtos);

        RenderOrderSummaryDetailDto renderOrderSummaryDetailDto = RenderOrderSummaryDetailDto.builder()
                .storeTotalFee(CalculateDetailHelper.invokeByEnmum(CalculateDetailEnum.ORDER_SUMMARY_STORE_FEE, list))
                .coinFee(CalculateDetailHelper.invokeByEnmum(CalculateDetailEnum.ORDER_SUMMARY_COIN_FEE, list))
                .platformCodeFee(CalculateDetailHelper.invokeByEnmum(CalculateDetailEnum.ORDER_SUMMARY_PLATFORM_COUPON_CODE_FEE, list))
                .platformCouponFee(CalculateDetailHelper.invokeByEnmum(CalculateDetailEnum.ORDER_SUMMARY_PLATFORM_COUPON_FEE, list))
                .totalFee(CalculateDetailHelper.invokeByEnmum(CalculateDetailEnum.ORDER_SUMMARY_TOTAL_FEE, list))
                .build();

        checkOutPriceRenderResponse.setOrderSummary(renderOrderSummaryDetailDto);
        checkOutPriceRenderResponse.setBody(result);
        return checkOutPriceRenderResponse;
    }


    /**
     * 店铺级别summary
     *
     * @param list
     * @return
     */
    private RenderSummaryDetailDto getSummaryForShop(List<TradePriceItemDto> list) throws Exception {
        RenderSummaryDetailDto renderSummaryDetailDto = RenderSummaryDetailDto.builder()
                .sellerDiscount(CalculateDetailHelper.invokeByEnmum(CalculateDetailEnum.SHOP_SUMMARY_SELLER_DISCOUNT_FEE, list))
                .shippingFee(CalculateDetailHelper.invokeByEnmum(CalculateDetailEnum.SHOP_SUMMARY_SHIPPING_FEE, list))
                .subTotalFee(CalculateDetailHelper.invokeByEnmum(CalculateDetailEnum.SHOP_SUMMARY_SUBTOTAL_FEE, list))
                .taxFee(CalculateDetailHelper.invokeByEnmum(CalculateDetailEnum.SHOP_SUMMARY_TAX_FEE, list))
                .totalFee(CalculateDetailHelper.invokeByEnmum(CalculateDetailEnum.SHOP_SUMMARY_TOTAL_FEE, list))
                .storeCouponFee(CalculateDetailHelper.invokeByEnmum(CalculateDetailEnum.SHOP_SUMMARY_STORE_COUPON_FEE, list))
                .build();


        return renderSummaryDetailDto;
    }

    /**
     * 商品明细构建
     *
     * @param list
     * @return
     */
    private List<RenderItemDetailDto> getRenderItem(List<TradePriceItemDto> list) throws Exception {
        List<RenderItemDetailDto> result = new ArrayList<>();

        for (TradePriceItemDto tradePriceItemDto : list) {

            RenderItemDetailDto renderItemDetailDto = RenderItemDetailDto.builder()
                    .itemId(tradePriceItemDto.getProductId())
                    .quality(tradePriceItemDto.getQuality())
                    .sku(tradePriceItemDto.getSku())
                    .skuId(tradePriceItemDto.getSkuId())
                    .title(tradePriceItemDto.getTitle())
                    .url(tradePriceItemDto.getUrl())
                    .pic(tradePriceItemDto.getPic())
                    .uniteFee(CalculateDetailHelper.getTradePriceSingleCalculateFactoryByEnum(CalculateDetailEnum.PRODUCT_ITEM_UNITE_FEE).calculate(Arrays.asList(tradePriceItemDto)))
                    .shippingFee(CalculateDetailHelper.getTradePriceSingleCalculateFactoryByEnum(CalculateDetailEnum.PRODUCT_ITEM_SHIPPING_FEE).calculate(Arrays.asList(tradePriceItemDto)))
                    .build();
            result.add(renderItemDetailDto);
        }
        return result;
    }


    /**
     * 根据订单按照店铺维度的拆分
     *
     * @param priceItems
     * @return
     */
    private Map<String, List<TradePriceItemDto>> groupBySellerId(Map<String, TradePriceItemDto> priceItems) {
        Map<String, List<TradePriceItemDto>> groupBySellerId = new HashMap<>();
        for (TradePriceItemDto tradePriceItemDto : priceItems.values()) {
            String sellerId = tradePriceItemDto.getSellerId();
            List<TradePriceItemDto> list = groupBySellerId.get(sellerId);
            if (CollectionUtils.isEmpty(list)) {
                list = new ArrayList<>();
            }
            list.add(tradePriceItemDto);
            groupBySellerId.put(sellerId, list);
        }
        return groupBySellerId;
    }


    /**
     * 获取订单全部的子订单信息
     *
     * @param priceItems
     * @return
     */
    private List<TradePriceItemDto> getAllItem(Map<String, TradePriceItemDto> priceItems) {
        List<TradePriceItemDto> list = new ArrayList<>();
        for (TradePriceItemDto tradePriceItemDto : priceItems.values()) {
            list.add(tradePriceItemDto);
        }
        return list;
    }
}
