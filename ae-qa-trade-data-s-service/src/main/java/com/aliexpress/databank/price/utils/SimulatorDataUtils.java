package com.aliexpress.databank.price.utils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliexpress.databank.diamond.TradePriceType4InterfaceNameDiamondRunner;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

@Slf4j
public class SimulatorDataUtils {
    private static String TRACE_QUERY_PAGE_URL = "https://simulator.alibaba-inc.com/api/3rdService/searchOnlineData?outSystem=ae-qa-trade-data-s";

    private static String TRACE_QUERY_DETAIL_URL = "https://simulator.alibaba-inc.com/api/3rdService/queryOnlineContext";

    /*
     * traceId是必须字段,
     * type:代表interfaceName
     */
    public static JSONObject getSimulatorResult(String cookie, String traceId, String type, String simulatorId, String appName) {

//        try {
//            BufferedReader bufferedReader = new BufferedReader(new FileReader(new File(TradeOrderPriceRenderServiceImpl.class.getResource("/").getPath() + "simData")));
//            String line = rebuildResponse(bufferedReader.readLine());
//
//            return JSONObject.parseObject(line);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
        //先通过query获取trace对应的列表，
        //构建列表的请求
        Map<String, String> header = getCommonHeader(cookie);

        String simulatorKey = StringUtils.isEmpty(simulatorId) ? getSimulatorId(traceId, header, type, null, appName) : simulatorId;

        //请求详情结果
        //子调用
        JSONObject subinvokes = getQuerySimulatorData(simulatorKey, "subinvokes", appName);

        //主调用
        JSONObject maininvokes = getQuerySimulatorData(simulatorKey, "mainInvoke", appName);
//        Map<String, String> params = new HashMap<>();
//        params.put("dataId", simulatorKey);
//        params.put("_input_charset", "utf-8");
//        params.put("appName","ae-trade-ns-s");
//        params.put("component","subinvokes");
//        try {
//            String result =
//                    HttpUtils.sendGet(TRACE_QUERY_DETAIL_URL, params, header);
//            return JSONObject.parseObject(rebuildResponse(result));
//        } catch (Exception e) {
//            throw new RuntimeException("获取天启详情数据异常。");
//        }

        JSONObject result = new JSONObject();
        result.put("mainInvoke", maininvokes.getJSONObject("body"));
        result.put("subInvokes", subinvokes.getJSONObject("body"));
        return result;


    }

    private static JSONObject getQuerySimulatorData(String simulatorKey, String component, String appName) {

        //请求详情结果
        Map<String, String> params = new HashMap<>();
        params.put("dataId", simulatorKey);
        params.put("appName", appName);
        params.put("component", component);
        params.put("outSystem", "ae-qa-trade-data-s");
        try {
            String result =
                    HttpUtils.sendGet(TRACE_QUERY_DETAIL_URL, params, new HashMap<>());
            String temp = result;
            if (!appName.equals("ae-pdp-s")) {
                temp = rebuildResponse(result);
            }
            return JSONObject.parseObject(temp);
        } catch (Exception e) {
            throw new RuntimeException("获取天启详情数据异常。");
        }
    }


    /**
     * 构建查询的trace,60天内
     *
     * @param traceId
     * @return
     */
    private static JSONObject getQueryRequest(String traceId, String appName) {
        JSONObject request = new JSONObject();
        JSONArray additionalFields = new JSONArray();
        JSONObject additionalField = new JSONObject();
        additionalField.put("fieldName", "trace_id");
        additionalField.put("fieldValue", traceId);
        additionalFields.add(additionalField);
        request.put("additionalField", additionalFields);
        request.put("page", 1);
        request.put("pageSize", 20);
        JSONObject time = new JSONObject();
        LocalDateTime now = LocalDateTime.now();
        time.put("start", now.plusDays(-60).with(LocalTime.MIN).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        time.put("end", now.with(LocalTime.MAX).format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        request.put("time", time);
        request.put("appName", appName);
        return request;
    }

    /**
     * 构建通用的header，主要是cookie
     *
     * @param cookie
     * @return
     */
    private static Map<String, String> getCommonHeader(String cookie) {

        Map<String, String> header = new HashMap<>();
//        header.put("cookie", cookie);
        return header;

    }


    private static boolean filterTypeForInterface(JSONObject simulatorRes, String type, String hsf) {
        if (StringUtils.isBlank(hsf)) {
            JSONObject configs = TradePriceType4InterfaceNameDiamondRunner.getConfig();
            if (MapUtils.isEmpty(configs)) {
                return false;
            }
            String value = configs.getString(type);
            for (String temp : value.split(",")) {
                if (temp.equalsIgnoreCase(simulatorRes.getString("interfaceName"))) {
                    return true;
                }
            }
        }
        return hsf.equalsIgnoreCase(simulatorRes.getString("interfaceName"));
    }


    /**
     * 要把科学计数法转化一下下。
     *
     * @param source
     * @return
     */
    private static String rebuildResponse(String source) {

        String temp = source;
        int start = temp.indexOf("\"number\":");
        while (start > 0) {

            String result = temp.substring(start + 9, temp.indexOf(",", start + 9));
            if (result.contains("E+")) {
                temp = temp.replace(result, new BigDecimal(result).doubleValue() + "");
            }
            start = temp.indexOf("\"number\":", start + 9);
        }


        return temp;
    }

    /**
     * 获取天启key
     *
     * @param traceId
     * @param header
     * @param type
     * @return
     */
    private static String getSimulatorId(String traceId, Map<String, String> header, String type, String hsf, String appName) {
        JSONObject jsonObject = null;
        try {

            String result =
                    HttpUtils.sendPostJson(TRACE_QUERY_PAGE_URL, getQueryRequest(traceId, appName).toJSONString(), header);
            log.info("getSimulatorId(): simulator res: " + result);
            try {
                JSONObject pageResult = JSONObject.parseObject(result);
                pageResult.getJSONObject("body");
                JSONArray data = pageResult.getJSONObject("body").getJSONArray("data");
                if (data.size() == 0) {
                    throw new RuntimeException("获取天启接口数据异常，请先确认trace在天启上能搜索成功");
                }
                //需要遍历出列表,满足列表对应的接口
                for (int i = 0; i < data.size(); i++) {
                    JSONObject pageListResult = data.getJSONObject(i);
                    if (filterTypeForInterface(pageListResult, type, hsf)) {
                        jsonObject = pageListResult;
                        break;
                    }
                }
            } catch (Exception e) {
                throw new RuntimeException(e.getMessage());
            }
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }

        if (jsonObject == null) {
            throw new RuntimeException("没有匹配到对应到接口方法，请确认输入的trace是否是配置的接口，联系人：磅蟹");
        }
        //获取dataId，作为详情请求结果

        String dataId = jsonObject.getString("id");
        return dataId;
    }


    public static JSONObject getSimulatorResult(String traceId, String hsf, String appName) {

        Map<String, String> header = getCommonHeader(null);

        // 根据trace和hsf获取天启id
        String simulatorKey = getSimulatorId(traceId, header, null, hsf, appName);

        //请求详情结果
        //子调用
        JSONObject subinvokes = getQuerySimulatorData(simulatorKey, "subinvokes", appName);

        //主调用
        JSONObject maininvokes = getQuerySimulatorData(simulatorKey, "mainInvoke", appName);

        JSONObject userData = getQuerySimulatorData(simulatorKey, "userData", appName);

        JSONObject result = new JSONObject();
        result.put("mainInvoke", maininvokes.getJSONObject("body"));
        result.put("subInvokes", subinvokes.getJSONObject("body"));
        result.put("mtop", userData);
        log.info("getSimulatorResult(): result: " + JSONObject.toJSONString(result));
        return result;
    }
}
