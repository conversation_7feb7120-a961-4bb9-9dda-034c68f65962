package com.aliexpress.databank.price.factory.impl.jsonpath;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.aliexpress.databank.price.annotation.TradePriceJsonPathFactoryAnnotation;
import com.aliexpress.databank.price.dto.common.TradePriceJsonPathDto;
import com.aliexpress.databank.price.dto.common.simulatordata.SimulatorJsonPathResponseDto;
import org.noear.snack.ONode;
import org.springframework.stereotype.Component;

@Component
@TradePriceJsonPathFactoryAnnotation(value = "inventoryServiceFactory")
public class InventoryServiceFactory extends AbstractOrderPriceRenderFactory {


    @Override
    public void handleResult(SimulatorJsonPathResponseDto result, TradePriceJsonPathDto tradePriceJsonPathDto, ONode target, ONode selectResult) {
        JSONArray selectResultJSON = JSON.parseArray(selectResult.toJson());
        //优惠根据key来获取，所以要选则出对应skuid和key的关系，在params里面
        String paramsPath = tradePriceJsonPathDto.getExt().get("params");
    }
}
