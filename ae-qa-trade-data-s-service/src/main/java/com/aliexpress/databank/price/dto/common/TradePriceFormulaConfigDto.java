package com.aliexpress.databank.price.dto.common;


import com.aliexpress.databank.price.constant.TradePriceConfigTypeEnum;
import lombok.Data;
import lombok.Setter;
import org.apache.commons.collections4.MapUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class TradePriceFormulaConfigDto {

    @Setter
    private Map<String, List<TradePriceFormulaDto>> tradePriceFormulaConfigList;

    public List<TradePriceFormulaDto> getConfigByEnum(TradePriceConfigTypeEnum tradePriceConfigTypeEnum) {
        if (MapUtils.isEmpty(tradePriceFormulaConfigList)) {
            return new ArrayList<>();
        }
        return tradePriceFormulaConfigList.get(tradePriceConfigTypeEnum.name());
    }
}
