package com.aliexpress.databank.price.factory.impl.calculate;

import com.alibaba.fastjson.JSONObject;
import com.aliexpress.databank.price.dto.common.render.common.PriceCalculateDto;
import com.aliexpress.databank.price.dto.common.simulatordata.data.PromotionInfoDto;
import com.aliexpress.databank.price.dto.common.simulatordata.data.TradePriceItemDto;
import com.aliexpress.databank.price.factory.TradePriceSingleCalculateFactory;
import com.aliexpress.databank.utils.FundCalculator;
import org.apache.commons.collections4.CollectionUtils;

import javax.money.MonetaryAmount;
import java.util.List;

public class ItemUnitFeeFactory implements TradePriceSingleCalculateFactory {

    @Override
    public PriceCalculateDto calculate(List<TradePriceItemDto> tradePriceItemDtos) throws Exception {
        TradePriceItemDto tradePriceItemDto = tradePriceItemDtos.get(0);
        PriceCalculateDto priceCalculateDto = new PriceCalculateDto();
        //金额不能为null
        MonetaryAmount price = tradePriceItemDto.getPrice();
        if (price == null) {
            throw new Exception();
        }
        //公式 (icPrice*(1+rate)-itemSingleDiscountFee)*ExRate
        priceCalculateDto.setFormulaOfCalculate("(icPrice*(1+3cRate)-itemSingleDiscountFee/quality)*ExRate");
        JSONObject detail = new JSONObject();
        priceCalculateDto.setDetail(detail);
        detail.put("icPrice", tradePriceItemDto.getPrice().toString());
        detail.put("3cRate", tradePriceItemDto.getRate());
        detail.put("quality", tradePriceItemDto.getQuality());

        price = price.multiply(1 + (tradePriceItemDto.getRate() == null ? 0 : tradePriceItemDto.getRate().doubleValue()));
        List<PromotionInfoDto> list = tradePriceItemDto.getItemSinglePromotions();
        if (CollectionUtils.isNotEmpty(list)) {
            for (PromotionInfoDto promotionInfoDto : list) {
                //单品优惠营销返回是全部金额，所以需要单独除数量
                price = price.subtract(promotionInfoDto.getDiscountFeeForBase().divide(tradePriceItemDto.getQuality()));
            }
            detail.put("itemSingleDiscountFee", list);
        }

        price = FundCalculator.calculateAmountByExchange(tradePriceItemDto.getExchangeInfoDto().getRate(), price, tradePriceItemDto.getExchangeInfoDto().getQutoCurrency(), Boolean.TRUE);
        priceCalculateDto.setPrice(price.toString());
        return priceCalculateDto;
    }


}
