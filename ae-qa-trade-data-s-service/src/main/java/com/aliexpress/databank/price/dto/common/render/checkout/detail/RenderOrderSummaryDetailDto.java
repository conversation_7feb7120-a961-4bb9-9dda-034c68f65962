package com.aliexpress.databank.price.dto.common.render.checkout.detail;

import com.aliexpress.databank.price.dto.common.render.common.PriceCalculateDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class RenderOrderSummaryDetailDto {
    /**
     * 店铺订单金额总和
     */
    private PriceCalculateDto storeTotalFee;


    /**
     * 金币优惠
     */
    private PriceCalculateDto coinFee;

    /**
     * 平台coupon优惠
     */
    private PriceCalculateDto platformCouponFee;

    /**
     * 平台code优惠
     */
    private PriceCalculateDto platformCodeFee;

    /**
     * 总金额
     */
    private PriceCalculateDto totalFee;

}
