package com.aliexpress.databank.price.dto.response;

import com.alibaba.global.money.Money;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class TradeItemFee {


    /**
     * 主订单id
     */
    private Long tradeOrderId;

    /**
     * 子订单id
     */
    private Long tradeOrderLineId;

    /**
     * 商品单价
     */
    private Money uniteFee;

    /**
     * 运费
     */
    private Money shippingFee;

    /**
     * 税费
     */
    private Money taxFee;

    /**
     * 商品非金本位优惠
     */
    private Money itemNoGoldDiscountFee;

    /**
     * 商品金本位优惠
     */
    private Money itemGoldDiscountFee;

    /**
     * 商品运费优惠
     */
    private Money shippingDiscountFee;

    /**
     * 调价
     */
    private Money adjustFee;

    /**
     * 订单金额
     */
    private Money orderMount;

    /**
     * 订单实际支付金额
     */
    private Money actualFee;

    /**
     * 订单原始支付金额
     */
    private Money originalActualFee;

    /**
     * 商品调价费用
     */
    private Money gaf;

    /**
     * 运费调价费用
     */
    private Money paf;
}
