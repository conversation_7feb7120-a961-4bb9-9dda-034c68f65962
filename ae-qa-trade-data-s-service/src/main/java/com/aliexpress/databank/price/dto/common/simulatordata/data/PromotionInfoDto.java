package com.aliexpress.databank.price.dto.common.simulatordata.data;

import com.aliexpress.databank.price.constant.PromotionMoldEnum;
import com.aliexpress.databank.price.constant.PromotionTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.money.MonetaryAmount;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PromotionInfoDto {

    /**
     * 报价币种的优惠信息
     */
    private MonetaryAmount discountFeeForBase;

    /**
     * 意向币种的优惠信息
     */
    private MonetaryAmount discountFeeForQuote;

    /**
     * 营销工具类型
     */
    private String toolCode;

    /**
     * 类型:运费/商品
     */
    private PromotionTypeEnum type;

    /**
     * 是否平台出资
     */
    private boolean isPlatform = false;


    /**
     * 优惠类型，跨店，商品，店铺
     */
    private PromotionMoldEnum promotionMold;
    /**
     * 是否单品优惠
     */
    private boolean isSingle;
    /**
     * 渠道库存
     */
    private String channelCode;


}
