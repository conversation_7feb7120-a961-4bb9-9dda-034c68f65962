package com.aliexpress.databank.price.dto.common.render.checkout.detail;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 金额渲染店铺模块
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class RenderShopDetailDto {
    /**
     * 商品相关的模块
     */
    private List<RenderItemDetailDto> items;

    /**
     * 卖家id
     */
    private String sellerId;

    /**
     * 店铺级别的渲染
     */
    private RenderSummaryDetailDto renderSummaryDetailDto;


}
