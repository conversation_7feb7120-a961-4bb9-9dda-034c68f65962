package com.aliexpress.databank.price.dto.common.simulatordata.data;

import com.alibaba.fastjson.annotation.JSONField;
import com.aliexpress.databank.price.constant.Promotion4TradeTypeEnum;
import com.aliexpress.databank.price.constant.PromotionTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

import javax.money.MonetaryAmount;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TradePriceItemDto {

    /**
     * 商品标题
     */
    private String title;

    /**
     * 商品链接
     */
    private String url;

    /**
     * 商品对应的sku
     */
    private String sku;

    /**
     * 图片
     */
    private String pic;

    /**
     * 购物车id
     */
    private String cartId;

    /**
     * 商品id
     */
    private String productId;

    /**
     * sku属性
     */
    private String skuId;

    /**
     * 价格
     */
    private MonetaryAmount price;

    /**
     * 数量
     */
    private int quality;

    /**
     * 3c汇率
     */
    private BigDecimal rate;


    /**
     * 卖家id
     */
    private String sellerId;

    /**
     * 报价币种
     */
    private String baseCurrency;

    /**
     * 意向币种
     */
    private String quoteCurrency;

    /**
     * 币种转化汇率
     */
    private ExchangeInfoDto exchangeInfoDto;

    /**
     * 价格中心数据信息
     */

    @JSONField(serialize = false)
    private PriceCenterInfoDto priceCenterInfoDto;


    /**
     * 运费相关
     */
    private ShippingFeeDto shippingFeeDto;

    /**
     * 是否是量价
     */
    private boolean isDp = false;

    /**
     * 下单命中总优惠明细，量价指量价优惠，非量价是默认优惠
     */
    @JSONField(serialize = false)
    private List<PromotionInfoDto> promotionInfoDtos = new ArrayList<>();


    /**
     * 聚合优惠明细(包含，量价优惠，原价优惠，单品优惠计算)
     */
    private List<PromotionDetailDto> promotionDetailDtos = new ArrayList<>();


    /**
     * 单品优惠
     */
    @JSONField(serialize = false)
    private List<PromotionInfoDto> itemSinglePromotions;

    /**
     * item非金本位优惠
     */
    @JSONField(serialize = false)
    private List<PromotionInfoDto> itemPromotions;

    /**
     * item金本位优惠
     */
    @JSONField(serialize = false)
    private List<PromotionInfoDto> itemGoldPromotions;

    /**
     * 金本位优惠
     */
    @JSONField(serialize = false)
    private List<PromotionInfoDto> goldPromotions;


    /**
     * 运费优惠
     */
    @JSONField(serialize = false)
    private List<PromotionInfoDto> shippingPromotions;

    /**
     * id标示，用于获取币种
     */
    private String id;

    /**
     * 税率
     */
    private BigDecimal taxRate;


    /**
     * 税相关的数据
     */
    @JSONField(serialize = false)
    private List<TaxDetailDto> taxDetails;

//    /**
//     * 交易给营销的原价
//     */
//    private MonetaryAmount calRetailPrice;
//
//    /**
//     * 交易给营销的售价
//     */
//    private MonetaryAmount calSalePrice;

    /**
     * 交易计算后的物流折后价
     */
    private MonetaryAmount calDiscountPrice;


    /**
     * 获取商品的单品优惠
     *
     * @return
     */
    public List<PromotionInfoDto> getItemSinglePromotions() {
        List<PromotionInfoDto> list = new ArrayList<>();
        if (CollectionUtils.isEmpty(promotionInfoDtos)) {
            return list;
        }
        for (PromotionInfoDto promotionInfoDto : promotionInfoDtos) {
            if (promotionInfoDto.isSingle()) {
                list.add(promotionInfoDto);
            }
        }
        return list;
    }


    public List<PromotionInfoDto> getItemSinglePromotions(List<PromotionInfoDto> promotionInfoDtos) {
        List<PromotionInfoDto> list = new ArrayList<>();
        if (CollectionUtils.isEmpty(promotionInfoDtos)) {
            return list;
        }
        for (PromotionInfoDto promotionInfoDto : promotionInfoDtos) {
            if (promotionInfoDto.isSingle()) {
                list.add(promotionInfoDto);
            }
        }
        return list;
    }



    /**
     * 获取商品的运费优惠
     *
     * @return
     */
    public List<PromotionInfoDto> getShippingPromotions() {
        List<PromotionInfoDto> list = new ArrayList<>();
        if (CollectionUtils.isEmpty(promotionInfoDtos)) {
            return list;
        }
        for (PromotionInfoDto promotionInfoDto : promotionInfoDtos) {
            if (PromotionTypeEnum.SHIPPING.equals(promotionInfoDto.getType())) {
                list.add(promotionInfoDto);
            }
        }
        return list;
    }


    /**
     * 获取商品的非单品优惠
     *
     * @return
     */
    public List<PromotionInfoDto> getItemPromotions() {
        List<PromotionInfoDto> list = new ArrayList<>();
        if (CollectionUtils.isEmpty(promotionInfoDtos)) {
            return list;
        }
        for (PromotionInfoDto promotionInfoDto : promotionInfoDtos) {
            if (!PromotionTypeEnum.SHIPPING.equals(promotionInfoDto.getType()) && !promotionInfoDto.isSingle()) {
                list.add(promotionInfoDto);
            }
        }
        return list;
    }


    /**
     * 获取商品的非单品优惠的金本位优惠
     *
     * @return
     */
    public List<PromotionInfoDto> getGoldPromotions() {
        List<PromotionInfoDto> list = new ArrayList<>();
        if (CollectionUtils.isEmpty(promotionInfoDtos)) {
            return list;
        }
        for (PromotionInfoDto promotionInfoDto : promotionInfoDtos) {
            if (!promotionInfoDto.isSingle() && promotionInfoDto.isPlatform()) {
                list.add(promotionInfoDto);
            }
        }
        return list;
    }

    /**
     * 获取商品的非单品优惠的金本位优惠
     *
     * @return
     */
    public List<PromotionInfoDto> getItemGoldPromotions() {
        List<PromotionInfoDto> list = new ArrayList<>();
        if (CollectionUtils.isEmpty(promotionInfoDtos)) {
            return list;
        }
        for (PromotionInfoDto promotionInfoDto : promotionInfoDtos) {
            if (!PromotionTypeEnum.SHIPPING.equals(promotionInfoDto.getType()) && !promotionInfoDto.isSingle() && promotionInfoDto.isPlatform()) {
                list.add(promotionInfoDto);
            }
        }
        return list;
    }


    public PromotionDetailDto getPromotionDetail4Enum(Promotion4TradeTypeEnum promotion4TradeTypeEnum) {
        if (CollectionUtils.isEmpty(promotionDetailDtos)) {
            promotionDetailDtos = new ArrayList<>();
        }

        return promotionDetailDtos.stream().filter(promotionDetailDto ->
                promotion4TradeTypeEnum.equals(promotionDetailDto.getType())
        ).findFirst().orElse(null);

    }

}
