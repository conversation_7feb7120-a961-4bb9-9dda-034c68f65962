package com.aliexpress.databank.price.dto.common.simulatordata.data;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import javax.money.MonetaryAmount;
import java.util.ArrayList;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PriceCenterInfoDto {

    /**
     * 原价相关数据
     */
    private ProductPriceDto originPrice;


    /**
     * 量价相关数据
     */
    private ProductPriceDto dynamicPrice;

    /**
     * 是否是量价
     */
    private boolean isDp;

    /**
     * 是否新价格体系
     */
    private boolean isNewPrice;


    /**
     * 是否含税价
     */
    private String isIncTax;




    /**
     * 优惠明细  ,如果这个是量价，则存放原价的优惠数据
     */
    @JSONField(serialize = false)
    private List<PromotionInfoDto> promotionInfoDtos = new ArrayList<>();


    /**
     * 获取价格
     * @return
     */
    public MonetaryAmount getPrice() {
        if (isDp) {
            return this.dynamicPrice.getOriginPrice();
        }
         return this.originPrice.getOriginPrice();
    }


}
