package com.aliexpress.databank.price.factory.impl.adapt;

import com.alibaba.global.money.Money;
import com.aliexpress.databank.price.constant.Promotion4TradeTypeEnum;
import com.aliexpress.databank.price.dto.common.CheckFiledDTO;
import com.aliexpress.databank.price.dto.common.simulatordata.SimulatorJsonPathResponseDto;
import com.aliexpress.databank.price.dto.common.simulatordata.data.PromotionDetailDto;
import com.aliexpress.databank.price.dto.common.simulatordata.data.PromotionInfoDto;
import com.aliexpress.databank.price.dto.common.simulatordata.data.TradePriceItemDto;
import org.apache.commons.collections4.CollectionUtils;

import javax.money.MonetaryAmount;
import java.util.ArrayList;
import java.util.List;

import static com.aliexpress.databank.price.utils.TradeItemPriceUtils.getAllItem;

/**
 * 子调用参数check
 */
public class CheckSubInvokeFactory {

    public  static List<CheckFiledDTO> checkSubInvoke(SimulatorJsonPathResponseDto result) throws Exception{
        List<CheckFiledDTO> checkFiledDTO = new ArrayList<>();

        List<TradePriceItemDto> list = getAllItem(result.getPriceItems());
        for(TradePriceItemDto tradePriceItemDto:list){
            //check 第一次item优惠计算，retailPrice(原价)& salePrice（售价）
            checkFiledDTO.add(checkPromotionParams(tradePriceItemDto));
            //check Fulfillment discountPrice(物流折扣价)
            checkFiledDTO.add(checkFulfillmentPrice(tradePriceItemDto));

        }

        return checkFiledDTO;

    }

    private static CheckFiledDTO checkFulfillmentPrice(TradePriceItemDto tradePriceItemDto) {
        CheckFiledDTO c = new CheckFiledDTO();
        c.setProductId(tradePriceItemDto.getProductId());
        c.setSkuId(tradePriceItemDto.getSkuId());
        c.setFiled("discountPrice（物流折扣价）");
        c.setCalRule("给表达的运费计算的价格检查，原价减去单品优惠金额(retailPrice-itemSinglePromotions/件数)");

        if(CollectionUtils.isEmpty(tradePriceItemDto.getItemSinglePromotions())){
            if(tradePriceItemDto.getCalDiscountPrice().isEqualTo(tradePriceItemDto.getPriceCenterInfoDto().getOriginPrice().getOriginPrice())){
                c.setCalFormula(tradePriceItemDto.getCalDiscountPrice()+"="+tradePriceItemDto.getPriceCenterInfoDto().getOriginPrice().getOriginPrice());
                c.setCalResult(true);
            }else{
                c.setCalFormula(tradePriceItemDto.getCalDiscountPrice()+"!="+tradePriceItemDto.getPriceCenterInfoDto().getOriginPrice().getOriginPrice());
                c.setCalResult(false);
            }

        }else{
            MonetaryAmount itemSingleDiscount =  Money.zero(tradePriceItemDto.getBaseCurrency());
            //第一次营销优惠计算单品优惠总和/件

            for(PromotionDetailDto promotionDetailDto:tradePriceItemDto.getPromotionDetailDtos()){
                if(promotionDetailDto.getType().equals(Promotion4TradeTypeEnum.item)){
                    List<PromotionInfoDto> itemSingleDiscounts = tradePriceItemDto.getItemSinglePromotions(promotionDetailDto.getPromotionInfoDtos());

                    for(PromotionInfoDto promotionInfoDto :itemSingleDiscounts){

                        itemSingleDiscount=itemSingleDiscount.add(promotionInfoDto.getDiscountFeeForBase());

                    }
                }

            }
            //除以件数
            itemSingleDiscount = itemSingleDiscount.divide(tradePriceItemDto.getQuality());

            if(tradePriceItemDto.getCalDiscountPrice().isEqualTo(tradePriceItemDto.getPriceCenterInfoDto().getOriginPrice().getOriginPrice().subtract(itemSingleDiscount))){
                c.setCalFormula(tradePriceItemDto.getCalDiscountPrice()+"="+tradePriceItemDto.getPriceCenterInfoDto().getOriginPrice().getOriginPrice()+"-"+itemSingleDiscount);
                c.setCalResult(true);
            }else{
                c.setCalFormula(tradePriceItemDto.getCalDiscountPrice()+"!="+tradePriceItemDto.getPriceCenterInfoDto().getOriginPrice().getOriginPrice()+"-"+itemSingleDiscount);
                c.setCalResult(false);
            }

        }
        return c;

    }

    public static CheckFiledDTO checkPromotionParams(TradePriceItemDto tradePriceItemDto) {

        CheckFiledDTO c = new CheckFiledDTO();
        c.setProductId(tradePriceItemDto.getProductId());
        c.setSkuId(tradePriceItemDto.getSkuId());
        c.setFiled("retailPrice(原价)& salePrice（售价）");
        c.setCalRule("给第一次营销单品计算的价格检查，始终给原价(retailPrice = originPrice  & salePrice 逻辑一样)");
        for(PromotionDetailDto promotionDetailDto :tradePriceItemDto.getPromotionDetailDtos()){
            if(Promotion4TradeTypeEnum.item.equals(promotionDetailDto.getType())){

                if(tradePriceItemDto.getPriceCenterInfoDto().getOriginPrice().getOriginPrice()!=null&&tradePriceItemDto.getPriceCenterInfoDto().getOriginPrice().getSalePrice()!=null){
                    if (promotionDetailDto.getCalRetailPrice().isEqualTo(
                            tradePriceItemDto.getPriceCenterInfoDto().getOriginPrice().getOriginPrice())
                            && promotionDetailDto.getCalSalePrice().isEqualTo(
                            tradePriceItemDto.getPriceCenterInfoDto().getOriginPrice().getSalePrice())) {
                        c.setCalFormula(promotionDetailDto.getCalRetailPrice() + "=" + tradePriceItemDto.getPriceCenterInfoDto().getOriginPrice().getOriginPrice() +" and"+promotionDetailDto.getCalSalePrice() + "=" + tradePriceItemDto.getPriceCenterInfoDto().getOriginPrice().getSalePrice());
                        c.setCalResult(true);
                    } else {
                        c.setCalFormula(promotionDetailDto.getCalRetailPrice() + "!=" + tradePriceItemDto.getPriceCenterInfoDto().getOriginPrice().getOriginPrice() +" or"+promotionDetailDto.getCalSalePrice() + "!=" + tradePriceItemDto.getPriceCenterInfoDto().getOriginPrice().getSalePrice());
                        c.setCalResult(false);
                    }
                }else{
                    //无SalePrice，取OriginPrice
                    if (promotionDetailDto.getCalRetailPrice().isEqualTo(
                            tradePriceItemDto.getPriceCenterInfoDto().getOriginPrice().getOriginPrice())
                            && promotionDetailDto.getCalSalePrice().isEqualTo(
                            tradePriceItemDto.getPriceCenterInfoDto().getOriginPrice().getOriginPrice())) {
                        c.setCalFormula(promotionDetailDto.getCalRetailPrice() + "=" + tradePriceItemDto.getPriceCenterInfoDto().getOriginPrice().getOriginPrice() +" and"+promotionDetailDto.getCalSalePrice() + "=" + tradePriceItemDto.getPriceCenterInfoDto().getOriginPrice().getOriginPrice());
                        c.setCalResult(true);
                    } else {
                        c.setCalFormula(promotionDetailDto.getCalRetailPrice() + "!=" + tradePriceItemDto.getPriceCenterInfoDto().getOriginPrice().getOriginPrice() +" or"+promotionDetailDto.getCalSalePrice() + "!=" + tradePriceItemDto.getPriceCenterInfoDto().getOriginPrice().getOriginPrice());
                        c.setCalResult(false);
                    }
                }


            }

        }

        return c;

    }



}
