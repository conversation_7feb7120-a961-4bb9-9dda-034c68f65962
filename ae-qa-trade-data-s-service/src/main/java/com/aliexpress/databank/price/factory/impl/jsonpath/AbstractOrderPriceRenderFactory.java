package com.aliexpress.databank.price.factory.impl.jsonpath;

import com.alibaba.fastjson.JSON;
import com.aliexpress.databank.diamond.AuthDiamondRunner;
import com.aliexpress.databank.price.dto.common.TradePriceJsonPathDto;
import com.aliexpress.databank.price.dto.common.simulatordata.SimulatorJsonPathResponseDto;
import com.aliexpress.databank.price.factory.TradePriceJsonPathFactory;
import com.jayway.jsonpath.JsonPath;
import com.taobao.eagleeye.EagleEye;
import org.apache.commons.lang3.StringUtils;
import org.noear.snack.ONode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public abstract class AbstractOrderPriceRenderFactory implements TradePriceJsonPathFactory {

    private static final Logger logger = LoggerFactory.getLogger(AbstractOrderPriceRenderFactory.class);

    @Override
    public void invoke(SimulatorJsonPathResponseDto result, TradePriceJsonPathDto tradePriceJsonPathDto, ONode target) {

        ONode selectResult = getJSONpathResult(tradePriceJsonPathDto, target);
        if (selectResult == null || selectResult.isNull()) {
            //打个日志
            logger.warn("jsonPath factory get result error: factory:{},jsonPath:{},traceId:{}", tradePriceJsonPathDto.getFactory(), tradePriceJsonPathDto.getJsonPath(), EagleEye.getTraceId());
            return;
        }
        //处理一下结果
        handleResult(result, tradePriceJsonPathDto, target, selectResult);

        afterResult(result, tradePriceJsonPathDto, target, selectResult);
    }


    /**
     * 预处理
     *
     * @param tradePriceJsonPathDto
     * @param target
     * @return
     */
    public ONode getJSONpathResult(TradePriceJsonPathDto tradePriceJsonPathDto, ONode target) {

        if (StringUtils.isEmpty(tradePriceJsonPathDto.getJsonPath())) {
            //打个日志
            return null;
        }
        ONode selectResult = null;
        if (!tradePriceJsonPathDto.isNotUseONode()) {
            selectResult = target.select(tradePriceJsonPathDto.getJsonPath());
        } else {
            //他大爷的，多个参数居然不支持jsonpath,只能用这个比较慢的玩意
            String jsonPath = JSON.toJSONString(JsonPath.read(target.toJson(), tradePriceJsonPathDto.getJsonPath()));
            selectResult = ONode.load(jsonPath);
        }
        if (selectResult == null) {
            //打个日志
            return null;
        }
        return selectResult;

    }

    /**
     * 实际处理逻辑
     *
     * @param result
     * @param tradePriceJsonPathDto
     * @param target
     * @param selectResult
     */
    public abstract void handleResult(SimulatorJsonPathResponseDto result, TradePriceJsonPathDto tradePriceJsonPathDto, ONode target, ONode selectResult);


    @Override
    public void afterResult(SimulatorJsonPathResponseDto result, TradePriceJsonPathDto tradePriceJsonPathDto, ONode target, ONode selectResult) {

    }
}
