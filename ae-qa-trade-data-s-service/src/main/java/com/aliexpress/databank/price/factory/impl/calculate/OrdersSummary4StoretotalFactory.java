package com.aliexpress.databank.price.factory.impl.calculate;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.global.money.Money;
import com.aliexpress.databank.price.constant.CalculateDetailEnum;
import com.aliexpress.databank.price.dto.common.render.common.PriceCalculateDto;
import com.aliexpress.databank.price.dto.common.simulatordata.data.PromotionInfoDto;
import com.aliexpress.databank.price.dto.common.simulatordata.data.TradePriceItemDto;
import com.aliexpress.databank.price.factory.TradePriceSingleCalculateFactory;
import com.aliexpress.databank.price.helper.CalculateDetailHelper;
import com.aliexpress.databank.price.utils.TradeItemPriceUtils;
import com.aliexpress.databank.utils.FundCalculator;
import org.apache.commons.collections4.CollectionUtils;

import javax.money.MonetaryAmount;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class OrdersSummary4StoretotalFactory implements TradePriceSingleCalculateFactory {


    @Override
    public PriceCalculateDto calculate(List<TradePriceItemDto> tradePriceItemDtos) throws Exception {
        PriceCalculateDto priceCalculateDto = new PriceCalculateDto();
        priceCalculateDto.setFormulaOfCalculate("sum(shopTotalFee)");

        TradePriceItemDto temp = tradePriceItemDtos.get(0);
        MonetaryAmount money = Money.zero(temp.getQuoteCurrency());

        Map<String, List<TradePriceItemDto>> map = TradeItemPriceUtils.groupBySellerId(tradePriceItemDtos);

        List<JSONObject> details = new ArrayList<>();
        for (List<TradePriceItemDto> list : map.values()) {
            //店铺的总和已经计算过了，直接调用工厂进行重新计算
            PriceCalculateDto itemTemp = CalculateDetailHelper.invokeByEnmum(CalculateDetailEnum.SHOP_SUMMARY_TOTAL_FEE, list);
            money = money.add(Money.of(itemTemp.getPrice()));
            details.add(itemTemp.getDetail());
        }
        JSONObject result = new JSONObject();
        result.put("shopTotalFee", details);

        priceCalculateDto.setPrice(money.toString());
        priceCalculateDto.setDetail(result);
        return priceCalculateDto;


    }
}
