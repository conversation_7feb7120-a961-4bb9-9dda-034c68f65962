package com.aliexpress.databank.price.factory.impl.jsonpath;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliexpress.databank.price.annotation.TradePriceJsonPathFactoryAnnotation;
import com.aliexpress.databank.price.context.TradePriceRenderContext;
import com.aliexpress.databank.price.dto.common.TradePriceJsonPathDto;
import com.aliexpress.databank.price.dto.common.simulatordata.SimulatorJsonPathResponseDto;
import com.aliexpress.databank.price.factory.TradePriceJsonPathFactory;
import org.apache.commons.lang3.StringUtils;
import org.noear.snack.ONode;
import org.noear.snack.core.DEFAULTS;
import org.springframework.stereotype.Component;

@Component
@TradePriceJsonPathFactoryAnnotation(value = "simpleJsonPathFactory")
public class SimpleJsonPathFactory extends AbstractOrderPriceRenderFactory {

    @Override
    public void handleResult(SimulatorJsonPathResponseDto result, TradePriceJsonPathDto tradePriceJsonPathDto, ONode target, ONode selectResult) {
        String jsonValue = selectResult.toJson();
        if (selectResult.isArray()) {
            result.getExtend().put(tradePriceJsonPathDto.getName(), JSONArray.parseArray(jsonValue));
        } else if (selectResult.isObject()) {
            result.getExtend().put(tradePriceJsonPathDto.getName(), JSONObject.parseObject(jsonValue));
        } else {
            result.getExtend().put(tradePriceJsonPathDto.getName(), selectResult.getString());
        }

    }
}
