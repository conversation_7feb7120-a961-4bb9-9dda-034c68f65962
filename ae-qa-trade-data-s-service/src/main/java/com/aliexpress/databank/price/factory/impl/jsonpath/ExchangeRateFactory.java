package com.aliexpress.databank.price.factory.impl.jsonpath;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliexpress.databank.price.annotation.TradePriceJsonPathFactoryAnnotation;
import com.aliexpress.databank.price.dto.common.TradePriceJsonPathDto;
import com.aliexpress.databank.price.dto.common.simulatordata.SimulatorJsonPathResponseDto;
import com.aliexpress.databank.price.dto.common.simulatordata.data.ExchangeInfoDto;
import com.aliexpress.databank.price.dto.common.simulatordata.data.TradePriceItemDto;
import com.aliexpress.databank.price.factory.TradePriceJsonPathFactory;
import com.jayway.jsonpath.JsonPath;
import org.apache.commons.lang3.StringUtils;
import org.noear.snack.ONode;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
@TradePriceJsonPathFactoryAnnotation(value = "exchangeRateFactory")
public class ExchangeRateFactory extends AbstractOrderPriceRenderFactory {



    @Override
    public void handleResult(SimulatorJsonPathResponseDto result, TradePriceJsonPathDto tradePriceJsonPathDto, ONode target, ONode selectResult) {
        String temp = selectResult.toJson();
        JSONArray rateInfos = JSONArray.parseArray(temp);


        for (int i = 0; i < rateInfos.size(); i++) {

            JSONObject rate = rateInfos.getJSONObject(i);
            //根据报价币种去匹配对应的汇率,存在$ref说明有重复，所以不需要处理
            if (rate==null||rate.containsKey("$ref")) {
                continue;
            }
            setExchangeInfoToPrice(rate, result.getPriceItems());

        }
    }


    private void setExchangeInfoToPrice(JSONObject rate, Map<String, TradePriceItemDto> priceItems) {
        for (TradePriceItemDto tradePriceItemDto : priceItems.values()) {
            if (!tradePriceItemDto.getBaseCurrency().equalsIgnoreCase(rate.getString("baseCurrency"))) {
                continue;
            }
            ExchangeInfoDto exchangeInfoDto = ExchangeInfoDto.builder()
                    .baseCurrency(tradePriceItemDto.getBaseCurrency())
                    .qutoCurrency(rate.getString("quoteCurrency"))
                    .rate(rate.getBigDecimal("rate")).build();
            tradePriceItemDto.setExchangeInfoDto(exchangeInfoDto);
        }
    }
}
