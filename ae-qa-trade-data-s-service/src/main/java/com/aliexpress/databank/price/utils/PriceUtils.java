package com.aliexpress.databank.price.utils;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.global.money.Money;
import com.alibaba.global.order.management.api.model.PromotionFeeInfoDTO;
import com.alibaba.global.order.management.api.model.TradeOrderDTO;
import com.alibaba.global.order.management.api.model.TradeOrderLineDTO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import javax.money.Monetary;
import javax.money.MonetaryAmount;
import java.math.BigDecimal;

public class PriceUtils {


    /**
     * 获取订单总额
     * @param tradeOrderDTO
     * @return
     */
    public static MonetaryAmount getOrderAmount(TradeOrderDTO tradeOrderDTO) {
        MonetaryAmount monetaryAmount=null;
        for(TradeOrderLineDTO tradeOrderLineDTO:tradeOrderDTO.getOrderLines()){
            if(monetaryAmount==null){
                monetaryAmount=getOrderLineOrderAmount(tradeOrderLineDTO);
            }else {
                monetaryAmount=monetaryAmount.add(getOrderLineOrderAmount(tradeOrderLineDTO));
            }
        }
        return monetaryAmount;
    }


    /**
     * 获取调价金额订单总额
     * @param tradeOrderDTO
     * @return
     */
    public static MonetaryAmount getOrderAdjustAmount(TradeOrderDTO tradeOrderDTO) {
        MonetaryAmount monetaryAmount=null;
        for(TradeOrderLineDTO tradeOrderLineDTO:tradeOrderDTO.getOrderLines()){
            if(monetaryAmount==null){
                monetaryAmount=tradeOrderLineDTO.getAdjustFee();
            }else {
                monetaryAmount=monetaryAmount.add(tradeOrderLineDTO.getAdjustFee());
            }
        }
        return monetaryAmount;
    }

    /**
     * 获取子订单的订单金额
     * @param tradeOrderLineDTO
     * @return
     */
    public  static MonetaryAmount getOrderLineOrderAmount(TradeOrderLineDTO tradeOrderLineDTO){

        String curryCode=tradeOrderLineDTO.getPayableFee().getCurrency().getCurrencyCode();
        //这边actualFee是意向币种的，所以最好是组装出来。或者直接转化
        MonetaryAmount orderAmount = getOrderLineActualFee(tradeOrderLineDTO,curryCode,false);
        //增加金本位优惠
        orderAmount = orderAmount.add(getGoldStandardFee(tradeOrderLineDTO,curryCode));
        if (tradeOrderLineDTO.getTaxActualFee()!=null) {
            orderAmount = orderAmount.subtract(tradeOrderLineDTO.getTaxActualFee());
        }
        return orderAmount;
    }


    /**
     * 获取子订单的的actualFee
     * @param tradeOrderLineDTO
     * @param curryCode
     * @return
     */
    public static MonetaryAmount getOrderLineActualFee(TradeOrderLineDTO tradeOrderLineDTO,String curryCode,boolean isOriginal){
        //actualFee获取公式，单价+shippingActualFee+taxFee-ItemDiscountFee+adjustFee
        MonetaryAmount monetaryAmount=Money.zero(curryCode);
        monetaryAmount=monetaryAmount.add(tradeOrderLineDTO.getUnitFee().multiply(tradeOrderLineDTO.getQuantity()))
                .add(tradeOrderLineDTO.getShippingActualFee())
                .add(tradeOrderLineDTO.getTaxActualFee())
                .subtract(getItemDiscountFee(tradeOrderLineDTO,curryCode));
        if(!isOriginal){
            //原始金额不存在调价
            monetaryAmount=monetaryAmount.add(tradeOrderLineDTO.getAdjustFee());
        }

        return monetaryAmount;
    }


    /**
     * 获取优惠中的金本位优惠
     * @param tradeOrderLineDTO
     * @return
     */
    public static MonetaryAmount getGoldStandardFee(TradeOrderLineDTO tradeOrderLineDTO,String curryCode){

        if(CollectionUtils.isEmpty(tradeOrderLineDTO.getOriginalSaleDiscountInfo())){
            return null;
        }
        MonetaryAmount monetaryAmount=Money.zero(curryCode);
        for(PromotionFeeInfoDTO promotionFeeInfoDTO:tradeOrderLineDTO.getOriginalSaleDiscountInfo()){
            monetaryAmount=monetaryAmount.add(promotionFeeInfoDTO.getGoldStandardDiscountFee());
        }

        return monetaryAmount;
    }

    /**
     * 获取优惠中的金本位优惠
     * @param tradeOrderLineDTO
     * @return
     */
    public static MonetaryAmount getNoGoldStandardFee(TradeOrderLineDTO tradeOrderLineDTO,String curryCode){

        if(CollectionUtils.isEmpty(tradeOrderLineDTO.getSaleDiscountInfo())){
            return null;
        }
        MonetaryAmount monetaryAmount=Money.zero(curryCode);
        for(PromotionFeeInfoDTO promotionFeeInfoDTO:tradeOrderLineDTO.getOriginalSaleDiscountInfo()){
            monetaryAmount=monetaryAmount.add(promotionFeeInfoDTO.getNonGoldStandardDiscountFee());
        }

        return monetaryAmount;
    }


    /**
     * 获取运费优惠中的非金本位优惠
     * @param tradeOrderLineDTO
     * @return
     */
    public static MonetaryAmount getShippingNoGoldStandardFee(TradeOrderLineDTO tradeOrderLineDTO,String curryCode){

        if(CollectionUtils.isEmpty(tradeOrderLineDTO.getShippingDiscountInfo())){
            return null;
        }
        MonetaryAmount monetaryAmount=Money.zero(curryCode);
        for(PromotionFeeInfoDTO promotionFeeInfoDTO:tradeOrderLineDTO.getOriginalShippingDiscountInfo()){
            monetaryAmount=monetaryAmount.add(promotionFeeInfoDTO.getNonGoldStandardDiscountFee());
        }


        return monetaryAmount;
    }

    /**
     * 获取子订单的金额优惠
     * @param tradeOrderLineDTO
     * @param curryCode
     * @return
     */
    public static MonetaryAmount getItemDiscountFee(TradeOrderLineDTO tradeOrderLineDTO,String curryCode){

        if(CollectionUtils.isEmpty(tradeOrderLineDTO.getSaleDiscountInfo())){
            return null;
        }
        MonetaryAmount monetaryAmount=Money.zero(curryCode);
        for(PromotionFeeInfoDTO promotionFeeInfoDTO:tradeOrderLineDTO.getOriginalSaleDiscountInfo()){
            monetaryAmount=monetaryAmount.add(promotionFeeInfoDTO.getDiscountFee());
        }

        return monetaryAmount;

    }

    public static Money convertMoney(JSONObject jsonObject, String key,boolean needDiv){

        JSONObject jsonObject1=jsonObject.getJSONObject(key);
        String currencyCode=jsonObject1.getString("currencyCode");
        if(StringUtils.isBlank(currencyCode)){
            currencyCode=jsonObject1.getString("currency");
        }
        BigDecimal bigDecimal=needDiv?jsonObject1.getBigDecimal("amount").divide(BigDecimal.valueOf(100)):jsonObject1.getBigDecimal("amount");
        Money money= Money.of(bigDecimal,currencyCode);
        return money;
    }
}
