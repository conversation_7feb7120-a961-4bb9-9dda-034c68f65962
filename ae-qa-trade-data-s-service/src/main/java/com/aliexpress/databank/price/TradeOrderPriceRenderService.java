package com.aliexpress.databank.price;

import com.alibaba.global.qa.dc.model.param.SystemDTO;
import com.alibaba.global.qa.dc.model.result.ResultDTO;
import com.aliexpress.databank.price.dto.common.render.common.BaseResult;
import com.aliexpress.databank.price.dto.common.render.common.PriceRenderResponse;

public interface TradeOrderPriceRenderService {

    BaseResult<PriceRenderResponse> getOrderRenderResult(String trace, String cookie, String type,String simulatorId) throws Exception;

    ResultDTO getOrderRenderResult (String params, SystemDTO systemDTO) throws Exception;


}
