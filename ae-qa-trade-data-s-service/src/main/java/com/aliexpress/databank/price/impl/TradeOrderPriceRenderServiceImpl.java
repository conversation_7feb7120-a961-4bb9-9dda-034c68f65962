package com.aliexpress.databank.price.impl;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.global.qa.dc.model.builder.QueryResultBuilder;
import com.alibaba.global.qa.dc.model.param.SystemDTO;
import com.alibaba.global.qa.dc.model.result.QueryResultUnit;
import com.alibaba.global.qa.dc.model.result.ResultDTO;
import com.aliexpress.databank.constant.Constant;
import com.aliexpress.databank.diamond.TradePriceConfigDiamondRunner;
import com.aliexpress.databank.price.TradeOrderPriceRenderService;
import com.aliexpress.databank.price.constant.TradePriceConfigTypeEnum;
import com.aliexpress.databank.price.context.TradePriceRenderContext;
import com.aliexpress.databank.price.dto.common.TradePriceConfigDto;
import com.aliexpress.databank.price.dto.common.render.checkout.CheckOutPriceRenderResponse;
import com.aliexpress.databank.price.dto.common.render.checkout.detail.RenderShopDetailDto;
import com.aliexpress.databank.price.dto.common.render.common.BaseResult;
import com.aliexpress.databank.price.dto.common.render.common.PriceRenderResponse;
import com.aliexpress.databank.price.helper.TradePriceJsonPathHelper;
import com.aliexpress.databank.price.helper.TradePriceRenderFactoryHelper;
import com.aliexpress.databank.price.utils.SimulatorDataUtils;
import com.aliexpress.databank.utils.HsfUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.noear.snack.ONode;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

@Service
@HSFProvider(serviceInterface = TradeOrderPriceRenderService.class)
@Slf4j
public class TradeOrderPriceRenderServiceImpl implements TradeOrderPriceRenderService {

    @Autowired
    private TradePriceJsonPathHelper tradePriceJsonPathHelper;

    @Override
    public ResultDTO getOrderRenderResult(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        String empId = systemDTO.getOperator();
        Map<String, QueryResultUnit> data = new LinkedHashMap<>();
        JSONObject jsonObject = JSONObject.parseObject(params);
        String traceId = jsonObject.getString(Constant.TRACE_ID);
        String simulatorKey = jsonObject.getString(Constant.SIMULATOR_KEY);
        String cookie = systemDTO.getCookies();
        //这边是jsonArray的格式

        BaseResult renderResult = getOrderRenderResult(traceId, handleCookies(cookie), TradePriceConfigTypeEnum.RENDER.toString(), simulatorKey);

        if (StringUtils.isNotEmpty(renderResult.getErrorMessage())) {
            result.setSuccess(false);
            result.setMessage(renderResult.getErrorMessage());
            result.setData("getOrderRenderResult failed");
            return result;
        }
        //目前只支持渲染，后续拆分
        CheckOutPriceRenderResponse checkOutPriceRenderResponse = (CheckOutPriceRenderResponse) renderResult.getResult();
        List<RenderShopDetailDto> shopModels = checkOutPriceRenderResponse.getShopModel();

        //下单渲染总金额
        Map<String, QueryResultUnit> orderSummary = QueryResultBuilder.buildQueryResult("下单渲染总金额", null, null, checkOutPriceRenderResponse.getOrderSummary());
        data.putAll(orderSummary);


        //主订单-按店铺拆单
        for (RenderShopDetailDto shopModel : shopModels) {
            Map<String, QueryResultUnit> sm = QueryResultBuilder.buildQueryResult("店铺" + shopModel.getSellerId(), null, getShopModelkey(), shopModel.getItems());
            data.putAll(sm);

            Map<String, QueryResultUnit> smt = QueryResultBuilder.buildQueryResult("店铺总金额" + shopModel.getSellerId(), null, null, shopModel.getRenderSummaryDetailDto());
            data.putAll(smt);

        }


        // 根据trace获取基础价格
        Map<String, QueryResultUnit> basePrice = QueryResultBuilder.buildQueryResult("子调用来源价格", null, null, checkOutPriceRenderResponse.getBody().getPriceItems());
        data.putAll(basePrice);

        result.setData(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat,SerializerFeature.DisableCircularReferenceDetect));
        result.setSuccess(true);
        HsfUtil.measureAll("/trade/jobId="+systemDTO.getSite(),empId);
        return result;
    }

    @Override
    public BaseResult<PriceRenderResponse> getOrderRenderResult(String trace, String cookie, String type, String simulatorId) {

        BaseResult<PriceRenderResponse> baseResult = new BaseResult<>();

        //通过天启获取到上下游到依赖
        JSONObject jsonObject = null;
        try {
            jsonObject = SimulatorDataUtils.getSimulatorResult(cookie, trace, type, simulatorId, "ae-trade-ns-s");
        } catch (Exception e) {
            baseResult.setErrorMessage(e.getMessage());
            return baseResult;
        }


        if (jsonObject == null) {
            baseResult.setErrorMessage("天启数据获取为空，请检测是否有采集");
            return baseResult;
        }

        ONode o = ONode.load(jsonObject.toJSONString());

        if (o == null) {
            return null;
        }
        TradePriceConfigDto tradePriceConfigDto = TradePriceConfigDiamondRunner.getConfig();
        if (tradePriceConfigDto == null) {
            baseResult.setErrorMessage("数据配置异常，请联系@磅蟹检测配置");
            return baseResult;
        }
        //构建上下文
        TradePriceConfigTypeEnum tradePriceConfigTypeEnum = TradePriceConfigTypeEnum.valueOf(type);
        TradePriceRenderContext tradePriceRenderContext = TradePriceRenderContext.builder()
                .priceJsonPathDtos(tradePriceConfigDto.getTradePriceForJsonPathConfigDto().getConfigByEnum(tradePriceConfigTypeEnum))
                .tradePriceConfigTypeEnum(tradePriceConfigTypeEnum)
                .simulatorNode(o)
                .build();
        //理论上使用责任链来支持，但是这边就直接循环调用吧
        try {
            tradePriceJsonPathHelper.handleJsonPathParse(tradePriceRenderContext);
        } catch (Exception e) {
            log.error("error",e);
            baseResult.setErrorMessage("下游返回值解析异常，请联系@磅蟹修复");
            return baseResult;
        }

        //获取到了原始数据，需要根据数据来还原页面

        try {
            PriceRenderResponse priceRenderResponse = TradePriceRenderFactoryHelper.getTradePriceRenderFactoryByEnum(tradePriceConfigTypeEnum).invoke(tradePriceRenderContext.getBody());

            baseResult.setResult(priceRenderResponse);
        } catch (Exception e) {
            e.printStackTrace();
            baseResult.setErrorMessage("金额计算异常，请联系@磅蟹修复");
            return baseResult;
        }


        return baseResult;
    }

    private List<String> getShopModelkey() {

        List<String> key = new ArrayList<>();
        key.add("title");
        key.add("itemId");
        key.add("sku");
        key.add("skuId");
        key.add("quality");
        key.add("uniteFee");
        key.add("shippingFee");
        return key;

    }

    public String handleCookies(String cookie) {
        StringBuilder stringBuilder = new StringBuilder();
        JSONArray temp = JSON.parseArray(cookie);
        for (int i = 0; i < temp.size(); i++) {
            JSONObject cookieDetail = temp.getJSONObject(i);

            stringBuilder.append(cookieDetail.getString("name")).append("=").append(cookieDetail.get("value")).append(";");

        }
        return stringBuilder.toString();
    }


}
