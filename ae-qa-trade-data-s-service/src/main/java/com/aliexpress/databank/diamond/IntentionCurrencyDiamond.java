package com.aliexpress.databank.diamond;

import com.alibaba.fastjson.JSONArray;
import com.aliexpress.databank.constant.Constant;
import com.taobao.diamond.client.Diamond;
import com.taobao.diamond.manager.ManagerListenerAdapter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Component
@Order(11)
public class IntentionCurrencyDiamond implements CommandLineRunner {

    private static final Logger logger = LoggerFactory.getLogger(IntentionCurrencyDiamond.class);


    private static String DATA_ID = "intention.currency.code";

    private static String GROUP_ID = "ae-qa-databank";

    @Override
    public void run(String... args) {
        try {
            String configInfo = Diamond
                    .getConfig(DATA_ID, GROUP_ID, ********);
            Constant.INTENTION_CURRENCY = JSONArray.parseArray(configInfo, String.class);
        } catch (IOException e) {
            e.printStackTrace();
        }

        // 启动用，并且变化需要立即推送最新值
        Diamond.addListener(DATA_ID, GROUP_ID,
                new ManagerListenerAdapter() {
                    public void receiveConfigInfo(String configInfo) {
                        try {
                            logger.info("Receive Intention Currency Info " + configInfo);
                            Constant.INTENTION_CURRENCY = JSONArray.parseArray(configInfo, String.class);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                });
    }

}
