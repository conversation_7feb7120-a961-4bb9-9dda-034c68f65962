package com.aliexpress.databank.diamond;

import com.aliexpress.databank.constant.Constant;
import com.aliexpress.databank.dataobject.OrderScenarioModule;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.taobao.diamond.client.Diamond;
import com.taobao.diamond.manager.ManagerListenerAdapter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * item configured
 */
@Component
@Order(11)
public class ReverseOrderModuleDiamondRunner implements CommandLineRunner {

    private static final Logger logger = LoggerFactory.getLogger(ReverseOrderModuleDiamondRunner.class);

    static final String DATA_ID = "com.alibaba.ae.qa.databank.order.scenario";

    @Override
    public void run(String... args) {
        try {
            String configInfo = Diamond
                    .getConfig(DATA_ID, Constant.DIAMOND_GROUP_ID, ********);
            Constant.ORDER_SCENARIOS = new Gson().fromJson(configInfo, new TypeToken<Map<String, List<OrderScenarioModule>>>() {
            }.getType());
        } catch (IOException e1) {
        }

        // 启动用，并且变化需要立即推送最新值
        Diamond.addListener(DATA_ID, Constant.DIAMOND_GROUP_ID, new ManagerListenerAdapter() {
            public void receiveConfigInfo(String configInfo) {
                try {
                    logger.info("Received Order Scenario Info: " + configInfo);
                    Constant.ORDER_SCENARIOS = new Gson().fromJson(configInfo, new TypeToken<Map<String, List<OrderScenarioModule>>>() {
                    }.getType());
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
    }
}
