package com.aliexpress.databank.diamond;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliexpress.databank.constant.Constant;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.taobao.diamond.client.Diamond;
import com.taobao.diamond.manager.ManagerListenerAdapter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.List;
import java.util.Map;

@Component
@Order(11)
public class TradePriceType4InterfaceNameDiamondRunner implements CommandLineRunner {
    private static final Logger logger = LoggerFactory.getLogger(TradePriceType4InterfaceNameDiamondRunner.class);

    private volatile static JSONObject TRADE_TYPE_INTERFACENAME = new JSONObject();

    @Override
    public void run(String... strings) throws Exception {
        try {
            String configInfo = Diamond
                    .getConfig("com.alibaba.ae.qa.databank.trade.type4InterfaceName", "ae-qa-databank", ********);
            TRADE_TYPE_INTERFACENAME = JSONObject.parseObject(configInfo);
        } catch (IOException e1) {
        }

        // 启动用，并且变化需要立即推送最新值
        Diamond.addListener("com.alibaba.ae.qa.databank.trade.type4InterfaceName", "ae-qa-databank",
                new ManagerListenerAdapter() {
                    @Override
                    public void receiveConfigInfo(String configInfo) {
                        try {
                            logger.info("Received type4InterfaceName Info: " + configInfo);
                            synchronized (TRADE_TYPE_INTERFACENAME) {
                                TRADE_TYPE_INTERFACENAME = JSONObject.parseObject(configInfo);
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                });
    }

    /**
     * 返回配置信息
     *
     * @return
     */
    public static JSONObject getConfig() {
        if (true) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("RENDER", "com.alibaba.global.buy.api.facade.CheckoutMultiterminalFacade@renderOrder(RenderOrderRequest):1.0.0,com.alibaba.global.buy.api.facade.wireless.WirelessCheckoutFacade@renderOrder(RenderQueryParams):1.0.0,c.a.g.b.a.f.w.WirelessCheckoutFacade@adjustRender(AdjustQueryParams):1.0.0,c.a.g.b.a.f.w.WirelessCheckoutFacade@renderOrder(RenderQueryParams):1.0.0.AE,c.a.g.b.a.f.CheckoutMultiterminalFacade@renderOrder(RenderOrderRequest):1.0.0.AER,c.a.g.b.a.f.CheckoutMultiterminalFacade@createOrder(CreateOrderRequest):1.0.0.AER,c.a.g.b.a.f.w.WirelessCheckoutFacade@createOrder(CreateQueryParams):1.0.0.AER");
            return jsonObject;
        }
        return TRADE_TYPE_INTERFACENAME;
    }
}
