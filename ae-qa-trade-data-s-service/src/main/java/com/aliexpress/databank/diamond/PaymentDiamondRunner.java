package com.aliexpress.databank.diamond;


import com.alibaba.fastjson.JSONObject;
import com.aliexpress.databank.constant.Constant;
import com.taobao.diamond.client.Diamond;
import com.taobao.diamond.manager.ManagerListenerAdapter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Component
@Order(11)
public class PaymentDiamondRunner  implements CommandLineRunner {

    private static final Logger logger = LoggerFactory.getLogger(PaymentDiamondRunner.class);

    private static final String DATA_ID = "com.alibaba.ae.qa.databank.payment";

    private static final String GROUP_ID = "ae-qa-databank";

    @Override
    public void run(String... strings) throws Exception {
        try {
            String configInfo = Diamond
                    .getConfig(DATA_ID, GROUP_ID, ********);
            JSONObject jsonObject = JSONObject.parseObject(configInfo);
            logger.info("配置：---------"+jsonObject);
            Constant.PAYMENT_METHOD_CHECK_GOODNAME = jsonObject.getJSONArray("goodsName").toJavaList(String.class);
            Constant.PAYMENT_METHOD_CHECK_GOODURL = jsonObject.getJSONArray("goodsUrl").toJavaList(String.class);
            Constant.PAYMENT_METHOD_SPLIT_ORDER = jsonObject.getJSONArray("splitOrder").toJavaList(String.class);
            Constant.PAYMENT_METHOD_META_DAYA = jsonObject.getJSONObject("meteData");
        }
            catch (IOException e1) {
        }

        // 启动用，并且变化需要立即推送最新值
        Diamond.addListener("com.alibaba.ae.qa.databank.payment", "ae-qa-databank",
                new ManagerListenerAdapter() {
                    public void receiveConfigInfo(String configInfo) {
                        try {
                            logger.info("Received Items Info: " + configInfo);
                            JSONObject jsonObject = JSONObject.parseObject(configInfo);
                            Constant.PAYMENT_METHOD_CHECK_GOODNAME = jsonObject.getJSONArray("goodName").toJavaList(String.class);
                            Constant.PAYMENT_METHOD_CHECK_GOODURL = jsonObject.getJSONArray("goodUrl").toJavaList(String.class);
                            Constant.PAYMENT_METHOD_SPLIT_ORDER = jsonObject.getJSONArray("splitOrder").toJavaList(String.class);
                            Constant.PAYMENT_METHOD_META_DAYA = jsonObject.getJSONObject("meteData");
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                });
    }
}
