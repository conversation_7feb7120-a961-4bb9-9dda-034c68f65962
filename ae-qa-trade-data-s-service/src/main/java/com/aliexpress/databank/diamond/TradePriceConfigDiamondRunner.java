package com.aliexpress.databank.diamond;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliexpress.databank.constant.Constant;
import com.aliexpress.databank.price.constant.TradePriceConfigTypeEnum;
import com.aliexpress.databank.price.dto.common.TradePriceConfigDto;
import com.aliexpress.databank.price.dto.common.TradePriceForJsonPathConfigDto;
import com.aliexpress.databank.price.dto.common.TradePriceJsonPathDto;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.taobao.diamond.client.Diamond;
import com.taobao.diamond.manager.ManagerListenerAdapter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Order(11)
public class TradePriceConfigDiamondRunner implements CommandLineRunner {
    private static final Logger logger = LoggerFactory.getLogger(TradePriceType4InterfaceNameDiamondRunner.class);

    private volatile static TradePriceConfigDto TRADE_PRICE_CONFIG = new TradePriceConfigDto();

    @Override
    public void run(String... strings) throws Exception {
        try {
            String configInfo = Diamond
                    .getConfig("com.alibaba.ae.qa.databank.trade.ConfigDiamond", "ae-qa-databank", ********);
            TRADE_PRICE_CONFIG = JSONObject.parseObject(configInfo, TradePriceConfigDto.class);
        } catch (IOException e1) {
        }

        // 启动用，并且变化需要立即推送最新值
        Diamond.addListener("com.alibaba.ae.qa.databank.trade.ConfigDiamond", "ae-qa-databank",
                new ManagerListenerAdapter() {
                    @Override
                    public void receiveConfigInfo(String configInfo) {
                        try {
                            logger.info("Received type4InterfaceName Info: " + configInfo);
                            synchronized (TRADE_PRICE_CONFIG) {
                                TRADE_PRICE_CONFIG = JSONObject.parseObject(configInfo, TradePriceConfigDto.class);
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                });
    }

    /**
     * 返回配置信息
     *
     * @return
     */
    public static TradePriceConfigDto getConfig() {
        if (true) {
            List<TradePriceJsonPathDto> list = new ArrayList<>();
            TradePriceConfigDto tradePriceConfigDto = new TradePriceConfigDto();
            TradePriceJsonPathDto tradePriceJsonPathDto = new TradePriceJsonPathDto();
            tradePriceJsonPathDto.setFactory("simpleJsonPathFactory");
            tradePriceJsonPathDto.setJsonPath("$.mainInvoke.params.[0].commonDTO.source.platformType.name");
            tradePriceJsonPathDto.setName("platform");
            list.add(tradePriceJsonPathDto);

            tradePriceJsonPathDto = new TradePriceJsonPathDto();
            tradePriceJsonPathDto.setFactory("simpleJsonPathFactory");
            tradePriceJsonPathDto.setJsonPath("$.mainInvoke.params.[0].extraParams.buyerCountry");
            tradePriceJsonPathDto.setName("shipTo");
            list.add(tradePriceJsonPathDto);

            tradePriceJsonPathDto = new TradePriceJsonPathDto();
            tradePriceJsonPathDto.setFactory("simpleJsonPathFactory");
            tradePriceJsonPathDto.setJsonPath("$.subInvokes['com.alibaba.global.address.api.facade.UserAddressReadFacade@listUserAddress(UserAddressQueryRequest)'].response.module[0][0].addressLocationTree.countryIsoCode");
            tradePriceJsonPathDto.setName("shipTo");
            list.add(tradePriceJsonPathDto);

            tradePriceJsonPathDto = new TradePriceJsonPathDto();
            tradePriceJsonPathDto.setFactory("simpleJsonPathFactory");
            tradePriceJsonPathDto.setJsonPath("$.subInvokes['com.alibaba.global.address.api.facade.UserAddressReadFacade@getSelectedUserAddress(SelectedAddressRequest)'].response.module[0][0].selectedUserAddress.addressLocationTree.countryIsoCode");
            tradePriceJsonPathDto.setName("shipTo");
            list.add(tradePriceJsonPathDto);


            tradePriceJsonPathDto = new TradePriceJsonPathDto();
            tradePriceJsonPathDto.setFactory("simpleJsonPathFactory");
            tradePriceJsonPathDto.setJsonPath("$.mainInvoke.params.[0].items");
            tradePriceJsonPathDto.setName("itemParam");
            list.add(tradePriceJsonPathDto);

            Map<String, String> ext = new HashMap<>();
            tradePriceJsonPathDto = new TradePriceJsonPathDto();
            tradePriceJsonPathDto.setFactory("iCPriceRenderFactory");
            tradePriceJsonPathDto.setJsonPath("$.subInvokes['com.alibaba.global.ic.api.CustomerProductServiceFacade@queryProduct(ProductQueryRequest)'].response.model..skuIdAndSkuMap");
            ext.put("params", "$.subInvokes['com.alibaba.global.ic.api.CustomerProductServiceFacade@queryProduct(ProductQueryRequest)'].response.model");
            tradePriceJsonPathDto.setExt(ext);
            list.add(tradePriceJsonPathDto);

            tradePriceJsonPathDto = new TradePriceJsonPathDto();
            tradePriceJsonPathDto.setFactory("fulfillmentServiceFactory");
            tradePriceJsonPathDto.setJsonPath("$.subInvokes['com.alibaba.global.expression.protocol.sdk.facade.FulfillmentServiceFacade@queryFulfillmentGroupService(QueryFulfillmentGroupServiceRequest)'][*].response.result..fulfillmentGroupResults[*]");
            ext = new HashMap<>();
            ext.put("params", "$.subInvokes['com.alibaba.global.expression.protocol.sdk.facade.FulfillmentServiceFacade@queryFulfillmentGroupService(QueryFulfillmentGroupServiceRequest)'].params[*]..fulfillmentGroups[*]");
            tradePriceJsonPathDto.setExt(ext);
            list.add(tradePriceJsonPathDto);

            tradePriceJsonPathDto = new TradePriceJsonPathDto();
            tradePriceJsonPathDto.setFactory("fulfillmentServiceFactory");
            tradePriceJsonPathDto.setJsonPath("$.subInvokes['com.alibaba.global.expression.protocol.sdk.facade.FulfillmentServiceFacade@queryFulfillmentGroupService(QueryFulfillmentGroupServiceRequest)'][*].response.result..fulfillmentGroupResults[*]");
            ext = new HashMap<>();
            ext.put("params", "$.subInvokes['com.alibaba.global.expression.protocol.sdk.facade.FulfillmentServiceFacade@queryFulfillmentGroupService(QueryFulfillmentGroupServiceRequest)'].params[*]..fulfillmentGroups[*]");
            tradePriceJsonPathDto.setExt(ext);
            list.add(tradePriceJsonPathDto);


            tradePriceJsonPathDto = new TradePriceJsonPathDto();
            tradePriceJsonPathDto.setFactory("calculatePayCurrencyFactory");
            tradePriceJsonPathDto.setJsonPath("$.subInvokes['com.alibaba.global.payment.api.facade.CashierFacade@calculatePayCurrency(CalculatePayCurrencyRequest)']..response..module.mergePayItemGroups..payCcy");
            ext = new HashMap<>();
            ext.put("params", "$.subInvokes['com.alibaba.global.payment.api.facade.CashierFacade@calculatePayCurrency(CalculatePayCurrencyRequest)']..params..calculatePayCurrencyItems[*]");
            tradePriceJsonPathDto.setExt(ext);
            list.add(tradePriceJsonPathDto);

            tradePriceJsonPathDto = new TradePriceJsonPathDto();
            tradePriceJsonPathDto.setFactory("priceCenterRenderFactory");
            tradePriceJsonPathDto.setJsonPath("$.subInvokes['com.alibaba.aliexpress.price.api.common.client.product.ProductPriceQueryClient@queryBatchProductPrice(BatchProductPriceQueryParamDTO)'].response.data[*].productPriceQueryResultMap.*");

            list.add(tradePriceJsonPathDto);
            tradePriceJsonPathDto = new TradePriceJsonPathDto();
            tradePriceJsonPathDto.setFactory("exchangeRateFactory");
            tradePriceJsonPathDto.setJsonPath("$.subInvokes['com.taobao.payment.exchangeplatform.client.api.ExchangeQueryService@queryExchangeRateFromCache(List\\,String\\,String)'][*].response.rateInfo");
            tradePriceJsonPathDto.setNotUseONode(true);
            list.add(tradePriceJsonPathDto);


            tradePriceJsonPathDto = new TradePriceJsonPathDto();
            tradePriceJsonPathDto.setFactory("promiseResultsJsonPathFactory");
            tradePriceJsonPathDto.setJsonPath("$.subInvokes['com.alibaba.intl.ae.biz.promisetemplate.share.service.interfaces.PromiseTemplateService@calculatePromiseResultsByProductList(List)']..response");
            list.add(tradePriceJsonPathDto);

            tradePriceJsonPathDto = new TradePriceJsonPathDto();
            tradePriceJsonPathDto.setFactory("promotionInfoFactory");
            tradePriceJsonPathDto.setJsonPath("$.subInvokes['com.alibaba.global.ump.protocol.sdk.facade.GlobalTradePromotionMultiCalculateFacade@calculatePromotion4Checkout(GlobalTradeMultiCalculateRequest)'][0].response.result");
            ext = new HashMap<>();
            ext.put("params", "$.subInvokes['com.alibaba.global.ump.protocol.sdk.facade.GlobalTradePromotionMultiCalculateFacade@calculatePromotion4Checkout(GlobalTradeMultiCalculateRequest)'][0].params..tradeItemClusters..tradeItems[*]");
            ext.put("type", "dp");
            tradePriceJsonPathDto.setExt(ext);
            list.add(tradePriceJsonPathDto);

            tradePriceJsonPathDto = new TradePriceJsonPathDto();
            tradePriceJsonPathDto.setFactory("promotionInfoFactory");
            tradePriceJsonPathDto.setJsonPath("$.subInvokes['com.alibaba.global.ump.protocol.sdk.facade.GlobalTradePromotionMultiCalculateFacade@calculatePromotion4Checkout(GlobalTradeMultiCalculateRequest)'][1].response.result");
            ext = new HashMap<>();
            ext.put("params", "$.subInvokes['com.alibaba.global.ump.protocol.sdk.facade.GlobalTradePromotionMultiCalculateFacade@calculatePromotion4Checkout(GlobalTradeMultiCalculateRequest)'][1].params..tradeItemClusters..tradeItems[*]");
            ext.put("type", "origin");
            tradePriceJsonPathDto.setExt(ext);
            list.add(tradePriceJsonPathDto);

            tradePriceJsonPathDto = new TradePriceJsonPathDto();
            tradePriceJsonPathDto.setFactory("promotionInfoFactory");
            tradePriceJsonPathDto.setJsonPath("$.subInvokes['com.alibaba.global.ump.protocol.sdk.facade.GlobalTradePromotionMultiCalculateFacade@calculateItemPromotion4Checkout(GlobalTradeMultiCalculateRequest)'][0].response.result");
            ext = new HashMap<>();
            ext.put("params", "$.subInvokes['com.alibaba.global.ump.protocol.sdk.facade.GlobalTradePromotionMultiCalculateFacade@calculateItemPromotion4Checkout(GlobalTradeMultiCalculateRequest)'][0].params..tradeItemClusters..tradeItems[*]");
            ext.put("type", "item");
            tradePriceJsonPathDto.setExt(ext);
            list.add(tradePriceJsonPathDto);



//
//            tradePriceJsonPathDto = new TradePriceJsonPathDto();
//            tradePriceJsonPathDto.setFactory("promotionInfoFactory");
//            tradePriceJsonPathDto.setJsonPath("$.body.onlineContext.subInvokes['com.alibaba.global.ump.protocol.sdk.facade.GlobalTradePromotionMultiCalculateFacade@calculatePromotion4Checkout(GlobalTradeMultiCalculateRequest)'].response.result");
//            ext = new HashMap<>();
//            ext.put("params", "$.body.onlineContext.subInvokes['com.alibaba.global.ump.protocol.sdk.facade.GlobalTradePromotionMultiCalculateFacade@calculatePromotion4Checkout(GlobalTradeMultiCalculateRequest)'].params..tradeItemClusters..tradeItems[*]");
//            tradePriceJsonPathDto.setExt(ext);
//            list.add(tradePriceJsonPathDto);


            tradePriceJsonPathDto = new TradePriceJsonPathDto();
            tradePriceJsonPathDto.setFactory("taxCalculateFactory");
            tradePriceJsonPathDto.setJsonPath("$.subInvokes['com.alibaba.global.finance.taxation.api.calculate.TaxCalculateFacade@batchCalculateTax(BatchCalculateTaxRequest)'].response..data[*]");
            list.add(tradePriceJsonPathDto);




            TradePriceForJsonPathConfigDto tradePriceForJsonPathConfigDto = new TradePriceForJsonPathConfigDto();
            Map<String, List<TradePriceJsonPathDto>> map = new HashMap<>();
            map.put(TradePriceConfigTypeEnum.RENDER.name(), list);
            tradePriceForJsonPathConfigDto.setTradePriceJsonPathDtoList(map);
            tradePriceConfigDto.setTradePriceForJsonPathConfigDto(tradePriceForJsonPathConfigDto);
            return tradePriceConfigDto;
        }
        return TRADE_PRICE_CONFIG;
    }
}
