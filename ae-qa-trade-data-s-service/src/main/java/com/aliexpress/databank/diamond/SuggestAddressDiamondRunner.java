package com.aliexpress.databank.diamond;

import com.aliexpress.databank.constant.Constant;
import com.taobao.diamond.client.Diamond;
import com.taobao.diamond.manager.ManagerListenerAdapter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * buyer and seller white list
 */
@Component
@Order(11)
public class SuggestAddressDiamondRunner implements CommandLineRunner {

    private static final Logger logger = LoggerFactory.getLogger(SuggestAddressDiamondRunner.class);

    @Override
    public void run(String... args) {
        try {
            String configInfo = Diamond
                    .getConfig("com.alibaba.ae.qa.databank.suggestAddress", "ae-qa-databank", ********);
            Constant.SUGGEST_ADDRESS = configInfo;
            logger.info("configinfo~~~~~~~"+configInfo);
        } catch (IOException e1) {
        }

        // 启动用，并且变化需要立即推送最新值
        Diamond.addListener("com.alibaba.ae.qa.databank.suggestAddress", "ae-qa-databank",
                new ManagerListenerAdapter() {
                    public void receiveConfigInfo(String configInfo) {
                        try {
                            logger.info("address Info " + configInfo);
                            Constant.SUGGEST_ADDRESS = configInfo;
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                });
    }

}
