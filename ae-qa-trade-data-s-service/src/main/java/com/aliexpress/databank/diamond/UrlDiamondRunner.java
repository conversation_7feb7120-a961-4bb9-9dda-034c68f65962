package com.aliexpress.databank.diamond;

import com.aliexpress.databank.constant.Constant;
import com.aliexpress.databank.dataobject.Auth;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.taobao.diamond.client.Diamond;
import com.taobao.diamond.manager.ManagerListenerAdapter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Map;
import java.util.Set;

/**
 * no need auth url  -- Constant.NO_NEED_AUTH
 * admins  -- Constant.ADMIN_AUTH
 * url which admins no need auth  -- Constant.ADMIN_AUTH
 */
@Component
@Order(11)
public class UrlDiamondRunner implements CommandLineRunner {

    private static final Logger logger = LoggerFactory.getLogger(UrlDiamondRunner.class);

    @Override
    public void run(String... args) {
        try {
            String configInfo = Diamond
                    .getConfig("com.alibaba.ae.qa.databank.admin", "ae-qa-databank", ********);
            Map<String, Set<String>> auths = new Gson().fromJson(configInfo, new TypeToken<Map<String, Set<String>>>() {
            }.getType());
            Auth.updateNoNeedAuthFilterMethod(auths.get(Constant.NO_NEED_AUTH));
            Auth.updateAuthNeedAuthFilterMethod(auths.get(Constant.ADMIN_AUTH));
            Auth.updateBuyerAuth(auths.get(Constant.BUYER_AUTH));
            Auth.updateSellerAuth(auths.get(Constant.SELLER_AUTH));

        } catch (IOException e1) {
        }

        // 启动用，并且变化需要立即推送最新值
        Diamond.addListener("com.alibaba.ae.qa.databank.admin", "ae-qa-databank",
                new ManagerListenerAdapter() {
                    public void receiveConfigInfo(String configInfo) {
                        try {
                            logger.info("Receive Admin Info " + configInfo);
                            Map<String, Set<String>> auths = new Gson().fromJson(configInfo, new TypeToken<Map<String, Set<String>>>() {
                            }.getType());
                            Auth.updateNoNeedAuthFilterMethod(auths.get(Constant.NO_NEED_AUTH));
                            Auth.updateAuthNeedAuthFilterMethod(auths.get(Constant.ADMIN_AUTH));
                            Auth.updateAdmin(auths.get(Constant.ADMIN));
                            Auth.updateBuyerAuth(auths.get(Constant.BUYER_AUTH));
                            Auth.updateSellerAuth(auths.get(Constant.SELLER_AUTH));
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                });
    }

}
