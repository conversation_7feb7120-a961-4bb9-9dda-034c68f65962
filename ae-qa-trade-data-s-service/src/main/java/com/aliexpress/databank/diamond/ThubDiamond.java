package com.aliexpress.databank.diamond;

import com.alibaba.fastjson.JSONObject;
import com.aliexpress.databank.constant.Constant;
import com.taobao.diamond.client.Diamond;
import com.taobao.diamond.manager.ManagerListenerAdapter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Component
@Order(11)
public class ThubDiamond implements CommandLineRunner {

    private static final Logger logger = LoggerFactory.getLogger(ThubDiamond.class);

    @Override
    public void run(String... args) {
        try {
            String configInfo = Diamond
                    .getConfig("thub.key", "ae-qa-databank", ********);
            Constant.THUB_INFO = JSONObject.parseObject(configInfo);
        } catch (IOException e) {
            e.printStackTrace();
        }

        // 启动用，并且变化需要立即推送最新值
        Diamond.addListener("thub.key", "ae-qa-databank",
                new ManagerListenerAdapter() {
                    public void receiveConfigInfo(String configInfo) {
                        try {
                            logger.info("Receive THUB Info " + configInfo);
                            Constant.THUB_INFO = JSONObject.parseObject(configInfo);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                });
    }

}
