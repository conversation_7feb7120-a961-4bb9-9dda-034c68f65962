package com.aliexpress.databank.diamond.hpy;

import com.alibaba.fastjson.JSON;
import com.aliexpress.databank.hsf.SendDingMsgService;
import com.google.common.collect.Lists;
import com.taobao.diamond.client.Diamond;
import com.taobao.diamond.manager.ManagerListenerAdapter;
import com.taobao.eagleeye.EagleEye;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.List;

/***
 * diamond
 * 还没有追偿成功的订单；
 * ipay 的 excel 消息体的全集；
 */
@Component
@Order(14)
@Slf4j
public class ChargeBackResultRuleList implements CommandLineRunner {

    private static final Logger logger = LoggerFactory.getLogger(ChargeBackResultRuleList.class);

    public static final String DATA_ID = "com.alibaba.ae.chargebackresult";

    public static final String GROUP_ID = "ae-qa-databank";

    /***
     * 财务的脚步，对账出来，还没有追偿成功的订单！！！
     */
    @Getter
    @Setter
    private List<String> targetDisputeIdList = Lists.newArrayList();

    /***
     * ipay 的excel
     */
    @Getter
    @Setter
    private static List<String> ruleList = Lists.newCopyOnWriteArrayList();

    @Getter
    @Setter
    private static List<String> chargeOpenList = Lists.newCopyOnWriteArrayList();

    @Override
    public void run(String... strings) throws Exception {
        try {
            String configInfo = Diamond.getConfig(DATA_ID, GROUP_ID, ********);
            logger.info("targetDisputeIdList1|" + EagleEye.getTraceId() +"|" + configInfo);

            String[] split = configInfo.split("\n");
            if (split.length != 0) {
                List<String> collect = Lists.newArrayList();
                for (int i = 0; i < split.length; i++) {
                    String s = split[i];
                    if (StringUtils.isNotBlank(s)) {
                        log.error("targetDisputeIdList#each#string|" + EagleEye.getTraceId() + "|" + i + "|" + s);
                        collect.add(s.trim());
                    }
                }
                targetDisputeIdList = collect;
                log.error("targetDisputeIdList#result|" + EagleEye.getTraceId() + "|" + JSON.toJSONString(targetDisputeIdList));
            }
        } catch (Throwable t) {
            log.error("targetDisputeIdList#result|" + EagleEye.getTraceId() + "|" + JSON.toJSONString(targetDisputeIdList));
        }

        log.error("ruleList#before|" + EagleEye.getTraceId() + "|");
        // 启动用，并且变化需要立即推送最新值
        Diamond.addListener(DATA_ID, "ae-qa-databank",
                new ManagerListenerAdapter() {
                    public void receiveConfigInfo(String configInfo) {
                        try {
                            logger.info("targetDisputeIdList222|" + EagleEye.getTraceId() +"|" + configInfo);
                            String[] split = configInfo.split("\n");
                            if (split.length != 0) {
                                List<String> collect = Lists.newArrayList();
                                for (int i = 0; i < split.length; i++) {
                                    String s = split[i];
                                    collect.add(s);
                                    log.error("targetDisputeIdList#each#string222|" + EagleEye.getTraceId() + "|" + i + "|" + s);
                                }
                                targetDisputeIdList = collect;
                            }
                            log.error("targetDisputeIdList#result222|" + EagleEye.getTraceId() + "|" + JSON.toJSONString(targetDisputeIdList));
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                });


        List<String> arr = Lists.newArrayList("_1", "_2", "_3");

        log.error("ruleList#before|" + EagleEye.getTraceId() + "|");

        arr.stream().forEach(i -> {
            Diamond.addListener(DATA_ID + i, "ae-qa-databank",
                    new ManagerListenerAdapter() {
                        public void receiveConfigInfo(String configInfo) {
                            synchronized (i) {
                                try {
                                    logger.info("ruleList#config|" + EagleEye.getTraceId() + "|" + i + "|" + configInfo);

                                    String[] split = configInfo.split("\n");
                                    if (split.length != 0) {
                                        List<String> collect = Lists.newArrayList();
                                        for (int i = 0; i < split.length; i++) {
                                            String s = split[i];
                                            if (StringUtils.isNotBlank(s)) {
                                                collect.add(s);
                                                logger.info("ruleList#each#string|" + EagleEye.getTraceId() + "|" + s);
                                            }
                                        }
                                        ruleList.addAll(collect);
                                        logger.info("ruleList#result1|" + EagleEye.getTraceId() + "|" + JSON.toJSONString(ruleList));
                                    }
                                } catch (Throwable e) {
                                    logger.error("ruleListParseError|" + i, e);
                                }
                                logger.info("ruleList#result2|" + EagleEye.getTraceId() + "|" + ruleList);
                            }
                            logger.info("ruleList#result#final|" + EagleEye.getTraceId() + "|" + ruleList);
                        }
                    });

        });


        log.error("ruleList#before#finally|" + EagleEye.getTraceId() + "|" + ruleList);


        log.error("chargeOpenList#before|" + EagleEye.getTraceId() + "|");
        // 启动用，并且变化需要立即推送最新值
        Diamond.addListener("com.alibaba.ae.chargebackopen", "ae-qa-databank",
                new ManagerListenerAdapter() {
                    public void receiveConfigInfo(String configInfo) {
                        try {
                            logger.info("chargeOpenList222|" + EagleEye.getTraceId() +"|" + configInfo);
                            String[] split = configInfo.split("\n");
                            if (split.length != 0) {
                                List<String> collect = Lists.newArrayList();
                                for (int i = 0; i < split.length; i++) {
                                    String s = split[i];
                                    collect.add(s);
                                    log.error("chargeOpenList#each#string222|" + EagleEye.getTraceId() + "|" + i + "|" + s);
                                }
                                chargeOpenList = collect;
                            }
                            log.error("chargeOpenList#result222|" + EagleEye.getTraceId() + "|" + JSON.toJSONString(chargeOpenList));
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                });

    }


}