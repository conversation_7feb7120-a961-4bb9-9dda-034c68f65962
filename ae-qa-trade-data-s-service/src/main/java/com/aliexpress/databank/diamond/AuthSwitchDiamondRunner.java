package com.aliexpress.databank.diamond;

import com.aliexpress.databank.constant.Constant;
import com.aliexpress.databank.dataobject.Auth;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.taobao.diamond.client.Diamond;
import com.taobao.diamond.manager.ManagerListenerAdapter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.Map;
import java.util.Set;

@Component
@Order(11)
public class AuthSwitchDiamondRunner implements CommandLineRunner {

    private static final Logger logger = LoggerFactory.getLogger(AuthSwitchDiamondRunner.class);

    @Override
    public void run(String... args) {
        try {
            String configInfo = Diamond
                    .getConfig("com.alibaba.ae.qa.databank.auth.flag", "ae-qa-databank", ********);
            Constant.IS_TEST_FLAG = new Gson().fromJson(configInfo, Boolean.class);
        } catch (IOException e1) {
        }

        // 启动用，并且变化需要立即推送最新值
        Diamond.addListener("com.alibaba.ae.qa.databank.auth.flag", "ae-qa-databank",
                new ManagerListenerAdapter() {
                    public void receiveConfigInfo(String configInfo) {
                        try {
                            logger.info("Receive Auth Flag Info " + configInfo);
                            Constant.IS_TEST_FLAG = new Gson().fromJson(configInfo, Boolean.class);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                });
    }

}
