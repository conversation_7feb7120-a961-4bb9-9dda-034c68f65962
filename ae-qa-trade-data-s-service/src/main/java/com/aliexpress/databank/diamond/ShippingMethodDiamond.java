package com.aliexpress.databank.diamond;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.alibaba.global.satellite.proxy.diamond.annotation.SatelliteDiamondListener;
import com.alibaba.global.satellite.proxy.diamond.listener.SatelliteDiamondDataCallback;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

//@SatelliteDiamondListener(dataId = "com.alibaba.ae.qa.databank.order.shippingmethod", groupId = "shippingMethod", executeAfterInit = true)
//@Slf4j
//public class ShippingMethodDiamond implements SatelliteDiamondDataCallback {
//
//    private static Map<String, List<String>> ShippingMethodMap = new HashMap<>();
//
//
//    @Override
//    public void received(String s) {
//        if (StringUtils.isBlank(s)) {
//            return;
//        }
//        try {
//            ShippingMethodMap = JSON.parseObject(s, new TypeReference<Map<String, List<String>>>(){});
//        } catch (Throwable e) {
//            log.error("parse ShippingMethodMap failed.", e);
//        }
//    }
//
//    public static List<String> getShippingMethods(String shippingScenario) {
//        List<String> shippingMethods = ShippingMethodMap.get(shippingScenario);
//        if (CollectionUtils.isEmpty(shippingMethods)) {
//            return null;
//        }
//        return shippingMethods;
//    }
//}

import com.taobao.diamond.client.Diamond;
import com.taobao.diamond.manager.ManagerListenerAdapter;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Component
@Slf4j
@Order(11)
public class ShippingMethodDiamond implements CommandLineRunner {
    private static Map<String, List<String>> ShippingMethodMap = new HashMap<>();

    private static String DATA_ID = "com.alibaba.ae.qa.databank.order.shippingmethod";

    private static String GROUP_ID = "shippingMethod";

    public static List<String> getShippingMethods(String shippingScenario) {
        List<String> shippingMethods = ShippingMethodMap.get(shippingScenario);
        if (CollectionUtils.isEmpty(shippingMethods)) {
            return null;
        }
        return shippingMethods;
    }

    @Override
    public void run(String... args) {
        try {
            String configInfo = Diamond
                    .getConfig(DATA_ID, GROUP_ID, ********);
            ShippingMethodMap = JSON.parseObject(configInfo, new TypeReference<Map<String, List<String>>>(){});
        } catch (IOException e) {
            e.printStackTrace();
        }

        // 启动用，并且变化需要立即推送最新值
        Diamond.addListener(DATA_ID, GROUP_ID,
                new ManagerListenerAdapter() {
                    public void receiveConfigInfo(String configInfo) {
                        try {
                            log.info("Receive Intention Currency Info " + configInfo);
                            ShippingMethodMap = JSON.parseObject(configInfo, new TypeReference<Map<String, List<String>>>(){});
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                });
    }
}
