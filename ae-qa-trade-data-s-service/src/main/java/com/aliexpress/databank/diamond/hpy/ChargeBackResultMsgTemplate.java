package com.aliexpress.databank.diamond.hpy;

import com.taobao.diamond.client.Diamond;
import com.taobao.diamond.manager.ManagerListenerAdapter;
import com.taobao.eagleeye.EagleEye;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.Optional;

@Component
@Order(14)
@Slf4j
public class ChargeBackResultMsgTemplate implements CommandLineRunner {

    private static final Logger logger = LoggerFactory.getLogger(ChargeBackResultMsgTemplate.class);

    private static final String DATA_ID = "com.alibaba.ae.chargeback.switch";

    private static final String GROUP_ID = "ae-qa-databank";

    /***
     * global_payment_event_topic chargeBack 消息的模板
     */
    @Getter
    @Setter
    private String templateMsg = "";

    @Getter
    @Setter
    private String chargeOpenTemplate = "";
    /***
     * 是否允许在线上的环境，补发消息；
     */
    @Getter
    @Setter
    private String enableOnlineMetaq = "true";

    @Override
    public void run(String... strings) throws Exception {
        try {
            String configInfo = Diamond.getConfig(DATA_ID, GROUP_ID, ********);
            processStr(configInfo);
        } catch (Throwable t) {
            logger.error("ChargeBackResultMsgTemplateError：---------|" + EagleEye.getTraceId(), t);
        }

        // 启动用，并且变化需要立即推送最新值
        Diamond.addListener(DATA_ID, "ae-qa-databank",
                new ManagerListenerAdapter() {
                    public void receiveConfigInfo(String configInfo) {
                        try {
                            processStr(configInfo);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                });
    }

    private void processStr(String configInfo) {
        logger.info("ChargeBackResultMsgTemplateBefore：---------|" + EagleEye.getTraceId() + "|" + configInfo);

        String[] split = configInfo.split("\n");
        if (split.length != 0) {
            Optional.ofNullable(split[0])
                    .filter(StringUtils::isNotBlank)
                    .map(String::trim)
                    .ifPresent(i -> {
                        templateMsg = split[0];
                        logger.info("ChargeBackResultMsgTemplateResult1：---------|" + EagleEye.getTraceId() + "|" + templateMsg);
                    });
            Optional.ofNullable(split[1])
                    .filter(StringUtils::isNotBlank)
                    .map(String::trim)
                    .ifPresent(i -> {
                        chargeOpenTemplate = split[1];
                        logger.info("chargeOpenTemplate123|" + EagleEye.getTraceId() + "|" + chargeOpenTemplate);
                    });

            Optional.ofNullable(split[2])
                    .filter(StringUtils::isNotBlank)
                    .map(String::trim)
                    .ifPresent(i -> {
                        enableOnlineMetaq = split[2];
                        logger.info("ChargeBackResultMsgTemplateResult34|" + EagleEye.getTraceId() + "|" + enableOnlineMetaq);
                    });
        }
        logger.info("ChargeBackResultMsgTemplateResult999：---------|" + EagleEye.getTraceId() + "|" + templateMsg + "|"+enableOnlineMetaq+ "|"+chargeOpenTemplate);
    }

}