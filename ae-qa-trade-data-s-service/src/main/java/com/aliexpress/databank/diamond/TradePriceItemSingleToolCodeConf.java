package com.aliexpress.databank.diamond;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliexpress.databank.constant.Constant;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.taobao.diamond.client.Diamond;
import com.taobao.diamond.manager.ManagerListenerAdapter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Component
@Order(11)
public class TradePriceItemSingleToolCodeConf implements CommandLineRunner {
    private static final Logger logger = LoggerFactory.getLogger(TradePriceItemSingleToolCodeConf.class);

    private volatile static List<String> TRADE_ITEM_SINGLE_TOOLCODE = new ArrayList<>();

    @Override
    public void run(String... strings) throws Exception {
        try {
            String configInfo = Diamond
                    .getConfig("com.alibaba.ae.qa.databank.trade.itemSingleToolCode", "ae-qa-databank", ********);
            TRADE_ITEM_SINGLE_TOOLCODE = JSONArray.parseArray(configInfo, String.class);
        } catch (IOException e1) {
        }

        // 启动用，并且变化需要立即推送最新值
        Diamond.addListener("com.alibaba.ae.qa.databank.trade.type4InterfaceName", "ae-qa-databank",
                new ManagerListenerAdapter() {
                    @Override
                    public void receiveConfigInfo(String configInfo) {
                        try {
                            logger.info("Received type4InterfaceName Info: " + configInfo);
                            synchronized (TRADE_ITEM_SINGLE_TOOLCODE) {
                                TRADE_ITEM_SINGLE_TOOLCODE = JSONArray.parseArray(configInfo, String.class);
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                });
    }

    /**
     * 返回配置信息
     *
     * @return
     */
    public static List<String> getConfig() {
        if (true) {
            List<String> list = new ArrayList<>();
            list.add("limitedDiscount");
            list.add("limitedDiscount");

            return list;
        }
        return TRADE_ITEM_SINGLE_TOOLCODE;
    }
}
