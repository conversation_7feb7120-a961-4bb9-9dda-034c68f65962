package com.aliexpress.databank.diamond;

import com.taobao.diamond.client.Diamond;

import java.io.IOException;

public class DiamondUtils {

    public static String getDiamond(String dataId, String group) throws IOException {
        return Diamond.getConfig(dataId, group, 3000);
    }

    public static String changeDiamond(String dataId, String group) throws IOException {
        return Diamond.getConfig(dataId, group, 3000);
    }

}
