package com.aliexpress.databank.diamond;

import com.aliexpress.databank.constant.Constant;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.taobao.diamond.client.Diamond;
import com.taobao.diamond.manager.ManagerListenerAdapter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * item configured
 */
@Component
@Order(11)
public class DeliveryOptionDiamondRunner implements CommandLineRunner {

    private static final Logger logger = LoggerFactory.getLogger(DeliveryOptionDiamondRunner.class);

    @Override
    public void run(String... args) {
        try {
            String configInfo = Diamond
                    .getConfig("com.alibaba.ae.qa.databank.order.deliveryoption", "ae-qa-databank", ********);
            Constant.ORDER_DELIVERY_OPTION = new Gson().fromJson(configInfo, new TypeToken<Map<String, List<String>>>() {
            }.getType());
        } catch (IOException e1) {
        }

        // 启动用，并且变化需要立即推送最新值
        Diamond.addListener("com.alibaba.ae.qa.databank.order.deliveryoption", "ae-qa-databank",
                new ManagerListenerAdapter() {
                    public void receiveConfigInfo(String configInfo) {
                        try {
                            logger.info("Received Items Info: " + configInfo);
                            Constant.ORDER_DELIVERY_OPTION = new Gson().fromJson(configInfo, new TypeToken<Map<String, List<String>>>() {
                            }.getType());
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                });
    }
}
