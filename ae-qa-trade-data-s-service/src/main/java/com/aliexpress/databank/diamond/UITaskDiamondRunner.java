package com.aliexpress.databank.diamond;

import com.alibaba.fastjson.JSONObject;
import com.aliexpress.databank.constant.Constant;
import com.aliexpress.databank.dataobject.UITask;
import com.taobao.diamond.client.Diamond;
import com.taobao.diamond.manager.ManagerListenerAdapter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Component
@Order(13)
public class UITaskDiamondRunner  implements CommandLineRunner {

    private static final Logger logger = LoggerFactory.getLogger(UITaskDiamondRunner.class);

    private static final String DATA_ID = "com.alibaba.ae.qa.databank.uitask";

    private static final String GROUP_ID = "ae-qa-databank";

    @Override
    public void run(String... strings) throws Exception {
        try {
            String configInfo = Diamond
                    .getConfig(DATA_ID, GROUP_ID, ********);
            JSONObject jsonObject = JSONObject.parseObject(configInfo);
            logger.info("配置：---------"+jsonObject);
            Constant.IosTaskConfig = jsonObject.getJSONArray("ios").toJavaList(UITask.class);
            Constant.AndroidTaskConfig = jsonObject.getJSONArray("android").toJavaList(UITask.class);
            Constant.iosPackageUrl = jsonObject.getString("iosPackage");
            Constant.androidPackageUrl = jsonObject.getString("androidPackage");
        }
        catch (IOException e1) {
        }

        // 启动用，并且变化需要立即推送最新值
        Diamond.addListener("com.alibaba.ae.qa.databank.uitask", "ae-qa-databank",
                new ManagerListenerAdapter() {
                    public void receiveConfigInfo(String configInfo) {
                        try {
                            logger.info("Received Items Info: " + configInfo);
                            JSONObject jsonObject = JSONObject.parseObject(configInfo);
                            Constant.IosTaskConfig = jsonObject.getJSONArray("ios").toJavaList(UITask.class);
                            Constant.AndroidTaskConfig = jsonObject.getJSONArray("android").toJavaList(UITask.class);
                            Constant.iosPackageUrl = jsonObject.getString("iosPackage");
                            Constant.androidPackageUrl = jsonObject.getString("androidPackage");
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                });
    }
}