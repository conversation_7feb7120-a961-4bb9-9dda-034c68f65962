package com.aliexpress.databank.taojp.hsf.provider;

import com.alibaba.boot.hsf.annotation.HSFConsumer;
import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.common.lang.StringUtil;
import com.alibaba.ecommerce.exchange.dataobject.GlobalRateInfo;
import com.alibaba.ecommerce.module.Response;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.global.landlord.LandlordContext;
import com.alibaba.global.order.management.api.facade.OrderQueryForBuyerFacade;
import com.alibaba.global.order.management.api.model.TradeOrderDTO;
import com.alibaba.global.payment.api.request.PaymentRecordQueryRequest;
import com.alibaba.global.payment.api.response.PaymentRecordQueryResponse;
import com.alibaba.global.qa.dc.model.param.SystemDTO;
import com.alibaba.global.qa.dc.model.result.QueryResultUnit;
import com.alibaba.global.qa.dc.model.result.ResultDTO;
import com.aliexpress.databank.constant.Constant;
import com.aliexpress.databank.dataobject.*;
import com.aliexpress.databank.hsf.DataPoolService;
import com.aliexpress.databank.hsf.ExchangeRateServiceHsf;
import com.aliexpress.databank.hsf.SendDingMsgService;
import com.aliexpress.databank.taojp.hsf.OrderServiceJP;
import com.aliexpress.databank.taojp.hsf.PaymentServiceJP;
import com.aliexpress.databank.taojp.utils.HsfUtilJP;
import com.aliexpress.databank.utils.HsfUtil;
import com.taobao.eagleeye.EagleEye;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.*;


@HSFProvider(serviceInterface = PaymentServiceJP.class, serviceGroup = "HSF", serviceVersion = "1.0.0_TAO_JP", clientTimeout = 20000)
public class PaymentServiceImplJP implements PaymentServiceJP {

    private static final Logger logger = LoggerFactory.getLogger(PaymentServiceImplJP.class);

    @Autowired
    private OrderServiceJP orderServiceJP;

    @Autowired
    SendDingMsgService sendDingMsgService;

    @Autowired
    DataPoolService dataPoolService;

    @HSFConsumer(serviceVersion = "1.0.0_TAO_JP", serviceGroup = "gps", clientTimeout = 30000)
    private com.alibaba.global.payment.api.facade.PaymentQueryFacade paymentQueryFacadeJP;

    @Autowired
    private ExchangeRateServiceHsf exchangeRateServiceHsf;

    @HSFConsumer(serviceVersion = "1.0.0_TAO_JP", serviceGroup = "HSF", clientTimeout = 20000)
    private OrderQueryForBuyerFacade orderQueryForBuyerFacadeJP;


    //mock支付成功
    @Override
    public ResultDTO mockIPaySuccess(String params, SystemDTO systemDTO) throws Exception {
        String empId = systemDTO.getOperator();
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        String orderId = jsonObject.getString("orderId");
        //绕过查询直接输入buyerId
        String buyerId = jsonObject.getString("buyerId");
        String messageType = jsonObject.getString("messageType");
        String envType = jsonObject.getString("envType");
        String ip = jsonObject.getString("ip");
        String dpath = jsonObject.getString("dpath");
        String instructionNo = jsonObject.getString("instructionNo");
        // 是否开启验款
        String enableAmountCheck = jsonObject.getString("enableAmountCheck");
        boolean isAmountCheck = enableAmountCheck != null && !enableAmountCheck.equals("否");
        //判断开启dpath路由
        if (StringUtil.isNotBlank(dpath)) {
            EagleEye.putUserData("dpath_env", dpath);
        }
        // 支付指令单idx
        Integer index = 0;

        PGNotifyBody pgNotifyBody = new PGNotifyBody();
        pgNotifyBody.setMid("ae");
        pgNotifyBody.setServiceProvider("ipayagh");
        pgNotifyBody.setSource("gps");
        Long payerId;
        if(StringUtil.isNotBlank(buyerId)) {
            payerId = Long.valueOf(buyerId);
        } else {
            payerId = orderServiceJP.getPayerIdByOrderId(orderId);
        }
//        if (payerId == null) {
//            resultDTO.setData("根据订单查询买家id异常，请检查输入订单号是否正确");
//            resultDTO.setSuccess(true);
//            return resultDTO;
//        }
        //TODO: 这部分可能要去掉，后续有标了再删除
//        Boolean userFlag = dataPoolService.checkTestAccount(payerId);
//        if (!userFlag) {
//            resultDTO.setData("查询买家账号信息失败，或买家账号不是测试账号无法mock");
//            resultDTO.setSuccess(true);
//            return resultDTO;
//        }
        pgNotifyBody.setUserId(payerId);

        JSONObject res = getPayInstructionDistByOrderId(orderId);
        if (res == null || res.getJSONArray("records").isEmpty()) {
            resultDTO.setData("支付查询失败，或当前无支付单需重新提交支付");
            resultDTO.setSuccess(true);
            return resultDTO;
        }

        JSONArray list = res.getJSONArray("records");
        String checkoutOrderNo = list.getJSONObject(index).getString("checkoutOrderNo");
        JSONObject payInstructionRes = getPayInstructionByCheckoutOrderNo(checkoutOrderNo);
        JSONObject payInstruction = new JSONObject();
        String payStatusInfo = "";
        if (!payInstructionRes.isEmpty()) {
            payInstruction = payInstructionRes.getJSONArray("records").getJSONObject(index);
            String status = payInstruction.getString("status");
            String instrumentCode = payInstruction.getString("instrumentCode");
            String payPlanNo = payInstruction.getString("payPlanNo");

            payStatusInfo = "推送消息前最近一笔支付单状态：" + status + "，支付方式：" + instrumentCode + "，支付单号：" + payPlanNo + "。";
            switch (status) {
                case "INIT":
                    payStatusInfo += "提交支付未完成，请重新提交。";
                    break;
                // todo 有个支付单cancel标=Y的情况，在支付指令里拿不到，也无法推进
                case "AUTHORIZING":
                    payStatusInfo += "提交支付成功，需要推送支付成功消息。";
                    break;
                case "AUTHORIZED":
                    payStatusInfo += "支付成功消息消费成功，下一步需要推送支付风控通过消息。";
                    break;
                case "PAID":
                    payStatusInfo += "风控通过消息消费成功，mock支付完成。";
                    break;
                case "FAILED":
                    payStatusInfo += "支付失败，请确保提交支付走到mock逻辑（卡渠道：预发+mock加白）。";
                    break;
            }
        } else {
            payStatusInfo = "支付指令查询失败。";
        }

        //如果走验证款，需要查一下当前支付单汇率
        BigDecimal fxRate = null;
        if (isAmountCheck) {
            try {
                logger.info("【mockIPaySuccess_queryPayInstruction】return object：" + payInstruction);
                if (!payInstruction.isEmpty() && payInstruction.getString("fxRate") != null) {
                    fxRate = new BigDecimal(payInstruction.getString("fxRate"));
                }

                //如果查支付单数据没拿到汇率，实时查询汇率兜底吧
                if (fxRate == null) {
                    logger.error("【mockIPaySuccess_queryPayInstruction】get fxRate is null");
                    GlobalRateInfo rateInfo = exchangeRateServiceHsf.getExchangeRate(list.getJSONObject(index).getString("orderCurrency"), list.getJSONObject(index).getString("payCurrency")).getModule();
                    logger.info("【mockIPaySuccess_getExchangeRate】return object：" + JSON.toJSONString(rateInfo));
                    if (rateInfo != null && rateInfo.getRate() != null) {
                        fxRate = rateInfo.getRate();
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        try {
            switch (messageType) {
                case "支付成功":
                    pgNotifyBody.setExchangeCode("ipayagh.gn.notification.pay.result");
                    PaymentResult paymentResult = new PaymentResult();
                    PaymentResult.Result result = new PaymentResult.Result();
                    result.setResultCode("SUCCESS");
                    result.setResultMessage("success.");
                    result.setResultStatus("S");

                    paymentResult.setPaymentId("2386492874627346283732232");
                    paymentResult.setPaymentTime("2023-05-15T08:32:54-07:00");
                    paymentResult.setSuggestedSettleTime("2023-05-19T08:32:54-07:00");
                    paymentResult.setPayToId("2386492874627346283732232");
                    if (StringUtil.isNotBlank(instructionNo)) {
                        paymentResult.setPayToRequestId(instructionNo);
                    } else {
                        paymentResult.setPayToRequestId(res.getJSONArray("records").getJSONObject(index).getString("instructionNo"));
                    }
                    paymentResult.setResult(result);
                    paymentResult.setExtendInfo("{}");
                    List<PaymentResult.PaymentDetailSummary> paymentDetailSummaries = new ArrayList<>();
                    com.alibaba.global.payment.api.vo.Money payToAmount = new com.alibaba.global.payment.api.vo.Money();
                    com.alibaba.global.payment.api.vo.Money payAmount = new com.alibaba.global.payment.api.vo.Money();
                    PayInstructionMoney payInstructionMoney = getPaymentMoney(list, paymentResult.getPayToRequestId());

                    // 1、PaymentDetailSummary金额组装： 包括渠道支付cash金额+coupon金额
                    PaymentResult.PaymentDetailSummary paymentDetailSummary = new PaymentResult.PaymentDetailSummary();
                    paymentDetailSummary.setExtendInfo("{\"authCode\":\"8MUCGZ\",\"cardIndexNo\":\"2022092311027100159157123385896\",\"cardCountry\":\"BR\",\"maskedCardNo\":\"************6057\",\"threeDSResult\":\"{\\\"cavv\\\":\\\"kAMe3XpqjkE8OgIVqaoxn5hhLZEM\\\",\\\"dsTransactionId\\\":\\\"5ea3d134-9847-4129-9747-8243c1477954\\\",\\\"eci\\\":\\\"02\\\",\\\"threeDSVersion\\\":\\\"2.2.0\\\"}\",\"authType\":\"3D\",\"payBrand\":\"MASTERCARD\",\"cvvResultRawCode\":\"\"}");
                    paymentDetailSummary.setPaymentMethodMetaData("{\"uniqueIndex\":\"ebcd08713b50d4de5fe27fda87fe01c1e491a847c3c88bfc350ac360201df8e7\"}");
                    paymentDetailSummary.setCustomerId("2188207747315665");

                    com.alibaba.global.payment.api.vo.Money payMoney = new com.alibaba.global.payment.api.vo.Money();
                    //验款则重设实际支付的cash金额paymentAmount*0.7
                    //测试金额改大，改为1.3，改小为0.7
                    Float amountScale = isAmountCheck ? ("改大".equals(enableAmountCheck) ? 1.3f : 0.7f) : 1.0f;
                    // 日本走Credit前置，不需要扣除优惠金额
                    Long cashPaymentAmount = Float.valueOf((payInstructionMoney.getGoodCommissionPaymentValue()) * amountScale).longValue();
                    //Long cashPaymentAmount = Float.valueOf((payInstructionMoney.getGoodCommissionPaymentValue() - payInstructionMoney.getPromotionOrderPaymentValue()) * amountScale).longValue();
                    payMoney.setValue(cashPaymentAmount.toString());
                    payMoney.setCurrency(payInstructionMoney.getPaymentCurrency());
                    paymentDetailSummary.setPaymentAmount(payMoney);
                    paymentDetailSummary.setPaymentMethodType(payInstructionMoney.getInstrumentCode());
                    paymentDetailSummaries.add(paymentDetailSummary);

                    //coupon后置后支付成功通知金额不能包含优惠金额
//                    if (payInstructionMoney.getPromotionOrderPaymentValue() != 0) {
//                        paymentDetailSummary = new PaymentResult.PaymentDetailSummary();
//                        paymentDetailSummary.setPaymentMethodType("MERCHANT_COUPON");
//                        paymentDetailSummary.setExtendInfo("{}");
//                        payMoney = new com.alibaba.global.payment.api.vo.Money();
//                        payMoney.setValue(Long.toString(payInstructionMoney.getPromotionOrderPaymentValue()));
//                        payMoney.setCurrency(payInstructionMoney.getPaymentCurrency());
//                        paymentDetailSummary.setPaymentAmount(payMoney);
//                        paymentDetailSummaries.add(paymentDetailSummary);
//                    }
                    paymentResult.setPaymentDetailSummaries(paymentDetailSummaries);
                    paymentResult.setPaymentRequestId("20990591211240618890800021178");

                    // 2、支付金额（cash支付金额+优惠金额）组装
                    // 因为日本走的前置Credit逻辑，因此payAmountScaled 不需要再加上优惠金额
                    //Long payAmountScaled = cashPaymentAmount + payInstructionMoney.getPromotionOrderPaymentValue();
                    Long payAmountScaled = cashPaymentAmount;
                    payAmount.setValue(cashPaymentAmount.toString());
                    payAmount.setCurrency(payInstructionMoney.getPaymentCurrency());
                    paymentResult.setPaymentAmount(payAmount);

                    // 3、报价金额（手续费 + good金额-优惠金额）组装，若是验款&汇率不为null场景 报价金额=paymentAmount/汇率，否则不做特殊处理了
                    Long payToMoney = isAmountCheck && fxRate != null ? (new BigDecimal(payAmountScaled).divide(fxRate, 2, BigDecimal.ROUND_HALF_EVEN)).longValue() : payInstructionMoney.getCashOrderValue();
                    payToAmount.setValue(payToMoney.toString());
                    payToAmount.setCurrency(payInstructionMoney.getOrderCurrency());
                    paymentResult.setPayToAmount(payToAmount);

                    JSONObject da = new JSONObject();
                    da.put("extendInfo", JSONObject.toJSONString(paymentResult));
                    pgNotifyBody.setContext(da.toJSONString());
                    logger.info("pgNotifyBody:" + JSONObject.toJSONString(pgNotifyBody));
                    String str = sendDingMsgService.sendMetaq("GL_pg_rt1_pg_msg_TAO_JP", JSONObject.toJSONString(pgNotifyBody), "PAY", envType, ip, Long.toString(payerId), null, dpath, "");
                    resultDTO.setData(payStatusInfo + "发送消息结果：" + str);
                    resultDTO.setSuccess(true);
                    break;
                case "支付风控通过":
                    pgNotifyBody.setExchangeCode("ipayagh.gn.notification.capture.result");
                    CaptureResult captureResult = new CaptureResult();
                    CaptureResult.Result result1 = new CaptureResult.Result();
                    result1.setResultCode("SUCCESS");
                    result1.setResultMessage("success.");
                    result1.setResultStatus("S");
                    captureResult.setCaptureTime("2023-05-15T08:32:54-07:00");
                    captureResult.setResult(result1);
                    captureResult.setSuggestedSettleTime("2024-06-22T10:54:38-07:00");
                    captureResult.setPaymentId("239279837283372837289372");
                    captureResult.setPayToId("236427893628361982370129837");
                    captureResult.setCaptureId("11236427893628361982370129837");
                    if (StringUtil.isNotBlank(instructionNo)) {
                        captureResult.setCaptureRequestId(instructionNo);
                    } else {
                        captureResult.setCaptureRequestId(res.getJSONArray("records").getJSONObject(index).getString("instructionNo"));
                    }
                    PayInstructionMoney money = getPaymentMoney(list, captureResult.getCaptureRequestId());
                    payToAmount = new com.alibaba.global.payment.api.vo.Money();
                    // capture的订单金额不能该小，消费时会做校验：S_PP_INST_NOTIFY_AMOUNT_NOT_EQUALS
                    payToAmount.setValue(Long.toString(money.getGoodCommissionOrderValue()));
                    payToAmount.setCurrency(money.getOrderCurrency());
                    captureResult.setCaptureAmount(payToAmount);//报价
                    JSONObject ca = new JSONObject();
                    ca.put("extendInfo", JSONObject.toJSONString(captureResult));
                    pgNotifyBody.setContext(ca.toJSONString());
                    logger.info("pgNotifyBody:" + JSONObject.toJSONString(pgNotifyBody));
                    String str2 = sendDingMsgService.sendMetaq("GL_pg_rt1_pg_msg_TAO_JP", JSONObject.toJSONString(pgNotifyBody), "CAPTURE", envType, ip, Long.toString(payerId), null, dpath, "");
                    resultDTO.setData(payStatusInfo + "发送消息结果：" + str2);
                    resultDTO.setSuccess(true);
                    break;
                case "交易支付成功":
                case "交易风控通过":
                    TradePaymentResult tradePaymentResult = new TradePaymentResult();
                    tradePaymentResult.setBizOrderId(orderId);
                    tradePaymentResult.setBatchPayNo(getBatchPayRealtionNoByBizOrderId(orderId).get(0));
                    String instrumentNo = res.getJSONArray("records").getJSONObject(0).getString("instructionNo");
//                    tradePaymentResult.setBuyerId(payerId);
                    long goodAmount = 0;
                    long orderAmount = 0;
                    long promotionPay = 0;
                    long promotionOrder = 0;
                    long orderPromotionPay = 0;
                    long orderPromotionOrder = 0;
                    String payCurrency = null;
                    String orderCurrency = null;
                    for (int i = 0; i < list.size(); i++) {
                        if ("GOODS".equals(list.getJSONObject(i).getString("fundType")) && instrumentNo.equals(list.getJSONObject(i).getString("instructionNo"))) {
                            tradePaymentResult.setCheckoutOrderId(res.getJSONArray("records").getJSONObject(i).getString("checkoutOrderNo"));
                            tradePaymentResult.setPayOptionCode(res.getJSONArray("records").getJSONObject(i).getString("instrumentCode"));
                            tradePaymentResult.setPayPlanNo(res.getJSONArray("records").getJSONObject(i).getString("payPlanNo"));
                            goodAmount = Long.parseLong(list.getJSONObject(i).getString("payAmount"));
                            payCurrency = list.getJSONObject(i).getString("payCurrency");
                            orderAmount = Long.parseLong(list.getJSONObject(i).getString("orderAmount"));
                            orderCurrency = list.getJSONObject(i).getString("orderCurrency");
                            tradePaymentResult.setCheckoutOrderAmount(new TradePaymentResult.Money(orderAmount, orderCurrency));
                        } else if ("PROMOTION".equals(list.getJSONObject(i).getString("fundType")) && instrumentNo.equals(list.getJSONObject(i).getString("instructionNo"))) {
                            promotionPay = Long.parseLong(list.getJSONObject(i).getString("payAmount"));
                            promotionOrder = Long.parseLong(list.getJSONObject(i).getString("orderAmount"));
                            tradePaymentResult.setPaymentPromotionPayAmount(new TradePaymentResult.Money(goodAmount - promotionPay, payCurrency));
                            tradePaymentResult.setPaymentPromotionPostAmount(new TradePaymentResult.Money(orderAmount - promotionOrder, orderCurrency));
                        } else if ("ORDER_PROMOTION".equals(list.getJSONObject(i).getString("fundType")) && instrumentNo.equals(list.getJSONObject(i).getString("instructionNo"))) {
                            orderPromotionPay = Long.parseLong(list.getJSONObject(i).getString("payAmount"));
                            orderPromotionOrder = Long.parseLong(list.getJSONObject(i).getString("orderAmount"));
                        }
                    }
                    tradePaymentResult.setActualPaidAmount(new TradePaymentResult.Money(goodAmount - orderPromotionPay, payCurrency));
                    tradePaymentResult.setActualPaidPostAmount(new TradePaymentResult.Money(orderAmount - orderPromotionOrder, orderCurrency));
                    tradePaymentResult.setActualPaidCash(new TradePaymentResult.Money(goodAmount - orderPromotionPay - promotionPay, payCurrency));
                    tradePaymentResult.setActualPaidPostCash(new TradePaymentResult.Money(orderAmount - orderPromotionOrder - promotionOrder, orderCurrency));
                    if ("交易支付成功".equals(messageType)) {
                        tradePaymentResult.setStatus("AUTHORIZED");
                    } else if ("交易风控通过".equals(messageType)) {
                        tradePaymentResult.setStatus("SUCCEED");
                    }
                    logger.info("jjjjjj:" + JSONObject.toJSONString(tradePaymentResult));
//                    String str3 = sendDingMsgService.sendMetaq("global_payment_event_topic", JSONObject.toJSONString(tradePaymentResult), "PAY", envType, ip, Long.toString(payerId), null, dpath, "");
//                    resultDTO.setData("发送消息结果：" + str3);
                    resultDTO.setSuccess(true);
                    break;
                default:
                    logger.info("暂不支持");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        HsfUtil.measureAll("/payment/jobId="+systemDTO.getSite(),empId);
        return resultDTO;
    }

    //退款消息发送
    @Override
    public ResultDTO generateMessage(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        Map<String, QueryResultUnit> data = new LinkedHashMap<>();
//        LandlordContext.setTenantSpec("AE_GLOBAL", -1);

        JSONObject jsonObject = JSONObject.parseObject(params);
        String refundChannel = jsonObject.getString("refundChannel");
        String messageType = jsonObject.getString("messageType");
        String messageState = jsonObject.getString("messageState");  // pending, settle, refundComplete
        String bizOrderNo = jsonObject.getString("orderIdStr");
        String refundInstructionNo = jsonObject.getString("refundInstructionNo");
        String envType = jsonObject.getString("envType");
        String ip = jsonObject.getString("ip");
        String dpath = jsonObject.getString("dpath");

        //判断开启dpath路由
        if (StringUtil.isNotBlank(dpath)) {
            EagleEye.putUserData("dpath_env", dpath);
        }

        Long payerId = orderServiceJP.getPayerIdByOrderId(bizOrderNo);
        if (payerId == null) {
            resultDTO.setData("查询用户id异常，请检查输入订单号是否正确");
            resultDTO.setSuccess(true);
            return resultDTO;
        }
//        Boolean userFlag = dataPoolService.checkTestAccount(payerId);
//        if (!userFlag) {
//            resultDTO.setData("仅支持测试账号mock");
//            resultDTO.setSuccess(true);
//            return resultDTO;
//        }

        MessageDTO messageDTO = new MessageDTO();
        resultDTO.setSuccess(true);

        if (messageType.equals(Constant.REFUND_PROCESS)) {
            messageDTO.setExchangeCode(Constant.REFUND_PROCESS_EXCHANGECODE);
            messageDTO.setMid("ae");
            messageDTO.setSource("gps");
            messageDTO.setServiceProvider("ipayagh");
            RefundInfo refundInfo = new RefundInfo();
            //查询退款信息
            JSONObject refundRes = HsfUtilJP.queryRefundInstruction(bizOrderNo, payerId);
            JSONArray records = refundRes.getJSONArray("records");
            if (records.isEmpty()) {
                resultDTO.setData("无退款记录");
                return resultDTO;
            }
            com.alibaba.global.payment.api.vo.Money refundMoney = new com.alibaba.global.payment.api.vo.Money();
            com.alibaba.global.payment.api.vo.Money refundAmount = new com.alibaba.global.payment.api.vo.Money();
            String paymentMethod = null;
            for (int i = 0; i < records.size(); i++) {
                if (refundInstructionNo.equals(records.getJSONObject(i).getString("instructionNo"))) {

                    refundMoney.setValue(records.getJSONObject(i).getString("orderAmount"));
                    refundMoney.setCurrency(records.getJSONObject(i).getString("orderCurrency"));
                    refundAmount.setValue(records.getJSONObject(i).getString("refundAmount"));
                    refundAmount.setCurrency(records.getJSONObject(i).getString("refundCurrency"));
                    paymentMethod = records.getJSONObject(i).getString("instrumentCode");
                    break;
                }
            }
            refundInfo.setRefundAmount(refundMoney);
            refundInfo.setRefundRequestId(refundInstructionNo);
            refundInfo.setPaymentId(refundInstructionNo + UUID.randomUUID().toString());
            refundInfo.setPayToId(refundInstructionNo + UUID.randomUUID().toString());
            refundInfo.setRefundStage(messageState);

            if ("settle".equals(messageState)) {
                refundInfo.setRefundStatus("FINISH");
            } else if ("pending".equals(messageState)) {
                refundInfo.setRefundStageTime(ZonedDateTime.now().toString().replace("[Asia/Shanghai]", ""));
            } else {  // refundComplete
                refundInfo.setRefundStatus("SUCCESS");
                refundInfo.setRefundedTime(ZonedDateTime.now().toString().replace("[Asia/Shanghai]", ""));
                refundInfo.setRefundStageTime(ZonedDateTime.now().toString().replace("[Asia/Shanghai]", ""));
            }

            RefundStatements refundStatements = new RefundStatements();
            refundStatements.setRefundStatementStage(messageState);
            if ("BONUS".equals(refundChannel) && "settle".equals(messageState)) {
                refundStatements.setRefundStrategy("NON_ORIGINAL");
            }
            refundStatements.setRefundStatementId(refundInstructionNo + UUID.randomUUID().toString());

            RefundStatements.RefundAssetDetails refundAssetDetails = new RefundStatements.RefundAssetDetails();
            if ("init".equals(messageState) || "send".equals(messageState)) {
                refundAssetDetails.setFundType("EXTERNAL");
                refundAssetDetails.setOutAbility("EXTERNAL");
                refundAssetDetails.setPayProvider("WPG");
            }
            refundAssetDetails.setRefundAmount(refundAmount);
            refundAssetDetails.setRefundAssetStage(messageState);
            RefundStatements.RefundMethod refundMethod = new RefundStatements.RefundMethod();
            refundMethod.setPaymentMethodType(paymentMethod);
            refundAssetDetails.setRefundMethod(refundMethod);
            refundStatements.setRefundAssetDetails(Arrays.asList(refundAssetDetails));
            refundInfo.setRefundStatementsList(Arrays.asList(refundStatements));
            JSONObject res = new JSONObject();
            res.put("notifyType", messageType);
            res.put("notifyId", refundInstructionNo + UUID.randomUUID().toString());
            res.put("refundInfo", refundInfo);
            messageDTO.setContext(res.toJSONString());
        }

        String msg = JSON.toJSONString(messageDTO);
        String str = sendDingMsgService.sendMetaq("global_payment_event_topic", msg, "refundProcess", envType, ip, Long.toString(payerId), null, dpath, "");
        resultDTO.setData("发送消息结果：" + str + " " + "消息内容：" + msg);
        resultDTO.setSuccess(true);
        return resultDTO;
    }

    /**
     * 订单号获取支付成功支付单号
     *
     * @param orderId
     * @return
     */
    @Override
    public JSONObject getPayInstructionDistByOrderId(String orderId) {
        UniqueRes checkOutOrderNo = getCheckOutOrderNoByOrderId(orderId);
        try {
            JSONObject res = HsfUtilJP.queryPayInstructionDist(checkOutOrderNo.getUniqueValue());
            logger.info("RES:" + res.toJSONString());
            return res;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 通过订单号获取收单号
     *
     * @param orderId
     * @return
     */
    @Override
    public UniqueRes getCheckOutOrderNoByOrderId(String orderId) {
        UniqueRes uniqueRes = new UniqueRes();
        JSONObject res = null;
        try {
            res = HsfUtilJP.queryBatchCheckoutOrder(orderId);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (null != res && res.containsKey("records")) {
            JSONArray records = res.getJSONArray("records");
            if (!records.isEmpty()) {
                uniqueRes.setUniqueValue(records.getJSONObject(0).getString("checkoutOrderNo"));
                uniqueRes.setPayerId(records.getJSONObject(0).getLong("payerId"));
                return uniqueRes;
            }
        }
        return null;
    }

    @Override
    public JSONObject getPayInstructionByCheckoutOrderNo(String checkoutOrderNo) {
        try {
            JSONObject res = HsfUtilJP.queryPayInstruction(checkoutOrderNo);
            logger.info("RES:" + res.toJSONString());
            return res;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public List<String> getBatchPayRealtionNoByBizOrderId(String orderId) {
        PaymentRecordQueryRequest paymentRecordQueryRequest = new PaymentRecordQueryRequest();
        paymentRecordQueryRequest.setBizOrderNo(orderId);
        paymentRecordQueryRequest.setPlatform("AE");
        paymentRecordQueryRequest.setSource("global.trade");
        List<String> list = new ArrayList<>();
        try {
            Response<TradeOrderDTO> tradeOrderDTOResponse = orderQueryForBuyerFacadeJP.queryTradeOrderById(Long.parseLong(orderId));
            if (tradeOrderDTOResponse.isSuccess()) {
                Long buyerId = tradeOrderDTOResponse.getModule().getBuyer().getBuyerId();
                paymentRecordQueryRequest.setPayerId(buyerId);
                paymentRecordQueryRequest.setRouteId(buyerId);
            } else {
                logger.error("获取订单买家信息异常：" + tradeOrderDTOResponse.getModule());
            }
            PaymentRecordQueryResponse paymentRecordQueryResponse = paymentQueryFacadeJP.queryPaymentRecordsByBizOrderId(paymentRecordQueryRequest);

            if (paymentRecordQueryResponse.getSucceeded()) {
                String checkoutOrderNo = paymentRecordQueryResponse.getCheckoutOrderNo();
                JSONObject res = HsfUtilJP.queryPayInstruction(checkoutOrderNo);
                JSONArray records = res.getJSONArray("records");
                if (records.size() > 0) {
                    for (int i = 0; i < records.size(); i++) {
                        list.add(records.getJSONObject(i).getString("batchPayRelationNo"));
                    }
                    logger.info("batchPayRelationNo:" + list.toString());
                    return list;
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private PayInstructionMoney getPaymentMoney(JSONArray payInstructionDistList, String payToRequestId) {
        PayInstructionMoney payInstructionMoney = new PayInstructionMoney();
        long orderCommissionValue = 0;
        long paymentCommissionValue = 0;
        long promotionValue = 0;
        long promotionOrderValue = 0;
        for (int i = 0; i < payInstructionDistList.size(); i++) {
            if (("GOODS".equals(payInstructionDistList.getJSONObject(i).getString("fundType")) ||
                    "COMMISSION".equals(payInstructionDistList.getJSONObject(i).getString("fundType")))) {
                if (payInstructionDistList.getJSONObject(i).getString("instructionNo").equals(payToRequestId)) {
                    orderCommissionValue = orderCommissionValue + Long.parseLong(payInstructionDistList.getJSONObject(i).getString("orderAmount"));
                    payInstructionMoney.setOrderCurrency(payInstructionDistList.getJSONObject(i).getString("orderCurrency"));
                    paymentCommissionValue = paymentCommissionValue + Long.parseLong(payInstructionDistList.getJSONObject(i).getString("payAmount"));
                    payInstructionMoney.setPaymentCurrency(payInstructionDistList.getJSONObject(i).getString("payCurrency"));
                    payInstructionMoney.setInstrumentCode(payInstructionDistList.getJSONObject(i).getString("instrumentCode"));
                    continue;
                }
            }

            if (("PROMOTION".equals(payInstructionDistList.getJSONObject(i).getString("fundType")) ||
                    "ORDER_PROMOTION".equals(payInstructionDistList.getJSONObject(i).getString("fundType")))) {
                if (payInstructionDistList.getJSONObject(i).getString("instructionNo").equals(payToRequestId)) {
                    promotionValue = promotionValue + Long.parseLong(payInstructionDistList.getJSONObject(i).getString("payAmount"));
                    promotionOrderValue = promotionOrderValue + Long.parseLong(payInstructionDistList.getJSONObject(i).getString("orderAmount"));
                }
            }
        }
        payInstructionMoney.setGoodCommissionOrderValue(orderCommissionValue);
        payInstructionMoney.setGoodCommissionPaymentValue(paymentCommissionValue);
        payInstructionMoney.setPromotionOrderOrderValue(promotionOrderValue);
        payInstructionMoney.setPromotionOrderPaymentValue(promotionValue);
//        payInstructionMoney.setCashPaymentValue(paymentCommissionValue - promotionValue);
//        payInstructionMoney.setCashOrderValue(orderCommissionValue - promotionOrderValue);
        // 后置逻辑不需要扣减优惠金额
        payInstructionMoney.setCashPaymentValue(paymentCommissionValue);
        payInstructionMoney.setCashOrderValue(orderCommissionValue);
        return payInstructionMoney;
    }

}

