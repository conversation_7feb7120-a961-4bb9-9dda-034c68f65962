package com.aliexpress.databank.taojp.hsf;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.global.qa.dc.model.param.SystemDTO;
import com.alibaba.global.qa.dc.model.result.ResultDTO;
import com.aliexpress.databank.dataobject.UniqueRes;

import java.util.List;

public interface PaymentServiceJP {

    /**
     * mock支付成功
     * @param params
     * @param systemDTO
     * @return
     * @throws Exception
     */
    ResultDTO mockIPaySuccess(String params, SystemDTO systemDTO) throws Exception;

    JSONObject getPayInstructionDistByOrderId(String orderId);

    UniqueRes getCheckOutOrderNoByOrderId(String orderId);

    JSONObject getPayInstructionByCheckoutOrderNo(String checkoutOrderNo);

    List<String> getBatchPayRealtionNoByBizOrderId(String orderId);

    /**
     * 退款消息发送
     * @param params
     * @param systemDTO
     * @return
     * @throws Exception
     */
    ResultDTO generateMessage(String params, SystemDTO systemDTO) throws Exception;

}

