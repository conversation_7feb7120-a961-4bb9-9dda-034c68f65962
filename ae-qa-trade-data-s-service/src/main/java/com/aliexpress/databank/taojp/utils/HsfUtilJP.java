package com.aliexpress.databank.taojp.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.metrics.StringUtils;
import com.taobao.hsf.app.api.util.HSFApiConsumerBean;
import com.taobao.hsf.remoting.service.GenericService;
import com.taobao.hsf.util.RequestCtxUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;

public class HsfUtilJP {
    private static final Logger logger = LoggerFactory.getLogger(HsfUtilJP.class);

    public static JSONObject queryBatchCheckoutOrder(String repayOrderId) throws Exception {
        String serviceName = "com.taobao.payment.bops.service.CheckoutService";
        String serviceVersion = "1.0.0";
        String methodName = "queryBatchCheckoutOrderByTenantId";
        String groupId = "HSF";
        String[] parameterType = {"java.lang.String","java.lang.String","java.lang.String"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(repayOrderId);
        paramList.add("jp");
        paramList.add("TAOBAO_JP");
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, "33.81.226.22", parameterType, paramList.toArray());
    }

    public static JSONObject queryPayInstructionDist(String checkoutOrder) throws Exception {
        String serviceName = "com.taobao.payment.bops.service.PayInstructionService";
        String serviceVersion = "1.0.0";
        String methodName = "queryPayInstructionDistByTenantId";
        String groupId = "HSF";
        String[] parameterType = {"java.lang.String","java.lang.String","java.lang.String"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(checkoutOrder);
        paramList.add("jp");
        paramList.add("TAOBAO_JP");
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, "33.81.226.22", parameterType, paramList.toArray());
    }

    public static JSONObject queryPayInstruction(String checkoutOrder) throws Exception {
        String serviceName = "com.taobao.payment.bops.service.PayInstructionService";
        String serviceVersion = "1.0.0";
        String methodName = "queryPayInstructionByTenantId";
        String groupId = "HSF";
        String[] parameterType = {"java.lang.String","java.lang.String","java.lang.String"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(checkoutOrder);
        paramList.add("jp");
        paramList.add("TAOBAO_JP");
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, "33.81.226.22", parameterType, paramList.toArray());
    }

    public static JSONObject queryRefundInstruction(String bizOrderNo, Long payerId) throws Exception {
        String serviceName = "com.taobao.payment.bops.service.QueryRefundInstructionService";
        String serviceVersion = "1.0.0";
        String methodName = "queryRefundInstruction";
        String groupId = "HSF";
        String[] parameterType = {"com.taobao.payment.bops.controller.bean.pp.RefundQueryVO"};
        JSONObject request = new JSONObject();
        request.put("siteId", "AE_GLOBAL");
        request.put("payerId", payerId);
        request.put("bizOrderNo", bizOrderNo);
        request.put("tenantId", "jp");
        request.put("business", "TAOBAO_JP");

        List<Object> paramList = new ArrayList<>();
        paramList.add(request.toJavaObject(Object.class));
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject genericServiceInvoke(
            String serviceName, String serviceVersion, String methodName, String groupId, String targetIp,
            String[] parameterType, Object[] parameterValue) throws Exception {
        HSFApiConsumerBean consumerBean = createConsumerBean(serviceName, serviceVersion, groupId, targetIp);
        GenericService genericService = (GenericService) consumerBean.getObject();
        if(StringUtils.isNotBlank(targetIp)) {
            RequestCtxUtil.setDirectTargetServerIp(targetIp);
        }
        Object result = genericService.$invoke(methodName, parameterType, parameterValue);
        return JSONObject.parseObject(JSON.toJSONString(result));
    }

    public static HSFApiConsumerBean createConsumerBean(String serviceName, String serviceVersion, String groupId, String targetIp) throws Exception {
        HSFApiConsumerBean consumerBean = new HSFApiConsumerBean();
        consumerBean.setInterfaceName(serviceName);
        consumerBean.setGeneric("true");
        consumerBean.setVersion(serviceVersion);
        consumerBean.setGroup(groupId);
        consumerBean.setClientTimeout(30000);
        if (StringUtils.isNotBlank(targetIp)) {
            consumerBean.setTarget(targetIp);
        }
        consumerBean.init();
        return consumerBean;
    }

}
