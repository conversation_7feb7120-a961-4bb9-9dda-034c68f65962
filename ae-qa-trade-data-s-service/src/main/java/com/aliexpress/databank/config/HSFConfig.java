package com.aliexpress.databank.config;

import com.alibaba.ae.qa.api.FulfillmentQueryTestFacade;
import com.alibaba.boot.hsf.annotation.HSFConsumer;
import com.aliexpress.qa.platform.api.service.MeasureLogService;
import org.springframework.context.annotation.Configuration;

@Configuration
public class HSFConfig {

    @HSFConsumer(clientTimeout = 3000)
    private FulfillmentQueryTestFacade fulfillmentQueryTestFacade;

    @HSFConsumer(serviceGroup = "HSF", serviceVersion = "1.0.0")
    private MeasureLogService measureLogService;

}
