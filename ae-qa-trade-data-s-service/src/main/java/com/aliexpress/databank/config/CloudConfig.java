//package com.aliexpress.databank.config;
//
//import com.aliyun.kms.security.client.common.SecurityCloudClientManager;
//import com.aliyuncs.kms.secretsmanager.cacheclient.exception.CacheSecretException;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.PostConstruct;
//import java.io.IOException;
//
//@Component
//public class CloudConfig {
//
//    private volatile boolean finishInit;
//
//    @PostConstruct
//    private void init() throws CacheSecretException {
//        initSecurityCloudClientManager();
//        initPermanentClient();
//    }
//
//    private void initSecurityCloudClientManager() throws CacheSecretException {
//        SecurityCloudClientManager.bindingApplication("ae-qa-trade-data-s");
//        this.finishInit = true;
//        Runtime.getRuntime().addShutdownHook(new Thread() {
//            @Override
//            public void run() {
//                if (finishInit) {
//                    try {
//                        SecurityCloudClientManager.shutdown();
//                    } catch (IOException e) {
//                        e.printStackTrace();
//                    }
//                }
//            }
//        });
//    }
//
//    private void initPermanentClient() {
//
//    }
//
//}
