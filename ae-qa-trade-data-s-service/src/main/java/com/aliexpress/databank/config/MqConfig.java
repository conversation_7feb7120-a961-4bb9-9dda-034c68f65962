package com.aliexpress.databank.config;

import bsh.StringUtil;
import com.alibaba.global.satellite.proxy.message.metaq.SatelliteMetaProducer;
import com.alibaba.rocketmq.client.exception.MQBrokerException;
import com.alibaba.rocketmq.client.exception.MQClientException;
import com.alibaba.rocketmq.client.producer.SendResult;
import com.alibaba.rocketmq.common.message.Message;
import com.alibaba.rocketmq.remoting.exception.RemotingException;
import com.aliexpress.databank.constant.Constant;
import com.taobao.metaq.client.MetaProducer;
import com.taobao.top.messaging.util.SerializeUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

import java.util.Map;

@Configuration
@Component
public class MqConfig {

    @Value("metaq.client.url")
    private String clientUrl;

    private MetaProducer producer;

    private SatelliteMetaProducer satelliteProducer;

    public void start() throws MQClientException {
        producer = new MetaProducer(Constant.MQ_PRODUCER);
        producer.setClientIP(clientUrl);
        producer.setProducerGroup(Constant.MQ_PRODUCER);
        producer.start();
    }

    public void satelliteStart() throws MQClientException {
        satelliteProducer = new SatelliteMetaProducer(Constant.SATELLITE_MQ_PRODUCER);
        satelliteProducer.setClientIP(clientUrl);
        satelliteProducer.setProducerGroup(Constant.SATELLITE_MQ_PRODUCER);
        satelliteProducer.start();
    }

    public SendResult sendMessage(String topic, String tag, String key, Map<String, String> properties, String body) throws MQClientException, RemotingException, InterruptedException, MQBrokerException {
        if (producer == null) {
            start();
        }
        Message message = new Message(topic, tag, key, body.getBytes());
        if (!properties.isEmpty()) {
            properties.keySet().forEach(it -> message.putUserProperty(it, properties.get(it)));
        }
        return producer.send(message);
    }

    public SendResult sendMessage(String topic, String tag, String key, String body) throws MQClientException, RemotingException, InterruptedException, MQBrokerException {
        if (producer == null) {
            start();
        }
        Message message = new Message(topic, tag, key, body.getBytes());
        return producer.send(message);
    }

    public SendResult sendMessageViaTop(String topic, String tag, String key, Object body) throws Exception {
        if (producer == null) {
            start();
        }
        Message message = new Message(topic, tag, key, SerializeUtils.convertObjectToBytes(body));
        return producer.send(message);
    }

    public SendResult sendLandlordMessage(String topic, String tag, String key, String body) throws MQClientException, RemotingException, InterruptedException, MQBrokerException {
        if (satelliteProducer == null) {
            satelliteStart();
        }
        Message message = new Message(topic, tag, key, body.getBytes());
        return satelliteProducer.send(message);
    }


}
