package com.aliexpress.databank.config;

import com.alibaba.boot.hsf.annotation.EnableHSF;
import com.aliexpress.qa.common.api.model.output.HsfResult;
import com.aliexpress.qa.platform.api.param.MeasureLogParam;
import com.aliexpress.qa.platform.api.service.MeasureLogService;
import com.aliexpress.qa.platform.api.util.MeasureLogger;
import com.taobao.hsf.app.spring.util.annotation.HSFConsumer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import javax.annotation.PostConstruct;

@Configuration
@ComponentScan(basePackages = "com.aliexpress.qa.platform.api.service")
public class Config {
    @Value("${spring.env}")
    private String env;

    @Autowired
    private MeasureLogService measureLogService;

    @PostConstruct
    private void init() {
        // 应用初始化后设置一次即可
        MeasureLogger.Env e = "online".equals(env) ? MeasureLogger.Env.PROD : ("pre".equals(env)
                ? MeasureLogger.Env.PRE : MeasureLogger.Env.DAILY);
        MeasureLogger.setAppName("ae-qa-trade-data-s");
        MeasureLogger.setSecret("0PptT48s6ao2L3CSRGQYVw==");
        MeasureLogger.setEnv(e);
        MeasureLogger.setRegion("SG");
        MeasureLogger.setMeasureLogService(measureLogService);
    }
}

