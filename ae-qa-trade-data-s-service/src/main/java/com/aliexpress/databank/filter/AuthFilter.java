package com.aliexpress.databank.filter;

import com.alibaba.common.lang.ObjectUtil;
import com.alibaba.common.lang.StringUtil;
import com.alibaba.ecommerce.module.Response;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.global.landlord.LandlordContext;
import com.alibaba.global.order.management.api.facade.OrderQueryForBuyerFacade;
import com.alibaba.global.order.management.api.model.TradeOrderDTO;
import com.alibaba.global.order.management.api.model.TradeOrderLineDTO;
import com.alibaba.global.qa.dc.model.param.SystemDTO;
import com.alibaba.global.qa.dc.model.result.ResultDTO;
import com.aliexpress.databank.constant.Constant;
import com.aliexpress.databank.dataobject.Auth;
import com.aliexpress.databank.hsf.provider.ThubAdaptationServiceImpl;
import com.aliexpress.databank.utils.ConvertParam;
import com.aliexpress.databank.utils.HsfUtil;
import com.aliexpress.issue.common.result.PlainResult;
import com.aliexpress.issue.dispute.pojo.common.Issue;
import com.aliexpress.issue.dispute.service.AeIssueDisputeQueryRemoteService;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.taobao.eagleeye.EagleEye;
import com.taobao.hsf.ApplicationModelAware;
import com.taobao.hsf.domain.HSFResponse;
import com.taobao.hsf.invocation.Invocation;
import com.taobao.hsf.invocation.InvocationHandler;
import com.taobao.hsf.invocation.RPCResult;
import com.taobao.hsf.invocation.filter.ServerFilter;
import com.taobao.hsf.model.ApplicationModel;
import com.taobao.hsf.util.concurrent.Futures;
import com.taobao.hsf.util.concurrent.ListenableFuture;
import com.taobao.hsf.util.concurrent.SettableFuture;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Component
public class AuthFilter implements ServerFilter, ApplicationModelAware {

    private static final Logger logger = LoggerFactory.getLogger(AuthFilter.class);


    private ApplicationModel applicationModel;

    @Autowired
    private OrderQueryForBuyerFacade orderQueryForBuyerFacade;

    @Autowired
    private AeIssueDisputeQueryRemoteService issueDisputeQueryRemoteService;

//    @Autowired
//    private CustomerProductServiceFacade productService;

    @Override
    public ListenableFuture<RPCResult> invoke(InvocationHandler invocationHandler, Invocation invocation) throws Throwable {
        if (orderQueryForBuyerFacade == null) {
            orderQueryForBuyerFacade = (OrderQueryForBuyerFacade) this.applicationModel.getConsumedServiceModel("com.alibaba.global.order.management.api.facade.OrderQueryForBuyerFacade:1.0.0").getProxyObject();
        }
        // 莫名其妙没找到
//        if (productService == null) {
//            productService = (CustomerProductServiceFacade) this.applicationModel.getConsumedServiceModel("com.alibaba.global.ic.api.CustomerProductServiceFacade:1.0.0").getProxyObject();
//        }
        if (issueDisputeQueryRemoteService == null) {
            issueDisputeQueryRemoteService = (AeIssueDisputeQueryRemoteService) this.applicationModel.getConsumedServiceModel("com.aliexpress.issue.dispute.service.AeIssueDisputeQueryRemoteService:1.0.0").getProxyObject();
        }

        Object[] args = invocation.getMethodArgs();
        // admin auth
        SystemDTO systemDTO = getSystemDTO(args);

        if (!Objects.isNull(systemDTO)) {
            // 统计打点
            String empId = systemDTO.getOperator();
            if (!Objects.equals(systemDTO.getSite(), "10190")) {
                HsfUtil.measureAll("/trade/jobId=" + systemDTO.getSite(), empId);
            }
        }

        String buyerId = getParamFromArgs(args, Constant.BUYER_ID);
        Set<String> buyerIds = new HashSet<>();
        Set<String> sellerIds = new HashSet<>();
        String sellerId = getParamFromArgs(args, Constant.SELLER_ID);

        if (buyerId != null && !buyerId.isEmpty()) {
            buyerIds.add(buyerId);
        }

//        Set<String> orderProductIdList = new HashSet<>();
        String orderProductId = getParamFromArgs(args, Constant.ORDER_PRODUCT_ID);
//        if (orderProductId != null && !orderProductId.isEmpty()) {
//            orderProductIdList.add(orderProductId);
//        }


        String orderIds = getParamFromArgs(args, Constant.ORDERS);
        String orderId = getParamFromArgs(args, Constant.ORDER_ID);
        String subOrderId = getParamFromArgs(args, Constant.SUB_ORDER_ID);
        String issueId = getParamFromArgs(args, Constant.ISSUE_ID);
        String reverseOrderLineId = getParamFromArgs(args, Constant.REVERSE_ORDER_LINE_ID);
        String havanaId = getParamFromArgs(args, Constant.HAVANA_ID);
        String tradeOrderLineId = getParamFromArgs(args, Constant.TRADE_ORDER_LINE_ID);
        String breachId = getParamFromArgs(args, Constant.BREACH_ID);
        String dpath = getParamFromArgs(args, Constant.DPATH_ENV);
        String guaranteeLineId = getParamFromArgs(args, Constant.GUARANTEE_LINE_ID);

        //子单列表
        String subOrderIds = getParamFromArgs(args, Constant.NEED_CANCEL_ORDER_LINE_DATA);

        // custom attributes for api
        Map<String, String> customArgs = new ConcurrentHashMap<>();
        // get buyerId and sellerId from orderInfo
        if (StringUtils.isNotBlank(orderIds)) {
            String[] orders = orderIds.split(",");
            for (String order : orders) {
                Response<TradeOrderDTO> tradeOrderDTOResponse = orderQueryForBuyerFacade.queryTradeOrderById(Long.parseLong(order));
                if (tradeOrderDTOResponse.isNotSuccess()) {
                    return getErrorResponse("Fail to get response from trade-center. TraceId: " + EagleEye.getTraceId());
                }

                String buyerIdByOrder = tradeOrderDTOResponse.getModule().getBuyer().getBuyerId().toString();
                String sellerIdByOrder = tradeOrderDTOResponse.getModule().getOrderLines().get(0).getSeller().getSellerId().toString();

                customArgs.put(Constant.BUYER_ID, buyerIdByOrder);
                customArgs.put(Constant.SELLER_ID, sellerIdByOrder);

                buyerIds.add(buyerIdByOrder);
                sellerIds.add(sellerIdByOrder);
                log.info("buyerIds: " + JSONObject.toJSONString(buyerIds));
                log.info("sellerIds: " + JSONObject.toJSONString(sellerIds));
            }
        } else if (StringUtil.isNotEmpty(orderId)) {
            try {
                LandlordContext.setTenantSpec("AE_GLOBAL", -1);

                String staging = getParamFromArgs(args, Constant.STAGING);
                if (StringUtil.isNotBlank(staging)) {
                    EagleEye.putUserData("scm_project", staging);//临时处理
                }

                Response<TradeOrderDTO> tradeOrderDTOResponse = orderQueryForBuyerFacade.queryTradeOrderById(Long.parseLong(orderId));

                if (tradeOrderDTOResponse.isNotSuccess()) {
                    return getErrorResponse("Fail to get response from trade-center. TraceId: " + EagleEye.getTraceId());
                }

                if (tradeOrderDTOResponse.getModule() == null) {
                    // 还款订单
                    JSONObject repayRes = HsfUtil.queryBatchCheckoutOrder(orderId);
                    if (null != repayRes) {
                        JSONArray records = repayRes.getJSONArray("records");
                        if (!records.isEmpty()) {
                            buyerId = records.getJSONObject(0).getString("payerId");
                            sellerId = records.getJSONObject(0).getString("payeeId");
                        }
                    } else {
                        return getErrorResponse("该主单号不存在，请检查输入正确的交易主单id，Order Not Exist");
                    }
                } else {
                    buyerId = tradeOrderDTOResponse.getModule().getBuyer().getBuyerId().toString();
                    sellerId = tradeOrderDTOResponse.getModule().getOrderLines().get(0).getSeller().getSellerId().toString();
                    customArgs.put(Constant.ORDER_INFO, JSON.toJSONString(tradeOrderDTOResponse));
                    customArgs.put(Constant.ORDER_LINES, JSON.toJSONString(tradeOrderDTOResponse.getModule().getOrderLines()));
                    customArgs.put(Constant.SUB_ORDER_ID, tradeOrderDTOResponse.getModule().getOrderLines().get(0).getTradeOrderLineId().toString());
                }
                sellerIds.add(sellerId);
//                orderProductId = tradeOrderDTOResponse.getModule().getOrderLines().get(0).getProduct().getItemId();
//                orderProductIdList.add(orderProductId);
                customArgs.put(Constant.BUYER_ID, buyerId);
                customArgs.put(Constant.SELLER_ID, sellerId);
            } catch (JSONException e) {
                return getErrorResponse("Illegal Order Id. Order Id should be Long type");
            } catch (Exception e) {
                return getErrorResponse(e.getMessage());
            }
        } else if (!StringUtil.isEmpty(subOrderId)) {
            LandlordContext.setTenantSpec("AE_GLOBAL", -1);

            String staging = getParamFromArgs(args, Constant.STAGING);
            if (StringUtil.isNotBlank(staging)) {
                EagleEye.putUserData("scm_project", staging);//临时处理
            }

            Response<TradeOrderLineDTO> tradeOrderLineDTOResponse = orderQueryForBuyerFacade.queryTradeOrderLineById(Long.parseLong(subOrderId));
            if (tradeOrderLineDTOResponse.isNotSuccess()) {
                return getErrorResponse("Fail to get response from trade-center. TraceId: " + EagleEye.getTraceId());
            }

            if (tradeOrderLineDTOResponse.getModule() == null) {
                return getErrorResponse("该子单号不存在，请检查输入正确的交易子单id，TradeOrderLineId Not Exist");
            }
            Response<TradeOrderDTO> tradeOrderDTOResponse = orderQueryForBuyerFacade.queryTradeOrderById(tradeOrderLineDTOResponse.getModule().getTradeOrderId());


            buyerId = tradeOrderLineDTOResponse.getModule().getBuyer().getBuyerId().toString();
            sellerId = tradeOrderLineDTOResponse.getModule().getSeller().getSellerId().toString();
            sellerIds.add(sellerId);
            orderProductId = tradeOrderLineDTOResponse.getModule().getProduct().getItemId();

            customArgs.put(Constant.ORDER_ID, tradeOrderDTOResponse.getModule().getTradeOrderId().toString());
            customArgs.put(Constant.ORDER_INFO, JSON.toJSONString(tradeOrderDTOResponse));
            customArgs.put(Constant.SUB_ORDER_INFO, JSON.toJSONString(tradeOrderLineDTOResponse));
            customArgs.put(Constant.BUYER_ID, buyerId);
            customArgs.put(Constant.SELLER_ID, sellerId);
            customArgs.put(Constant.ORDER_PRODUCT_ID, orderProductId);
            customArgs.put(Constant.ORDER_LINES, JSON.toJSONString(tradeOrderDTOResponse.getModule().getOrderLines()));
        } else if (!StringUtil.isEmpty(tradeOrderLineId)) {
            LandlordContext.setTenantSpec("AE_GLOBAL", -1);

            String staging = getParamFromArgs(args, Constant.STAGING);
            if (StringUtil.isNotBlank(staging)) {
                EagleEye.putUserData("scm_project", staging);//临时处理
            }

            Response<TradeOrderLineDTO> tradeOrderLineDTOResponse = orderQueryForBuyerFacade.queryTradeOrderLineById(Long.parseLong(tradeOrderLineId));
            if (tradeOrderLineDTOResponse.isNotSuccess()) {
                return getErrorResponse("Fail to get response from trade-center. TraceId: " + EagleEye.getTraceId());
            }

            if (tradeOrderLineDTOResponse.getModule() == null) {
                return getErrorResponse("该子单号不存在，请检查输入正确的交易子单id，TradeOrderLineId Not Exist");
            }
            Response<TradeOrderDTO> tradeOrderDTOResponse = orderQueryForBuyerFacade.queryTradeOrderById(tradeOrderLineDTOResponse.getModule().getTradeOrderId());


            buyerId = tradeOrderLineDTOResponse.getModule().getBuyer().getBuyerId().toString();
            sellerId = tradeOrderLineDTOResponse.getModule().getSeller().getSellerId().toString();
            sellerIds.add(sellerId);
            orderProductId = tradeOrderLineDTOResponse.getModule().getProduct().getItemId();
         //   String countryCode = tradeOrderDTOResponse.getModule().getDeliveryAddress().getCountryCode();

            customArgs.put(Constant.ORDER_ID, tradeOrderDTOResponse.getModule().getTradeOrderId().toString());
            customArgs.put(Constant.SUB_ORDER_ID,tradeOrderLineId);
            customArgs.put(Constant.TRADE_ORDER_LINE_ID,tradeOrderLineId);
            customArgs.put(Constant.ORDER_INFO, JSON.toJSONString(tradeOrderDTOResponse));
            customArgs.put(Constant.SUB_ORDER_INFO, JSON.toJSONString(tradeOrderLineDTOResponse));
            customArgs.put(Constant.BUYER_ID, buyerId);
            customArgs.put(Constant.SELLER_ID, sellerId);
            customArgs.put(Constant.ORDER_PRODUCT_ID, orderProductId);
         //   customArgs.put(Constant.COUNTRY_CODE, countryCode);
        } else if (!StringUtil.isEmpty(subOrderIds)) {
            LandlordContext.setTenantSpec("AE_GLOBAL", -1);

            String staging = getParamFromArgs(args, Constant.STAGING);
            if (StringUtil.isNotBlank(staging)) {
                EagleEye.putUserData("scm_project", staging);//临时处理
            }

            String[] subOrders = subOrderIds.split(",");
            List<Long> orderLineIds = Lists.newArrayList();

            Long tradeOrderId = null;
            Response<TradeOrderLineDTO> tradeOrderLineDTOResponse = null;
            List<Response<TradeOrderLineDTO>> tradeOrderLineDTOResponseList = new ArrayList<>();
            for (String subId : subOrders) {
                tradeOrderLineDTOResponse = orderQueryForBuyerFacade.queryTradeOrderLineById(Long.parseLong(subId));
                if (tradeOrderLineDTOResponse.isNotSuccess()) {
                    return getErrorResponse("Fail to get response from trade-center. TraceId: " + EagleEye.getTraceId());
                }

                if (tradeOrderLineDTOResponse.getModule() == null) {
                    return getErrorResponse("该子单号不存在，请检查输入正确的交易子单id，order Not Exist");
                }
                tradeOrderId = tradeOrderLineDTOResponse.getModule().getTradeOrderId();
                tradeOrderLineDTOResponseList.add(tradeOrderLineDTOResponse);
                orderLineIds.add(Long.parseLong(subId));
            }
            if (tradeOrderId == null || tradeOrderLineDTOResponse == null) {
                return getErrorResponse("该子单号不存在，请检查输入正确的交易子单id，Order Not Exist");
            }

            Response<TradeOrderDTO> tradeOrderDTOResponse = orderQueryForBuyerFacade.queryTradeOrderById(tradeOrderId);
            buyerId = tradeOrderLineDTOResponse.getModule().getBuyer().getBuyerId().toString();
            sellerId = tradeOrderLineDTOResponse.getModule().getSeller().getSellerId().toString();
            sellerIds.add(sellerId);
            orderProductId = tradeOrderLineDTOResponse.getModule().getProduct().getItemId();

            customArgs.put(Constant.ORDER_ID, tradeOrderDTOResponse.getModule().getTradeOrderId().toString());
            customArgs.put(Constant.ORDER_INFO, JSON.toJSONString(tradeOrderDTOResponse));
            customArgs.put(Constant.SUB_ORDER_INFO, JSON.toJSONString(tradeOrderLineDTOResponseList));
            customArgs.put(Constant.BUYER_ID, buyerId);
            customArgs.put(Constant.SELLER_ID, sellerId);
            customArgs.put(Constant.ORDER_PRODUCT_ID, orderProductId);
            customArgs.put(Constant.NEED_CANCEL_ORDER_LINE_DATA, subOrderIds);
            customArgs.put(Constant.ORDER_LINE_IDS, JSONObject.toJSONString(orderLineIds));
        } else if (!StringUtil.isEmpty(tradeOrderLineId) & !StringUtil.isEmpty(buyerId)) {
            JSONObject reverseOrderLines = HsfUtil.getLastReverseOrderByTradeOrderLineId(Long.parseLong(buyerId), Long.parseLong(tradeOrderLineId));
            if (null != reverseOrderLines) {
                JSONObject lastReverseOrder = reverseOrderLines.getJSONObject("result");
                if (!lastReverseOrder.isEmpty()) {
                    String sellerId1 = lastReverseOrder.getString("sellerId");
                    sellerIds.add(sellerId1);
                    customArgs.put(Constant.SELLER_ID, sellerId1);
                } else {
                    return getErrorResponse("没有查到符合的逆向单");
                }
            } else {
                return getErrorResponse("该子单号不存在，请检查输入正确的交易子单id，Order Not Exist");
            }
        } else if (StringUtil.isNotBlank(breachId)) {
            JSONObject breachContract = HsfUtil.queryBreachContractDO(Long.valueOf(breachId));
            if (breachContract != null) {
                sellerId = breachContract.getString("defaultingPartyId");
                sellerIds.add(sellerId);
                customArgs.put(Constant.SELLER_ID, sellerId);
                customArgs.put(Constant.BREACH_CONTRACT_INFO, breachContract.toJSONString());
            }
        }
        else if (StringUtil.isNotBlank(guaranteeLineId)) {
            JSONObject breachContract = HsfUtil.queryBreachContractDO(Long.valueOf(guaranteeLineId));
            if (breachContract != null) {
                buyerId = breachContract.getString("defaultingPartyId");
                buyerIds.add(buyerId);
                customArgs.put(Constant.BUYER_ID, buyerId);
                customArgs.put(Constant.BREACH_CONTRACT_INFO, breachContract.toJSONString());
            }
        }


        // 添加dpath
        if (StringUtil.isNotBlank(dpath)) {
            EagleEye.putUserData("dpath_env", dpath);
        }


        if (Auth.getNoNeedAuthFilterMethod().contains(invocation.getMethodName())) {
            invocation.setMethodArgs(rebuildArgs(args, customArgs));
            return invocationHandler.invoke(invocation);
        } else if (StringUtil.isNotBlank(getParamFromArgs(args, Constant.ITEM_IDS))) {
            String productIdsStr = getParamFromArgs(args, Constant.ITEM_IDS);
            if (StringUtil.isEmpty(productIdsStr)) {
                return getErrorResponse("Illegal Item Id. No Item Id Exist");
            }
            List<Long> productIds = ConvertParam.parseLongFromStr(productIdsStr);
            for (Long productId : productIds) {
                JSONObject queryResult = HsfUtil.getProductByProductId(productId);
                if (!queryResult.getBoolean("success")) {
                    return getErrorResponse("Fail to get response from ic | " + EagleEye.getTraceId());
                }
                if (queryResult.getJSONObject("singleProduct") == null) {
                    return getErrorResponse("Illegal Item Id. Fail to get item. Item Id: " + productId);
                }
                sellerIds.add(queryResult.getJSONObject("singleProduct").getJSONObject("sellerQueryResultDTO").getString("id"));
            }

            customArgs.put(Constant.BUYER_ID, buyerId);
            customArgs.put(Constant.ITEM_IDS, JSON.toJSONString(productIds));
            customArgs.put(Constant.SELLER_ID, JSON.toJSONString(sellerIds));
        } else if (!StringUtil.isEmpty(issueId)) {
            PlainResult<Issue> issue = issueDisputeQueryRemoteService.findIssueByIssueIdForInternalService(Long.parseLong(issueId));
            if (!issue.isSuccess() || issue.getData() == null) {
                return getErrorResponse("Illegal IssueId. Issue id doesn't exist. IssueId: " + issueId);
            }
            buyerId = issue.getData().getBuyerAliid().toString();
            sellerIds.add(issue.getData().getSellerAliid().toString());
            customArgs.put(Constant.BUYER_ID, buyerId);
            customArgs.put(Constant.SELLER_ID, JSON.toJSONString(sellerIds));
            customArgs.put(Constant.ISSUE_INFO, JSON.toJSONString(issue.getData()));
        } else if (StringUtil.isNotBlank(reverseOrderLineId)) {
            JSONObject reverseOrderInfo = HsfUtil.getReverseOrderByReverseOrderLineId(Long.valueOf(reverseOrderLineId));
            buyerId = reverseOrderInfo.getJSONObject("module").getJSONObject("buyer").getString("userId");
            sellerIds.add(reverseOrderInfo.getJSONObject("module").getJSONObject("seller").getString("userId"));
            customArgs.put(Constant.BUYER_ID, buyerId);
            customArgs.put(Constant.SELLER_ID, JSON.toJSONString(sellerIds));
        } else {
            if (havanaId != null && !havanaId.isEmpty()) {
                JSONObject userInfo = HsfUtil.getUserByHavanaId(Long.valueOf(havanaId));
                if (!userInfo.getBoolean("success") || userInfo.getJSONObject("module") == null) {
                    return getErrorResponse("Illegal havana Id. Fail to get user by this havanaId");
                }
                Long userId = userInfo.getJSONObject("module").getLong("userId");
                if ((Auth.getAdminAuthFilterMethod().contains(invocation.getMethodName()) && Auth.getADMIN().contains(systemDTO.getOperator())) || isTestBuyer(String.valueOf(userId))) {
                    invocation.setMethodArgs(rebuildArgs(args, customArgs));
                    return invocationHandler.invoke(invocation);
                } else {
                    return getErrorResponse("Illegal User Id. User should be test account");
                }
            }
        }


        if (Auth.getAdminAuthFilterMethod().contains(invocation.getMethodName()) && Auth.getADMIN().contains(systemDTO.getOperator())) {
            invocation.setMethodArgs(rebuildArgs(args, customArgs));
            return invocationHandler.invoke(invocation);
        }

        // customPlaceOrder only need buyerId. itemId is from diamond
        if (Auth.BUYER_AUTH.contains(invocation.getMethodName())) {
            if (isTestBuyer(buyerId)) {
                invocation.setMethodArgs(rebuildArgs(args, customArgs));
                return invocationHandler.invoke(invocation);
            } else {
                return getErrorResponse("Illegal Buyer Id. BuyerId should be test account");
            }
        }

        // seller auth
        if (Auth.SELLER_AUTH.contains(invocation.getMethodName())) {
            if (isTestSeller(sellerId)) {
                invocation.setMethodArgs(rebuildArgs(args, customArgs));
                return invocationHandler.invoke(invocation);
            }
            return getErrorResponse("Illegal Seller Id. Seller should be test account");
        }


        if ((StringUtil.isEmpty(buyerId) && buyerIds.isEmpty()) || sellerIds.isEmpty()) {
            return getErrorResponse("Missing Buyer Id or Seller Id.");
        }

        if (!isTestBuyer(buyerId)) {
            if (!isTestBuyer(buyerIds)) {
                return getErrorResponse("Illegal Buyer Id. BuyerId should be test account");
            }
        }

        if (sellerIds.isEmpty()) {
            return getErrorResponse("Illegal Product. Product should be test product");
        } else
            for (String item : sellerIds) {
                if (!isTestSeller(item)) {
                    return getErrorResponse("Illegal Product. Product should be test product");
                }
            }

        invocation.setMethodArgs(rebuildArgs(args, customArgs));
        return invocationHandler.invoke(invocation);
    }

    private boolean isTestBuyer(Set<String> buyerIds) throws Exception {
        for (String buyerId : buyerIds) {
            if (!isTestBuyer(buyerId)) {
                return false;
            }
        }
        return true;
    }

    private boolean isTestBuyer(String userId) throws Exception {
        if (Constant.IS_TEST_FLAG) {
            JSONObject res = HsfUtil.getUserTagsByUserIdAndTag(userId, Constant.TEST_USER_FLAG);
            return res.getBoolean("success") && res.getBoolean("module");
        } else {
            return Constant.USER_WHITE_LIST.contains(Long.valueOf(userId));
        }
    }

    private boolean isTestSeller(String userId) throws Exception {
        if (Constant.IS_TEST_FLAG) {
            JSONObject res = HsfUtil.getSellerTagBySellerId(userId, Constant.TEST_USER_FLAG);
            if (res.getBoolean("success") && res.getJSONObject("module") != null) {
                JSONObject module = res.getJSONObject("module");
                Map<Long, Map<String, Boolean>> tags = new Gson().fromJson(module.getJSONObject("booleanTagCodeResultMap").toJSONString(),
                        new TypeToken<Map<Long, Map<String, Boolean>>>() {
                        }.getType());
                return tags.get(Long.valueOf(userId)).get(Constant.TEST_USER_FLAG);
            }
            // return false;
            return Constant.USER_WHITE_LIST.contains(Long.valueOf(userId));
        } else {
            return Constant.USER_WHITE_LIST.contains(Long.valueOf(userId));
        }
    }

    private String getParamFromArgs(Object[] args, String key) {
        for (Object arg : args) {
            if (arg.toString().contains(key)) {
                if (arg instanceof String) {
                    JSONObject params = JSON.parseObject(arg.toString());
                    return params.getString(key);
                } else {
                    String data = JSON.toJSONString(arg);
                    JSONObject params = JSON.parseObject(data);
                    return params.getString(key);
                }
            }
        }
        return null;
    }

    private SystemDTO getSystemDTO(Object[] args) {
        SystemDTO systemDTO = null;
        for (Object arg : args) {
            if (arg instanceof SystemDTO) {
                systemDTO = (SystemDTO) arg;
            }
        }
        return systemDTO;
    }

    private Object[] rebuildArgs(Object[] args, Map<String, String> customArgs) {
        if (customArgs.isEmpty()) {
            return args;
        }
        if (args[0] instanceof String) {
            String arg = args[0].toString();
            StringBuilder sb = new StringBuilder(arg);
            sb.deleteCharAt(sb.length() - 1);
            for (String key : customArgs.keySet()) {
                sb.append(",\"").append(key).append("\":")
                        .append(customArgs.get(key));
            }
            sb.append("}");
            args[0] = sb.toString();
        }
        Constant.EXTRA_PARAMS = customArgs;
        return args;
    }

    private ListenableFuture<RPCResult> getErrorResponse(String errorMessage) {
        SettableFuture<RPCResult> defaultRPCFuture = Futures.createSettableFuture();
        RPCResult rpcResult = new RPCResult();
        ResultDTO resultDTO = new ResultDTO();
        resultDTO.setSuccess(false);
        resultDTO.setMessage(errorMessage);
        resultDTO.setData(errorMessage);
        // remove will throw NPE because setAppResponse need HsfResponse
        rpcResult.setHsfResponse(new HSFResponse());
        rpcResult.setAppResponse(resultDTO);
        defaultRPCFuture.set(rpcResult);
        return defaultRPCFuture;
    }

    @Override
    public void onResponse(Invocation invocation, RPCResult rpcResult) {
    }

    @Override
    public void setApplicationModel(ApplicationModel applicationModel) {
        this.applicationModel = applicationModel;
    }

}
