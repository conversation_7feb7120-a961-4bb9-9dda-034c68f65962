package com.aliexpress.databank.filter;

import com.aliexpress.databank.hsf.provider.WalletServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.*;

public class UserCallTracker {
    private static final Logger logger = LoggerFactory.getLogger(UserCallTracker.class);
    private final Map<String, List<Long>> userCallRecords = new ConcurrentHashMap<>();
    private final long timeWindow; // 时间窗口（毫秒）
    private final int maxCalls; // 允许的最大调用次数

    public UserCallTracker(int maxCalls, long timeWindow) {
        this.maxCalls = maxCalls;
        this.timeWindow = timeWindow;
    }

    // 记录调用次数
    public boolean recordCall(String userId) {
        long currentTime = System.currentTimeMillis();
        userCallRecords.putIfAbsent(userId, new CopyOnWriteArrayList<>());

        List<Long> callTimestamps = userCallRecords.get(userId);
        // 清除超出时间窗口的数据
        callTimestamps.removeIf(timestamp -> currentTime - timestamp > timeWindow);
        logger.info("User ID: " + userId + ", Current Calls: " + callTimestamps.size() + ", Max Calls Allowed: " + maxCalls);
        if (callTimestamps.size() >= maxCalls) {
            logger.info("Call limit reached for user: " + userId);
            return false; // 超过次数限制
        }
        logger.info("Recorded call for user: " + userId + " at time: " + currentTime);
        callTimestamps.add(currentTime); // 记录当前调用时间
        return true; // 调用成功
    }
}
