package com.aliexpress.databank.service;

import com.alibaba.ecommerce.module.Response;
import com.alibaba.global.buy.api.response.CreateOrderResult;
import com.alibaba.global.order.management.api.model.TradeOrderDTO;
import com.aliexpress.databank.dataobject.GetPlaceOrderDTO;
import com.taobao.mtop.common.Result;

import java.util.List;

public interface TradeService {

    Response<CreateOrderResult> getPlaceOrderResponse(Long buyerId, Long itemId, String countryCode, String currencyCode, String deliveryOption) throws Exception;

    Result<CreateOrderResult> getPlaceOrderResponse(GetPlaceOrderDTO getPlaceOrderDTO) throws Exception;

    Response<TradeOrderDTO> queryTradeOrderById(Long buyerId, Long orderId);

    Response<TradeOrderDTO> queryTradeOrderById(Long orderId);

    Response<CreateOrderResult> getPlaceOrderResponse(TradeOrderDTO tradeOrderDTO) throws Exception;

}
