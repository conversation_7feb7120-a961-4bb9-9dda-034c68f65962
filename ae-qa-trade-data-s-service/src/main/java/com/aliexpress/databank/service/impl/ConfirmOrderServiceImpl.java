package com.aliexpress.databank.service.impl;

import com.alibaba.ecommerce.module.Response;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.global.order.management.api.facade.OrderManagementRegionFacade;
import com.alibaba.global.order.management.api.facade.OrderQueryForBuyerFacade;
import com.alibaba.global.order.management.api.model.TradeOrderDTO;
import com.alibaba.global.order.management.api.model.TradeOrderLineDTO;
import com.alibaba.global.order.management.api.request.BuyerConfirmGoodsRequest;
import com.aliexpress.databank.dataobject.ReverseOrder;
import com.aliexpress.databank.mapper.ReverseOrderMapper;
import com.aliexpress.databank.service.ConfirmOrderService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ConfirmOrderServiceImpl implements ConfirmOrderService {

    @Autowired
    private ReverseOrderMapper reverseOrderMapper;

    @Autowired
    private OrderQueryForBuyerFacade orderQueryForBuyerFacade;

    @Autowired
    private OrderManagementRegionFacade orderManagementRegionFacade;

    @Override
    public void confirmOrders() {
        try {
            List<ReverseOrder> orders = reverseOrderMapper.findUnConfirmOrder();
            orders.forEach(order -> {
                Response<TradeOrderDTO> tradeOrderDTOResponse = orderQueryForBuyerFacade.queryTradeOrderById(order.getBuyerId(), order.getOrderId());
                if (tradeOrderDTOResponse.getModule().getOrderStatus() == 4) {
                    List<Long> tradeOrderLineIds = new ArrayList<>();
                    tradeOrderLineIds.addAll(tradeOrderDTOResponse.getModule().getOrderLines().stream().map(TradeOrderLineDTO::getTradeOrderLineId).collect(Collectors.toList()));
                    BuyerConfirmGoodsRequest buyerConfirmGoodsRequest = new BuyerConfirmGoodsRequest();
                    buyerConfirmGoodsRequest.setBuyerId(order.getBuyerId());
                    buyerConfirmGoodsRequest.setTradeOrderLineIds(tradeOrderLineIds);
                    Response res = orderManagementRegionFacade.buyerConfirmGoods(buyerConfirmGoodsRequest);
                    if (res.isSuccess()) {
                        order.setConfirmed(true);
                        reverseOrderMapper.updateReverseOrder(order);
                    } else {
                        log.error("Fail to Confirm Order. Error: " + JSONObject.toJSONString(res));
                    }
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
