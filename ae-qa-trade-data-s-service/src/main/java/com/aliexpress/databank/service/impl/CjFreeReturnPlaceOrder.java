package com.aliexpress.databank.service.impl;

import com.aliexpress.databank.constant.OrderScenario;
import com.aliexpress.databank.dataobject.PlaceOrderValidation;
import org.springframework.stereotype.Service;
// 无忧退款：保司退货 cj：场金
@Service
public class CjFreeReturnPlaceOrder extends BasePlaceOrderStrategy {

    @Override
    public OrderScenario getScenario() {
        return OrderScenario.CJ_FREE_RETURN;
    }

    @Override
    public PlaceOrderValidation validate(Long tradeOrderId, Long promiseTemplateId, String features) {
        return super.validate(tradeOrderId, 17L, "cj");
    }

}

