package com.aliexpress.databank.service;

import com.alibaba.ecommerce.module.Response;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.global.order.management.api.model.TradeOrderLineDTO;
import com.alibaba.global.reverse.api.model.ReverseOrderDTO;
import com.alibaba.global.reverse.api.model.ReverseOrderLineDTO;
import com.aliexpress.issue.common.result.PlainResult;
import com.aliexpress.issue.dispute.pojo.common.CancelOrderOperatorRequest;
import com.aliexpress.issue.dispute.pojo.common.IssueCancelOrderOperatorResult;

import java.util.List;

/**
 * Response<TradeOrderDTO> tradeOrderDTOResponse = orderQueryForBuyerFacade.queryTradeOrderById(orderId);
 * List<TradeOrderLineDTO> tradeOrderLineDTOs = tradeOrderDTOResponse.getModule().getOrderLines();
 */
public interface ReverseService {

    PlainResult<IssueCancelOrderOperatorResult> cancelOrder(Long buyerId, Long orderId, String cancelEvent, List<TradeOrderLineDTO> tradeOrderLineDTOs, String refundChannel);

    CancelOrderOperatorRequest getCancelOrderRequest(Long buyerId, Long orderId, String cancelEvent, List<TradeOrderLineDTO> orderLines, String refundChannel);

    PlainResult<IssueCancelOrderOperatorResult> rejectCancelOrder(Long buyerId, Long sellerId, Long orderId, String applyReason, List<TradeOrderLineDTO> tradeOrderLineDTOs);

    PlainResult<IssueCancelOrderOperatorResult> agreeCancelOrder(Long buyerId, Long sellerId, Long orderId, String applyReason, List<TradeOrderLineDTO> tradeOrderLineDTOs);

    Response<List<ReverseOrderLineDTO>> getBuyerReverseOrder(Long buyerId, Long orderId);

    Response<ReverseOrderLineDTO> getReverseOrderLineByReverseOrderLineId(Long buyerId, Long reverseOrderLineId);

    Response<List<ReverseOrderDTO>> getSellerSearchReverseOrder(Long orderId);

    Long placeReverseOrderByScenario(String scenario, Long buyerId, Long sellerId, String tradeOrderId, String tradeOrderLineId, JSONObject tradeOrderInfo) throws Exception;

    JSONObject getCreateReverseReq(String tradeOrderLineId, String tradeOrderId, String refundChannel, Long buyerId, boolean isReceived, String returnOrRefund, String amount, String currency, String returnType, String returnReason, int quantity, Boolean isOverSize);

    JSONObject getCreateReverseV3Req(String tradeOrderLineId, String tradeOrderId, String refundChannel, Long buyerId, String returnReason, int quantity)throws Exception;

    JSONObject getComment();

    JSONObject getRefundAmount(String amount, String currency);

    JSONObject getActiveSolution(JSONObject solutionRes);

}