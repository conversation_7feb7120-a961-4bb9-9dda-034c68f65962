package com.aliexpress.databank.service;

import com.alibaba.rocketmq.client.exception.MQBrokerException;
import com.alibaba.rocketmq.client.exception.MQClientException;
import com.alibaba.rocketmq.remoting.exception.RemotingException;


public interface RefundRecordService {

    String sendSecondRefundMsg(Long buyerId, Long tradeOrderId, Long tradeOrderLineId, String outUniqueSeq) throws InterruptedException, RemotingException, MQClientException, MQBrokerException;

    String sendAsyncSecondRefundMsg(Long buyerId, Long tradeOrderId, Long tradeOrderLineId, String outUniqueSeq) throws InterruptedException, RemotingException, MQClientException, MQBrokerException;

    String sendInitOrSettleRecordMsg(Long buyerId, String refundChannel, Long tradeOrderId, Long tradeOrderLineId, String valueOf, String refundStage, String type) throws InterruptedException, RemotingException, MQ<PERSON><PERSON>Exception, MQBrokerException;
}
