package com.aliexpress.databank.service;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.global.buy.api.request.CreateOrderRequest;
import com.aliexpress.databank.constant.Constant;
import com.aliexpress.databank.constant.OrderScenario;
import com.aliexpress.databank.dataobject.OrderScenarioModule;
import com.aliexpress.databank.dataobject.PlaceOrderValidation;
import com.aliexpress.databank.dataobject.ReverseOrder;
import com.aliexpress.databank.mapper.ReverseOrderMapper;
import com.google.common.collect.Lists;
import lombok.extern.log4j.Log4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
@Log4j
public class PlaceOrderTemplate {

    @Autowired
    private ReverseOrderMapper reverseOrderMapper;

    public void placeOrder(Long buyerId, PlaceOrderStrategy placeOrderStrategy) {
        OrderScenario orderScenario = placeOrderStrategy.getScenario();
        List<OrderScenarioModule> orderScenarioModules = Constant.ORDER_SCENARIOS.get(orderScenario.getName());
        orderScenarioModules.forEach(orderScenarioModule -> {
                    try {
                        int count = placeOrderStrategy.isGenerate(buyerId, orderScenario.getName(), orderScenarioModule.getIndex());
                        List<Long> orderIds = Lists.newArrayList();
                        for (int i = 0; i < count; i++) {
                            try {
                                CreateOrderRequest createOrderRequest = placeOrderStrategy.getCreateOrderRequest(buyerId, orderScenarioModule.getItemIds(),
                                        orderScenarioModule.getCurrencyCode(), orderScenarioModule.getDeliveryCountry(), orderScenarioModule.getShippingMethod());
                                if (createOrderRequest == null) {
                                    log.error("Fail to get CreateOrderRequest. Scenario: " + orderScenario.getName()
                                            + ". BuyerId: " + buyerId + ", OrderScenarioModule: " + JSONObject.toJSONString(orderScenarioModule));
                                    continue;
                                }

                                List<String> orderIdsGenerate = placeOrderStrategy.placeOrder(createOrderRequest);
                                if (CollectionUtils.isEmpty(orderIdsGenerate)) {
                                    log.error("Fail to get CreateOrderRequest. Scenario: " + orderScenario.getName()
                                            + ". BuyerId: " + buyerId + ", OrderScenarioModule: " + JSONObject.toJSONString(orderScenarioModule));
                                    continue;
                                }

                                orderIdsGenerate.forEach(orderId -> {
                                    PlaceOrderValidation validation = placeOrderStrategy.validate(Long.valueOf(orderId), orderScenario.getName());
                                    if (validation.isMatch()) {
                                        orderIds.add(Long.valueOf(orderId));
                                    }
                                });
                            } catch (Exception e) {
                                e.printStackTrace();
                                log.error("Place Order Error. Scenario: " + orderScenario.getName(), e);
                            }
                        }

                        List<ReverseOrder> reverseOrders = new ArrayList<>();
                        orderIds.forEach(orderId -> {
                            if (placeOrderStrategy.placeOrderResConvert2ReverseOrder(orderId, orderScenario.getName(), orderScenarioModule.getIndex()) != null) {
                                reverseOrders.add(placeOrderStrategy.placeOrderResConvert2ReverseOrder(orderId, orderScenario.getName(), orderScenarioModule.getIndex()));
                            }
                        });
                        List<List<ReverseOrder>> reverseOrderSplits = Lists.partition(reverseOrders, 10);

                        reverseOrderSplits.forEach(reverseOrderSplit -> {
                            try {
                                reverseOrderMapper.insertReverseOrders(reverseOrderSplit);
                            } catch (Exception e) {
                                log.error("Fail to insert to the db. Orders: " + JSONObject.toJSONString(reverseOrderSplit));
                            }
                        });
                    } catch (Exception e) {
                        e.printStackTrace();
                        log.error("Fail to place Order. Scenario: " + JSONObject.toJSONString(orderScenarioModule));
                    }
                }
        );
    }

}
