package com.aliexpress.databank.service.impl;

import com.aliexpress.databank.constant.OrderScenario;
import com.aliexpress.databank.mapper.ReverseOrderMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class NegotiationPlaceOrder extends BasePlaceOrderStrategy {

    @Autowired
    private ReverseOrderMapper reverseOrderMapper;

    @Override
    public OrderScenario getScenario() {
        return OrderScenario.NORMAL_NEGOTIATION;
    }






}

