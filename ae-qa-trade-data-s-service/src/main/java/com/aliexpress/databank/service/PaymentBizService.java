package com.aliexpress.databank.service;

import com.alibaba.ecommerce.module.Response;
import com.alibaba.global.order.management.api.model.TradeOrderDTO;
import com.alibaba.global.payment.api.dto.payment.ChosenIntentionDTO;
import com.alibaba.global.payment.api.request.RenderIntentionRequest;
import com.alibaba.global.payment.api.response.RenderIntentionResponse;

public interface PaymentBizService {

    RenderIntentionRequest buildRenderIntentionRequest(TradeOrderDTO module, ChosenIntentionDTO chosenIntentionDTO);

    ChosenIntentionDTO getWalletPay();

    Response<RenderIntentionResponse> confirmIntention(TradeOrderDTO module, ChosenIntentionDTO chosenIntentionDTO);

}
