package com.aliexpress.databank.service.impl;

import com.alibaba.ecommerce.module.Response;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.global.buy.api.model.source.PlatformType;
import com.alibaba.global.money.Money;
import com.alibaba.global.order.management.api.model.*;
import com.alibaba.global.order.management.constants.AttributeConstants;
import com.alibaba.global.payment.api.dto.env.CommonDTO;
import com.alibaba.global.payment.api.dto.env.HeaderDTO;
import com.alibaba.global.payment.api.dto.env.MobileDTO;
import com.alibaba.global.payment.api.dto.fx.FxRateDTO;
import com.alibaba.global.payment.api.dto.payment.ChosenIntentionDTO;
import com.alibaba.global.payment.api.dto.payment.MergeCashierPayRequest;
import com.alibaba.global.payment.api.facade.GlobalPaymentCashierServiceFacade;
import com.alibaba.global.payment.api.request.CheckoutRequest;
import com.alibaba.global.payment.api.request.RenderIntentionRequest;
import com.alibaba.global.payment.api.response.RenderIntentionResponse;
import com.alibaba.global.payment.api.utils.CashierRequestTokenUtils;
import com.alibaba.global.payment.api.vo.*;
import com.aliexpress.databank.service.PaymentBizService;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.money.CurrencyUnit;
import javax.money.MonetaryAmount;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Service
public class PaymentBizServiceImpl implements PaymentBizService {

    @Autowired
    private GlobalPaymentCashierServiceFacade globalPaymentCashierServiceFacade;


    @Override
    public RenderIntentionRequest buildRenderIntentionRequest(TradeOrderDTO tradeOrderDTO, ChosenIntentionDTO chosenIntentionDTO) {
        RenderIntentionRequest renderIntentionRequest = new RenderIntentionRequest();
        TradeOrderMsgDTO tradeOrderMsgDTO = getTradeOrderMsg();
        ArrayList<TradeOrderDTO> arrayList = new ArrayList<>();
        arrayList.add(tradeOrderDTO);
        renderIntentionRequest.setCashierToken(CashierRequestTokenUtils.getCashierRequestToken(getMergeCashierPayRequest(arrayList)));
        renderIntentionRequest.setPayer(buildBuyer(tradeOrderDTO.getBuyer()));
        renderIntentionRequest.setCheckoutRequests(buildCheckoutOrders(arrayList, tradeOrderMsgDTO));
        renderIntentionRequest.setChosenIntentionDTO(chosenIntentionDTO);
        renderIntentionRequest.setCommonDTO(buildCommonDTO(tradeOrderMsgDTO.getSourceDTO()));
        //待商量
        renderIntentionRequest.setPayCurrency(tradeOrderDTO.getActualFee().getCurrency().getCurrencyCode());
        renderIntentionRequest.setPlatform("PC");
        renderIntentionRequest.setRouteId(tradeOrderDTO.getBuyer().getBuyerId());
        //线上看到为null
        renderIntentionRequest.setSource(null);
        return renderIntentionRequest;
    }

    @Override
    public ChosenIntentionDTO getWalletPay() {
        ChosenIntentionDTO chosenIntentionDTO = new ChosenIntentionDTO();
        chosenIntentionDTO.setIntentionLineId("WALLET_PAYPAL");
        Map<String, String> params = new ConcurrentHashMap<>();
        params.put("selectListId", "WALLET_PAYPAL");
        chosenIntentionDTO.setParams(params);
        return chosenIntentionDTO;
    }

    @Override
    public Response<RenderIntentionResponse> confirmIntention(TradeOrderDTO module, ChosenIntentionDTO chosenIntentionDTO) {
        RenderIntentionRequest renderIntentionRequest = buildRenderIntentionRequest(module, chosenIntentionDTO);
        return globalPaymentCashierServiceFacade.confirmIntention(renderIntentionRequest);
    }

    private TradeOrderMsgDTO getTradeOrderMsg() {
        TradeOrderMsgDTO tradeOrderMsgDTO = new TradeOrderMsgDTO();
        SourceDTO sourceDTO = new SourceDTO();
        SourceSystemDTO sourceSystemDTO = new SourceSystemDTO();
        sourceSystemDTO.setPlatformType("PC");
        sourceDTO.setSourceSystem(sourceSystemDTO);
        tradeOrderMsgDTO.setSourceDTO(sourceDTO);
        return tradeOrderMsgDTO;
    }

    private UserInfo buildBuyer(BuyerInfoDTO buyer) {
        UserInfo buyerInfo = new UserInfo();
        if (buyer != null) {
            buyerInfo.setAliId(buyer.getBuyerId());
            buyerInfo.setPhoneNumber(buyer.getBuyerPhone());
            buyerInfo.setPhoneNoCountryCode(buyer.getBuyerPhonePrefixCode());
            buyerInfo.setEmail(buyer.getBuyerEmail());
            buyerInfo.setUserName(buyer.getBuyerFullName());
        }
        return buyerInfo;
    }

    private List<MergeCashierPayRequest> getMergeCashierPayRequest(List<TradeOrderDTO> tradeOrders) {
        if (CollectionUtils.isEmpty(tradeOrders)) {
            return null;
        } else {
            List<MergeCashierPayRequest> mergeCashierPayRequests = Lists.newArrayList();
            tradeOrders.forEach((tradeOrderDTO) -> {
                MergeCashierPayRequest mergeCashierPayRequest = new MergeCashierPayRequest();
                mergeCashierPayRequest.setOrderId(tradeOrderDTO.getPayOrderDTO().getPaymentCheckoutId());
                mergeCashierPayRequests.add(mergeCashierPayRequest);
            });
            return mergeCashierPayRequests;
        }
    }

    private List<CheckoutRequest> buildCheckoutOrders(List<TradeOrderDTO> tradeOrders, TradeOrderMsgDTO tradeOrderMsgDTO) {
        List<CheckoutRequest> result = Lists.newArrayList();
        tradeOrders.forEach(
                order -> {
                    CheckoutRequest checkoutRequest = buildCheckoutOrder(order, tradeOrderMsgDTO);
                    result.add(checkoutRequest);
                }
        );
        return result;
    }


    private CheckoutRequest buildCheckoutOrder(TradeOrderDTO order, TradeOrderMsgDTO tradeOrderMsgDTO) {
        CheckoutRequest checkoutRequest = new CheckoutRequest();
        checkoutRequest.setBizOrderNo(String.valueOf(order.getTradeOrderId()));
        MonetaryAmount orderAmount = order.getActualFee();
        CurrencyUnit currencyUnit = orderAmount.getCurrency();
        checkoutRequest.setOrderAmount(Money.of(orderAmount));
        checkoutRequest.setPayerId(order.getBuyer().getBuyerId());
        checkoutRequest.setPayerName(order.getBuyer().getBuyerFullName());
        checkoutRequest.setPayeeId(order.getOrderLines().get(0).getSeller().getSellerId());
        checkoutRequest.setPayeeName(order.getOrderLines().get(0).getSeller().getSellerFullName());
        TradeOrderInfo tradeOrderInfo = new TradeOrderInfo();
        tradeOrderInfo.setOrderId(String.valueOf(order.getTradeOrderId()));
        tradeOrderInfo.setTotalAmount(Money.of(orderAmount));
        tradeOrderInfo.setOrderEnvInfo(buildTradeEnvInfo(tradeOrderMsgDTO.getSourceDTO()));

        UserInfo buyer = new UserInfo();
        buyer.setAliId(order.getBuyer().getBuyerId());
        buyer.setPhoneNumber(order.getBuyer().getBuyerPhonePrefixCode());
        buyer.setEmail(order.getBuyer().getBuyerEmail());
        buyer.setUserName(order.getBuyer().getBuyerFullName());
        tradeOrderInfo.setBuyer(buyer);

        UserInfo seller = new UserInfo();
        seller.setAliId(order.getOrderLines().get(0).getSeller().getSellerId());
        seller.setPhoneNumber(order.getOrderLines().get(0).getSeller().getSellerPhone());
        seller.setEmail(order.getOrderLines().get(0).getSeller().getSellerEmail());
        seller.setUserName(order.getOrderLines().get(0).getSeller().getSellerFullName());
        tradeOrderInfo.setSeller(seller);
        //checkoutRequest.setPayTermRequests(buildStepInfo(order));
        tradeOrderInfo.setSubTradeOrderInfos(buildCheckoutDetails(order.getOrderLines(), order, tradeOrderInfo));
        MonetaryAmount shippingFee = order.getShippingActualFee();
        if (shippingFee == null) {
            shippingFee = Money.zero(currencyUnit);
        }
        tradeOrderInfo.getLogisticInfo().setLogisticAmount(
                Money.of(shippingFee));

        Map<String, String> extMap = Maps.newHashMap();

        extMap.put("ACTIVITY_TIME_OUT", String.valueOf(2524579200000L));
        tradeOrderInfo.setExtAttrMap(extMap);
        checkoutRequest.setTradeOrderInfo(tradeOrderInfo);
        checkoutRequest.setFxRate(buildFxRate(order));
        if (null != order.getPayOrderDTO().getPaymentPromotionFee()) {
            checkoutRequest.setOrderPromotionFee(Money.of(order.getPayOrderDTO().getPaymentPromotionFee()));
        }
        checkoutRequest.setOrderAmount(Money.of(order.getOrderAmount()));
        MonetaryAmount payFee = order.getActualFee();
        checkoutRequest.setOrderPayFee(Money.of(payFee));
        return checkoutRequest;
    }

    protected static FxRateDTO buildFxRate(TradeOrderDTO order) {
        FxRateDTO rateDTO = new FxRateDTO();
        rateDTO.setId(order.getOrderLines().get(0).getFeatures().getFeature(AttributeConstants.ATTR_EXCHANGE_RATE_ID));
        rateDTO.setOutId(order.getOrderLines().get(0).getFeatures().getFeature(AttributeConstants.ATTR_EXCHANGE_RATE_OUT_ID));
        ExchangeInfoDTO exchangeInfo = order.getOrderLines().get(0).getExchangeInfo();
        rateDTO.setFxRate(exchangeInfo.getExchangeRate());
        rateDTO.setBaseCcy(exchangeInfo.getBaseCurrency());
        rateDTO.setQuoteCcy(exchangeInfo.getQuoteCurrency());
        return rateDTO;
    }

    private static List<SubTradeOrderInfo> buildCheckoutDetails(List<TradeOrderLineDTO> orderLines, TradeOrderDTO order,
                                                                TradeOrderInfo tradeOrderInfo) {
        List<SubTradeOrderInfo> result = Lists.newArrayList();
        for (TradeOrderLineDTO orderLine : orderLines) {
            SubTradeOrderInfo checkoutOrderLine = new SubTradeOrderInfo();
            checkoutOrderLine.setBizCode(orderLine.getBizCode());
            ArrayList<String> arrayList = new ArrayList<>();
            arrayList.add("com.alibaba.business.product.cod.CODProduct");
            checkoutOrderLine.setProductCodeList(arrayList);
            checkoutOrderLine.setMainOrderNo(String.valueOf(order.getTradeOrderId()));
            //待确定
            checkoutOrderLine.setSubOrderNo(String.valueOf(orderLine.getTradeOrderLineId()));
            MonetaryAmount subOrderAmount = orderLine.getActualFee();
            checkoutOrderLine.setAmount(Money.of(subOrderAmount));
            ItemInfo itemInfo = new ItemInfo();
            itemInfo.setItemId(String.valueOf(orderLine.getProduct().getItemId()));
            itemInfo.setItemName(orderLine.getProduct().getItemTitle());
            String categoryId = orderLine.getProduct().getCategoryId();
            if (categoryId != null) {
                itemInfo.setCategoryId(categoryId);
            }
            itemInfo.setSku(String.valueOf(orderLine.getProduct().getSku().getSkuId()));
            ProductDTO product = orderLine.getProduct();

            MonetaryAmount itemUnitPrice = orderLine.getUnitFee();
            itemInfo.setUnitAmount(
                    Money.of(itemUnitPrice));
            itemInfo.setNum(orderLine.getQuantity().intValue());

            checkoutOrderLine.setItemInfos(Lists.newArrayList(itemInfo));
            checkoutOrderLine.setSeller(tradeOrderInfo.getSeller());

            LogisticsInfo logisticInfo = new LogisticsInfo();
            //待确定
            TransportMethodDTO chosenTransportMethod = orderLine.getLogisticsOrderDTO().getTransportMethodDTO();
            if (chosenTransportMethod != null) {
                logisticInfo.setLogisticType(chosenTransportMethod.getDeliveryType());
            }
            ReceiveUserInfoDTO receiver = order.getDeliveryAddress().getReceiver();
            if (receiver != null) {
                logisticInfo.setAddress1(order.getDeliveryAddress().getDetailAddress());
                logisticInfo.setAddress2(order.getDeliveryAddress().getAddress2());
                logisticInfo.setCountry(order.getDeliveryAddress().getCountryCode());
                logisticInfo.setState(order.getDeliveryAddress().getState());
                logisticInfo.setCity(order.getDeliveryAddress().getCity());
                logisticInfo.setAreaName(order.getDeliveryAddress().getThirdLevelAddressName());
                logisticInfo.setZipCode(order.getDeliveryAddress().getPostCode());
                logisticInfo.setPhoneNo(receiver.getPhone());
                logisticInfo.setPhoneArea(receiver.getPhoneArea());
                logisticInfo.setPhoneCountryCode(receiver.getPhoneCountry());
                logisticInfo.setMobileNo(receiver.getMobileNo());
                logisticInfo.setMobileCountryNo(receiver.getPhoneCountry());
                logisticInfo.setLastName(receiver.getFullName());
                MonetaryAmount shippingFee = orderLine.getShippingActualFee();
                logisticInfo.setLogisticAmount(Money.of(shippingFee));
            }
            checkoutOrderLine.setLogisticInfo(logisticInfo);
            tradeOrderInfo.setLogisticInfo(logisticInfo);
            result.add(checkoutOrderLine);
        }
        return result;
    }

    private String buildTradeEnvInfo(SourceDTO source) {
        JSONObject tradeEnvInfo = new JSONObject();
        if (source.getSourceSystem() != null) {
            tradeEnvInfo.put("os", source.getSourceSystem().getOs());
            JSONObject extendInfo = new JSONObject();
            extendInfo.put("ttid", source.getSourceSystem().getTtid());
            tradeEnvInfo.put("extendInfo", extendInfo);
        }

        if (source.getSourceDevice() != null) {
            tradeEnvInfo.put("device", source.getSourceDevice().getId());
        }
        return tradeEnvInfo.toJSONString();
    }


    private CommonDTO buildCommonDTO(SourceDTO sourceDTO) {
        CommonDTO commonDTO = new CommonDTO();
        if (null != sourceDTO) {
            commonDTO.setHeaderDTO(buildHeader(sourceDTO));
            commonDTO.setMobile(buildMobile(sourceDTO));
            commonDTO.setSource(buildSource(sourceDTO));
        }
        return commonDTO;
    }

    private MobileDTO buildMobile(SourceDTO sourceDTO) {
        MobileDTO mobileDTO = new MobileDTO();
        if ("WIRELESS".equals(sourceDTO.getSourceSystem().getPlatformType())) {
            mobileDTO.setTtid(sourceDTO.getSourceSystem().getTtid());
            mobileDTO.setAppKey("13022");
            mobileDTO.setAppVersion("8.8.0");
        }

        return null;
    }

    private com.alibaba.global.payment.api.dto.env.SourceDTO buildSource(SourceDTO sourceDTO) {
        com.alibaba.global.payment.api.dto.env.SourceDTO sourceDTO1 = new com.alibaba.global.payment.api.dto.env.SourceDTO();
        if (null != sourceDTO.getSourceSystem().getPlatformType()) {

            if ("PC".equals(sourceDTO.getSourceSystem().getPlatformType())) {
                sourceDTO1.setPlatformType(PlatformType.PC.getPlatformType());

            }
            if ("WIRELESS".equals(sourceDTO.getSourceSystem().getPlatformType())) {
                sourceDTO1.setPlatformType(
                        com.alibaba.global.buy.api.model.source.PlatformType.WIRELESS.getPlatformType());
            }
            if ("H5".equals(sourceDTO.getSourceSystem().getPlatformType())) {
                sourceDTO1.setPlatformType(com.alibaba.global.buy.api.model.source.PlatformType.H5.getPlatformType());
            }

        }
        return sourceDTO1;
    }

    private HeaderDTO buildHeader(SourceDTO sourceDTO) {
        HeaderDTO headerDTO = new HeaderDTO();
        headerDTO.setSid(sourceDTO.getSourceNetwork().getSessionId());
        headerDTO.setCid(sourceDTO.getSourceNetwork().getCookieId());
        headerDTO.setUmidToken("XnNL6ZNLOloGtzVxvWbClQkFA84Mrqlc");
        headerDTO.setIp(sourceDTO.getSourceNetwork().getClientIp());
        return headerDTO;
    }

}
