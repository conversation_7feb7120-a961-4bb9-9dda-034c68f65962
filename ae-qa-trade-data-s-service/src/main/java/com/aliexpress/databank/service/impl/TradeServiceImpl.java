package com.aliexpress.databank.service.impl;

import com.alibaba.ae.extend.buy.AEMultiterminalCheckoutFacade;
import com.alibaba.ecommerce.error.ErrorCode;
import com.alibaba.ecommerce.module.Response;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.global.buy.api.facade.CheckoutMultiterminalFacade;
import com.alibaba.global.buy.api.facade.RegionCheckoutFacade;
import com.alibaba.global.buy.api.facade.wireless.WirelessCheckoutFacade;
import com.alibaba.global.buy.api.model.OrderLineDTO;
import com.alibaba.global.buy.api.request.CreateOrderRequest;
import com.alibaba.global.buy.api.request.RenderOrderRequest;
import com.alibaba.global.buy.api.request.param.CreateQueryParams;
import com.alibaba.global.buy.api.request.param.RenderProtocol;
import com.alibaba.global.buy.api.request.param.RenderQueryParams;
import com.alibaba.global.buy.api.response.CreateOrderResult;
import com.alibaba.global.buy.api.response.natives.Result;
import com.alibaba.global.landlord.LandlordContext;
import com.alibaba.global.order.management.api.facade.OrderQueryForBuyerFacade;
import com.alibaba.global.order.management.api.model.TradeOrderDTO;
import com.alibaba.global.order.management.api.model.TradeOrderLineDTO;
import com.alibaba.ultron.model.protocol.ISerializedProtocol;
import com.alibaba.ultron.model.protocol.Protocol;
import com.aliexpress.databank.constant.Constant;
import com.aliexpress.databank.service.TradeService;
import com.aliexpress.databank.utils.AstoreSerializedProtocol;
import com.aliexpress.databank.utils.ConvertParam;
import com.taobao.eagleeye.EagleEye;
import com.taobao.wsgfstjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Slf4j
@Service
public class TradeServiceImpl implements TradeService {

    @Autowired
    private CheckoutMultiterminalFacade checkoutMultiterminalFacade;

    @Autowired
    private AEMultiterminalCheckoutFacade aeMultiterminalCheckoutFacade;

    @Autowired
    private WirelessCheckoutFacade wirelessCheckoutFacade;

    @Autowired
    RegionCheckoutFacade regionCheckoutFacade;

    @Autowired
    private OrderQueryForBuyerFacade orderQueryForBuyerFacade;

    @Override
    public Response<CreateOrderResult> getPlaceOrderResponse(Long buyerId, Long itemId, String countryCode,
                                                             String currencyCode, String deliveryOption) throws Exception {
        RenderOrderRequest renderOrderRequest = ConvertParam.getRenderOrderRequest(buyerId, Arrays.asList(itemId), countryCode, currencyCode);
        LandlordContext.setTenantSpec("AE_GLOBAL", -1);
        Response renderOrderResponse = checkoutMultiterminalFacade.renderOrder(renderOrderRequest);
        String deliveryOptionCode = "";
        if (renderOrderResponse != null && renderOrderResponse.isSuccess()) {
            Result result = ((Result) renderOrderResponse.getModule());
            if (StringUtils.isNotBlank(deliveryOption) || !deliveryOption.equals("随机")) {
                Set<String> shippingCodes = ConvertParam.getDeliveryOptionFromRenderRes(result);
                Optional<String> deliveryOptionCodeAim = matchDeliveryOption(shippingCodes, deliveryOption);
                if (!deliveryOptionCodeAim.isPresent()) {
                    return Response.failed(ErrorCode.frontEndCode("未找到对应的物流方式，请检查物流模版",
                            "只支持" + JSONObject.toJSONString(shippingCodes)));
                }
            }
            CreateOrderRequest createOrderRequest = ConvertParam.toCreateOrderRequest(result, buyerId, countryCode, "USD", deliveryOptionCode);
            Response<CreateOrderResult> createOrderResultResponse = checkoutMultiterminalFacade.createOrder(createOrderRequest);
            return createOrderResultResponse;
        }
        return null;
    }

    @Override
    public com.taobao.mtop.common.Result<CreateOrderResult> getPlaceOrderResponse(Long buyerId, List<String> itemIds, String skuId, Integer quantity, String countryCode, String currencyCode, String shippingMethod, String shareGroup, String shareGroupCode) throws Exception {
        RenderQueryParams renderOrderRequest = ConvertParam.getRenderQueryParams(buyerId, itemIds, skuId, quantity, shippingMethod, countryCode, currencyCode, shareGroup, shareGroupCode);
        LandlordContext.setTenantSpec("AE_GLOBAL", -1);
        com.taobao.mtop.common.Result<ISerializedProtocol> renderOrderResponse = (com.taobao.mtop.common.Result<ISerializedProtocol>) regionCheckoutFacade.renderOrder(renderOrderRequest);
        log.info("renderOrder traceId:" + EagleEye.getTraceId());
        if (renderOrderResponse != null && renderOrderResponse.isSuccess()) {
            HashMap<String, Object> renderOrderResponseModel = (HashMap<String, Object>) renderOrderResponse.getModel();
            CreateQueryParams createQueryParams = ConvertParam.getCreateQueryParams(renderOrderResponseModel, buyerId);
            com.taobao.mtop.common.Result<CreateOrderResult> createOrderResultResponse = (com.taobao.mtop.common.Result<CreateOrderResult>) regionCheckoutFacade.createOrder(createQueryParams);
            log.info("createOrder traceId:" + EagleEye.getTraceId());
            return createOrderResultResponse;
        }
        else {
            com.taobao.mtop.common.Result<CreateOrderResult> createOrderResultResponse = new com.taobao.mtop.common.Result<>();
            createOrderResultResponse.setSuccess(false);
            createOrderResultResponse.setMsgCode(renderOrderResponse.getMsgCode());
            createOrderResultResponse.setMsgInfo(renderOrderResponse.getMsgInfo());
            return createOrderResultResponse;
        }
    }

    @Override
    public Response<CreateOrderResult> getPlaceOrderResponse(TradeOrderDTO tradeOrderDTO) throws Exception {
        RenderOrderRequest renderOrderRequest = getRenderOrderRequest(tradeOrderDTO);
        LandlordContext.setTenantSpec("AE_GLOBAL", -1);
        Response renderOrderResponse = checkoutMultiterminalFacade.renderOrder(renderOrderRequest);
        if (renderOrderResponse != null && renderOrderResponse.isSuccess()) {
            Result result = ((Result) renderOrderResponse.getModule());
            String currency = tradeOrderDTO.getOrderLines().get(0).getExchangeInfo().getQuoteCurrency();
            CreateOrderRequest createOrderRequest = ConvertParam.toCreateOrderRequest(result, tradeOrderDTO.getBuyer().getBuyerId(), tradeOrderDTO.getDeliveryAddress().getCountryCode(), currency, "");
            Response<CreateOrderResult> createOrderResultResponse = checkoutMultiterminalFacade.createOrder(createOrderRequest);
            return createOrderResultResponse;
        }
        return null;
    }

    private Optional<String> matchDeliveryOption(Set<String> deliveryOptions, String deliveryOption) {
        return Constant.ORDER_DELIVERY_OPTION.get(deliveryOption).stream().filter(deliveryOptions::contains).findFirst();
    }

    @Override
    public Response<TradeOrderDTO> queryTradeOrderById(Long buyerId, Long orderId) {
        return orderQueryForBuyerFacade.queryTradeOrderById(buyerId, orderId);
    }


    @Override
    public Response<TradeOrderDTO> queryTradeOrderById(Long orderId) {
        return orderQueryForBuyerFacade.queryTradeOrderById(orderId);
    }


    private RenderOrderRequest getRenderOrderRequest(TradeOrderDTO tradeOrderDTO) throws Exception {
        RenderOrderRequest renderOrderRequest = new RenderOrderRequest();
        List<OrderLineDTO> orderLineDTOS = new ArrayList<>();
        String currency = tradeOrderDTO.getOrderLines().get(0).getExchangeInfo().getQuoteCurrency();

        for (TradeOrderLineDTO oriOrderLineDTO : tradeOrderDTO.getOrderLines()) {
            OrderLineDTO orderLineDTO = new OrderLineDTO();
            orderLineDTO.setItemId(Long.valueOf(oriOrderLineDTO.getProduct().getItemId()));
            orderLineDTO.setQuantity(oriOrderLineDTO.getQuantity());
            orderLineDTO.setSkuId(Long.valueOf(oriOrderLineDTO.getProduct().getSku().getSkuId()));
            orderLineDTOS.add(orderLineDTO);

        }

        renderOrderRequest.setItems(orderLineDTOS);
        renderOrderRequest.setRenderProtocol(RenderProtocol.NATIVE);
        renderOrderRequest.setCommonDTO(ConvertParam.getCommonDTO(tradeOrderDTO.getBuyer().getBuyerId()));
        renderOrderRequest.setExtraParams(ConvertParam.getExtraParams(currency, tradeOrderDTO.getDeliveryAddress().getCountryCode()));
        return renderOrderRequest;
    }

}
