package com.aliexpress.databank.service.impl;

import com.alibaba.ecommerce.module.Response;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.global.buy.api.facade.CheckoutMultiterminalFacade;
import com.alibaba.global.buy.api.request.CreateOrderRequest;
import com.alibaba.global.buy.api.request.RenderOrderRequest;
import com.alibaba.global.buy.api.response.CreateOrderResult;
import com.alibaba.global.buy.api.response.natives.Result;
import com.alibaba.global.landlord.LandlordContext;
import com.alibaba.global.order.management.api.facade.OrderQueryForBuyerFacade;
import com.alibaba.global.order.management.api.model.TradeOrderDTO;
import com.alibaba.global.order.management.api.model.TradeOrderLineDTO;
import com.aliexpress.databank.constant.Constant;
import com.aliexpress.databank.constant.ErrorCode;
import com.aliexpress.databank.constant.OrderScenario;
import com.aliexpress.databank.dataobject.OrderScenarioModule;
import com.aliexpress.databank.dataobject.PlaceOrderValidation;
import com.aliexpress.databank.dataobject.ReverseOrder;
import com.aliexpress.databank.mapper.ReverseOrderMapper;
import com.aliexpress.databank.service.PlaceOrderStrategy;
import com.aliexpress.databank.utils.ConvertParam;
import com.aliexpress.databank.utils.HsfUtil;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Service
public class BasePlaceOrderStrategy implements PlaceOrderStrategy {

    @Autowired
    private ReverseOrderMapper reverseOrderMapper;

    @Autowired
    private OrderQueryForBuyerFacade orderQueryForBuyerFacade;

    @Autowired
    private CheckoutMultiterminalFacade checkoutMultiterminalFacade;


    @Override
    public OrderScenario getScenario() {
        return OrderScenario.DEFAULT;
    }

    @Override
    public int isGenerate(Long buyerId, String scenario, int index) {
        int count = reverseOrderMapper.findOrderCountByBuyerIdAndScenario(buyerId, scenario, index);
        Optional<OrderScenarioModule> orderScenarioModule = Constant.ORDER_SCENARIOS.get(scenario).stream().filter(order -> order.getIndex() == index).findFirst();
        return orderScenarioModule.map(scenarioModule -> scenarioModule.getCount() - count).orElse(0);
    }

    @Override
    public CreateOrderRequest getCreateOrderRequest(Long buyerId, List<Long> itemIds, String currencyCode,
                                                    String countryCode, String deliveryOption) throws Exception {
        RenderOrderRequest renderOrderRequest = ConvertParam.getRenderOrderRequest(buyerId, itemIds, countryCode, currencyCode);
        LandlordContext.setTenantSpec("AE_GLOBAL", -1);
        Response renderOrderResponse = checkoutMultiterminalFacade.renderOrder(renderOrderRequest);
        if (renderOrderResponse != null && renderOrderResponse.isSuccess()) {
            Result result = ((Result) renderOrderResponse.getModule());
            return ConvertParam.toCreateOrderRequest(result, buyerId, countryCode, currencyCode, deliveryOption);
        }
        return null;
    }

    @Override
    public List<String> placeOrder(CreateOrderRequest createOrderRequest) {
        LandlordContext.setTenantSpec("AE_GLOBAL", -1);
        Response<CreateOrderResult> response = checkoutMultiterminalFacade.createOrder(createOrderRequest);
        if (response.isSuccess() && response.getModule() != null && CollectionUtils.isNotEmpty(response.getModule().getOrderIds())) {
            return response.getModule().getOrderIds();
        }
        return Lists.newArrayList();
    }

    @Override
    public PlaceOrderValidation validate(Long tradeOrderId, String scenario) {
        return PlaceOrderValidation.success(tradeOrderId, scenario);
    }

    @Override
    public PlaceOrderValidation validate(Long tradeOrderId, Long promiseTemplateId, String features) {
        PlaceOrderValidation placeOrderValidation = new PlaceOrderValidation();
        placeOrderValidation.setTradeOrderId(tradeOrderId);
        placeOrderValidation.setMatch(false);
        Response<TradeOrderDTO> tradeOrder = orderQueryForBuyerFacade.queryTradeOrderById(tradeOrderId);
        if (tradeOrder.isSuccess() && tradeOrder.getModule() != null) {
            tradeOrder.getModule().getOrderLines().forEach(orderLine -> {
                String promiseTemplates = orderLine.getFeatures().getFeature("promiseTemplate");
                JSONArray promiseTemplateArray = JSONArray.parseArray(promiseTemplates);
                Map<Long, Map<String, Boolean>> promiseTemplateIds = getPromiseTemplateMap(promiseTemplateArray);
                if (!promiseTemplateIds.containsKey(promiseTemplateId)) {
                    placeOrderValidation.addErrorInfo(tradeOrderId, orderLine.getTradeOrderLineId(), ErrorCode.PROMISE_ID_NOT_MATCH);
                }
                if (StringUtils.isNotBlank(features)) {
                    if (!promiseTemplateIds.get(promiseTemplateId).containsKey(features)
                            && !promiseTemplateIds.get(promiseTemplateId).get(features)) {
                        placeOrderValidation.addErrorInfo(tradeOrderId, orderLine.getTradeOrderLineId(), ErrorCode.PROMISE_ID_FEATURES_NOT_MATCH);
                    }
                }
            });
        } else {
            placeOrderValidation.addErrorInfo(tradeOrderId, ErrorCode.PROMISE_ID_NOT_MATCH);
        }
        if (placeOrderValidation.getErrors().isEmpty()) {
            placeOrderValidation.setMatch(true);
        }
        return placeOrderValidation;
    }

    @Override
    public ReverseOrder placeOrderResConvert2ReverseOrder(Long tradeOrderId, String scenario, int scenarioIndex) {
        ReverseOrder reverseOrder = new ReverseOrder();
        Response<TradeOrderDTO> tradeOrderRes = orderQueryForBuyerFacade.queryTradeOrderById(tradeOrderId);
        if (tradeOrderRes.isSuccess() && tradeOrderRes.getModule() != null) {
            try {
                TradeOrderDTO tradeOrderDTO = tradeOrderRes.getModule();
                List<TradeOrderLineDTO> orderLines = tradeOrderDTO.getOrderLines();
                String promiseTemplates = getPromiseTemplate(orderLines);
                Date now = new Date();
                reverseOrder = ReverseOrder.builder()
                        .orderId(tradeOrderDTO.getTradeOrderId())
                        .buyerId(tradeOrderDTO.getBuyer().getBuyerId())
                        .sellerId(orderLines.get(0).getSeller().getSellerId())
                        .buyerLocation("default - no use")
                        .used(false)
                        .refund(false)
                        .shipped(false)
                        .promiseTemplate(promiseTemplates)
                        .shippingMethod(getShippingMethod(orderLines.get(0)))
                        .reverseType(getReverseType(promiseTemplates))
                        .multiOrderLines(orderLines.size() > 1)
                        .overDisputeProtection(false)
                        .confirmed(false)
                        .scenario(scenario)
                        .scenarioIndex(String.valueOf(scenarioIndex))
                        .gmtCreated(now)
                        .gmtModified(now)
                        .build();
                Map<String, String> buyerEnv = getLocation(reverseOrder.getBuyerId());
                String buyerPreEnv = buyerEnv.get(Constant.PRE_ENV);
                String buyerOnlineEnv = buyerEnv.get(Constant.ONLINE_ENV);
                Map<String, String> sellerEnv = getLocation(reverseOrder.getSellerId());
                String sellerPreEnv = sellerEnv.get(Constant.PRE_ENV);
                String sellerOnlineEnv = sellerEnv.get(Constant.ONLINE_ENV);
                reverseOrder.setBuyerPreEnv(buyerPreEnv);
                reverseOrder.setBuyerOnlineEnv(buyerOnlineEnv);
                reverseOrder.setSellerPreEnv(sellerPreEnv);
                reverseOrder.setSellerOnlineEnv(sellerOnlineEnv);
                reverseOrder.setSellerLocation(getSellerLocation(reverseOrder.getSellerId()));
            } catch (Exception e) {
                e.printStackTrace();
                log.error("Fail to convert to the ReverseOrder. OrderId: " + tradeOrderId, e);
                return null;
            }
        }
        return reverseOrder;
    }

    @Override
    public List<Long> getOrderIdsByScenarioIndex(String scenario, String scenarioIndex, int size) {
        try {
            List<Long> orderIds = reverseOrderMapper.getOrderIdsByScenarioIndex(scenario, scenarioIndex, size);
            orderIds.forEach(orderId -> reverseOrderMapper.updateUsedByOrderId(orderId));
            return orderIds;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return Lists.newArrayList();
    }

    @Override
    public List<Long> getOrderIdsByScenario(String scenario, int size) {
        List<Long> orderIds = reverseOrderMapper.getOrderIdsByScenario(scenario, size);
        orderIds.forEach(orderId -> reverseOrderMapper.updateUsedByOrderId(orderId));
        return orderIds;
    }

    private String getSellerLocation(Long sellerId) throws Exception {
        JSONObject result = HsfUtil.getSellerTagBySellerId(String.valueOf(sellerId), "oversea_seller_location");
        if (result.getBoolean("success") && result.getJSONObject("module") != null) {
            return result.getJSONObject("module").getJSONObject("sellerResultMap")
                    .getJSONObject(sellerId.toString()).getString("country");
        }
        return "CN";
    }

    private Map<String, String> getLocation(Long userId) throws Exception {
        Map<String, String> env = new HashMap<>();
        JSONObject jsonObject = HsfUtil.getRouting(userId);
        String preEnv = jsonObject.getJSONObject("unitRegionNo").getString("csEnv");
        String onlineEnv = jsonObject.getJSONObject("unitRegionNo").getString("unitEnv");
        env.put(Constant.PRE_ENV, preEnv);
        env.put(Constant.ONLINE_ENV, onlineEnv);
        return env;
    }

    private String getReverseType(String promiseTemplateStr) {
        StringBuilder sb = new StringBuilder("普通协商");
        List<String> promiseTemplates = Lists.newArrayList(promiseTemplateStr.split(","));
        for (String promiseTemplate : promiseTemplates) {
            if ("11".equals(promiseTemplate)) {
                sb.append(",售后宝");
            } else if ("22".equals(promiseTemplate)) {
                sb.append(",官方仓退货");
            } else if ("17".equals(promiseTemplate)) {
                sb.append(",无忧退");
            }
        }
        return sb.toString();
    }

    private String getPromiseTemplate(List<TradeOrderLineDTO> orderLines) {
        Set<String> promiseTemplateIds = new HashSet<>();
        try {
            orderLines.forEach(orderLine -> {
                String promiseTemplates = orderLine.getFeatures().getFeature("promiseTemplate");
                JSONArray jsonArray = JSONArray.parseArray(promiseTemplates);
                promiseTemplateIds.add(jsonArray.getLong(0).toString());
                for (int i = 1; i < jsonArray.size(); i++) {
                    promiseTemplateIds.add(jsonArray.getJSONObject(i).getLong("id").toString());
                }
            });
            return JSONObject.toJSONString(promiseTemplateIds)
                    .replaceAll("\\[", "")
                    .replaceAll("\\]", "")
                    .replaceAll("\\\"", "");
        } catch (Exception e) {
            e.printStackTrace();
            return "2";
        }
    }

    private String getShippingMethod(TradeOrderLineDTO tradeOrderLineDTO) {
        Map<String, String> featureMap = tradeOrderLineDTO.getFeatures().getFeatureMap();
        JSONObject tml = JSONObject.parseObject(featureMap.get("tml"));
        return tml.getString("deliveryType");
    }

    private Map<Long, Map<String, Boolean>> getPromiseTemplateMap(JSONArray promiseTemplateArray) {
        Map<Long, Map<String, Boolean>> map = new ConcurrentHashMap<>();
        for (int i = 0; i < promiseTemplateArray.size(); i++) {
            JSONObject jsonObject = promiseTemplateArray.getJSONObject(i);
            Long id = jsonObject.getLong("id");
            Map<String, Boolean> valueMaps = new Gson().fromJson(jsonObject.getString("valueMaps"),
                    new TypeToken<Map<String, Object>>() {
                    }.getType());
            map.put(id, valueMaps);
        }
        return map;
    }

}
