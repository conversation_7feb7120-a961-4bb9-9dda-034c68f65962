package com.aliexpress.databank.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.rocketmq.client.exception.MQBrokerException;
import com.alibaba.rocketmq.client.exception.MQClientException;
import com.alibaba.rocketmq.client.producer.SendResult;
import com.alibaba.rocketmq.remoting.exception.RemotingException;
import com.aliexpress.databank.config.MqConfig;
import com.aliexpress.databank.constant.Constant;
import com.aliexpress.databank.service.RefundRecordService;
import com.google.common.collect.Maps;
import groovy.util.logging.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

@Slf4j
@Service
public class RefundRecordServiceImpl implements RefundRecordService {

    @Autowired
    private MqConfig mqConfig;

    @Override
    public String sendSecondRefundMsg(Long buyerId, Long tradeOrderId, Long tradeOrderLineId, String outUniqueSeq) throws InterruptedException, RemotingException, MQClientException, MQBrokerException {
        JSONObject msg = new JSONObject();
        Map<String, String> attributes = Maps.newConcurrentMap();
        attributes.put("MAIN_TRADE_ORDER_NO", String.valueOf(tradeOrderId));
        attributes.put("TRADE_ORDER_LINE_NO", String.valueOf(tradeOrderLineId));
        attributes.put("channelErrorCode", "REFUND_TIME_EXCEED_LIMIT");
        msg.put("attributes", attributes);

        msg.put("mainTradeOrderNo", String.valueOf(tradeOrderId));
        msg.put("outUniqueSeq", outUniqueSeq);
        msg.put("payerId", buyerId);
        msg.put("refundOrderNo", "12990591208240131657500013244");
        msg.put("subTradeOrderNos", String.valueOf(tradeOrderLineId));

        SendResult result = mqConfig.sendMessage(Constant.PAYMENT_TOPIC, Constant.REFUND_FAIL_TAG, String.valueOf(tradeOrderLineId), msg.toJSONString());
        return JSONObject.toJSONString(result);
    }

    @Override
    public String sendAsyncSecondRefundMsg(Long buyerId, Long tradeOrderId, Long tradeOrderLineId, String outUniqueSeq) throws InterruptedException, RemotingException, MQClientException, MQBrokerException {
        JSONObject msg = new JSONObject();
        JSONArray refundStatements = new JSONArray();
        JSONObject refundStatement = getRefundPendingStatement();
        refundStatements.add(refundStatement);
        msg.put("outUniqueSeq", outUniqueSeq);
        msg.put("payerId", buyerId);
        msg.put("refundOrderNo", "12990591208240131657500013244");
        msg.put("tradeOrderId", String.valueOf(tradeOrderId));
        msg.put("tradeOrderLineId", String.valueOf(tradeOrderLineId));
        msg.put("refundStage", "pending");
        msg.put("refundChannel", "creditcard");
        msg.put("refundStatements", refundStatements);

        SendResult result = mqConfig.sendMessage(Constant.PAYMENT_TOPIC, Constant.REFUND_PROCESS_TAG, String.valueOf(tradeOrderLineId), msg.toJSONString());
        return JSONObject.toJSONString(result);
    }

    @Override
    public String sendInitOrSettleRecordMsg(Long buyerId, String refundChannel, Long tradeOrderId, Long tradeOrderLineId,
                                            String outUniqueSeq, String refundStage, String type) throws InterruptedException, RemotingException, MQClientException, MQBrokerException {
        JSONObject msg = new JSONObject();
        msg.put("checkoutSource", "global.trade");
        msg.put("externalRefNo", "1000100110624032000026405791002");
        msg.put("notifyId", "1000100110624032000026405791002-" + refundStage);
        msg.put("outUniqueSeq", outUniqueSeq);
        msg.put("payInstructionNo", "21990501203240319203500082202");
        msg.put("payerId", buyerId);
        msg.put("refundChannel", refundChannel);
        msg.put("refundInstructionNo", "21990501209240320985601762202");
        msg.put("refundOrderNo", "21990501208240320017601772202");
        msg.put("refundStage", refundStage);
        msg.put("refundStageTime", "2024-03-20T17:05:18+08:00");
        msg.put("tradeOrderId", String.valueOf(tradeOrderId));
        msg.put("tradeOrderLineId", String.valueOf(tradeOrderLineId));

        JSONArray refundStatements = new JSONArray();
        JSONObject refundStatement = new JSONObject();
        refundStatement.put("refundStatementId", "1000100110624032000026405791002");
        refundStatement.put("refundStatementStage", refundStage);

        JSONArray refundAssetDetails = new JSONArray();
        JSONObject refundAssetDetail = new JSONObject();
        JSONObject refundAmount = new JSONObject();
        refundAmount.put("currency", "USD");
        refundAmount.put("value", 1);
        refundAssetDetail.put("refundAmount", refundAmount);
        refundAssetDetail.put("refundAssetStage", refundStage);
        refundAssetDetail.put("refundAssetStageTime", "2024-03-20T17:05:18+08:00");

        JSONObject refundMethod = new JSONObject();
        JSONObject paymentMethodType = new JSONObject();
        paymentMethodType.put("customerId", String.valueOf(buyerId));
        paymentMethodType.put("paymentMethodType", "MIXEDCARD");
        refundMethod.put("paymentMethodType", paymentMethodType);
        refundAssetDetail.put("refundMethod", refundMethod);

        refundAssetDetails.add(refundAssetDetail);
        refundStatement.put("refundAssetDetails", refundAssetDetails);

        refundStatements.add(refundStatement);

        msg.put("refundStatements", refundStatements);

        if (("bonus2origin").equals(type)){
            JSONObject refundAttribute = new JSONObject();
            refundAttribute.put("assetTransferOrderNo", "210020019100124120500457580010724");
            refundAttribute.put("refundBizSign", "fastRefund2");
            refundAttribute.put("refundScene", "FAST_TO_ORIGINAL");
            msg.put("refundAttribute", refundAttribute);
        }
        SendResult result = mqConfig.sendMessage(Constant.PAYMENT_TOPIC, Constant.REFUND_PROCESS_TAG, String.valueOf(tradeOrderLineId), msg.toJSONString());
        return JSONObject.toJSONString(result);
    }

    @NotNull
    private static JSONObject getRefundPendingStatement() {
        JSONObject refundStatement = new JSONObject();
        JSONArray refundAssetDetails = new JSONArray();
        JSONObject refundAssetDetail = new JSONObject();
        JSONObject refundAmount = new JSONObject();
        refundAmount.put("currency", "USD");
        refundAmount.put("value", "1");
        JSONObject refundMethod = new JSONObject();
        refundMethod.put("paymentMethodType", "MIXEDCARD");

        refundAssetDetail.put("refundAmount", refundAmount);
        refundAssetDetail.put("fundType", "EXTERNAL");
        refundAssetDetail.put("pendingReason", "CHANNEL_REFUND_NOT_SUPPORTED");
        refundAssetDetail.put("outAbility", "EXTERNAL");
        refundAssetDetail.put("payProvider", "CYBSAMEX");
        refundAssetDetail.put("refundAssetStage", "pending");
        refundAssetDetail.put("refundMethod", refundMethod);

        refundAssetDetails.add(refundAssetDetail);

        refundStatement.put("refundAssetDetails", refundAssetDetails);
        refundStatement.put("refundStatementId", "20240132154010801300188610287626743");
        refundStatement.put("refundStatementStage", "pending");
        return refundStatement;
    }

}
