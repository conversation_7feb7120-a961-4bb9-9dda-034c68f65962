package com.aliexpress.databank.service.impl;

import com.alibaba.ecommerce.module.Response;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.global.landlord.LandlordContext;
import com.alibaba.global.order.management.api.facade.OrderQueryForBuyerFacade;
import com.alibaba.global.order.management.api.model.TradeOrderLineDTO;
import com.alibaba.global.reverse.api.facade.GlobalReverseOrderQueryForBuyerFacade;
import com.alibaba.global.reverse.api.facade.GlobalReverseOrderQueryForSellerFacade;
import com.alibaba.global.reverse.api.model.ReverseOrderDTO;
import com.alibaba.global.reverse.api.model.ReverseOrderLineDTO;
import com.alibaba.global.reverse.api.model.request.seller.SearchReverseOrderRequest;
import com.aliexpress.databank.dataobject.RefundInfoLineRequest;
import com.aliexpress.databank.service.ReverseService;
import com.aliexpress.databank.utils.ConvertParam;
import com.aliexpress.databank.utils.HsfUtil;
import com.aliexpress.issue.api.AeIssueCancelOrderWriteFacade;
import com.aliexpress.issue.common.result.PlainResult;
import com.aliexpress.issue.dispute.pojo.common.CancelOrderOperatorRequest;
import com.aliexpress.issue.dispute.pojo.common.IssueCancelOrderOperatorResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class ReverseServiceImpl implements ReverseService {

    @Autowired
    private OrderQueryForBuyerFacade orderQueryForBuyerFacade;

    @Autowired
    private AeIssueCancelOrderWriteFacade cancelOrderWriteFacade;

    @Autowired
    private GlobalReverseOrderQueryForBuyerFacade reverseOrderQueryForBuyerFacade;

    @Autowired
    private GlobalReverseOrderQueryForSellerFacade globalReverseOrderQueryForSellerFacade;

    private static final Logger logger = LoggerFactory.getLogger(ReverseServiceImpl.class);

    @Override
    public PlainResult<IssueCancelOrderOperatorResult> cancelOrder(Long buyerId, Long orderId,
                                                                   String cancelEvent, List<TradeOrderLineDTO> tradeOrderLineDTOs, String refundChannel) {
        CancelOrderOperatorRequest cancelOrderOperatorRequest = getCancelOrderRequest(buyerId, orderId, cancelEvent, tradeOrderLineDTOs, refundChannel);
        LandlordContext.setTenantSpec("AE_GLOBAL", -1);
        return cancelOrderWriteFacade.openCancelOrderIssue(buyerId, null, cancelOrderOperatorRequest);
    }

    @Override
    public CancelOrderOperatorRequest getCancelOrderRequest(Long buyerId, Long orderId, String cancelEventFromRequest, List<TradeOrderLineDTO> orderLines, String refundChannel) {
        CancelOrderOperatorRequest cancelOrderOperatorRequest = new CancelOrderOperatorRequest();
        if (orderLines != null) {
            List<Long> tradeOrderLineIds = orderLines.stream().map(TradeOrderLineDTO::getTradeOrderLineId).collect(Collectors.toList());
            cancelOrderOperatorRequest.setOrderLineIds(tradeOrderLineIds);
        }
        cancelOrderOperatorRequest.setAdminAliId(buyerId);
        cancelOrderOperatorRequest.setOrderId(orderId);
        String cancelEvent = getCancelEvent(cancelEventFromRequest);
        cancelOrderOperatorRequest.setCancelEvent(cancelEvent);
        cancelOrderOperatorRequest.setCancelReason(getCancelReason(cancelEvent, cancelEventFromRequest));
        cancelOrderOperatorRequest.setOperatorAliId(buyerId);
        cancelOrderOperatorRequest.setCurrentOperatorRole("platform");
        cancelOrderOperatorRequest.setIssueOperateSource("hsf");
        cancelOrderOperatorRequest.setOperatorRole("platform");
        cancelOrderOperatorRequest.setOperatorMemo("cancelled by data-bank");
        cancelOrderOperatorRequest.setRefundPaymentMethodType(getRefundChannel(refundChannel));
        return cancelOrderOperatorRequest;
    }

    private String getRefundChannel(String refundChannel) {
        switch (refundChannel){
            case "原理退":
                return "";
            case "Bonus":
                return "BONUS";
            case "BALANCE":
                return "BALANCE";
        }
        return "";
    }

    private String getCancelEvent(String cancelEventFromRequest) {
        switch (cancelEventFromRequest) {
            case "部分发货超时":
            case "发货超时":
            case "拼团超时":
            case "支付超时":
                return "timeoutCancel";
            case "bops":
                return "bopsCancel";
            case "买家":
                return "buyerCancel";
            case "发货后风控关单":
            case "cro":
            case "量价风控关单":
                return "mteeCancel";
            case "支付":
                return "paymentCancel";
            case "物流":
                return "LogisticCloseCancel";
            case "支付风控":
                return "paymentRiskCancel";
            case "营销":
            case "定制商品审核关单":
                return "promotionCancel";
            case "卖家":
                return "sellerCancel";
            case "JIT不可达关单":
                return "supplyChainCancel";
        }
        return "buyerCancel";
    }

    @Override
    public PlainResult<IssueCancelOrderOperatorResult> rejectCancelOrder(Long buyerId, Long sellerId, Long orderId,
                                                                         String applyReason, List<TradeOrderLineDTO> tradeOrderLineDTOs) {
        List<Long> tradeOrderLineIds = tradeOrderLineDTOs.stream().map(TradeOrderLineDTO::getTradeOrderId).collect(Collectors.toList());
        CancelOrderOperatorRequest cancelOrderOperatorRequest = ConvertParam.getCancelOrderOperatorRequestBySellerSide(sellerId, orderId, applyReason, tradeOrderLineIds);
        return cancelOrderWriteFacade.denyCancelOrderIssue(buyerId, "", cancelOrderOperatorRequest);
    }

    @Override
    public PlainResult<IssueCancelOrderOperatorResult> agreeCancelOrder(Long buyerId, Long sellerId, Long orderId, String applyReason, List<TradeOrderLineDTO> tradeOrderLineDTOs) {
        List<Long> tradeOrderLineIds = tradeOrderLineDTOs.stream().map(TradeOrderLineDTO::getTradeOrderId).collect(Collectors.toList());
        CancelOrderOperatorRequest cancelOrderOperatorRequest = ConvertParam.getCancelOrderOperatorRequestBySellerSide(sellerId, orderId, applyReason, tradeOrderLineIds);
        PlainResult<IssueCancelOrderOperatorResult> agreeCancelOrderResponse = cancelOrderWriteFacade.approveCancelOrderIssue(buyerId, "", cancelOrderOperatorRequest);
        return agreeCancelOrderResponse;
    }

    @Override
    public Response<List<ReverseOrderLineDTO>> getBuyerReverseOrder(Long buyerId, Long orderId) {
        return reverseOrderQueryForBuyerFacade.queryReverseOrderLinesByTradeOrderId(buyerId, orderId);
    }

    //根据纠纷id查
    @Override
    public Response<ReverseOrderLineDTO> getReverseOrderLineByReverseOrderLineId(Long buyerId, Long reverseOrderLineId) {
        return reverseOrderQueryForBuyerFacade.queryReverseOrderLineById(buyerId, reverseOrderLineId);
    }


    @Override
    public Response<List<ReverseOrderDTO>> getSellerSearchReverseOrder(Long orderId) {
        SearchReverseOrderRequest searchReverseOrderRequest = getSearchReverseOrderRequest(orderId);
        return globalReverseOrderQueryForSellerFacade.searchReverseOrderDTOs(searchReverseOrderRequest);
    }

    @Override
    public Long placeReverseOrderByScenario(String scenario, Long buyerId, Long sellerId, String tradeOrderId,
                                            String tradeOrderLineId, JSONObject tradeOrderInfo) throws Exception {
        int quantity = tradeOrderInfo.getJSONObject("module").getIntValue("quantity");
        String amount = "";
        String currency = "";
        JSONObject createReverseReq = null;
        switch (scenario) {
            case "普通协商-等待买家响应-仅退款":
            case "普通协商-等待买家响应-退货退款":
            case "普通协商-等待买家响应-拒绝退款":
            case "普通协商-等待卖家响应-仅退款":
                return beginWithRefund(tradeOrderLineId, tradeOrderId, buyerId, amount, currency, quantity);
            case "普通协商-等待卖家响应-退货退款":
                return beginWithReturn(tradeOrderLineId, tradeOrderId, buyerId, amount, currency, quantity);
            case "普通协商-等待买家退货":
            case "普通协商-等待卖家收货":
            case "普通协商-卖家确认收货":
            case "普通协商-纠纷结束-仅退款":
                return finishOnlyRefund(tradeOrderLineId, tradeOrderId, buyerId, sellerId, amount, currency, quantity);
            case "普通协商-纠纷结束-退货退款":
                return finishReturn(tradeOrderLineId, tradeOrderId, buyerId, sellerId, amount, currency, quantity);
            case "普通协商-纠纷结束-0退款":
        }
        return null;
    }

    private Long finishReturn(String tradeOrderLineId, String tradeOrderId, Long buyerId, Long sellerId,
                              String amount, String currency, int quantity) throws Exception {
        JSONObject createReverseReq = getCreateReverseReq(tradeOrderLineId, tradeOrderId, "", buyerId,
                true, "", amount, currency, "RETURN", "51000", quantity, false);
        Long reverseOrderLineId = HsfUtil.createReverse(createReverseReq).getJSONArray("result").getJSONObject(0)
                .getJSONArray("reverseOrderLines").getJSONObject(0).getLong("reverseOrderLineId");
        JSONObject solutionRes = HsfUtil.getReverseSolution(reverseOrderLineId);
        JSONObject solution = getActiveSolution(solutionRes);
        Long solutionId = solution.getLong("solutionId");
        String addressId = HsfUtil.getReturnAddress(sellerId).getJSONObject(0).getString("id");
        HsfUtil.acceptSolutionBySeller(reverseOrderLineId, solutionId, sellerId, addressId);
        return reverseOrderLineId;
    }

    private Long beginWithReturn(String tradeOrderLineId, String tradeOrderId, Long buyerId,
                                 String amount, String currency, int quantity) throws Exception {
        JSONObject createReverseReq = getCreateReverseReq(tradeOrderLineId, tradeOrderId, "", buyerId,
                true, "", amount, currency, "RETURN", "51000", quantity, false);
        return HsfUtil.createReverse(createReverseReq).getJSONArray("result").getJSONObject(0)
                .getJSONArray("reverseOrderLines").getJSONObject(0).getLong("reverseOrderLineId");
    }

    private Long beginWithRefund(String tradeOrderLineId, String tradeOrderId, Long buyerId,
                                 String amount, String currency, int quantity) throws Exception {
        JSONObject createReverseReq = getCreateReverseReq(tradeOrderLineId, tradeOrderId, "", buyerId,
                true, "", amount, currency, "ONLY_REFUND", "51000", quantity, false);
        return HsfUtil.createReverse(createReverseReq).getJSONArray("result").getJSONObject(0)
                .getJSONArray("reverseOrderLines").getJSONObject(0).getLong("reverseOrderLineId");
    }

    private Long finishOnlyRefund(String tradeOrderLineId, String tradeOrderId, Long buyerId, Long sellerId,
                                  String amount, String currency, int quantity) throws Exception {
        JSONObject createReverseReq = getCreateReverseReq(tradeOrderLineId, tradeOrderId, "", buyerId,
                true, "", amount, currency, "ONLY_REFUND", "51000", quantity, false);
        Long reverseOrderLineId = HsfUtil.createReverse(createReverseReq).getJSONArray("result").getJSONObject(0)
                .getJSONArray("reverseOrderLines").getJSONObject(0).getLong("reverseOrderLineId");
        JSONObject solutionRes = HsfUtil.getReverseSolution(reverseOrderLineId);
        JSONObject solution = getActiveSolution(solutionRes);
        Long solutionId = solution.getLong("solutionId");
        HsfUtil.acceptSolutionBySeller(reverseOrderLineId, solutionId, sellerId, "");
        return reverseOrderLineId;
    }

    private SearchReverseOrderRequest getSearchReverseOrderRequest(Long orderId) {
        SearchReverseOrderRequest searchReverseOrderRequest = new SearchReverseOrderRequest();
        List<Long> tradeOrderIds = new ArrayList<>();
        tradeOrderIds.add(orderId);
        searchReverseOrderRequest.setTradeOrderIds(tradeOrderIds);
        return searchReverseOrderRequest;
    }

    private String getCancelReason(String cancelEvent, String timeoutType) {
        switch (cancelEvent) {
            case "bopsCancel":
                return "bops_manual_cancel";
            case "buyerCancel":
                return "buyerDonotwantOrder";
            case "LogisticCloseCancel":
                return "logistics_close";
            case "mteeCancel":
                if ("发货后风控关单".equalsIgnoreCase(timeoutType)) {
                    return "security_close6";
                }
                if ("量价风控关单".equalsIgnoreCase(timeoutType)) {
                    return "security_close7";
                }
                else {
                    return "security_close1";
                }
            case "paymentRiskCancel":
                return "security_close";
            case "paymentCancel":
                return "risk_reject_closed";
            case "promotionCancel":
                if ("定制商品审核关单".equals(timeoutType)) {
                    return "customized_products_audited_failed";
                }
                return "group_failure";
            case "sellerCancel":
                return "freightCommitDayNotMatch";
            case "timeoutCancel":
                if ("部分发货超时".equals(timeoutType)) {
                    return "partial_shipping_overtime_closed";
                } else if ("拼团超时".equals(timeoutType)) {
                    return "share_group_timeout";
                } else if ("支付超时".equals(timeoutType)) {
                    return "buyer_pay_timeout";
                }
                return "seller_send_goods_timeout";
            case "supplyChainCancel":
                return "warehouse_unable_sendout";
            default:
                return "";
        }
    }

    @Override
    public JSONObject getCreateReverseReq(String tradeOrderLineId, String tradeOrderId, String refundChannel, Long buyerId,
                                          boolean isReceived, String returnOrRefund, String amount, String currency,
                                          String returnType, String returnReason, int quantity, Boolean isOverSize) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("reverseType", returnOrRefund);
        if (returnOrRefund.equals("RETURN")) {
            JSONObject returnInfoReqDTO = getReturnType(returnType, isOverSize);
            jsonObject.put("returnInfoReqDTO", returnInfoReqDTO);
        }
        jsonObject.put("tradeOrderId", tradeOrderId);
        if (refundChannel.equalsIgnoreCase("bonus")) {
            JSONObject channel = new JSONObject();
            channel.put("refundChannelCode", refundChannel);
            jsonObject.put("refundChannel", channel);
        }
        jsonObject.put("buyerId", buyerId);
        JSONArray createReverseOrderLines = new JSONArray();
        JSONObject createReverseOrderLine = new JSONObject();
        createReverseOrderLine.put("quantity", quantity);
        createReverseOrderLine.put("isReceived", isReceived);
        createReverseOrderLine.put("tradeOrderLineId", tradeOrderLineId);
        JSONObject applyReason = getApplyReason(returnReason);
        createReverseOrderLine.put("applyReason", applyReason);
        JSONObject comment = getComment();
        createReverseOrderLine.put("comment", comment);
        JSONObject refundAmount = getRefundAmount(amount, currency);
        createReverseOrderLine.put("refundAmount", refundAmount);
        createReverseOrderLines.add(createReverseOrderLine);
        jsonObject.put("createReverseOrderLines", createReverseOrderLines);

        JSONObject refundAmt = new JSONObject();
        refundAmt.put("amount", amount);
        refundAmt.put("currencyCode", currency);
        JSONObject totalRefundAmount = new JSONObject();
        totalRefundAmount.put("refundAmt", refundAmt);
        jsonObject.put("totalRefundAmount", totalRefundAmount);

        return jsonObject;
    }

    @Override
    public JSONObject getCreateReverseV3Req(String tradeOrderLineId, String tradeOrderId, String refundChannel, Long buyerId,  String returnReason, int quantity)throws Exception {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("reverseType", "RETURN");

        JSONObject returnInfoReqDTO = new JSONObject();
        returnInfoReqDTO.put("returnWay", "free_return_service");
        returnInfoReqDTO.put("emailNum", "<EMAIL>");
        returnInfoReqDTO.put("overSize", false);
        jsonObject.put("returnInfoReqDTO", returnInfoReqDTO);

        jsonObject.put("tradeOrderId", tradeOrderId);
        if (refundChannel.equalsIgnoreCase("balance")) {
            JSONObject channel = new JSONObject();
            channel.put("refundChannelCode", refundChannel);
            jsonObject.put("refundChannel", channel);
        }
        jsonObject.put("buyerId", buyerId);
        JSONArray createReverseOrderLines = new JSONArray();
        JSONObject createReverseOrderLine = new JSONObject();
        createReverseOrderLine.put("quantity", quantity);
        createReverseOrderLine.put("isReceived", true);
        createReverseOrderLine.put("tradeOrderLineId", tradeOrderLineId);
        JSONObject applyReason = getApplyReason(returnReason);
        createReverseOrderLine.put("applyReason", applyReason);
        JSONObject comment = getComment();
        createReverseOrderLine.put("comment", comment);

        JSONObject refundInfo = getRefundInfo(tradeOrderLineId, tradeOrderId,buyerId,quantity);
        if (refundInfo!=null) {
            String amount = refundInfo.getString("amount");
            String currency = refundInfo.getString("currency");
            JSONObject refundAmt = new JSONObject();
            refundAmt.put("amount", amount);
            refundAmt.put("currencyCode", currency);
            JSONObject totalRefundAmount = new JSONObject();
            totalRefundAmount.put("refundAmt", refundAmt);
            jsonObject.put("totalRefundAmount", totalRefundAmount);

            JSONObject refundAmount = getRefundAmount("0", currency);
            createReverseOrderLine.put("refundAmount", refundAmount);
        }

        createReverseOrderLines.add(createReverseOrderLine);
        jsonObject.put("createReverseOrderLines", createReverseOrderLines);
        return jsonObject;
    }

    private JSONObject getRefundInfo(String tradeOrderLineId, String tradeOrderId, Long buyerId, int quantity) throws Exception {
        JSONObject InitiateReverseRenderLocalRequest = new JSONObject();
        RefundInfoLineRequest refundInfoLineRequest = new RefundInfoLineRequest();
        List<RefundInfoLineRequest> refundInfoLineRequestList = new ArrayList<>();
        refundInfoLineRequest.setTradeOrderLineId(tradeOrderLineId);
        refundInfoLineRequest.setQuantity(quantity);
        refundInfoLineRequestList.add(refundInfoLineRequest);
        InitiateReverseRenderLocalRequest.put("refundInfoLineRequests", refundInfoLineRequestList);
        InitiateReverseRenderLocalRequest.put("tradeOrderId", tradeOrderId);
        InitiateReverseRenderLocalRequest.put("buyerId", buyerId);

        JSONObject refundInfo = new JSONObject();
        JSONObject reverseRefundInfo = HsfUtil.queryReverseRefundInfo(InitiateReverseRenderLocalRequest);
        if (reverseRefundInfo.getJSONObject("module") != null) {
            JSONObject applyRefund = reverseRefundInfo.getJSONObject("module").getJSONObject("applyRefund");
            if (applyRefund != null) {
                JSONObject maxMoneyDTO = applyRefund.getJSONObject("maxMoneyDTO");
                if (maxMoneyDTO != null) {
                    refundInfo.put("amount", maxMoneyDTO.getString("unit"));
                    refundInfo.put("currency", maxMoneyDTO.getString("currency"));
                    return refundInfo;
                }
            }
        }
        return null;
    }

    private JSONObject getReturnType(String returnType, Boolean isOverSize) {
        JSONObject jsonObject = new JSONObject();
        if ("自寄".equals(returnType)) {
            jsonObject.put("returnWay", "self_drop-off");
        } else {
            jsonObject.put("returnWay", "free_return_service");
            jsonObject.put("emailNum", "<EMAIL>");
            if (isOverSize) {
                jsonObject.put("overSize", true);
            }

        }
        return jsonObject;
    }

    private JSONObject getApplyReason(String returnReason) {
        JSONObject reason = new JSONObject();
        // 默认无理由原因
        reason.put("reasonCode", 51000);
        if ("卖家原因".equals(returnReason)) {
            reason.put("reasonCode", 50801); // size not as describe
        } else if ("买家原因".equals(returnReason)) {
            reason.put("reasonCode", 51000); // i don't like any more
        } else if ("物流原因".equals(returnReason)) {
            reason.put("reasonCode", 50500); // no tracking info
        } else if ("短装原因".equals(returnReason)) {
            // 短装
            reason.put("reasonCode", 51400); // Items are missing
        }
        return reason;
    }

    @Override
    public JSONObject getComment() {
        JSONObject comment = new JSONObject();
        JSONArray files = new JSONArray();
        JSONObject file = new JSONObject();
        file.put("fileKey", "Hc56d4f012307487a8e7a4c065139ac5fq.jpeg");
        file.put("url", "https://ae01.alicdn.com/kf/Hdaf5b3ed4af34374b54ca8788724e5eeO.png");
        file.put("fileType", "PICTURE");
        files.add(file);
        comment.put("files", files);
        comment.put("memo", "Test by Data Bank");
        return comment;
    }

    @Override
    public JSONObject getRefundAmount(String amount, String currency) {
        JSONObject refund = new JSONObject();
        JSONObject amt = new JSONObject();
        amt.put("amount", amount);
        amt.put("currencyCode", currency);
        refund.put("refundAmt", amt);
        return refund;
    }

    @Override
    public JSONObject getActiveSolution(JSONObject solutionRes) {
        JSONArray solutions = solutionRes.getJSONArray("result");
        for (int i = 0; i < solutionRes.size(); i++) {
            JSONObject solution = solutions.getJSONObject(i);
            if (solution.getIntValue("solutionStatus") == 1) {
                return solution;
            }
        }
        return null;
    }

}
