package com.aliexpress.databank.service;

import com.alibaba.global.buy.api.request.CreateOrderRequest;
import com.aliexpress.databank.constant.OrderScenario;
import com.aliexpress.databank.dataobject.PlaceOrderValidation;
import com.aliexpress.databank.dataobject.ReverseOrder;

import java.util.List;

public interface PlaceOrderStrategy {

    OrderScenario getScenario();

    int isGenerate(Long buyerId, String scenario, int index);

    CreateOrderRequest getCreateOrderRequest(Long buyerId, List<Long> itemIds, String currencyCode,
                                             String countryCode, String deliveryOption) throws Exception;

    // return tradeOrderId
    List<String> placeOrder(CreateOrderRequest createOrderRequest);


    PlaceOrderValidation validate(Long tradeOrderId, String scenario);

    PlaceOrderValidation validate(Long tradeOrderId, Long promiseTemplateId, String features);

    ReverseOrder placeOrderResConvert2ReverseOrder(Long tradeOrderId, String scenario, int scenarioIndex);

    List<Long> getOrderIdsByScenarioIndex(String scenario, String scenarioIndex, int size);

    List<Long> getOrderIdsByScenario(String scenario, int size);

}
