package com.aliexpress.databank.service.impl;

import com.aliexpress.databank.constant.OrderScenario;
import com.aliexpress.databank.dataobject.PlaceOrderValidation;
import org.springframework.stereotype.Service;
// 官方仓海外退货 cn：cainiao
@Service
public class CnFreeReturnPlaceOrder extends BasePlaceOrderStrategy {

    @Override
    public OrderScenario getScenario() {
        return OrderScenario.CN_FREE_RETURN;
    }

    @Override
    public PlaceOrderValidation validate(Long tradeOrderId, Long promiseTemplateId, String features) {
        return super.validate(tradeOrderId, 22L, "");
    }

}

