package com.aliexpress.databank.service.impl;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.ecommerce.module.Response;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.global.uop.api.FulfillmentOrderQueryNoRoutingFacade;
import com.alibaba.global.uop.api.FulfillmentOrderStatusUpdateFacade;
import com.alibaba.global.uop.api.request.FulfillmentOrderStatusUpdateRequest;
import com.alibaba.global.uop.api.response.FulfillmentOrderDTO;
import com.alibaba.global.uop.api.response.FulfillmentOrderStatusUpdateResponseDTO;
import com.alibaba.global.uop.api.response.FulfillmentOrderWithPkgDTO;
import com.aliexpress.databank.dataobject.ReverseOrder;
import com.aliexpress.databank.hsf.OrderService;
import com.aliexpress.databank.mapper.ReverseOrderMapper;
import com.aliexpress.databank.service.ShipOrderService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Slf4j
@Service
@HSFProvider(serviceInterface = ShipOrderService.class, serviceGroup = "HSF", serviceVersion = "1.0.0", clientTimeout = 20000)
public class ShipOrderServiceImpl implements ShipOrderService {


    @Autowired
    private FulfillmentOrderQueryNoRoutingFacade fulfillmentOrderQueryNoRoutingFacade;

    @Autowired
    private FulfillmentOrderStatusUpdateFacade fulfillmentOrderStatusUpdateFacade;

    @Autowired
    private ReverseOrderMapper reverseOrderMapper;

    @Override
    public void shipOrders() {
        try {
            List<ReverseOrder> reverseOrders = reverseOrderMapper.findUnShipmentOrder();
            if (CollectionUtils.isNotEmpty(reverseOrders)) {
                reverseOrders.forEach(order -> {
                    Response<List<FulfillmentOrderWithPkgDTO>> fulfillmentOrderRes = fulfillmentOrderQueryNoRoutingFacade.queryFulfillmentOrderWithPkgByToId(order.getOrderId().toString(), null);
                    if (fulfillmentOrderRes.isSuccess() || fulfillmentOrderRes.getModule() != null) {
                        if (fulfillmentOrderRes.getModule().get(0).getFulfillmentOrderDTO().getOrderStatus() == 700) {
                            order.setShipped(true);
                            reverseOrderMapper.updateReverseOrder(order);
                        } else {
                            FulfillmentOrderStatusUpdateRequest fulfillmentOrderStatusUpdateRequest = getFulfillmentOrderStatusUpdateRequest(order.getOrderId().toString(), order.getShippingMethod(), fulfillmentOrderRes.getModule().get(0));
                            Response<FulfillmentOrderStatusUpdateResponseDTO> fulfillmentOrderStatusUpdateResponseDTOResponse = fulfillmentOrderStatusUpdateFacade.onEvent(fulfillmentOrderStatusUpdateRequest);
                            if (fulfillmentOrderStatusUpdateResponseDTOResponse.isSuccess()) {
                                order.setShipped(true);
                                reverseOrderMapper.updateReverseOrder(order);
                            } else {
                                log.error("Fail to Ship Order. Error: " + JSONObject.toJSONString(fulfillmentOrderStatusUpdateResponseDTOResponse));
                            }
                        }
                    }

                });
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    private FulfillmentOrderStatusUpdateRequest getFulfillmentOrderStatusUpdateRequest(String orderId, String shipType, FulfillmentOrderWithPkgDTO fulfillmentOrderWithPkgDTO) {
        FulfillmentOrderStatusUpdateRequest fulfillmentOrderStatusUpdateRequest = new FulfillmentOrderStatusUpdateRequest();
        fulfillmentOrderStatusUpdateRequest.setDataMap(getDataMap());
        fulfillmentOrderStatusUpdateRequest.setEvent(shipType.equalsIgnoreCase("部分发货") ? "PARTIAL_SHIPPING" : "COMPLETE_SHIPPING");
        fulfillmentOrderStatusUpdateRequest.setEventTime(System.currentTimeMillis());
        fulfillmentOrderStatusUpdateRequest.setEventSendTime(System.currentTimeMillis());
        fulfillmentOrderStatusUpdateRequest.setOutBizId(orderId);
        fulfillmentOrderStatusUpdateRequest.setSource("cainiao");
        fulfillmentOrderStatusUpdateRequest.setTrackingNumber(UUID.randomUUID().toString());
        fulfillmentOrderStatusUpdateRequest.setFulfillmentOrderId(fulfillmentOrderWithPkgDTO.getFulfillmentOrderDTO().getFulfillmentOrderId());
        fulfillmentOrderStatusUpdateRequest.setFulfillmentOrderItemList(getFulfillmentOrderItemIds(fulfillmentOrderWithPkgDTO.getFulfillmentOrderDTO()));
        return fulfillmentOrderStatusUpdateRequest;
    }

    private Map<String, Object> getDataMap() {
        Map<String, Object> dataMap = new ConcurrentHashMap<>();
        dataMap.put("shippingName", "AliExpress Standard Shipping");
        dataMap.put("shippingServiceCode", "CAINIAO_STANDARD");
        dataMap.put("isTrackingInfoExist", "Y");
        return dataMap;
    }

    private List<String> getFulfillmentOrderItemIds(FulfillmentOrderDTO fulfillmentOrderDTO) {
        List<String> fulfillmentOrderItemIds = new ArrayList<>();
        fulfillmentOrderDTO.getFulfillmentOrderItemDTOList().forEach(it -> fulfillmentOrderItemIds.add(it.getFulfillmentOrderItemId()));
        return fulfillmentOrderItemIds;
    }


}
