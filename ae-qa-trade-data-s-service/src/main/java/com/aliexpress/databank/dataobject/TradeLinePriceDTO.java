package com.aliexpress.databank.dataobject;

import com.alibaba.global.order.management.api.model.*;
import lombok.Data;

import javax.money.MonetaryAmount;
import java.util.List;

@Data
public class TradeLinePriceDTO {
    private Long tradeOrderId;
    private Long tradeOrderLineId;
    private BuyerInfoDTO buyer;
    private SellerInfoDTO seller;
    private Integer payStatus;
    private Integer deliveryStatus;
    private ExchangeInfoDTO exchangeInfo;
    private String unitFee;
    private String actualFee;
    private String payableFee;
    private String actualFeeOfPurposeCurrency;
    private String saleDiscountFee;
    private List<PromotionFeeInfoDTO> saleDiscountInfo;
    private List<PromotionFeeInfoDTO> originalSaleDiscountInfo;
    private MonetaryAmount nonGoldStandardDiscountFee;
    private MonetaryAmount goldStandardDiscountFee;
    private MonetaryAmount payNonGoldStandardDiscountFee;
    private MonetaryAmount payGoldStandardDiscountFee;
    private String shippingActualFee;
    private String shippingFee;
    private String shippingSurchargeFee;
    private String shippingDiscountFee;
    private List<PromotionFeeInfoDTO> shippingDiscountInfo;
    private List<PromotionFeeInfoDTO> originalShippingDiscountInfo;
    private MonetaryAmount nonGoldShippingDiscountFee;
    private MonetaryAmount goldShippingDiscountFee;
    private MonetaryAmount payNonGoldShippingDiscountFee;
    private MonetaryAmount payGoldShippingDiscountFee;
    private String taxActualFee;
    private String taxFee;
    private String taxPercentage;
    private String taxCode;
    private String taxRebateFee;
    private List<TaxRebateInfoDTO> taxRebateInfo;
    private String paymentDiscountFee;
    private String adjustFee;
    private PayOrderDTO payOrderDTO;
    private List<PromotionOrderDTO> promotionOrderDTO;
    private SnapshotDTO snapshotInfo;
    private VolumePriceDTO volumePrice;
    private String endReason;
    private Integer quantity;
    private TradeLineFeatureDTO tradeLineFeatureDTO;
    private TradeLineDynamicDTO tradeLineDynamicDTO;
//    private IntentionPriceDTO tradeIntentionPriceDTO;


}
