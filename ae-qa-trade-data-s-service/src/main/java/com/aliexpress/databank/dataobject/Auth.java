package com.aliexpress.databank.dataobject;

import com.alibaba.dubbo.common.utils.ConcurrentHashSet;

import java.util.Set;

public class Auth {

    private static Set<String> NO_NEED_AUTH_FILTER_METHOD = new ConcurrentHashSet<>();
    private static Set<String> ADMIN_AUTH_FILTER_METHOD = new ConcurrentHashSet<>();
    private static Set<String> ADMIN = new ConcurrentHashSet<>();
    public static Set<String> BUYER_AUTH = new ConcurrentHashSet<>();
    public static Set<String> SELLER_AUTH = new ConcurrentHashSet<>();



    public static Set<String> getNoNeedAuthFilterMethod() {
        return NO_NEED_AUTH_FILTER_METHOD;
    }

    public static boolean isInNoNeedAuthFilterMethod(String method) {
        return getNoNeedAuthFilterMethod().contains(method.trim());
    }

    public static Set<String> getAdminAuthFilterMethod() {
        return ADMIN_AUTH_FILTER_METHOD;
    }

    public static boolean isAdminAuthFilterMethod(String method) {
        return getAdminAuthFilterMethod().contains(method.trim());
    }

    public static void updateNoNeedAuthFilterMethod(Set<String> methods) {
        NO_NEED_AUTH_FILTER_METHOD.clear();
        NO_NEED_AUTH_FILTER_METHOD.addAll(methods);
    }

    public static void updateAuthNeedAuthFilterMethod(Set<String> methods) {
        ADMIN_AUTH_FILTER_METHOD.clear();
        ADMIN_AUTH_FILTER_METHOD.addAll(methods);
    }

    public static Set<String> getADMIN() {
        return ADMIN;
    }

    public static void updateAdmin(Set<String> admins) {
        ADMIN.clear();
        ADMIN.addAll(admins);
    }

    public static void updateBuyerAuth(Set<String> methods) {
        BUYER_AUTH.clear();
        BUYER_AUTH.addAll(methods);
    }

    public static void updateSellerAuth(Set<String> methods) {
        SELLER_AUTH.clear();
        SELLER_AUTH.addAll(methods);
    }
}
