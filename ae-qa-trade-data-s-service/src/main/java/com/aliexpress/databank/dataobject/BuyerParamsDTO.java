package com.aliexpress.databank.dataobject;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@NoArgsConstructor
@Data
public class BuyerParamsDTO {

    @JsonProperty("items")
    private List<ItemsDTO> items;
    @JsonProperty("orderFrom")
    private String orderFrom;

    @NoArgsConstructor
    @Data
    public static class ItemsDTO {
        @JsonProperty("quantity")
        private Integer quantity;
        @JsonProperty("itemId")
        private String itemId;
        @JsonProperty("deliveryOption")
        private String deliveryOption;
        @JsonProperty("skuId")
        private String skuId;
        @JsonProperty("attrs")
        private AttrsDTO attrs;

        @NoArgsConstructor
        @Data
        public static class AttrsDTO {
            @JsonProperty("skuAttr")
            private String skuAttr;
            @JsonProperty("carAdditionalInfo")
            private String carAdditionalInfo;
        }
    }
}