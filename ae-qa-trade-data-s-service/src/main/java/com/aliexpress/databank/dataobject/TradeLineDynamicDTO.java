package com.aliexpress.databank.dataobject;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.global.order.management.api.model.Features;
import lombok.Data;

import javax.money.MonetaryAmount;
import java.util.List;
import java.util.Map;

@Data
public class TradeLineDynamicDTO {

    private Long tradeOrderLineId;
    private MonetaryAmount shippingDiscountFee;
    private MonetaryAmount originalFee;
    private MonetaryAmount payableFee;
    private JSONObject discountFee;
    private List<JSONObject> promotionFeeDetails;
    private String paf;
    private String gaf;
    private MonetaryAmount actualFee;
    private MonetaryAmount adjustFee;
    private MonetaryAmount unitFee;
    private MonetaryAmount oriUnitFee;
    private Map<String, String> features;
    private JSONObject payCurrDiscountFee;
    private MonetaryAmount itemSingleDiscountFee;
    private MonetaryAmount itemGoldStandardFee;
    private MonetaryAmount itemNonGoldStandardFee;
    private MonetaryAmount shippingGoldStandardFee;
    private MonetaryAmount shippingNonGoldStandardFee;
    private MonetaryAmount pay_itemSingleDiscountFee;
    private MonetaryAmount pay_itemGoldStandardFee;
    private MonetaryAmount pay_itemNonGoldStandardFee;
    private MonetaryAmount pay_shippingGoldStandardFee;
    private MonetaryAmount pay_shippingNonGoldStandardFee;

}



