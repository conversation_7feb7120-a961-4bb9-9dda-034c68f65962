package com.aliexpress.databank.dataobject;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import javax.money.MonetaryAmount;
import java.util.List;

@Data
public class TradeIntentionPriceDTO {
    private Long tradeOrderLineId;
    private MonetaryAmount unitFee;
    private MonetaryAmount payableFee;
    private MonetaryAmount  actualFee;
    private JSONObject discountFeeDO;
    private List<JSONObject>  promotionFeeDetailDOS;
    private MonetaryAmount shippingFee;
    private List<JSONObject> taxDetails;
    private MonetaryAmount payPromotion;
    private JSONObject extra;
    private MonetaryAmount itemSingleGoldStandardDiscountFee;
    private MonetaryAmount itemGoldStandardFee;
    private MonetaryAmount itemNonGoldStandardFee;
}
