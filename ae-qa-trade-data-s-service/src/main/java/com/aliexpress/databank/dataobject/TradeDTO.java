package com.aliexpress.databank.dataobject;


import lombok.Data;

import java.util.List;

@Data
public class TradeDTO {

    public Long buyerId;

    public List<Long> productidlist;

    public Integer productcount; //商品数量

    public String currency = "USD";//币种

    public Long addressId =10013300002L;


    public String getCurrency() {
        return currency;
    }

    public void setCurrency(String currency) {
        this.currency = currency;
    }

    public Long getBuyerId() {
        return buyerId;
    }

    public void setBuyerId(Long buyerId) {
        this.buyerId =  buyerId;
    }

    public Integer getProductcount() {
        return productcount;
    }

    public void setProductcount(Integer productcount) {
        this.productcount =  productcount;
    }


    public  List<Long> getProductidlist() {
        return productidlist;
    }

    public void setProductidlist( List<Long> productidlist) {
        this.productidlist =  productidlist;
    }



}
