package com.aliexpress.databank.dataobject;

import com.aliexpress.databank.constant.ErrorCode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PlaceOrderValidation {

    private boolean match;
    private Long tradeOrderId;
    private String scenario;
    private List<ErrorInfo> errors = new ArrayList<>();

    public void addErrorInfo(Long tradeOrderId, Long tradeOrderLineId, ErrorCode errorCode) {
        ErrorInfo errorInfo = ErrorInfo.builder()
                .tradeOrderId(tradeOrderId)
                .tradeOrderLineId(tradeOrderLineId)
                .errorCode(errorCode)
                .build();
        this.errors.add(errorInfo);
    }

    public void addErrorInfo(Long tradeOrderId, ErrorCode errorCode) {
        ErrorInfo errorInfo = ErrorInfo.builder()
                .tradeOrderId(tradeOrderId)
                .errorCode(errorCode)
                .build();
        this.errors.add(errorInfo);
    }

    public static PlaceOrderValidation success(Long tradeOrderId, String scenario) {
        return PlaceOrderValidation.builder().tradeOrderId(tradeOrderId).match(true).scenario(scenario).build();
    }

}


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
class ErrorInfo {

    private Long tradeOrderId;
    private Long tradeOrderLineId;
    private ErrorCode errorCode;

}
