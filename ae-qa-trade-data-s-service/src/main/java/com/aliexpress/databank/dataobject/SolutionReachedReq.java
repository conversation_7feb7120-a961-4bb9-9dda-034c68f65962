package com.aliexpress.databank.dataobject;

import com.alibaba.global.protocol.common.model.request.BaseRequest;
import com.alibaba.global.reverse.api.model.base.OperatorDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class SolutionReachedReq extends BaseRequest implements Serializable {
    private Long reverseOrderLineId;

    private String receiverAddressId;

    private OperatorDTO operatorDTO;

    private Integer timeoutId;

    private String timeoutType;

    private List<Long> reverseOrderLineIdList;
}
