package com.aliexpress.databank.dataobject;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import javax.money.MonetaryAmount;
import java.math.BigDecimal;
import java.util.List;

@Data
public class TradeLineFeatureDTO {

    private Long tradeOrderLineId;
    private String intent_pay_cur;
    private BigDecimal intent_pay_rate;
    private JSONObject pdf;
    private List<JSONObject> pcpdf;
    private String ppf;
    private String pcf;
    private String cbImport;
    private String gaf;
    private String paf;
    private String gt;
    private String pft;
    private String include_tax;
    private List<JSONObject> v_o_tax_detail;
    private List<JSONObject> tax_detail;
    private MonetaryAmount proRetailPrice;
    private String d_p;
    private String v_P;
    private String r_v_p;
    private String d_p_shipping_settle;
    private String o_p_shipping_settle;
    private String promiseTemplate;
    private JSONObject _product_feature;
    private String _sku_tag;
    private List<JSONObject> _dp_pcpdf;
    private JSONObject _dynamic_price;
    private JSONObject intention_price;



}
