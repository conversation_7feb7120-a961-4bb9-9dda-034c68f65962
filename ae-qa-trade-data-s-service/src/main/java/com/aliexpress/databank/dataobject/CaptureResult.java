package com.aliexpress.databank.dataobject;

import com.alibaba.global.payment.api.vo.Money;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

@Data
public class CaptureResult {
	private Result result;
	private String payToId;
	private String paymentId;
	private String captureTime;
	private String captureRequestId;
	private Money captureAmount;
	private String suggestedSettleTime;
	private String captureId;
	private String notifyType = "CAPTURE_RESULT";
	@Getter
	@Setter
	public static class Result{
		private String resultStatus;

		private String resultCode;

		private String resultMessage;
	}
}
