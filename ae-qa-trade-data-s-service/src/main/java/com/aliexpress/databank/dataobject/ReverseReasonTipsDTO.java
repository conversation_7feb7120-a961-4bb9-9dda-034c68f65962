package com.aliexpress.databank.dataobject;

import lombok.Data;

@Data
public class ReverseReasonTipsDTO {
    public ReverseReasonTipsDTO() {
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        } else if (!(o instanceof ReverseReasonTipsDTO)) {
            return false;
        } else {
            ReverseReasonTipsDTO other = (ReverseReasonTipsDTO)o;
            return other.canEqual(this);
        }
    }

    protected boolean canEqual(Object other) {
        return other instanceof ReverseReasonTipsDTO;
    }

    public int hashCode() {
        int result = 1;
        return result;
    }

    public String toString() {
        return "ReverseReasonTipsDTO()";
    }
}
