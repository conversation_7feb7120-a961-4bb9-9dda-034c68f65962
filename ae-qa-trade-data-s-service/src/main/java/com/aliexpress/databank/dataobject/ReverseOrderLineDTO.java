package com.aliexpress.databank.dataobject;

import lombok.Data;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

@Data
public class ReverseOrderLineDTO implements Serializable {
    private Long reverseOrderLineId;
    private Long reverseOrderId;
    private Long tradeOrderId;
    private Long tradeOrderLineId;
    private Long buyerId;
    private Long sellerId;
   /* private ZonedDateTime gmtCreate;
    private ZonedDateTime gmtModified;*/
    private Integer reverseType;
    private Integer reverseStatus;
    private Integer goodsStatus;
    private String disputeStatus;
    private ReverseReasonDTO applyReason;
    private ReverseReasonDTO refuseReason;
    private Map<String, String> features = new HashMap();
    private FundOrderDTO fundOrderDTO;
}
