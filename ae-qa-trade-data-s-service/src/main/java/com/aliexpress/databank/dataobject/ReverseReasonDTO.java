package com.aliexpress.databank.dataobject;

import com.google.common.collect.Maps;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Data
public class ReverseReasonDTO implements Serializable {
    private Long id;
    private Long code;
    private MultiLanguageDTO displayText;
    private List<ReverseReasonTipsDTO> reasonTipsList;
    private Map<String, String> features = Maps.newHashMap();
    private Long parentReasonId;
    private ReverseReasonDTO parentReason;
}
