package com.aliexpress.databank.dataobject;

import lombok.Data;

import java.util.List;

@Data
public class DataDisableDTO {

	private List<String> userNoExist;

	private List<String> userRiskControl;

	private List<String> productNoExist;

	private List<String> productStatusError;

	private List<String> productNotEnough;

	private List<String> productStockWarn;

	private List<String> addressNotMatch;

	private List<String> needConfirm;

}
