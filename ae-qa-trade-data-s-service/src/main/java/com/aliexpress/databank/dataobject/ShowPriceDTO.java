package com.aliexpress.databank.dataobject;

import lombok.Data;
import java.util.Map;

@Data
public class ShowPriceDTO {
    private Long tradeOrderId;
    private Long tradeOrderLineId;
    private String unitPrice;
    private String quantity;
    private String productPrice;//商品总额
    private String productPrices;//产品总金额
    private String shippingCost;
    private String adjustFee;
    private String tax;
    private String duty;
    private Map<String,String> discountInfo;
    private String nonGoldStandardDiscountFee;
    private String goldStandardDiscountFee;
    private String totalAmount;
    private String actualPaidFee;
    private String paymentPromotionFee;
    private String paymentCostFee;
    private String payableFee;
}
