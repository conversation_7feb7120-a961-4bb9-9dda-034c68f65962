package com.aliexpress.databank.dataobject;

import lombok.Data;

import java.util.Date;

@Data
public class UserPool {
    /**
     * 主键
     */
    private Long id;
    /**
     * 用户名或email
     */
    private String userName;
    /**
     * 登录密码
     */
    private String password;
    /**
     * 用户id
     */
    private Long accountId;
    /**
     * 可用状态
     */
    private Integer enableType;
    /**
     * 扩展字段
     */
    private String features;
    /**
     * 地址标签
     */
    private String addressTag;
    /**
     * 是否被占用，false为未占用
     */
    private Integer isUsed;
    /**
     * 用户标签
     */
    private String userTag;
    /**
     * 买家机房
     */
    private String buyerLocation;

    /**
     * 是否已删除0.未删除,1.已删除
     */
    private Integer isDel;
    /**
     * 创建时间
     */
    private Date gmtCreate;
    /**
     * 更新时间
     */
    private Date gmtModified;

    /**
     * 使用场景，ui数据池-ui，默认为null
     */
    private String scene;

    /**
     * 会员id
     */
    private Long memberId;

    /**
     * 失效时间
     */
    private Long gmtDead;
}
