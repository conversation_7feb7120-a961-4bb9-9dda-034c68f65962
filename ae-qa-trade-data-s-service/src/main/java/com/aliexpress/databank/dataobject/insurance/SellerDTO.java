package com.aliexpress.databank.dataobject.insurance;

import lombok.Data;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
@Data
public class SellerDTO implements Serializable {
    private static final long serialVersionUID = 8611372821343380300L;
    private Long id;
    private String email;
    private Map<String, String> dataTags = new HashMap();
    private Map<String, Boolean> booleanTags = new HashMap();
}
