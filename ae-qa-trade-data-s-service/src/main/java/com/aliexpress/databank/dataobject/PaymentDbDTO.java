package com.aliexpress.databank.dataobject;


import com.alibaba.fastjson.JSONObject;
import com.alibaba.global.payment.api.vo.PaymentRecord;
import lombok.Data;

/**
 * 支付数据库返回支付、退款数据
 */
@Data
public class PaymentDbDTO {
    private String checkoutOrderNo;

    private String status;

    private String refundStatus;

    private String instructionNo;

    private PaymentRecord paymentRecord;

    //支付渠道
    private String paymentMethod;

    //实际支付金额
    private Integer payAmount;

    /**
     * 这里优惠=订单金本位+支付金本位
     */
    private Integer promotionAmount;

    private Integer orderPromotionAmount;

    private Integer paymentPromotionAmount;

    private Integer pointsAmount;

    private Integer orderAmount; //报价币种-不包含订单金本位

    private JSONObject refundData;

}
