package com.aliexpress.databank.dataobject;

import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
public class ResultVo<T> {

    private Boolean success;

    private T res;

    private String errorCode;

    private String errorMsg;

    public static ResultVo ofSuccess(Object data) {
        ResultVo resultVo = new ResultVo();
        resultVo.res = data;
        resultVo.success = true;
        return resultVo;
    }

    public static ResultVo ofFail(String errorCode, String errorMsg) {
        ResultVo resultVo = new ResultVo();
        resultVo.errorCode = errorCode;
        resultVo.errorMsg = errorMsg;
        return resultVo;
    }

}
