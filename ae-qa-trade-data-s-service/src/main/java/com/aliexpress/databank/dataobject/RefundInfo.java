package com.aliexpress.databank.dataobject;


import com.alibaba.global.payment.api.vo.Money;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class RefundInfo implements Serializable {
	private String refundStatus;

	private String refundRequestId;

	private String paymentId;

	private String refundId;

	private String payToId;

	private String refundReason = "use refund";

	//init/send/settle
	private String refundStage;

	private String refundStageTime;

	private String refundedTime;

	private Money refundAmount;

	private List<RefundStatements> refundStatementsList;

}
