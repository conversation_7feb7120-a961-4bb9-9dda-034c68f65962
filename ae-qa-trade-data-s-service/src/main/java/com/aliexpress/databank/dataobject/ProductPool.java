package com.aliexpress.databank.dataobject;

import lombok.Data;

import java.util.Date;

@Data
public class ProductPool {

    private Long id;
    /**
     * 商品id
     */
    private Long productId;
    /**
     * 可用状态
     */
    private Integer enableType;
    /**
     * 库存个数
     */
    private Long stockNum;
    /**
     * 扩展字段
     */
    private String features;

    /**
     * 商品类型
     */
    private String productType;
    /**
     * 是否被占用，false为未占用
     */
    private Integer isUsed;
    /**
     * 商家id
     */
    private Long sellerId;

    /**
     * 商品最低价格
     */
    private Long price;

    /**
     * 商品类目id
     */
    private Long categoryId;

    /**
     * 是否已删除0.未删除,1.已删除
     */
    private Integer isDel;
    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新时间
     */
    private Date gmtModified;
}
