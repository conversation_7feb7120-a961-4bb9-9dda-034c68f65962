package com.aliexpress.databank.dataobject.insurance;

import lombok.Data;

import java.io.Serializable;
import java.util.Map;
@Data
public class InsuranceCaseOrderDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    private Long insuranceCaseId;
    private String reportNo;
    private Integer status;
    private Long accidentTime;
    private Long reportTime;
    private Long reportSuccessTime;
    private InsuranceCaseFeeDTO insuranceCaseFee;
    private InsuranceClaimOrderDTO claimOrder;
    private Map<String, String> featureMap;
}
