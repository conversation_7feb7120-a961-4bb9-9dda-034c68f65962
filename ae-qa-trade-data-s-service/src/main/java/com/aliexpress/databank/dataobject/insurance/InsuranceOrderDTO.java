package com.aliexpress.databank.dataobject.insurance;

import lombok.Data;

import java.io.Serializable;
import java.util.Map;

@Data
public class InsuranceOrderDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    private Long insuranceId;
    private BuyerDTO buyer;
    private SellerDTO seller;
    private BizOrderDTO bizOrder;
    private String policyNo;
    private String productPlatform;
    private String productCode;
    private Integer status;
    private Long insureTime;
    private Long effectTime;
    private Long expireTime;
    private InsuranceFeeDTO insuranceFee;
    private Map<String, String> featureMap;
}
