package com.aliexpress.databank.dataobject;

import com.alibaba.global.order.management.api.model.*;
import lombok.Data;
import java.util.List;

@Data
public class TradePriceDTO {

    private Long tradeOrderId;
    private String actualFee;
    private String orderAmount;
    private String saleOriginalFee;
    private String saleDiscountFee;
    private String shippingActualFee;
    private String shippingFee;
    private String shippingSurchargeFee;
    private String shippingDiscountFee;
    private String taxActualFee;
    private String taxFee;
    private String taxRebateFee;
    private String actualFeeOfPurposeCurrency;
    private PayOrderDTO payOrderDTO;
    private PayOrderInfoDTO payOrderInfoDTO;
    private String snapshotId;
    private String promotionSnapshotId;
    private BuyerInfoDTO buyer;
    private Integer searchStatus;
    private Integer orderStatus;
    private Integer payStatus;
    private Integer deliverStatus;
    private String endReason;
    private String otherOrderInfo;
    private List<TradeLinePriceDTO> tradeLinePriceDTO;

}
