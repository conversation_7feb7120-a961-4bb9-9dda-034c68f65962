package com.aliexpress.databank.dataobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReverseOrder {

    private Long id;
    private Long orderId;
    private Long buyerId;
    private String buyerPreEnv;
    private String buyerOnlineEnv;
    private Long sellerId;
    private String sellerLocation;
    private String sellerPreEnv;
    private String sellerOnlineEnv;
    private Boolean used;
    private Boolean refund;
    private String promiseTemplate;
    private String reverseType;
    private String shippingMethod;
    private Boolean multiOrderLines;
    private Boolean overDisputeProtection;
    private Boolean shipped;
    private Boolean confirmed;
    private String features;
    private String scenario;
    private String scenarioIndex;
    private Date gmtUsed;
    private Date gmtCreated;
    private Date gmtModified;
    private String buyerLocation;

}
