package com.aliexpress.databank.dataobject;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

@Data
public class TradePaymentResult {
	//订单优惠后支付优惠前的金额 支付币种
	private Money actualPaidAmount;
	//实际支付金额 支付币种
	private Money actualPaidCash;
	//订单优惠后支付优惠前的金额 报价币种
	private Money actualPaidPostAmount;
	//实际支付金额 报价币种
	private Money actualPaidPostCash;

	private Boolean amountCheck=false;

	private JSONObject attributes = new JSONObject();

	private String batchPayNo; //批次

	private String bizOrderId;

	private Long buyerId;

	private Money checkoutOrderAmount; //收单金额

	private String checkoutOrderId;

	private String eventType="CHECKOUT_ORDER";

	private JSONObject fxRate;

	private Boolean mulitPayEvent = false;

	private String payOptionCode;

	private String payPlanNo;

	private String payTermName ="DEFAULT";

	private Money paymentPromotionPayAmount; //promotion 支付币种

	private Money paymentPromotionPostAmount; //promotion 报价币种

	private String promotionSanpshotId;

	private String status;

	private String paidDate = "2023-05-15T01:34:12.199-07:00[America/Los_Angeles]";



	@Getter
	@Setter
	public static class Money{
		private Long cent;

		private String currencyCode;


		public Money(Long cent, String currencyCode) {
			this.cent = cent;
			this.currencyCode = currencyCode;
		}
	}
}
