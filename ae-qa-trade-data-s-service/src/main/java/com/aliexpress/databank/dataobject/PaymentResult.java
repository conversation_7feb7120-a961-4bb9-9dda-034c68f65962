package com.aliexpress.databank.dataobject;

import com.alibaba.global.payment.api.vo.Money;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

@Data
public class PaymentResult {
	private PremiumRiskInfo premiumRiskInfo;
	private Result result;
	private String paymentId;
	private String payToRequestId;
	private String paymentRequestId;
	private String payToId;
	private List<PaymentDetailSummary> paymentDetailSummaries;
	private Money payToAmount;
	private Money paymentAmount;
	private String paymentTime;
	private String extendInfo;
	private String notifyType = "PAYMENT_RESULT";
	/**
	 * 可放款时间 2020-04-23T05:48:44-07:00
	 */
	private String suggestedSettleTime;

	@Getter
	@Setter
	public static class Result{
		private String resultStatus;
		private String resultCode;
		private String resultMessage;
	}

	@Getter
	@Setter
	public static class PaymentDetailSummary implements Serializable {
		private static final long serialVersionUID = -4497856403810870991L;
		/** 应收支付金额 */
		private Money paymentAmount;
		/** 支付方式 */
		private String paymentMethodType;
		/** 买家账号(iPay会员id) */
		private String customerId;
		/** 支付工具的其他支付要素信息 */
		private String paymentMethodMetaData;
		/** 扩展信息 */
		private String extendInfo;
	}

	@Getter
	@Setter
	public static class PremiumRiskInfo {
		private String needManualReview;
		private String isecurityBizId;
	}
}
