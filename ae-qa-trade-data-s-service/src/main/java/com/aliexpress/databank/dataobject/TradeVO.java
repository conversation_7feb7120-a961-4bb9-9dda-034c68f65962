package com.aliexpress.databank.dataobject;

import com.alibaba.global.order.management.api.model.TradeOrderDTO;
import com.aliexpress.databank.constant.Constant;
import lombok.Data;

@Data
public class TradeVO {

    private Long orderId;

    private String sellerLoginId;

    private Long sellerAccountId;

    private String buyerLoginId;

    private Long buyerAccountId;

    private String orderStatus;

    private String reverseStatus;

    private String payAmount;

    private String postAmount;

    private String orderCreateTime;

    private String orderPaymentTime;

    private String gmtModified;

    private String deliveryStatus;

    private String frozen;

    public TradeVO() {

    }

    public TradeVO(TradeOrderDTO tradeOrderDTO, String buyerLoginId) {
        this.orderId = tradeOrderDTO.getTradeOrderId();
        this.sellerAccountId = tradeOrderDTO.getOrderLines().get(0).getSeller().getSellerId();
        this.buyerAccountId = tradeOrderDTO.getBuyer().getBuyerId();
        this.buyerLoginId = buyerLoginId;
        this.orderStatus = getOrderStatus(tradeOrderDTO.getSearchStatus());
        this.payAmount = String.valueOf(tradeOrderDTO.getActualFee());
        this.postAmount = String.valueOf(tradeOrderDTO.getOrderAmount());
        this.orderCreateTime = tradeOrderDTO.getCreateTime() == null ?
                "未从交易获取到" : tradeOrderDTO.getCreateTime().format(Constant.FORMATTER);
        this.orderPaymentTime = tradeOrderDTO.getPaidTime() == null ?
                "未从交易获取到" : tradeOrderDTO.getPaidTime().format(Constant.FORMATTER);
        this.gmtModified = tradeOrderDTO.getModifyTime() == null ?
                "未从交易获取到" : tradeOrderDTO.getModifyTime().format(Constant.FORMATTER);
        this.gmtModified = tradeOrderDTO.getModifyTime() == null ?
                "未从交易获取到" : tradeOrderDTO.getModifyTime().format(Constant.FORMATTER);
        this.deliveryStatus = getDeliveryStatus(tradeOrderDTO.getDeliverStatus());
        this.frozen = getFrozen(tradeOrderDTO.getFreeze());

    }

    private String getDeliveryStatus(Integer deliverStatus) {
        switch (deliverStatus) {
            case 0:
                return "未生成履约单";
            case 4:
                return "待卖家发货";
            case 5:
                return "卖家部分发货";
            case 6:
                return "卖家已全部发货";
            case 7:
                return "买家确认收货";
            case 8:
                return "发货关闭";
        }
        return String.valueOf(deliveryStatus);
    }

    private String getFrozen(Integer freeze) {
        return freeze == 0 ? "未冻结" : "已冻结";
    }

    private String getOrderStatus(Integer orderStatus) {
        switch (orderStatus) {
            case 1:
                return "待支付";
            case 4:
                return "支付风控中";
            case 5:
                return "支付缓冲中";
            case 6:
                return "等待卖家发货";
            case 7:
                return "卖家部分发货";
            case 8:
                return "等待买家收货";
            case 9:
                return "订单完结";
            case 10:
                return "订单取消中";
        }
        return String.valueOf(orderStatus);
    }
}
