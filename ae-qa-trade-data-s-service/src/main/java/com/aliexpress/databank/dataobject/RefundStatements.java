package com.aliexpress.databank.dataobject;

import com.alibaba.global.payment.api.vo.Money;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class RefundStatements implements Serializable {

	private String refundStatementId;

	private String refundStrategy;

	private String refundStatementStage;

	private List<RefundAssetDetails> refundAssetDetails;

	@Data
	public static class RefundAssetDetails{

		private String fundType;

		private String payProvider;

		private String refundAssetStageTime;

		private String outAbility;

		private RefundMethod refundMethod;

		private String refundAssetStage;

		private Money refundAmount;
	}

	@Data
	public static class RefundMethod{
		private String paymentMethodType;
	}

}
