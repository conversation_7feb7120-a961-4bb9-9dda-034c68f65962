package com.aliexpress.databank.dataobject;


import com.alibaba.fastjson.JSONArray;
import lombok.Data;




@Data
public class OrderDTO {

    public String orderId;

    public String buyerEmail;

    public JSONArray linkTags;

    public String payStatus;

    public String getPayStatus() {
        return payStatus;
    }

    public void setPayStatus(String payStatus) {
        this.payStatus = payStatus;
    }

    public String getDeliveryStatus() {
        return deliveryStatus;
    }

    public void setDeliveryStatus(String deliveryStatus) {
        this.deliveryStatus = deliveryStatus;
    }

    public String deliveryStatus;

    public String getBuyId() {
        return buyId;
    }

    public void setBuyId(String buyId) {
        this.buyId = buyId;
    }

    public String buyId;

//    public String getOrderStatus() {
//        return orderStatus;
//    }
//
//    public void setOrderStatus(String orderStatus) {
//        this.orderStatus = orderStatus;
//    }
//
//    public String orderStatus;


    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getBuyerEmail() {
        return buyerEmail;
    }

    public void setBuyerEmail(String buyerEmail) {
        this.buyerEmail = buyerEmail;
    }

    public JSONArray getLinkTags() {
        return linkTags;
    }

    public void setLinkTags(JSONArray  linkTags) {
        this.linkTags = linkTags;
    }
}
