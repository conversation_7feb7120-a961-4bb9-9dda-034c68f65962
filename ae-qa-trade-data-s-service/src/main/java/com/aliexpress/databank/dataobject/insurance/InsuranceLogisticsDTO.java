package com.aliexpress.databank.dataobject.insurance;

import lombok.Data;

import java.io.Serializable;
@Data
public class InsuranceLogisticsDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    private InsuranceLogisticsWarehouseDTO logisticsWarehouse;
    private String mailNo;
    private String expressUrl;
    private String logisticsCompanyCode;
    private String barCode;
    private String postManName;
    private String postManPhoneNumber;
    private String currentStatus;
    private Long logisticsTime;
    private String deliveryFailedReason;
    private String deliveryFailedCode;
}
