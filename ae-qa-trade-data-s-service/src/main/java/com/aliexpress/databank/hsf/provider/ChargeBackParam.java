package com.aliexpress.databank.hsf.provider;

import com.aliexpress.databank.constant.Constant;
import lombok.Builder;
import lombok.Data;

/**
 * @className: ChargeBackParam
 * @description: TODO 单元测试使用，记得删除
 * @author: jinchuan
 * @date: 2022/10/31
 **/
@Data
@Builder
public class ChargeBackParam {
    Long orderIdStr;
    Long buyerId;
    String isNewCb;
    String rowCode;
    Boolean isMerge;
    String chargebackId;
    String resolveDueTime;
    String customerMsgs;
    String shippingCarrier;
    String shippingNumber;
    String judgementMsg;
    String currencyCode; //USD
    String chargebackAmount;
}


