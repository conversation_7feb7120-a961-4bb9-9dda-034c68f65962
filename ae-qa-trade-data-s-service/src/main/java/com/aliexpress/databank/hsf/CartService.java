package com.aliexpress.databank.hsf;

import com.alibaba.global.carts.api.request.CartAddRequest;
import com.alibaba.global.qa.dc.model.param.SystemDTO;
import com.alibaba.global.qa.dc.model.result.ResultDTO;

import java.util.Map;


public interface CartService {
    ResultDTO queryShoppingCart(String params, SystemDTO systemDTO);

    ResultDTO addCartItems(String params, SystemDTO systemDTO);

    ResultDTO emptyShoppingCart(String params, SystemDTO systemDTO);

    ResultDTO emptyCartItems(String params, SystemDTO systemDTO);

    ResultDTO getCartItemByID(String params, SystemDTO systemDTO) throws Exception;

    CartAddRequest getAddCartRequest(long buyerId, Map<Long, Long> items, Map<Long, Integer> quantities, String currency, String shipTo, String business);
    ResultDTO queryBrainIdRealtimeGroups(String params, SystemDTO systemDTO);
}