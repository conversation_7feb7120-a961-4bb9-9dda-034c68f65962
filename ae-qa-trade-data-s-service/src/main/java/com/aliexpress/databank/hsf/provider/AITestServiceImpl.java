package com.aliexpress.databank.hsf.provider;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.global.qa.dc.model.param.SystemDTO;
import com.alibaba.global.qa.dc.model.result.ResultDTO;
import com.alibaba.intl.ae.logistics.address.dto.WlMailingAddressResultDTO;
import com.aliexpress.databank.constant.Constant;
import com.aliexpress.databank.dataobject.*;
import com.aliexpress.databank.hsf.AIAdaptationService;
import com.aliexpress.databank.hsf.AddressService;
import com.aliexpress.databank.hsf.CartService;
import com.aliexpress.databank.hsf.OrderService;
import com.aliexpress.databank.hsf.api.AITestService;
import com.aliexpress.databank.hsf.api.request.AIEnhancedTestDataRequest;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.log4j.Log4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Autowired;

import java.lang.reflect.Field;
import java.util.Map;

@Log4j
@HSFProvider(serviceInterface = AITestService.class, serviceGroup = "HSF")
public class AITestServiceImpl implements AITestService {


    @Override
    public ResultDTO generateAIEnhancedTestData(AIEnhancedTestDataRequest aiEnhancedTestDataRequest) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        String url = "https://aistudio.alibaba-inc.com/api/aiapp/run/tnRKqamJbEG/latest";
        OkHttpClient client = new OkHttpClient();
        try {
            Field connectTimeoutField = OkHttpClient.class.getDeclaredField("connectTimeout");
            connectTimeoutField.setAccessible(true);
            connectTimeoutField.set(client, 300_000); // 单位：毫秒

            Field readTimeoutField = OkHttpClient.class.getDeclaredField("readTimeout");
            readTimeoutField.setAccessible(true);
            readTimeoutField.set(client, 300_000);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            throw new RuntimeException("无法设置超时时间", e);
        }

        // 构建JSON数据
        String jsonInputString = String.format(
                "{\"empId\": \"%s\"," +
                        "\"question\": \"%s\"," +
                        "\"sessionId\": \"%s\"," +
                        "\"stream\": false}",
                aiEnhancedTestDataRequest.getEmpId(),
                aiEnhancedTestDataRequest.getQuestion(),
                aiEnhancedTestDataRequest.getSessionId());

        RequestBody body = RequestBody.create(MediaType.parse("application/json"), jsonInputString);
        Request request = new Request.Builder()
                .url(url)
                .addHeader("X-AK", Constant.AK_INFO.getString("generateAIEnhancedTestData"))
                .addHeader("accept", "*/*")
                .addHeader("content-type", "application/json")
                .post(body)
                .build();

        try (Response response = client.newCall(request).execute();) {
            if (response.isSuccessful()&& response.body() != null) {
                String responseBody = response.body().string();
                ObjectMapper mapper = new ObjectMapper();

                JsonNode rootNode = mapper.readTree(responseBody);
                if(rootNode.path("success").asBoolean()){
                    String content = rootNode.path("data").path("content").asText();
                    resultDTO.setData(content);
                    resultDTO.setSuccess(true);
                }else{
                    // 提取错误信息
                    String errorCode = rootNode.path("errorCode").asText(); // 外层errorCode
                    String errorMsg = rootNode.path("errorMsg").asText();
                    String traceId = rootNode.path("traceId").asText();

                    // 提取traceId（假设traceId在data字段内）
                    resultDTO.setSuccess(false);
                    resultDTO.setMessage(errorMsg);
                    resultDTO.setErrorCode(errorCode);
                    resultDTO.setData(traceId);
                }
            } else {
                resultDTO.setSuccess(false);
                resultDTO.setMessage("请求失败，状态码：" + response.code());
            }
        }
        return resultDTO;
    }
}
