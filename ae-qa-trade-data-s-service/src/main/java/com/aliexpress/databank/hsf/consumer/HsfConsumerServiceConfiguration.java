package com.aliexpress.databank.hsf.consumer;

import com.alibaba.ae.reverse.api.AeAfterSaleStrategyFacade;
import com.alibaba.ae.reverse.api.AeReverseOrderQueryFacade;
import com.alibaba.ae.reverse.api.AeReverseOrderQueryForConsoleFacade;
import com.alibaba.ae.reverse.server.api.facade.InternalToolFacade;
import com.alibaba.ae.service.open.facade.BreachContractArbitrationFacade;
import com.alibaba.ae.service.open.facade.InternalToolsFacade;
import com.alibaba.ae.service.open.facade.TimeoutCalculateFacade;
import com.alibaba.boot.hsf.lightapi.ServiceFactory;
import com.alibaba.global.address.api.facade.GlobalAddressValidateFacade;
import com.alibaba.rdc.xcommerce.dispute.client.service.DisputeWriteService;
import com.alibaba.reverse.cathedral.api.facade.ReverseArbitrationFacade;
import com.lazada.imptest.api.facade.ic.ProductOpenService;
import com.lazada.imptest.api.facade.ic.ProductToolService;
import com.taobao.hsf.app.api.util.HSFApiConsumerBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Arrays;

@Configuration
public class HsfConsumerServiceConfiguration {

    @Bean(name="ProductToolService")
    public ProductToolService getProductToolService() throws Exception{

	    HSFApiConsumerBean consumerBean = new HSFApiConsumerBean();
        consumerBean.setInterfaceClass(ProductToolService.class);
        consumerBean.setVersion("1.0.0");
        consumerBean.setGroup("HSF");
	    consumerBean.setClientTimeout(5000);
        consumerBean.setConfigserverCenter(Arrays.asList("pre"));
	    consumerBean.setIncludeRouters(Arrays.asList("cluster-router"));
	    consumerBean.setIncludeFilters(Arrays.asList("cluster-filter"));
	    consumerBean.init(true);

        return (ProductToolService)consumerBean.getObject();
    }

    @Bean(name="ProductOpenService")
    public ProductOpenService getProductOpenService() throws Exception{

        HSFApiConsumerBean consumerBean = new HSFApiConsumerBean();
        consumerBean.setInterfaceClass(ProductOpenService.class);
        consumerBean.setVersion("1.0.0");
        consumerBean.setGroup("HSF");
        consumerBean.setClientTimeout(5000);
        consumerBean.setConfigserverCenter(Arrays.asList("pre"));
        consumerBean.setIncludeRouters(Arrays.asList("cluster-router"));
        consumerBean.setIncludeFilters(Arrays.asList("cluster-filter"));
        consumerBean.init(true);

        return (ProductOpenService)consumerBean.getObject();
    }


    @Bean(name="GlobalAddressValidateFacade")
    public GlobalAddressValidateFacade getAllFieldValidateConfigByFieldName() throws Exception{

        HSFApiConsumerBean consumerBean = new HSFApiConsumerBean();
        consumerBean.setInterfaceClass(GlobalAddressValidateFacade.class);
        consumerBean.setVersion("1.0.0.ae");
        consumerBean.setGroup("HSF");
        consumerBean.setClientTimeout(5000);
        consumerBean.setConfigserverCenter(Arrays.asList("rg-us-east-pre"));
        consumerBean.setIncludeRouters(Arrays.asList("cluster-router"));
        consumerBean.setIncludeFilters(Arrays.asList("cluster-filter"));
        consumerBean.init(true);

        return (GlobalAddressValidateFacade)consumerBean.getObject();
    }


    @Bean
    public AeReverseOrderQueryFacade aeReverseOrderQueryFacade() {
        return (AeReverseOrderQueryFacade) ServiceFactory.consumer().service(AeReverseOrderQueryFacade.class)
                .timeout(3000).group("HSF").version("1.0.0").generic().sync().subscribe();
    }

    @Bean
    public InternalToolFacade internalToolFacade() {
        return (InternalToolFacade) ServiceFactory.consumer().service(InternalToolFacade.class)
                .timeout(3000).group("HSF").version("1.0.0").generic().sync().subscribe();
    }

    @Bean
    public ReverseArbitrationFacade reverseArbitrationFacade() {
        return (ReverseArbitrationFacade) ServiceFactory.consumer().service(ReverseArbitrationFacade.class)
                .timeout(3000).group("HSF").version("1.0.0").generic().sync().subscribe();
    }

    @Bean
    public AeReverseOrderQueryForConsoleFacade aeReverseOrderQueryForConsoleFacade() {
        return (AeReverseOrderQueryForConsoleFacade) ServiceFactory.consumer().service(AeReverseOrderQueryForConsoleFacade.class)
                .timeout(3000).group("HSF").version("1.0.0").generic().sync().subscribe();
    }

    @Bean
    public AeAfterSaleStrategyFacade aeAfterSaleStrategyFacade() {
        return (AeAfterSaleStrategyFacade) ServiceFactory.consumer().service(AeAfterSaleStrategyFacade.class)
                .timeout(3000).group("HSF").version("1.0.0").generic().sync().subscribe();
    }

    @Bean
    public DisputeWriteService disputeWriteService() {
        return (DisputeWriteService) ServiceFactory.consumer().service(DisputeWriteService.class)
                .timeout(3000).group("HSF").version("1.0.0.icbu_xcommerce-i18n-ae").generic().sync().subscribe();
    }

   /* @Bean
    public IAtmosphereService iAtmosphereService() {
        return (IAtmosphereService) ServiceFactory.consumer().service(IAtmosphereService.class)
                .timeout(3000).group("HSF").version("1.0.0").generic().sync().subscribe();
    }*/

    @Bean
    public TimeoutCalculateFacade timeoutCalculateFacade() {
        return (TimeoutCalculateFacade) ServiceFactory.consumer().service(TimeoutCalculateFacade.class)
                .timeout(3000).group("HSF").version("1.0.0").generic().sync().subscribe();
    }

    @Bean
    public BreachContractArbitrationFacade breachContractArbitrationFacade() {
        return (BreachContractArbitrationFacade) ServiceFactory.consumer().service(BreachContractArbitrationFacade.class)
                .timeout(3000).group("HSF").version("1.0.0").generic().sync().subscribe();
    }


    @Bean
    public InternalToolsFacade internalToolsFacade() {
        return (InternalToolsFacade) ServiceFactory.consumer().service(InternalToolsFacade.class)
                .timeout(3000).group("HSF").version("1.0.0").generic().sync().subscribe();
    }
}
