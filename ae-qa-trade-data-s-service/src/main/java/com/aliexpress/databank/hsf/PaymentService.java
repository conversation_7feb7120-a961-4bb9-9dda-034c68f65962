package com.aliexpress.databank.hsf;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.global.qa.dc.model.param.SystemDTO;
import com.alibaba.global.qa.dc.model.result.QueryResultUnit;
import com.alibaba.global.qa.dc.model.result.ResultDTO;
import com.aliexpress.databank.dataobject.UniqueRes;

import java.util.List;
import java.util.Map;

public interface PaymentService {

    Map<String, QueryResultUnit> queryPayInstructionDist(String params) throws Exception;

    ResultDTO queryRefund(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO queryPaymentInfo(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO queryPaymentCommonExtInfo(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO queryPayInstructionRecods(String params, SystemDTO systemDTO) throws Exception;

    //根据订单id查checkoutid、instructionid
    ResultDTO queryPaymentRecords(String params, SystemDTO systemDTO) throws Exception;

    //汇率相关接口
    ResultDTO getCurrency(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO getExchangeRate(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO getExchangePrice(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO checkPaymentMethodData(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO queryRefundByBiz(String params, SystemDTO systemDTO) throws Exception;

    List<String> getBatchPayRealtionNoByBizOrderId(String orderId);

    ResultDTO getPayPromotionInfo(String params, SystemDTO systemDTO);

    ResultDTO checkOfflinePaymentMsgData(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO getAllCurs(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO refundBeforePayToIPay(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO refundBeforePayToIPay(Long tradeOrderId, String refundChannel);

    ResultDTO generateMessage(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO getRepayPaymentAndRefundInfo(String params, SystemDTO systemDTO) throws Exception;

    UniqueRes getCheckOutOrderNoByOrderId(String orderId);

    UniqueRes getPayInstructionNoByCheckoutOrderNo(String checkoutOrder);

    UniqueRes getPayInstructionNoByOrderId(String orderId);

    JSONObject getPayInstructionDistByOrderId(String orderId);

    JSONObject getPayInstructionByCheckoutOrderNo(String checkoutOrderNo);

    //mock支付成功
//	ResultDTO mockIPaySuccess(String params, SystemDTO systemDTO) throws Exception;

    //mock支付成功
    ResultDTO mockIPaySuccess(String params, SystemDTO systemDTO) throws Exception;

    //查询aepay路由失败原因
    ResultDTO queryAepayRouteFail(String trace, SystemDTO systemDTO);

    //卡在init状态退款的订单，原退款单重试
    ResultDTO refundRetry(String params, SystemDTO systemDTO);

    ResultDTO authCode(String params, SystemDTO systemDTO);

    //支付switch查询
    ResultDTO switchValueQuery(String params, SystemDTO systemDTO) throws Exception;

    //refund接口，直接请求ipay退款
    ResultDTO refund(String params, SystemDTO systemDTO) throws Exception;

    //使用 Bonus 支付订单
    ResultDTO bonusPay(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO bonusPay4Trade(String orderId, String payCurrency, String testUserId) throws Exception;

    //给 Saiga 链路脚本用的汇率调用
    ResultDTO getExchangePrice4LinkedCase(String params, SystemDTO systemDTO) throws Exception;

    //给 Saiga 链路脚本用的查询用户指定卡信息
    ResultDTO getCardInfo4LinkedCase(String params, SystemDTO systemDTO) throws Exception;

    String getSaigaReplayCompare(String params);

    ResultDTO bonusPayPrep4LinkedCase(Long cent, String payCurrency, String buyerId) throws Exception;
}

