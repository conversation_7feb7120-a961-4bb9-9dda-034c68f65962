package com.aliexpress.databank.hsf.provider;


import com.alibaba.boot.hsf.annotation.HSFProvider;

import com.alibaba.ecommerce.module.Response;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.global.landlord.LandlordContext;
import com.alibaba.global.order.management.api.model.*;
import com.alibaba.global.payment.api.request.*;
import com.alibaba.global.payment.api.vo.*;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.global.buy.api.model.source.PlatformType;
import com.alibaba.global.money.Money;
import com.alibaba.global.order.management.api.facade.OrderQueryForBuyerFacade;
import com.alibaba.global.order.management.api.request.QueryOrdersRequest;
import com.alibaba.global.order.management.constants.AttributeConstants;
import com.alibaba.global.payment.api.dto.Features;
import com.alibaba.global.payment.api.dto.env.CommonDTO;
import com.alibaba.global.payment.api.dto.env.HeaderDTO;
import com.alibaba.global.payment.api.dto.env.MobileDTO;
import com.alibaba.global.payment.api.dto.fx.FxRateDTO;
import com.alibaba.global.payment.api.dto.payment.ChosenIntentionDTO;
import com.alibaba.global.payment.api.dto.payment.MergeCashierPayRequest;
import com.alibaba.global.payment.api.facade.GlobalPaymentCashierMtopFacade;
import com.alibaba.global.payment.api.facade.GlobalPaymentCashierServiceFacade;

import com.alibaba.global.payment.api.request.mtop.AsyncPaymentCashierMtopRequest;
import com.alibaba.global.payment.api.response.MergePayResponse;

import com.alibaba.global.payment.api.response.RenderIntentionResponse;
import com.alibaba.global.payment.api.utils.CashierRequestTokenUtils;

import com.alibaba.global.qa.dc.model.builder.QueryResultBuilder;
import com.alibaba.global.qa.dc.model.param.SystemDTO;
import com.alibaba.global.qa.dc.model.result.QueryResultUnit;
import com.alibaba.global.qa.dc.model.result.ResultDTO;
import com.alibaba.ultron.utils.JacksonUtils;

import com.aliexpress.databank.hsf.PayTestService;
import com.aliexpress.databank.price.utils.MoneyUtils;
import com.aliexpress.databank.price.utils.PaymentToolConfig;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.taobao.eagleeye.EagleEye;
import com.taobao.mtop.common.Result;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.money.CurrencyUnit;
import javax.money.MonetaryAmount;

import java.util.*;


@HSFProvider(serviceInterface = PayTestService.class, serviceGroup = "HSF", serviceVersion = "1.0.0", clientTimeout = 20000)
public class PayTestImpl implements PayTestService {

    private static final Logger LOGGER = LoggerFactory.getLogger(PayTestImpl.class);



    @Autowired
    private OrderQueryForBuyerFacade orderQueryForBuyerFacade;
    @Autowired
    private GlobalPaymentCashierServiceFacade globalPaymentCashierServiceFacade;

    @Autowired
    private GlobalPaymentCashierMtopFacade globalPaymentCashierMtopFacade;


//    public MergePayResponse ApplyPay(Long buyerId,List<Long> orderIds) {
//        MergePayResponse mergePayResponse=new MergePayResponse();
//        QueryOrdersRequest queryOrdersRequest = new QueryOrdersRequest();
//        queryOrdersRequest.setBuyerId(buyerId);
//        queryOrdersRequest.setTradeOrderIds(orderIds);
//        List<TradeOrderDTO> tradeOrderDTOList = orderQueryForBuyerFacade.queryBatchTradeOrders(queryOrdersRequest).getModule();
//        if (CollectionUtil.isEmpty(tradeOrderDTOList)) {
//            mergePayResponse.setSucceeded(false);
//            mergePayResponse.setResponseMessage("orderQueryForBuyerFacade.queryBatchTradeOrders is null");
//            return mergePayResponse;
//        }
////        BatchCheckoutPayableRequest batchCheckoutPayableRequest=new BatchCheckoutPayableRequest();
////        batchCheckoutPayableRequest.setRouteId(tradeOrderDTO.getBuyer().getBuyerId());
////        List<CheckoutPayableRequest> checkoutPayableRequests =new ArrayList<CheckoutPayableRequest>();
////        CheckoutPayableRequest checkoutPayableRequest=new CheckoutPayableRequest();
////        checkoutPayableRequest.setCheckoutOrderNo(tradeOrderDTO.get);
////        batchCheckoutPayableRequest.se
////        batchCheckoutPayable
////        request:[{"attributes":{},"checkoutPayableRequests":[{"attributes":{},"checkoutOrderNo":"20990501103211122413095461299"}],"routeId":1861581299}]|
//        RenderIntentionRequest renderIntentionRequest=null;
//        try{
//         renderIntentionRequest = buildRenderIntentionRequest(tradeOrderDTOList);
//            LOGGER.warn("doNewApplyPaymentByAstore start trace={0}", EagleEye.getTraceId());
//            Result<?> result= doNewApplyPaymentByAstore(renderIntentionRequest);
//        }catch (Exception e){
//            mergePayResponse.setSucceeded(false);
//            mergePayResponse.setResponseMessage(" exception:"+e.getMessage());
//            return mergePayResponse;
//        }
//        mergePayResponse.setSucceeded(true);
//
//        return mergePayResponse;
//    }

    @Override
    public ResultDTO ApplyPaywithBalance(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();

        Map<String, QueryResultUnit> data = new LinkedHashMap<>();
        LandlordContext.setTenantSpec("AE_GLOBAL", -1);


        JSONObject jsonObject = JSONObject.parseObject(params);
        String orderId = jsonObject.getString("orderIdStr");
        Long buyerId = Long.valueOf(jsonObject.getString("buyerIdStr")).longValue();
        List<Long> orderIds = new ArrayList<Long>();
        orderIds.add(Long.parseLong(orderId));


        QueryOrdersRequest queryOrdersRequest = new QueryOrdersRequest();
        queryOrdersRequest.setBuyerId(buyerId);
        queryOrdersRequest.setTradeOrderIds(orderIds);
        List<TradeOrderDTO> tradeOrderDTOList = orderQueryForBuyerFacade.queryBatchTradeOrders(queryOrdersRequest).getModule();
        if (tradeOrderDTOList.isEmpty()) {
            result.setSuccess(false);
            result.setData("orderQueryForBuyerFacade.queryBatchTradeOrders is null");
            return result;
        } else {
            RenderIntentionRequest renderIntentionRequest = null;
            Result<?> payresult = null;
            try {
                renderIntentionRequest = buildRenderIntentionRequest(tradeOrderDTOList);
                LOGGER.warn("doNewApplyPaymentByAstore start trace={0}", EagleEye.getTraceId());
                payresult = doNewApplyPaymentByAstore(renderIntentionRequest);
            } catch (Exception e) {
                result.setSuccess(false);
                result.setData(e.getMessage());
                return result;
            }

            Map<String, QueryResultUnit> paymentResult = QueryResultBuilder.buildQueryResult("支付信息", null, null, payresult);
            data.putAll(paymentResult);

            result.setData(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
            result.setSuccess(true);


            return result;
        }
    }


    private RenderIntentionRequest buildRenderIntentionRequest(List<TradeOrderDTO> tradeOrderDTOList) throws Exception {
        if (tradeOrderDTOList.isEmpty()) {
            return null;
        }
        RenderIntentionRequest renderIntentionRequest = getBaseRenderIntentionRequest();
        ArrayList<TradeOrderDTO> arrayList = new ArrayList<>(tradeOrderDTOList);
        TradeOrderDTO oneTradeOrder = tradeOrderDTOList.get(0);


        String source = oneTradeOrder.getFeatures().getFeature("source");
        SourceDTO sourceDTO = null;
        if (source != null) {
            sourceDTO = JacksonUtils.deserialize(source, SourceDTO.class);
        }


        renderIntentionRequest.setRouteId(oneTradeOrder.getBuyer().getBuyerId());
        renderIntentionRequest.setCashierToken(CashierRequestTokenUtils.getCashierRequestToken(getMergeCashierPayRequest(arrayList)));
        renderIntentionRequest.setPayer(buildBuyer(oneTradeOrder.getBuyer()));
        renderIntentionRequest.setCheckoutRequests(buildCheckoutOrders(tradeOrderDTOList, sourceDTO));
        renderIntentionRequest.setChosenIntentionDTO(buildChosenIntentionDTO());
        renderIntentionRequest.setCommonDTO(buildCommonDTO(sourceDTO));


        //待商量
        renderIntentionRequest.setPayCurrency(oneTradeOrder.getActualFee().getCurrency().getCurrencyCode());
        if (sourceDTO != null) {

            renderIntentionRequest.setPlatform(com.alibaba.global.payment.api.constants.PlatformType.AE.name());
        }
        renderIntentionRequest.setRouteId(oneTradeOrder.getBuyer().getBuyerId());
        return renderIntentionRequest;

    }

    private Result<?> doNewApplyPaymentByAstore(RenderIntentionRequest confirmIntentionRequest) {
        RenderIntentionRequest renderRequest = new RenderIntentionRequest();

        renderRequest.setPayer(confirmIntentionRequest.getPayer());
        renderRequest.setPayCurrency(confirmIntentionRequest.getPayCurrency());
        renderRequest.setCommonDTO(confirmIntentionRequest.getCommonDTO());
        renderRequest.setChosenIntentionDTO(confirmIntentionRequest.getChosenIntentionDTO());
        renderRequest.setCheckoutRequests(confirmIntentionRequest.getCheckoutRequests());

        Money payFee = Money.of(0, confirmIntentionRequest.getCheckoutRequests().get(0).getOrderPayFee().getCurrencyCode());
        for (CheckoutRequest request : confirmIntentionRequest.getCheckoutRequests()) {
            if (request.getOrderPayFee() != null) {
                payFee = payFee.add(request.getOrderPayFee());
            }
        }
        renderRequest.setOrderPayAmt(payFee);

        Features features = new Features();
        features.addFeature("ORIGINAL_INTENTION_CCY", confirmIntentionRequest.getPayCurrency());
        renderRequest.setExtraFeatures(features);

        Response<RenderIntentionResponse> result = globalPaymentCashierServiceFacade.renderIntention(renderRequest);
        if (result.isNotSuccess()) {

            LOGGER.error("buildPromotionInfo errorCode={0}, traceId={1}", result.getErrorCode(), EagleEye.getTraceId());
            return null;
        }


        LOGGER.warn("doNewApplyPaymentByAstore asyncCashier request={0},result={1}, trace={2}", JSON.toJSONString(renderRequest), JSON.toJSONString(result.getModule()), EagleEye.getTraceId());

        AsyncPaymentCashierMtopRequest asyncPaymentCashierMtopRequest = new AsyncPaymentCashierMtopRequest();
        asyncPaymentCashierMtopRequest.setParams(buildParams(confirmIntentionRequest, result.getModule()));
        asyncPaymentCashierMtopRequest.setPayCurrency(confirmIntentionRequest.getPayCurrency());
        asyncPaymentCashierMtopRequest.setUserId(String.valueOf(confirmIntentionRequest.getRouteId()));
        asyncPaymentCashierMtopRequest.setUltronVersion("3.0");

        if (null != confirmIntentionRequest.getCommonDTO().getHeaderDTO()) {
            asyncPaymentCashierMtopRequest.setSid(confirmIntentionRequest.getCommonDTO().getHeaderDTO().getSid());
            asyncPaymentCashierMtopRequest.setUmidToken(confirmIntentionRequest.getCommonDTO().getHeaderDTO().getUmidToken());
            asyncPaymentCashierMtopRequest.setUmid(confirmIntentionRequest.getCommonDTO().getHeaderDTO().getUmid());
            asyncPaymentCashierMtopRequest.setAlipayToken(confirmIntentionRequest.getCommonDTO().getHeaderDTO().getAlipayUmidToken());
        }

        if (null != confirmIntentionRequest.getCommonDTO().getMobile()) {
            asyncPaymentCashierMtopRequest.setWua(confirmIntentionRequest.getCommonDTO().getMobile().getWua());
        }

        if (null != confirmIntentionRequest.getCommonDTO().getDeviceDTO()) {
            asyncPaymentCashierMtopRequest.setLanguage(confirmIntentionRequest.getCommonDTO().getDeviceDTO().getWebLanguage());
        }


        LOGGER.warn("doNewApplyPaymentByAstore asyncCashier request={0}, trace={1}", JSON.toJSONString(asyncPaymentCashierMtopRequest), EagleEye.getTraceId());

        Result<?> result1 = globalPaymentCashierMtopFacade.asyncCashier(asyncPaymentCashierMtopRequest);

        LOGGER.warn("doNewApplyPaymentByAstore asyncCashier result {0}, trace={1}", result1.getModel(), EagleEye.getTraceId());
        if (!result1.isSuccess()) {
            LOGGER.error("doNewApplyPaymentByAstore consumeMessage failed, errorCode={0}, traceId={1}", result1.getMsgCode(), EagleEye.getTraceId());
        }
        return result1;
    }

    private String buildParams(RenderIntentionRequest confirmIntentionRequest, RenderIntentionResponse result) {

        String component = result.getCashierResult().getCashierComponent();
        LOGGER.warn("doNewApplyPaymentByAstore buildPromotionInfo component={0}, traceId={1}", component, EagleEye.getTraceId());

        String cashierToken = confirmIntentionRequest.getCashierToken();

        String cashierComponentStr = result.getCashierResult().getCashierComponent();


        JSONObject cashierComponent = JSON.parseObject(cashierComponentStr);

        if (cashierComponentStr.contains("chosenChannel_86731")) {

            cashierComponent.put("operator", "chosenChannel_86731");
            String dataString = cashierComponent.getString("data");
            JSONObject data = JSON.parseObject(dataString);

            /** update chosenchannel组件 **/
            String chosenChannel_86731 = data.getString("chosenChannel_86731");
            JSONObject chosenChannel = JSON.parseObject(chosenChannel_86731);
            String fieldsStr = chosenChannel.getString("fields");
            JSONObject fields = JSON.parseObject(fieldsStr);
            fields.put("isSubmit", "true");
            fields.put("cashierOrderToken", cashierToken);
            chosenChannel.put("fields", fields);
            data.put("chosenChannel_86731", chosenChannel);

//
//            /** update addcard组件 **/
////            String addCard_87426_str = data.getString("addCard_87426");
////            JSONObject addCard = JSON.parseObject(addCard_87426_str);
////
////            String addcardFieldsStr = addCard.getString("fields");
////            JSONObject addcardFields = JSON.parseObject(addcardFieldsStr);
////
////
////            String cardTokenString = SimpleDiamondUtil.getDiamondStringValue(SimpleDiamondUtil.SimpleDiamondEnum.CARD_TOKEN, "ALIPAYv87UCQQGsck+efJiNQFmylLxxoCzYz5yNxGzHzipk1TJDFUCrNkUZ8oHpmgLIS741TWtIJoG8TjLoRw+1c1JA==");
////            String[] split = cardTokenString.split(SPLIT_SING);
////            String cardTempToken = split[new Random().nextInt(split.length)];
////
////            addcardFields.put("tempToken", cardTempToken);
////            addCard.put("fields", addcardFields);
////            data.put("addCard_87426", addCard);
//
//
//            /** update radiolist组件 **/
//            String radioList_86436_str = data.getString("radioList_86436");
//            JSONObject radioList = JSON.parseObject(radioList_86436_str);
//            String radioListFieldsStr = radioList.getString("fields");
//
//            JSONObject radioListFields = JSON.parseObject(radioListFieldsStr);
//
//            JSONObject paymentData = new JSONObject();
//            paymentData.put("saveCard", "true");
//            paymentData.put("tempToken", cardTempToken);
//            paymentData.put("cardBrand", PaymentStressTestSwitch.CARD_BRAND);
//            paymentData.put("prefixIndex", PaymentStressTestSwitch.cardBin);
//
//            radioListFields.put("paymentData", paymentData);
//            radioList.put("fields", radioListFields);
//            data.put("radioList_86436", radioList);
//
            cashierComponent.put("data", data);
//
        } else {
            cashierComponent.put("operator", "chosenChannel_104450");

            String dataString = cashierComponent.getString("data");
            JSONObject data = JSON.parseObject(dataString);

            String chosenChannel_104450 = data.getString("chosenChannel_104450");
            JSONObject chosenChannel = JSON.parseObject(chosenChannel_104450);
            String fieldsStr = chosenChannel.getString("fields");
            JSONObject fields = JSON.parseObject(fieldsStr);
            fields.put("isSubmit", "true");
            fields.put("cashierOrderToken", cashierToken);
            chosenChannel.put("fields", fields);
            data.put("chosenChannel_104450", chosenChannel);


            /** update addcard组件 **/
//            String addCard_104443_str = data.getString("addCard_104443");
//            JSONObject addCard = JSON.parseObject(addCard_104443_str);
//
//            String addcardFieldsStr = addCard.getString("fields");
//            JSONObject addcardFields = JSON.parseObject(addcardFieldsStr);
//
//            String cardTokenString = SimpleDiamondUtil.getDiamondStringValue(SimpleDiamondUtil.SimpleDiamondEnum.CARD_TOKEN, "ALIPAYv87UCQQGsck+efJiNQFmylLxxoCzYz5yNxGzHzipk1TJDFUCrNkUZ8oHpmgLIS741TWtIJoG8TjLoRw+1c1JA==");
//            String[] split = cardTokenString.split(SPLIT_SING);
//            addcardFields.put("tempToken", split[new Random().nextInt(split.length)]);
//            addCard.put("fields", addcardFields);
//            data.put("addCard_104443", addCard);
//
            cashierComponent.put("data", data);
        }

        return JSON.toJSONString(cashierComponent);
    }

    private ChosenIntentionDTO buildChosenIntentionDTO() {
        //---------------选中balance---------------
        ChosenIntentionDTO chosenIntentionDTO = new ChosenIntentionDTO();
        chosenIntentionDTO.setIntentionLineId(PaymentToolConfig.INTENTION_LINE_ID);
        chosenIntentionDTO.getParams().put("selectListId", PaymentToolConfig.SELECT_LIST_ID);
//        chosenIntentionDTO.getParams().put("cardBrand", PaymentStressTestSwitch.CARD_BRAND);
//        chosenIntentionDTO.getParams().put("cardBin", PaymentStressTestSwitch.cardBin);
//        chosenIntentionDTO.getParams().put("cardBinCountry", PaymentStressTestSwitch.cardBinCountry);
//        chosenIntentionDTO.getParams().put("payChannelEchoExtAttribute", PaymentStressTestSwitch.PAY_CHANNEL_ECHO_EXT_ATTRIBUTE);
//
//        String cardTokenString = SimpleDiamondUtil.getDiamondStringValue(SimpleDiamondUtil.SimpleDiamondEnum.CARD_TOKEN, "ALIPAYv87UCQQGsck+efJiNQFmylLxxoCzYz5yNxGzHzipk1TJDFUCrNkUZ8oHpmgLIS741TWtIJoG8TjLoRw+1c1JA==");
//        String[] split = cardTokenString.split(SPLIT_SING);
//        chosenIntentionDTO.getParams().put("token", split[new Random().nextInt(split.length)]);
        return chosenIntentionDTO;
    }

    /**
     * @param tradeOrders list of tradeOrder
     * @return list of checkoutRequest
     */
    private static List<CheckoutRequest> buildCheckoutOrders(List<TradeOrderDTO> tradeOrders, SourceDTO sourceDTO) {
        List<CheckoutRequest> result = Lists.newArrayList();
        tradeOrders.forEach(
                order -> {
                    CheckoutRequest checkoutRequest = buildCheckoutOrder(order, sourceDTO);
                    if (checkoutRequest != null) {
                        result.add(checkoutRequest);
                    }
                }
        );
        return result;
    }

    private static CheckoutRequest buildCheckoutOrder(TradeOrderDTO order, SourceDTO sourceDTO) {
        CheckoutRequest checkoutRequest = new CheckoutRequest();
        checkoutRequest.setBizOrderNo(String.valueOf(order.getTradeOrderId()));
        MonetaryAmount orderAmount = order.getActualFee();
        CurrencyUnit currencyUnit = orderAmount.getCurrency();
        checkoutRequest.setOrderAmount(Money.of(orderAmount));
        checkoutRequest.setPayerId(order.getBuyer().getBuyerId());
        checkoutRequest.setPayerName(order.getBuyer().getBuyerFullName());
        checkoutRequest.setPayeeId(order.getOrderLines().get(0).getSeller().getSellerId());
        checkoutRequest.setPayeeName(order.getOrderLines().get(0).getSeller().getSellerFullName());
        TradeOrderInfo tradeOrderInfo = new TradeOrderInfo();
        tradeOrderInfo.setOrderId(String.valueOf(order.getTradeOrderId()));
        tradeOrderInfo.setTotalAmount(Money.of(orderAmount));
        tradeOrderInfo.setOrderEnvInfo(buildTradeEnvInfo(sourceDTO));

        UserInfo buyer = new UserInfo();
        buyer.setAliId(order.getBuyer().getBuyerId());
        buyer.setPhoneNumber(order.getBuyer().getBuyerPhonePrefixCode());
        buyer.setEmail(order.getBuyer().getBuyerEmail());
        buyer.setUserName(order.getBuyer().getBuyerFullName());
        tradeOrderInfo.setBuyer(buyer);

        UserInfo seller = new UserInfo();
        seller.setAliId(order.getOrderLines().get(0).getSeller().getSellerId());
        seller.setPhoneNumber(order.getOrderLines().get(0).getSeller().getSellerPhone());
        seller.setEmail(order.getOrderLines().get(0).getSeller().getSellerEmail());
        seller.setUserName(order.getOrderLines().get(0).getSeller().getSellerFullName());
        tradeOrderInfo.setSeller(seller);
        tradeOrderInfo.setSubTradeOrderInfos(buildCheckoutDetails(order.getOrderLines(), order, tradeOrderInfo));
        MonetaryAmount shippingFee = order.getShippingActualFee();
        if (shippingFee == null) {
            shippingFee = Money.zero(currencyUnit);
        }
        tradeOrderInfo.getLogisticInfo().setLogisticAmount(
                Money.of(shippingFee));

        Map<String, String> extMap = Maps.newHashMap();
        extMap.put("ACTIVITY_TIME_OUT", String.valueOf(2524579200000L));
        tradeOrderInfo.setExtAttrMap(extMap);
        checkoutRequest.setTradeOrderInfo(tradeOrderInfo);
        checkoutRequest.setFxRate(buildFxRate(order));
        checkoutRequest.setOrderAmount(Money.of(order.getOrderAmount()));
        MonetaryAmount payFee = order.getActualFee();
        checkoutRequest.setOrderPayFee(Money.of(payFee));


        //计算优惠费用
        if (!checkoutRequest.getOrderPayFee().equals(checkoutRequest.getOrderAmount())) {
            if (checkoutRequest.getOrderAmount().getCurrencyCode().equals(checkoutRequest.getOrderPayFee().getCurrencyCode())) {
                checkoutRequest.setOrderPromotionFee(checkoutRequest.getOrderAmount().subtract(checkoutRequest.getOrderPayFee()));
            } else {
                Money result = MoneyUtils.getPriceByMultiply(checkoutRequest.getOrderAmount(),
                        checkoutRequest.getOrderPayFee().getCurrencyCode(),
                        checkoutRequest.getFxRate().getFxRate());
                checkoutRequest.setOrderPromotionFee(result);
            }
        }
        return checkoutRequest;
    }

    private static List<SubTradeOrderInfo> buildCheckoutDetails(List<TradeOrderLineDTO> orderLines, TradeOrderDTO order,
                                                                TradeOrderInfo tradeOrderInfo) {
        List<SubTradeOrderInfo> result = Lists.newArrayList();
        for (TradeOrderLineDTO orderLine : orderLines) {
            SubTradeOrderInfo checkoutOrderLine = new SubTradeOrderInfo();
            checkoutOrderLine.setBizCode(orderLine.getBizCode());
            ArrayList<String> arrayList = new ArrayList<>();
            arrayList.add("com.alibaba.business.product.cod.CODProduct");
            checkoutOrderLine.setProductCodeList(arrayList);
            checkoutOrderLine.setMainOrderNo(String.valueOf(order.getTradeOrderId()));
            //待确定
            checkoutOrderLine.setSubOrderNo(String.valueOf(orderLine.getTradeOrderLineId()));
            MonetaryAmount subOrderAmount = orderLine.getActualFee();
            checkoutOrderLine.setAmount(Money.of(subOrderAmount));
            ItemInfo itemInfo = new ItemInfo();
            itemInfo.setItemId(String.valueOf(orderLine.getProduct().getItemId()));
            itemInfo.setItemName(orderLine.getProduct().getItemTitle());
            String categoryId = orderLine.getProduct().getCategoryId();
            if (categoryId != null) {
                itemInfo.setCategoryId(categoryId);
            }
            itemInfo.setSku(String.valueOf(orderLine.getProduct().getSku().getSkuId()));
            MonetaryAmount itemUnitPrice = orderLine.getUnitFee();
            itemInfo.setUnitAmount(
                    Money.of(itemUnitPrice));
            itemInfo.setNum(orderLine.getQuantity().intValue());

            checkoutOrderLine.setItemInfos(Lists.newArrayList(itemInfo));
            checkoutOrderLine.setSeller(tradeOrderInfo.getSeller());

            LogisticsInfo logisticInfo = new LogisticsInfo();
            //待确定
            TransportMethodDTO chosenTransportMethod = orderLine.getLogisticsOrderDTO().getTransportMethodDTO();
            if (chosenTransportMethod != null) {
                logisticInfo.setLogisticType(chosenTransportMethod.getDeliveryType());
            }
            ReceiveUserInfoDTO receiver = order.getDeliveryAddress().getReceiver();
            if (receiver != null) {
                logisticInfo.setAddress1(order.getDeliveryAddress().getDetailAddress());
                logisticInfo.setAddress2(order.getDeliveryAddress().getAddress2());
                logisticInfo.setCountry(order.getDeliveryAddress().getCountryCode());
                logisticInfo.setCity(order.getDeliveryAddress().getCity());
                logisticInfo.setAreaName(order.getDeliveryAddress().getThirdLevelAddressName());
                logisticInfo.setZipCode(order.getDeliveryAddress().getPostCode());
                logisticInfo.setPhoneNo(receiver.getPhone());
                logisticInfo.setPhoneArea(receiver.getPhoneArea());
                logisticInfo.setPhoneCountryCode(receiver.getPhoneCountry());
                logisticInfo.setMobileNo(receiver.getMobileNo());
                logisticInfo.setMobileCountryNo(receiver.getPhoneCountry());
                logisticInfo.setLastName(receiver.getFullName());
                MonetaryAmount shippingFee = orderLine.getShippingActualFee();
                logisticInfo.setLogisticAmount(Money.of(shippingFee));
                logisticInfo.setState(order.getDeliveryAddress().getState());

            }
            checkoutOrderLine.setLogisticInfo(logisticInfo);
            tradeOrderInfo.setLogisticInfo(logisticInfo);
            result.add(checkoutOrderLine);
        }
        return result;
    }

    public static String buildTradeEnvInfo(SourceDTO source) {
        if (source == null) {
            return null;
        }
        JSONObject tradeEnvInfo = new JSONObject();
        if (source.getSourceSystem() != null) {
            tradeEnvInfo.put("os", source.getSourceSystem().getOs());
            JSONObject extendInfo = new JSONObject();
            extendInfo.put("ttid", source.getSourceSystem().getTtid());
            tradeEnvInfo.put("extendInfo", extendInfo);
        }

        if (source.getSourceDevice() != null) {
            tradeEnvInfo.put("device", source.getSourceDevice().getId());
        }
        return tradeEnvInfo.toJSONString();
    }

    protected static FxRateDTO buildFxRate(TradeOrderDTO order) {
        FxRateDTO rateDTO = new FxRateDTO();
        rateDTO.setId(order.getOrderLines().get(0).getFeatures().getFeature(AttributeConstants.ATTR_EXCHANGE_RATE_ID));
        rateDTO.setOutId(order.getOrderLines().get(0).getFeatures().getFeature(AttributeConstants.ATTR_EXCHANGE_RATE_OUT_ID));
        ExchangeInfoDTO exchangeInfo = order.getOrderLines().get(0).getExchangeInfo();
        rateDTO.setFxRate(exchangeInfo.getExchangeRate());
        rateDTO.setBaseCcy(exchangeInfo.getBaseCurrency());
        rateDTO.setQuoteCcy(exchangeInfo.getQuoteCurrency());
        return rateDTO;
    }

    public static List<MergeCashierPayRequest> getMergeCashierPayRequest(List<TradeOrderDTO> tradeOrders) {
        if (tradeOrders.isEmpty()) {
            return null;
        } else {
            List<MergeCashierPayRequest> mergeCashierPayRequests = Lists.newArrayList();
            tradeOrders.forEach((tradeOrderDTO) -> {
                MergeCashierPayRequest mergeCashierPayRequest = new MergeCashierPayRequest();
                mergeCashierPayRequest.setOrderId(tradeOrderDTO.getPayOrderDTO().getPaymentCheckoutId());
                mergeCashierPayRequests.add(mergeCashierPayRequest);
            });
            return mergeCashierPayRequests;
        }
    }

    private static UserInfo enrichUserInfo(BuyerInfoDTO buyer, UserInfo userInfo) {
        if (buyer != null) {
            userInfo.setAliId(buyer.getBuyerId());
            userInfo.setPhoneNumber(buyer.getBuyerPhone());
            userInfo.setPhoneNoCountryCode(buyer.getBuyerPhonePrefixCode());
            userInfo.setEmail(buyer.getBuyerEmail());
            userInfo.setUserName(buyer.getBuyerFullName());
        }

        return userInfo;
    }

    protected static UserInfo buildBuyer(BuyerInfoDTO buyer) {
        UserInfo buyerInfo = new UserInfo();
        enrichUserInfo(buyer, buyerInfo);
        return buyerInfo;
    }


    private RenderIntentionRequest getBaseRenderIntentionRequest() {
        RenderIntentionRequest renderIntentionRequest = new RenderIntentionRequest();

        //线上看到为null
        renderIntentionRequest.setDisableReasonCode(null);
        // 可以为null
        renderIntentionRequest.setEchoPaymentExtAttribute(null);
        //线上看到为null
        renderIntentionRequest.setExtraFeatures(null);
        //线上看到为null
        renderIntentionRequest.setOrderPayAmt(null);
        //线上看到为null
        renderIntentionRequest.setPayable(null);
        //线上看到为null
        renderIntentionRequest.setSource(null);
        return renderIntentionRequest;
    }

    private CommonDTO buildCommonDTO(SourceDTO sourceDTO) {
        CommonDTO commonDTO = new CommonDTO();
        if (null != sourceDTO) {
            commonDTO.setHeaderDTO(buildHeader(sourceDTO));
            commonDTO.setMobile(buildMobile(sourceDTO));
            commonDTO.setSource(buildSource(sourceDTO));
        }
        return commonDTO;
    }

    private static MobileDTO buildMobile(SourceDTO sourceDTO) {
        MobileDTO mobileDTO = new MobileDTO();
        if ("WIRELESS".equals(sourceDTO.getExtraParams().get("source"))) {
            mobileDTO.setAppKey("13022");
            mobileDTO.setAppVersion("8.8.0");
            return mobileDTO;
        }

        return null;
    }

    private static HeaderDTO buildHeader(SourceDTO sourceDTO) {
        HeaderDTO headerDTO = new HeaderDTO();
        headerDTO.setSid(sourceDTO.getExtraParams().get("sessionId"));
        headerDTO.setCid(sourceDTO.getExtraParams().get("cookieId"));
        headerDTO.setUmidToken(sourceDTO.getExtraParams().get("umid"));
        headerDTO.setIp(sourceDTO.getExtraParams().get("ip"));
        headerDTO.setAlipayUmidToken("7boA4ZxLPEUvfwF9VWXEloKMLA2phsGg");
        return headerDTO;
    }

    private com.alibaba.global.payment.api.dto.env.SourceDTO buildSource(SourceDTO sourceDTO) {
        com.alibaba.global.payment.api.dto.env.SourceDTO sourceDTO1 = new com.alibaba.global.payment.api.dto.env.SourceDTO();
        if (null != sourceDTO.getExtraParams().get("source")) {
            if ("PC".equals(sourceDTO.getExtraParams().get("source"))) {
                sourceDTO1.setPlatformType(PlatformType.PC.getPlatformType());

            }
            if ("WIRELESS".equals(sourceDTO.getExtraParams().get("source"))) {
                sourceDTO1.setPlatformType(com.alibaba.global.buy.api.model.source.PlatformType.WIRELESS.getPlatformType());
            }
            if ("H5".equals(sourceDTO.getExtraParams().get("source"))) {
                sourceDTO1.setPlatformType(com.alibaba.global.buy.api.model.source.PlatformType.H5.getPlatformType());
            }

        }
        return sourceDTO1;
    }

}



