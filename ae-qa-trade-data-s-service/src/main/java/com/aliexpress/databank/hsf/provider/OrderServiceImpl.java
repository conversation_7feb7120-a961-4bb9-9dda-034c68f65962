package com.aliexpress.databank.hsf.provider;

import com.alibaba.ae.qa.api.FulfillmentQueryTestFacade;
import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.common.lang.StringUtil;
import com.alibaba.ecommerce.module.Response;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.global.buy.api.response.CreateOrderResult;
import com.alibaba.global.carts.api.facade.CartFacade;
import com.alibaba.global.carts.api.request.CartAddRequest;
import com.alibaba.global.ic.dto.scenario.query.ProductQueryRequest;
import com.alibaba.global.ic.dto.scenario.query.SingleProductQueryCondition;
import com.alibaba.global.landlord.LandlordContext;
import com.alibaba.global.money.Money;
import com.alibaba.global.order.management.api.facade.OrderManagementRegionFacade;
import com.alibaba.global.order.management.api.facade.OrderQueryForBuyerFacade;
import com.alibaba.global.order.management.api.facade.OrderViewFacade;
import com.alibaba.global.order.management.api.model.*;
import com.alibaba.global.order.management.api.request.*;
import com.alibaba.global.qa.dc.model.builder.QueryResultBuilder;
import com.alibaba.global.qa.dc.model.param.SystemDTO;
import com.alibaba.global.qa.dc.model.result.QueryResultUnit;
import com.alibaba.global.qa.dc.model.result.ResultDTO;
import com.alibaba.global.reverse.api.model.ReverseOrderLineDTO;
import com.alibaba.global.uop.api.FulfillmentOrderQueryNoRoutingFacade;
import com.alibaba.global.uop.api.FulfillmentOrderStatusUpdateFacade;
import com.alibaba.global.uop.api.request.FulfillmentOrderStatusUpdateRequest;
import com.alibaba.global.uop.api.response.FulfillmentOrderDTO;
import com.alibaba.global.uop.api.response.FulfillmentOrderStatusUpdateResponseDTO;
import com.alibaba.global.uop.api.response.FulfillmentOrderWithPkgDTO;
import com.alibaba.rocketmq.client.producer.SendResult;
import com.aliexpress.databank.config.MqConfig;
import com.aliexpress.databank.constant.Constant;
import com.aliexpress.databank.constant.OrderFeatures;
import com.aliexpress.databank.dataobject.GetPlaceOrderDTO;
import com.aliexpress.databank.dataobject.TradeVO;
import com.aliexpress.databank.hsf.CartService;
import com.aliexpress.databank.hsf.OrderService;
import com.aliexpress.databank.hsf.TimeoutService;
import com.aliexpress.databank.service.ReverseService;
import com.aliexpress.databank.service.TradeService;
import com.aliexpress.databank.service.impl.NegotiationPlaceOrder;
import com.aliexpress.databank.utils.ConvertParam;
import com.aliexpress.databank.utils.HsfUtil;
import com.aliexpress.issue.api.AeIssueCancelOrderWriteFacade;
import com.aliexpress.issue.common.result.PlainResult;
import com.aliexpress.issue.dispute.pojo.common.CancelOrderOperatorRequest;
import com.aliexpress.issue.dispute.pojo.common.IssueCancelOrderOperatorResult;
import com.aliexpress.issue.dispute.pojo.common.OrderLineParam;
import com.aliexpress.qa.platform.api.util.MeasureLogger;
import com.ascp.uop.kernel.common.model.InvokeInfoDTO;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Maps;
import com.taobao.eagleeye.EagleEye;
import com.taobao.hsf.app.api.util.HSFApiConsumerBean;
import com.taobao.hsf.remoting.service.GenericService;
import com.taobao.mtop.common.Result;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;


import javax.money.MonetaryAmount;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Slf4j
@HSFProvider(serviceInterface = OrderService.class, serviceGroup = "HSF", serviceVersion = "1.0.0", clientTimeout = 20000)
public class OrderServiceImpl implements OrderService {

    @Autowired
    private TimeoutService timeoutService;

    @Autowired
    private AeIssueCancelOrderWriteFacade cancelOrderWriteFacade;

    @Autowired
    private OrderQueryForBuyerFacade orderQueryForBuyerFacade;

    @Autowired
    private OrderManagementRegionFacade orderManagementRegionFacade;

    @Autowired
    private FulfillmentOrderQueryNoRoutingFacade fulfillmentOrderQueryNoRoutingFacade;

    @Autowired
    private FulfillmentOrderStatusUpdateFacade fulfillmentOrderStatusUpdateFacade;

    @Autowired
    private TradeService tradeService;

    @Autowired
    private ReverseService reverseService;

    @Autowired
    private CartService cartService;

    @Autowired
    private MqConfig mqConfig;

    @Autowired
    private OrderViewFacade orderViewFacade;

    @Autowired
    private FulfillmentQueryTestFacade fulfillmentQueryTestFacade;

    @Autowired
    private NegotiationPlaceOrder placeOrderStrategy;

    @Autowired
    private CartFacade cartFacade;
    private String params;
    private SystemDTO systemDTO;
    @Override
    public ResultDTO getOrderByOrderIdAndBuyerId(String params, SystemDTO systemDTO) throws Exception {
        MeasureLogger measureLogger = MeasureLogger.start("ACCESS_LOG", "PVUV", MeasureLogger.Level.INFO);
        measureLogger.setInvokeStartTime(System.currentTimeMillis());
        ResultDTO result = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long orderId = jsonObject.getLong(Constant.ORDER_ID);
        String staging = jsonObject.getString(Constant.STAGING);
        //查询区域化接口
        LandlordContext.setTenantSpec("AE_GLOBAL", -1);
        if (StringUtil.isNotBlank(staging)) {
            EagleEye.putUserData("scm_project", staging);//临时处理

        }

        Response<TradeOrderDTO> tradeOrderDTOResponse = orderQueryForBuyerFacade.queryTradeOrderById(jsonObject.getLong(Constant.BUYER_ID), jsonObject.getLong(Constant.ORDER_ID));

        if (tradeOrderDTOResponse.isNotSuccess()) {
            log.error("queryTradeOrderById() query failed");
            result.setSuccess(false);
            result.setMessage("queryTradeOrderById() query failed");
            return result;
        }

        String s = JSON.toJSONString(tradeOrderDTOResponse.getModule().getOrderLines(), SerializerFeature.DisableCircularReferenceDetect);
        List<TradeOrderLineDTO> tradeOrderLines = JSON.parseArray(s, TradeOrderLineDTO.class);


        Map<String, QueryResultUnit> data = new LinkedHashMap<>();
        Map<String, QueryResultUnit> order = QueryResultBuilder.buildQueryResult("主订单详情", null, null, tradeOrderDTOResponse.getModule());
        Map<String, QueryResultUnit> orderLine = QueryResultBuilder.buildQueryResult("子订单详情", null, null, tradeOrderLines);
        data.putAll(order);
        data.putAll(orderLine);

        // 主单feature
        Map<String, String> featureMapForOrder = tradeOrderDTOResponse.getModule().getFeatures().getFeatureMap();
        JSONObject res = new JSONObject();


        List<Map<String, String>> orderLineFeatures = new ArrayList<>();
        for (TradeOrderLineDTO tradeOrderLineDTO : tradeOrderLines) {
            Map<String, String> orderLineFeature = tradeOrderLineDTO.getFeatures().getFeatureMap();
            orderLineFeatures.add(orderLineFeature);
            String idStr = tradeOrderLineDTO.getTradeOrderLineId().toString();
            String substring = idStr.substring(idStr.length() - 5);
            res.put("子单" + substring + "-json", mapToJson(orderLineFeature).toJSONString());
        }
        // 直接展示feature的json格式，避免测试平台的自动缩起，查不到值；
        // 后放的先展示
        res.put("主单-json", mapToJson(featureMapForOrder).toJSONString());
        // ===新增主子单feature
        Map<String, QueryResultUnit> jsonFeature = QueryResultBuilder.buildQueryResult("主子单feature-json版", null, null, res);
        data.putAll(jsonFeature);

        Map<String, QueryResultUnit> orderLineFeature = QueryResultBuilder.buildQueryResult("子订单feature", null, null, orderLineFeatures);
        data.putAll(orderLineFeature);


        try {
            // 支付超时
            Map<String, QueryResultUnit> payTimeout = timeoutService.getAlarmTimeoutVo(Constant.PAY_TIMEOUT_TYPE, orderId.toString());
            // 发货超时
            Map<String, QueryResultUnit> shipTimeout = timeoutService.getAlarmTimeoutVo(Constant.SHIP_TIMEOUT_TYPE, orderId.toString());
            // 确认收货超时
            Map<String, QueryResultUnit> succeedTimeout = timeoutService.getAlarmTimeoutVo(Constant.SUCCEED_TIMEOUT_TYPE, orderId.toString());
//            // 发货超时提醒 - 3天
//            Map<String, QueryResultUnit> reminderShip3DaysTimeout = timeoutService.getAlarmTimeoutVo(Constant.SHIP_REMINDER_TIMEOUT_TYPE, orderId.toString().trim() + "_259200");
//            // 发货超时提醒 - 1天
//            Map<String, QueryResultUnit> reminderShip1DayTimeout = timeoutService.getAlarmTimeoutVo(Constant.SHIP_REMINDER_TIMEOUT_TYPE, orderId.toString().trim() + "_86400");
            // 卖家同意/拒绝取消订单超时
            Map<String, QueryResultUnit> reminderSellerCancelOrderTimeout = timeoutService.getAlarmTimeoutVo(Constant.SELLER_CANCEL_ORDER_TIMEOUT_TYPE, orderId.toString().trim());

            data.putAll(payTimeout);
            data.putAll(shipTimeout);
            data.putAll(succeedTimeout);
//            data.putAll(reminderShip3DaysTimeout);
//            data.putAll(reminderShip1DayTimeout);
            data.putAll(reminderSellerCancelOrderTimeout);
        } catch (Exception e) {
            log.error("getOrderByOrderIdAndBuyerId(): fail to get timeout info.");
            result.setMessage(e.getMessage());
            result.setSuccess(false);
            return result;

        }
        result.setData(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
        result.setSuccess(true);
        measureLogger.setSuccess(true).setInvokeEndTime(System.currentTimeMillis()).setEmpId(systemDTO.getOperator()).setContent("/trade/jobId=" + systemDTO.getSite()).end();
        return result;
    }

    @Override
    public ResultDTO formatOrderFeature(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long orderId = jsonObject.getLong(Constant.ORDER_ID);
        String str = jsonObject.getString(Constant.FEATURE);
        JSONObject json = new JSONObject();
        json = mapToJson(toMap(str));
        Map<String, QueryResultUnit> data = new LinkedHashMap<>();
        JSONObject res = new JSONObject();
        // 迫不得已写另一个value存放，不然平台展示不了json；
        res.put("value", json.toJSONString());
//        result.setData(StringEscapeUtils.unescapeJson(json.toJSONString()));
//        result.setMessage(json.toJSONString());
        Map<String, QueryResultUnit> jsonFeature = QueryResultBuilder.buildQueryResult("json", null, null, res);
        Map<String, QueryResultUnit> orderLineFeature = QueryResultBuilder.buildQueryResult("feature", null, null, json);
        data.putAll(jsonFeature);
        data.putAll(orderLineFeature);
        result.setData(JSONObject.toJSONString(data, SerializerFeature.PrettyFormat));
        result.setSuccess(true);
        return result;
    }

    @Override
    public ResultDTO cancelOrder(Long buyerId, String refundChannel, Long orderId) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        CancelOrderOperatorRequest cancelOrderOperatorRequest = reverseService.getCancelOrderRequest(buyerId, orderId, "buyerCancel", null, refundChannel);
        LandlordContext.setTenantSpec("AE_GLOBAL", -1);
        PlainResult<IssueCancelOrderOperatorResult> response = new PlainResult<>();
        response = cancelOrderWriteFacade.openCancelOrderIssue(buyerId, null, cancelOrderOperatorRequest);
        if (!response.isSuccess()) {
            if (response.getErrorMessage().contains("CAN-CANCEL-APPLY-VALIDATION-HAVE-REVERSE-ORDER-IN-PROCESSING-ERROR")) {
            } else {
                resultDTO.setErrorCode("取消订单失败:" + JSON.toJSONString(response));
                resultDTO.setSuccess(false);
                return resultDTO;
            }
        }
        Response<List<ReverseOrderLineDTO>> reverseOrderLinesResponse = reverseService.getBuyerReverseOrder(buyerId, orderId);
        if (reverseOrderLinesResponse.isNotSuccess() || reverseOrderLinesResponse.getModule() == null || reverseOrderLinesResponse.getModule().size() == 0) {
            resultDTO.setErrorCode("查询取消订单理由失败:" + JSON.toJSONString(reverseOrderLinesResponse));
            resultDTO.setSuccess(false);
            return resultDTO;
        }
        String applyReason = reverseOrderLinesResponse.getModule().get(0).getApplyReason().getText();
        Long sellerId = reverseOrderLinesResponse.getModule().get(0).getSeller().getUserId();
        CancelOrderOperatorRequest sellerCancelOrderOperatorRequest = ConvertParam.getCancelOrderOperatorRequestBySellerSide(sellerId, orderId, applyReason, null);
        response = cancelOrderWriteFacade.approveCancelOrderIssue(buyerId, "", sellerCancelOrderOperatorRequest);
        if (!response.isSuccess()) {
            resultDTO.setErrorCode("同意退款失败:" + JSON.toJSONString(response));
            resultDTO.setSuccess(false);
        } else {
            resultDTO.setMessage("取消订单->同意退款成功");
            resultDTO.setSuccess(true);
        }
        return resultDTO;
    }

    @Override
    public ResultDTO cancelOrder(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long orderId = jsonObject.getLong(Constant.ORDER_ID);
        String cancelEvent = jsonObject.getString(Constant.CANCEL_EVENT);
        String dpath = jsonObject.getString(Constant.DPATH_ENV);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        Integer unit = jsonObject.getInteger(Constant.UNIT);
        String refundChannel = jsonObject.getString(Constant.REFUND_CHANNEL).equals("原路退") ? "" : jsonObject.getString("refundChannel");
        JSONObject tradeOrderResponse = jsonObject.getJSONObject(Constant.ORDER_INFO).getJSONObject("module");
        List<TradeOrderLineDTO> tradeOrderLineDTOs = tradeOrderResponse.getJSONArray("orderLines").toJavaList(TradeOrderLineDTO.class);
        CancelOrderOperatorRequest cancelOrderOperatorRequest = reverseService.getCancelOrderRequest(buyerId, orderId, cancelEvent, tradeOrderLineDTOs, refundChannel);
        String needCancelOrderLineData = jsonObject.getString(Constant.NEED_CANCEL_ORDER_LINE_DATA);
        List<OrderLineParam> cancelOrderDataDTOS = new ArrayList<>();
        if (StringUtil.isNotEmpty(needCancelOrderLineData)) {
            String[] orderLines = needCancelOrderLineData.split(",");
            for (String orderLineId : orderLines) {
                OrderLineParam cancelOrderDataDTO = new OrderLineParam();
                cancelOrderDataDTO.setOrderLineId(Long.parseLong(orderLineId));
                if (unit != null) {
                    cancelOrderDataDTO.setUnit(unit);
                } else {
                    cancelOrderDataDTO.setUnit(1);
                }
                cancelOrderDataDTOS.add(cancelOrderDataDTO);
            }
        }
        // 取消订单子单列表
        if (CollectionUtils.isNotEmpty(cancelOrderDataDTOS)) {
            //cancelOrderOperatorRequest.setNeedCancelOrderLineData(cancelOrderData);
            cancelOrderOperatorRequest.setNeedCanceledOrderLines(cancelOrderDataDTOS);
        }

        LandlordContext.setTenantSpec("AE_GLOBAL", -1);
        if (StringUtil.isNotBlank(dpath)) {
            EagleEye.putUserData("dpath_env", dpath);
        }
        PlainResult<IssueCancelOrderOperatorResult> cancelOrderResponse = cancelOrderWriteFacade.openCancelOrderIssue(buyerId, null, cancelOrderOperatorRequest);
        resultDTO.setSuccess(true);
        if (cancelOrderResponse.getData() != null && cancelOrderResponse.isSuccess()) {
            resultDTO.setData("发起取消订单成功。 Res: " + JSON.toJSONString(cancelOrderResponse));
            resultDTO.setMessage("发起取消订单成功。 Res: " + JSON.toJSONString(cancelOrderResponse));
        } else {
            resultDTO.setData("发起取消订单失败。 Res: " + JSON.toJSONString(cancelOrderResponse));
            resultDTO.setMessage("发起取消订单失败。 Res: " + JSON.toJSONString(cancelOrderResponse));
        }
        return resultDTO;
    }
    private JSONObject buildParam(Map<String, String> extraParams, JSONObject jsonObject) {
        for (String key : extraParams.keySet()) {
            jsonObject.put(key, extraParams.get(key));
        }
        return jsonObject;
    }
    @Override
    public ResultDTO cancelOrder4LLM(Long orderId,String cancelEvent,String dpath,String refundChannel) throws Exception {
        ResultDTO result = new ResultDTO();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("orderId", orderId);
        jsonObject.put("cancelEvent", cancelEvent);
        jsonObject.put("dpath", dpath);
        jsonObject.put("refundChannel", refundChannel);

        String params = buildParam(Constant.EXTRA_PARAMS, jsonObject).toJSONString();
        result = cancelOrder(params, null);
        return result;
    }

    @Override
    public ResultDTO customPlaceOrder(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
//
//        Constant.ORDER_SCENARIOS.forEach(scenairo -> {});
//        String scenario = jsonObject.getString(Constant.ORDER_SCENARIO);
//        if (OrderScenario.isContains(scenario)) {
//            List<Long> itemIds = Constant.ORDER_SCENARIOS.getItemIds();
//            for (Long itemId : itemIds) {
//                Response<CreateOrderResult> createOrderResultResponse = tradeService.getPlaceOrderResponse(buyerId,
//                        itemId, "", "USD", "");
//                if (createOrderResultResponse != null && createOrderResultResponse.isSuccess()) {
//                    Long orderId = Long.parseLong(createOrderResultResponse.getModule().getOrderIds().get(0));
//                    LandlordContext.setTenantSpec("AE_GLOBAL", -1);
//                    Response<TradeOrderLineDTO> tradeOrderLineDTOResponse = orderQueryForBuyerFacade.queryTradeOrderLineById(buyerId, orderId + 10000);
//                    String promiseTemplate = tradeOrderLineDTOResponse.getModule().getFeatures().getFeature("promiseTemplate");
//                    if (OrderScenario.getPromiseTemplate(scenario).equalsIgnoreCase("-1") || promiseTemplate.contains(Objects.requireNonNull(OrderScenario.getPromiseTemplate(scenario)))) {
//                        resultDTO.setSuccess(true);
//                        resultDTO.setMessage("OrderId: " + orderId);
//                        resultDTO.setData("OrderId: " + orderId);
//                        return resultDTO;
//                    } else {
//                        // TODO send email or dingTalk
//                        log.error("Invalid Item. ItemId: " + itemId + ". Scenario: " + scenario);
//
//                    }
//                }
//                resultDTO.setSuccess(false);
//                resultDTO.setMessage("Item is invalid. Please contract to the admin");
//                resultDTO.setData("Item is invalid. Please contract to the admin");
//                return resultDTO;
//            }
//        } else {
//            resultDTO.setMessage("Illegal Scenario.");
//            resultDTO.setData("Illegal Scenario.");
//            resultDTO.setSuccess(false);
//        }
        return resultDTO;
    }

//    @Override
//    public ResultDTO dynamicPrice(String params, SystemDTO systemDTO) {
//        JSONObject jsonObject = JSON.parseObject(params);
//        Long itemId = jsonObject.getLong(Constant.PARAM_ITEM_ID);
//        Long skuId = jsonObject.getLong(Constant.PARAM_SKU_ID);
//        String shipToCountry = jsonObject.getString(Constant.PARAM_SHIP_TO_COUNTRY);
//        String intentionCurrency = jsonObject.getString(Constant.INTENTIONAL_CURRENCY);
//        ResultDTO resultDTO = new ResultDTO();
//        try {
//            AdjustPriceDTO adjustPriceDTO =
//                    adjustPriceCalculateService.calculate(AdjustPriceRequestBuilder.from(itemId, skuId, intentionCurrency, shipToCountry));
//            if (null != adjustPriceDTO && CollectionUtils.isNotEmpty(adjustPriceDTO.getSkuAdjustPriceResultList())) {
//                resultDTO.setSuccess(true);
//                resultDTO.setData(JSON.toJSONString(adjustPriceDTO.getSkuAdjustPriceResultList(), SerializerFeature.WriteClassName));
//                resultDTO.setMessage("dynamic price");
//            } else {
//                resultDTO.setSuccess(false);
//                resultDTO.setMessage("dynamic price is empty");
//            }
//        } catch (Exception exp) {
//            resultDTO.setSuccess(false);
//            resultDTO.setMessage(exp.getMessage());
//        }
//        return resultDTO;
//    }

    @Override
    public ResultDTO queryProduct(String params, SystemDTO systemDTO) throws Exception {
        JSONObject jsonObject = JSON.parseObject(params);
        Long itemId = jsonObject.getLong(Constant.PARAM_ITEM_ID);
        ResultDTO resultDTO = new ResultDTO();
        LandlordContext.setTenantSpec("AE_GLOBAL", -1);
        SingleProductQueryCondition.QueryByProductIdBuilder queryByProductIdBuilder = SingleProductQueryCondition.queryByProductIdBuilder(itemId);
        ProductQueryRequest productQueryRequest = ProductQueryRequest.builder().addQueryCondition(queryByProductIdBuilder.build()).build();
//        ProductQueryResponse queryResult = customerProductServiceFacade.queryProduct(productQueryRequest);
////        LoggerFactory.getLogger(OrderService.class.getName()).info("queryProduct: " + JSON.toJSONString(queryResult));
//        if (!queryResult.isSuccess()) {
//            resultDTO.setData("Fail to get response from ic");
//        }
//        if (queryResult.getModel() == null) {
//            resultDTO.setData("Illegal Item Id. Fail to get item. Item Id: " + itemId);
//        }
//        Map<String, QueryResultUnit> data = QueryResultBuilder.buildQueryResult("商品SKU信息", null, null, queryResult.getModel().get(0).getSkuList());
//        resultDTO.setSuccess(queryResult.isSuccess());
//        resultDTO.setData(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
        return resultDTO;
    }

    @Override
    public ResultDTO getOrderPriceById(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long orderId = jsonObject.getLong(Constant.ORDER_ID);
        String staging = jsonObject.getString(Constant.STAGING);
        //查询区域化接口
        LandlordContext.setTenantSpec("AE_GLOBAL", -1);
        if (StringUtil.isNotBlank(staging)) {
            EagleEye.putUserData("scm_project", staging);//临时处理

        }
        Response<TradeOrderDTO> tradeOrderDTOResponse = orderQueryForBuyerFacade.queryTradeOrderById(jsonObject.getLong(Constant.BUYER_ID), orderId);

        if (tradeOrderDTOResponse.isNotSuccess()) {
            log.error("queryTradeOrderById() query failed");
            result.setSuccess(false);
            result.setMessage("queryTradeOrderById() query failed");
            return result;
        }

        List<String> ordertotalPricekey = new ArrayList<>();
        ordertotalPricekey.add("tradeOrderId");
        ordertotalPricekey.add("payStatus");
        ordertotalPricekey.add("deliverStatus");
        ordertotalPricekey.add("features");
        ordertotalPricekey.add("searchStatus");
        ordertotalPricekey.add("saleOriginalFee");
        ordertotalPricekey.add("orderAmount");
        ordertotalPricekey.add("actualFee");
        ordertotalPricekey.add("actualFeeOfPurposeCurrency");
        ordertotalPricekey.add("shippingActualFee");
        ordertotalPricekey.add("shippingDiscountFee");
        ordertotalPricekey.add("taxActualFee");
        ordertotalPricekey.add("taxRebateFee");
        ordertotalPricekey.add("payOrderDTO");
        ordertotalPricekey.add("promotionSnapshotId");


        String s = JSON.toJSONString(tradeOrderDTOResponse.getModule().getOrderLines(), SerializerFeature.DisableCircularReferenceDetect);
        List<TradeOrderLineDTO> tradeOrderLines = JSON.parseArray(s, TradeOrderLineDTO.class);


        List<String> orderPricekey = new ArrayList<>();
        orderPricekey.add("tradeOrderLineId");
        orderPricekey.add("exchangeInfo");
        orderPricekey.add("unitFee");
        orderPricekey.add("quantity");
        orderPricekey.add("shippingActualFee");
        orderPricekey.add("shippingFee");
        orderPricekey.add("shippingDiscountFee");
        orderPricekey.add("originalShippingDiscountInfo");
        orderPricekey.add("shippingDiscountInfo");
        orderPricekey.add("saleDiscountFee");
        orderPricekey.add("originalSaleDiscountInfo");
        orderPricekey.add("saleDiscountInfo");
        orderPricekey.add("taxActualFee");
        orderPricekey.add("taxFee");
        orderPricekey.add("taxRebateFee");
        orderPricekey.add("actualFee");
        orderPricekey.add("actualFeeOfPurposeCurrency");
        orderPricekey.add("payableFee");
        orderPricekey.add("adjustFee");
        orderPricekey.add("payStatus");
        orderPricekey.add("deliveryStatus");
        orderPricekey.add("endReason");
        orderPricekey.add("promotionOrderDTO");
        orderPricekey.add("paymentDiscountFee");
        orderPricekey.add("seller");
        orderPricekey.add("buyer");


        Map<String, QueryResultUnit> data = new LinkedHashMap<>();
        Map<String, QueryResultUnit> orderPrice = QueryResultBuilder.buildQueryResult("主订单金额", null, ordertotalPricekey, tradeOrderDTOResponse.getModule());
        Map<String, QueryResultUnit> orderLinePrice = QueryResultBuilder.buildQueryResult("子订单原价及优惠", null, orderPricekey, tradeOrderLines);

        List<Map<String, String>> orderLineFeatures = new ArrayList<>();
        for (TradeOrderLineDTO tradeOrderLineDTO : tradeOrderLines) {
            Map<String, String> orderLineFeature = tradeOrderLineDTO.getFeatures().getFeatureMap();
            orderLineFeatures.add(orderLineFeature);

        }


        List<String> pdfPricekey = new ArrayList<>();
        pdfPricekey.add("intent_pay_rate");
        pdfPricekey.add("exchangeRate");
        pdfPricekey.add("intent_pay_cur");
        pdfPricekey.add("intent_pay_rate");
        pdfPricekey.add("pdf");
        pdfPricekey.add("pcpdf");
        pdfPricekey.add("ppf");//支付优惠
        pdfPricekey.add("pcf");//支付手续费
        pdfPricekey.add("cbImport");
        pdfPricekey.add("gaf");
        pdfPricekey.add("paf");
        pdfPricekey.add("gt");
        pdfPricekey.add("pft");
        pdfPricekey.add("include_tax");
        pdfPricekey.add("tax_detail");
        pdfPricekey.add("v_o_tax_detail");
        pdfPricekey.add("tax_time");
        pdfPricekey.add("proRetailPrice");//原价
        pdfPricekey.add("d_p");
        pdfPricekey.add("v_p");//量价
        pdfPricekey.add("_dp_pcpdf");
        pdfPricekey.add("_dynamic_price");
        pdfPricekey.add("d_p_shipping_settle");
        pdfPricekey.add("o_p_shipping_settle");
        pdfPricekey.add("_product_feature");
        pdfPricekey.add("_sku_tag");
        pdfPricekey.add("promiseTemplate");


        Map<String, QueryResultUnit> orderLinePriceCurrency = QueryResultBuilder.buildQueryResult("子订单feature", null, pdfPricekey, orderLineFeatures);


        List<JSONObject> dynamicPrices = new ArrayList<>();
        for (Map<String, String> dynamicPrice : orderLineFeatures) {
            if (StringUtil.isNotBlank(dynamicPrice.get("_dynamic_price"))) {
                JSONObject _dynamic_price = JSONObject.parseObject(dynamicPrice.get("_dynamic_price"));
                dynamicPrices.add(_dynamic_price);
            }
        }
        Map<String, QueryResultUnit> dynamicPrice = QueryResultBuilder.buildQueryResult("子订单量价金dynamic_price", null, null, dynamicPrices);


        List<VolumePriceDTO> valumePrices = new ArrayList<>();
        for (TradeOrderLineDTO tradeOrderLineDTO : tradeOrderLines) {
            VolumePriceDTO volumePriceDTO = tradeOrderLineDTO.getVolumePrice();
            valumePrices.add(volumePriceDTO);

        }
        Map<String, QueryResultUnit> valumePrice = QueryResultBuilder.buildQueryResult("子订单量价金额valumePrice", null, null, valumePrices);

        data.putAll(orderPrice);
        data.putAll(orderLinePrice);
        data.putAll(orderLinePriceCurrency);
        data.putAll(dynamicPrice);
        data.putAll(valumePrice);

        result.setData(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
        result.setSuccess(true);
        return result;
    }

    @Override
    public ResultDTO freezeTestTradeOrder(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long tradeOrderId = jsonObject.getLong(Constant.ORDER_ID);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        String freezeType = jsonObject.getString(Constant.FREEZE_TYPE);
        FreezeTradeOrderRequest va1 = new FreezeTradeOrderRequest();
        va1.setBuyerId(buyerId);
        va1.setFreezeType(freezeType);
        va1.setTradeOrderId(tradeOrderId);
        va1.setOperatorRole("sys_issue");
        LandlordContext.setTenantSpec("AE_GLOBAL", -1);
        Response result = orderManagementRegionFacade.freezeTradeOrder(va1);
        if (result.isSuccess()) {
            resultDTO.setSuccess(result.isSuccess());
            resultDTO.setData(tradeOrderId + "freezeTradeOrder Successfully.");
        } else {
            resultDTO.setSuccess(false);
            resultDTO.setErrorCode(JSONObject.toJSONString(result.getErrorCode()));
            resultDTO.setMessage(JSONObject.toJSONString(result.getErrorCode()));

        }

        return resultDTO;
    }

    @Override
    public ResultDTO unfreezeTestTradeOrder(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long tradeOrderId = jsonObject.getLong(Constant.ORDER_ID);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        String freezeType = jsonObject.getString(Constant.FREEZE_TYPE);
        UnfreezeTradeOrderRequest va1 = new UnfreezeTradeOrderRequest();
        va1.setBuyerId(buyerId);
        va1.setFreezeType(freezeType);
        va1.setTradeOrderId(tradeOrderId);
        va1.setOperatorRole("sys_issue");
        LandlordContext.setTenantSpec("AE_GLOBAL", -1);
        Response result = orderManagementRegionFacade.unfreezeTradeOrder(va1);
        if (result.isSuccess()) {
            resultDTO.setSuccess(result.isSuccess());
            resultDTO.setData(tradeOrderId + "freezeTradeOrder Successfully.");
        } else {
            resultDTO.setSuccess(false);
            resultDTO.setErrorCode(JSONObject.toJSONString(result.getErrorCode()));
            resultDTO.setMessage(JSONObject.toJSONString(result.getErrorCode()));

        }

        return resultDTO;
    }

    @Override
    public ResultDTO placeTrialOrder(String params, SystemDTO systemDTO) {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long itemId = jsonObject.getLong(Constant.PARAM_ITEM_ID);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        Long activityId = jsonObject.getLong(Constant.ACTIVITY_ID);
        Set<Long> buyerIds = new HashSet<>();
        buyerIds.add(buyerId);


        HSFApiConsumerBean hsfApiConsumerBean = new HSFApiConsumerBean();
        hsfApiConsumerBean.setInterfaceName("com.aliexpress.trial.service.TrialToolService");
        hsfApiConsumerBean.setVersion("1.0.0");
        hsfApiConsumerBean.setGroup("HSF");
        hsfApiConsumerBean.setGeneric("true");

        try {
            GenericService genericOrderService = (GenericService) hsfApiConsumerBean.getObject();
            Map<String, Object> param = new ConcurrentHashMap<>();
            param.put("activityId", activityId);
            param.put("productId", itemId);
            param.put("whiteBuyerAliIds", buyerIds);
            Map orderModelMap = (Map) genericOrderService.$invoke("mockCanPlaceOrderData", new String[]{Map.class.getName()}, new Object[]{param});


            hsfApiConsumerBean.setInterfaceName("com.aliexpress.buyer.trial.hsf.OrderTest");
            List<String> providerIps = new ArrayList<>();
            providerIps.add("*************");
            hsfApiConsumerBean.setUserRawAddresses(providerIps);

            genericOrderService = (GenericService) hsfApiConsumerBean.getObject();
            orderModelMap = (Map) genericOrderService.$invoke("placeTradeOrder", new String[]{Long.class.getName(), Long.class.getName()}, new Object[]{activityId, itemId});
            resultDTO.setMessage(JSONObject.toJSONString(orderModelMap));
            resultDTO.setData(JSONObject.toJSONString(orderModelMap));
        } catch (Exception e) {
            e.printStackTrace();
        }

        resultDTO.setSuccess(true);
        return resultDTO;
    }

    @Override
    public ResultDTO shipOrder(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        String orderId = jsonObject.getString(Constant.ORDER_ID);
        String shipType = jsonObject.getString(Constant.SHIP_TYPE);
        String dpath = jsonObject.getString(Constant.DPATH_ENV);

        if (StringUtil.isNotBlank(dpath)) {
            EagleEye.putUserData("dpath_env", dpath);

        }

        Response<List<FulfillmentOrderWithPkgDTO>> fulfillmentOrderRes = fulfillmentOrderQueryNoRoutingFacade.queryFulfillmentOrderWithPkgByToId(orderId, null);
        if (!fulfillmentOrderRes.isSuccess() || CollectionUtils.isEmpty(fulfillmentOrderRes.getModule())) {
            result.setSuccess(false);
            result.setData("Fail to find fulfillment info. Order Id : " + orderId + " traceId：" + EagleEye.getTraceId());
            result.setMessage("Fail to find fulfillment info. Order Id : " + orderId + " traceId：" + EagleEye.getTraceId());
            return result;
        }
        FulfillmentOrderStatusUpdateRequest fulfillmentOrderStatusUpdateRequest = getFulfillmentOrderStatusUpdateRequest(orderId, shipType, fulfillmentOrderRes.getModule().get(0));
        Response<FulfillmentOrderStatusUpdateResponseDTO> fulfillmentOrderStatusUpdateResponseDTOResponse = fulfillmentOrderStatusUpdateFacade.onEvent(fulfillmentOrderStatusUpdateRequest);
        result.setSuccess(fulfillmentOrderStatusUpdateResponseDTOResponse.isSuccess());
        result.setData(JSON.toJSONString(fulfillmentOrderStatusUpdateResponseDTOResponse.getModule()));
        result.setMessage(JSON.toJSONString(fulfillmentOrderStatusUpdateResponseDTOResponse.getModule()));
//        if (!shipType.equals("已妥投")) {
//            FulfillmentOrderStatusUpdateRequest fulfillmentOrderStatusUpdateRequest = getFulfillmentOrderStatusUpdateRequest(orderId, shipType, fulfillmentOrderRes.getModule().get(0));
//            Response<FulfillmentOrderStatusUpdateResponseDTO> fulfillmentOrderStatusUpdateResponseDTOResponse = fulfillmentOrderStatusUpdateFacade.onEvent(fulfillmentOrderStatusUpdateRequest);
//            result.setSuccess(fulfillmentOrderStatusUpdateResponseDTOResponse.isSuccess());
//            result.setData(JSON.toJSONString(fulfillmentOrderStatusUpdateResponseDTOResponse.getModule()));
//            result.setMessage(JSON.toJSONString(fulfillmentOrderStatusUpdateResponseDTOResponse.getModule()));
//        }else{
//            String sellerId = jsonObject.getString(Constant.SELLER_ID);
//            JSONArray request = getReConsumeMsgByUop();
//            JSONObject response =  HsfUtil.reConsumeMsgByUop(request);
//            result.setSuccess(true);
//            result.setData(response.toJSONString());
//            result.setMessage(response.toJSONString());
//        }
        return result;
    }
    @Override
    public ResultDTO shipOrder4LLM(String orderId, String shipType,String dpath) throws Exception {
        ResultDTO result = new ResultDTO();

        if (StringUtil.isNotBlank(dpath)) {
            EagleEye.putUserData("dpath_env", dpath);

        }

        Response<List<FulfillmentOrderWithPkgDTO>> fulfillmentOrderRes = fulfillmentOrderQueryNoRoutingFacade.queryFulfillmentOrderWithPkgByToId(orderId, null);
        if (!fulfillmentOrderRes.isSuccess() || CollectionUtils.isEmpty(fulfillmentOrderRes.getModule())) {
            result.setSuccess(false);
            result.setData("Fail to find fulfillment info. Order Id : " + orderId + " traceId：" + EagleEye.getTraceId());
            result.setMessage("Fail to find fulfillment info. Order Id : " + orderId + " traceId：" + EagleEye.getTraceId());
            return result;
        }
        FulfillmentOrderStatusUpdateRequest fulfillmentOrderStatusUpdateRequest = getFulfillmentOrderStatusUpdateRequest(orderId, shipType, fulfillmentOrderRes.getModule().get(0));
        Response<FulfillmentOrderStatusUpdateResponseDTO> fulfillmentOrderStatusUpdateResponseDTOResponse = fulfillmentOrderStatusUpdateFacade.onEvent(fulfillmentOrderStatusUpdateRequest);
        result.setSuccess(fulfillmentOrderStatusUpdateResponseDTOResponse.isSuccess());
        result.setData(JSON.toJSONString(fulfillmentOrderStatusUpdateResponseDTOResponse.getModule()));
        result.setMessage(JSON.toJSONString(fulfillmentOrderStatusUpdateResponseDTOResponse.getModule()));
        return result;
    }

    @Override
    public ResultDTO shipOrderOnline(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        String tradeOrderId = jsonObject.getString(Constant.ORDER_ID);
        String globalUserId = jsonObject.getString(Constant.SELLER_ID);
        String solutionCode = getSolutionCode(jsonObject.getString(Constant.SOLUTION_CODE));
//        String itemStr = jsonObject.getString(Constant.ITEM_IDS);
//        List<Long> itemList = parseLongFromStr(itemStr);
        List<Long> itemList = new ArrayList<>();
        JSONArray orderLines = jsonObject.getJSONArray(Constant.ORDER_LINES);
        for (int i = 0; i < orderLines.size(); i++) {
            JSONObject orderLine = orderLines.getJSONObject(i);
            Long itemId = orderLine.getJSONObject("product").getLong("itemId");
            itemList.add(itemId);
        }

        JSONObject request = JSONObject.parseObject(Constant.ONLINE_SHIP_PARAM);
        JSONObject orderParam = request.getJSONObject("orderParam");
        JSONObject tradeOrderParam = orderParam.getJSONObject("tradeOrderParam");
        tradeOrderParam.put("tradeOrderId", tradeOrderId);
        JSONObject sellerInfoParam = orderParam.getJSONObject("sellerInfoParam");
        sellerInfoParam.put("globalUserId", globalUserId);
        JSONObject solutionParam = orderParam.getJSONObject("solutionParam");
        solutionParam.put("solutionCode", solutionCode);

        JSONArray itemParams = orderParam.getJSONArray("logisticPackageParams").getJSONObject(0).getJSONArray("itemParams");
        JSONObject itemParam = JSONObject.parseObject(Constant.ONLINE_SHIP_ITEM_PARAM);
        int size = itemList.size();
        if (size == 1) {
            itemParam.put("itemId", itemList.get(0));
            itemParams.add(0, itemParam);
        } else {
            for (int i = 0; i < size; i++) {
                JSONObject duplicateItemParam = new JSONObject();
                duplicateItemParam = JSONObject.parseObject(Constant.ONLINE_SHIP_ITEM_PARAM);
                duplicateItemParam.put("itemId", itemList.get(i));
                itemParams.add(duplicateItemParam);
            }
        }
        JSONObject shipOnlineResult = HsfUtil.shipOnline(request);
        result.setSuccess(true);
        if (shipOnlineResult.getBoolean("success")) {
            result.setData("菜鸟线上发货成功");
            result.setMessage("菜鸟线上发货成功");
        } else {
            result.setData("菜鸟线上发货失败。 Resquest:" + request.toJSONString() + "Res:" + shipOnlineResult.toJSONString());
            result.setMessage("菜鸟线上发货失败。 Resquest:" + request.toJSONString() + "Res: " + shipOnlineResult.toJSONString());
        }
        return result;
    }

    private String getSolutionCode(String solution) {
        switch (solution) {
            case "无忧标准":
                return "CAINIAO_STANDARD";
            case "无忧优先":
                return "CAINIAO_PREMIUM";
            case "无忧简易":
                return "CAINIAO_ECONOMY";
            case "超级经济":
                return "CAINIAO_SUPER_ECONOMY";
            case "COD无忧集运":
                return "CAINIAO_CONSOLIDATION";
        }
        return "";
    }

    public static List<Long> parseLongFromStr(String str) {
        List<Long> ids = new ArrayList<>();
        for (String id : str.split(",")) {
            ids.add(Long.parseLong(id));
        }
        return ids;
    }

    @Override
    public ResultDTO paymentRiskCancelOrder(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        String orderId = jsonObject.getString(Constant.ORDER_ID);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        JSONObject body = new JSONObject();
        body.put("bizOrderId", orderId);
        body.put("buyerId", buyerId);
        body.put("checkoutOrderId", "12990501103241204524613710987");
        body.put("checkoutSource", "global.trade");
        body.put("eventType", "CHECKOUT_ORDER");
        body.put("reverseType", "RISK_REJECT");
        SendResult sendResult = mqConfig.sendMessage(Constant.PAYMENT_TOPIC, Constant.PAYMENT_RISK_TAG, orderId, body.toJSONString());
        result.setSuccess(true);
        result.setData(JSON.toJSONString(sendResult));
        result.setMessage(JSON.toJSONString(sendResult));
        return result;
    }

    @Override
    public ResultDTO getOrderByLoginIdOrBuyerId(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        String loginId = jsonObject.getString(Constant.LOGIN_ID);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        if (buyerId == null || buyerId < 0) {
            JSONObject result = HsfUtil.getUserByLoginId(loginId);
            if (result == null || result.getJSONObject("module") == null) {
                resultDTO.setSuccess(true);
                resultDTO.setMessage("Fail to get user info from uic. Res: " + result);
                return resultDTO;
            }
            buyerId = result.getJSONObject("module").getLong("userId");
        }
        TradeOrderListPageRequest tradeOrderListPageRequest = new TradeOrderListPageRequest();
        tradeOrderListPageRequest.setBuyerId(buyerId);
        tradeOrderListPageRequest.setPage(1);
        tradeOrderListPageRequest.setPageSize(50);
        LandlordContext.setTenantSpec("AE_GLOBAL", -1);
        Response<TradeOrderListViewDTO> tradeOrderListViewDTOResponse = orderViewFacade.getTradeOrderList(tradeOrderListPageRequest);

        Map<String, QueryResultUnit> data = new LinkedHashMap<>();
        if (tradeOrderListViewDTOResponse.isSuccess() && tradeOrderListViewDTOResponse.getModule() != null) {
            tradeOrderListViewDTOResponse.getModule().getTradeOrderViews().forEach(it -> {
                try {
                    Long tradeOrderId = it.getTradeOrder().getTradeOrderId();
                    Response<TradeOrderDTO> tradeOrderDTO = orderQueryForBuyerFacade.queryTradeOrderById(tradeOrderId);
                    TradeVO tradeVO = new TradeVO(tradeOrderDTO.getModule(), loginId);
                    tradeVO.setSellerLoginId(getSellerLoginId(tradeVO.getSellerAccountId()));
                    Map<String, QueryResultUnit> order = QueryResultBuilder.buildQueryResult("订单详情 -- " + tradeOrderId, Constant.LOGIN_ORDER_HEADS, null, tradeVO);
                    data.putAll(order);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            });
        } else {
            resultDTO.setData("Fail to get message from trade. Res: " + tradeOrderListViewDTOResponse);
        }
        resultDTO.setData(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
        resultDTO.setSuccess(tradeOrderListViewDTOResponse.isSuccess());
        return resultDTO;
    }

    private String getSellerLoginId(Long sellerAccountId) throws Exception {
        JSONObject jsonObject = HsfUtil.getUserByUserId(sellerAccountId);
        return jsonObject.getJSONObject("module").getString("loginId");
    }


    private FulfillmentOrderStatusUpdateRequest getFulfillmentOrderStatusUpdateRequest(String orderId, String shipType, FulfillmentOrderWithPkgDTO fulfillmentOrderWithPkgDTO) {
        FulfillmentOrderStatusUpdateRequest fulfillmentOrderStatusUpdateRequest = new FulfillmentOrderStatusUpdateRequest();
        fulfillmentOrderStatusUpdateRequest.setDataMap(getDataMap());
        fulfillmentOrderStatusUpdateRequest.setEvent(getShippingEvent(shipType));
        fulfillmentOrderStatusUpdateRequest.setEventTime(System.currentTimeMillis());
        fulfillmentOrderStatusUpdateRequest.setEventSendTime(System.currentTimeMillis());
        fulfillmentOrderStatusUpdateRequest.setOutBizId(orderId);
        fulfillmentOrderStatusUpdateRequest.setSource("cainiao");
        fulfillmentOrderStatusUpdateRequest.setTrackingNumber(UUID.randomUUID().toString());
        fulfillmentOrderStatusUpdateRequest.setFulfillmentOrderId(fulfillmentOrderWithPkgDTO.getFulfillmentOrderDTO().getFulfillmentOrderId());
        fulfillmentOrderStatusUpdateRequest.setFulfillmentOrderItemList(getFulfillmentOrderItemIds(fulfillmentOrderWithPkgDTO.getFulfillmentOrderDTO()));
        return fulfillmentOrderStatusUpdateRequest;
    }

    private String getShippingEvent(String shipType) {
        switch (shipType) {
            case "全部发货":
                return "COMPLETE_SHIPPING";
            case "部分发货":
                return "PARTIAL_SHIPPING";
            case "已妥投":
                return "LAST_MILE_GTMS_SIGNED";
        }
        return "COMPLETE_SHIPPING";
    }

    private List<String> getFulfillmentOrderItemIds(FulfillmentOrderDTO fulfillmentOrderDTO) {
        List<String> fulfillmentOrderItemIds = new ArrayList<>();
        fulfillmentOrderDTO.getFulfillmentOrderItemDTOList().forEach(it -> fulfillmentOrderItemIds.add(it.getFulfillmentOrderItemId()));
        return fulfillmentOrderItemIds;
    }

    private Map<String, Object> getDataMap() {
        Map<String, Object> dataMap = new ConcurrentHashMap<>();
        dataMap.put("shippingName", "AliExpress Standard Shipping");
        dataMap.put("shippingServiceCode", "CAINIAO_STANDARD");
        dataMap.put("isTrackingInfoExist", "Y");
        return dataMap;
    }

    @Override
    public ResultDTO placeOrder(String params, SystemDTO systemDTO) throws Exception {
        String empId = Optional.ofNullable(systemDTO)
                .map(SystemDTO::getOperator)
                .orElse("");
        HsfUtil.measureAll("/trade/createOrder", empId);
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        String countryCode = jsonObject.getString(Constant.SHIP_TO_COUNTRY);
        String deliveryOption = jsonObject.getString(Constant.SHIPPING_METHOD);
        String itemIDS = jsonObject.getString(Constant.ITEM_IDS);
        String skuId = jsonObject.getString(Constant.SKU_ID);
        Integer quantity = jsonObject.getInteger(Constant.QUANTITY);
        String shareGroup = jsonObject.getString(Constant.SHARE_GROUP);
        String shareGroupCode = jsonObject.getString(Constant.SHARE_GROUP_CODE);
        List<String> productIds = JSON.parseArray(itemIDS, String.class);
        String cur = jsonObject.getString(Constant.CURRENCY);
        LandlordContext.setTenantSpec("AE_GLOBAL", -1);

        String staging = jsonObject.getString(Constant.STAGING);

        if (StringUtil.isNotBlank(staging)) {
            EagleEye.putUserData("dpath_env", staging);
        }
        GetPlaceOrderDTO getPlaceOrderDTO = new GetPlaceOrderDTO();
        getPlaceOrderDTO.setBuyerId(buyerId);
        getPlaceOrderDTO.setItemIds(productIds);
        getPlaceOrderDTO.setCountryCode(countryCode);
        getPlaceOrderDTO.setShippingMethod(deliveryOption);
        getPlaceOrderDTO.setSkuId(skuId);
        getPlaceOrderDTO.setQuantity(quantity);
        getPlaceOrderDTO.setShareGroup(shareGroup);
        getPlaceOrderDTO.setShareGroupCode(shareGroupCode);
        getPlaceOrderDTO.setCurrencyCode(cur);
        try {
            Result<CreateOrderResult> createOrderResultResponse = tradeService.getPlaceOrderResponse(getPlaceOrderDTO);
            if (createOrderResultResponse != null && createOrderResultResponse.isSuccess()) {
                // 获取所有订单ID
                List<String> orderIds = createOrderResultResponse.getModel().getOrderIds();

                JSONObject jsonObj = new JSONObject();
                if (orderIds.size() == 1) {
                    // 如果只有一个订单ID，保持原有行为
                    Long orderId = Long.parseLong(orderIds.get(0));
                    jsonObj.put("orderId", orderId);
                } else {
                    // 如果有多个订单ID，则返回数组
                    List<Long> orderIdList = new ArrayList<>();
                    for (String orderIdStr : orderIds) {
                        orderIdList.add(Long.parseLong(orderIdStr));
                    }
                    jsonObj.put("orderIds", orderIdList);
                }

                String jsonString = jsonObj.toString();
                resultDTO.setMessage("create order success");
                resultDTO.setData(jsonString);
                resultDTO.setSuccess(true);
                return resultDTO;
            } else if (createOrderResultResponse != null) {
                resultDTO.setSuccess(false);
                resultDTO.setMessage(createOrderResultResponse.getMsgInfo() + "|" + "traceId:" + EagleEye.getTraceId());
                resultDTO.setData(createOrderResultResponse.getMsgCode() + "|" + "traceId:" + EagleEye.getTraceId());
            }
        } catch (Exception e) {
            resultDTO.setSuccess(false);
            resultDTO.setMessage(e.getMessage() + "traceId:" + EagleEye.getTraceId());
            resultDTO.setData(e.getMessage() + "traceId:" + EagleEye.getTraceId());
        }
        return resultDTO;
    }

//    @Override
//    public ResultDTO placeOrder4LLM(Long buyerId,String countryCode,String deliveryOption,String itemIDS,String skuId,Integer quantity,String cur,String staging,String shareGroup,String shareGroupCode) throws Exception {
//        ResultDTO resultDTO = new ResultDTO();
//        itemIDS="[" +itemIDS+"]";
//        List<String> productIds = JSON.parseArray(itemIDS, String.class);
////        Long itemId = Long.valueOf(jsonObject.getJSONArray(Constant.ITEM_IDS).get(0).toString());
//        LandlordContext.setTenantSpec("AE_GLOBAL", -1);
//
//
//        if (StringUtil.isNotBlank(staging)) {
//            EagleEye.putUserData("dpath_env", staging);
//        }
//        try {
//            Result<CreateOrderResult> createOrderResultResponse = tradeService.getPlaceOrderResponse(buyerId, productIds, skuId, quantity, countryCode, cur, deliveryOption, shareGroup, shareGroupCode);
//            if (createOrderResultResponse != null && createOrderResultResponse.isSuccess()) {
//                Long orderId = Long.parseLong(createOrderResultResponse.getModel().getOrderIds().get(0));
//
//                JSONObject jsonObj = new JSONObject();
//                jsonObj.put("orderId", orderId);
//
//                String jsonString = jsonObj.toString();
//                resultDTO.setMessage("create order success");
//                resultDTO.setData(jsonString);
//                resultDTO.setSuccess(true);
//                return resultDTO;
//            } else if (createOrderResultResponse != null) {
//                resultDTO.setSuccess(false);
//                resultDTO.setMessage(createOrderResultResponse.getMsgInfo() + "|" + "traceId:" + EagleEye.getTraceId());
//                resultDTO.setData(createOrderResultResponse.getMsgCode() + "|" + "traceId:" + EagleEye.getTraceId());
//            }
//        } catch (Exception e) {
//            resultDTO.setSuccess(false);
//            resultDTO.setMessage(e.getMessage() + "traceId:" + EagleEye.getTraceId());
//            resultDTO.setData(e.getMessage() + "traceId:" + EagleEye.getTraceId());
//        }
//        return resultDTO;
//    }

    @Override
    public ResultDTO placeOrderForFreeReturn(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        Long itemId = Long.valueOf(jsonObject.getJSONArray(Constant.ITEM_IDS).get(0).toString());
        String country = jsonObject.getString(Constant.SHIP_TO_COUNTRY);
        String deliveryOption = jsonObject.getString(Constant.DELIVERY_OPTION);
        List<Long> orderIds = new ArrayList<>();
        StringBuilder sb = new StringBuilder();
        if (country.equalsIgnoreCase("ALL")) {
            String[] countries = Constant.FREE_RETURN_COUNTRY.split(",");
            for (String countryCode : countries) {
                try {
                    Response<CreateOrderResult> createOrderResultResponse = tradeService.getPlaceOrderResponse(buyerId, itemId, countryCode, "USD", deliveryOption);
                    if (createOrderResultResponse != null && createOrderResultResponse.isSuccess()) {
                        Long orderId = Long.parseLong(createOrderResultResponse.getModule().getOrderIds().get(0));
                        orderIds.add(orderId);
                    } else {
                        resultDTO.setMessage("Item is invalid. Please contract to the admin");
                        resultDTO.setData("Item is invalid. Please contract to the admin");
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    sb.append("Fail to place order in country: " + countryCode + ".");
                }
            }
            resultDTO.setMessage("orderIds: " + JSONObject.toJSONString(orderIds) + "." + sb.toString());
            resultDTO.setData("orderIds: " + JSONObject.toJSONString(orderIds) + "." + sb.toString());
        } else {
            Response<CreateOrderResult> createOrderResultResponse = tradeService.getPlaceOrderResponse(buyerId, itemId, country, "USD", deliveryOption);
            if (createOrderResultResponse != null && createOrderResultResponse.isSuccess()) {
                Long orderId = Long.parseLong(createOrderResultResponse.getModule().getOrderIds().get(0));
                resultDTO.setMessage("orderId: " + orderId);
                resultDTO.setData("orderId: " + orderId);
            } else {
                resultDTO.setMessage("Place Order Error. Error Info: " + JSON.toJSONString(createOrderResultResponse));
                resultDTO.setData("Place Order Error. Error Info: " + JSON.toJSONString(createOrderResultResponse));
            }
        }
        resultDTO.setSuccess(true);
        return resultDTO;
    }

    @Override
    public ResultDTO cloneOrder(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long orderId = jsonObject.getLong(Constant.ORDER_ID);

        Response<TradeOrderDTO> tradeOrderDTOResponse = orderQueryForBuyerFacade.queryTradeOrderById(orderId);

        if (tradeOrderDTOResponse.isNotSuccess()) {
            log.info("queryTradeOrderById() query failed");
            resultDTO.setSuccess(false);
            resultDTO.setData("queryTradeOrderById() query failed");
            return resultDTO;
        }

        Response<CreateOrderResult> createOrderResultResponse = tradeService.getPlaceOrderResponse(tradeOrderDTOResponse.getModule());
        if (createOrderResultResponse != null && createOrderResultResponse.isSuccess()) {
            Long newrOderId = Long.parseLong(createOrderResultResponse.getModule().getOrderIds().get(0));
            resultDTO.setMessage("orderId:" + newrOderId);
            resultDTO.setData("orderId:" + newrOderId);
            resultDTO.setSuccess(true);

        } else {
            resultDTO.setSuccess(false);
            resultDTO.setMessage(createOrderResultResponse.getErrorCode().getKey() + createOrderResultResponse.getErrorCode().getDisplayMessage());
            resultDTO.setData(createOrderResultResponse.getErrorCode().getKey() + createOrderResultResponse.getErrorCode().getDisplayMessage());
        }

        return resultDTO;
    }

    @Override
    public ResultDTO confirmDelivery(String params, SystemDTO systemDTO) throws Exception {
        String empId = Optional.ofNullable(systemDTO)
                .map(SystemDTO::getOperator)
                .orElse("");
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        Long orderId = jsonObject.getLong(Constant.ORDER_ID);
        Long subOrderId = jsonObject.getLong(Constant.SUB_ORDER_ID);
        List<Long> tradeOrderLineIds = new ArrayList<>();
        if (subOrderId != null && subOrderId != 0) {
            tradeOrderLineIds.add(subOrderId);
        } else {
            Response<TradeOrderDTO> tradeOrderDTOResponse = orderQueryForBuyerFacade.queryTradeOrderById(buyerId, orderId);
            tradeOrderLineIds.addAll(tradeOrderDTOResponse.getModule().getOrderLines().stream().map(TradeOrderLineDTO::getTradeOrderLineId).collect(Collectors.toList()));
        }
        BuyerConfirmGoodsRequest buyerConfirmGoodsRequest = new BuyerConfirmGoodsRequest();
        buyerConfirmGoodsRequest.setBuyerId(buyerId);
        buyerConfirmGoodsRequest.setTradeOrderLineIds(tradeOrderLineIds);
        Response res = orderManagementRegionFacade.buyerConfirmGoods(buyerConfirmGoodsRequest);
        resultDTO.setSuccess(true);
        if (res.isSuccess()) {
            resultDTO.setData("确认收货成功.");
        } else {
            resultDTO.setData("确认收货失败. 请重试. 失败原因: " + JSONObject.toJSONString(res));
        }
        HsfUtil.measureAll("/trade/jobId=" + (systemDTO != null ? systemDTO.getSite() : "defaultSite"), empId);
        return resultDTO;
    }
    @Override
    public ResultDTO confirmDelivery4LLM(Long orderId,Long subOrderId) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        Long buyerId = Long.valueOf(Constant.EXTRA_PARAMS.get("buyerId"));

        List<Long> tradeOrderLineIds = new ArrayList<>();
        if (subOrderId != null && subOrderId != 0) {
            tradeOrderLineIds.add(subOrderId);
        } else {
            Response<TradeOrderDTO> tradeOrderDTOResponse = orderQueryForBuyerFacade.queryTradeOrderById(buyerId, orderId);
            tradeOrderLineIds.addAll(tradeOrderDTOResponse.getModule().getOrderLines().stream().map(TradeOrderLineDTO::getTradeOrderLineId).collect(Collectors.toList()));
        }
        BuyerConfirmGoodsRequest buyerConfirmGoodsRequest = new BuyerConfirmGoodsRequest();
        buyerConfirmGoodsRequest.setBuyerId(buyerId);
        buyerConfirmGoodsRequest.setTradeOrderLineIds(tradeOrderLineIds);
        Response res = orderManagementRegionFacade.buyerConfirmGoods(buyerConfirmGoodsRequest);
        resultDTO.setSuccess(true);
        if (res.isSuccess()) {
            resultDTO.setData("确认收货成功.");
        } else {
            resultDTO.setData("确认收货失败. 请重试. 失败原因: " + JSONObject.toJSONString(res));
        }
        return resultDTO;
    }

    @Override
    public List<Long> getOrderIdsByScenarioIndexId(String scenario, String scenarioIndex, int size) throws Exception {
        return placeOrderStrategy.getOrderIdsByScenarioIndex(scenario, scenarioIndex, size);
    }

    @Override
    public List<Long> getOrderIdsByScenario(String scenario, int size) throws Exception {
        return placeOrderStrategy.getOrderIdsByScenario(scenario, size);
    }

    private static final String SP = ";";
    private static final String SSP = ":";
    private static final String R_SP = "#3B";
    private static final String R_SSP = "#3A";

    /**
     * 正向 #3A代表:  #3B代表;
     */
    private static String decode(String val) {
        return StringUtils.replace(StringUtils.replace(val, R_SP, SP), R_SSP, SSP);
    }

    /**
     * 逆向 #3A代表;  #3B代表:
     */
    private static String decodeReverse(String val) {
        return StringUtils.replace(StringUtils.replace(val, R_SSP, SP), R_SP, SSP);
    }

    /**
     * 正逆向
     */
    public static Map<String, String> toMap(String str) {
        Map<String, String> attrs = new HashMap<String, String>();
        boolean isReverse = str.contains("request_type");
        if (StringUtils.isNotBlank(str)) {
            String[] arr = StringUtils.split(str, SP);
            for (String kv : arr) {
                if (StringUtils.isNotBlank(kv)) {
                    String[] ar = kv.split(SSP);
                    if (ar.length == 2) {
                        String k;
                        String v;
                        if (!isReverse) {
                            k = decode(ar[0]);
                            v = decode(ar[1]);
                        } else {
                            k = decodeReverse(ar[0]);
                            v = decodeReverse(ar[1]);
                        }
                        if (StringUtils.isNotBlank(k) && StringUtils.isNotBlank(v)) {
                            attrs.put(k, v);
                        }
                    }
                }
            }
        }
        return attrs;
    }

    public static JSONObject mapToJson(Map<String, String> map) {
        JSONObject result = new JSONObject();
        for (String key : map.keySet()) {
            String value = map.get(key);
            if (value.startsWith("{") || value.startsWith("[")) {
                // 解析json
                try {
                    Object subJson = JSONObject.parse(value);
                    result.put(key, subJson);
                } catch (JSONException e) {
                    // 解析失败，拿字符串塞入
                    result.put(key, value);
                }
            } else {
                result.put(key, value);
            }
        }
        return result;
    }

    @Override
    public ResultDTO shippingWarehouseOrder(String params, SystemDTO systemDTO) throws Exception {
        this.params = params;
        this.systemDTO = systemDTO;
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long orderId = jsonObject.getLong(Constant.ORDER_ID);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        JSONObject tradeOrderResponse = jsonObject.getJSONObject(Constant.ORDER_INFO).getJSONObject("module");
        List<TradeOrderLineDTO> tradeOrderLineDTOs = tradeOrderResponse.getJSONArray("orderLines").toJavaList(TradeOrderLineDTO.class);
        String foId = tradeOrderLineDTOs.get(0).getOutDeliveryId();
        try {
            if (foId.startsWith("FO")) {
                String acceptResult = fulfillmentQueryTestFacade.toolForAccept(foId, orderId, "");
                String outBoundResult = fulfillmentQueryTestFacade.toolForOutBound(foId, buyerId, "", String.valueOf(orderId));
                resultDTO.setMessage("AcceptResult: " + JSONObject.toJSONString(acceptResult) + ";  outBoundResult : " + JSONObject.toJSONString(outBoundResult));
                resultDTO.setData("AcceptResult: " + JSONObject.toJSONString(acceptResult) + "; outBoundResult : " + JSONObject.toJSONString(outBoundResult));
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.info("shippingWarehouseOrder(): error + " + JSONObject.toJSONString(e));
        }
        resultDTO.setSuccess(true);
        return resultDTO;
    }

    @Override
    public ResultDTO shippingUnWarehouseOrder(String params, SystemDTO systemDTO) throws Exception {
        this.params = params;
        this.systemDTO = systemDTO;
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long orderId = jsonObject.getLong(Constant.ORDER_ID);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        JSONObject tradeOrderResponse = jsonObject.getJSONObject(Constant.ORDER_INFO).getJSONObject("module");
        List<TradeOrderLineDTO> tradeOrderLineDTOs = tradeOrderResponse.getJSONArray("orderLines").toJavaList(TradeOrderLineDTO.class);
        String foId = tradeOrderLineDTOs.get(0).getOutDeliveryId();
        try {
            if (foId.startsWith("FO")) {
                String outBoundResult = fulfillmentQueryTestFacade.toolForOutBound(foId, buyerId, "", String.valueOf(orderId));
                resultDTO.setMessage("OutBoundResult: " + JSONObject.toJSONString(outBoundResult));
                resultDTO.setData("OutBoundResult: " + JSONObject.toJSONString(outBoundResult));
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.info("shippingWarehouseOrder(): error + " + JSONObject.toJSONString(e));
        }
        resultDTO.setSuccess(true);
        return resultDTO;
    }


    @Override
    public ResultDTO createMiniAppOrder(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        String buyerId = jsonObject.getString(Constant.BUYER_ID);
        String price = jsonObject.getString(Constant.PRICE);
        String currency = jsonObject.getString(Constant.CURRENCY);
        String staging = jsonObject.getString(Constant.STAGING);
        Number addressId = jsonObject.getDouble(Constant.ADDRESS_ID);
        if (StringUtil.isNotBlank(staging)) {
            EagleEye.putUserData("dpath_env", staging);//临时处理
        }
        JSONObject result = HsfUtil.createMiniAppOrder(buyerId, price, currency, addressId);
        resultDTO.setMessage("result:" + result.toJSONString());
        resultDTO.setData("result:" + result.toJSONString());

        return resultDTO;
    }

    @Override
    public ResultDTO add2CartByOrder(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        Map<Long, Long> skuIds = Maps.newConcurrentMap();
        Map<Long, Integer> quantities = Maps.newConcurrentMap();
        JSONArray orderLines = jsonObject.getJSONArray(Constant.ORDER_LINES);
        for (int i = 0; i < orderLines.size(); i++) {
            JSONObject orderLine = orderLines.getJSONObject(i);
            Long itemId = orderLine.getJSONObject("product").getLong("itemId");
            Long skuId = 0L;
            if (orderLine.getJSONObject("product").getJSONObject("sku") != null) {
                skuId = orderLine.getJSONObject("product").getJSONObject("sku").getLong("skuId");
            }
            skuIds.put(itemId, skuId);
            quantities.put(itemId, orderLine.getInteger("quantity"));
        }
        CartAddRequest cartAddRequest = cartService.getAddCartRequest(buyerId, skuIds, quantities, "USD", "US", null);
        Response response = cartFacade.add(cartAddRequest);
        if (response.isSuccess()) {
            resultDTO.setSuccess(true);
            resultDTO.setData("add to cart successfully");
        } else {
            resultDTO.setSuccess(false);
            resultDTO.setData(response.getErrorCode().getLogMessage() + response.getErrorCode().getDisplayMessage() + " traceId: " + EagleEye.getTraceId());
        }
        return resultDTO;
    }

    @Override
    public ResultDTO qdOrderConfirm(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long orderId = jsonObject.getLong(Constant.ORDER_ID);
        JSONObject tradeOrderResponse = jsonObject.getJSONObject(Constant.ORDER_INFO).getJSONObject("module");
        Features features = JSONObject.parseObject(tradeOrderResponse.getJSONObject("features").toJSONString(), Features.class);
        String orderFrom = features.getFeatureMap().get("orderFrom");
        if (!"QD".equalsIgnoreCase(orderFrom)) {
            resultDTO.setSuccess(true);
            resultDTO.setData("Invalid QD order");
            return resultDTO;
        }
        JSONObject res = HsfUtil.changOrderStatus(String.valueOf(orderId), "ORDER_CONFIRM");
        if (res.getBoolean("success")) {
            resultDTO.setData("确认收货成功");
            resultDTO.setMessage("确认收货成功");
        } else {
            resultDTO.setData("确认收货失败。Res: " + res.toJSONString());
            resultDTO.setMessage("确认收货失败。Res: " + res.toJSONString());
        }
        return resultDTO;
    }

    @Override
    public ResultDTO getOrderPrice(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long orderId = jsonObject.getLong(Constant.ORDER_ID);
        Boolean dynamic = jsonObject.getBoolean(Constant.DYNAMIC_PRICE);
        JSONObject result = HsfUtil.getOrderDynamicPrice(orderId, dynamic);
        if (result.getBoolean("success")) {
            Map<String, QueryResultUnit> data = QueryResultBuilder.buildQueryResult("订单信息", null, null, result.getJSONObject("module"));
            resultDTO.setData(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
        } else {
            resultDTO.setSuccess(false);
            resultDTO.setMessage(JSONObject.toJSONString(result));
            resultDTO.setData(JSONObject.toJSONString(result));
        }
        return resultDTO;
    }

    @Override
    public ResultDTO addOrderFeaturesTag(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        String empId = systemDTO.getOperator();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long orderId = jsonObject.getLong(Constant.ORDER_ID);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        String tags = jsonObject.getString(Constant.TAGS);
        String orderLineScenario = jsonObject.getString(Constant.ORDER_LINE_SCENARIO);
        Long orderLineId = jsonObject.getLong(Constant.SUB_ORDER_ID);
        List<Long> orderLineIds = Lists.newArrayList();
        if (orderLineId != null) {
            orderLineIds.add(orderLineId);
        } else {
            JSONArray orderLines = jsonObject.getJSONArray(Constant.ORDER_LINES);
            for (int i = 0; i < orderLines.size(); i++) {
                JSONObject orderLine = orderLines.getJSONObject(i);
                orderLineIds.add(orderLine.getLongValue("tradeOrderLineId"));
            }
        }
        JSONObject req = getModifyOrderInfoReq(buyerId, orderId, orderLineIds, orderLineScenario, tags);
        JSONObject result = HsfUtil.modifyOrderInfo(req.toJavaObject(Object.class));
        resultDTO.setSuccess(result.getBoolean("success"));
        resultDTO.setMessage(resultDTO.getSuccess() ? "打标成功. Res: " + JSONObject.toJSONString(result) : "打标失败" + JSONObject.toJSONString(result));
        resultDTO.setData(resultDTO.getSuccess() ? "打标成功. Res: " + JSONObject.toJSONString(result) : "打标失败" + JSONObject.toJSONString(result));
        HsfUtil.measureAll("/trade/jobId=" + systemDTO.getSite(), empId);
        return resultDTO;
    }

    @Override
    public ResultDTO addOrderLineFeaturesTag(String params, SystemDTO systemDTO) throws Exception {
        String empId = systemDTO.getOperator();
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long orderId = jsonObject.getLong(Constant.ORDER_ID);
        Long orderLineId = jsonObject.getLong(Constant.SUB_ORDER_ID);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        String tags = jsonObject.getString(Constant.TAGS);
        String orderLineScenario = jsonObject.getString(Constant.ORDER_LINE_SCENARIO);
        JSONObject req = getModifyOrderLineInfoReq(buyerId, orderId, orderLineId, orderLineScenario, tags);
        JSONObject result = HsfUtil.modifyOrderInfo(req.toJavaObject(Object.class));
        resultDTO.setSuccess(result.getBoolean("success"));
        resultDTO.setMessage(resultDTO.getSuccess() ? "打标成功. Res: " + JSONObject.toJSONString(result) : "打标失败" + JSONObject.toJSONString(result));
        resultDTO.setData(resultDTO.getSuccess() ? "打标成功. Res: " + JSONObject.toJSONString(result) : "打标失败" + JSONObject.toJSONString(result));
        HsfUtil.measureAll("/trade/jobId=" + systemDTO.getSite(), empId);
        return resultDTO;
    }

    private JSONObject getModifyOrderLineInfoReq(Long buyerId, Long orderId, Long orderLineId, String orderLineScenario, String tags) {
        JSONObject req = new JSONObject();
        req.put("buyerId", buyerId);

        OrderFeatures orderLineScene = getOrderLineScene(orderLineScenario, tags, orderLineId);
        Map<String, String> orderLineFeatures = orderLineScene.getOrderFeatures();

        req.put("scene", orderLineScene.getScene());

        JSONArray orderLines = new JSONArray();

        JSONObject orderLine = new JSONObject();
        orderLine.put("orderLineId", orderLineId);
        Map<String, String> features = Maps.newConcurrentMap();
        for (String key : orderLineFeatures.keySet()) {
            features.put(key, orderLineFeatures.get(key));
        }
        orderLine.put("features", features);
        orderLines.add(orderLine);

        req.put("orderLines", orderLines);

        JSONArray orders = new JSONArray();
        JSONObject order = new JSONObject();
        order.put("orderId", orderId);
        orders.add(order);

        req.put("orders", orders);
        return req;
    }

    private OrderFeatures getOrderLineScene(String scenario, String tags, Long orderLineId) {
        OrderFeatures orderFeatures = new OrderFeatures();
        Map<String, String> orderLineFeatureMap = Maps.newConcurrentMap();
        switch (scenario) {
            case "邀约供货价":
            case "供货价":
                orderFeatures.setScene("invitationProcurementPriceTest");
                if (StringUtils.isNotBlank(getPPOC(orderLineId, scenario, tags))) {
                    orderLineFeatureMap.put("p_p_o_c", getPPOC(orderLineId, scenario, tags));
                }
                break;
        }
        if (CollectionUtils.isNotEmpty(Collections.singleton(orderLineFeatureMap))) {
            orderFeatures.setOrderFeatures(orderLineFeatureMap);
        }
        return orderFeatures;
    }

    private String getPPOC(Long orderLineId, String scenario, String amount) {
        Response<TradeOrderLineDTO> tradeOrderLineDTOResponse = orderQueryForBuyerFacade.queryTradeOrderLineById(orderLineId);
        String[] prices = amount.split("_");
        if (tradeOrderLineDTOResponse.isSuccess() && tradeOrderLineDTOResponse.getModule() != null) {
            String price = prices[0];
            String currencyCode = prices.length > 1 ? prices[1] :
                    tradeOrderLineDTOResponse.getModule().getUnitFee().getCurrency().getCurrencyCode();
            if (StringUtils.isNotBlank(tradeOrderLineDTOResponse.getModule().getFeatures().getFeature("p_p_o_c"))) {
                String feature = tradeOrderLineDTOResponse.getModule().getFeatures().getFeature("p_p_o_c");
                JSONObject ppoc = JSONObject.parseObject(feature);
                if ("邀约供货价".equals(scenario)) {
                    JSONObject invitationProcurementPrice = new JSONObject();
                    invitationProcurementPrice.put("currencyCode", currencyCode);
                    invitationProcurementPrice.put("amount", Long.valueOf(price));
                    ppoc.put("invitationProcurementPrice", invitationProcurementPrice);
                } else {
                    ppoc.getJSONObject("productSupplyCost").put("amount", Long.valueOf(price));
                    ppoc.getJSONObject("productSupplyCost").put("currencyCode", currencyCode);
                }
                return ppoc.toJSONString();
            } else {
                JSONObject ppoc = JSONObject.parseObject(Constant.P_P_O_C);
                ppoc.getJSONObject("productSupplyCost").put("currencyCode", currencyCode);
                ppoc.getJSONObject("productLogisticsCost").put("currencyCode", currencyCode);
                ppoc.getJSONObject("productOperatingCost").put("currencyCode", currencyCode);
                ppoc.getJSONObject("productMarketingCost").put("currencyCode", currencyCode);
                ppoc.getJSONObject("productMarkUpCost").put("currencyCode", currencyCode);
                ppoc.getJSONObject("productDdpCost").put("currencyCode", currencyCode);
                ppoc.getJSONObject("invitationProcurementPrice").put("currencyCode", currencyCode);
                if ("邀约供货价".equals(scenario)) {
                    ppoc.getJSONObject("invitationProcurementPrice").put("amount", Long.valueOf(price));
                } else {
                    ppoc.getJSONObject("productSupplyCost").put("amount", Long.valueOf(price));
                }
                return ppoc.toJSONString();
            }
        }
        return null;
    }

    @Override
    public ResultDTO mockInbound(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        String po = jsonObject.getString(Constant.PO_ORDER);
        String wh = jsonObject.getString(Constant.WH_ORDER);
        SendResult result = mqConfig.sendMessage(Constant.AE_INBOUND_TOPIC, Constant.AE_INBOUND_TAG, wh, replaceInfo(po, wh, Constant.AE_INBOUND_MSG));
        resultDTO.setSuccess(true);
        resultDTO.setData(JSON.toJSONString(result));
        resultDTO.setMessage(JSON.toJSONString(result));
        return resultDTO;
    }

    @Override
    public ResultDTO manualCreateSnapshot(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        String orderIds = jsonObject.getString(Constant.ORDERS);
        String[] orderIdlist = StringUtils.split(orderIds, ",");
        List<Long> orders = new ArrayList<>();
        for (int i = 0; i < orderIdlist.length; i++) {
            orders.add(Long.parseLong(orderIdlist[i]));
        }

        JSONArray result = HsfUtil.manualCreateSnapshot(orders);
        resultDTO.setSuccess(true);
        resultDTO.setData(JSON.toJSONString(result));
        resultDTO.setMessage(JSON.toJSONString(result));
        return resultDTO;

    }


    @Override
    public ResultDTO mockCodDelivered(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        String orderId = jsonObject.getString(Constant.AE_COD_ORDERLIST);
        String type = jsonObject.getString(Constant.AE_COD_SHIP_type);
        String dpath = jsonObject.getString(Constant.DPATH_ENV);
        if (StringUtil.isNotBlank(dpath)) {
            EagleEye.putUserData("dpath_env", dpath);
        }
        if (type.matches("发货")) {
            JSONObject msg = buildShippedMsgObj(orderId);
            log.warn("mockCodDelivered_发货_{}", msg.toJSONString());
            SendResult result = mqConfig.sendMessage(Constant.AE_COD_SHIPPED_TOPIC, Constant.AE_COD_SHIPPED_TAG, orderId, msg.toJSONString());
            resultDTO.setSuccess(true);
            resultDTO.setData(JSON.toJSONString(result));
            resultDTO.setMessage(JSON.toJSONString(result));

        }
        if (type.matches("妥投")) {
            JSONObject msg2 = buildDeliverdMsgObj(orderId);
            log.warn("mockCodDelivered_妥投_{}", msg2.toJSONString());
            SendResult result = mqConfig.sendMessage(Constant.AE_COD_DELIVERED_TOPIC, Constant.AE_COD_DELIVERED_TAG, orderId, msg2.toJSONString());
            resultDTO.setSuccess(true);
            resultDTO.setData(JSON.toJSONString(result));
            resultDTO.setMessage(JSON.toJSONString(result));
        }

        return resultDTO;
    }


    private JSONObject buildDeliverdMsgObj(String orderId) throws Exception {
        JSONObject msg = new JSONObject();
        msg.put("dateTimeHappened", System.currentTimeMillis());
        msg.put("fulfillmentOrderId", "FO2551512661824002");
        msg.put("tradeOrderId", orderId);
        msg.put("srcOrderStatus", "900");
        msg.put("targetOrderStatus", "900");
        JSONObject orderObj = HsfUtil.queryTradeOrderById(Long.parseLong(orderId));
        log.warn("queryTradeOrderById param:{}, result:{}", orderId, JSON.toJSONString(orderObj));
        JSONArray orderLines = orderObj.getJSONObject("module").getJSONArray("orderLines");
        String sellerId = orderLines.getJSONObject(0).getJSONObject("seller").getString("sellerId");
        msg.put("sellerId", sellerId);
        String buyerId = orderLines.getJSONObject(0).getJSONObject("buyer").getString("buyerId");
        msg.put("buyerId", buyerId);

        JSONArray orderItemStatusList = new JSONArray();
        for (int i = 0; i < orderLines.size(); i++) {
            JSONObject orderLine = orderLines.getJSONObject(i);
            String orderLineId = orderLine.getString("tradeOrderLineId");
//            itemStatus.put("fulfillmentOrderItemStatusUpdatedList", buildfulfillmentOrderItemStatusUpdated(orderLineId));
            orderItemStatusList.add(buildfulfillmentOrderItemStatusUpdated(orderLineId));
        }
        msg.put("fulfillmentOrderItemStatusUpdatedList", orderItemStatusList);
        return msg;
    }

    private JSONObject buildfulfillmentOrderItemStatusUpdated(String orderItemId) {
        JSONObject itemStatus = new JSONObject();
        itemStatus.put("fulfillmentOrderItemId", "FO25" + orderItemId);
        itemStatus.put("tradeOrderItemId", orderItemId);
        itemStatus.put("srcStatus", "900");
        itemStatus.put("targetStatus", "900");
        itemStatus.put("reason", "repush latest status");
        return itemStatus;
    }


    private JSONObject buildShippedMsgObj(String orderId) {
        JSONObject msg = new JSONObject();
        String[] orderIdArray = StringUtils.split(orderId, ",");
        List<String> orderIdList = Arrays.asList(orderIdArray);
        msg.put("logisticsPartnerOrderNo", "LP00653412107281");
        msg.put("batchTradeGroup", orderId);
        msg.put("fulfillmentOrderList", buildFulfillmentOrderList(orderIdList));
        return msg;
    }

    private JSONArray buildFulfillmentOrderList(List<String> orderIdList) {
        JSONArray fulfillmentOrderList = new JSONArray();
        try {
            for (String orderId : orderIdList) {
                JSONObject orderObj = HsfUtil.queryTradeOrderById(Long.parseLong(orderId));
                log.warn("queryTradeOrderById param:{}, result:{}", orderId, JSON.toJSONString(orderObj));
                if (orderObj == null) {
                    log.warn("queryTradeOrderById result is null. orderId:{}", orderId);
                    continue;
                }
                JSONObject order = buildFulfillmentOrder(orderId, orderObj);
                fulfillmentOrderList.add(order);
            }
        } catch (Exception e) {
            log.error("buildFulfillmentOrderList fail.", e);
        }
        return fulfillmentOrderList;
    }

    private JSONObject buildFulfillmentOrder(String orderId, JSONObject orderObj) {
        JSONObject order = new JSONObject();
        order.put("dateTimeHappened", System.currentTimeMillis());
        order.put("fulfillmentOrderId", "FO2551512661824002");
        order.put("siteId", "GLOBAL");
        order.put("srcOrderStatus", "510");
        order.put("targetOrderStatus", "510");
//        order.put("outSubBizId", orderId);
        order.put("outBizId", "Af956766e-5c31-474a-b38a-1844bf9fcba" + System.currentTimeMillis());
        JSONArray orderLines = orderObj.getJSONObject("module").getJSONArray("orderLines");
        JSONArray orderItemStatusList = new JSONArray();
        for (int i = 0; i < orderLines.size(); i++) {
            JSONObject orderLine = orderLines.getJSONObject(i);
            String sellerId = orderLine.getJSONObject("seller").getString("sellerId");
            order.put("sellerId", sellerId);
            String orderItemId = orderLine.getString("tradeOrderLineId");
            order.put("tradeOrderId", orderId);
            String buyerId = orderLine.getJSONObject("buyer").getString("buyerId");
            order.put("buyerId", buyerId);
            JSONObject orderItemStatus = buildFulfillmentOrderItemStatusUpdated(orderId, sellerId, orderItemId);
            orderItemStatusList.add(orderItemStatus);
        }
        order.put("fulfillmentOrderItemStatusUpdatedList", orderItemStatusList);
        return order;
    }


    private JSONObject buildFulfillmentOrderItemStatusUpdated(String orderId, String sellerId, String orderItemId) {
        JSONObject itemStatus = new JSONObject();
        itemStatus.put("fulfillmentOrderItemId", "FO2551513544499002");
        itemStatus.put("srcStatus", "510100");
        itemStatus.put("targetStatus", "700");
        itemStatus.put("outSubBizId", orderId);
        itemStatus.put("tradeOrderItemId", orderItemId);
        itemStatus.put("sellerId", sellerId);
        return itemStatus;
    }

    private String replaceCodOrderInfo(String orderId, String aeDeliverdMsg) {
        JSONObject jsonObject = JSONObject.parseObject(aeDeliverdMsg);
        jsonObject.put("gmtModified", System.currentTimeMillis());
        jsonObject.put("tradeOrderId", orderId);
        return jsonObject.toJSONString();
    }

    private String replaceOrderInfo(String orderId, String aeDeliverdMsg) {
        JSONObject jsonObject = JSONObject.parseObject(aeDeliverdMsg);
        jsonObject.put("gmtModified", System.currentTimeMillis());
        jsonObject.put("tradeOrderId", orderId);
        return jsonObject.toJSONString();
    }

    @Override
    public ResultDTO resumeCancelOrder(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long orderId = jsonObject.getLong(Constant.ORDER_ID);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        CancelOrderOperatorRequest cancelOrderOperatorRequest = reverseService.getCancelOrderRequest(buyerId, orderId, "buyerCancel", null, "");
        LandlordContext.setTenantSpec("AE_GLOBAL", -1);
        PlainResult<IssueCancelOrderOperatorResult> res = cancelOrderWriteFacade.stopCancelOrderIssue(buyerId, "", cancelOrderOperatorRequest);
        resultDTO.setSuccess(res.isSuccess());
        if (res.isSuccess()) {
            resultDTO.setMessage(JSONObject.toJSONString(res.getData()));
            resultDTO.setData(JSONObject.toJSONString(res.getData()));
        } else {
            resultDTO.setMessage(JSONObject.toJSONString(res.getErrorCode()) + JSONObject.toJSONString(res.getErrorMessage()));
            resultDTO.setData(JSONObject.toJSONString(res.getErrorCode()) + JSONObject.toJSONString(res.getErrorMessage()));
        }
        return resultDTO;
    }

    @Override
    public ResultDTO updateOrderFeatures(String params, SystemDTO systemDTO) throws Exception {
        String empId = systemDTO.getOperator();
        ResultDTO resultDTO = new ResultDTO();
        JSONObject param = JSONObject.parseObject(params);
        Long orderId = param.getLong(Constant.ORDER_ID);
        Long buyerId = param.getLong(Constant.BUYER_ID);
        String tags = param.getString(Constant.TAGS);
        String scenario = param.getString(Constant.ORDER_SCENARIO);
        JSONObject scenarioReq = getScenario(orderId, scenario, tags);
        JSONObject result = HsfUtil.updateOrderLineFeatures(buyerId, scenarioReq);
        resultDTO.setSuccess(true);
        resultDTO.setData("执行结果: " + result.toJSONString());
        resultDTO.setMessage("执行结果: " + result.toJSONString());
        HsfUtil.measureAll("/trade/jobId=" + systemDTO.getSite(), empId);
        return resultDTO;
    }

    @Override
    public ResultDTO updateOrdersFeatures(String params, SystemDTO systemDTO) throws Exception {
        String empId = systemDTO.getOperator();
        ResultDTO resultDTO = new ResultDTO();
        JSONObject param = JSONObject.parseObject(params);
        String orders = param.getString(Constant.ORDERS);
        Long buyerId = param.getLong(Constant.BUYER_ID);
        String scenario = param.getString(Constant.ORDER_SCENARIO);
        String[] orderId = orders.split(",");
        JSONObject result = null;
        for (int i = 0; i < orderId.length; i++) {
            JSONObject scenarioReq = getScenarioForOrders(Long.parseLong(orderId[i]), orders, scenario);
            result = HsfUtil.updateOrderLineFeatures(buyerId, scenarioReq);
        }
        resultDTO.setSuccess(true);
        resultDTO.setData("执行结果: " + result.toJSONString());
        resultDTO.setMessage("执行结果: " + result.toJSONString());
        HsfUtil.measureAll("/trade/jobId=" + systemDTO.getSite(), empId);
        return resultDTO;
    }

    @Override
    public ResultDTO sendMsg(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject param = JSONObject.parseObject(params);
        Long orderId = param.getLong(Constant.ORDER_ID);
        String propertiesFromInput = param.getString(Constant.PROPERTIES);
        String msgBody = param.getString(Constant.MSG_BODY);
        String msgTopic = param.getString(Constant.MSG_TOPIC);
        String msgTag = param.getString(Constant.MSG_TAG);
        String dpath = param.getString(Constant.DPATH_ENV);
        if (StringUtil.isNotBlank(dpath)) {
            EagleEye.putUserData("dpath_env", dpath);
        }
        LandlordContext.setTenantSpec("AE_GLOBAL", -1);
        Map<String, String> properties = getProperties(propertiesFromInput);
        SendResult sendResult = mqConfig.sendMessage(msgTopic, msgTag, orderId.toString(), properties, msgBody);
        resultDTO.setSuccess(true);
        resultDTO.setMessage(JSON.toJSONString(sendResult));
        resultDTO.setData(JSON.toJSONString(sendResult));
        return resultDTO;
    }

    private Map<String, String> getProperties(String propertiesFromInput) {
        Map<String, String> properties = Maps.newConcurrentMap();
        switch (propertiesFromInput) {
            case "M2A":
                properties.put("aidc_market_name", "miravia");
                break;
        }
        return properties;
    }

    @Override
    public ResultDTO sendLandloardMsg(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject param = JSONObject.parseObject(params);
        Long orderId = param.getLong(Constant.ORDER_ID);
        String msgBody = param.getString(Constant.MSG_BODY);
        String msgTopic = param.getString(Constant.MSG_TOPIC);
        String msgTag = param.getString(Constant.MSG_TAG);
        String dpath = param.getString(Constant.DPATH_ENV);
        if (StringUtil.isNotBlank(dpath)) {
            EagleEye.putUserData("dpath_env", dpath);
        }
        LandlordContext.setTenantSpec("AE_GLOBAL", -1);
        SendResult sendResult = mqConfig.sendLandlordMessage(msgTopic, msgTag, orderId.toString(), msgBody);
        resultDTO.setSuccess(true);
        resultDTO.setMessage(JSON.toJSONString(sendResult));
        resultDTO.setData(JSON.toJSONString(sendResult));
        return resultDTO;
    }

    @Override
    public ResultDTO codPrePaymentOrderFail(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        try {
            JSONObject param = JSONObject.parseObject(params);
            String orders = param.getString(Constant.ORDERS);
            Long buyerId = param.getLong(Constant.BUYER_ID);
            String msgBody = getPrePayMessage(buyerId, orders);
            SendResult sendResult = mqConfig.sendMessage(Constant.PAYMENT_TOPIC, Constant.PRE_PAY, orders, msgBody);
            resultDTO.setMessage(JSON.toJSONString(sendResult));
            resultDTO.setData(JSON.toJSONString(sendResult));
        } catch (Exception e) {
            resultDTO.setMessage(JSON.toJSONString(e));
            resultDTO.setData(JSON.toJSONString(e));
        }
        return resultDTO;
    }

    private String getPrePayMessage(Long buyerId, String orders) {
        JSONObject msg = JSONObject.parseObject(Constant.PRE_PAY_MSG);
        msg.put("buyerId", buyerId);
        List<String> orderList = Arrays.asList(orders.split(","));
        msg.put("bizOrderNo", JSONObject.toJSONString(orderList));
        msg.put("prePayFinishTimeStamp", System.currentTimeMillis());
        return msg.toJSONString();
    }


    private JSONObject getScenario(Long tradeOrderId, String scenario, String tags) {
        JSONObject req = new JSONObject();
        req.put("tradeOrderId", tradeOrderId);
        JSONObject features = new JSONObject();
        Map<String, String> featureMap = Maps.newHashMap();
        switch (scenario) {
            case "举手时间":
                JSONObject sellerTag = new JSONObject();
                sellerTag.put("GS_JIT_ON_VACATION", tags);
                featureMap.put("sellerTag", sellerTag.toString());
                features.put("featureMap", featureMap);
                req.put("features", features);
                break;
            case "拼团一代-待支付":
                featureMap.put("shareGroup", "1");
                featureMap.put("hold", "hareGroup^c^172800^1");
                features.put("featureMap", featureMap);
                req.put("features", features);
                break;
            case "拼团一代-hold单":
                featureMap.put("shareGroup", "1");
                featureMap.put("hold", "hareGroup^c^172800^2");
                features.put("featureMap", featureMap);
                req.put("features", features);
                break;
            case "拼团一代-解hold单":
                featureMap.put("shareGroup", "1");
                featureMap.put("hold", "hareGroup^c^172800^9");
                features.put("featureMap", featureMap);
                req.put("features", features);
                break;
            case "拼团三代-待支付":
                featureMap.put("shareGroup", "3");
                featureMap.put("hold", "hareGroup^c^172800^1");
                features.put("featureMap", featureMap);
                req.put("features", features);
                break;
            case "拼团三代-hold单":
                featureMap.put("shareGroup", "3");
                featureMap.put("hold", "hareGroup^c^172800^2");
                features.put("featureMap", featureMap);
                req.put("features", features);
                break;
            case "拼团三代-解hold单":
                featureMap.put("shareGroup", "3");
                featureMap.put("hold", "hareGroup^c^172800^9");
                features.put("featureMap", featureMap);
                req.put("features", features);
                break;
            case "COD":
                featureMap.put("COD_V2", "1");
                features.put("featureMap", featureMap);
                req.put("features", features);
                break;
            case "夺宝订单":
                featureMap.put("raffle", "1");
                featureMap.put("hold", "raffle^r^604800^2");
                features.put("featureMap", featureMap);
                req.put("features", features);
                break;
            case "夺宝虚拟品订单":
                featureMap.put("raffle", "1");
                featureMap.put("hold", "raffle^r^604800^2");
                featureMap.put("simpleVirtual", "1");
                features.put("featureMap", featureMap);
                req.put("features", features);
                break;
            case "夺宝订单中奖":
                featureMap.put("raffleStatus", "1");
                features.put("featureMap", featureMap);
                req.put("features", features);
                break;
            case "x日达晚必赔被动赔付":
                JSONObject xDayDelayPassiveCompensation = new JSONObject();
                xDayDelayPassiveCompensation.put("delayCompensation", true);
                xDayDelayPassiveCompensation.put("delayCompensationAmount", "1");
                xDayDelayPassiveCompensation.put("delayCompensationCurrencyCode", "USD");
                xDayDelayPassiveCompensation.put("delayCompensationMode", "passive");
                xDayDelayPassiveCompensation.put("delayCompensationType", "xday");
                featureMap.put("delayCompensation", xDayDelayPassiveCompensation.toJSONString());
                features.put("featureMap", featureMap);
                req.put("features", features);
                break;
            case "线路晚必赔被动赔付":
                JSONObject deliveryDelayPassiveCompensation = new JSONObject();
                deliveryDelayPassiveCompensation.put("delayCompensation", true);
                deliveryDelayPassiveCompensation.put("delayCompensationAmount", "1");
                deliveryDelayPassiveCompensation.put("delayCompensationCurrencyCode", "USD");
                deliveryDelayPassiveCompensation.put("delayCompensationMode", "passive");
                deliveryDelayPassiveCompensation.put("delayCompensationType", "delivery");
                featureMap.put("delayCompensation", deliveryDelayPassiveCompensation.toJSONString());
            case "用增标":
                JSONObject bCrowdTag = new JSONObject();
                bCrowdTag.put("oneshop_b_tag_core", "-25932");
                featureMap.put("bCrowdTag", bCrowdTag.toJSONString());
                features.put("featureMap", featureMap);
                req.put("features", features);
            case "线路晚必赔":
                JSONObject deliveryDelayCompensation = new JSONObject();
                deliveryDelayCompensation.put("delayCompensation", true);
                deliveryDelayCompensation.put("delayCompensationAmount", "1");
                deliveryDelayCompensation.put("delayCompensationCurrencyCode", "USD");
                deliveryDelayCompensation.put("delayCompensationMode", "passive");
                deliveryDelayCompensation.put("delayCompensationType", "delivery");
                featureMap.put("delayCompensation", deliveryDelayCompensation.toJSONString());
                features.put("featureMap", featureMap);
                req.put("features", features);
                break;
            case "x日达晚必赔主动赔付":
                JSONObject xDayDelayActiveCompensation = new JSONObject();
                xDayDelayActiveCompensation.put("delayCompensation", true);
                xDayDelayActiveCompensation.put("delayCompensationAmount", "1");
                xDayDelayActiveCompensation.put("delayCompensationCurrencyCode", "USD");
                xDayDelayActiveCompensation.put("delayCompensationMode", "active");
                xDayDelayActiveCompensation.put("delayCompensationType", "xday");
                featureMap.put("delayCompensation", xDayDelayActiveCompensation.toJSONString());
                features.put("featureMap", featureMap);
                req.put("features", features);
                break;
            case "线路晚必赔主动赔付":
                JSONObject deliveryDelayActiveCompensation = new JSONObject();
                deliveryDelayActiveCompensation.put("delayCompensation", true);
                deliveryDelayActiveCompensation.put("delayCompensationAmount", "1");
                deliveryDelayActiveCompensation.put("delayCompensationCurrencyCode", "USD");
                deliveryDelayActiveCompensation.put("delayCompensationMode", "active");
                deliveryDelayActiveCompensation.put("delayCompensationType", "delivery");
                featureMap.put("delayCompensation", deliveryDelayActiveCompensation.toJSONString());
                features.put("featureMap", featureMap);
                req.put("features", features);
                break;
            case "晚必赔去标":
                JSONObject delayCompensation = new JSONObject();
                delayCompensation.put("delayCompensation", false);
                featureMap.put("delayCompensation", delayCompensation.toJSONString());
                features.put("featureMap", featureMap);
                req.put("features", features);
                break;
            case "新服务标":
                featureMap.put("promiseVersion", "2");
                features.put("featureMap", featureMap);
                req.put("features", features);
                break;
            case "新服务去标":
                featureMap.put("promiseVersion", "0");
                features.put("featureMap", featureMap);
                req.put("features", features);
                break;
            case "X天未妥投超时标":
                featureMap.put("delivery_failed_type", "1");
                features.put("featureMap", featureMap);
                req.put("features", features);
                break;
            case "物流妥投异常标":
                featureMap.put("delivery_failed_type", "2");
                features.put("featureMap", featureMap);
                req.put("features", features);
                break;
            case "美国新订单标":
                featureMap.put("us_new", "1");
                features.put("featureMap", featureMap);
                req.put("features", features);
                break;
            case "半托管转POP-hold单":
                featureMap.put("s_s", "1");
                features.put("featureMap", featureMap);
                req.put("features", features);
                break;
            case "半托管转POP-解hold单":
                featureMap.put("hold", "semi2Pop^r^86400^2");
                features.put("featureMap", featureMap);
                req.put("features", features);
                break;
            case "POP自发货":
                featureMap.put("s_s_r", "POP");
                features.put("featureMap", featureMap);
                req.put("features", features);
                break;
            case "M2A":
                featureMap.put("m2a", "1");
                features.put("featureMap", featureMap);
                req.put("features", features);
                break;
            case "magalu2Ae":
                featureMap.put("sellerTag", "{\"brazil_local_platform\":\"magalu\"}");
                featureMap.put("market", "magalu");
                features.put("featureMap", featureMap);
                req.put("features", features);
                break;
            case "飞猪订单标":
                featureMap.put("aidc_market_name", "fliggy");
                features.put("featureMap", featureMap);
                req.put("features", features);
                break;
            case "Trendyol2AE":
                featureMap.put("sellerTag", "{\"oversea_seller_location\":\"CN\",\"trendyol_to_ae_whitelist\":\"true\"}");
                features.put("featureMap", featureMap);
                req.put("features", features);
                break;
            case "drawback标":
                featureMap.put("drawback", tags);
                break;
            case "M招商商家标":
                featureMap.put("sellerTag", "{\"oversea_seller_location\":\"IT\",\"GS_gsp_miravia_bdsource\":\"314735\"}");
                features.put("featureMap", featureMap);
                req.put("features", features);
                break;
        }
        features.put("featureMap", featureMap);
        req.put("features", features);
        return req;
    }

    private JSONObject getScenarioForOrders(Long tradeOrderId, String orders, String scenario) {
        JSONObject req = new JSONObject();
        req.put("tradeOrderId", tradeOrderId);
        req.put("OrderIds", orders);
        JSONObject features = new JSONObject();
        Map<String, String> featureMap = Maps.newHashMap();
        switch (scenario) {
            case "订单组":
                featureMap.put("o_g", "nn|" + orders);
                features.put("featureMap", featureMap);
                req.put("features", features);
                break;
        }
        return req;
    }

    private String replaceInfo(String po, String wh, String aeInboundMsg) {
        JSONObject jsonObject = JSONObject.parseObject(aeInboundMsg);
        jsonObject.put("gmtModified", System.currentTimeMillis());
        jsonObject.put("purchaseOrderNo", po);
        jsonObject.put("purchaseRequirementOrderNo", wh);
        return jsonObject.toJSONString();
    }

    private static JSONObject getModifyOrderInfoReq(Long buyerId, Long orderId, List<Long> orderLineIds, String orderLineScenario, String tags) {
        JSONObject req = new JSONObject();
        req.put("buyerId", buyerId);

        OrderFeatures orderLineScene = getOrderLineScene(orderLineScenario, tags);
        Map<String, String> orderLineFeatures = orderLineScene.getOrderFeatures();

        req.put("scene", orderLineScene.getScene());

        JSONArray orderLines = new JSONArray();
        for (Long orderLineId : orderLineIds) {
            JSONObject orderLine = new JSONObject();
            orderLine.put("orderLineId", orderLineId);
            if (StringUtils.isNotBlank(orderLineScenario)) {
                Map<String, String> features = Maps.newConcurrentMap();
                for (String key : orderLineFeatures.keySet()) {
                    features.put(key, orderLineFeatures.get(key));
                }
                orderLine.put("features", features);
            }
            orderLines.add(orderLine);
        }
        req.put("orderLines", orderLines);
        JSONArray orders = new JSONArray();
        JSONObject order = new JSONObject();
        order.put("orderId", orderId);
        orders.add(order);
        req.put("orders", orders);
        return req;
    }

    private static OrderFeatures getOrderLineScene(String scenario, String tags) {
        OrderFeatures orderFeatures = new OrderFeatures();
        Map<String, String> orderLineFeatureMap = Maps.newConcurrentMap();
        switch (scenario) {
            case "重大件":
                orderFeatures.setScene("canBeFulfilledTest");
                orderLineFeatureMap.put("canBeFulfilled", "true");
                break;
            case "商品报价":
                orderFeatures.setScene("oipTest");
                orderLineFeatureMap.put("oip", tags);
                break;
            case "沙特商超":
                orderFeatures.setScene("saMartTest");
                orderLineFeatureMap.put("sellerRegion", "SA");
                orderLineFeatureMap.put("sendGoodsCountryCode", "SA");
                orderLineFeatureMap.put("business_code", "mart");
                break;
            case "拼团一代-待支付":
                orderFeatures.setScene("shareGroupTest");
                orderLineFeatureMap.put("shareGroup", "1");
                orderLineFeatureMap.put("hold", "shareGroup^r^86400^1");
                break;
            case "拼团一代-grouping":
                orderFeatures.setScene("shareGroupTest");
                orderLineFeatureMap.put("shareGroup", "1");
                orderLineFeatureMap.put("hold", "shareGroup^r^86400^2");
                break;
            case "拼团一代-成团":
                orderFeatures.setScene("shareGroupTest");
                orderLineFeatureMap.put("shareGroup", "1");
                orderLineFeatureMap.put("hold", "shareGroup^r^86400^9");
                break;
            case "拼团三代-待支付":
                orderFeatures.setScene("shareGroupTest");
                orderLineFeatureMap.put("shareGroup", "3");
                orderLineFeatureMap.put("hold", "shareGroup^r^86400^1");
                break;
            case "拼团三代-grouping":
                orderFeatures.setScene("shareGroupTest");
                orderLineFeatureMap.put("shareGroup", "3");
                orderLineFeatureMap.put("hold", "shareGroup^r^86400^2");
                break;
            case "拼团三代-成团":
                orderFeatures.setScene("shareGroupTest");
                orderLineFeatureMap.put("shareGroup", "3");
                orderLineFeatureMap.put("hold", "shareGroup^r^86400^9");
                break;
            case "发货合格时效":
                orderFeatures.setScene("reverse-service");
                JSONObject data = new JSONObject();
                JSONObject scd = new JSONObject();
                scd.put("timeoutType", 48);
                scd.put("qualifiedTime", Long.valueOf(tags));
                data.put("s_c_d", scd);
                orderLineFeatureMap.put("rs", data.toJSONString());
                break;
            case "定制商品发货合格时效":
                orderFeatures.setScene("reverse-service");
                JSONObject json = new JSONObject();
                JSONObject scdContent = new JSONObject();
                scdContent.put("timeoutType", -1);
                scdContent.put("qualifiedTime", Long.valueOf(tags));
                scdContent.put("customizeGood", true);
                json.put("s_c_d", scdContent);
                orderLineFeatureMap.put("rs", json.toJSONString());
                break;
            case "发货国":
                orderFeatures.setScene("sendGoodsCountryCodeTest");
                orderLineFeatureMap.put("sendGoodsCountryCode", tags);
                break;
            case "仓code":
                orderFeatures.setScene("wcTest");
                orderLineFeatureMap.put("wc", tags);
                break;
            case "召回商品":
                orderFeatures.setScene("CRORiskControl");
                orderLineFeatureMap.put("CROProductSafetyRecallOrder", "true");
                break;
            case "服务模版":
                orderFeatures.setScene("promiseTemplateTest");
                if (StringUtils.isNotBlank(tags)) {
                    orderLineFeatureMap.put("promiseTemplate", getPromiseTemplates(tags));
                }
                break;
            case "sub":
                orderFeatures.setScene("promiseTemplateTest");
                if (StringUtils.isNotBlank(tags)) {
                    orderLineFeatureMap.put("promiseTemplate", getPromiseTemplates(tags));
                }
                break;
            case "上网时效考核":
                orderFeatures.setScene("reverse-service");
                if (StringUtils.isNotBlank(tags)) {
                    orderLineFeatureMap.put("rs", getInternetAccess(tags).toString());
                }
                break;
            case "渠道":
                orderFeatures.setScene("NXTest");
                if (StringUtils.isNotBlank(tags)) {
                    orderLineFeatureMap.put("sourceType", tags);
                }
                break;
            case "自营":
                orderFeatures.setScene("selfShopTest");
                orderLineFeatureMap.put("s_s_b", "1");
                break;
            case "半托管":
                orderFeatures.setScene("orderBusinessModelTest");
                orderLineFeatureMap.put("o_b_m", "SEMI_CHOICE_SELF");
                break;
            case "全托管":
                orderFeatures.setScene("orderBusinessModelTest");
                orderLineFeatureMap.put("o_b_m", "CHOICE_SELF");
                break;
            case "本地托管":
                orderFeatures.setScene("orderBusinessModelTest");
                orderLineFeatureMap.put("o_b_m", "LOCAL_SERVICE_STORE_OWNED");
                break;
            case "全托管3PL":
                orderFeatures.setScene("choiceTplTest");
                orderLineFeatureMap.put("o_s_c_t_m", "CHOICE_TPL");
                break;
            case "盲盒":
                orderFeatures.setScene("blindBoxTest");
                orderLineFeatureMap.put("blindBox", "1");
                break;
            case "物流上网管控圈品":
                orderFeatures.setScene("logisticsOnlineControlTest");
                orderLineFeatureMap.put("l_o_c", "1");
                break;
            case "POP":
                orderFeatures.setScene("orderBusinessModelTest");
                orderLineFeatureMap.put("o_b_m", "UN_CHOICE");
                break;
            case "线路晚必赔":
                orderFeatures.setScene("featuresForTradeTest");
                orderLineFeatureMap.put("featuresForTrade", "delayCompensation#true|delayCompensationAmount#1|delayCompensationCurrencyCode#USD|delayCompensationType#delivery|delayCompensationMode#passive|delayCompensationTime#0|pretreatmentTag#seed_multiple_payments_upgrade_pop|plServiceCategory#4pl|logisticsDisplay#fast|composeTypeUpdate#multiple_payments_upgrade_pop");
                orderLineFeatureMap.put("tml", "{\"tradeOrderLineId\"#3A3034128471572202,\"leadingTime\"#3A{\"shippingProviderCode\"#3A\"EMS\",\"shippingProviderName\"#3A\"EMS\",\"deliveryMinTime\"#3A1714550061.765000000,\"deliveryMaxTime\"#3A1714550061.765000000,\"deliveryDate\"#3A\"2024-05-01\",\"freightCommitDay\"#3A75,\"platformWarehousePromiseTime\"#3A1713081261.765000000,\"featuresForTrade\"#3A\"delayCompensation#true|delayCompensationAmount#1300|delayCompensationCurrencyCode#KRW|delayCompensationType#delivery|delayCompensationMode#active|delayCompensationTime#0|plServiceCategory#3pl\"},\"deliveryType\"#3A\"EMS\",\"deliveryName\"#3A\"EMS\",\"solutionCode\"#3A\"EMS\",\"supportCod\"#3Afalse}");
                break;
            case "5日达晚必赔":
                orderFeatures.setScene("featuresForTradeTest");
                orderLineFeatureMap.put("featuresForTrade", "leadTimeTag#5|leadTimeType#self_wh|delayCompensation#true|delayCompensationAmount#1|delayCompensationCurrencyCode#USD|delayCompensationType#xday|delayCompensationMode#active|delayCompensationTime#0");
                orderLineFeatureMap.put("tml", "{\"tradeOrderLineId\"#3A3034128471572202,\"leadingTime\"#3A{\"shippingProviderCode\"#3A\"EMS\",\"shippingProviderName\"#3A\"EMS\",\"deliveryMinTime\"#3A1714550061.765000000,\"deliveryMaxTime\"#3A1714550061.765000000,\"deliveryDate\"#3A\"2024-05-01\",\"freightCommitDay\"#3A75,\"platformWarehousePromiseTime\"#3A1713081261.765000000,\"featuresForTrade\"#3A\"leadTimeTag#5|delayCompensation#true|delayCompensationAmount#1300|delayCompensationCurrencyCode#KRW|delayCompensationType#delivery|delayCompensationMode#active|delayCompensationTime#0|plServiceCategory#3pl\"},\"deliveryType\"#3A\"EMS\",\"deliveryName\"#3A\"EMS\",\"solutionCode\"#3A\"EMS\",\"supportCod\"#3Afalse}");
                break;
            case "7日达晚必赔":
                orderFeatures.setScene("featuresForTradeTest");
                orderLineFeatureMap.put("featuresForTrade", "leadTimeTag#7|leadTimeType#self_wh|delayCompensation#true|delayCompensationAmount#1|delayCompensationCurrencyCode#USD|delayCompensationType#xday|delayCompensationMode#active|delayCompensationTime#0");
                orderLineFeatureMap.put("tml", "{\"tradeOrderLineId\"#3A3034128471572202,\"leadingTime\"#3A{\"shippingProviderCode\"#3A\"EMS\",\"shippingProviderName\"#3A\"EMS\",\"deliveryMinTime\"#3A1714550061.765000000,\"deliveryMaxTime\"#3A1714550061.765000000,\"deliveryDate\"#3A\"2024-05-01\",\"freightCommitDay\"#3A75,\"platformWarehousePromiseTime\"#3A1713081261.765000000,\"featuresForTrade\"#3A\"leadTimeTag#7|delayCompensation#true|delayCompensationAmount#1300|delayCompensationCurrencyCode#KRW|delayCompensationType#delivery|delayCompensationMode#active|delayCompensationTime#0|plServiceCategory#3pl\"},\"deliveryType\"#3A\"EMS\",\"deliveryName\"#3A\"EMS\",\"solutionCode\"#3A\"EMS\",\"supportCod\"#3Afalse}");
                break;
            case "10日达晚必赔":
                orderFeatures.setScene("featuresForTradeTest");
                orderLineFeatureMap.put("featuresForTrade", "leadTimeTag#10|leadTimeType#self_wh|delayCompensation#true|delayCompensationAmount#1|delayCompensationCurrencyCode#USD|delayCompensationType#xday|delayCompensationMode#active|delayCompensationTime#0");
                orderLineFeatureMap.put("tml", "{\"tradeOrderLineId\"#3A3034128471572202,\"leadingTime\"#3A{\"shippingProviderCode\"#3A\"EMS\",\"shippingProviderName\"#3A\"EMS\",\"deliveryMinTime\"#3A1714550061.765000000,\"deliveryMaxTime\"#3A1714550061.765000000,\"deliveryDate\"#3A\"2024-05-01\",\"freightCommitDay\"#3A75,\"platformWarehousePromiseTime\"#3A1713081261.765000000,\"featuresForTrade\"#3A\"leadTimeTag#10|delayCompensation#true|delayCompensationAmount#1300|delayCompensationCurrencyCode#KRW|delayCompensationType#delivery|delayCompensationMode#active|delayCompensationTime#0|plServiceCategory#3pl\"},\"deliveryType\"#3A\"EMS\",\"deliveryName\"#3A\"EMS\",\"solutionCode\"#3A\"EMS\",\"supportCod\"#3Afalse}");
                break;
            case "入库单接单":
                orderFeatures.setScene("jitDncTest");
                orderLineFeatureMap.put("jit_dnc", "1");
                break;
            case "入库单未接单":
                orderFeatures.setScene("jitDncTest");
                orderLineFeatureMap.put("jit_dnc", "0");
                break;
            case "美国新订单":
                orderFeatures.setScene("usNewTest");
                orderLineFeatureMap.put("us_new", "1");
                break;
            case "X日达":
                orderFeatures.setScene("leadTimeTagTest");
                orderLineFeatureMap.put("leadTimeTag", StringUtils.isNoneBlank(tags) ? tags : "5");
                break;
            case "订单已妥投":
                orderFeatures.setScene("arrivedTest");
                orderLineFeatureMap.put("arrived", "1");
                break;
            case "DeliveryDays修改":
                orderFeatures.setScene("promiseTemplateTest");
                if (StringUtils.isNotBlank(tags)) {
                    orderLineFeatureMap.put("promiseTemplate", updateDeliveryDays(tags));
                }
                break;
            case "买N免X":
                orderFeatures.setScene("NXTest");
                orderLineFeatureMap.put("isFreeGift", "1");
                orderLineFeatureMap.put("sourceType", "690");
                break;
            case "NN买免":
                orderFeatures.setScene("NXTest");
                orderLineFeatureMap.put("isFreeGift", "1");
                break;
            case "X天未妥投申请入口":
                orderFeatures.setScene("reverse-deliveryFailed");
                orderLineFeatureMap.put("delivery_failed_type", "1");
                break;
            case "X天未妥投自动退":
                orderFeatures.setScene("reverse-service");
                JSONObject rs = new JSONObject();
                rs.put("s_c_a_r", "1");
                orderLineFeatureMap.put("rs", rs.toJSONString());
                break;
            case "定制商品":
                orderFeatures.setScene("customizeInfoIdTest");
                orderLineFeatureMap.put("customizeInfoId", "20000013001142");
                orderLineFeatureMap.put("item_tags", "[1243273]");
                break;
            case "备货期":
                orderFeatures.setScene("updateGoodsPrepareTime");
                orderLineFeatureMap.put("goodsPrepareTime", StringUtils.isNoneBlank(tags) ? tags : "8");
                break;
            case "M2A":
                orderFeatures.setScene("m2aOrderTag");
                orderLineFeatureMap.put("m2a", "1");
                orderLineFeatureMap.put("nrInitiateTime", StringUtils.isNoneBlank(tags) ? tags : "1721726463617");
                break;
            case "featuresForTrade":
                orderFeatures.setScene("featuresForTrade");
                orderLineFeatureMap.put("featuresForTrade", StringUtils.isNoneBlank(tags) ? tags : "leadTimeType#self_wh");
                break;
            case "价保":
                orderFeatures.setScene("ppInfoTest");
                orderLineFeatureMap.put("ppInfo", StringUtils.isNoneBlank(tags) ? tags : "1724223948844,1726815948844");
                break;
            case "飞猪酒店标":
                orderFeatures.setScene("fliggyTest");
                orderLineFeatureMap.put("fliggy_business_type", "hotel");
                break;
            case "飞猪机票标":
                orderFeatures.setScene("fliggyTest");
                orderLineFeatureMap.put("fliggy_business_type", "flight");
                break;
            case "飞猪度假标":
                orderFeatures.setScene("fliggyTest");
                orderLineFeatureMap.put("fliggy_business_type", "vacation");
                break;
            case "choice纠纷渲染freeReturn心智":
                orderFeatures.setScene("choiceFRIcons");
                orderLineFeatureMap.put("o_v", "1");
                orderLineFeatureMap.put("atmo", "CHOICE_DISPLAY");
                break;
            case "韩国mega":
                orderFeatures.setScene("megaTest");
                orderLineFeatureMap.put("business_code", "mega");
                break;
            case "虚拟订单":
                orderFeatures.setScene("simpleVirtualTest");
                orderLineFeatureMap.put("simpleVirtual", "1");
             //   orderLineFeatureMap.put("virtual_product", "1");
                break;
            case "菜鸟标准线路":
                orderFeatures.setScene("logisticsTypeTest");
                orderLineFeatureMap.put("logisticsType", "CAINIAO_STANDARD");
                break;
            case "纯电不可承运":
                orderFeatures.setScene("canNotBeFulfilledTest");
                orderLineFeatureMap.put("canBeFulfilled", "false");
                orderLineFeatureMap.put("usrc", "ELECTRIC_ITEM_NOT_SUPPORT");
                break;
            case "不可承运原因":
                orderFeatures.setScene("canNotBeFulfilledTest");
                orderLineFeatureMap.put("canBeFulfilled", "false");
                orderLineFeatureMap.put("usrc", tags);
                break;
            case "海外发货认证仓wtn":
                orderFeatures.setScene("wtnTest");
                orderLineFeatureMap.put("wtn", "ae_own_warehouse");
                break;
            case "wt":
                orderFeatures.setScene("wtTest");
                orderLineFeatureMap.put("wt", "ae_own_warehouse");
                break;
            case "wc":
                orderFeatures.setScene("wcTest");
                orderLineFeatureMap.put("wc", "ae_own_warehouse");
                break;
                
        }
        if (CollectionUtils.isNotEmpty(Collections.singleton(orderLineFeatureMap))) {
            orderFeatures.setOrderFeatures(orderLineFeatureMap);
        }
        return orderFeatures;
    }


    // [2,{"id":17,"valueMaps":{"cj":true}},{"id":24,"valueMaps":{}}]
    public static String getPromiseTemplates(String tags) {
        List<Object> promiseTemplates = Lists.newArrayList();
        promiseTemplates.add(2);
        String[] tagArr = tags.split(",");
        if (tagArr.length > 0) {
            for (String tag : tagArr) {
                Map<String, Object> promiseTemplate = Maps.newConcurrentMap();
                Map<String, Object> valueMaps = Maps.newConcurrentMap();
                if (tag.contains("-") && tag.split("-").length == 2) {
                    String[] values = tag.split("-");
                    Long promiseId = Long.valueOf(values[0]);
                    promiseTemplate.put("id", promiseId);
                    if (promiseId == 28L) {
                        valueMaps.put("ab", values[1]);
                    }
                    if (promiseId == 31L || promiseId == 32L || promiseId == 33L) {
                        valueMaps.put("status", values[1]);
                    }
                    if (promiseId == 27L) {
                        if ("displayDays".equals(values[1])) {
                            valueMaps.put(values[1], 90);
                        } else {
                            valueMaps.put(values[1], 1);
                        }
                    }
                } else if (tag.contains("-") && tag.split("-").length == 3) {
                    String[] values = tag.split("-");
                    Long promiseId = Long.valueOf(values[0]);
                    promiseTemplate.put("id", promiseId);
                    Long fourPoint = Long.valueOf(values[1]);
                    Long status = Long.valueOf(values[2]);
                    if (promiseId == 33L) {
                        valueMaps.put("status", status);
                        valueMaps.put("contentShowType", "dynamic");
                        valueMaps.put("deliveryDays", 30);
                        if (fourPoint == 1L) {
                            valueMaps.put("displayVersion", "fourPoint");
                        }
                        valueMaps.put("currency", "KRW");
                        valueMaps.put("moneyFormat", "₩ 1,300");
                        valueMaps.put("mode", "passive");
                        valueMaps.put("amount", "1");
                        valueMaps.put("time", 0);
                        valueMaps.put("type", "delivery");
                    }
                } else {
                    Long promiseId = Long.valueOf(tag.trim());
                    if (promiseId == 28L) {
                        valueMaps.put("ab", "a");
                    }
                    if (promiseId == 31L) {
                        valueMaps.put("status", 0);
                    }
                    if (promiseId == 32L || promiseId == 33L) {
                        valueMaps.put("status", 0);
                        valueMaps.put("contentShowType", "dynamic");
                        valueMaps.put("deliveryDays", 6);
                        valueMaps.put("displayVersion", "fourPoint");
                        valueMaps.put("currency", "KRW");
                        valueMaps.put("moneyFormat", "₩ 1,300");
                        valueMaps.put("mode", "passive");
                        valueMaps.put("amount", "1");
                        valueMaps.put("time", 0);
                        valueMaps.put("type", "delivery");
                    }
                    if (promiseId == 35L) {
                        valueMaps.put("visible", "false");
                    }
                    promiseTemplate.put("id", promiseId);
                }
                if ("17".equalsIgnoreCase(tag.trim())) {
                    valueMaps.put("cj", true);
                }
                promiseTemplate.put("valueMaps", valueMaps);
                promiseTemplates.add(promiseTemplate);
            }
        }
        return JSONObject.toJSONString(promiseTemplates);
    }

    public static JSONObject getInternetAccess(String tags) {
        JSONObject js = new JSONObject();
        Map<String, Long> map = new HashMap<String, Long>();

        if (tags == null || tags.equals("")) {
            return new JSONObject();
        } else {
            String[] tagArr = tags.split(",");
            map.put("timeoutType", Long.parseLong(tagArr[0]));
            map.put("qualifiedTime", Long.parseLong(tagArr[1]));
        }

        js.put("s_c_d", map);
        return js;
    }

    public static String updateDeliveryDays(String tags) {
        List<Object> promiseTemplates = Lists.newArrayList();
        promiseTemplates.add(2);
        String[] tagArr = tags.split(",");
        if (tagArr.length > 0) {
            for (String tag : tagArr) {
                Map<String, Object> promiseTemplate = Maps.newConcurrentMap();
                Map<String, Object> valueMaps = Maps.newConcurrentMap();
                valueMaps.put("status", 0);
                valueMaps.put("contentShowType", "dynamic");
                valueMaps.put("deliveryDays", Integer.parseInt(tag));
                valueMaps.put("displayVersion", "fourPoint");
                valueMaps.put("currency", "KRW");
                valueMaps.put("moneyFormat", "₩ 1,300");
                valueMaps.put("mode", "passive");
                valueMaps.put("amount", "1");
                valueMaps.put("time", 0);
                valueMaps.put("type", "delivery");
                promiseTemplate.put("id", 33L);
                promiseTemplate.put("valueMaps", valueMaps);
                promiseTemplates.add(promiseTemplate);
            }
        }
        return JSONObject.toJSONString(promiseTemplates);
    }

    /**
     * 根据主单号获取payerId
     *
     * @param orderId
     * @return
     */
    @Override
    public Long getPayerIdByOrderId(String orderId) {
        Response<TradeOrderDTO> tradeOrderDTOResponse = orderQueryForBuyerFacade.queryTradeOrderById(Long.parseLong(orderId));
        if (tradeOrderDTOResponse.isNotSuccess()) {
            log.error("Fail to get response from trade-center. TraceId: " + EagleEye.getTraceId());
            return null;
        }
        if (tradeOrderDTOResponse.getModule() == null) {
            log.error("Order Not Exist. TraceId: " + EagleEye.getTraceId());
            return null;
        } else {
            return tradeOrderDTOResponse.getModule().getBuyer().getBuyerId();
        }
    }

    @Override
    public ResultDTO mockRaffleOrder(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        String orderIds = jsonObject.getString(Constant.ORDERS);
        String[] orderIdlist = StringUtils.split(orderIds, ",");
        List<Long> orders = new ArrayList<>();
        for (int i = 0; i < orderIdlist.length; i++) {
            orders.add(Long.parseLong(orderIdlist[i]));
        }

        JSONArray result = HsfUtil.manualCreateSnapshot(orders);
        resultDTO.setSuccess(true);
        resultDTO.setData(JSON.toJSONString(result));
        resultDTO.setMessage(JSON.toJSONString(result));
        return resultDTO;
    }

    @Override
    public ResultDTO shippingUnWarehouseOrderByOnEventByRegion(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long orderId = jsonObject.getLong(Constant.ORDER_ID);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        InvokeInfoDTO invokeInfoDTO = new InvokeInfoDTO();
        invokeInfoDTO.setAppName("ae-qa-trade-data-s");
        JSONObject result = HsfUtil.queryFulfillmentOrderByTradeId(buyerId,orderId);

        if (result == null || result.getJSONArray("module") == null) {
            resultDTO.setSuccess(true);
            resultDTO.setMessage("Fail to get FulfillmentOrder info from uop. Res: " + result);
            return resultDTO;
        }
        JSONArray module = result.getJSONArray("module");
        if (module != null && module.size() > 0) {
            for (int i = 0; i < module.size(); i++) {
                String fulfillmentOrderId = module.getJSONObject(i).getString("fulfillmentOrderId");
                JSONArray fulfillmentOrderItemDTOList = module.getJSONObject(i).getJSONArray("fulfillmentOrderItemDTOList");
                for (int j = 0; j < fulfillmentOrderItemDTOList.size(); j++) {
                    String fulfillmentOrderItemId = fulfillmentOrderItemDTOList.getJSONObject(j).getString("fulfillmentOrderItemId");
                }
            }
    }

        return resultDTO;
    }
}

