package com.aliexpress.databank.hsf.provider;

import com.alibaba.ae.service.open.facade.BreachContractArbitrationFacade;
import com.alibaba.ae.service.open.param.CcoQueryBusinessDataParam;
import com.alibaba.ae.service.open.response.Response;
import com.alibaba.ae.service.open.response.vo.ArbitrationBreachContractVO;
import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.global.money.Money;
import com.alibaba.global.order.management.api.facade.OrderManagementRegionFacade;
import com.alibaba.global.order.management.api.request.EditPayPriceRequest;
import com.alibaba.global.qa.dc.model.param.SystemDTO;
import com.alibaba.global.qa.dc.model.result.ResultDTO;
import com.alibaba.intl.ae.logistics.address.dto.WlMailingAddressResultDTO;
import com.aliexpress.databank.constant.Constant;
import com.aliexpress.databank.constant.ThubScenesEnum;
import com.aliexpress.databank.dataobject.*;
import com.aliexpress.databank.dataobject.insurance.SolutionReachedDto;
import com.aliexpress.databank.dataobject.*;
import com.aliexpress.databank.hsf.AIAdaptationService;
import com.aliexpress.databank.hsf.AddressService;
import com.aliexpress.databank.hsf.CartService;
import com.aliexpress.databank.hsf.OrderService;
import com.aliexpress.databank.hsf.ReverseOrderService;
import com.aliexpress.databank.hsf.ThubAdaptationService;
import com.aliexpress.databank.utils.HsfUtil;
import com.aliexpress.databank.hsf.*;
import com.aliexpress.databank.utils.ThubAdaptationUtils;
import com.aliexpress.databank.hsf.api.request.AIEnhancedTestDataRequest;
import com.aliexpress.databank.utils.ValidParamsUtil;
import com.aliexpress.qa.common.api.model.output.HsfResult;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.taobao.eagleeye.EagleEye;
import lombok.extern.log4j.Log4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.lang.reflect.Field;
import java.util.List;
import java.util.Map;


@Log4j
@HSFProvider(serviceInterface = AIAdaptationService.class, serviceGroup = "HSF")
public class AIAdaptationServiceImpl implements AIAdaptationService {

    @Autowired
    OrderService orderService;
    @Autowired
    CartService cartService;

    @Autowired
    ReverseOrderService reverseOrderService;

    @Autowired
    ThubAdaptationService thubAdaptationService;

    @Autowired
    AddressService addressService;
    
    @Autowired
    BreachContractArbitrationFacade breachContractArbitrationFacade;

    @Autowired
    private OrderManagementRegionFacade orderManagementRegionFacade;

    /*@Autowired
    IAtmosphereService  iAtmosphereService;
*/
    SystemDTO aiSystemDTO;

    {
        try {
            aiSystemDTO = new SystemDTO(null,"Aliexpress","AI_Studio",null);
            aiSystemDTO.setOperator("AI_Studio");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    @Override
    public NewResultDTO cancelOrder4LLM(CancelOrderDto cancelOrderDto) throws Exception {
        ResultDTO result;
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("orderIdStr", cancelOrderDto.getOrderIdStr());
        jsonObject.put("cancelEvent", cancelOrderDto.getCancelEvent());
        jsonObject.put("dpath", cancelOrderDto.getDpath());
        jsonObject.put("refundChannel", cancelOrderDto.getRefundChannel());

        String params = buildParam(Constant.EXTRA_PARAMS, jsonObject).toJSONString();
        result = orderService.cancelOrder(params, null);
        return new NewResultDTO(result);
    }

    private JSONObject buildParam(Map<String, String> extraParams, JSONObject jsonObject) {
        for (String key : extraParams.keySet()) {
            jsonObject.put(key, extraParams.get(key));
        }
        return jsonObject;
    }

    @Override
    public ResultDTO editPayPrice4LLM(EditPayPriceDto editPayPriceDto) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        ResultDTO validResult = ValidParamsUtil.validateEditPayPriceDto(editPayPriceDto);
        if (validResult != null) {
            resultDTO.setSuccess(false);
            resultDTO.setMessage(validResult.getMessage());
            resultDTO.setData(validResult.getData());
            return resultDTO;
        }
        EditPayPriceRequest editPayPriceRequest = new EditPayPriceRequest();
        editPayPriceRequest.setSellerId(editPayPriceDto.getSellerId());
        editPayPriceRequest.setTradeOrderId(editPayPriceDto.getOrderId());
        editPayPriceRequest.setBuyerId(editPayPriceDto.getBuyerId());
        editPayPriceRequest.setAdjustReason(editPayPriceDto.getAdjustReason());
        editPayPriceRequest.setAsync(false);
        editPayPriceRequest.setOldOrderPrice(Money.of(editPayPriceDto.getOldPrice(), editPayPriceDto.getCurrencyCode()));
        editPayPriceRequest.setNewOrderPrice(Money.of(editPayPriceDto.getNewPrice(), editPayPriceDto.getCurrencyCode()));
        try{
            com.alibaba.ecommerce.module.Response editPayPriceResponse = orderManagementRegionFacade.editPayPrice(editPayPriceRequest);
            if (editPayPriceResponse != null && editPayPriceResponse.isSuccess()) {
                resultDTO.setSuccess(true);
                resultDTO.setMessage("edit pay price success");
                resultDTO.setData("edit pay price success");
            } else if (editPayPriceResponse != null && !editPayPriceResponse.isSuccess()){
                resultDTO.setSuccess(false);
                resultDTO.setErrorCode(String.valueOf(editPayPriceResponse.getErrorCode()));
                resultDTO.setData("edit pay price failed");
            }
        }catch (Exception e) {
            resultDTO.setSuccess(false);
            resultDTO.setMessage(e.getMessage() + "traceId:" + EagleEye.getTraceId());
            resultDTO.setData(e.getMessage() + "traceId:" + EagleEye.getTraceId());
        }
        return resultDTO;
    }

    @Override
    public ResultDTO placeOrder4LLM(PlaceOrderDto placeOrderDto) throws Exception {
        ResultDTO result;
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("buyerId", placeOrderDto.getBuyerId());
        jsonObject.put("country", placeOrderDto.getCountryCode());
        jsonObject.put("shippingMethod", placeOrderDto.getDeliveryOption() != null ? placeOrderDto.getDeliveryOption() : "");
        jsonObject.put("productidlist", "[" + placeOrderDto.getItemIDS() + "]");
        jsonObject.put("skuId", placeOrderDto.getSkuId() != null ? placeOrderDto.getSkuId() : "");
        jsonObject.put("quantity", placeOrderDto.getQuantity());
        jsonObject.put("currency", placeOrderDto.getCurrency());
        jsonObject.put("staging", placeOrderDto.getStaging() != null ? placeOrderDto.getStaging() : "");
        jsonObject.put("shareGroup", placeOrderDto.getShareGroup() != null ? placeOrderDto.getShareGroup() : "");
        jsonObject.put("shareGroupCode", placeOrderDto.getShareGroupCode() != null ? placeOrderDto.getShareGroupCode() : "");
        //新增默认地址
        WlMailingAddressResultDTO wlMailingAddressResultDTO = new WlMailingAddressResultDTO();
        wlMailingAddressResultDTO=addressService.addDefaultAddress(placeOrderDto.getBuyerId(), placeOrderDto.getCountryCode());

        result = orderService.placeOrder(jsonObject.toJSONString(), null);
        //删除默认地址
        if(wlMailingAddressResultDTO.isSuccess()) {
            Long addressId = wlMailingAddressResultDTO.getId();
            addressService.delectAddress(placeOrderDto.getBuyerId(), addressId);
        }
        return result;
    }

    @Override
    public ResultDTO addCartItems4LLM(AddCartItemsDto addCartItemsDto) throws Exception{
        ResultDTO result;
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("buyerId", addCartItemsDto.getBuyerId());
        jsonObject.put("productIdList", addCartItemsDto.getItemIDS());
        jsonObject.put("skuList", addCartItemsDto.getSkuIDS());
        jsonObject.put("addCartQuantities", addCartItemsDto.getAddCartQuantities());
        jsonObject.put("currency", addCartItemsDto.getCurrency());
        jsonObject.put("business", addCartItemsDto.getBusiness());
        jsonObject.put("country", addCartItemsDto.getShipTo());
        jsonObject.put("dpath", addCartItemsDto.getDpath());
        jsonObject.put("isFreeGift", addCartItemsDto.getIsFreeGift());
        jsonObject.put("bizParams", addCartItemsDto.getBizParams());
        jsonObject.put("fulfillmentService", addCartItemsDto.getFulfillmentService());
        result = cartService.addCartItems(jsonObject.toJSONString(), aiSystemDTO);
        return result;
    }
    @Override
    public ResultDTO createAddress4LLM(CreateAddressDto createAddressDto) throws Exception {
        ResultDTO result;
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("buyerId", createAddressDto.getBuyerId());
        jsonObject.put("countryCode", createAddressDto.getCountryCode());
        result = addressService.createAddress(jsonObject.toJSONString(), aiSystemDTO);
        return result;
    }
    @Override
    public ResultDTO createRecommendAddress4LLM(CreateAddressDto createAddressDto) throws Exception {
        ResultDTO result;
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("buyerId", createAddressDto.getBuyerId());
        jsonObject.put("countryCode", createAddressDto.getCountryCode());
        result = addressService.createRecommendAddress(jsonObject.toJSONString(), aiSystemDTO);
        return result;
    }
    @Override
    public ResultDTO insertUserAddress4LLM(InsertUserAddressDto insertUserAddressDto) throws Exception {
        ResultDTO result;
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("buyerId", insertUserAddressDto.getBuyerId());
        jsonObject.put("country", insertUserAddressDto.getCountry());
        jsonObject.put("addressType", insertUserAddressDto.getAddressType());
        result = addressService.insertUserAddress(jsonObject.toJSONString(), aiSystemDTO);
        return result;
    }

    @Override
    public ResultDTO confirmDelivery4LLM(ConfirmDeliveryDto confirmDeliveryDto) throws Exception {
        ResultDTO result;
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("orderIdStr", confirmDeliveryDto.getOrderIdStr());
        jsonObject.put("subOrderId", confirmDeliveryDto.getSubOrderId());

        String params = buildParam(Constant.EXTRA_PARAMS, jsonObject).toJSONString();
        result = orderService.confirmDelivery(params, null);
        return result;
    }
    @Override
    public ResultDTO shipOrder4LLM(ShipOrderDto shipOrderDto) throws Exception {
        ResultDTO result;
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("orderIdStr", shipOrderDto.getOrderIdStr());
        jsonObject.put("shipType", shipOrderDto.getShipType());
        jsonObject.put("dpath", shipOrderDto.getDpath() != null ? shipOrderDto.getDpath() : "");

        String params = buildParam(Constant.EXTRA_PARAMS, jsonObject).toJSONString();
        result = orderService.shipOrder(params, null);
        return result;
    }

    @Override
    public NewResultDTO mockCcoJudgement4LLM(MockCcoJudgementDto mockCcoJudgementDto) throws Exception {
        ResultDTO result;
        JSONObject jsonObject = new JSONObject();
        jsonObject.put(Constant.JUDGE_RESULT, mockCcoJudgementDto.getJudgeResult());
        jsonObject.put(Constant.RESPONSIBLE_PARTY, mockCcoJudgementDto.getResponsibleParty());
        jsonObject.put(Constant.FAKE_ITEM, mockCcoJudgementDto.getFakeItem());
        jsonObject.put(Constant.RETURN_AMT, mockCcoJudgementDto.getReturnAmt());
        jsonObject.put(Constant.ARBITRATION_TO_SELFDROP, mockCcoJudgementDto.getArbitrationToSelfDrop());
        jsonObject.put(Constant.SUB_ORDER_ID, mockCcoJudgementDto.getSubOrderId());
       // jsonObject.put(Constant.FAST_ARBITRATION_RESULT, mockCcoJudgementDto.getFastArbitrationResult());
      //  jsonObject.put(Constant.PROCESS_TYPE, mockCcoJudgementDto.getProcessType());
        jsonObject.put(Constant.IS_FAST_ARBITRATION, mockCcoJudgementDto.getIsFastArbitration());
        String params = buildParam(Constant.EXTRA_PARAMS, jsonObject).toJSONString();
        result = reverseOrderService.mockCcoJudgement1(params, null);
        return new NewResultDTO(result);
    }

    @Override
    public NewResultDTO solutionReached4LLM(SolutionReachedDto solutionReachedDto) throws Exception {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("tradeOrderLineId", solutionReachedDto.getTradeOrderLineId());
        jsonObject.put("dpath", solutionReachedDto.getDpath());
        String params = buildParam(Constant.EXTRA_PARAMS, jsonObject).toJSONString();
        return new NewResultDTO(reverseOrderService.solutionReached(params, null));

    }

    @Override
    public NewResultDTO mockVCBuyerInfoError4LLM(ReqDto reqDto) throws Exception {
        ThubReqDto thubReqDto = new ThubReqDto();
        thubReqDto.setTradeOrderLineId(reqDto.getTradeOrderLineId());
        thubReqDto.setScenes(ThubScenesEnum.THUB_MOCK_VC_BUYER_INFO_ERROR.getScenes());
        return new NewResultDTO(thubAdaptationService.thubMockVCBuyerInfoError(thubReqDto));
    }

    @Override
    public NewResultDTO selfDropOff4LLM(ReqDto reqDto) throws Exception {
        ThubReqDto thubReqDto = new ThubReqDto();
        thubReqDto.setTradeOrderLineId(reqDto.getTradeOrderLineId());
        thubReqDto.setScenes(ThubScenesEnum.THUB_MOCK_FR_SELF_DROP_OFF.getScenes());
        return new NewResultDTO(thubAdaptationService.thubSelfDropOff(thubReqDto));
    }

    @Override
    public NewResultDTO sellerConfirmGoods4LLM(ReqDto reqDto) throws Exception {
        ThubReqDto thubReqDto = new ThubReqDto();
        thubReqDto.setTradeOrderLineId(reqDto.getTradeOrderLineId());
        thubReqDto.setScenes(ThubScenesEnum.THUB_MOCK_FR_SELLER_CONFIRM_GOODS.getScenes());
        return new NewResultDTO(thubAdaptationService.thubSellerConfirmGoods(thubReqDto));
    }

    @Override
    public NewResultDTO mockCollectAgain4LLM(ReqDto reqDto) throws Exception {
        ThubReqDto thubReqDto = new ThubReqDto();
        thubReqDto.setTradeOrderLineId(reqDto.getTradeOrderLineId());
        thubReqDto.setScenes(ThubScenesEnum.THUB_MOCK_VC_COLLECT_AGAIN.getScenes());
        return new NewResultDTO(thubAdaptationService.thubMockCollectAgain(thubReqDto));
    }

    @Override
    public NewResultDTO mockAcceptSuccess4LLM(ReqDto reqDto) throws Exception {
        ThubReqDto thubReqDto = new ThubReqDto();
        thubReqDto.setTradeOrderLineId(reqDto.getTradeOrderLineId());
        thubReqDto.setScenes(ThubScenesEnum.THUB_MOCK_ACCEPT_SUCCESS.getScenes());
        return new NewResultDTO(thubAdaptationService.thubMockAcceptSuccess(thubReqDto));
    }

    @Override
    public NewResultDTO mockMailNoReturn4llM(ReqDto reqDto) throws Exception {
        ThubReqDto thubReqDto = new ThubReqDto();
        thubReqDto.setTradeOrderLineId(reqDto.getTradeOrderLineId());
        thubReqDto.setScenes(ThubScenesEnum.THUB_MOCK_ACCEPT_SUCCESS.getScenes());
        return new NewResultDTO(thubAdaptationService.thubMockAcceptSuccess(thubReqDto));
    }



    @Override
    public NewResultDTO mockReceivedAscan4LLM(ReqDto reqDto) throws Exception {
        ThubReqDto thubReqDto = new ThubReqDto();
        thubReqDto.setTradeOrderLineId(reqDto.getTradeOrderLineId());
        thubReqDto.setScenes(ThubScenesEnum.THUB_MOCK_RECEIVED_ASCAN.getScenes());
        return new NewResultDTO(thubAdaptationService.thubMockReceivedAscan(thubReqDto));
    }

    @Override
    public NewResultDTO mockReceivedDscan4LLM(ReqDto reqDto) throws Exception {
        ThubReqDto thubReqDto = new ThubReqDto();
        thubReqDto.setTradeOrderLineId(reqDto.getTradeOrderLineId());
        thubReqDto.setScenes(ThubScenesEnum.THUB_MOCK_RECEIVED_DSCAN.getScenes());
        return new NewResultDTO(thubAdaptationService.thubMockReceivedDscan(thubReqDto));
    }

    @Override
    public NewResultDTO mockQualityChecked4LLM(ReqDto reqDto) throws Exception {
        ThubReqDto thubReqDto = new ThubReqDto();
        thubReqDto.setTradeOrderLineId(reqDto.getTradeOrderLineId());
        thubReqDto.setScenes(ThubScenesEnum.THUB_MOCK_QUALITY_CHECKED.getScenes());
        return new NewResultDTO(thubAdaptationService.thubMockQualityChecked(thubReqDto));
    }

    @Override
    public NewResultDTO acceptSolution4LLM(ReqDto reqDto) throws Exception {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("subOrderId", reqDto.getTradeOrderLineId());
        jsonObject.put("returnOrRefund", "");
        jsonObject.put("returnAddress", "");
        String params = buildParam(Constant.EXTRA_PARAMS, jsonObject).toJSONString();
        return  new NewResultDTO(reverseOrderService.acceptSolution(params,null));
    }


    @Override
    public NewResultDTO analyseReverseOrder4LLM(ReverseReqDto reqDto) throws Exception {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("orderIdStr", reqDto.getOrderIdStr());
        String params = buildParam(Constant.EXTRA_PARAMS, jsonObject).toJSONString();
        return new NewResultDTO(reverseOrderService.reverseLifeCycle(params, null));
    }

    @Override
    public NewResultDTO openV3Dispute4LLM(OpenDisputeReq req) throws Exception{
        ResultDTO result = new ResultDTO();
        result.setSuccess(true);

        // 校验订单状态
        String buyerId = Constant.EXTRA_PARAMS.get("buyerId");
        try {
            ReverseOrderLineDTO reverseOrder = ThubAdaptationUtils.getLastReverseOrderByTradeOrderLineId(Long.parseLong(buyerId), Long.parseLong(req.getTradeOrderLineId()));
            if (reverseOrder != null) {
                result.setMessage("创建纠纷成功，reverseOrder=" + JSONObject.toJSONString(reverseOrder));
                result.setData("创建纠纷成功，reverseOrder=" + JSONObject.toJSONString(reverseOrder));
                return new NewResultDTO(result);
            }

            JSONObject jsonObject = new JSONObject();
            jsonObject.put("refundChannel", req.getRefundChannel());
            jsonObject.put("returnReason", req.getReturnReason());
            jsonObject.put("quantity", req.getQuantity());
            jsonObject.put("tradeOrderLineId", req.getTradeOrderLineId());

            String params = buildParam(Constant.EXTRA_PARAMS, jsonObject).toJSONString();

            result = reverseOrderService.returnAndRefund3(params, null);
        } catch (Exception e) {
            e.printStackTrace();
            result.setSuccess(false);
            result.setData(e.getMessage());
            result.setMessage(e.getMessage());
        }
        return new NewResultDTO(result);
    }

    @Override
    public NewResultDTO querySellerStrategiesInfo4LLM(QuerySellerStrategiesDto reqDto) throws Exception {
        return new NewResultDTO(reverseOrderService.querySellerStrategiesInfo(reqDto));
    }

    @Override
    public NewResultDTO timeoutExecute4LLM(TimeoutReqDto timeoutReqDto) throws Exception {
        return new NewResultDTO(reverseOrderService.timeoutExecute(timeoutReqDto));
    }

    @Override
    public NewResultDTO querySlsLog(SlsReqDto slsReqDto) throws Exception {
        ResultDTO result = new ResultDTO();
        /*JSONObject jsonObject=new JSONObject();
        JSONObject allSlsMsg4Self = HsfUtil.getAllSlsMsg4Self(jsonObject);
        result.setSuccess(true);
        result.setData(allSlsMsg4Self.getData().toString());
        result.setMessage(allSlsMsg4Self.getData().toString());*/
        return  new NewResultDTO(result);

    }

    @Override
    public NewResultDTO chargeBack(ChargeBackDto req) throws Exception {
        ResultDTO result = new ResultDTO();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("orderIdStr", req.getOrderIdStr());
        jsonObject.put("isNewCb", req.getIsNewCb());
        jsonObject.put("isMerge", req.getIsMerge());
        jsonObject.put("paymentChannel", req.getPaymentChannel());

        String params = buildParam(Constant.EXTRA_PARAMS, jsonObject).toJSONString();
        reverseOrderService.chargeBack(params, null);
        return new NewResultDTO(result);
    }

    @Override
    public NewResultDTO chargeBackJudge(ChargeBackDto chargeBackDto) throws Exception {
        ResultDTO result = new ResultDTO();
        /*JSONObject jsonObject = new JSONObject();
        jsonObject.put("orderIdStr", req.getOrderIdStr());
        jsonObject.put("isNewCb", req.getIsNewCb());
        jsonObject.put("isMerge", req.getIsMerge());
        jsonObject.put("paymentChannel", req.getPaymentChannel());

        String params = buildParam(Constant.EXTRA_PARAMS, jsonObject).toJSONString();
        reverseOrderService.chargeBack(params, null);*/
        return new NewResultDTO(result);
    }

    @Override
    public NewResultDTO queryBreachContracts(CompensationDto compensationDto) throws Exception {
        ResultDTO result = new ResultDTO();
        CcoQueryBusinessDataParam ccoQueryBusinessDataParam = new CcoQueryBusinessDataParam();
        ccoQueryBusinessDataParam.setTargetId(compensationDto.getOrderIdStr());
        ccoQueryBusinessDataParam.setTargetType("AE_MAIN_ORDER");
        ccoQueryBusinessDataParam.setDefaultingPartyId(Constant.EXTRA_PARAMS.get("buyerId"));
        ccoQueryBusinessDataParam.setDefaultingPartyRole("Buyer");
        ccoQueryBusinessDataParam.setResponsibilityCode(compensationDto.getResponsibilityCode());
        Response<List<ArbitrationBreachContractVO>> listResponse = breachContractArbitrationFacade.queryBreachContracts(ccoQueryBusinessDataParam);
        if (listResponse.isSuccess()) {
            result.setSuccess(true);
            result.setData(JSONObject.toJSONString(JSONObject.toJSONString(listResponse.getData())));
            result.setMessage(JSONObject.toJSONString(JSONObject.toJSONString(listResponse.getData())));
        }else {
            result.setSuccess(false);
            result.setMessage(listResponse.getErrorMessage());
            result.setData(listResponse.getErrorMessage());
        }
        return new NewResultDTO(result);
    }

}
