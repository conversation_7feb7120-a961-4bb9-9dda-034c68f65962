package com.aliexpress.databank.hsf.provider;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.common.lang.StringUtil;
import com.alibaba.ecommerce.module.Response;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.global.carts.api.facade.CartFacade;
import com.alibaba.global.carts.api.facade.CartMtopFacade;
import com.alibaba.global.carts.api.request.CartAddRequest;
import com.alibaba.global.carts.api.request.CartDeleteRequest;
import com.alibaba.global.carts.api.request.CartQueryRequest;
import com.alibaba.global.carts.api.request.dto.CartAddItemDTO;
import com.alibaba.global.carts.api.response.dto.CartDetailResult;
import com.alibaba.global.carts.api.response.dto.detailFieldsDto.CartItemDetailDTO;
import com.alibaba.global.ic.api.CustomerProductServiceFacade;
import com.alibaba.global.qa.dc.model.builder.QueryResultBuilder;
import com.alibaba.global.qa.dc.model.param.SystemDTO;
import com.alibaba.global.qa.dc.model.result.QueryResultUnit;
import com.alibaba.global.qa.dc.model.result.ResultDTO;
import com.aliexpress.databank.constant.Constant;
import com.aliexpress.databank.hsf.CartService;
import com.aliexpress.databank.utils.ConvertParam;
import com.aliexpress.databank.utils.HsfUtil;
import com.aliexpress.qa.platform.api.util.MeasureLogger;
import com.taobao.eagleeye.EagleEye;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

@Slf4j
@HSFProvider(serviceInterface = CartService.class, serviceGroup = "HSF",serviceVersion = "1.0.0", clientTimeout = 20000)
public class CartServiceImpl implements CartService {
    private final static int ADD_ITEM_MAX_SIZE = 3;
    private final static Set<Long> BUYER_WHITE_LIST = new HashSet<Long>() {{
        add(1859103223L);
    }};
//    @Autowired
//    private DataModelRemoteService dataModelRemoteService;


    @Autowired
    private CustomerProductServiceFacade customerProductServiceFacade;

    @Autowired
    private CartFacade cartFacade;
    private CartMtopFacade cartMtopFacade;

    //购物车查询
    public CartQueryRequest getCartQueryRequest(Long buyerId, String shipTo, String currency, String business) {
        CartQueryRequest cartQueryRequest = new CartQueryRequest();
        cartQueryRequest.setUserId(buyerId);
        cartQueryRequest.setBusiness(business);
        cartQueryRequest.setHttpHeaders(ConvertParam.getHttpHeaders());
        cartQueryRequest.setCartShipToDTO(ConvertParam.getCartShipTo(shipTo));
        cartQueryRequest.setPageOption(ConvertParam.getPageOption());
        cartQueryRequest.setExtParams(ConvertParam.getExtraParams(currency));
        cartQueryRequest.setCurrency(currency);
        log.warn("[CartServiceImpl - cartQueryRequest] request: " + JSON.toJSONString(cartQueryRequest));
        return cartQueryRequest;
    }

    @Override
    public ResultDTO queryShoppingCart(String params, SystemDTO systemDTO) {
        ResultDTO result = new ResultDTO();
        log.warn("queryShoppingCart params {}", params);
        JSONObject jsonObject = JSONObject.parseObject(params);
        long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        String business = jsonObject.getString(Constant.BUSINESS);
        if(business == null || business.equals("")){
            business = "null";
        }
        JSONObject response = null;
        try {
            response = HsfUtil.cartMtopFacadeQuery(buyerId,business);
            log.warn(JSON.toJSONString(response));
        } catch (Exception e) {
            log.error(String.valueOf(e));
        }
        if (!response.getBoolean("success")) {
            result.setSuccess(false);
            result.setData("Failed to query "+JSON.toJSONString(response)+" traceId: " + EagleEye.getTraceId());
            result.setMessage("Failed to query "+JSON.toJSONString(response)+" traceId: " + EagleEye.getTraceId());
        }else{
            Map<String, QueryResultUnit> data = new LinkedHashMap<>();
            JSONObject res = new JSONObject();
            res.put("value", JSON.toJSONString(response));
            Map<String, QueryResultUnit> jsonFeature = null;
            try {
                jsonFeature = QueryResultBuilder.buildQueryResult("json", null, null, res);
                log.warn(String.valueOf(jsonFeature));
            } catch (Exception e) {
                log.error(String.valueOf(e));
            }

            data.putAll(jsonFeature);
            result.setData(JSONObject.toJSONString(data, SerializerFeature.PrettyFormat));
            result.setSuccess(true);
        }
        return result;
    }

    //购物车清空
    public CartDeleteRequest getCartDeleteRequest(Long buyerId, List<Long> cartIds, String shipTo, String currency) {
        CartDeleteRequest cartDeleteRequest = new CartDeleteRequest();
        cartDeleteRequest.setUserId(buyerId);
        cartDeleteRequest.setCartShipToDTO(ConvertParam.getCartShipTo(shipTo));
        cartDeleteRequest.setHttpHeaders(ConvertParam.getHttpHeaders());
        cartDeleteRequest.setCartItemIds(new ArrayList<>(cartIds));
        cartDeleteRequest.setExtParams(ConvertParam.getExtraParams(currency));
        return cartDeleteRequest;
    }

    private boolean deleteShoppingCart(Long buyerId, List<Long> cartIds, String shipTo, String currency) {
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(cartIds)) {
            return true;
        }
        CartDeleteRequest cartDeleteRequest = getCartDeleteRequest(buyerId, cartIds, shipTo, currency);
        log.warn("[CartServiceImpl - deleteShoppingCart] before delete traceId: " + EagleEye.getTraceId() + ". request: " + JSON.toJSONString(cartDeleteRequest) + ". cartIds: " + JSON.toJSONString(cartIds) + ". Thread Id: " + Thread.currentThread().getId());
        Response response = cartFacade.delete(cartDeleteRequest);
        log.warn("[CartServiceImpl - deleteShoppingCart] after delete traceId: " + EagleEye.getTraceId() + ".result" + response.isSuccess() + ". request: " + JSON.toJSONString(cartDeleteRequest) + ". cartIds: " + JSON.toJSONString(cartIds) + ". Thread Id: " + Thread.currentThread().getId());
        return response.isSuccess();
    }

    @Override
    public ResultDTO emptyShoppingCart(String params, SystemDTO systemDTO) {
        ResultDTO result = new ResultDTO();
        log.warn("emptyShoppingCart params {}", params);
        JSONObject jsonObject = JSONObject.parseObject(params);
        long buyerId = Long.parseLong(jsonObject.getString(Constant.BUYER_ID));
        String business = jsonObject.getString(Constant.BUSINESS);
        if (business.equals("null")){
            business=null;
        }
        if (!BUYER_WHITE_LIST.contains(buyerId)) {
            log.warn("emptyShoppingCart invalid buyerId {}", buyerId);
            result.setSuccess(false);
            result.setData("invalid buyerId");
            result.setMessage("invalid buyerId");
            return result;
        }
        log.warn("emptyShoppingCart buyerId {}", buyerId);
        String shipTo = jsonObject.getString(Constant.SHIP_TO_COUNTRY);
        String currency = jsonObject.getString(Constant.CURRENCY);
        CartQueryRequest cartQueryRequest = getCartQueryRequest(buyerId, shipTo, currency,business);
        Response<CartDetailResult> queryShoppingCartRes = cartFacade.query(cartQueryRequest);
        log.warn("[CartServiceImpl - emptyShoppingCart] query shoppingcart traceId: " + EagleEye.getTraceId() + ".result " + queryShoppingCartRes.isSuccess() + " .response: " + JSON.toJSONString(queryShoppingCartRes));
        if (queryShoppingCartRes.isSuccess() && queryShoppingCartRes.getModule() != null) {
            List<CartItemDetailDTO> items = queryShoppingCartRes.getModule().getCartItems();
            List<Long> cartIds = new ArrayList<>();
            for (CartItemDetailDTO item : items) {
                Long cartId = item.getIntentionItem().getProduct().getCartId();
                cartIds.add(cartId);
            }
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(cartIds)) {
                deleteShoppingCart(buyerId, cartIds, shipTo, currency);
            }
        }
        result.setSuccess(queryShoppingCartRes.isSuccess());
        result.setData(JSON.toJSONString(queryShoppingCartRes.getModule()));
        result.setMessage(JSON.toJSONString(queryShoppingCartRes.getModule()));
        return result;
    }


    //购物车加购
    /*private Set<Long> getValidSkuIds(Long itemId) {
        Set<Long> skuIds = new HashSet<>();
        JSONObject queryResult = null;
        try {
            queryResult = HsfUtil.getProductByProductId(itemId);
        } catch (Exception e) {
            log.error("getValidSkuIds itemId={}", itemId, e);
        }
        if (!queryResult.getBoolean("success")) {
            log.warn("Fail to get response from ic");
        }
        if (queryResult.getJSONObject("singleProduct") == null) {
            log.warn("Illegal Item Id. Fail to get item. Item Id: " + itemId);
        }

        //*SingleProductQueryCondition.QueryByProductIdBuilder queryByProductIdBuilder =SingleProductQueryCondition.queryByProductIdBuilder(itemId);
        List<SingleProductQueryCondition> productQueryConditions=new ArrayList<>();
        productQueryConditions.add(queryByProductIdBuilder.build());
        ProductQueryRequest productQueryRequest=new ProductQueryRequest();
        productQueryRequest.setProductQueryConditions(productQueryConditions);*//*
        //ProductQueryRequest productQueryRequest = ProductQueryRequest.builder().addQueryCondition(queryByProductIdBuilder.build()).build();
        log.warn(JSON.toJSONString(productQueryRequest));
        log.warn("[CartServiceImpl - queryProduct] before query skuId traceId: " + EagleEye.getTraceId() + " .request: " + JSON.toJSONString(productQueryRequest));
        ProductQueryResponse productInfo = customerProductServiceFacade.queryProduct(productQueryRequest);
        log.warn("[CartServiceImpl - queryProduct] after query skuId traceId: " + EagleEye.getTraceId() + ".result " + productInfo.isSuccess() + " .response: " + JSON.toJSONString(productInfo));
        if (productInfo == null || !productInfo.isSuccess() || productInfo.getModel() == null) {
            log.error("CartAddItemServiceImpl getValidSkuIds failed: {}", productInfo.isSuccess());
            return null;
        }
        log.info("CartAddItemServiceImpl single product: {}", productInfo.getSingleProduct());
        if (productInfo.getSingleProduct().getStatus().intValue() == ProductStatusConstants.NORMAL.intValue()
                && productInfo.getSingleProduct().getAuditStatus().equals(AuditStatusConstants.PASS.getValue())) {
            if (productInfo.getModel().isEmpty()) {
                return skuIds;
            }
            if (CollectionUtils.isNotEmpty(productInfo.getModel().get(0).getSkuList())) {
                productInfo.getModel().get(0).getSkuList().forEach(it -> skuIds.add(it.getSkuId()));
            }
        }
        return skuIds;
    }*/

/*    @Override
    public ResultDTO addCartItems(String params, SystemDTO systemDTO) {
        ResultDTO result = new ResultDTO();
        log.warn("addCartItem params {}", params);
        JSONObject jsonObject = JSONObject.parseObject(params);
        long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        JSONArray itemList = jsonObject.getJSONArray(Constant.ITEM_IDS);
        if (itemList.isEmpty()) {
            result.setSuccess(false);
            result.setData("item list is empty");
            result.setMessage("item list is empty");
            return result;
        }
        Map<Long, Set<Long>> items = createItemMap(itemList.getLong(0));
        items.forEach((itemId, val) -> log.warn("addCartItem itemId {}", itemId));
        CartAddRequest addCartRequest = addCartRequest(buyerId, items);
        Response<CartAddResponse> cartAddResponse = cartFacade.add(addCartRequest);
        System.out.println("add cart request " + cartAddResponse.getErrorCode());
        CartAddResponse response = cartAddResponse.getModule();
        System.out.println("cart num " + response.getCartNum());
        for (CartErrorItemDTO item : response.getErrorItems()) {
            System.out.println("item id is " + item.getItemId() + " " + item.getFailedReason());
        }
        result.setSuccess(cartAddResponse.isSuccess());
        result.setData(JSON.toJSONString(cartAddResponse.getModule()));
        result.setMessage(JSON.toJSONString(cartAddResponse.getModule()));
        return result;
    }*/

    private static final String SP = ";";
    private static final String SSP = ":";
    private static final String R_SP = "#3B";
    private static final String R_SSP = "#3A";
    /**
     * 正向 #3A代表:  #3B代表;
     */
    private static String decode(String val) {
        return StringUtils.replace(StringUtils.replace(val, R_SP, SP), R_SSP, SSP);
    }
    /**
     * 逆向 #3A代表;  #3B代表:
     */
    private static String decodeReverse(String val) {
        return StringUtils.replace(StringUtils.replace(val, R_SSP, SP), R_SP, SSP);
    }

    public static Map<String, String> toMap(String str) {
        Map<String, String> attrs = new HashMap<String, String>();
        boolean isReverse = str.contains("request_type");
        if (StringUtils.isNotBlank(str)) {
            String[] arr = StringUtils.split(str, SP);
            for (String kv : arr) {
                if (StringUtils.isNotBlank(kv)) {
                    String[] ar = kv.split(SSP);
                    if (ar.length == 2) {
                        String k;
                        String v;
                        if (!isReverse) {
                            k = decode(ar[0]);
                            v = decode(ar[1]);
                        } else {
                            k = decodeReverse(ar[0]);
                            v = decodeReverse(ar[1]);
                        }
                        if (StringUtils.isNotBlank(k) && StringUtils.isNotBlank(v)) {
                            attrs.put(k, v);
                        }
                    }
                }
            }
        }
        return attrs;
    }
    public static JSONObject mapToJson(Map<String, String> map) {
        JSONObject result = new JSONObject();
        for (String key : map.keySet()) {
            String value = map.get(key);
            if (value.startsWith("{") || value.startsWith("[")) {
                // 解析json
                try {
                    Object subJson = JSONObject.parse(value);
                    result.put(key, subJson);
                } catch (JSONException e) {
                    // 解析失败，拿字符串塞入
                    result.put(key, value);
                }
            } else {
                result.put(key, value);
            }
        }
        return result;
    }
//购物车加购-ml
    @Override
        public ResultDTO addCartItems(String params, SystemDTO systemDTO) {
        MeasureLogger measureLogger = MeasureLogger.start("ACCESS_LOG", "PVUV", MeasureLogger.Level.INFO);
        measureLogger.setInvokeStartTime(System.currentTimeMillis());
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        String itemIDS = "[" +jsonObject.getString(Constant.PRODUCT_IDS)+"]";
        String skuIDS = "[" + Optional.ofNullable(jsonObject.getString(Constant.SKU_IDS)).orElse("") + "]";
        String addCartQuantities = "[" + Optional.ofNullable(jsonObject.getString(Constant.ADD_CART_QUANTITIES)).orElse("") + "]";
        String currency = jsonObject.getString(Constant.CURRENCY);
        String business = Optional.ofNullable(jsonObject.getString(Constant.BUSINESS)).orElse("null");
        String shipTo = jsonObject.getString(Constant.SHIP_TO_COUNTRY);
        String dpath = jsonObject.getString(Constant.DPATH_ENV);
        String isFreeGift = Optional.ofNullable(jsonObject.getString(Constant.IS_FREE_GIFT)).orElse("false");
        String bizParams=jsonObject.getString(Constant.BIZ_PARAMS);
        String fulfillmentService=jsonObject.getString(Constant.FULFILLMENT_SERVICE);

	    if (StringUtil.isNotBlank(dpath)) {
            EagleEye.putUserData("dpath_env", dpath);

        }
        if(fulfillmentService == null || fulfillmentService.equals("")){
            fulfillmentService=null;
        }
        List<Long> itemList = JSON.parseArray(itemIDS, Long.class);
        List<Long> skuList = JSON.parseArray(skuIDS, Long.class);
        List<Integer> quantityList = JSON.parseArray(addCartQuantities, Integer.class);
        if (currency == null || currency.equals("")) {
            currency = "USD";
        }
        if(shipTo == null || shipTo.equals("")){
            shipTo = "US";
        }
        if(bizParams == null || bizParams.equals("")){
            bizParams = "";
        }
        if (itemList.isEmpty()) {
            resultDTO.setSuccess(false);
            resultDTO.setData("item list is empty");
            resultDTO.setMessage("item list is empty");
            return resultDTO;
        }
        Map<Long, Long> items = new HashMap<>();
        if (skuList.isEmpty()) {
            for (int i = 0; i < itemList.size(); i++) {
                Long itemId = itemList.get(i);
                Set<Long> skuIds = new HashSet<>();
                JSONObject queryResult = null;
                try {
                    queryResult = HsfUtil.getProductByProductId(itemId);
                } catch (Exception e) {
                    log.error("getValidSkuIds itemId={}", itemId, e);
                }
                if (!queryResult.getBoolean("success")) {
                    resultDTO.setSuccess(false);
                    resultDTO.setData("Fail to get response from ic");
                    resultDTO.setMessage("Fail to get response from ic");
                    return resultDTO;
                }
                if (queryResult.getJSONObject("singleProduct") == null) {
                    resultDTO.setSuccess(false);
                    resultDTO.setData("Illegal Item Id. Fail to get item. Item Id: " + itemId);
                    resultDTO.setMessage("Illegal Item Id. Fail to get item. Item Id: " + itemId);
                    return resultDTO;
                }
                JSONObject skuMapJson=queryResult.getJSONObject("singleProduct").getJSONObject("skuIdAndSkuMap");
                Map<String,JSONObject> skuMap=new HashMap<>();
                Iterator it =skuMapJson.entrySet().iterator();
                while (it.hasNext()) {
                    Map.Entry<String, JSONObject> entry = (Map.Entry<String, JSONObject>) it.next();
                    skuMap.put(entry.getKey(), entry.getValue());
                }
                for(String key:skuMap.keySet()){
                    if(skuMap.get(key).getString("salable")=="true"){
                        items.put(itemId,Long.parseLong(key));
                        skuList.add(Long.parseLong(key));
                        break;
                    }
                }
            }
            if(itemList.size() != skuList.size()){
                resultDTO.setSuccess(false);
                resultDTO.setData("Please check the product status.");
                resultDTO.setMessage("Please check the product status.");
                return resultDTO;
            }
        } else {
            if(itemList.size() != skuList.size()){
                resultDTO.setSuccess(false);
                resultDTO.setData("ProductList does not match SkuList");
                resultDTO.setMessage("ProductList does not match SkuList");
                return resultDTO;
            }
            for (int i = 0; i < itemList.size(); i++) {
                items.put(itemList.get(i), skuList.get(i));
            }
        }
        //Map<Long, Long> items = createItemMap(itemList);
        if ((itemList.size() != quantityList.size()) && (quantityList.size() != 0)) {
            resultDTO.setSuccess(false);
            resultDTO.setData("ProductList does not match QuantityList");
            resultDTO.setMessage("ProductList does not match QuantityList");
            return resultDTO;
        }
        Map<Long, Integer> quantities = new HashMap<>();
        for (int i = 0; i < itemList.size(); i++) {
            if (quantityList.size() == 0) {
                quantities.put(itemList.get(i), 1);
            } else {
                quantities.put(itemList.get(i), quantityList.get(i));
            }
        }
        Map<String, String> gifts = new HashMap<>();
        log.warn(JSON.toJSONString(business));
        if (business.equals("561")||business.equals("562")||business.equals("620")){
            gifts.put("sourceType",business);
            business=null;
            log.warn(JSON.toJSONString(gifts));
        }else if (business.equals("null")){
            business=null;
        }
        log.warn(JSON.toJSONString(gifts));
        gifts.put("isFreeGift",isFreeGift);
        log.warn(JSON.toJSONString(gifts));
        List<Map> cartAddItems = new ArrayList<>();
        for (Long itemId : items.keySet()) {
            Map cartAddItem = new HashMap<>();
            cartAddItem.put("itemId",itemId);
            cartAddItem.put("quantity",quantities.get(itemId));
            cartAddItem.put("skuId",items.get(itemId));
            cartAddItem.put("attributes",gifts);
            cartAddItem.put("bundleId",0);
            cartAddItem.put("bundleItem",false);
            cartAddItem.put("bundleSellerId",0);
            if(fulfillmentService!=null){
                cartAddItem.put("fulfillmentservice",fulfillmentService);
            }
            cartAddItems.add(cartAddItem);
        }
        JSONObject response = null;
        log.warn(JSON.toJSONString(cartAddItems));
        try {
            response = HsfUtil.cartMtopFacadeAdd(buyerId, JSON.toJSONString(cartAddItems), currency,shipTo ,business,bizParams);
        } catch (Exception e) {
            log.error("add failed={}", buyerId, JSON.toJSONString(cartAddItems), currency,shipTo ,business,bizParams, e);
        }
        log.warn(JSON.toJSONString(response));
        if (!response.getBoolean("success")) {
            resultDTO.setSuccess(false);
            resultDTO.setData("Failed to add to shopping cart "+JSON.toJSONString(response)+" traceId: " + EagleEye.getTraceId());
            resultDTO.setMessage("Failed to add to shopping cart "+JSON.toJSONString(response)+" traceId: " + EagleEye.getTraceId());
        }else{
            resultDTO.setSuccess(true);
            resultDTO.setData("add to cart successfully "+JSON.toJSONString(response)+" traceId: " + EagleEye.getTraceId());
        }
        measureLogger.setSuccess(true)
                .setInvokeEndTime(System.currentTimeMillis())
                .setEmpId(systemDTO.getOperator())
                .setContent("/trade/jobId="+systemDTO.getSite())
                .end();
        return resultDTO;

//        if (business != null && business.equals("nn_gift")){
//            Map<String, String> gifts = new HashMap<>();
//            String isFreeGift="false";
//            if (business.equals("nn_gift")){
//                business="nn_mix";
//                isFreeGift="true";
//                gifts.put("isFreeGift",isFreeGift);
//            }
//            List<CartAddItemDTO> cartAddItemDTOS = new ArrayList<>();
//            for (Long itemId : items.keySet()) {
//                CartAddItemDTO cartAddItemDTO = new CartAddItemDTO();
//                cartAddItemDTO.setItemId(itemId);
//                cartAddItemDTO.setQuantity(quantities.get(itemId));
//                cartAddItemDTO.setSkuId(items.get(itemId));
//                cartAddItemDTO.setAttributes(gifts);
//                cartAddItemDTOS.add(cartAddItemDTO);
//            }
//            JSONObject response = null;
//            log.warn(JSON.toJSONString(cartAddItemDTOS));
//            try {
//                response = HsfUtil.cartMtopFacadeAdd(buyerId, JSON.toJSONString(cartAddItemDTOS), currency,shipTo ,business);
//            } catch (Exception e) {
//                log.error("add failed={}", buyerId, JSON.toJSONString(cartAddItemDTOS), currency,shipTo ,business, e);
//            }
//            log.warn(JSON.toJSONString(response));
//            if (!response.getBoolean("success")) {
//                resultDTO.setSuccess(false);
//                resultDTO.setData("Failed to add to shopping cart "+JSON.toJSONString(response)+" traceId: " + EagleEye.getTraceId());
//                resultDTO.setMessage("Failed to add to shopping cart "+JSON.toJSONString(response)+" traceId: " + EagleEye.getTraceId());
//            }else{
//                resultDTO.setSuccess(true);
//                resultDTO.setData("add to cart successfully "+JSON.toJSONString(response));
//            }
//            measureLogger.setSuccess(true)
//                    .setInvokeEndTime(System.currentTimeMillis())
//                    .setEmpId(systemDTO.getOperator())
//                    .setContent("/trade/jobId="+systemDTO.getSite())
//                    .end();
//            return resultDTO;
//        }else{
//            CartAddRequest cartAddRequest = getAddCartRequest(buyerId, items, quantities, currency,shipTo ,business);
//            log.warn(JSON.toJSONString(cartAddRequest));
//            Response response = cartFacade.add(cartAddRequest);
//            if (response.isSuccess()) {
//                resultDTO.setSuccess(true);
//                resultDTO.setData("add to cart successfully");
//            } else {
//                resultDTO.setSuccess(true);
//                resultDTO.setData(response.getErrorCode().getLogMessage() + response.getErrorCode().getDisplayMessage()+" traceId: " + EagleEye.getTraceId());
//            }
//            measureLogger.setSuccess(true)
//                    .setInvokeEndTime(System.currentTimeMillis())
//                    .setEmpId(systemDTO.getOperator())
//                    .setContent("/trade/jobId="+systemDTO.getSite())
//                    .end();
//            return resultDTO;
//        }
    }
//    private DataRequestContion getDataRequestContion(List<String> tags) {
//        DataRequestContion dataRequestContion = new DataRequestContion();
//        dataRequestContion.setEachTagDataNum(1L);
//        dataRequestContion.setGetAllSceneData(!CollectionUtils.isNotEmpty(tags));
//        dataRequestContion.setTotalNum(20000L);
//        dataRequestContion.setIcDataTags(tags);
//        return dataRequestContion;
//    }

    //get items randomly
//    public Map<Long, Set<Long>> getItems(String tag) {
//        List<String> tags = new ArrayList<>();
//        if (!tag.isEmpty()) {
//            tags.add(tag);
//        }
//        DataRequestContion dataRequestContion = getDataRequestContion(tags);
//        List<DataResponseDTO> dataResponseDTOs = dataModelRemoteService.getProductIdsByCondition(dataRequestContion, false);
//
//        Map<Long, Set<Long>> validItems = new HashMap<>();
//        dataResponseDTOs.forEach(item -> {
//            if (validItems.size() > ADD_ITEM_MAX_SIZE) {
//                return;
//            }
//            if (item != null) {
//                long itemId = item.getItemId();
//                Set<Long> skuIds = getValidSkuIds(itemId);
//                if (CollectionUtils.isNotEmpty(skuIds)) {
//                    validItems.put(itemId, skuIds);
//                }
//            }
//        });
//        return validItems;
//    }

    //通过itemID，获取其中一个SKU
/*    public Map<Long, Long> createItemMap(Long itemId) {
        log.warn("createItemMap itemId: {}", itemId);
        Map<Long, Long> validItems = new HashMap<>();
        Set<Long> skuIds = getValidSkuIds(itemId);
        if (CollectionUtils.isNotEmpty(skuIds)) {
            Long skuId=skuIds.iterator().next();
            validItems.put(itemId, skuId);
        }
        return validItems;
    }*/

    //parameter construction
/*    public CartAddRequest addCartRequest(long buyerId, Map<Long, Set<Long>> items) {
            CartAddRequest cartAddRequest = new CartAddRequest();

            Map<String, String> headers = new HashMap<>();
            headers.put("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_13_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/73.0.3683.103 Safari/537.36");
            headers.put("Host", "localhost:7003");
            cartAddRequest.setHttpHeaders(headers);
            cartAddRequest.setDebugInfo(false);
            cartAddRequest.setCoverage(true);

            List<CartAddItemDTO> cartAddItemDTOS = new ArrayList<>();

            for (Long itemId : items.keySet()) {
                for (Long skuId : items.get(itemId)) {
                    CartAddItemDTO cartAddItemDTO = new CartAddItemDTO();
                    cartAddItemDTO.setItemId(itemId);
                    cartAddItemDTO.setQuantity(1);
                    cartAddItemDTO.setSkuId(skuId);
                    cartAddItemDTOS.add(cartAddItemDTO);
                }
            }
            cartAddRequest.setUserId(buyerId);
            cartAddRequest.setAddItems(cartAddItemDTOS);
            cartAddRequest.setCurrency("USD");
            return cartAddRequest;
        }*/

    @Override
    public CartAddRequest getAddCartRequest(long buyerId, Map<Long, Long> items, Map<Long, Integer> quantities, String currency, String shipTo, String business) {

        CartAddRequest cartAddRequest = new CartAddRequest();
        Map<String, String> headers = new HashMap<>();
        headers.put("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_13_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/73.0.3683.103 Safari/537.36");
        headers.put("Host", "localhost:7003");
        cartAddRequest.setHttpHeaders(headers);
        cartAddRequest.setDebugInfo(false);

        List<CartAddItemDTO> cartAddItemDTOS = new ArrayList<>();
        for (Long itemId : items.keySet()) {
            CartAddItemDTO cartAddItemDTO = new CartAddItemDTO();
            cartAddItemDTO.setItemId(itemId);
            cartAddItemDTO.setQuantity(quantities.get(itemId));
            cartAddItemDTO.setSkuId(items.get(itemId));
            cartAddItemDTOS.add(cartAddItemDTO);
        }
        cartAddRequest.setUserId(buyerId);
        cartAddRequest.setCartShipToDTO(ConvertParam.getCartShipTo(shipTo));
        cartAddRequest.setAddItems(cartAddItemDTOS);
        cartAddRequest.setCurrency(currency);
        cartAddRequest.setBusiness(business);
        return cartAddRequest;
    }

    //购物车清空-ml
    @Override
    public ResultDTO emptyCartItems(String params, SystemDTO systemDTO) {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        String business = jsonObject.getString(Constant.BUSINESS);
        if (business.equals("null")){
            business=null;
        }
        CartQueryRequest cartQueryRequest = getCartQueryRequest(buyerId, "US", "USD",business);
        Response<CartDetailResult> queryShoppingCartRes = cartFacade.query(cartQueryRequest);
        log.warn("[CartServiceImpl - query] query shoppingCart traceId: " + EagleEye.getTraceId() + ".result " + queryShoppingCartRes.isSuccess() + " .response: " + JSON.toJSONString(queryShoppingCartRes));
        if (!queryShoppingCartRes.isSuccess()||queryShoppingCartRes.getModule() == null){
            resultDTO.setSuccess(false);
            resultDTO.setData("Failed to query shoppingCart!");
            resultDTO.setMessage("Failed to query shoppingCart!");
            return resultDTO;
        }
        List<CartItemDetailDTO> items = queryShoppingCartRes.getModule().getCartItems();
        List<Long> cartIds= new ArrayList<>();
        for (CartItemDetailDTO item : items) {
            cartIds.add(item.getIntentionItem().getProduct().getCartId());
        }
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(cartIds)) {
            resultDTO.setSuccess(true);
            resultDTO.setData("Empty shoppingCart successfully!");
            resultDTO.setMessage("Empty shoppingCart successfully!");
            return resultDTO;
        }
        CartDeleteRequest cartDeleteRequest = new CartDeleteRequest();
        cartDeleteRequest.setUserId(buyerId);
        Map<String, String> headers = new HashMap<>();
        headers.put("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_13_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/73.0.3683.103 Safari/537.36");
        headers.put("Host", "localhost:7003");
        cartDeleteRequest.setHttpHeaders(headers);
        cartDeleteRequest.setCartItemIds(cartIds);
        cartDeleteRequest.setBusiness(business);
        cartDeleteRequest.setCurrency("USD");
        log.warn("[CartServiceImpl - delete] before delete traceId: " + EagleEye.getTraceId() + ". request: " + JSON.toJSONString(cartDeleteRequest) + ". cartIds: " + JSON.toJSONString(cartIds) + ". Thread Id: " + Thread.currentThread().getId());
        Response response = cartFacade.delete(cartDeleteRequest);
        log.warn("[CartServiceImpl - delete] after delete traceId: " + EagleEye.getTraceId() + ".result" + response.isSuccess() + ". request: " + JSON.toJSONString(response) + ". cartIds: " + JSON.toJSONString(cartIds) + ". Thread Id: " + Thread.currentThread().getId());
        if (response.isSuccess()) {
            resultDTO.setSuccess(true);
            resultDTO.setData("Empty shoppingCart successfully!");
            resultDTO.setMessage("Empty shoppingCart successfully!");
        } else {
            resultDTO.setSuccess(true);
            resultDTO.setData(response.getErrorCode().getLogMessage() + response.getErrorCode().getDisplayMessage());
            resultDTO.setMessage(response.getErrorCode().getLogMessage() + response.getErrorCode().getDisplayMessage());
        }
        return resultDTO;
    }
//查询购物车-ml
    @Override
    public ResultDTO getCartItemByID (String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        String business = jsonObject.getString(Constant.BUSINESS);
        if (business.equals("null")){
            business=null;
        }
        CartQueryRequest cartQueryRequest = getCartQueryRequest(buyerId, "US", "USD",business);
        Response<CartDetailResult> queryShoppingCartRes = cartFacade.query(cartQueryRequest);
        log.warn("[CartServiceImpl - query] query shoppingCart traceId: " + EagleEye.getTraceId() + ".result " + queryShoppingCartRes.isSuccess() + " .response: " + JSON.toJSONString(queryShoppingCartRes));
        if (!queryShoppingCartRes.isSuccess()||queryShoppingCartRes.getModule() == null){
            resultDTO.setSuccess(false);
            resultDTO.setData("Failed to query shoppingCart!");
            resultDTO.setMessage("Failed to query shoppingCart!");
            return resultDTO;
        }
        String s = JSON.toJSONString(queryShoppingCartRes.getModule().getCartItems(), SerializerFeature.DisableCircularReferenceDetect);
        log.warn(s);
        List<CartItemDetailDTO> cartItems = JSON.parseArray(s, CartItemDetailDTO.class);
        Map<String, QueryResultUnit> data = new LinkedHashMap<>();
        Map<String, QueryResultUnit> cartItem= QueryResultBuilder.buildQueryResult("购物车商品", null, null, cartItems);
        Map<String, QueryResultUnit> cart= QueryResultBuilder.buildQueryResult("购物车", null, null, queryShoppingCartRes.getModule());
        data.putAll(cartItem);
        log.warn(String.valueOf(data.isEmpty()));
        data.putAll(cart);
        log.warn(String.valueOf(data.isEmpty()));
        resultDTO.setSuccess(queryShoppingCartRes.isSuccess());
        log.warn(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
        resultDTO.setData(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
        return resultDTO;
    }

    @Override
    public ResultDTO queryBrainIdRealtimeGroups(String params, SystemDTO systemDTO) {
        ResultDTO result = new ResultDTO();
        log.warn("queryBrainIdRealtimeGroups params {}", params);
        JSONObject jsonObject = JSONObject.parseObject(params);
        String shipTo = jsonObject.getString(Constant.SHIP_TO_COUNTRY);
        String itemIDS = jsonObject.getString(Constant.PRODUCT_IDS);
        String skuIDS = jsonObject.getString(Constant.SKU_IDS);

        if(shipTo == null || shipTo.equals("")){
            shipTo = "US";
        }
        List<Long> itemIds = new ArrayList<>();
        List<Long> skuIds = new ArrayList<>();
        if(itemIDS == null || itemIDS.equals("")){
            itemIds = null;
        }else{
            String[] itemIDSStr = itemIDS.split(",");
            for (int i = 0; i < itemIDSStr.length; i++) {
                itemIds.add(Long.valueOf(itemIDSStr[i]));
            }
        }
        if(skuIDS == null || skuIDS.equals("")){
            skuIds = null;
        }else {
            String[] skuIDSStr = skuIDS.split(",");
            for (int i = 0; i < skuIDSStr.length; i++) {
                skuIds.add(Long.valueOf(skuIDSStr[i]));
            }
        }

        JSONObject response = null;
        try {
            response = HsfUtil.queryAEBrainIdRealtimeGroups(shipTo,itemIds,skuIds);
            log.warn(JSON.toJSONString(response));
        } catch (Exception e) {
            log.error(String.valueOf(e));
        }
        if (!response.getBoolean("success")) {
            result.setSuccess(false);
            result.setData("Failed to query "+JSON.toJSONString(response)+" traceId: " + EagleEye.getTraceId());
            result.setMessage("Failed to query "+JSON.toJSONString(response)+" traceId: " + EagleEye.getTraceId());
        }else{
            Map<String, QueryResultUnit> data = new LinkedHashMap<>();
            JSONObject res = new JSONObject();
            res.put("itemIdToAEBrainIdMap", JSON.toJSONString(response.getJSONObject("data").getJSONObject("itemIdToAEBrainIdMap")));
            res.put("skuIdToAEBrainIdMap", JSON.toJSONString(response.getJSONObject("data").getJSONObject("skuIdToAEBrainIdMap")));
            Map<String, QueryResultUnit> jsonFeature = null;
            try {
                jsonFeature = QueryResultBuilder.buildQueryResult("json", null, null, res);
                log.warn(String.valueOf(jsonFeature));
            } catch (Exception e) {
                log.error(String.valueOf(e));
            }

            data.putAll(jsonFeature);
            result.setData(JSONObject.toJSONString(data, SerializerFeature.PrettyFormat));
            result.setSuccess(true);
        }
        return result;
    }
}