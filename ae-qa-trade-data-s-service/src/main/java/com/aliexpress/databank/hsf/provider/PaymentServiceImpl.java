package com.aliexpress.databank.hsf.provider;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.common.lang.StringUtil;
import com.alibaba.ecommerce.exchange.dataobject.CurrencyType;
import com.alibaba.ecommerce.exchange.dataobject.GlobalPriceDTO;
import com.alibaba.ecommerce.exchange.dataobject.GlobalRateInfo;
import com.alibaba.ecommerce.exchange.dataobject.Language;
import com.alibaba.ecommerce.exchange.facade.GlobalExchangeFacade;
import com.alibaba.ecommerce.module.Response;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.global.exchange.api.facade.currency.CurrencyConfig;
import com.alibaba.global.landlord.LandlordContext;
import com.alibaba.global.money.Money;
import com.alibaba.global.order.management.api.facade.OrderQueryForBuyerFacade;
import com.alibaba.global.order.management.api.model.TradeOrderDTO;
import com.alibaba.global.payment.api.facade.PaymentFacade;
import com.alibaba.global.payment.api.facade.admin.PaymentOpsFacade;
import com.alibaba.global.payment.api.request.*;
import com.alibaba.global.payment.api.response.CheckoutQueryResponse;
import com.alibaba.global.payment.api.response.PayFundDetailQueryResponse;
import com.alibaba.global.payment.api.response.PaymentRecordQueryResponse;
import com.alibaba.global.payment.api.response.RefundDetailQueryResponse;
import com.alibaba.global.payment.api.vo.PaymentRecord;
import com.alibaba.global.payment.api.vo.RefundDetailItem;
import com.alibaba.global.payment.api.vo.RefundFundDetail;
import com.alibaba.global.qa.dc.model.builder.QueryResultBuilder;
import com.alibaba.global.qa.dc.model.param.SystemDTO;
import com.alibaba.global.qa.dc.model.result.QueryResultUnit;
import com.alibaba.global.qa.dc.model.result.ResultDTO;
import com.alibaba.payment.gateway.channels.ipay.vo.PaymentCodeDetail;
import com.alibaba.payment.gateway.channels.ipay.vo.PaymentCodeForm;
import com.alibaba.payment.gateway.channels.ipay.vo.PaymentCodeInfo;
import com.alibaba.saiga.api.SaigaCaseOperateFacade;
import com.alibaba.saiga.api.model.request.LinkCaseOperateRequest;
import com.alibaba.saiga.api.model.response.LinkCaseOperateResponse;
import com.alibaba.saiga.base.model.ApiResponse;
import com.alibaba.saiga.sdk.replay.service.ReplayService;
import com.aliexpress.databank.constant.AepayRouteFailReasonEum;
import com.aliexpress.databank.constant.CheckCaseEnum;
import com.aliexpress.databank.constant.Constant;
import com.aliexpress.databank.constant.PayCheckSceneEnum;
import com.aliexpress.databank.dataobject.*;
import com.aliexpress.databank.hsf.*;
import com.aliexpress.databank.service.ReverseService;
import com.aliexpress.databank.utils.*;
import com.aliexpress.issue.api.AeIssueCancelOrderWriteFacade;
import com.aliyun.openservices.log.common.LogContent;
import com.aliyun.openservices.log.common.QueriedLog;
import com.taobao.eagleeye.EagleEye;
import com.taobao.hsf.util.RequestCtxUtil;
import com.taobao.payment.boot.techsdk.facade.PaymentBootBizQueryFacade;
import com.taobao.payment.boot.techsdk.facade.request.QueryPayRecordsRequest;
import com.taobao.payment.boot.techsdk.facade.request.RefundQueryRequest;
import com.taobao.payment.boot.techsdk.facade.response.RefundQueryResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.net.URL;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.aliexpress.databank.utils.SlsClientUtil.*;


@HSFProvider(serviceInterface = PaymentService.class, serviceGroup = "HSF", serviceVersion = "1.0.0", clientTimeout = 20000)
public class PaymentServiceImpl implements PaymentService {

    private static final Logger logger = LoggerFactory.getLogger(PaymentServiceImpl.class);

    @Autowired
    private OrderService orderService;

    @Autowired
    SendDingMsgService sendDingMsgService;

    @Autowired
    DataPoolService dataPoolService;

    @Autowired
    private com.alibaba.global.payment.api.facade.PaymentQueryFacade paymentQueryFacade;

    @Autowired
    private com.alibaba.global.payment.api.facade.simple.PaymentQueryFacade paymentQueryFacade1;

    @Autowired
    private PaymentBootBizQueryFacade paymentBootBizQueryFacade;

    @Autowired
    private PaymentOpsFacade paymentOpsFacade;

    @Autowired
    private ExchangeRateServiceHsf exchangeRateServiceHsf;

    @Autowired
    private OrderQueryForBuyerFacade orderQueryForBuyerFacade;

    @Autowired
    private PaymentFacade paymentFacade;

    @Autowired
    PaymentUtil paymentUtil;

    @Autowired
    private ReverseService reverseService;

    @Autowired
    private AeIssueCancelOrderWriteFacade cancelOrderWriteFacade;


    private GlobalExchangeFacade globalExchangeFacade;

    @Autowired
    private SaigaCaseOperateFacade saigaCaseOperateFacade;

    @Autowired
    private ReplayService replayService;

    @Override
    public ResultDTO queryAepayRouteFail(String params, SystemDTO systemDTO) {
        JSONObject jsonObject = JSONObject.parseObject(params);
        String traceId = jsonObject.getString("traceId");

        ResultDTO resultDTO = new ResultDTO();
        //只有返回success页面才能透出
        resultDTO.setSuccess(true);

        try {
            //1、组装aepay路由失败查询sls请求参数，查询日志
            String queryParams = "aepay process failed" + " and \uD83D\uDCB8  and " + traceId;
            logger.info("【queryAepayRouteFail】sls request params: " + queryParams);
            ArrayList<QueriedLog> queriedLogs = SlsClientUtil.query(queryParams);
            logger.info("【queryAepayRouteFail】sls return object: " + JSON.toJSONString(queriedLogs));

            if (CollectionUtils.isEmpty(queriedLogs) || queriedLogs.get(0).mLogItem == null) {
                resultDTO.setData("query sls log fail,pls contact @若葵 to check!!!");
                return resultDTO;
            }

            //2、只需要获取第一条日志查询原因
            ArrayList<LogContent> logContents = queriedLogs.get(0).mLogItem.mContents;
            for (LogContent logContent : logContents) {
                //content内容：	"mValue": "2023-10-31 01:11:47.022 ERROR 163407 --- [HSFBizProcessor-DEFAULT-12-thread-37] global.payment.application.error  : oeId:AE|siteId:GLOBAL|trafficId:detail|pressTest:false|traceId:2101584516987399061443967e2a54|client:mtop2|clientIp:************|currentEnv:rg-us-east|isTraffic:N|className:|methodName:|errorMsg:aepay process failed class = {0} ,scene = {1},💸|request:\"CardBrandRuleProcessor\"|response:\"defaultscene\"|"
                if (logContent.mKey.equals("content")) {
                    String content = logContent.mValue;
                    String failReason = content.substring(content.indexOf("request") + 9, content.indexOf("\"|response"));
                    String msg = AepayRouteFailReasonEum.getRealMsg(failReason) != null ? AepayRouteFailReasonEum.getRealMsg(failReason) : failReason;
                    resultDTO.setData("aepay route fail reason:" + failReason + "，可能原因为：" + msg);
                    return resultDTO;
                }
            }

            //3、查询无结果则返回查询返回空
            resultDTO.setData("query sls log aepay route fail reason is empty,pls contact @若葵 to check!!!");
            return resultDTO;
        } catch (Exception e) {
            e.printStackTrace();
            resultDTO.setData("query sls log fail,pls contact @若葵 to check!!!");
            return resultDTO;
        }
    }

    @Override
    public ResultDTO queryPaymentInfo(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        Map<String, QueryResultUnit> data = new LinkedHashMap<>();
        data.putAll(queryPayInstructionDist(params));

        result.setSuccess(true);
        result.setData(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
        return result;

    }

    @Override
    public ResultDTO getAllCurs(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        Map<String, QueryResultUnit> data = new LinkedHashMap<>();
        List<String> list = new ArrayList<String>();

        try {
            Response<Map<String, Map<String, CurrencyConfig>>> curlist = globalExchangeFacade.getCurrencyMaps();
            Map<String, Map<String, CurrencyConfig>> curmap = curlist.getModule();
            System.out.println(curmap);
            if (!curmap.isEmpty()) {
                //quote currency
                Map<String, CurrencyConfig> quotelist = curmap.get("quote");
                //intention currency
                Iterator<Map.Entry<String, CurrencyConfig>> it = curmap.get("intention").entrySet().iterator();
                while (it.hasNext()) {
                    Map.Entry<String, CurrencyConfig> entry = it.next();
                    list.add("key = " + entry.getKey() + "simbol=" + entry.getValue().getSymbol() + ", value = " + entry.getValue());

                    System.out.println("key = " + entry.getKey() + ", value = " + entry.getValue());
                }

            } else {
                result.setSuccess(false);
                result.setData("get currencies empty");
                return result;
            }


        } catch (Exception e) {
            result.setSuccess(false);
            result.setData("get currencies exception");
            return result;
        }
        Map<String, QueryResultUnit> curmap = QueryResultBuilder.buildQueryResult("币种扩展信息", null, null, list);

        data.putAll(curmap);

        result.setSuccess(true);
        result.setData(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
        return result;
    }

    /**
     * 卡在init状态退款的订单，原退款单重试
     *
     * @param params ：orderIdStr=订单号，refundOrderNo=支付退款订单号，dpathEnv：指定dpath环境标
     */
    @Override
    public ResultDTO refundRetry(String params, SystemDTO systemDTO) {
        ResultDTO result = new ResultDTO();
        //只有success=true，页面才会有透出
        result.setSuccess(true);

        JSONObject jsonObject = JSONObject.parseObject(params);
        String bizOrderNo = jsonObject.getString(Constant.ORDER_ID);
        String refundOrderNo = jsonObject.getString("refundOrderNo");

        String dPathEnv = jsonObject.getString("dpathEnv");
        if (!StringUtils.isEmpty(dPathEnv)) {
            EagleEye.putUserData("dpath_env", dPathEnv);
        }

        Long buyerId = paymentUtil.getBuyerIdByOrderNo(bizOrderNo);
        if (buyerId == null) {
            result.setData("query buyerId by bizOrderNo return null,pls contact @若葵！！！");
            return result;
        }

        logger.info("【refundRetry】request params: " + params);

        if (StringUtils.isEmpty(refundOrderNo)) {
            //1、用户未指定退款单号，则查询卡在INIT状态的退款单号refundOrderNo
            RefundDetailQueryRequest refundDetailQueryRequest = new RefundDetailQueryRequest();
            refundDetailQueryRequest.setRouteId(buyerId);
            refundDetailQueryRequest.setPayerId(buyerId);
            refundDetailQueryRequest.setBizOrderNo(bizOrderNo);
            RefundDetailQueryResponse refundDetailQueryResponse = null;
            try {
                logger.info("【paymentQueryFacade.batchQueryRefundDetailInfo】request params: " + JSON.toJSONString(refundDetailQueryRequest));
                refundDetailQueryResponse = paymentQueryFacade.batchQueryRefundDetailInfo(refundDetailQueryRequest);
                logger.info("【paymentQueryFacade.batchQueryRefundDetailInfo】response: " + JSON.toJSONString(refundDetailQueryResponse));

            } catch (Exception e) {
                e.printStackTrace();
                result.setData("【paymentQueryFacade.batchQueryRefundDetailInfo】request exception！！！");
                return result;
            }

            if (refundDetailQueryResponse == null || CollectionUtils.isEmpty(refundDetailQueryResponse.getRefundDetailItemList())) {
                result.setData("【paymentQueryFacade.batchQueryRefundDetailInfo】response is empty！！！");
                return result;
            }

            Optional<RefundDetailItem> refundDetailItem = refundDetailQueryResponse.getRefundDetailItemList().stream().filter(item -> "INIT".equals(item.getRefundStatus())).findFirst();

            if (!refundDetailItem.isPresent()) {
                result.setData("the order cannot find init status refund order!");
                return result;
            }

            //2、原退款单退款重试
            refundOrderNo = refundDetailItem.get().getRefundOrderNo();
        }

        boolean b = false;
        try {
            logger.info("【paymentFacade.retryExecute】request params: buyerId=" + buyerId + ", refundOrderNo=" + refundOrderNo);
            b = paymentFacade.retryExecute(buyerId, refundOrderNo, "REFUND_DRIVE_EVENT");
        } catch (Exception e) {
            e.printStackTrace();
            result.setData("refund retry exception,pls contact @若葵 ！！！");
            return result;
        }

        result.setData(b ? "重试成功！" : "重试失败！");

        return result;
    }

    @Override
    public ResultDTO queryPaymentCommonExtInfo(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();

        Map<String, QueryResultUnit> data = new LinkedHashMap<>();
        LandlordContext.setTenantSpec("AE_GLOBAL", -1);


        JSONObject jsonObject = JSONObject.parseObject(params);
        String orderId = jsonObject.getString("orderIdStr");

        String pay_instruction_no = "";
        Response<String> commonext = null;

        PaymentRecordQueryRequest paymentRecordQueryRequest = new PaymentRecordQueryRequest();
        paymentRecordQueryRequest.setBizOrderNo(orderId);
        paymentRecordQueryRequest.setPlatform("AE");
        paymentRecordQueryRequest.setPayerId(jsonObject.getLong(Constant.BUYER_ID));
        paymentRecordQueryRequest.setRouteId(jsonObject.getLong(Constant.BUYER_ID));
        paymentRecordQueryRequest.setSource("global.trade");

        try {
            PaymentRecordQueryResponse paymentRecordQueryResponse = paymentQueryFacade.queryPaymentRecordsByBizOrderId(paymentRecordQueryRequest);

            if (paymentRecordQueryResponse.getSucceeded()) {
                List<PaymentRecord> paymentRecordList = paymentRecordQueryResponse.getPaymentRecordList();
                if (paymentRecordList.size() > 0) {
                    ZonedDateTime date = paymentRecordList.get(0).getPayRequestTime();
                    pay_instruction_no = paymentRecordList.get(0).getInstructionNo();
                    for (int i = 0; i < paymentRecordList.size(); i++) {
                        if (paymentRecordList.get(i).getPayRequestTime().isAfter(date)) {
                            date = paymentRecordList.get(i).getPayRequestTime();
                            pay_instruction_no = paymentRecordList.get(i).getInstructionNo();

                        }
                    }

                } else {
                    result.setSuccess(false);
                    result.setData("no paymentRecordList");
                    return result;
                }

            }
            commonext = paymentOpsFacade.queryCommonExtInfo(jsonObject.getLong(Constant.BUYER_ID), "PAY_INSTRUCTION_EXT", pay_instruction_no);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setData("no queryPaymentRecordsByBizOrderId result");
            return result;
        }

        Map<String, QueryResultUnit> paymentInstructionDetail = QueryResultBuilder.buildQueryResult("支付扩展信息", null, null, commonext);
        data.putAll(paymentInstructionDetail);

        result.setData(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
        result.setSuccess(true);

        return result;

    }

    @Override
    public ResultDTO queryPaymentRecords(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();

        Map<String, QueryResultUnit> data = new LinkedHashMap<>();
        LandlordContext.setTenantSpec("AE_GLOBAL", -1);


        JSONObject jsonObject = JSONObject.parseObject(params);
        String orderId = jsonObject.getString("orderIdStr");
        String empId = systemDTO.getOperator();
        Response<String> commonext = null;

        PaymentRecordQueryRequest paymentRecordQueryRequest = new PaymentRecordQueryRequest();
        paymentRecordQueryRequest.setBizOrderNo(orderId);
        paymentRecordQueryRequest.setPlatform("AE");
        paymentRecordQueryRequest.setPayerId(jsonObject.getLong(Constant.BUYER_ID));
        paymentRecordQueryRequest.setRouteId(jsonObject.getLong(Constant.BUYER_ID));
        paymentRecordQueryRequest.setSource("global.trade");

        try {
            PaymentRecordQueryResponse paymentRecordQueryResponse = paymentQueryFacade.queryPaymentRecordsByBizOrderId(paymentRecordQueryRequest);

            if (paymentRecordQueryResponse.getSucceeded()) {
                List<PaymentRecord> paymentRecordList = paymentRecordQueryResponse.getPaymentRecordList();
                if (paymentRecordList.size() > 0) {
                    Map<String, QueryResultUnit> paymentInstructionDetail = QueryResultBuilder.buildQueryResult("支付信息", null, null, paymentRecordQueryResponse);
                    data.putAll(paymentInstructionDetail);

                    result.setData(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
                    result.setSuccess(true);
                    HsfUtil.measureAll("/payment/jobId="+systemDTO.getSite(),empId);

                } else {
                    result.setSuccess(false);
                    result.setData("no paymentRecordList");
                }
            }

        } catch (Exception e) {
            result.setSuccess(false);
            result.setData("no checkoutid or instructionid ByBizOrderId ");
        }
        return result;
    }


    @Override
    public ResultDTO queryPayInstructionRecods(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();

        Map<String, QueryResultUnit> data = new LinkedHashMap<>();
        LandlordContext.setTenantSpec("AE_GLOBAL", -1);

        JSONObject jsonObject = JSONObject.parseObject(params);
        String orderId = jsonObject.getString("orderIdStr");
        String empId = systemDTO.getOperator();

        QueryPayRecordsRequest queryPayRecordsRequest = new QueryPayRecordsRequest();
        queryPayRecordsRequest.setPayerId(jsonObject.getString(Constant.BUYER_ID));
        queryPayRecordsRequest.setBizOrderNo(orderId);
        queryPayRecordsRequest.setPlatform("AE");
        queryPayRecordsRequest.setSource("global.trade");
        try {

            String response = paymentOpsFacade.queryPayRecords(jsonObject.getLong(Constant.BUYER_ID), JSONObject.toJSONString(queryPayRecordsRequest));

            Map<String, QueryResultUnit> paymentInstructionDetail = QueryResultBuilder.buildQueryResult("支付指令", null, null, JSONObject.parseObject(response));
            data.putAll(paymentInstructionDetail);

            result.setData(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
            result.setSuccess(true);
            HsfUtil.measureAll("/payment/jobId="+systemDTO.getSite(),empId);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setData("no payRecords result");
        }

        return result;

    }

    @Override
    public Map<String, QueryResultUnit> queryPayInstructionDist(String params) throws Exception {

        JSONObject jsonObject = JSONObject.parseObject(params);
        String orderId = jsonObject.getString("orderIdStr");
        Long buyerId = jsonObject.getLong("buyerId");
        Map<String, QueryResultUnit> paymentDetail = new LinkedHashMap<>();


        PayFundDetailQueryRequest payFundDetailQueryRequest = new PayFundDetailQueryRequest();
        payFundDetailQueryRequest.setBizOrderNo(orderId);
        payFundDetailQueryRequest.setBuyerId(buyerId);

        PayFundDetailQueryResponse payFundDetailQueryResponse = new PayFundDetailQueryResponse();
        try {
            payFundDetailQueryResponse = paymentQueryFacade.queryPayFundDetail(payFundDetailQueryRequest);

            if (payFundDetailQueryResponse.isSucceeded()) {
                paymentDetail = QueryResultBuilder.buildQueryResult("支付单明细详情", null, null, payFundDetailQueryResponse.getPayTermFundsDetailList());
                return paymentDetail;

            }
        } catch (Exception e) {

            return paymentDetail;

        }

        return paymentDetail;

    }

    @Override
    public ResultDTO queryRefund(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();

        Map<String, QueryResultUnit> data = new LinkedHashMap<>();
        LandlordContext.setTenantSpec("AE_GLOBAL", -1);

        try {
            JSONObject refundData = getRefundData(params);

            if (null != refundData && !refundData.isEmpty()) {
                Map<String, QueryResultUnit> refundRecord = QueryResultBuilder.buildQueryResult("退款信息", null, null, refundData);
                data.putAll(refundRecord);
                result.setData(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
                result.setSuccess(true);

            }
        } catch (Exception e) {
            result.setSuccess(false);
            result.setData("no refund records result");
        }
        return result;
    }


    @Override
    public ResultDTO queryRefundByBiz(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        String empId = systemDTO.getOperator();
        Map<String, QueryResultUnit> data = new LinkedHashMap<>();
        LandlordContext.setTenantSpec("AE_GLOBAL", -1);
        JSONObject jsonObject = JSONObject.parseObject(params);
        String orderId = jsonObject.getString("orderIdStr");
        try {
            Long buyerId = null;
            Response<TradeOrderDTO> tradeOrderDTOResponse = orderQueryForBuyerFacade.queryTradeOrderById(Long.parseLong(orderId));
            if (tradeOrderDTOResponse.isSuccess()) {
                buyerId = tradeOrderDTOResponse.getModule().getBuyer().getBuyerId();
            } else {
                logger.error("获取订单买家信息异常：" + tradeOrderDTOResponse.getModule());
            }
            RefundQueryRequest refundQueryRequest = new RefundQueryRequest();
            refundQueryRequest.setBizOrderNo(orderId);
            refundQueryRequest.setPayerId(buyerId);
            refundQueryRequest.setPlatform("AE");
            refundQueryRequest.setSource("global.trade");
            RefundQueryResponse refundData = paymentBootBizQueryFacade.queryRefund(buyerId, refundQueryRequest);

            if (null != refundData && refundData.getRefundRecordVOList().size() > 0) {
                Map<String, QueryResultUnit> refundRecord = QueryResultBuilder.buildQueryResult("退款信息", null, null, refundData);
                data.putAll(refundRecord);
                result.setData(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
                result.setSuccess(true);
                HsfUtil.measureAll("/payment/jobId="+systemDTO.getSite(),empId);
            }
        } catch (Exception e) {
            result.setSuccess(false);
            result.setData("no refund records result");
        }
        return result;
    }


    /**
     * 获取支付记录信息
     *
     * @param params
     * @return
     */
    private PaymentRecordQueryResponse getPaymentData(String params) {
        JSONObject jsonObject = JSONObject.parseObject(params);
        String orderId = jsonObject.getString("orderIdStr");

        PaymentRecordQueryResponse paymentRecordQueryResponse = new PaymentRecordQueryResponse();
        PaymentRecordQueryRequest paymentRecordQueryRequest = new PaymentRecordQueryRequest();
        paymentRecordQueryRequest.setBizOrderNo(orderId);
        paymentRecordQueryRequest.setPlatform("AE");
        paymentRecordQueryRequest.setPayerId(jsonObject.getLong(Constant.BUYER_ID));
        paymentRecordQueryRequest.setRouteId(jsonObject.getLong(Constant.BUYER_ID));
        paymentRecordQueryRequest.setSource("global.trade");

        try {
            paymentRecordQueryResponse = paymentQueryFacade.queryPaymentRecordsByBizOrderId(paymentRecordQueryRequest);

        } catch (Exception e) {
            logger.error("获取支付记录信息异常");
        }
        return paymentRecordQueryResponse;
    }

    /**
     * 获取支付指令信息
     *
     * @param params
     * @return
     */
    private JSONArray getPaymentInstrumentNoData(String params) {
        LandlordContext.setTenantSpec("AE_GLOBAL", -1);

        JSONObject jsonObject = JSONObject.parseObject(params);
        String orderId = jsonObject.getString("orderIdStr");

        QueryPayRecordsRequest queryPayRecordsRequest = new QueryPayRecordsRequest();
        queryPayRecordsRequest.setPayerId(jsonObject.getString(Constant.BUYER_ID));
        queryPayRecordsRequest.setBizOrderNo(orderId);
        queryPayRecordsRequest.setPlatform("AE");
        queryPayRecordsRequest.setSource("global.trade");
        JSONArray instrumentlist = new JSONArray();
        try {

            String response = paymentOpsFacade.queryPayRecords(jsonObject.getLong(Constant.BUYER_ID), JSONObject.toJSONString(queryPayRecordsRequest));
            if (!StringUtil.isEmpty(response)) {
                instrumentlist = JSONObject.parseObject(response).getJSONArray("payInstructionVOList");
            }
        } catch (Exception e) {
            logger.error("获取支付指令信息异常");
        }
        return instrumentlist;
    }

    @Override
    public List<String> getBatchPayRealtionNoByBizOrderId(String orderId) {
        PaymentRecordQueryRequest paymentRecordQueryRequest = new PaymentRecordQueryRequest();
        paymentRecordQueryRequest.setBizOrderNo(orderId);
        paymentRecordQueryRequest.setPlatform("AE");
        paymentRecordQueryRequest.setSource("global.trade");
        List<String> list = new ArrayList<>();
        try {
            Response<TradeOrderDTO> tradeOrderDTOResponse = orderQueryForBuyerFacade.queryTradeOrderById(Long.parseLong(orderId));
            if (tradeOrderDTOResponse.isSuccess()) {
                Long buyerId = tradeOrderDTOResponse.getModule().getBuyer().getBuyerId();
                paymentRecordQueryRequest.setPayerId(buyerId);
                paymentRecordQueryRequest.setRouteId(buyerId);
            } else {
                logger.error("获取订单买家信息异常：" + tradeOrderDTOResponse.getModule());
            }
            PaymentRecordQueryResponse paymentRecordQueryResponse = paymentQueryFacade.queryPaymentRecordsByBizOrderId(paymentRecordQueryRequest);

            if (paymentRecordQueryResponse.getSucceeded()) {
                String checkoutOrderNo = paymentRecordQueryResponse.getCheckoutOrderNo();
                JSONObject res = HsfUtil.queryPayInstruction(checkoutOrderNo);
                JSONArray records = res.getJSONArray("records");
                if (records.size() > 0) {
                    for (int i = 0; i < records.size(); i++) {
                        list.add(records.getJSONObject(i).getString("batchPayRelationNo"));
                    }
                    logger.info("batchPayRelationNo:" + list.toString());
                    return list;
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    @Override
    public ResultDTO getPayPromotionInfo(String params, SystemDTO systemDTO) {
        ResultDTO result = new ResultDTO();

        Map<String, QueryResultUnit> data = new LinkedHashMap<>();
        LandlordContext.setTenantSpec("AE_GLOBAL", -1);

        JSONObject jsonObject = JSONObject.parseObject(params);
        String orderId = jsonObject.getString("orderIdStr");
        String checkoutOrder = jsonObject.getString("checkoutOrder");
        PaymentRecordQueryRequest paymentRecordQueryRequest = new PaymentRecordQueryRequest();
        paymentRecordQueryRequest.setBizOrderNo(orderId);
        paymentRecordQueryRequest.setPlatform("AE");
        paymentRecordQueryRequest.setPayerId(jsonObject.getLong(Constant.BUYER_ID));
        paymentRecordQueryRequest.setRouteId(jsonObject.getLong(Constant.BUYER_ID));
        paymentRecordQueryRequest.setSource("global.trade");
        try {
            if (StringUtil.isBlank(checkoutOrder)) {
                PaymentRecordQueryResponse paymentRecordQueryResponse = paymentQueryFacade.queryPaymentRecordsByBizOrderId(paymentRecordQueryRequest);
                logger.info("PaymentRecordQueryResponse:" + paymentRecordQueryResponse);
                if (paymentRecordQueryResponse.getSucceeded()) {
                    checkoutOrder = paymentRecordQueryResponse.getCheckoutOrderNo();
                }
            }
            JSONObject res = HsfUtil.queryPayInstructionDist(checkoutOrder);
            JSONObject res1 = new JSONObject();
            if (res != null && !res.isEmpty()) {
                res1.put("订单金本位", res.containsKey("orderPromotion") ? res.getString("orderPromotion") : "无");
                res1.put("支付立减", res.containsKey("PAYMENT_PROMOTION_TYPE_COMMON") ? res.getString("PAYMENT_PROMOTION_TYPE_COMMON") : "无");
                res1.put("pointsPromotion", res.containsKey("PAYMENT_PROMOTION_TYPE_POINTS") ? res.getString("PAYMENT_PROMOTION_TYPE_POINTS") : "无");
                res1.put("aeBonus", res.containsKey("PAYMENT_PROMOTION_TYPE_AE_BONUS") ? res.getString("PAYMENT_PROMOTION_TYPE_AE_BONUS") : "无");
                res1.put("cashback", res.containsKey("PAYMENT_PROMOTION_TYPE_CASHBACK") ? res.getString("PAYMENT_PROMOTION_TYPE_CASHBACK") : "无");
            }
            Map<String, QueryResultUnit> promotion = QueryResultBuilder.buildQueryResult("优惠信息", null, null, res1);
            data.putAll(promotion);

            result.setData(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
            result.setSuccess(true);
        } catch (Exception e) {
            result.setSuccess(false);
            result.setData("no promotion result");
        }
        return result;
    }


    /**
     * 获取退款信息和指令记录
     *
     * @param params
     * @return
     */
    private JSONObject getRefundData(String params) {
        JSONObject jsonObject = JSONObject.parseObject(params);
        String orderId = jsonObject.getString("orderIdStr");
        try {

            JSONObject refundInstructionResult = HsfUtil.queryRefundInstruction(orderId, jsonObject.getLong(Constant.BUYER_ID));
            JSONObject refundInstructionDistResult = HsfUtil.queryRefundInstructionDist(orderId, jsonObject.getLong(Constant.BUYER_ID));
            logger.info("queryRefundInstruction:" + refundInstructionResult.toJSONString());
            logger.info("queryRefundInstructionDist:" + refundInstructionDistResult.toJSONString());

            JSONObject result = new JSONObject();
            result.put("queryRefundInstruction", refundInstructionResult);
            result.put("queryRefundInstructionDist", refundInstructionDistResult);

            return result;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }


    /**
     * check the offline payment message to trade system,for the button in orderlist page
     */
    @Override
    public ResultDTO checkOfflinePaymentMsgData(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();

        Map<String, QueryResultUnit> data = new LinkedHashMap<>();
        LandlordContext.setTenantSpec("AE_GLOBAL", -1);


        JSONObject jsonObject = JSONObject.parseObject(params);
        String orderId = jsonObject.getString("orderIdStr");
        List<String> resultList = new ArrayList<>();


        try {
            //获取日志
            List<Object> successList = slsSceneData(PayCheckSceneEnum.PAYMENT_CODE_MESSAGE.name(), orderId);
            if (successList.size() != 0) {
                JSONObject successLog1 = (JSONObject) successList.get(0);
                if (successLog1.containsKey("attributes")) {
                    JSONObject successLog = (JSONObject) (successLog1.get("attributes"));
                    if (successLog.containsKey("hideOfflineCodeTime")) {
                        resultList.add(CheckCaseEnum.PAYMENT_CODE_MESSAGE_HIDEOFFLINECODETIME.getName() + "----（✅）");
                    } else {
                        resultList.add(CheckCaseEnum.PAYMENT_CODE_MESSAGE_HIDEOFFLINECODETIME.getName() + "----（❌）");

                    }
                    if (successLog.containsKey("appPayResultUrl")) {
                        resultList.add(CheckCaseEnum.PAYMENT_CODE_MESSAGE_APPPAYRESULTURL.getName() + "----（✅）");
                    } else {
                        resultList.add(CheckCaseEnum.PAYMENT_CODE_MESSAGE_APPPAYRESULTURL.getName() + "----（❌）");

                    }
                    if (successLog.containsKey("offlinePayment")) {
                        resultList.add(CheckCaseEnum.PAYMENT_CODE_MESSAGE_OFFLINEPAYMENT.getName() + "----（✅）");
                    } else {
                        resultList.add(CheckCaseEnum.PAYMENT_CODE_MESSAGE_OFFLINEPAYMENT.getName() + "----（❌）");

                    }
                    if (successLog.containsKey("msitePayResultUrl")) {
                        resultList.add(CheckCaseEnum.PAYMENT_CODE_MESSAGE_MSITEPAYRESULTURL.getName() + "----（✅）");
                    } else {
                        resultList.add(CheckCaseEnum.PAYMENT_CODE_MESSAGE_MSITEPAYRESULTURL.getName() + "----（❌）");

                    }
                    if (successLog.containsKey("paymentMethodType")) {
                        resultList.add(CheckCaseEnum.PAYMENT_CODE_MESSAGE_PAYMENTMETHODTYPE.getName() + "----（✅）");
                    } else {
                        resultList.add(CheckCaseEnum.PAYMENT_CODE_MESSAGE_PAYMENTMETHODTYPE.getName() + "----（❌）");

                    }
                    if (successLog.containsKey("payResultUrl")) {
                        resultList.add(CheckCaseEnum.PAYMENT_CODE_MESSAGE_PAYRESULTURL.getName() + "----（✅）");
                    } else {
                        resultList.add(CheckCaseEnum.PAYMENT_CODE_MESSAGE_PAYRESULTURL.getName() + "----（❌）");

                    }
                    if (successLog.containsKey("pcPayResultUrl")) {
                        resultList.add(CheckCaseEnum.PAYMENT_CODE_MESSAGE_PCPAYRESULTURL.getName() + "----（✅）");
                    } else {
                        resultList.add(CheckCaseEnum.PAYMENT_CODE_MESSAGE_PCPAYRESULTURL.getName() + "----（❌）");

                    }
                } else {
                    resultList.add(CheckCaseEnum.PAYMENT_CODE_MESSAGE_ATTRIBUTR.getName() + "----（❌）");

                }
                if (successLog1.containsKey("bizOrderId")) {
                    resultList.add(CheckCaseEnum.PAYMENT_CODE_MESSAGE_BIZORDERID.getName() + "----（✅）");
                } else {
                    resultList.add(CheckCaseEnum.PAYMENT_CODE_MESSAGE_BIZORDERID.getName() + "----（❌）");

                }
                if (successLog1.containsKey("checkoutOrderId")) {
                    resultList.add(CheckCaseEnum.PAYMENT_CODE_MESSAGE_CHECKOUTORDERID.getName() + "----（✅）");
                } else {
                    resultList.add(CheckCaseEnum.PAYMENT_CODE_MESSAGE_CHECKOUTORDERID.getName() + "----（❌）");

                }
                if (successLog1.containsKey("payPlanId")) {
                    resultList.add(CheckCaseEnum.PAYMENT_CODE_MESSAGE_PAYPLANID.getName() + "----（✅）");
                } else {
                    resultList.add(CheckCaseEnum.PAYMENT_CODE_MESSAGE_PAYPLANID.getName() + "----（❌）");

                }
                Map<String, QueryResultUnit> paymentcodemsg = QueryResultBuilder.buildQueryResult("支付发给交易线下码消息体校验", null, null, resultList);
                data.putAll(paymentcodemsg);

                result.setData(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
                result.setSuccess(true);


            } else {
                result.setSuccess(false);
                result.setData("no payment code  message for trade, pls check again!");
            }


        } catch (Exception e) {
            result.setSuccess(false);
            result.setData("no payment code  message for trade, pls check again!");
        }

        return result;
    }


    /**
     * 从日志中需要校验的请求体
     */
    private List<Object> slsSceneData(String scene, String uniqueID) {
        if (StringUtil.isEmpty(scene)) {
            return null;
        }
        //接口请求参数
        String requestPattern = "__input__: (.*),  __output__";

        String queryParam = "";
        List<String> response = null;
        List<Object> result = new ArrayList<>();
        if (scene.contains("REQUEST")) {
            if (scene.equals(PayCheckSceneEnum.CREATE_AND_PAY_REQUEST.name())) {
                queryParam = "__input__ and ipayagh.pay createAndPay and " + uniqueID;
            } else if (scene.equals(PayCheckSceneEnum.IPAY_REFUND_REQUEST.name())) {
                queryParam = "ipayagh.refund  __input__ Channel-Invoke NOT consult NOT query not batchQuery and " + uniqueID;
            }
            ArrayList<QueriedLog> logArrayList = query(queryParam);
            if (logArrayList != null) {
                response = slsResponse(logArrayList);
                result.addAll(getRequestData(response, requestPattern));
            }
        } else if (scene.contains("MESSAGE")) {
            if (scene.equals(PayCheckSceneEnum.PAY_SUCCESS_AUTH_MESSAGE.name())) {
                queryParam = "global_payment_event_topic and PAY and AUTHORIZED and " + uniqueID;
            } else if (scene.equals(PayCheckSceneEnum.PAY_SUCCESS_CAPTURE_MESSAGE.name())) {
                queryParam = "global_payment_event_topic and PAY and SUCCEED and " + uniqueID;
            } else if (scene.equals(PayCheckSceneEnum.PROMOTION_REVERSE_MESSAGE.name())) {
                queryParam = "global_payment_event_topic and promotionReverse and " + uniqueID;
            }
//            线下支付码
            else if (scene.equals(PayCheckSceneEnum.PAYMENT_CODE_MESSAGE.name())) {
                queryParam = "global_payment_event_topic and paymentCode and " + uniqueID;
            }
            ArrayList<QueriedLog> logArrayList = query(queryParam);
            if (logArrayList != null) {
                response = slsResponse(logArrayList);
                result.addAll(msgRemoveRepeat(response));
            }

        }
        return result;
    }


    /**
     * 从数据库或支付退款相关数据
     *
     * @param params
     * @return
     * @throws Exception
     */
    private PaymentDbDTO getDBData(String params) throws Exception {
        PaymentDbDTO paymentDbDTO = new PaymentDbDTO();
        try {
            //请求查询支付信息
            PaymentRecordQueryResponse paymentRecordQueryResponse = getPaymentData(params);
            if (!paymentRecordQueryResponse.isSucceeded()) {
                throw new Throwable("获取支付信息异常");
            }
            //checkoutOrderNo
            String checkoutOrderNo = paymentRecordQueryResponse.getCheckoutOrderNo();
            paymentDbDTO.setCheckoutOrderNo(checkoutOrderNo);
            logger.info("开始-------------checkoutOrderNo：" + checkoutOrderNo);
            //支付记录
            List<PaymentRecord> paymentRecordList = paymentRecordQueryResponse.getPaymentRecordList();
            logger.info("支付记录i：-=------" + paymentRecordList.get(0).getPayOptionCode());
            paymentDbDTO.setPaymentRecord(paymentRecordList.get(0));
            paymentDbDTO.setStatus(paymentRecordList.get(0).getStatus());
            paymentDbDTO.setInstructionNo(paymentRecordList.get(0).getInstructionNo());
            paymentDbDTO.setPaymentMethod(paymentRecordList.get(0).getPayOptionCode());
            //支付指令信息
            JSONArray instrumentNoData = getPaymentInstrumentNoData(params);
            if (instrumentNoData.isEmpty()) {
                logger.error("未获取到支付信息");
                return null;
            }
            JSONArray payInstructionDistVOList = instrumentNoData.getJSONObject(0).getJSONArray("payInstructionDistVOList");
            Integer payAmount = 0;
            Integer promotionAmount = 0;
            Integer orderPromotionAmount = 0;
            Integer paymentPromotionAmount = 0;
            Integer pointAmount = 0;
            Integer goodAmount = 0;
            Integer orderAmount = 0;
            for (int i = 0; i < payInstructionDistVOList.size(); i++) {
                JSONObject temp = payInstructionDistVOList.getJSONObject(i);
                if (temp.getString("fundType").equals("GOODS")) {
                    goodAmount = temp.getJSONObject("payAmount").getInteger("cent");
                    orderAmount = temp.getJSONObject("orderAmount").getInteger("cent");
                } else if (temp.getString("fundType").equals("PROMOTION")) {
                    paymentPromotionAmount = temp.getJSONObject("payAmount").getInteger("cent");
                    if (!temp.getJSONObject("bizExtAttrMap").isEmpty()) {
                        pointAmount = JSONObject.parseObject(temp.getJSONObject("bizExtAttrMap").getString("cashbackAmount")).getJSONObject("payAmount").getInteger("cent");
                    }
                } else if (temp.getString("fundType").equals("ORDER_PROMOTION")) {
                    orderPromotionAmount = temp.getJSONObject("payAmount").getInteger("cent");
                }
            }
            //数据库支付优惠和
            promotionAmount = orderPromotionAmount + paymentPromotionAmount;
            //数据库实际支付金额和
            payAmount = goodAmount - orderPromotionAmount - paymentPromotionAmount;
            paymentDbDTO.setOrderPromotionAmount(orderPromotionAmount);
            paymentDbDTO.setPayAmount(payAmount);
            paymentDbDTO.setPaymentPromotionAmount(paymentPromotionAmount);
            paymentDbDTO.setPointsAmount(pointAmount);
            paymentDbDTO.setPromotionAmount(promotionAmount);
            paymentDbDTO.setOrderAmount(orderAmount);
            //请求查询退款信息
            JSONObject refundResult = getRefundData(params);
            paymentDbDTO.setRefundData(refundResult);

        } catch (Throwable e) {
            e.printStackTrace();
        }

        return paymentDbDTO;

    }


    /**
     * 校验商品的商品名称长度不超过127
     * 是否有校验商品url
     * 是否拆分到子单且金额不为0
     * 校验订单金额 支付金额 优惠金额
     */
    private PaymentResultDTO checkCreateAndPay(PaymentDbDTO paymentDbDTO) {
        List<String> resultList = new ArrayList<>();
        PaymentResultDTO paymentResultDTO = new PaymentResultDTO();
        try {
            //提交支付
            List<Object> payList = slsSceneData(PayCheckSceneEnum.CREATE_AND_PAY_REQUEST.name(), paymentDbDTO.getCheckoutOrderNo());
            if (payList.size() != 0) {
                JSONObject createAndPayRequest = (JSONObject) payList.get(0);
                logger.info("开始-------------提交支付日志：" + createAndPayRequest);
                List<String> paymentMethodList1 = Constant.PAYMENT_METHOD_CHECK_GOODNAME;
                List<String> paymentMethodList2 = Constant.PAYMENT_METHOD_CHECK_GOODURL;
                List<String> paymentMethodList3 = Constant.PAYMENT_METHOD_SPLIT_ORDER;
                Map<String, Object> metaData = Constant.PAYMENT_METHOD_META_DAYA;
                logger.info("paymentMethodList1" + paymentMethodList1.toString());
                String goodsNameResult = "（✅）";
                String goodsUrlResult = "（✅）";
                String splitOrderResult = "（✅）";
                String metaDataResult = "（✅）";
                JSONArray orders = createAndPayRequest.getJSONObject("orderGroup").getJSONArray("orders");
                JSONArray paymentDetails = createAndPayRequest.getJSONArray("paymentDetails");

                //订单金额-不包含订单金额本位，包含支付金本位
                Integer orderAmount = 0;
                //循环多主
                for (int i = 0; i < orders.size(); i++) {
                    orderAmount = orderAmount + orders.getJSONObject(i).getJSONObject("orderAmount").getInteger("value");
                    //单主循环多子
                    JSONArray subOrders = orders.getJSONObject(i).getJSONArray("subOrders");
                    for (int j = 0; j < subOrders.size(); j++) {
                        //多sku
                        JSONArray goods = subOrders.getJSONObject(j).getJSONArray("goods");
                        for (int k = 0; k < goods.size(); k++) {
                            //goodsName
                            if (!goods.getJSONObject(k).containsKey("goodsName")) {
                                goodsNameResult = "商品信息不包含goodsName（❌）";
                            }
                            String goodsName = goods.getJSONObject(k).getString("goodsName");
                            //大于128字符-->122字符+"..."
                            if (goodsName.substring(goodsName.length() - 4, goodsName.length() - 1).equals("...") && goodsName.length() > 125) {
                                goodsNameResult = "goodsName大于127处理不正确（❌）";
                            }
                            //最大127字符
                            else if (goodsName.length() > 127) {
                                goodsNameResult = "goodsName大于127（❌）";
                            }
                            //goodsUrl
                            if (!goods.getJSONObject(k).containsKey("goodsUrl")) {
                                goodsUrlResult = "不包含goodsUrl（❌）";
                            }
                            //splitOrder
                            if (!goods.getJSONObject(k).containsKey("extendInfo")) {
                                splitOrderResult = "未拆分到子单（❌）";
                            } else if (goods.getJSONObject(k).containsKey("extendInfo")) {
                                JSONObject extendInfo = JSONObject.parseObject(goods.getJSONObject(k).getString("extendInfo"));
                                if (JSONObject.parseObject(extendInfo.getString("payAmount")).getInteger("value") == 0) {
                                    splitOrderResult = "未拆分到子单金额为0（❌）";
                                }
                            }

                        }
                    }
                }
                JSONObject paymentMethodMetaData = new JSONObject();
                Integer methodPaymentAmount = 0;
                Integer promotionAmount = 0;
                for (int j = 0; j < paymentDetails.size(); j++) {
                    JSONObject paymentMethodJson = paymentDetails.getJSONObject(j).getJSONObject("paymentMethod");
                    JSONObject paymentAmount = paymentDetails.getJSONObject(j).getJSONObject("paymentAmount");
                    if (paymentMethodJson.getString("paymentMethodType").equals(paymentDbDTO.getPaymentMethod())) {
                        paymentMethodMetaData = JSONObject.parseObject(paymentMethodJson.getString("paymentMethodMetaData"));
                        methodPaymentAmount = methodPaymentAmount + paymentAmount.getInteger("value");
                    } else if ("MERCHANT_COUPON".equals(paymentMethodJson.getString("paymentMethodType"))) {
                        promotionAmount = promotionAmount + paymentAmount.getInteger("value");
                        if ("PAD".equals(paymentDbDTO.getPaymentMethod())) {
                            promotionAmount = promotionAmount - paymentDbDTO.getPayAmount();
                        }
                    } else {
                        logger.error("出现其他类型的金额:" + paymentMethodJson.getString("paymentMethodType"));
                    }
                }

                if (paymentMethodList1.contains(paymentDbDTO.getPaymentMethod())) {
                    resultList.add(CheckCaseEnum.CREATE_AND_PAY_GOODS_NAME.getName() + "----" + goodsNameResult);
                }
                if (paymentMethodList2.contains(paymentDbDTO.getPaymentMethod())) {
                    resultList.add(CheckCaseEnum.CREATE_AND_PAY_GOODS_URL.getName() + "----" + goodsUrlResult);
                }
                if (paymentMethodList3.contains(paymentDbDTO.getPaymentMethod())) {
                    resultList.add(CheckCaseEnum.CREATE_AND_PAY_SPLIT_ORDER.getName() + "----" + splitOrderResult);
                }
                if (!orderAmount.equals(paymentDbDTO.getOrderAmount())) {
                    resultList.add(CheckCaseEnum.CREATE_AND_PAY_MONEY.getName() + "----" + "订单金本位优惠后金额不对（❌）");
                } else {
                    resultList.add(CheckCaseEnum.CREATE_AND_PAY_MONEY.getName() + "----" + "订单金本位优惠后金额（✅）");
                }
                if (!promotionAmount.equals(paymentDbDTO.getPromotionAmount())) {
                    resultList.add(CheckCaseEnum.CREATE_AND_PAY_MONEY.getName() + "----" + "总优惠金额不对（❌）");
                } else {
                    resultList.add(CheckCaseEnum.CREATE_AND_PAY_MONEY.getName() + "----" + "总优惠金额校验（✅）");
                }
                if (metaData.containsKey(paymentDbDTO.getPaymentMethod())) {
                    resultList.add(CheckCaseEnum.CREATE_AND_PAY_META_DATA.getName() + "----" + metaDataResult);
                }

                paymentResultDTO.setRequestLog((JSONObject) payList.get(0));
                paymentResultDTO.setResultData(resultList);
                paymentResultDTO.setPaymentMethod(paymentDbDTO.getPaymentMethod());
                paymentResultDTO.setCheckoutOrderNo(paymentDbDTO.getCheckoutOrderNo());

            } else {
                paymentResultDTO.setRequestLog(null);
                paymentResultDTO.setResultData(Arrays.asList("未查询到提交支付日志-----（❌）"));
                paymentResultDTO.setPaymentMethod(paymentDbDTO.getPaymentMethod());
                paymentResultDTO.setCheckoutOrderNo(paymentDbDTO.getCheckoutOrderNo());
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        return paymentResultDTO;
    }

    /**
     * 解冻消息判断
     * ○ 请求中可退金额是否正确
     * ○ 可退支付优惠金额是否正确
     * ○  有snapshotId-优惠快照时，returnPromotionAmount可退优惠金额包含points
     * ○  无snapshotId，returnPromotionAmount不包含points金额
     * ○  attributes中points/aeBonus金额正确
     */
    private PaymentResultDTO reverseMessageResult(PaymentDbDTO paymentDbDTO, String orderId) throws Exception {
        PaymentResultDTO paymentResultDTO = new PaymentResultDTO();
        List<Object> reverseList = slsSceneData(PayCheckSceneEnum.PROMOTION_REVERSE_MESSAGE.name(), orderId);
        paymentResultDTO.setStatus(paymentDbDTO.getStatus());
        paymentResultDTO.setPaymentMethod(paymentDbDTO.getPaymentMethod());
        paymentResultDTO.setCheckoutOrderNo(paymentDbDTO.getCheckoutOrderNo());
        if (reverseList != null && reverseList.isEmpty()) {
            paymentResultDTO.setRequestLog(null);
            paymentResultDTO.setResultData(Collections.singletonList("无日志"));
            return paymentResultDTO;
        }
        JSONArray refundInstructionList = paymentDbDTO.getRefundData().getJSONObject("queryRefundInstruction").getJSONArray("records");
        JSONArray refundInstructionDistList = paymentDbDTO.getRefundData().getJSONObject("queryRefundInstructionDist").getJSONArray("records");
        List<String> resultData = new ArrayList<>();
        //付款失败解冻
        if (refundInstructionList.size() == 0 && reverseList.size() != 0) {
            resultData.add("付款失败解冻,请手动比对");
            paymentResultDTO.setRequestLog((JSONObject) reverseList.get(0));
            return paymentResultDTO;
        }
        int flag = 0;
        JSONObject message = (JSONObject) reverseList.get(0);
        JSONObject promotion = HsfUtil.queryPayInstructionDist(paymentDbDTO.getCheckoutOrderNo());
        List<String> instructionNoList = new ArrayList<>();
        for (int i = 0; i < refundInstructionList.size(); i++) {
            String instructionNo = refundInstructionList.getJSONObject(i).getString("instructionNo");
            instructionNoList.add(instructionNo);
            String refundAmount = refundInstructionList.getJSONObject(i).getString("refundAmount");
            String returnPayableAmount = message.getJSONObject("returnPayableAmount").getString("price");
            if (refundAmount.equals(returnPayableAmount)) {
                resultData.add("instructionNo：" + instructionNo + ",请求中退款金额是否正确--（✅）");
                flag = 1;
            }
        }
        if (flag == 0) {
            resultData.add("请求中退款金额是否正确--（❌）");
        }
        for (int j = 0; j < refundInstructionDistList.size(); j++) {
            for (int k = 0; k < instructionNoList.size(); k++) {
                if (refundInstructionDistList.getJSONObject(j).getString("instructionNo").equals(instructionNoList.get(k))) {
                    if (("PROMOTION").equals(refundInstructionDistList.getJSONObject(j).getString("fundType"))) {
                        Integer refundAmount = refundInstructionDistList.getJSONObject(j).getInteger("refundAmount");
                        Integer returnPromotionAmount = message.getJSONObject("returnPromotionAmount").getInteger("price");
                        //有营销优惠
                        if (message.containsKey("snapshotId") && StringUtil.isNotEmpty(message.getString("snapshotId"))) {
                            //有points或aeBonus优惠
                            if (message.getJSONObject("attributes").containsKey("pointPromotionList")) {
                                JSONObject promotionJson = JSONArray.parseArray(message.getJSONObject("attributes").getString("pointPromotionList")).getJSONObject(0);
                                String cashbackCentValue = promotionJson.getString("cashbackCentValue");
                                String cashbackType = promotionJson.getString("cashbackType");
                                if (RegUtil.getNumber(promotion.getString(cashbackType)).equals(cashbackCentValue)) {
                                    resultData.add("instructionNo：" + instructionNoList.get(k) + ",请求中points/aeBonus金额是否正确--（✅）");
                                }
                            }
                            if (!returnPromotionAmount.equals(refundAmount)) {
                                resultData.add("instructionNo：" + instructionNoList.get(k) + ",有snapshotId-优惠快照时，returnPromotionAmount可退优惠金额不包含points--（✅）");
                            } else {
                                resultData.add("instructionNo：" + instructionNoList.get(k) + ",有snapshotId-优惠快照时，returnPromotionAmount可退优惠金额不包含points--（❌）");
                            }
                        } else {
                            if (returnPromotionAmount.equals(refundAmount)) {
                                resultData.add("instructionNo：" + instructionNoList.get(k) + ",无snapshotId，returnPromotionAmount包含points金额--（✅）");
                            } else {
                                resultData.add("instructionNo：" + instructionNoList.get(k) + ",无snapshotId，returnPromotionAmount包含points金额--（❌）");
                            }
                        }
                    }
                }
            }
        }
        paymentResultDTO.setRequestLog(message);
        paymentResultDTO.setResultData(resultData);

        return paymentResultDTO;
    }

    /**
     * 退款请求判断
     * ○ 请求中退款金额（不包含订单优惠）是否正确
     * ○ 请求中退款渠道是否正确
     */
    private PaymentResultDTO refundResult(PaymentDbDTO paymentDbDTO, String orderId) {
        PaymentResultDTO paymentResultDTO = new PaymentResultDTO();
        if (paymentDbDTO.getRefundData().isEmpty() || paymentDbDTO.getRefundData().getJSONObject("queryRefundInstruction").getJSONArray("records").isEmpty()) {
            paymentResultDTO.setRequestLog(null);
            paymentResultDTO.setResultData(Collections.singletonList("未发起退款"));
            paymentResultDTO.setStatus(paymentDbDTO.getStatus());
            paymentResultDTO.setPaymentMethod(paymentDbDTO.getPaymentMethod());
            paymentResultDTO.setCheckoutOrderNo(paymentDbDTO.getCheckoutOrderNo());
            return paymentResultDTO;
        }
        JSONArray refundInstructionList = paymentDbDTO.getRefundData().getJSONObject("queryRefundInstruction").getJSONArray("records");
        List<String> resultData = new ArrayList<>();
        JSONObject requestLog = new JSONObject();
        for (int i = 0; i < refundInstructionList.size(); i++) {
            String instructionNo = refundInstructionList.getJSONObject(i).getString("instructionNo");
            Integer refundAmount = refundInstructionList.getJSONObject(i).getInteger("refundAmount");
            String method = refundInstructionList.getJSONObject(i).getString("instrumentCode");

            List<Object> refundList = slsSceneData(PayCheckSceneEnum.IPAY_REFUND_REQUEST.name(), instructionNo);
            if (refundList == null || refundList.isEmpty()) {
                resultData.add("instructionNo：" + instructionNo + ",无日志❌");
            } else {
                JSONArray refundDetails = ((JSONObject) refundList.get(0)).getJSONArray("refundToDetails");
                int flag = 0;
                Integer refundToAmount = 0;
                for (int j = 0; j < refundDetails.size(); j++) {
                    String paymentMethodType = refundDetails.getJSONObject(j).getJSONObject("refundMethod").getString("paymentMethodType");
                    refundToAmount = refundToAmount + refundDetails.getJSONObject(j).getJSONObject("refundToAmount").getInteger("value");
                    if (paymentMethodType.equals(method)) {
                        resultData.add("instructionNo：" + instructionNo + ",请求中退款渠道是否正确--（✅）");
                        flag = 1;
                    }
                }
                if (refundAmount.equals(refundToAmount)) {
                    resultData.add("instructionNo：" + instructionNo + ",请求中退款金额是否正确--（✅）");
                } else {
                    resultData.add("instructionNo：" + instructionNo + ",请求中退款金额是否正确--（❌）");
                }
                if (flag == 1) {
                    resultData.add("instructionNo：" + instructionNo + ",请求中退款渠道是否正确--（❌）");
                }
            }
            requestLog.put(instructionNo, ((JSONObject) refundList.get(0)));
        }
        paymentResultDTO.setRequestLog(requestLog);
        paymentResultDTO.setResultData(resultData);
        paymentResultDTO.setStatus(paymentDbDTO.getStatus());
        paymentResultDTO.setPaymentMethod(paymentDbDTO.getPaymentMethod());
        paymentResultDTO.setCheckoutOrderNo(paymentDbDTO.getCheckoutOrderNo());

        return paymentResultDTO;
    }

    /**
     * ○ 报文中的payOptionCode和实际一致
     * ○ 订单优惠后金额actualPaidAmount是否正确
     * ○ 实际支付金额actualPaidCash是否正确
     * ○ 给钱包points优惠cashbackAmount是否正确
     * ○ 营销优惠金额paymentPromotionPayAmount是否正确
     */
    private PaymentResultDTO successMessageResult(PaymentDbDTO paymentDbDTO, String orderId) {
        //获取日志
        List<Object> successList = slsSceneData(PayCheckSceneEnum.PAY_SUCCESS_CAPTURE_MESSAGE.name(), orderId);
        PaymentResultDTO paymentResultDTO = new PaymentResultDTO();
        List<String> resultList = new ArrayList<>();
        if (successList.size() != 0 && "PAID".equals(paymentDbDTO.getStatus())) {
            JSONObject successLog = (JSONObject) successList.get(0);
            if (successLog.containsKey("actualPaidAmount") && (successLog.getJSONObject("actualPaidAmount").getInteger("cent") == (paymentDbDTO.getPayAmount() + paymentDbDTO.getPaymentPromotionAmount()))) {
                resultList.add(CheckCaseEnum.PAY_SUCCESS_MESSAGE_PAID_AMOUNT.getName() + "----（✅）");
            } else {
                resultList.add(CheckCaseEnum.PAY_SUCCESS_MESSAGE_PAID_AMOUNT.getName() + "----（❌）");
            }
            if (successLog.containsKey("payOptionCode") && successLog.getString("payOptionCode").equals(paymentDbDTO.getPaymentMethod())) {
                resultList.add(CheckCaseEnum.PAY_SUCCESS_MESSAGE_METHOD.getName() + "----（✅）");
            } else {
                resultList.add(CheckCaseEnum.PAY_SUCCESS_MESSAGE_METHOD.getName() + "----（❌）");
            }
            if (successLog.containsKey("actualPaidCash") && (successLog.getJSONObject("actualPaidCash").getInteger("cent").equals(paymentDbDTO.getPayAmount()))) {
                resultList.add(CheckCaseEnum.PAY_SUCCESS_MESSAGE_PAID_CASH.getName() + "----（✅）");
            } else {
                resultList.add(CheckCaseEnum.PAY_SUCCESS_MESSAGE_PAID_CASH.getName() + "----（❌）");
            }

            if (!successLog.getJSONObject("attributes").isEmpty() && successLog.getJSONObject("attributes").containsKey("cashbackAmount")) {
                Integer money = JSONObject.parseObject(successLog.getJSONObject("attributes").getString("cashbackAmount")).getJSONObject("payAmount").getInteger("cent");
                if (money.equals(paymentDbDTO.getPointsAmount())) {
                    resultList.add(CheckCaseEnum.PAY_SUCCESS_MESSAGE_CASHBACK_AMOUNT.getName() + "----（✅）");
                } else {
                    resultList.add(CheckCaseEnum.PAY_SUCCESS_MESSAGE_CASHBACK_AMOUNT.getName() + "----（❌）");
                }
            }
            if (successLog.containsKey("paymentPromotionPayAmount")) {
                if (StringUtil.isEmpty(successLog.getString("promotionSanpshotId"))) {
                    if (successLog.getJSONObject("paymentPromotionPayAmount").getInteger("cent").equals(paymentDbDTO.getPaymentPromotionAmount())) {
                        resultList.add(CheckCaseEnum.PAY_SUCCESS_MESSAGE_PAYMENT_PROMOTION_AMOUNT.getName() + "----（✅）");
                    } else {
                        resultList.add(CheckCaseEnum.PAY_SUCCESS_MESSAGE_PAYMENT_PROMOTION_AMOUNT.getName() + "----（❌）");
                    }
                } else {
                    if (successLog.getJSONObject("paymentPromotionPayAmount").getInteger("cent") == (paymentDbDTO.getPaymentPromotionAmount() - paymentDbDTO.getPointsAmount())) {
                        resultList.add(CheckCaseEnum.PAY_SUCCESS_MESSAGE_PAYMENT_PROMOTION_AMOUNT.getName() + "----（✅）");
                    } else {
                        resultList.add(CheckCaseEnum.PAY_SUCCESS_MESSAGE_PAYMENT_PROMOTION_AMOUNT.getName() + "----（❌）");
                    }
                }
            }
            paymentResultDTO.setRequestLog(successLog);
            paymentResultDTO.setResultData(resultList);
            paymentResultDTO.setPaymentMethod(paymentDbDTO.getPaymentMethod());
            paymentResultDTO.setCheckoutOrderNo(paymentDbDTO.getCheckoutOrderNo());
        } else {
            paymentResultDTO.setPaymentMethod(paymentDbDTO.getPaymentMethod());
            paymentResultDTO.setRequestLog((null));
            paymentResultDTO.setCheckoutOrderNo(paymentDbDTO.getCheckoutOrderNo());
            paymentResultDTO.setResultData(Arrays.asList("success消息有问题----（❌）"));
        }
        return paymentResultDTO;

    }


    /**
     * auth消息校验结果
     *
     * @param paymentDbDTO
     * @param orderId
     * @return
     */
    private PaymentResultDTO authResult(PaymentDbDTO paymentDbDTO, String orderId) {
        //auth消息
        List<Object> authList = slsSceneData(PayCheckSceneEnum.PAY_SUCCESS_AUTH_MESSAGE.name(), orderId);
        PaymentResultDTO paymentResultDTO = new PaymentResultDTO();
        if (authList.size() != 0 && (paymentDbDTO.getStatus().equals("PAID") || paymentDbDTO.getStatus().equals("AUTHORIZED"))) {
            logger.info("开始-------------支付成功日志：" + authList.get(0).toString());
            paymentResultDTO.setPaymentMethod(paymentDbDTO.getPaymentMethod());
            paymentResultDTO.setRequestLog((JSONObject) authList.get(0));
            paymentResultDTO.setCheckoutOrderNo(paymentDbDTO.getCheckoutOrderNo());
            paymentResultDTO.setResultData(Arrays.asList("auth消息已接收----（✅）"));

        } else {
            paymentResultDTO.setPaymentMethod(paymentDbDTO.getPaymentMethod());
            paymentResultDTO.setRequestLog((null));
            paymentResultDTO.setCheckoutOrderNo(paymentDbDTO.getCheckoutOrderNo());
            paymentResultDTO.setResultData(Arrays.asList("auth消息有问题----（❌）"));
        }
        return paymentResultDTO;
    }


    /***
     * 检测不同渠道不同场景数据
     * @param params String
     * @return ResultDTO
     * @throws Exception
     */
    @Override
    public ResultDTO checkPaymentMethodData(String params, SystemDTO systemDTO) throws Exception {
        logger.info("开始-------------checkPaymentMethodData");
        ResultDTO result = new ResultDTO();
        Map<String, QueryResultUnit> data = new LinkedHashMap<>();
        LandlordContext.setTenantSpec("AE_GLOBAL", -1);

        JSONObject jsonObject = JSONObject.parseObject(params);
        String orderId = jsonObject.getString("orderIdStr");
        String checkScenario = jsonObject.getString("checkScenario");
        try {
            PaymentDbDTO paymentDbDTO = getDBData(params);
            switch (checkScenario) {
                case "CreateAndPay (ipay)":
                    //提交支付
                    PaymentResultDTO paymentResultDTO = checkCreateAndPay(paymentDbDTO);
                    paymentResultDTO.setStatus(paymentDbDTO.getStatus());
                    Map<String, QueryResultUnit> payRecord = QueryResultBuilder.buildQueryResult("提交支付请求", null, null, paymentResultDTO);
                    data.putAll(payRecord);
                    break;
                case "AUTH消息":
                    //支付auth消息
                    PaymentResultDTO authResultDTO = authResult(paymentDbDTO, orderId);
                    authResultDTO.setStatus(paymentDbDTO.getStatus());
                    Map<String, QueryResultUnit> authRecord = QueryResultBuilder.buildQueryResult("支付auth消息", null, null, authResultDTO);
                    data.putAll(authRecord);
                    break;
                case "Success消息":
                    //支付capture消息
                    PaymentResultDTO successResultDTO = successMessageResult(paymentDbDTO, orderId);
                    successResultDTO.setStatus(paymentDbDTO.getStatus());
                    Map<String, QueryResultUnit> successRecord = QueryResultBuilder.buildQueryResult("支付capture消息", null, null, successResultDTO);
                    data.putAll(successRecord);
                    break;
                case "REFUND (ipay)":
                    PaymentResultDTO refundResultDTO = refundResult(paymentDbDTO, orderId);
                    refundResultDTO.setStatus(paymentDbDTO.getStatus());
                    Map<String, QueryResultUnit> refundRecord = QueryResultBuilder.buildQueryResult("refund请求", null, null, refundResultDTO);
                    data.putAll(refundRecord);
                    break;
                case "PromotionReverse消息":
                    PaymentResultDTO reverseResultDTO = reverseMessageResult(paymentDbDTO, orderId);
                    reverseResultDTO.setStatus(paymentDbDTO.getStatus());
                    Map<String, QueryResultUnit> reverseRecord = QueryResultBuilder.buildQueryResult("promotionReverse消息", null, null, reverseResultDTO);
                    data.putAll(reverseRecord);
                    break;

                default:
                    logger.error("不支持的场景");

            }
            result.setData(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
            result.setSuccess(true);

        } catch (Throwable e) {
            result.setSuccess(false);
            result.setData("check inner exception");
        }
        return result;
    }

    @Override
    public ResultDTO getCurrency(String params, SystemDTO systemDTO) throws Exception {

        ResultDTO resultDTO = new ResultDTO();
        LandlordContext.setTenantSpec("AE_GLOBAL", -1);
        Map<String, QueryResultUnit> data = new LinkedHashMap<>();

        JSONObject jsonObject = JSONObject.parseObject(params);
        String currencyType = jsonObject.getString("currencyType");
        try {
            CurrencyType currencyType1 = exchangeRateServiceHsf.getCurrency(currencyType).getModule();
            data = QueryResultBuilder.buildQueryResult("获取币种详细信息", null, null, currencyType1);
            resultDTO.setData(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
            resultDTO.setSuccess(true);
        } catch (Exception e) {
            resultDTO.setSuccess(false);
            resultDTO.setData("no currencytype result");
        }

        return resultDTO;
    }

    @Override
    public ResultDTO getExchangeRate(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        LandlordContext.setTenantSpec("AE_GLOBAL", -1);
        Map<String, QueryResultUnit> data = new LinkedHashMap<>();

        JSONObject jsonObject = JSONObject.parseObject(params);
        String baseCur = jsonObject.getString("baseCur");
        String quoteCur = jsonObject.getString("quoteCur");
        try {
            GlobalRateInfo getRateInfo = exchangeRateServiceHsf.getExchangeRate(baseCur, quoteCur).getModule();
            data = QueryResultBuilder.buildQueryResult("获取实时汇率", null, null, getRateInfo);
            resultDTO.setData(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
            resultDTO.setSuccess(true);
        } catch (Exception e) {
            resultDTO.setSuccess(false);
            resultDTO.setData("no exchangerate result");
        }
        return resultDTO;
    }


    @Override
    public ResultDTO getExchangePrice(String params, SystemDTO systemDTO) throws Exception {

        ResultDTO resultDTO = new ResultDTO();
        Map<String, QueryResultUnit> data = new LinkedHashMap<>();
        LandlordContext.setTenantSpec("AE_GLOBAL", -1);

        JSONObject jsonObject = JSONObject.parseObject(params);
        String baseAmount = jsonObject.getString("baseAmount");
        //原始币种
        String curr = jsonObject.getString("curr");
        String language = jsonObject.getString("language");
        //目标币种
        String quoteCur = jsonObject.getString("quoteCur");

        if (baseAmount.isEmpty() || curr.isEmpty() || language.isEmpty()) {
            resultDTO.setSuccess(false);
            resultDTO.setData(JSONObject.toJSONString("pls make sure the input is not null", SerializerFeature.WriteDateUseDateFormat));
        }
        Long baseAmount1 = Long.parseLong(baseAmount);
        Money money = Money.of(BigDecimal.valueOf(baseAmount1), curr);
        try {
            if (language.isEmpty()) {
                GlobalPriceDTO globalPriceDTO = exchangeRateServiceHsf.getExchangePrice(money, quoteCur).getModule();
                data = QueryResultBuilder.buildQueryResult("getExchangePrice", null, null, globalPriceDTO);
                resultDTO.setData(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
                resultDTO.setSuccess(true);
                return resultDTO;

            }
            Language language1 = Language.valueOf(language);
            GlobalPriceDTO globalPriceDTO = exchangeRateServiceHsf.getExchangePrice(money, quoteCur, language1).getModule();
            data = QueryResultBuilder.buildQueryResult("getExchangePrice", null, null, globalPriceDTO);
            resultDTO.setData(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
            resultDTO.setSuccess(true);
        } catch (Exception e) {
            resultDTO.setSuccess(false);
            resultDTO.setData(JSONObject.toJSONString("no exchangeprice result", SerializerFeature.WriteDateUseDateFormat));
        }
        return resultDTO;
    }

    @Override
    public ResultDTO refundBeforePayToIPay(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        Map<String, QueryResultUnit> data = new LinkedHashMap<>();
        LandlordContext.setTenantSpec("AE_GLOBAL", -1);

        JSONObject jsonObject = JSONObject.parseObject(params);
        String tradeOrderId = jsonObject.getString("tradeOrderId");
        String refundChannel = jsonObject.getString("refundChannel");
        try {
            Long buyerId = null;
            Response<TradeOrderDTO> tradeOrderDTOResponse = orderQueryForBuyerFacade.queryTradeOrderById(Long.parseLong(tradeOrderId));
            if (tradeOrderDTOResponse.isSuccess()) {
                buyerId = tradeOrderDTOResponse.getModule().getBuyer().getBuyerId();
            } else {
                logger.error("获取订单买家信息异常：" + tradeOrderDTOResponse.getModule());
            }
            //先资金归集
            RequestCtxUtil.setTargetCluster("vpc-sg-pre");
            JSONObject fundRes = HsfUtil.fundPreByOrderId(tradeOrderId);
            logger.info("资金归集:" + fundRes);
            resultDTO.setSuccess(true);
            if (fundRes == null) {
                resultDTO.setData("资金归集失败。 Res: " + JSON.toJSONString(fundRes));
                return resultDTO;
            }
            //查询资金预处理是否成功
            JSONObject pretreatmentDetail = HsfUtil.queryFundPretreatmentDetailByTargetAndUserId(tradeOrderId, buyerId);
            logger.info("查询资金预处理:" + pretreatmentDetail);
            if (pretreatmentDetail == null) {
                resultDTO.setData("查询资金预处理是否成功。 Res: " + JSON.toJSONString(fundRes));
                return resultDTO;
            }
            //取消订单
            JSONObject json = new JSONObject();
            json.put("orderIdStr", tradeOrderId);
            json.put("cancelEvent", "");
            json.put("buyerId", buyerId);
            json.put("refundChannel", refundChannel);
            ResultDTO res = orderService.cancelOrder(buyerId, refundChannel, Long.parseLong(tradeOrderId));
            return res;
        } catch (Exception e) {
            resultDTO.setSuccess(false);
            resultDTO.setData(e.getLocalizedMessage());
            e.printStackTrace();
        }
        return resultDTO;
    }


    @Override
    public ResultDTO refundBeforePayToIPay(Long tradeOrderId, String refundChannel) {
        LandlordContext.setTenantSpec("AE_GLOBAL", -1);
        try {
            Long buyerId = null;
            Response<TradeOrderDTO> tradeOrderDTOResponse = orderQueryForBuyerFacade.queryTradeOrderById(tradeOrderId);
            if (tradeOrderDTOResponse.isSuccess()) {
                buyerId = tradeOrderDTOResponse.getModule().getBuyer().getBuyerId();
            } else {
                logger.error("获取订单买家信息异常：" + tradeOrderDTOResponse.getModule());
            }
            //先资金归集
            JSONObject fundRes = HsfUtil.fundPreByOrderId(tradeOrderId.toString());
            logger.info("资金归集:" + fundRes);
            //查询资金预处理是否成功
            JSONObject pretreatmentDetail = HsfUtil.queryFundPretreatmentDetailByTargetAndUserId(tradeOrderId.toString(), buyerId);
            logger.info("查询资金预处理:" + pretreatmentDetail);

            //取消订单
            ResultDTO resultPlainResult = orderService.cancelOrder(buyerId, refundChannel, tradeOrderId);
            return resultPlainResult;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public ResultDTO generateMessage(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        Map<String, QueryResultUnit> data = new LinkedHashMap<>();
        LandlordContext.setTenantSpec("AE_GLOBAL", -1);

        JSONObject jsonObject = JSONObject.parseObject(params);
        String refundChannel = jsonObject.getString("refundChannel");
        String messageType = jsonObject.getString("messageType");
        String messageState = jsonObject.getString("messageState");  // pending, settle, refundComplete
        String bizOrderNo = jsonObject.getString("orderIdStr");
        String refundInstructionNo = jsonObject.getString("refundInstructionNo");
        String envType = jsonObject.getString("envType");
        String ip = jsonObject.getString("ip");
        String dpath = jsonObject.getString("dpath");

        Long payerId = orderService.getPayerIdByOrderId(bizOrderNo);
        if (payerId == null) {
            resultDTO.setData("查询用户id异常，请检查输入订单号是否正确");
            resultDTO.setSuccess(true);
            return resultDTO;
        }
        Boolean userFlag = dataPoolService.checkTestAccount(payerId);
        if (!userFlag) {
            resultDTO.setData("仅支持测试账号mock");
            resultDTO.setSuccess(true);
            return resultDTO;
        }

        MessageDTO messageDTO = new MessageDTO();
        resultDTO.setSuccess(true);

        if (messageType.equals(Constant.REFUND_PROCESS)) {
            messageDTO.setExchangeCode(Constant.REFUND_PROCESS_EXCHANGECODE);
            messageDTO.setMid("ae");
            messageDTO.setSource("gps");
            messageDTO.setServiceProvider("ipayagh");
            RefundInfo refundInfo = new RefundInfo();
            //查询退款信息
            JSONObject refundRes = HsfUtil.queryRefundInstruction(bizOrderNo, payerId);
            JSONArray records = refundRes.getJSONArray("records");
            if (records.isEmpty()) {
                resultDTO.setData("无退款记录");
                return resultDTO;
            }
            com.alibaba.global.payment.api.vo.Money refundMoney = new com.alibaba.global.payment.api.vo.Money();
            com.alibaba.global.payment.api.vo.Money refundAmount = new com.alibaba.global.payment.api.vo.Money();
            String paymentMethod = null;
            for (int i = 0; i < records.size(); i++) {
                if (refundInstructionNo.equals(records.getJSONObject(i).getString("instructionNo"))) {

                    refundMoney.setValue(records.getJSONObject(i).getString("orderAmount"));
                    refundMoney.setCurrency(records.getJSONObject(i).getString("orderCurrency"));
                    refundAmount.setValue(records.getJSONObject(i).getString("refundAmount"));
                    refundAmount.setCurrency(records.getJSONObject(i).getString("refundCurrency"));
                    paymentMethod = records.getJSONObject(i).getString("instrumentCode");
                    break;
                }
            }
            refundInfo.setRefundAmount(refundMoney);
            refundInfo.setRefundRequestId(refundInstructionNo);
            refundInfo.setPaymentId(refundInstructionNo + UUID.randomUUID().toString());
            refundInfo.setPayToId(refundInstructionNo + UUID.randomUUID().toString());
            refundInfo.setRefundStage(messageState);

            if ("settle".equals(messageState)) {
                refundInfo.setRefundStatus("FINISH");
            } else if ("pending".equals(messageState)) {
                refundInfo.setRefundStageTime(ZonedDateTime.now().toString().replace("[Asia/Shanghai]", ""));
            } else {  // refundComplete
                refundInfo.setRefundStatus("SUCCESS");
                refundInfo.setRefundedTime(ZonedDateTime.now().toString().replace("[Asia/Shanghai]", ""));
                refundInfo.setRefundStageTime(ZonedDateTime.now().toString().replace("[Asia/Shanghai]", ""));
            }

            RefundStatements refundStatements = new RefundStatements();
            refundStatements.setRefundStatementStage(messageState);
            if ("BONUS".equals(refundChannel) && "settle".equals(messageState)) {
                refundStatements.setRefundStrategy("NON_ORIGINAL");
            }
            refundStatements.setRefundStatementId(refundInstructionNo + UUID.randomUUID().toString());

            RefundStatements.RefundAssetDetails refundAssetDetails = new RefundStatements.RefundAssetDetails();
            if ("init".equals(messageState) || "send".equals(messageState)) {
                refundAssetDetails.setFundType("EXTERNAL");
                refundAssetDetails.setOutAbility("EXTERNAL");
                refundAssetDetails.setPayProvider("WPG");
            }
            refundAssetDetails.setRefundAmount(refundAmount);
            refundAssetDetails.setRefundAssetStage(messageState);
            RefundStatements.RefundMethod refundMethod = new RefundStatements.RefundMethod();
            refundMethod.setPaymentMethodType(paymentMethod);
            refundAssetDetails.setRefundMethod(refundMethod);
            refundStatements.setRefundAssetDetails(Arrays.asList(refundAssetDetails));
            refundInfo.setRefundStatementsList(Arrays.asList(refundStatements));
            JSONObject res = new JSONObject();
            res.put("notifyType", messageType);
            res.put("notifyId", refundInstructionNo + UUID.randomUUID().toString());
            res.put("refundInfo", refundInfo);
            messageDTO.setContext(res.toJSONString());
        }

        String msg = JSON.toJSONString(messageDTO);
        String str = sendDingMsgService.sendMetaq("global_payment_event_topic", msg, "refundProcess", envType, ip, Long.toString(payerId), null, dpath, "");
        resultDTO.setData("发送消息结果：" + str + " " + "消息内容：" + msg);
        resultDTO.setSuccess(true);
        return resultDTO;
    }

    @Override
    public ResultDTO getRepayPaymentAndRefundInfo(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        Map<String, QueryResultUnit> data = new LinkedHashMap<>();
        LandlordContext.setTenantSpec("AE_GLOBAL", -1);

        JSONObject jsonObject = JSONObject.parseObject(params);
        String orderId = jsonObject.getString("orderIdStr");

        JSONArray list = getPaymentInstrumentNoData(params);
        String instructionNo = "";
        for (int i = 0; i < list.size(); i++) {
            if ("PAID".equals(list.getJSONObject(i).getString("status"))) {
                instructionNo = list.getJSONObject(i).getString("instructionNo");
            }
        }
        if (StringUtil.isBlank(instructionNo)) {
            resultDTO.setSuccess(true);
            data = QueryResultBuilder.buildQueryResult("订单未支付成功，未到还款", null, null, list);
            resultDTO.setData(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
            return resultDTO;
        }
        Long buyerId = null;
        Response<TradeOrderDTO> tradeOrderDTOResponse = orderQueryForBuyerFacade.queryTradeOrderById(Long.parseLong(orderId));
        if (tradeOrderDTOResponse.isSuccess()) {
            buyerId = tradeOrderDTOResponse.getModule().getBuyer().getBuyerId();
        } else {
            logger.error("获取订单买家信息异常：" + tradeOrderDTOResponse.getModule());
            resultDTO.setSuccess(true);
            data = QueryResultBuilder.buildQueryResult("获取订单买家信息", null, null, "异常：" + tradeOrderDTOResponse.getModule());
            resultDTO.setData(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
            return resultDTO;
        }
        JSONObject res = HsfUtil.queryRepaymentDetail(Long.toString(buyerId), instructionNo);
        JSONArray checkOutOrderList = new JSONArray();

        if (null != res && res.containsKey("result")) {
            if (res.getJSONObject("result").getJSONArray("repayCheckoutOrderIds") != null && res.getJSONObject("result").getJSONArray("repayCheckoutOrderIds").size() > 0) {
                checkOutOrderList.addAll(res.getJSONObject("result").getJSONArray("repayCheckoutOrderIds"));
            } else {
                logger.error("获取还款收单信息：" + res);
                resultDTO.setSuccess(true);
                data = QueryResultBuilder.buildQueryResult("获取还款收单", null, null, "未发起还款");
                resultDTO.setData(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
                return resultDTO;
            }
        } else if (res != null && (!res.getBoolean("success"))) {
            logger.error("获取还款收单信息：" + res);
            resultDTO.setSuccess(true);
            data = QueryResultBuilder.buildQueryResult("获取还款收单", null, null, "未创建还款计划");
            resultDTO.setData(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
            return resultDTO;
        } else {
            logger.error("获取还款收单信息：" + res);
            resultDTO.setSuccess(true);
            data = QueryResultBuilder.buildQueryResult("获取还款收单", null, null, "异常：" + res);
            resultDTO.setData(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
            return resultDTO;
        }

        for (int i = 0; i < checkOutOrderList.size(); i++) {
            JSONObject result = new JSONObject();
            if (StringUtil.isBlank(checkOutOrderList.getString(i))) {
                continue;
            }
            JSONObject repayInfo = HsfUtil.queryPayInstruction(checkOutOrderList.getString(i));
            JSONObject pay = new JSONObject();
            if (!repayInfo.isEmpty() && repayInfo.getJSONArray("records").size() > 0) {
                pay.put("PayInstruction", repayInfo.getJSONArray("records"));
            }
            JSONObject repayDistInfo = HsfUtil.queryPayInstructionDist(checkOutOrderList.getString(i));
            if (!repayDistInfo.isEmpty() && repayDistInfo.getJSONArray("records").size() > 0) {
                pay.put("PayInstructionDist", repayDistInfo.getJSONArray("records"));
            }
            result.put("还款支付信息", pay);

            CheckoutQueryRequest checkoutQueryRequest = new CheckoutQueryRequest();
            checkoutQueryRequest.setCheckoutOrderId(checkOutOrderList.getString(i));
            checkoutQueryRequest.setPayerId(buyerId);
            checkoutQueryRequest.setRouteId(buyerId);
            CheckoutQueryResponse checkoutQueryResponse = paymentQueryFacade.query(checkoutQueryRequest);
            if (checkoutQueryResponse.isSucceeded()) {
                String repayOrderId = checkoutQueryResponse.getMerchantCheckoutOrderId();
                result.put("还款订单号", repayOrderId);
                result.put("还款收单信息", checkoutQueryResponse);
                result.put("还款收单状态", checkoutQueryResponse.getStatus());
                JSONObject refund = new JSONObject();
                JSONObject repayRefundDistInfo = HsfUtil.queryRefundInstructionDist(repayOrderId, buyerId);
                if (!repayRefundDistInfo.isEmpty() && repayRefundDistInfo.getJSONArray("records").size() > 0) {
                    refund.put("RefundInstructionDist", repayRefundDistInfo.getJSONArray("records"));
                }
                JSONObject repayRefundInfo = HsfUtil.queryRefundInstruction(repayOrderId, buyerId);
                if (!repayRefundInfo.isEmpty() && repayRefundInfo.getJSONArray("records").size() > 0) {
                    refund.put("RefundInstruction", repayRefundInfo.getJSONArray("records"));
                }
                result.put("还款退款信息", refund);
            }
            Map<String, QueryResultUnit> order = QueryResultBuilder.buildQueryResult("还款订单" + i, null, null, result);
            data.putAll(order);
        }
        resultDTO.setData(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
        resultDTO.setSuccess(true);
        return resultDTO;
    }

    /**
     * 通过订单号获取收单号
     *
     * @param orderId
     * @return
     */
    @Override
    public UniqueRes getCheckOutOrderNoByOrderId(String orderId) {
        UniqueRes uniqueRes = new UniqueRes();
        JSONObject res = null;
        try {
            res = HsfUtil.queryBatchCheckoutOrder(orderId);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (null != res && res.containsKey("records")) {
            JSONArray records = res.getJSONArray("records");
            if (!records.isEmpty()) {
                uniqueRes.setUniqueValue(records.getJSONObject(0).getString("checkoutOrderNo"));
                uniqueRes.setPayerId(records.getJSONObject(0).getLong("payerId"));
                return uniqueRes;
            }
        }
        return null;
    }

    /**
     * 通过收单号获取支付收单
     *
     * @param checkoutOrder
     * @return
     */
    @Override
    public UniqueRes getPayInstructionNoByCheckoutOrderNo(String checkoutOrder) {
        UniqueRes uniqueRes = new UniqueRes();
        JSONObject repayRes = null;
        try {
            repayRes = HsfUtil.queryPayInstruction(checkoutOrder);
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (null != repayRes && repayRes.containsKey("records")) {
            JSONArray records = repayRes.getJSONArray("records");
            if (!records.isEmpty()) {
                for (int i = 0; i < records.size(); i++) {
                    String status = records.getJSONObject(i).getString("status");
                    if ("PAID".equals(status) || (!records.contains("PAID") && "AUTHORIZED".equals(status))) {
                        uniqueRes.setUniqueValue(records.getJSONObject(i).getString("instructionNo"));
                        uniqueRes.setPayerId(records.getJSONObject(i).getLong("payerId"));
                        return uniqueRes;
                    }
                }
            }
        }
        return null;
    }

    /**
     * 订单号获取支付成功支付单号
     *
     * @param orderId
     * @return
     */
    @Override
    public UniqueRes getPayInstructionNoByOrderId(String orderId) {
        UniqueRes checkoutOrder = getCheckOutOrderNoByOrderId(orderId);
        if (checkoutOrder != null && StringUtil.isNotBlank(checkoutOrder.getUniqueValue())) {
            return getPayInstructionNoByCheckoutOrderNo(checkoutOrder.getUniqueValue());
        }
        return null;
    }

    /**
     * 订单号获取支付成功支付单号
     *
     * @param orderId
     * @return
     */
    @Override
    public JSONObject getPayInstructionDistByOrderId(String orderId) {
        UniqueRes checkOutOrderNo = getCheckOutOrderNoByOrderId(orderId);
        try {
            JSONObject res = HsfUtil.queryPayInstructionDist(checkOutOrderNo.getUniqueValue());
            return res;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 收单号获取支付指令单
     *
     * @param checkoutOrderNo
     * @return
     */
    @Override
    public JSONObject getPayInstructionByCheckoutOrderNo(String checkoutOrderNo) {
        try {
            JSONObject res = HsfUtil.queryPayInstruction(checkoutOrderNo);
            return res;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    private PayInstructionMoney getPaymentMoney(JSONArray payInstructionDistList, String payToRequestId) {
        PayInstructionMoney payInstructionMoney = new PayInstructionMoney();
        long orderCommissionValue = 0;
        long paymentCommissionValue = 0;
        long promotionValue = 0;
        long promotionOrderValue = 0;
        for (int i = 0; i < payInstructionDistList.size(); i++) {
            if (("GOODS".equals(payInstructionDistList.getJSONObject(i).getString("fundType")) ||
                    "COMMISSION".equals(payInstructionDistList.getJSONObject(i).getString("fundType")))) {
                if (payInstructionDistList.getJSONObject(i).getString("instructionNo").equals(payToRequestId)) {
                    orderCommissionValue = orderCommissionValue + Long.parseLong(payInstructionDistList.getJSONObject(i).getString("orderAmount"));
                    payInstructionMoney.setOrderCurrency(payInstructionDistList.getJSONObject(i).getString("orderCurrency"));
                    paymentCommissionValue = paymentCommissionValue + Long.parseLong(payInstructionDistList.getJSONObject(i).getString("payAmount"));
                    payInstructionMoney.setPaymentCurrency(payInstructionDistList.getJSONObject(i).getString("payCurrency"));
                    payInstructionMoney.setInstrumentCode(payInstructionDistList.getJSONObject(i).getString("instrumentCode"));
                    continue;
                }
            }

            if (("PROMOTION".equals(payInstructionDistList.getJSONObject(i).getString("fundType")) ||
                    "ORDER_PROMOTION".equals(payInstructionDistList.getJSONObject(i).getString("fundType")))) {
                if (payInstructionDistList.getJSONObject(i).getString("instructionNo").equals(payToRequestId)) {
                    promotionValue = promotionValue + Long.parseLong(payInstructionDistList.getJSONObject(i).getString("payAmount"));
                    promotionOrderValue = promotionOrderValue + Long.parseLong(payInstructionDistList.getJSONObject(i).getString("orderAmount"));
                }
            }
        }
        payInstructionMoney.setGoodCommissionOrderValue(orderCommissionValue);
        payInstructionMoney.setGoodCommissionPaymentValue(paymentCommissionValue);
        payInstructionMoney.setPromotionOrderOrderValue(promotionOrderValue);
        payInstructionMoney.setPromotionOrderPaymentValue(promotionValue);
        payInstructionMoney.setCashPaymentValue(paymentCommissionValue - promotionValue);
        payInstructionMoney.setCashOrderValue(orderCommissionValue - promotionOrderValue);
        return payInstructionMoney;
    }


    //mock支付成功
    @Override
    public ResultDTO mockIPaySuccess(String params, SystemDTO systemDTO) throws Exception {
        String empId = systemDTO.getOperator();
        ResultDTO resultDTO = new ResultDTO();

        JSONObject jsonObject = JSONObject.parseObject(params);
        String orderId = jsonObject.getString("orderIdStr");
        String messageType = jsonObject.getString("messageType");
        String envType = jsonObject.getString("envType");
        String ip = jsonObject.getString("ip");
        Integer index = jsonObject.getInteger("index");
        String dpath = jsonObject.getString("dpath");
        String instructionNo = jsonObject.getString("instructionNo");
        String secId = jsonObject.getString("secId");

        // 是否开启验款
        String enableAmountCheck = jsonObject.getString("enableAmountCheck");
        boolean isAmountCheck = enableAmountCheck != null && !enableAmountCheck.equals("否");
        // 支付指令单idx
        index = 0;

        PGNotifyBody pgNotifyBody = new PGNotifyBody();
        pgNotifyBody.setMid("ae");
        pgNotifyBody.setServiceProvider("ipayagh");
        pgNotifyBody.setSource("gps");
        Long payerId = orderService.getPayerIdByOrderId(orderId);
        if (payerId == null) {
            resultDTO.setData("根据订单查询买家id异常，请检查输入订单号是否正确");
            resultDTO.setSuccess(true);
            return resultDTO;
        }
        Boolean userFlag = dataPoolService.checkTestAccount(payerId);
        if (!userFlag) {
            resultDTO.setData("查询买家账号信息失败，或买家账号不是测试账号无法mock");
            resultDTO.setSuccess(true);
            return resultDTO;
        }
        pgNotifyBody.setUserId(payerId);

        JSONObject payInstructionDist = getPayInstructionDistByOrderId(orderId);
        if (payInstructionDist == null || payInstructionDist.getJSONArray("records").isEmpty()) {
            resultDTO.setData("支付查询失败或当前无支付单，需重新提交支付。");
            resultDTO.setSuccess(true);
            return resultDTO;
        }

        JSONArray list = payInstructionDist.getJSONArray("records");
        if (StringUtil.isBlank(instructionNo)) {
            instructionNo = list.getJSONObject(index).getString("instructionNo");
        }
        String checkoutOrderNo = list.getJSONObject(index).getString("checkoutOrderNo");
        JSONObject payInstructionRes = getPayInstructionByCheckoutOrderNo(checkoutOrderNo);
        JSONObject payInstruction;
        String payStatusInfo = "";
        if (!payInstructionRes.isEmpty()) {
            payInstruction = payInstructionRes.getJSONArray("records").getJSONObject(index);
            String status = payInstruction.getString("status");
            String instrumentCode = payInstruction.getString("instrumentCode");
            String payPlanNo = payInstruction.getString("payPlanNo");

            // bonus 0 元付需要推状态的单子不校验权限
            if (!"MERCHANT_COUPON".equals(instrumentCode)) {
                // 权限写死，避免被通过配置编辑
                List<String> paymentBuddies = Arrays.asList("360180", "118774", "333542", "453151", "WB01965067", "307834", "WB01959406", "WB02013498", "332322", "395187", "231172", "234062", "363105", "273148", "WB01162317", "378020", "408067", "WB02005700", "WB545149");
                if (!paymentBuddies.contains(empId)) {
                    resultDTO.setData("抱歉，您（" + empId + "）不在成员白名单（仅供支付内部调试使用）无法使用该工具（不对外开放申请）。如有支付需求，请使用 Bonus 充值抵扣支付（见上方链接文档），如 Bonus 支付卡单可使用本工具推送消息。如有疑问请联系@得月。");
                    resultDTO.setSuccess(true);
                    return resultDTO;
                }
            }

            payStatusInfo = "推送消息前最近一笔支付单状态：" + status + "，支付方式：" + instrumentCode + "，支付单号：" + payPlanNo + "。";
            switch (status) {
                case "INIT":
                    payStatusInfo += "提交支付未完成，请重新提交。";
                    break;
                // todo 有个支付单cancel标=Y的情况，在支付指令里拿不到，也无法推进
                case "AUTHORIZING":
                    payStatusInfo += "提交支付成功，需要推送支付成功消息。";
                    break;
                case "AUTHORIZED":
                    payStatusInfo += "支付成功消息消费成功，需要推送风控通过消息。";
                    break;
                case "PAID":
                    payStatusInfo += "风控通过消息消费成功，mock支付完成。";
                    break;
                case "FAILED":
                    payStatusInfo += "支付单已失败，请确保提交支付走到mock逻辑，并重新提交。";
                    break;
                case "CANCELED":
                    payStatusInfo += "订单关闭，请重新下单。";
                    break;
            }
        } else {
            payStatusInfo = "支付指令查询失败。";
        }

        //如果走验证款，需要查一下当前支付单汇率
        BigDecimal fxRate = null;
        if (isAmountCheck) {
            try {
                payInstruction = HsfUtil.queryPayInstruction(list.getJSONObject(0).getString("checkoutOrderNo"));
                logger.info("【mockIPaySuccess_queryPayInstruction】return object：" + payInstruction);
                if (!payInstruction.isEmpty() && payInstruction.getString("fxRate") != null) {
                    fxRate = new BigDecimal(payInstruction.getString("fxRate"));
                }

                //如果查支付单数据没拿到汇率，实时查询汇率兜底吧
                if (fxRate == null) {
                    logger.error("【mockIPaySuccess_queryPayInstruction】get fxRate is null");
                    GlobalRateInfo rateInfo = exchangeRateServiceHsf.getExchangeRate(list.getJSONObject(index).getString("orderCurrency"), list.getJSONObject(index).getString("payCurrency")).getModule();
                    logger.info("【mockIPaySuccess_getExchangeRate】return object：" + JSON.toJSONString(rateInfo));
                    if (rateInfo != null && rateInfo.getRate() != null) {
                        fxRate = rateInfo.getRate();
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        try {
            // 公共声明
            PaymentResult paymentResult;
            PaymentResult.Result result;
            List<PaymentResult.PaymentDetailSummary> paymentDetailSummaries;
            com.alibaba.global.payment.api.vo.Money payToAmount, payAmount, payMoney;
            PayInstructionMoney payInstructionMoney;
            PaymentResult.PaymentDetailSummary paymentDetailSummary;
            Float amountScale;
            Long cashPaymentAmount = 0L;
            Long payAmountScaled = 0L;
            Long payToMoney;
            Boolean isZeroPay;
            JSONObject da;
            String str;

            if (messageType.equals("线下支付码")) {
                payInstructionMoney = getPaymentMoney(list, instructionNo);
                String instCode = payInstructionMoney.getInstrumentCode();
                if (!Arrays.asList("OTC_PIX", "KONBINI", "BOLETO").contains(instCode)) {
                    resultDTO.setData("当前不支持该渠道发送Payment Code：" + instCode);
                    resultDTO.setSuccess(true);
                }

                pgNotifyBody.setExchangeCode("ipayagh.gn.notification.offline.paymentcode");
                HashMap<String, PaymentCode> resMap = new HashMap();
                PaymentCode codeInfo = new PaymentCode();

                PaymentCodeForm form = new PaymentCodeForm();
                form.setActionFormType("PaymentCodeForm");
                form.setPaymentCodeExpireTime("2025-07-24T04:47:29-07:00");
                List<PaymentCodeInfo> paymentCodeInfos = new LinkedList<>();

                // 设码
                PaymentCodeInfo info = new PaymentCodeInfo();
                info.setPaymentCodeInfoType("PAYMENT_CODE");
                PaymentCodeDetail digiCodeDetail, barCodeDetail, includeDetail;
                List<PaymentCodeDetail> paymentCodeDetails;
                digiCodeDetail = new PaymentCodeDetail();
                switch (instCode) {
                    case "OTC_PIX":
                        digiCodeDetail.setDisplayType("TEXT");
                        digiCodeDetail.setCodeValueType("DIGICODE");
                        digiCodeDetail.setCodeValue("66666666666666666666br.gov.bcb.pix2569api.developer.btgpactual.com/v1/p/v2/13ef992362044186a918eb3364ea0bcc5204000053039865802BR5908AlipayBR6009Sao Paulo61080400404062070503***6304F6D1");
                        barCodeDetail = new PaymentCodeDetail();
                        barCodeDetail.setDisplayType("IMAGE_CONVERT");
                        barCodeDetail.setCodeValueType("BARCODE");
                        barCodeDetail.setCodeValue(digiCodeDetail.getCodeValue());
                        paymentCodeDetails = Arrays.asList(digiCodeDetail, barCodeDetail);
                        info.setPaymentCodeDetails(paymentCodeDetails);
                        break;
                    case "KONBINI":
                        digiCodeDetail.setDisplayType("TEXT");
                        digiCodeDetail.setCodeValueType("DIGICODE");
                        digiCodeDetail.setCodeValue("666666");
                        paymentCodeDetails = Arrays.asList(digiCodeDetail);
                        info.setPaymentCodeDetails(paymentCodeDetails);
                        break;
                    case "BOLETO":
                        digiCodeDetail.setDisplayType("TEXT");
                        digiCodeDetail.setCodeValueType("DIGICODE");
                        digiCodeDetail.setCodeValue("66666666666666666666666666666666666666666666666");
                        barCodeDetail = new PaymentCodeDetail();
                        barCodeDetail.setDisplayType("IMAGE_CONVERT");
                        barCodeDetail.setCodeValueType("BARCODE");
                        barCodeDetail.setCodeValue(digiCodeDetail.getCodeValue());
                        includeDetail = new PaymentCodeDetail();
                        includeDetail.setDisplayType("INCLUDE");
                        includeDetail.setCodeValueType("PAGE");
                        includeDetail.setCodeValue("https://print.ebanx.com/print/?hash=66a21cbbe94a460cad98e5f91ede9107be060cffa11a1d92");
                        paymentCodeDetails = Arrays.asList(digiCodeDetail, barCodeDetail, includeDetail);
                        info.setPaymentCodeDetails(paymentCodeDetails);
                        break;
                }
                paymentCodeInfos.add(info);
                form.setPaymentCodeInfos(paymentCodeInfos);

                // 拿钱
                cashPaymentAmount = payInstructionMoney.getGoodCommissionPaymentValue() - payInstructionMoney.getPromotionOrderPaymentValue();
                com.alibaba.payment.gateway.channels.ipay.vo.Money paymentAmount =
                        new com.alibaba.payment.gateway.channels.ipay.vo.Money(cashPaymentAmount,payInstructionMoney.getPaymentCurrency());
                form.setPaymentAmount(paymentAmount);

                codeInfo.setPaymentCodeForm(form);
                codeInfo.setPaymentAmount(new com.alibaba.payment.gateway.channels.ipay.vo.Money(cashPaymentAmount,payInstructionMoney.getPaymentCurrency()));
                codeInfo.setPayToRequestId(instructionNo);
                codeInfo.setNotifyType("OFFLINE_PAYMENT_CODE");
                // 这俩和下面auth capture消息都配一样的
                codeInfo.setPaymentId("2386492874627346283732232");
                codeInfo.setPayToId("2386492874627346283732232");

                resMap.put("extendInfo", codeInfo);
                pgNotifyBody.setContext(JSONObject.toJSONString(resMap));
                logger.info("pgNotifyBody:" + JSONObject.toJSONString(pgNotifyBody));
                str = sendDingMsgService.sendMetaq("pg_rt1_pg_msg", JSONObject.toJSONString(pgNotifyBody), "PAY_OFFLINE_CODE", envType, ip, Long.toString(payerId), null, dpath, "");
                resultDTO.setData(payStatusInfo + " 发送消息结果：" + str);
                resultDTO.setSuccess(true);

            }
            else if (Arrays.asList("支付成功", "支付成功（风控审核）", "支付失败（风控拒绝）").contains(messageType)){
                pgNotifyBody.setExchangeCode("ipayagh.gn.notification.pay.result");
                paymentResult = new PaymentResult();
                result = new PaymentResult.Result();
                if (!messageType.equals("支付失败（风控拒绝）")) {
                    result.setResultCode("SUCCESS");
                    result.setResultMessage("success.");
                    result.setResultStatus("S");
                } else {
                    result.setResultCode("RISK_FRAUD_RISK");
                    result.setResultMessage("RISK_FRAUD_RISK");
                    result.setResultStatus("F");
                }
//
//                if (messageType.equals("支付成功（风控审核）")) {
//                    PaymentResult.PremiumRiskInfo info = new PaymentResult.PremiumRiskInfo();
//                    info.setNeedManualReview("true");
//                    info.setIsecurityBizId(secId);
//                    paymentResult.setPremiumRiskInfo(info);
//                }

                paymentResult.setPaymentId("2386492874627346283732232");
                paymentResult.setPaymentTime("2023-05-15T08:32:54-07:00");
                paymentResult.setSuggestedSettleTime("2023-05-19T08:32:54-07:00");
                paymentResult.setPayToId("2386492874627346283732232");
                paymentResult.setPayToRequestId(instructionNo);
                paymentResult.setResult(result);
                paymentResult.setExtendInfo("{}");
                paymentDetailSummaries = new ArrayList<>();
                payToAmount = new com.alibaba.global.payment.api.vo.Money();
                payAmount = new com.alibaba.global.payment.api.vo.Money();
                payInstructionMoney = getPaymentMoney(list, paymentResult.getPayToRequestId());

                // 1、PaymentDetailSummary金额组装： 包括渠道支付cash金额+coupon金额
                paymentDetailSummary = new PaymentResult.PaymentDetailSummary();
                paymentDetailSummary.setExtendInfo("{}");

                paymentDetailSummary.setPaymentMethodType(payInstructionMoney.getInstrumentCode());
                payMoney = new com.alibaba.global.payment.api.vo.Money();
                // 算实付金额
                isZeroPay = "MERCHANT_COUPON".equals(payInstructionMoney.getInstrumentCode());
                if (!isZeroPay) {
                    ///验款则重设实际支付的cash金额，改为*1.3，改小为0.7
                    amountScale = isAmountCheck ? ("改大".equals(enableAmountCheck) ? 1.3f : 0.7f) : 1.0f;
                    cashPaymentAmount = Float.valueOf((payInstructionMoney.getGoodCommissionPaymentValue() - payInstructionMoney.getPromotionOrderPaymentValue()) * amountScale).longValue();
                    payMoney.setValue(cashPaymentAmount.toString());
                } else {
                    // 0元付
                    payMoney.setValue("0");
                }
                payMoney.setCurrency(payInstructionMoney.getPaymentCurrency());
                paymentDetailSummary.setPaymentAmount(payMoney);
                paymentDetailSummaries.add(paymentDetailSummary);
                paymentResult.setPaymentDetailSummaries(paymentDetailSummaries);

                // 2、支付金额（cash支付金额+优惠金额）组装
                if (!isZeroPay) {
                    payAmountScaled = cashPaymentAmount + payInstructionMoney.getPromotionOrderPaymentValue();
                    payAmount.setValue(cashPaymentAmount.toString());
                } else {
                    payAmount.setValue("0");
                }
                payAmount.setCurrency(payInstructionMoney.getPaymentCurrency());
                paymentResult.setPaymentAmount(payAmount);

                // 3、报价金额
                payToAmount.setCurrency(payInstructionMoney.getOrderCurrency());
                if (payToAmount.getCurrency().equals(payMoney.getCurrency())) {
                    payToAmount.setValue(payMoney.getValue());
                } else if (!isZeroPay) {
                    //（手续费 + good金额-优惠金额）组装，若是验款&汇率不为null场景 报价金额=paymentAmount/汇率，否则不做特殊处理了
                    payToMoney = isAmountCheck && fxRate != null ? (new BigDecimal(payAmountScaled).divide(fxRate, 2, BigDecimal.ROUND_HALF_EVEN)).longValue() : payInstructionMoney.getCashOrderValue();
                    payToAmount.setValue(payToMoney.toString());
                } else {
                    payToAmount.setValue("0");
                }
                paymentResult.setPayToAmount(payToAmount);

                da = new JSONObject();
                da.put("extendInfo", JSONObject.toJSONString(paymentResult));
                pgNotifyBody.setContext(da.toJSONString());
                logger.info("pgNotifyBody:" + JSONObject.toJSONString(pgNotifyBody));
                str = sendDingMsgService.sendMetaq("pg_rt1_pg_msg", JSONObject.toJSONString(pgNotifyBody), "PAY", envType, ip, Long.toString(payerId), null, dpath, "");
                resultDTO.setData(payStatusInfo + "发送消息结果：" + str);
                resultDTO.setSuccess(true);

            } else if (messageType.equals("支付风控通过")) {
                pgNotifyBody.setExchangeCode("ipayagh.gn.notification.capture.result");
                CaptureResult captureResult = new CaptureResult();
                CaptureResult.Result result1 = new CaptureResult.Result();
                result1.setResultCode("SUCCESS");
                result1.setResultMessage("success.");
                result1.setResultStatus("S");
                captureResult.setCaptureTime("2023-05-15T08:32:54-07:00");
                captureResult.setResult(result1);
                captureResult.setSuggestedSettleTime("2023-05-15T08:32:54-07:00");
                captureResult.setPaymentId("239279837283372837289372");
                captureResult.setPayToId("236427893628361982370129837");
                captureResult.setCaptureRequestId(instructionNo);
                PayInstructionMoney money = getPaymentMoney(list, captureResult.getCaptureRequestId());
                payToAmount = new com.alibaba.global.payment.api.vo.Money();
                // capture的订单金额不能该小，消费时会做校验：S_PP_INST_NOTIFY_AMOUNT_NOT_EQUALS
                payToAmount.setValue(Long.toString(money.getGoodCommissionOrderValue()));
                payToAmount.setCurrency(money.getOrderCurrency());
                captureResult.setCaptureAmount(payToAmount);//报价
                JSONObject ca = new JSONObject();
                ca.put("extendInfo", JSONObject.toJSONString(captureResult));
                pgNotifyBody.setContext(ca.toJSONString());
                logger.info("pgNotifyBody:" + JSONObject.toJSONString(pgNotifyBody));
                String str2 = sendDingMsgService.sendMetaq("pg_rt1_pg_msg", JSONObject.toJSONString(pgNotifyBody), "CAPTURE", envType, ip, Long.toString(payerId), null, dpath, "");
                resultDTO.setData(payStatusInfo + "发送消息结果：" + str2);
                resultDTO.setSuccess(true);

            } else if (messageType.equals("交易风控通过")) {
                TradePaymentResult tradePaymentResult = new TradePaymentResult();
                tradePaymentResult.setBizOrderId(orderId);
                tradePaymentResult.setBatchPayNo(getBatchPayRealtionNoByBizOrderId(orderId).get(0));
                String instrumentNo = payInstructionDist.getJSONArray("records").getJSONObject(0).getString("instructionNo");
                tradePaymentResult.setBuyerId(payerId);
                long goodAmount = 0;
                long orderAmount = 0;
                long promotionPay = 0;
                long promotionOrder = 0;
                long orderPromotionPay = 0;
                long orderPromotionOrder = 0;
                String payCurrency = null;
                String orderCurrency = null;
                for (int i = 0; i < list.size(); i++) {
                    if ("GOODS".equals(list.getJSONObject(i).getString("fundType")) && instrumentNo.equals(list.getJSONObject(i).getString("instructionNo"))) {
                        tradePaymentResult.setCheckoutOrderId(payInstructionDist.getJSONArray("records").getJSONObject(i).getString("checkoutOrderNo"));
                        tradePaymentResult.setPayOptionCode(payInstructionDist.getJSONArray("records").getJSONObject(i).getString("instrumentCode"));
                        tradePaymentResult.setPayPlanNo(payInstructionDist.getJSONArray("records").getJSONObject(i).getString("payPlanNo"));
                        goodAmount = Long.parseLong(list.getJSONObject(i).getString("payAmount"));
                        payCurrency = list.getJSONObject(i).getString("payCurrency");
                        orderAmount = Long.parseLong(list.getJSONObject(i).getString("orderAmount"));
                        orderCurrency = list.getJSONObject(i).getString("orderCurrency");
                        tradePaymentResult.setCheckoutOrderAmount(new TradePaymentResult.Money(orderAmount, orderCurrency));
                    } else if ("PROMOTION".equals(list.getJSONObject(i).getString("fundType")) && instrumentNo.equals(list.getJSONObject(i).getString("instructionNo"))) {
                        promotionPay = Long.parseLong(list.getJSONObject(i).getString("payAmount"));
                        promotionOrder = Long.parseLong(list.getJSONObject(i).getString("orderAmount"));
                        tradePaymentResult.setPaymentPromotionPayAmount(new TradePaymentResult.Money(goodAmount - promotionPay, payCurrency));
                        tradePaymentResult.setPaymentPromotionPostAmount(new TradePaymentResult.Money(orderAmount - promotionOrder, orderCurrency));
                    } else if ("ORDER_PROMOTION".equals(list.getJSONObject(i).getString("fundType")) && instrumentNo.equals(list.getJSONObject(i).getString("instructionNo"))) {
                        orderPromotionPay = Long.parseLong(list.getJSONObject(i).getString("payAmount"));
                        orderPromotionOrder = Long.parseLong(list.getJSONObject(i).getString("orderAmount"));
                    }
                }
                tradePaymentResult.setActualPaidAmount(new TradePaymentResult.Money(goodAmount - orderPromotionPay, payCurrency));
                tradePaymentResult.setActualPaidPostAmount(new TradePaymentResult.Money(orderAmount - orderPromotionOrder, orderCurrency));
                tradePaymentResult.setActualPaidCash(new TradePaymentResult.Money(goodAmount - orderPromotionPay - promotionPay, payCurrency));
                tradePaymentResult.setActualPaidPostCash(new TradePaymentResult.Money(orderAmount - orderPromotionOrder - promotionOrder, orderCurrency));
                if ("交易支付成功".equals(messageType)) {
                    tradePaymentResult.setStatus("AUTHORIZED");
                } else if ("交易风控通过".equals(messageType)) {
                    tradePaymentResult.setStatus("SUCCEED");
                }
                logger.info("jjjjjj:" + JSONObject.toJSONString(tradePaymentResult));
                String str3 = sendDingMsgService.sendMetaq("global_payment_event_topic", JSONObject.toJSONString(tradePaymentResult), "PAY", envType, ip, Long.toString(payerId), null, dpath, "");
                resultDTO.setData("发送消息结果：" + str3);
                resultDTO.setSuccess(true);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        HsfUtil.measureAll("/payment/jobId="+systemDTO.getSite(),empId);
        return resultDTO;
    }

    @Override
    public ResultDTO authCode(String params, SystemDTO systemDTO) {
        logger.info("开始-------------checkPaymentMethodData");
        ResultDTO result = new ResultDTO();
        Map<String, QueryResultUnit> data = new LinkedHashMap<>();
        LandlordContext.setTenantSpec("AE_GLOBAL", -1);

        JSONObject jsonObject = JSONObject.parseObject(params);
        String json = jsonObject.getString("json");
        String dpath_env = jsonObject.getString("dpath_env");
        String ip = jsonObject.getString("ip");
        LandlordContext.setTenantSpec("AE_GLOBAL", -1);
        try {
            if (StringUtil.isNotBlank(ip)) {
                RequestCtxUtil.setDirectTargetServerIp(ip);
            }
            if (StringUtil.isNotBlank(dpath_env)) {
                EagleEye.putUserData("dpath_env", dpath_env);
            }
            JSONObject par = JSON.parseObject(json);
            Long payerId = par.getLong("payerId");
            if (!dataPoolService.checkTestAccount(payerId)) {
                result.setData("仅支持测试账号");
                result.setSuccess(true);
            }
            JSONObject res = HsfUtil.authCode(json, ip);
            logger.info("traceId: " + EagleEye.getTraceId());
            logger.info("res:" + JSON.toJSONString(res));
            result.setData(JSON.toJSONString(res));
            result.setSuccess(true);
        } catch (Throwable e) {
            result.setSuccess(false);
            result.setData("traceId: " + EagleEye.getTraceId() + "," + e.getMessage());
        }
        return result;
    }


    @Override
    public ResultDTO switchValueQuery(String params, SystemDTO systemDTO){
        String empId = systemDTO.getOperator();
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        String switchName = jsonObject.getString("switchName");
        String envType = jsonObject.getString("envType");
        String ip = jsonObject.getString("ip");

        // 传入 "<switchName> <说明>"
        if (switchName.contains(" ")) {
            switchName = switchName.split(" ", 2)[0];
        }

        String nodegroup;
        switch(envType){
            case "德国预发":
                nodegroup = "ae-payment-ns-s_de46_prehost";
                break;
            case "新加坡预发":
                nodegroup = "ae-payment-ns-s_sg-staging-sg52_prehost";
                break;
            case "俄罗斯预发":
                nodegroup = "ae-payment-ns-s_rg_ru_ru-staging-ru151_prehost";
                break;
            // 美东预发
            default:
                nodegroup = "ae-payment-ns-s_rg_us_east_us-staging-us68_prehost";
        }

        String url = "http://switch.pre.alibaba-inc.com/api/v1/ae-payment-ns-s/" + switchName + "/values.json";
        Map<String, String> httpParams = new LinkedHashMap<>();
        httpParams.put("authKey", "8e5eff6f35602be780743099ae4047f5");
        httpParams.put("authAppName", "ae-payment-ns-s");
        httpParams.put("envGroup", "aePreGroup");

        String httpRes = HttpUtils.doGet(url, httpParams);
        JSONArray resList = JSONObject.parseObject(httpRes).getJSONArray("data");

        resultDTO.setSuccess(true);
        JSONArray filteredList = new JSONArray();
        if (!StringUtil.isBlank(ip)) {
            filteredList = resList.stream()
                    .filter(i -> ip.equals(((JSONObject) i).getString("ip")))
                    .collect(Collectors.toCollection(JSONArray::new));
        } else if (!StringUtil.isBlank(nodegroup)) {
            String finalNodegroup = nodegroup;
            filteredList = resList.stream()
                    .filter(i -> finalNodegroup.equals(((JSONObject) i).getString("nodegroup")))
                    .collect(Collectors.toCollection(JSONArray::new));
        } else {
            resultDTO.setData("请输入环境或IP");
            resultDTO.setSuccess(false);
        }

        if (resultDTO.getSuccess()) {
            if (!filteredList.isEmpty()) {
                String res = ((JSONObject) filteredList.get(0)).getString("value");
                resultDTO.setData(res);
            } else {
                resultDTO.setData("查询结果为空");
                resultDTO.setSuccess(false);
            }
        }

        HsfUtil.measureAll("/payment/jobId="+systemDTO.getSite(),empId);
        return resultDTO;
    }

    @Override
    public ResultDTO refund(String params, SystemDTO systemDTO) throws Exception {
        String empId = systemDTO.getOperator();
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        String refundOrderAmountCent = jsonObject.getString("refundOrderAmountCent");
        String refundOrderAmountCurrency = jsonObject.getString("refundOrderAmountCurrency");
        String refundAmountCent = jsonObject.getString("refundAmountCent");
        String refundAmountCurrency = jsonObject.getString("refundAmountCurrency");
        String checkoutOrderNo = jsonObject.getString("checkoutOrderNo");
        String orderId = jsonObject.getString("orderId");
        String payerId = jsonObject.getString("payerId");

        RefundRequest request = new RefundRequest();
        Money refundOrderAmount = Money.of(BigDecimal.valueOf(Long.parseLong(refundOrderAmountCent)), refundOrderAmountCurrency);
        Money refundAmount = Money.of(BigDecimal.valueOf(Long.parseLong(refundAmountCent)), refundAmountCurrency);

        request.setPayerId(Long.parseLong(payerId));
        request.setCheckoutOrderNo(checkoutOrderNo);
        request.setRefundAmount(refundAmount);
        request.setRefundOrderAmount(refundOrderAmount);
        request.setBizRefundOrderNo("mocked" + orderId);
        request.setPlatform("AE");

        Map<String, String> extAttrMap = new HashMap<>();
        extAttrMap.put("TRADE_ORDER_LINE_NO", orderId);
        extAttrMap.put("MAIN_TRADE_ORDER_NO", orderId);
        request.setExtAttrMap(extAttrMap);

        request.setSource("global.trade");
        request.setRefundType("POST_DISBURSE_REFUND");

        List<RefundFundDetail> refundFundDetails = new LinkedList<>();
        RefundFundDetail detail = new RefundFundDetail();
        detail.setRefundAmount(refundAmount);
        detail.setRefundOrderAmount(refundOrderAmount);
        detail.setFundType("GOODS");
        refundFundDetails.add(detail);
        request.setRefundFundDetails(refundFundDetails);

        try {
            String res = paymentFacade.refund(request).toString();
            resultDTO.setSuccess(true);
            resultDTO.setData(res);
        } catch (Throwable e) {
            resultDTO.setSuccess(false);
            resultDTO.setData("traceId: " + EagleEye.getTraceId() + ", " + e.getMessage());
        }

        HsfUtil.measureAll("/payment/jobId="+systemDTO.getSite(),empId);
        return resultDTO;
    }

    @Override
    public ResultDTO bonusPay(String params, SystemDTO systemDTO) {
        String empId = systemDTO.getOperator();
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        String orderId = jsonObject.getString("orderId");
        String payCurrency = jsonObject.getString("payCurrency");
        String testUserId = jsonObject.getString("buyerId");

        if (orderId.isEmpty() || payCurrency.isEmpty() || testUserId.isEmpty()) {
            resultDTO.setSuccess(true);
            resultDTO.setData(JSONObject.toJSONString("参数输入为空", SerializerFeature.WriteDateUseDateFormat));
        }

        Long payerId = orderService.getPayerIdByOrderId(orderId);
        if (payerId == null) {
            resultDTO.setData("根据订单查询买家id异常，请检查输入订单号是否正确。");
            resultDTO.setSuccess(true);
            return resultDTO;
        }
        if (!String.valueOf(payerId).equals(testUserId)) {
            resultDTO.setData("买家账号和订单不一致。");
            resultDTO.setSuccess(true);
            return resultDTO;
        }
        Boolean userFlag = dataPoolService.checkTestAccount(payerId);
        if (!userFlag) {
            resultDTO.setData("查询买家账号信息失败，或买家账号不是测试账号。");
            resultDTO.setSuccess(true);
            return resultDTO;
        }

        LinkCaseOperateRequest request = new LinkCaseOperateRequest();
        request.setCaseId(2136L);
        request.setTenant("AEPAY");
        request.setExecUnit("aliyun-region-vpc-ap-southeast-1-pre");
        request.setParamJson("[{\"dataKey\":\"orderId\",\"dataModel\":\"电商支付_支付收单\",\"dataValue\":\"" +
                orderId +
                "\",\"dataName\":\"orderId\",\"key\":1,\"dataRefer\":\"data_126\"},{\"dataKey\":\"payAmount_ccyCode\",\"dataModel\":\"电商支付_提交支付\",\"dataValue\":\"" +
                payCurrency +
                "\",\"dataName\":\"payAmount_ccyCode\",\"key\":14,\"dataRefer\":\"data_166\"}]");
        ApiResponse<LinkCaseOperateResponse> resp = saigaCaseOperateFacade.operate(request);

        if (resp.isSuccess()) {
            Long recordId = resp.getBody().getRecordId();
            resultDTO.setData("https://aidc-case.alibaba-inc.com/industry/caseRunDetail?id="
                    + recordId
                    + "&caseId=2136&namespace=AEPAY");
            resultDTO.setSuccess(true);

        } else {
            resultDTO.setSuccess(false);
            resultDTO.setData("调用Saiga链路工具失败, "
                    + "traceId: " + EagleEye.getTraceId()
                    + ", "
                    + resp.getErrorMessage());
        }

        HsfUtil.measureAll("/payment/jobId=" + systemDTO.getSite(), empId);
        return resultDTO;
    }

    @Override
    public ResultDTO getExchangePrice4LinkedCase(String params, SystemDTO systemDTO) {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        String orderCurrency = jsonObject.getString("orderCurrency");
        String payCurrency = jsonObject.getString("payCurrency");
        String orderAmount = jsonObject.getString("orderAmount");   // 非cent，是0.01这种
        Map<String, String> resData = new LinkedHashMap<>();
        LandlordContext.setTenantSpec("AE_GLOBAL", -1);

        if (orderCurrency.isEmpty() || payCurrency.isEmpty() || orderAmount.isEmpty()) {
            resultDTO.setSuccess(false);
            resultDTO.setData(JSONObject.toJSONString("输入为空"));
        }
        Money money = Money.of(orderAmount, orderCurrency);

        try {
            GlobalPriceDTO globalPriceDTO = exchangeRateServiceHsf.getExchangePrice(money, payCurrency, Language.EN).getModule();

            resData.put("exchangeRate", globalPriceDTO.getRateInfo().getRate().toString());
            resData.put("exchangeRateNo",
                        orderCurrency.equals(payCurrency)? "DEFAULT_RATE_ID" :
                        globalPriceDTO.getRateInfo().getExchangeRateNo());
            resData.put("instExchangeRateNo",
                        orderCurrency.equals(payCurrency)? "DEFAULT_RATE_ID" :
                        globalPriceDTO.getRateInfo().getInstExchangeRateNo());
            resData.put("payAmount", String.valueOf(globalPriceDTO.getQuotePrice().getCent()));
            resultDTO.setData(JSONObject.toJSONString(resData));
            resultDTO.setSuccess(true);

        } catch (Exception e) {
            resultDTO.setSuccess(false);
            resultDTO.setData(JSONObject.toJSONString("获取汇率失败"));
        }

        return resultDTO;
    }

    @Override
    /**
     * 根据用户 ID、卡号后 4 位，查询静态收银缓存，获取匹配的卡信息
     */
    public ResultDTO getCardInfo4LinkedCase(String params, SystemDTO systemDTO) {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        String userId = jsonObject.getString("buyerId");
        String last4Digits = jsonObject.getString("last4Digits");
        Map<String, String> resData = new LinkedHashMap<>();
        LandlordContext.setTenantSpec("AE_GLOBAL", -1);

        if (userId.isEmpty() || last4Digits.isEmpty()) {
            resultDTO.setSuccess(false);
            resData.put("errorMsg", "输入为空");
            resultDTO.setData(JSONObject.toJSONString(resData));
            return resultDTO;
        }

        Boolean userFlag = dataPoolService.checkTestAccount(Long.valueOf(userId));
        if (!userFlag) {
            resultDTO.setSuccess(false);
            resData.put("errorMsg", "查询买家账号信息失败，或买家账号不是测试账号");
            resultDTO.setData(JSONObject.toJSONString(resData));
            return resultDTO;
        }

        try {
            // {\"data":"[{\"aepayToken\":\"AEPAY_PERM_Dy8CmcPbeSEdh9EJCB4GEHlqae2QPvsD41aI22gxgcK6/iasYsrH8pe8vGDbE/sSgmnYh/zZHKLPshzMYrDjvg==\",\"enabled\":true,\"extendInfo\":\"{\\\"aepayPaymentMethodDetail\\\":{\\\"aepayToken\\\":\\\"AEPAY_PERM_Dy8CmcPbeSEdh9EJCB4GEHlqae2QPvsD41aI22gxgcK6/iasYsrH8pe8vGDbE/sSgmnYh/zZHKLPshzMYrDjvg==\\\",\\\"enabled\\\":true,\\\"paymentMethodDetail\\\":\\\"{\\\\\\\"card\\\\\\\":{\\\\\\\"aepayToken\\\\\\\":\\\\\\\"AEPAY_PERM_Dy8CmcPbeSEdh9EJCB4GEHlqae2QPvsD41aI22gxgcK6/iasYsrH8pe8vGDbE/sSgmnYh/zZHKLPshzMYrDjvg==\\\\\\\",\\\\\\\"bindingCardDate\\\\\\\":\\\\\\\"2024-10-28T08:58:29Z\\\\\\\",\\\\\\\"brand\\\\\\\":\\\\\\\"VISA\\\\\\\",\\\\\\\"cardIssuer\\\\\\\":\\\\\\\"CHINA MERCHANTS BANK\\\\\\\",\\\\\\\"cardToken\\\\\\\":\\\\\\\"AEPAY_PERM_Dy8CmcPbeSEdh9EJCB4GEHlqae2QPvsD41aI22gxgcK6/iasYsrH8pe8vGDbE/sSgmnYh/zZHKLPshzMYrDjvg==\\\\\\\",\\\\\\\"countryIssue\\\\\\\":\\\\\\\"CN\\\\\\\",\\\\\\\"expiryMonth\\\\\\\":\\\\\\\"07\\\\\\\",\\\\\\\"expiryYear\\\\\\\":\\\\\\\"28\\\\\\\",\\\\\\\"instUserName\\\\\\\":{\\\\\\\"channelStrongPropertyExt\\\\\\\":{},\\\\\\\"firstName\\\\\\\":\\\\\\\"yiming\\\\\\\",\\\\\\\"lastName\\\\\\\":\\\\\\\"sun\\\\\\\"},\\\\\\\"ipayToken\\\\\\\":\\\\\\\"ALIPAYGvBSVy2VUa0HKJBnAFVHO9hLUMsuuIpxqDOlwJx732mGyhlYj5OvFj8V9BATUMSYyWCloE4MwfmN48sP1+rSPQ==\\\\\\\",\\\\\\\"last4\\\\\\\":\\\\\\\"7792\\\\\\\",\\\\\\\"mask\\\\\\\":\\\\\\\"451461******7792\\\\\\\",\\\\\\\"paymentMethodDetailMetadata\\\\\\\":\\\\\\\"{\\\\\\\\\\\\\\\"cardDetailIndex\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"2023071715027100188012505677826\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"cardDetailType\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"CREDIT_CARD\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"funding\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"CREDIT\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"cardBin\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"451461\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"uniqueIndex\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"536313e6cfb165ce7d05f6eb4f66b7a6c6ccdca23061ebbcb79d7c486e1c9c7b\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"expiredDateStatus\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"NORMAL\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"verifiedStatus\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"INIT\\\\\\\\\\\\\\\",\\\\\\\\\\\\\\\"cardIndex\\\\\\\\\\\\\\\":\\\\\\\\\\\\\\\"2023071715027100188636705426454\\\\\\\\\\\\\\\"}\\\\\\\",\\\\\\\"supportCardBrands\\\\\\\":[{\\\\\\\"cardBrand\\\\\\\":\\\\\\\"VISA\\\\\\\"}]},\\\\\\\"channelStrongPropertyExt\\\\\\\":{},\\\\\\\"paymentMethodDetailType\\\\\\\":\\\\\\\"card\\\\\\\"}\\\",\\\"paymentMethodType\\\":\\\"MIXEDCARD\\\",\\\"preferred\\\":true},\\\"assetSource\\\":\\\"ipayAndAepay\\\",\\\"cardIndex\\\":\\\"2023071715027100188636705426454\\\"}\",\"paymentMethodDetail\":\"{\\\"card\\\":{\\\"aepayToken\\\":\\\"AEPAY_PERM_Dy8CmcPbeSEdh9EJCB4GEHlqae2QPvsD41aI22gxgcK6/iasYsrH8pe8vGDbE/sSgmnYh/zZHKLPshzMYrDjvg==\\\",\\\"bindingCardDate\\\":\\\"2024-10-28T08:58:29Z\\\",\\\"brand\\\":\\\"VISA\\\",\\\"cardIssuer\\\":\\\"CHINA MERCHANTS BANK\\\",\\\"cardToken\\\":\\\"AEPAY_PERM_Dy8CmcPbeSEdh9EJCB4GEHlqae2QPvsD41aI22gxgcK6/iasYsrH8pe8vGDbE/sSgmnYh/zZHKLPshzMYrDjvg==\\\",\\\"countryIssue\\\":\\\"CN\\\",\\\"expiryMonth\\\":\\\"07\\\",\\\"expiryYear\\\":\\\"28\\\",\\\"instUserName\\\":{\\\"channelStrongPropertyExt\\\":{},\\\"firstName\\\":\\\"yiming\\\",\\\"lastName\\\":\\\"sun\\\"},\\\"ipayToken\\\":\\\"ALIPAYGvBSVy2VUa0HKJBnAFVHO9hLUMsuuIpxqDOlwJx732mGyhlYj5OvFj8V9BATUMSYyWCloE4MwfmN48sP1+rSPQ==\\\",\\\"last4\\\":\\\"7792\\\",\\\"mask\\\":\\\"451461******7792\\\",\\\"paymentMethodDetailMetadata\\\":\\\"{\\\\\\\"cardDetailIndex\\\\\\\":\\\\\\\"2023071715027100188012505677826\\\\\\\",\\\\\\\"cardDetailType\\\\\\\":\\\\\\\"CREDIT_CARD\\\\\\\",\\\\\\\"funding\\\\\\\":\\\\\\\"CREDIT\\\\\\\",\\\\\\\"cardBin\\\\\\\":\\\\\\\"451461\\\\\\\",\\\\\\\"uniqueIndex\\\\\\\":\\\\\\\"536313e6cfb165ce7d05f6eb4f66b7a6c6ccdca23061ebbcb79d7c486e1c9c7b\\\\\\\",\\\\\\\"expiredDateStatus\\\\\\\":\\\\\\\"NORMAL\\\\\\\",\\\\\\\"verifiedStatus\\\\\\\":\\\\\\\"INIT\\\\\\\",\\\\\\\"cardIndex\\\\\\\":\\\\\\\"2023071715027100188636705426454\\\\\\\"}\\\",\\\"supportCardBrands\\\":[{\\\"cardBrand\\\":\\\"VISA\\\"}]},\\\"channelStrongPropertyExt\\\":{},\\\"paymentMethodDetailType\\\":\\\"card\\\"}\",\"paymentMethodType\":\"MIXEDCARD\",\"preferred\":true}]"}
            JSONObject resp = HsfUtil.getStaticCashierPaymentMethod(Long.parseLong(userId));
            JSONArray data = resp.getJSONArray("data");

            for (int i = 0; i < data.size(); i++) {
                JSONObject item = data.getJSONObject(i);
                if (item.getString("paymentMethodType").equals("MIXEDCARD")) {
                    JSONObject paymentMethodDetail = item.getJSONObject("paymentMethodDetail");
                    JSONObject extendInfo = item.getJSONObject("extendInfo");
                    String last4 = paymentMethodDetail.getJSONObject("card").getString("last4");
                    if (last4.equals(last4Digits)) {
                        JSONObject card = paymentMethodDetail.getJSONObject("card");
                        JSONObject matchedCard = new JSONObject();

                        matchedCard.put("assetSource", extendInfo.getString("assetSource"));
                        matchedCard.put("cardIndex", extendInfo.getString("cardIndex"));
                        if (card.containsKey("aepayToken")) {
                            matchedCard.put("aepayToken", card.getString("aepayToken"));
                            // submitPayToken 优先取 aepayToken
                            matchedCard.put("submitPayToken", card.getString("aepayToken"));
                        } else {
                            matchedCard.put("aepayToken", "");
                        }
                        if (card.containsKey("ipayToken")) {
                            matchedCard.put("ipayToken", card.getString("ipayToken"));
                            if (!matchedCard.containsKey("submitPayToken")) {
                                matchedCard.put("submitPayToken", card.getString("ipayToken"));
                            }
                        } else {
                            matchedCard.put("ipayToken", "");
                        }
                        matchedCard.put("mask", card.getString("mask"));
                        matchedCard.put("CardBinCountry", card.getString("countryIssue"));
                        matchedCard.put("CardBin", card.getString("cardBin"));
                        matchedCard.put("CardBrand", card.getString("brand"));
                        matchedCard.put("CardIssuer", card.getString("cardIssuer"));

                        resData.put("matchedCard", matchedCard.toJSONString());
                        resData.put("originalResponse", data.toJSONString());

                        resultDTO.setSuccess(true);
                        resultDTO.setData(JSONObject.toJSONString(resData));
                        return resultDTO;
                    }
                }
            }
            resData.put("errorMsg", "未找到匹配的卡");
            resData.put("originalResponse", data.toJSONString());
            resultDTO.setSuccess(false);
            resultDTO.setData(JSONObject.toJSONString(resData));

        } catch (Exception e) {
            resData.put("errorMsg", e.getCause().getMessage());
            resultDTO.setData(JSONObject.toJSONString(resData));
            resultDTO.setSuccess(false);
        }

        return resultDTO;
    }

    @Override
    public String getSaigaReplayCompare(String replayId) {
        // 判断 ID 是不是跟 URL 传进来的，从 URL 提取出来
        final Pattern ID_PATTERN = Pattern.compile("(?<=id=)[^&]+");
        try {
            URL url = new URL(replayId);
            // 判断是否是合法的URL
            if (!url.getHost().isEmpty()) {
                Matcher matcher = ID_PATTERN.matcher(url.getQuery());
                if (matcher.find()) {
                    replayId = matcher.group();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        
        ApiResponse<Map<String, Object>> resp = replayService.getCollectAndReplayResult(replayId);
        return JSONObject.toJSONString(resp.getBody());
    }
}
