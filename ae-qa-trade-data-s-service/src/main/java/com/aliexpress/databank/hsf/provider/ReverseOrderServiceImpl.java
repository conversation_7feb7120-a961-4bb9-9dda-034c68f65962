package com.aliexpress.databank.hsf.provider;


import com.alibaba.ae.reverse.api.AeAfterSaleStrategyFacade;
import com.alibaba.ae.reverse.api.AeReverseOrderQueryFacade;
import com.alibaba.ae.reverse.api.AeReverseOrderQueryForConsoleFacade;
import com.alibaba.ae.reverse.model.AeReverseOrderLineDTO;
import com.alibaba.ae.reverse.model.ConsoleReverseOrderLineInfoDO;
import com.alibaba.ae.reverse.request.AeAfterSaleSellerStrategyQueryReq;
import com.alibaba.ae.reverse.response.AeAfterSaleStrategyResult;
import com.alibaba.ae.reverse.server.api.facade.InternalToolFacade;
import com.alibaba.ae.trade.open.bops.share.dataobject.base.TpbopsResultMsg;
import com.alibaba.ae.trade.open.bops.share.service.arbitration.TpbopsArbitrationTaskService;
import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.common.lang.StringUtil;
import com.alibaba.ecommerce.module.Response;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.global.g11n.utils.MonetaryUtils;
import com.alibaba.global.landlord.LandlordContext;
import com.alibaba.global.money.Money;
import com.alibaba.global.protocol.common.model.dto.ExtendParam;
import com.alibaba.global.protocol.common.model.dto.InvokeInfoDTO;
import com.alibaba.global.protocol.common.model.dto.MoneyDTO;
import com.alibaba.global.protocol.common.model.dto.RoutingInfoDTO;
import com.alibaba.global.qa.dc.model.builder.QueryResultBuilder;
import com.alibaba.global.qa.dc.model.param.SystemDTO;
import com.alibaba.global.qa.dc.model.result.QueryResultUnit;
import com.alibaba.global.qa.dc.model.result.ResultDTO;
import com.alibaba.global.reverse.protocol.sdk.model.dto.OperatorDTO;
import com.alibaba.global.reverse.protocol.sdk.model.dto.SponsorDTO;
import com.alibaba.global.uop.api.FulfillmentOrderQueryFacade;
import com.alibaba.global.uop.api.response.FulfillmentOrderDTO;
import com.alibaba.intl.ae.biz.promisetemplate.share.service.interfaces.PromiseTemplateInternalService;

import com.alibaba.rdc.xcommerce.base.commons.Result;
import com.alibaba.rdc.xcommerce.base.commons.Source;
import com.alibaba.rdc.xcommerce.base.commons.SourceEnum;
import com.alibaba.rdc.xcommerce.base.commons.User;
import com.alibaba.rdc.xcommerce.dispute.client.enums.ForceTypeEnum;
import com.alibaba.rdc.xcommerce.dispute.client.param.CancelDisputeParam;
import com.alibaba.rdc.xcommerce.dispute.client.service.DisputeWriteService;
import com.alibaba.reverse.cathedral.api.facade.ReverseArbitrationFacade;
import com.alibaba.reverse.cathedral.api.model.Features;
import com.alibaba.reverse.cathedral.api.model.ReverseArbitrationResultDTO;
import com.alibaba.reverse.cathedral.api.request.ArbitrationJudgementType;
import com.alibaba.reverse.cathedral.api.request.UpdateReverseArbitrationLineReqDTO;
import com.alibaba.reverse.cathedral.api.request.UpdateReverseArbitrationRequest;
import com.alibaba.rocketmq.client.exception.MQBrokerException;
import com.alibaba.rocketmq.client.exception.MQClientException;
import com.alibaba.rocketmq.client.producer.SendResult;
import com.alibaba.rocketmq.remoting.exception.RemotingException;
import com.aliexpress.databank.config.MqConfig;
import com.aliexpress.databank.constant.*;
import com.aliexpress.databank.dataobject.MockFRFulfillmentReq;
import com.aliexpress.databank.dataobject.QuerySellerStrategiesDto;
import com.aliexpress.databank.dataobject.TimeoutReqDto;
import com.aliexpress.databank.hsf.ReverseOrderService;
import com.aliexpress.databank.hsf.TimeoutService;
import com.aliexpress.databank.service.RefundRecordService;
import com.aliexpress.databank.service.ReverseService;
import com.aliexpress.databank.utils.HsfUtil;
import com.aliexpress.databank.utils.ReverseFulfilmentUtil;
import com.aliexpress.issue.api.AeIssueCancelOrderWriteFacade;
import com.aliexpress.issue.dispute.pojo.common.CancelOrderOperatorRequest;
import com.aliexpress.issue.dispute.pojo.common.Issue;
import com.aliexpress.issue.dispute.pojo.common.IssueCancelOrderOperatorResult;
import com.aliexpress.issue.dispute.pojo.common.OrderLineParam;
import com.aliexpress.issue.dispute.service.AeIssueDisputeQueryRemoteService;
import com.aliexpress.issue.dispute.service.DataMigration4TestService;
import com.aliexpress.issue.request.issue.IssueRefundChannelsRequest;
import com.aliexpress.issue.result.PlainResult;
import com.aliexpress.issue.result.dto.IssueRefundChannelDTO;
import com.aliexpress.issue.service.issue.IssueInstantRefundService;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.taobao.eagleeye.EagleEye;
import com.taobao.hsf.app.api.util.HSFApiConsumerBean;
import com.taobao.hsf.remoting.service.GenericService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.money.MonetaryAmount;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

import static com.aliexpress.databank.constant.Constant.*;

@Slf4j
@HSFProvider(serviceInterface = ReverseOrderService.class, serviceGroup = "HSF", serviceVersion = "1.0.0", clientTimeout = 20000)
public class ReverseOrderServiceImpl implements ReverseOrderService {

    @Autowired
    private MqConfig mqConfig;

    @Autowired
    private TpbopsArbitrationTaskService tpbopsArbitrationTaskService;

    @Autowired
    private DataMigration4TestService dataMigration4TestService;

    @Autowired
    private IssueInstantRefundService issueInstantRefundService;

    @Autowired
    private AeIssueDisputeQueryRemoteService issueDisputeQueryRemoteService;

    @Autowired
    private ReverseService reverseService;

    @Autowired
    private TimeoutService timeoutService;

    @Autowired
    private FulfillmentOrderQueryFacade fulfillmentOrderQueryFacade;

    @Autowired
    private PromiseTemplateInternalService promiseTemplateInternalService;

    @Autowired
    private AeIssueCancelOrderWriteFacade cancelOrderWriteFacade;

    @Autowired
    private RefundRecordService refundRecordService;

    @Autowired
    private AeReverseOrderQueryFacade aeReverseOrderQueryFacade;

    @Autowired
    private InternalToolFacade internalToolFacade;

    @Autowired
    private ReverseArbitrationFacade reverseArbitrationFacade;

    @Autowired
    private AeReverseOrderQueryForConsoleFacade aeReverseOrderQueryForConsoleFacade;

    @Autowired
    AeAfterSaleStrategyFacade aeAfterSaleStrategyFacade;

    @Autowired
    DisputeWriteService  disputeWriteService;


    private static final boolean DEBUG_FOR_CHARGEBACK = false;

    private static final Logger logger = LoggerFactory.getLogger(ReverseOrderServiceImpl.class);


    @Override
    public ResultDTO cancelOrderLine(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long orderId = jsonObject.getLong(Constant.ORDER_ID);
        Long orderLineId = jsonObject.getLong(Constant.SUB_ORDER_ID);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        Integer qty = jsonObject.getInteger(Constant.CANCEL_QUANTITY);
        String dpath = jsonObject.getString(Constant.DPATH_ENV);
        if (StringUtil.isNotBlank(dpath)) {
            EagleEye.putUserData("dpath_env", dpath);
        }
        CancelOrderOperatorRequest cancelOrderOperatorRequest = getCancelOrderOperatorRequest(buyerId, orderId, orderLineId, qty);
        com.aliexpress.issue.common.result.PlainResult<IssueCancelOrderOperatorResult> cancelOrderOperatorResultPlainResult = cancelOrderWriteFacade.openCancelOrderIssue(buyerId, "en_US", cancelOrderOperatorRequest);
        resultDTO.setSuccess(true);
        resultDTO.setData(JSONObject.toJSONString(cancelOrderOperatorResultPlainResult));
        resultDTO.setMessage(JSONObject.toJSONString(cancelOrderOperatorResultPlainResult));
        return resultDTO;
    }

    private CancelOrderOperatorRequest getCancelOrderOperatorRequest(Long buyerId, Long orderId, String source, String reason, String refundMethod) {
        CancelOrderOperatorRequest cancelOrderOperatorRequest = new CancelOrderOperatorRequest();
        cancelOrderOperatorRequest.setAdminAliId(buyerId);
        cancelOrderOperatorRequest.setCancelEvent(source);
        cancelOrderOperatorRequest.setCancelReason(reason);
        cancelOrderOperatorRequest.setIssueOperateSource("bundle@mt-aesc-app");
        cancelOrderOperatorRequest.setOperatorAliId(buyerId);
        cancelOrderOperatorRequest.setOperatorMemo("Test By QA");
        cancelOrderOperatorRequest.setOperatorRole("Seller");
        cancelOrderOperatorRequest.setOrderId(orderId);
        cancelOrderOperatorRequest.setOrderLineCancel(false);
        cancelOrderOperatorRequest.setRefundPaymentMethodType(refundMethod);
        return cancelOrderOperatorRequest;
    }

    private CancelOrderOperatorRequest getCancelOrderOperatorRequest(Long buyerId, Long orderId, Long orderLineId, Integer qty) {
        CancelOrderOperatorRequest cancelOrderOperatorRequest = new CancelOrderOperatorRequest();
        cancelOrderOperatorRequest.setOrderId(orderId);
        cancelOrderOperatorRequest.setOperatorAliId(buyerId);
        cancelOrderOperatorRequest.setCancelEvent("buyerCancel");
        cancelOrderOperatorRequest.setCancelReason("otherReasonsForSeller");
        cancelOrderOperatorRequest.setIssueOperateSource("mtop2");
        cancelOrderOperatorRequest.setAdminAliId(buyerId);
        cancelOrderOperatorRequest.setOperatorRole("buyer");
        cancelOrderOperatorRequest.setOperatorMemo("order_line_cancel_test");

        List<Long> orderLineIds = Lists.newArrayList();
        orderLineIds.add(orderLineId);
        cancelOrderOperatorRequest.setOrderLineIds(orderLineIds);

        List<OrderLineParam> orderLineParams = Lists.newArrayList();
        OrderLineParam orderLineParam = new OrderLineParam();
        orderLineParam.setOrderLineId(orderLineId);
        orderLineParam.setUnit(qty);
        orderLineParams.add(orderLineParam);
        cancelOrderOperatorRequest.setNeedCanceledOrderLines(orderLineParams);
        return cancelOrderOperatorRequest;
    }

    @Override
    public ResultDTO applyChargeBack(String params, SystemDTO systemDTO) throws InterruptedException, RemotingException, MQClientException, MQBrokerException {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        // 获取入参
        Long orderId = jsonObject.getLong(Constant.ORDER_ID);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        String isNewCb = jsonObject.getString(Constant.IS_NEW_CB);
        String rowCode = jsonObject.getString(Constant.CB_RAW_CODE);
        Boolean isMerge = jsonObject.getBoolean(Constant.IS_MERGE);
        String chargebackId = jsonObject.getString(Constant.CHARGE_BACK_ID);
        String resolveDueTime = jsonObject.getString(Constant.RESOLVE_DUE_TIME);
        List<String> customerMsgs = getCustomerMsgs(jsonObject);
        List<Map<String, String>> returnShippingInfos = getReturnShippingInfos(jsonObject);

        // 组装入参
        JSONObject request = JSONObject.parseObject(Constant.CB_MSG_HAPPEN_BODY);
        request.put(Constant.CB_ORDER_ID, orderId);
        request.put(Constant.BUYER_ID, buyerId);
        JSONObject attributes = request.getJSONObject("attributes");
        attributes.put(Constant.IS_NEW_CB, isNewCb);
        attributes.put(Constant.CHARGE_BACK_ID, chargebackId);
        List<Long> mergePayedTradeOrderIdList = getMergePayedTradeOrderIdList(isMerge);
        attributes.put(Constant.MERGE_PAYED_TRADE_ORDER_IDS, mergePayedTradeOrderIdList);
        JSONObject chargeBackEntity = attributes.getJSONObject("chargebackEntity");
        JSONObject extraInfo = chargeBackEntity.getJSONObject("extraInfo");
        extraInfo.getJSONObject("rawReason").put(Constant.CB_RAW_CODE, rowCode);
        JSONObject additionalInfo = chargeBackEntity.getJSONObject("additionalInfo");
        additionalInfo.put(Constant.CUSTOMER_MSGS, customerMsgs);
        additionalInfo.put(Constant.RETURN_SHIPPING_INFOS, returnShippingInfos);
        JSONObject disputeReasonInfo = chargeBackEntity.getJSONObject("disputeReasonInfo");
        disputeReasonInfo.put(Constant.CB_DISPUTE_RAW_CODE, rowCode);
        chargeBackEntity.put(Constant.CHARGE_BACK_ID, chargebackId);
        chargeBackEntity.put(Constant.RESOLVE_DUE_TIME, resolveDueTime);
        attributes.put("chargebackEntity", chargeBackEntity);
        request.put("attributes", attributes);

        //发送消息
        return sendChargeBackMessage(request, orderId);
    }

    @Override
    public ResultDTO chargeBack(String params, SystemDTO systemDTO) throws InterruptedException, RemotingException, MQClientException, MQBrokerException {
        ResultDTO resultDTO = new ResultDTO();
        try {
            JSONObject jsonObject = JSONObject.parseObject(params);
            // 获取入参
            Long orderId = jsonObject.getLong(Constant.ORDER_ID);
            Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
            String isNewCb = jsonObject.getString(Constant.IS_NEW_CB);
            String rowCode = StringUtils.isNotBlank(jsonObject.getString(Constant.CB_RAW_CODE)) ? jsonObject.getString(Constant.CB_RAW_CODE) : "PP-30";
            Boolean isMerge = jsonObject.getBoolean(Constant.IS_MERGE);
            String chargebackId = jsonObject.getString(Constant.CHARGE_BACK_ID);
            Integer appealDuration = jsonObject.getInteger(Constant.DURATION) == null ? 5 : jsonObject.getInteger(Constant.DURATION);
            String paymentChannel = jsonObject.getString(Constant.PAYMENT_CHANNEL);

            if (StringUtils.isEmpty(chargebackId)) {
                chargebackId = "2022102525013101532701937344";
            }

            // 组装入参
            JSONObject request = JSONObject.parseObject(Constant.CB_MSG_BODY);
            request.put(Constant.CB_ORDER_ID, orderId);
            request.put(Constant.BUYER_ID, buyerId);
            JSONObject attributes = request.getJSONObject("attributes");
            attributes.put(Constant.IS_NEW_CB, isNewCb);
            attributes.put(Constant.CHARGE_BACK_ID, chargebackId);
            List<Long> mergePayedTradeOrderIdList = getMergePayedTradeOrderIdList(isMerge);
            attributes.put(Constant.MERGE_PAYED_TRADE_ORDER_IDS, mergePayedTradeOrderIdList);
            JSONObject chargeBackEntity = attributes.getJSONObject("chargebackEntity");
            JSONObject extraInfo = chargeBackEntity.getJSONObject("extraInfo");
            extraInfo.getJSONObject("rawReason").put(Constant.CB_RAW_CODE, rowCode);
            JSONObject disputeReasonInfo = chargeBackEntity.getJSONObject("disputeReasonInfo");
            disputeReasonInfo.put(Constant.CB_DISPUTE_RAW_CODE, rowCode);
            chargeBackEntity.put(Constant.CHARGE_BACK_ID, chargebackId);
            if (appealDuration != -1) {
                chargeBackEntity.put("resolveDueTime", getCbResolveDueTime(appealDuration));
            } else {
                chargeBackEntity.remove("resolveDueTime");
            }
            attributes.put("chargebackEntity", chargeBackEntity);
            attributes.put("payOptionCode", paymentChannel);
            request.put("attributes", attributes);

//        发消息
            String dpath = jsonObject.getString(Constant.DPATH_ENV);
            if (StringUtil.isNotBlank(dpath)) {
                EagleEye.putUserData("dpath_env", dpath);
            }
            SendResult result = mqConfig.sendMessage(Constant.PAYMENT_TOPIC, Constant.CB_TAG, orderId.toString(), JSONObject.toJSONString(request));
            resultDTO.setSuccess(true);
            resultDTO.setData(JSONObject.toJSONString(result));
            resultDTO.setMessage(JSONObject.toJSONString(result));
        } catch (Exception e) {
            resultDTO.setMessage(JSONObject.toJSONString(e));
            resultDTO.setData(JSONObject.toJSONString(e));
        }
        return resultDTO;
    }

    private String getCbResolveDueTime(Integer appealDuration) {
        Long timestamp = System.currentTimeMillis();
        // 1天 = 8640000ms + 300000(5分钟buffer)
        Long appleTimestamp = appealDuration * 86400000L + 300000L + timestamp;
        Instant instant = Instant.ofEpochMilli(appleTimestamp);
        ZonedDateTime zonedDateTime = ZonedDateTime.ofInstant(instant, ZoneId.systemDefault());
        return zonedDateTime.format(Constant.FORMATTER_WITH_T) + "-08:00";
    }

    @Override
    public ResultDTO chargeBackRetry(String params, SystemDTO systemDTO) throws InterruptedException, RemotingException, MQClientException, MQBrokerException {
        JSONObject jsonObject = JSONObject.parseObject(params);
        // 获取入参
        Long orderId = jsonObject.getLong(Constant.ORDER_ID);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        String isNewCb = jsonObject.getString(Constant.IS_NEW_CB);
        Boolean isMerge = jsonObject.getBoolean(Constant.IS_MERGE);
        String chargebackId = jsonObject.getString(Constant.CHARGE_BACK_ID);
        String resolveDueTime = jsonObject.getString(Constant.RESOLVE_DUE_TIME);
        List<String> customerMsgs = getCustomerMsgs(jsonObject);
        List<Map<String, String>> returnShippingInfos = getReturnShippingInfos(jsonObject);

        // 组装入参
        JSONObject request = JSONObject.parseObject(Constant.CB_MSG_RETRY_BODY);
        request.put(Constant.CB_ORDER_ID, orderId);
        request.put(Constant.BUYER_ID, buyerId);
        JSONObject attributes = request.getJSONObject("attributes");
        attributes.put(Constant.IS_NEW_CB, isNewCb);
        attributes.put(Constant.CHARGE_BACK_ID, chargebackId);
        List<Long> mergePayedTradeOrderIdList = getMergePayedTradeOrderIdList(isMerge);
        attributes.put(Constant.MERGE_PAYED_TRADE_ORDER_IDS, mergePayedTradeOrderIdList);
        JSONObject chargeBackEntity = attributes.getJSONObject("chargebackEntity");
        chargeBackEntity.put(Constant.CHARGE_BACK_ID, chargebackId);
        if (StringUtils.isNotEmpty(resolveDueTime)) {
            chargeBackEntity.put(Constant.RESOLVE_DUE_TIME, resolveDueTime);
        }
        JSONObject additionalInfo = chargeBackEntity.getJSONObject("additionalInfo");
        additionalInfo.put(Constant.CUSTOMER_MSGS, customerMsgs);
        additionalInfo.put(Constant.RETURN_SHIPPING_INFOS, returnShippingInfos);
        attributes.put("chargebackEntity", chargeBackEntity);
        request.put("attributes", attributes);

        return sendChargeBackMessage(request, orderId);
    }

    @Override
    public ResultDTO chargeBackAppeal(String params, SystemDTO systemDTO) throws InterruptedException, RemotingException, MQClientException, MQBrokerException {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        // 获取入参
        Long orderId = jsonObject.getLong(Constant.ORDER_ID);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        String isNewCb = jsonObject.getString(Constant.IS_NEW_CB);
        Boolean isMerge = jsonObject.getBoolean(Constant.IS_MERGE);
        String chargebackId = jsonObject.getString(Constant.CHARGE_BACK_ID);
        String resolveDueTime = jsonObject.getString(Constant.RESOLVE_DUE_TIME);
        List<String> customerMsgs = getCustomerMsgs(jsonObject);
        List<Map<String, String>> returnShippingInfos = getReturnShippingInfos(jsonObject);

        // 组装入参
        JSONObject request = JSONObject.parseObject(Constant.CB_MSG_APPEAL_BODY);
        request.put(Constant.CB_ORDER_ID, orderId);
        request.put(Constant.BUYER_ID, buyerId);
        JSONObject attributes = request.getJSONObject("attributes");
        attributes.put(Constant.IS_NEW_CB, isNewCb);
        attributes.put(Constant.CHARGE_BACK_ID, chargebackId);
        List<Long> mergePayedTradeOrderIdList = getMergePayedTradeOrderIdList(isMerge);
        attributes.put(Constant.MERGE_PAYED_TRADE_ORDER_IDS, mergePayedTradeOrderIdList);
        JSONObject chargeBackEntity = attributes.getJSONObject("chargebackEntity");
        chargeBackEntity.put(Constant.CHARGE_BACK_ID, chargebackId);
        chargeBackEntity.put(Constant.RESOLVE_DUE_TIME, resolveDueTime);
        JSONObject additionalInfo = chargeBackEntity.getJSONObject("additionalInfo");
        additionalInfo.put(Constant.CUSTOMER_MSGS, customerMsgs);
        additionalInfo.put(Constant.RETURN_SHIPPING_INFOS, returnShippingInfos);
        attributes.put("chargebackEntity", chargeBackEntity);
        request.put("attributes", attributes);

        //发送消息
        return sendChargeBackMessage(request, orderId);
    }

    @Override
    public ResultDTO chargeBackJudge(String params, SystemDTO systemDTO) throws InterruptedException, RemotingException, MQClientException, MQBrokerException {
        //获取入参
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long orderId = jsonObject.getLong(Constant.ORDER_ID);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        String chargebackId = StringUtils.isNotBlank(jsonObject.getString(Constant.CHARGE_BACK_ID)) ? jsonObject.getString(Constant.CHARGE_BACK_ID) : "2022102525013101532701937344";
        String isNewCb = jsonObject.getString(Constant.IS_NEW_CB);
        String amount = jsonObject.getString(Constant.CB_CHARGE_BACK_AMT);
        String currency = jsonObject.getString(Constant.CB_CHARGE_BACK_CNY);
        Boolean isMerge = jsonObject.getBoolean(Constant.IS_MERGE);
        Money changeBackAmt = Money.ofMinorUnit(currency, Long.parseLong(amount));
        String judgementMsg = jsonObject.getString(Constant.JUDGEMENT_MSG);
        List<String> customerMsgs = getCustomerMsgs(jsonObject);
        List<Map<String, String>> returnShippingInfos = getReturnShippingInfos(jsonObject);

        //组装入参
        JSONObject request = JSONObject.parseObject(Constant.CB_MSG_RESULT_BODY);
        request.put(Constant.CB_ORDER_ID, orderId);
        request.put(Constant.BUYER_ID, buyerId);
        JSONObject attributes = request.getJSONObject("attributes");
        attributes.put(Constant.IS_NEW_CB, isNewCb);
        attributes.put(Constant.CHARGE_BACK_ID, chargebackId);
        List<Long> mergePayedTradeOrderIdList = getMergePayedTradeOrderIdList(isMerge);
        attributes.put(Constant.MERGE_PAYED_TRADE_ORDER_IDS, mergePayedTradeOrderIdList);
        JSONObject chargeBackEntity = attributes.getJSONObject("chargebackEntity");
        chargeBackEntity.put("chargebackAmount", JSONObject.toJSONString(changeBackAmt));
        chargeBackEntity.put(Constant.CHARGE_BACK_ID, chargebackId);
        JSONObject additionalInfo = chargeBackEntity.getJSONObject("additionalInfo");
        additionalInfo.put(Constant.JUDGEMENT_MSG, judgementMsg);
        additionalInfo.put(Constant.CUSTOMER_MSGS, customerMsgs);
        additionalInfo.put(Constant.RETURN_SHIPPING_INFOS, returnShippingInfos);
        attributes.put("chargebackEntity", chargeBackEntity);
        request.put("attributes", attributes);
        //加隔离标
        String dpath = jsonObject.getString(Constant.DPATH_ENV);
        if (StringUtil.isNotBlank(dpath)) {
            EagleEye.putUserData("dpath_env", dpath);
        }

        //发送消息
        return sendChargeBackMessage(request, orderId);
    }

    @Override
    public ResultDTO mockAscan(String params, SystemDTO systemDTO) throws InterruptedException, RemotingException, MQClientException, MQBrokerException {
        ResultDTO resultDTO = new ResultDTO();
        try {
            JSONObject jsonObject = JSONObject.parseObject(params);
            Long orderId = jsonObject.getLong(Constant.ORDER_ID);
            Long reverseOrderLineId = jsonObject.getLong("reverseId");
            String type = jsonObject.getString(Constant.FULFILLMENT_TYPE);
            String trackingNumber = "PQ56GB0762593130128806S";
//            String trackingNumber = jsonObject.getString(Constant.TRACKING_NUMBER);
//            trackingNumber = StringUtils.isEmpty(trackingNumber) ? "PQ56GB0762593130128806S" : trackingNumber;
            //组装入参
            String msg = "";
            switch (type) {
                case "0":
                    msg = Constant.FULFILLMENT_MSG_MAIL_BODY;
                    break;
                case "1":
                    msg = Constant.FULFILLMENT_MSG_ASCAN_BODY;
                    break;
                case "2":
                    msg = Constant.FULFILLMENT_MSG_DSCAN_BODY;
                    break;
                case "3":
                    msg = Constant.FULFILLMENT_MSG_QC_BODY;
                    break;
                default:
                    msg = "";
                    break;
            }
            JSONObject request = JSONObject.parseObject(msg);
            JSONObject fulfillmentOrderItemStatusUpdatedList = request.getJSONArray("fulfillmentOrderItemStatusUpdatedList").getJSONObject(0);
            fulfillmentOrderItemStatusUpdatedList.put("reverseTradeOrderItemId", reverseOrderLineId.toString());
            ArrayList<JSONObject> arrayList = new ArrayList<JSONObject>();
            arrayList.add(fulfillmentOrderItemStatusUpdatedList);
            request.put("fulfillmentOrderItemStatusUpdatedList", arrayList);
            if ("0".equals(type)) {
                JSONObject extendMap = request.getJSONObject("extendMap");
                extendMap.put("trackingNumber", trackingNumber);
            }
            //发送消息
            SendResult result = mqConfig.sendMessage(Constant.REVERSE_FULFILLMENT_TOPIC, Constant.REVERSE_FULFILLMENT_TAG, reverseOrderLineId.toString(), JSONObject.toJSONString(request));
            resultDTO.setSuccess(true);
            resultDTO.setData(JSONObject.toJSONString(result));
            resultDTO.setMessage(JSONObject.toJSONString(result));
            return resultDTO;
        } catch (Exception e) {
            log.error("mockSendFulfillmentMsgByType Exception", e);
        }
        return resultDTO;
    }

    @Override
    public ResultDTO mockSendFulfillmentMsgByType(String params, SystemDTO systemDTO) throws InterruptedException, RemotingException, MQClientException, MQBrokerException {
        ResultDTO resultDTO = new ResultDTO();
        try {
            JSONObject jsonObject = JSONObject.parseObject(params);
            Long reverseOrderLineId = jsonObject.getLong(Constant.REVERSE_ORDER_LINE_ID);
            String type = jsonObject.getString(Constant.FULFILLMENT_TYPE);
            String trackingNumber = jsonObject.getString(Constant.TRACKING_NUMBER);
            trackingNumber = StringUtils.isEmpty(trackingNumber) ? "PQ56GB0762593130128806S" : trackingNumber;
            //组装入参
            String msg = "";
            switch (type) {
                case "0":
                    msg = Constant.FULFILLMENT_MSG_MAIL_BODY;
                    break;
                case "1":
                    msg = Constant.FULFILLMENT_MSG_ASCAN_BODY;
                    break;
                case "2":
                    msg = Constant.FULFILLMENT_MSG_DSCAN_BODY;
                    break;
                case "3":
                    msg = Constant.FULFILLMENT_MSG_QC_BODY;
                    break;
                default:
                    msg = "";
                    break;
            }
            JSONObject request = JSONObject.parseObject(msg);
            JSONObject fulfillmentOrderItemStatusUpdatedList = request.getJSONArray("fulfillmentOrderItemStatusUpdatedList").getJSONObject(0);
            fulfillmentOrderItemStatusUpdatedList.put("reverseTradeOrderItemId", reverseOrderLineId.toString());
            request.put("fulfillmentOrderItemStatusUpdatedList", fulfillmentOrderItemStatusUpdatedList);
            if ("0".equals(type)) {
                JSONObject extendMap = request.getJSONObject("extendMap");
                extendMap.put("trackingNumber", trackingNumber);
            }
            //发送消息
            SendResult result = mqConfig.sendMessage(Constant.REVERSE_FULFILLMENT_TOPIC, Constant.REVERSE_FULFILLMENT_TAG, reverseOrderLineId.toString(), JSONObject.toJSONString(request));
            resultDTO.setSuccess(true);
            resultDTO.setData(JSONObject.toJSONString(result));
            resultDTO.setMessage(JSONObject.toJSONString(result));
            return resultDTO;
        } catch (Exception e) {
            log.error("mockSendFulfillmentMsgByType Exception", e);
        }
        return resultDTO;
    }

    @Override
    public ResultDTO hsfSendFulfillmentMsgByType(Long reverseOrderLineId, Long buyerId, Long sellerId, String type, String trackingNumber) throws InterruptedException, RemotingException, MQClientException, MQBrokerException {
        ResultDTO resultDTO = new ResultDTO();
        trackingNumber = StringUtils.isEmpty(trackingNumber) ? "PQ56GB0762593130128806S" : trackingNumber;
        //组装入参
        String msg = "";
        switch (type) {
            case "0":
                msg = Constant.FULFILLMENT_MSG_MAIL_BODY;
                break;
            case "1":
                msg = Constant.FULFILLMENT_MSG_ASCAN_BODY;
                break;
            case "2":
                msg = Constant.FULFILLMENT_MSG_DSCAN_BODY;
                break;
            case "3":
                msg = Constant.FULFILLMENT_MSG_QC_BODY;
                break;
            default:
                msg = "";
                break;
        }
        JSONObject request = JSONObject.parseObject(msg);
        JSONObject fulfillmentOrderItemStatusUpdatedList = request.getJSONArray("fulfillmentOrderItemStatusUpdatedList").getJSONObject(0);
        fulfillmentOrderItemStatusUpdatedList.put("reverseTradeOrderItemId", reverseOrderLineId.toString());
        request.put("fulfillmentOrderItemStatusUpdatedList", fulfillmentOrderItemStatusUpdatedList);
        if ("0".equals(type)) {
            JSONObject extendMap = request.getJSONObject("extendMap");
            extendMap.put("trackingNumber", trackingNumber);
        }
        // resultDTO.setData(JSONObject.toJSONString(request));

        //发送消息
        SendResult result = mqConfig.sendMessage(Constant.REVERSE_FULFILLMENT_TOPIC, Constant.REVERSE_FULFILLMENT_TAG, reverseOrderLineId.toString(), JSONObject.toJSONString(request));
        resultDTO.setSuccess(true);
        resultDTO.setData(JSONObject.toJSONString(result));
        resultDTO.setMessage(JSONObject.toJSONString(result));
        return resultDTO;
    }

    @Override
    public JSONObject mockSendGoods(String foId, Long orderIdStr, Long buyerId) {
//        JSONObject jsonObject = JSONObject.parseObject(params);
//        String foId = jsonObject.getString("foId");
//        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
//        Long sellerId = jsonObject.getLong(Constant.SELLER_ID);
        JSONObject countRes = new JSONObject();
        try {
            countRes = HsfUtil.doFulfillmentOrder(foId);
        } catch (Exception e) {
            countRes.put("error msg", e.getMessage());
            return countRes;
        }
        return countRes;
    }

    @Override
    public JSONObject mockCainiao(String foId, Long orderIdStr, Long buyerId) {
        JSONObject countRes = new JSONObject();
        try {
            countRes = HsfUtil.sendFulfill(foId, buyerId);
        } catch (Exception e) {
            countRes.put("error msg", e.getMessage());
            return countRes;
        }
        return countRes;
    }

    @Override
    public ResultDTO mockSendFulfillmentMessageBody(Long reverseOrderLineId, String messageBody) throws InterruptedException, RemotingException, MQClientException, MQBrokerException {
        ResultDTO resultDTO = new ResultDTO();
        //发送消息
        SendResult result = mqConfig.sendMessage(Constant.REVERSE_FULFILLMENT_TOPIC, Constant.REVERSE_FULFILLMENT_TAG, reverseOrderLineId.toString(), messageBody);
        resultDTO.setSuccess(true);
        resultDTO.setData(JSONObject.toJSONString(result));
        resultDTO.setMessage(JSONObject.toJSONString(result));

        return resultDTO;
    }

    @Override
    public ResultDTO mockSendMessageByTopic(Long reverseOrderLineId, String topic, String tag, String messageBody) throws InterruptedException, RemotingException, MQClientException, MQBrokerException {
        ResultDTO resultDTO = new ResultDTO();
        //发送消息
        SendResult result = mqConfig.sendMessage(topic, tag, reverseOrderLineId.toString(), messageBody);
        resultDTO.setSuccess(true);
        resultDTO.setData(JSONObject.toJSONString(result));
        resultDTO.setMessage(JSONObject.toJSONString(result));

        return resultDTO;
    }

    private List<Long> getMergePayedTradeOrderIdList(boolean isMerge) {
        List<Long> mergePayedTradeOrderIdList = new ArrayList<>();
        if (isMerge == true) {
            mergePayedTradeOrderIdList.add(1L);
            mergePayedTradeOrderIdList.add(2L);
        } else {
            mergePayedTradeOrderIdList.add(1L);
        }
        return mergePayedTradeOrderIdList;
    }

    @Override
    public ResultDTO endPayout(String params, SystemDTO systemDTO) {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long taskId = jsonObject.getLong(Constant.TASK_ID);
        TpbopsResultMsg<Boolean> result = tpbopsArbitrationTaskService.executeByTaskId(taskId);
        resultDTO.setSuccess(result.isSuccess());
        resultDTO.setMessage(JSON.toJSONString(result));
        resultDTO.setData(JSON.toJSONString(result));
        return resultDTO;
    }

    @Override
    public ResultDTO getRefundChannel(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        String reverseType = jsonObject.getString(Constant.REVERSE_TYPE);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        String currencyCode = jsonObject.getString(Constant.CURRENCY);
        String alipayToken = jsonObject.getString(Constant.ALIPAY_TOKEN);
        IssueRefundChannelsRequest issueRefundChannelsRequest = new IssueRefundChannelsRequest();
        issueRefundChannelsRequest.setOperatorAliId(buyerId);
        issueRefundChannelsRequest.setCurrency(currencyCode);
        if (reverseType.equalsIgnoreCase("取消订单")) {
            Long orderId = jsonObject.getLong(Constant.ORDER_ID);
            issueRefundChannelsRequest.setScenes("cancel");
            issueRefundChannelsRequest.setOrderId(orderId);
        } else if (reverseType.equalsIgnoreCase("纠纷")) {
            Long subOrderId = jsonObject.getLong(Constant.SUB_ORDER_ID);
            issueRefundChannelsRequest.setOrderId(subOrderId);
            issueRefundChannelsRequest.setScenes("issue");
        }
        issueRefundChannelsRequest.setOperatorRole("platform");

        issueRefundChannelsRequest.setAlipayToken(alipayToken);
        String orderInfo = jsonObject.getString(Constant.ORDER_INFO);
        String countryCode = JSON.parseObject(orderInfo).getJSONObject("module").getJSONObject("deliveryAddress").getString("countryCode");
        issueRefundChannelsRequest.setCountryCode(countryCode);
        issueRefundChannelsRequest.setUmidToken("z+xLIUNLOs3cazV3OK94Tn1sUmfC6Qu");
        issueRefundChannelsRequest.setTerminalType("APP");
        issueRefundChannelsRequest.set_lang("en_US");
        issueRefundChannelsRequest.setClientIp("*************");
        issueRefundChannelsRequest.setSessionId("82c7b8af-fc8d-4970-b6c0-04ae43e5cf73_al_b_pre");
        issueRefundChannelsRequest.setOsType("ios");
        PlainResult<IssueRefundChannelDTO> refundChannelDTOPlainResult = issueInstantRefundService.getIssueRefundChannels(buyerId, issueRefundChannelsRequest);
        resultDTO.setSuccess(refundChannelDTOPlainResult.isSuccess());
        if (resultDTO.getSuccess()) {
            Map<String, QueryResultUnit> data = QueryResultBuilder.buildQueryResult("退款渠道", null, null, refundChannelDTOPlainResult.getData());
            resultDTO.setData(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
        } else {
            resultDTO.setData(JSONObject.toJSONString(refundChannelDTOPlainResult.getMessage(), SerializerFeature.WriteDateUseDateFormat));
        }
        return resultDTO;
    }

    @Override
    public ResultDTO migrateOldIssue(String params, SystemDTO systemDTO) {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        String issueId = jsonObject.getString(Constant.ISSUE_ID);
        dataMigration4TestService.batchMigration(issueId);
        resultDTO.setSuccess(true);
        resultDTO.setData("迁移老纠纷成功");
        resultDTO.setMessage("执行成功");
        return resultDTO;
    }

    @Override
    public ResultDTO getIssue(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        String issueInfo = jsonObject.getString(Constant.ISSUE_INFO);
        String orderId = jsonObject.getString(Constant.ORDER_ID);
        String orderLineId = jsonObject.getString(Constant.SUB_ORDER_ID);
        if (StringUtil.isNotBlank(issueInfo)) {
            Map<String, QueryResultUnit> data = QueryResultBuilder.buildQueryResult("纠纷信息", null, null, JSONObject.parseObject(issueInfo, Issue.class));
            resultDTO.setData(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
        } else if (StringUtil.isNotBlank(orderId)) {
            com.aliexpress.issue.common.result.PlainResult<List<Issue>> issues = issueDisputeQueryRemoteService.findIssueByParentOrderIdForInternalService(Long.parseLong(orderId));
            Map<String, QueryResultUnit> data = QueryResultBuilder.buildQueryResult("纠纷信息", null, null, issues.getData());
            resultDTO.setData(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
        } else if (StringUtil.isNotBlank(orderLineId)) {
            com.aliexpress.issue.common.result.PlainResult<Issue> issue = issueDisputeQueryRemoteService.findIssueByOrderIdForInternalService(Long.parseLong(orderLineId));
            Map<String, QueryResultUnit> data = QueryResultBuilder.buildQueryResult("纠纷信息", null, null, issue.getData());
            resultDTO.setData(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
        }
        return resultDTO;
    }

    @Override
    public ResultDTO getReverseByOrderId(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long orderId = jsonObject.getLong(Constant.ORDER_ID);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        Long reverseOrderLineId = jsonObject.getLong(Constant.REVERSE_ORDER_LINE_ID);
        Long tradeOrderLineIdOfRequest = jsonObject.getLong(Constant.SUB_ORDER_ID);
        LandlordContext.setTenantSpec("AE_GLOBAL", -1);
        Map<String, QueryResultUnit> data = new LinkedHashMap<>();
        if (reverseOrderLineId != null && reverseOrderLineId != 0L) {
            JSONObject reverseOrderLine = HsfUtil.getReverseOrderLineByReverseOrderLineId(buyerId, reverseOrderLineId);
            JSONObject reverseOrder = HsfUtil.getReverseOrderByReverseOrderId(buyerId, reverseOrderLine.getJSONObject("result").getLong("reverseOrderId"));
            JSONObject solutions = HsfUtil.getReverseSolution(reverseOrderLineId);
            Map<String, QueryResultUnit> reverseOrderData = QueryResultBuilder.buildQueryResult("逆向主单信息", null, null, reverseOrder.getJSONObject("result"));
            Map<String, QueryResultUnit> reverseOrderLineData = QueryResultBuilder.buildQueryResult("逆向子单信息", null, null, reverseOrderLine.getJSONObject("result"));
            Map<String, QueryResultUnit> solutionData = QueryResultBuilder.buildQueryResult("方案表", null, null, solutions.getJSONArray("result"));
            data.putAll(reverseOrderData);
            data.putAll(reverseOrderLineData);
            data.putAll(solutionData);
            data.putAll(timeoutService.getReverseTimeouts("", reverseOrderLine.getJSONObject("result")));
        } else if (tradeOrderLineIdOfRequest != null && tradeOrderLineIdOfRequest != 0L) {
            JSONObject reverseOrderLine = HsfUtil.getReverseOrderLineByTradeOrderLineId(buyerId, tradeOrderLineIdOfRequest).getJSONObject("result");
            JSONObject reverseOrder = HsfUtil.getReverseOrderByReverseOrderId(buyerId, reverseOrderLine.getLong("reverseOrderId"));
            JSONObject solutions = HsfUtil.getReverseSolution(reverseOrderLineId);
            Map<String, QueryResultUnit> reverseOrderData = QueryResultBuilder.buildQueryResult("逆向主单信息", null, null, reverseOrder.getJSONObject("result"));
            Map<String, QueryResultUnit> reverseOrderLineData = QueryResultBuilder.buildQueryResult("逆向子单信息", null, null, reverseOrderLine);
            Map<String, QueryResultUnit> solutionData = QueryResultBuilder.buildQueryResult("方案表", null, null, solutions.getJSONArray("result"));
            data.putAll(reverseOrderData);
            data.putAll(reverseOrderLineData);
            data.putAll(solutionData);
            data.putAll(timeoutService.getReverseTimeouts("", reverseOrderLine));
        } else {
            JSONObject reverseOrderLines = HsfUtil.getReverseOrderLinesByOrderId(buyerId, orderId);
            Map<Long, Set<Long>> reversOrderIds = getReverseOrderIds(reverseOrderLines);
            Map<Long, JSONObject> reversOrderLineIds = getReverseOrderLineIds(reverseOrderLines);
            reversOrderIds.keySet().forEach(reverseOrderId -> {
                Long tradeOrderLineId = reversOrderLineIds.get(reversOrderIds.get(reverseOrderId).toArray()[0]).getLong("tradeOrderLineId");
                try {
                    // 逆向主单信息
                    JSONObject reverseOrderJsonObj = HsfUtil.getReverseOrderByReverseOrderId(buyerId, reverseOrderId);
                    Map<String, QueryResultUnit> reverseOrder = QueryResultBuilder.buildQueryResult("逆向主单信息 -- 子单id： " + tradeOrderLineId + " -- 逆向主单id：" + reverseOrderId, null, null, reverseOrderJsonObj.getJSONObject("result"));
                    data.putAll(reverseOrder);
                } catch (Exception e) {
                    e.printStackTrace();
                }

                reversOrderIds.get(reverseOrderId).forEach(it -> {
                    try {
                        // 逆向子单信息
                        Map<String, QueryResultUnit> reverseOrderLine = QueryResultBuilder.buildQueryResult("逆向子单信息 -- 子单id： " + tradeOrderLineId + " -- 逆向子单id：" + it, null, null, reversOrderLineIds.get(it));
                        data.putAll(reverseOrderLine);

                        // 方案表
                        JSONObject solutionJsonObj = HsfUtil.getReverseSolution(it);
                        Map<String, QueryResultUnit> solution = QueryResultBuilder.buildQueryResult("方案表 -- 子单id： " + tradeOrderLineId + " -- 逆向子单id：" + it, null, null, solutionJsonObj.getJSONArray("result"));
                        data.putAll(solution);

                        // 超时
                        data.putAll(timeoutService.getReverseTimeouts("", reversOrderLineIds.get(it)));

                        // 服务域
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                });
            });
        }
        result.setData(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
        result.setSuccess(true);
        return result;
    }

    @Override
    public ResultDTO returnAndRefund(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Boolean isOverSize = jsonObject.getBoolean(Constant.IS_OVERSIZE);
        String tradeOrderLineId = jsonObject.getString(Constant.SUB_ORDER_ID);
        String tradeOrderId = jsonObject.getString(Constant.ORDER_ID);
        String refundChannel = jsonObject.getString(Constant.REFUND_CHANNEL);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        boolean isReceived = jsonObject.getBoolean(Constant.IS_RECEIVED);
        String returnOrRefund = jsonObject.getString(Constant.RETURN_OR_REFUND);
        String amount = jsonObject.getString(Constant.RETURN_AMT);
        String currency = jsonObject.getString(Constant.CURRENCY);
        String returnType = jsonObject.getString(Constant.RETURN_TYPE);
        String returnReason = jsonObject.getString(Constant.RETURN_REASON);
        int quantity = jsonObject.getJSONObject(Constant.SUB_ORDER_INFO).getJSONObject("module").getIntValue("quantity");
        JSONObject createReverseReq = reverseService.getCreateReverseReq(tradeOrderLineId, tradeOrderId, refundChannel, buyerId,
                isReceived, returnOrRefund, amount, currency, returnType, returnReason, quantity, isOverSize);
        JSONObject createReverseResult = HsfUtil.createReverse(createReverseReq);
        result.setSuccess(true);
        if (createReverseResult.getBoolean("success")) {
            result.setData("开纠纷成功。 ReverseOrderLineId: " + createReverseResult.getJSONArray("result").getJSONObject(0).getJSONArray("reverseOrderLines").getJSONObject(0).getLong("reverseOrderLineId"));
            result.setMessage("开纠纷成功。 ReverseOrderLineId: " + createReverseResult.getJSONArray("result").getJSONObject(0).getJSONArray("reverseOrderLines").getJSONObject(0).getLong("reverseOrderLineId"));
        } else {
            result.setData("开纠纷失败。 Res: " + createReverseResult.toJSONString());
            result.setMessage("开纠纷失败。 Res: " + createReverseResult.toJSONString());
        }
        return result;
    }

    @Override
    public ResultDTO returnAndRefund3(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        String dpath = jsonObject.getString(Constant.DPATH_ENV);
        if (StringUtil.isNotBlank(dpath)) {
            EagleEye.putUserData("dpath_env", dpath);
        }
        String tradeOrderLineId = jsonObject.getString(Constant.TRADE_ORDER_LINE_ID);
        String tradeOrderId = jsonObject.getString(Constant.ORDER_ID);
        String refundChannel = jsonObject.getString(Constant.REFUND_CHANNEL);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        String returnReason = jsonObject.getString(Constant.RETURN_REASON);
        Integer quantity = jsonObject.getInteger("quantity");
        if (quantity == null || quantity == 0) {
            quantity = jsonObject.getJSONObject(Constant.SUB_ORDER_INFO).getJSONObject("module").getIntValue("quantity");
        }
        JSONObject createReverseReq = reverseService.getCreateReverseV3Req(tradeOrderLineId, tradeOrderId, refundChannel, buyerId, returnReason, quantity);
        logger.info("3.0开纠纷入参" + createReverseReq.toJSONString());
        JSONObject createReverseResult = HsfUtil.batchCreateReverseOrder(createReverseReq);
        if (createReverseResult.getBoolean("success")) {
            result.setData("3.0开纠纷成功。 ReverseOrderLineId: " + createReverseResult.getJSONArray("result").getJSONObject(0).getJSONArray("reverseOrderLines").getJSONObject(0).getLong("reverseOrderLineId"));
            result.setMessage("3.0开纠纷成功。 ReverseOrderLineId: " + createReverseResult.getJSONArray("result").getJSONObject(0).getJSONArray("reverseOrderLines").getJSONObject(0).getLong("reverseOrderLineId"));
            result.setSuccess(true);
        } else {
            result.setData("3.0开纠纷失败。 Res: " + createReverseResult.toJSONString());
            result.setMessage("3.0开纠纷失败。 Res: " + createReverseResult.toJSONString());
            result.setSuccess(false);
        }
        return result;
    }

    @Override
    public ResultDTO sellerArbitration(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long tradeOrderLineId = jsonObject.getLong(Constant.SUB_ORDER_ID);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        JSONObject reverseOrderLine = HsfUtil.getReverseOrderLineByTradeOrderLineId(buyerId, tradeOrderLineId);
        String reverseOrderLineId = reverseOrderLine.getJSONObject("result").getString("reverseOrderLineId");

        return result;
    }

    @Override
    public ResultDTO cancelDispute(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long tradeOrderLineId = jsonObject.getLong(Constant.SUB_ORDER_ID);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        JSONObject reverseOrderLine = HsfUtil.getReverseOrderLineByTradeOrderLineId(buyerId, tradeOrderLineId);
        String reverseOrderLineId = reverseOrderLine.getJSONObject("result").getString("reverseOrderLineId");
        JSONObject res = HsfUtil.cancelRequest(buyerId, reverseOrderLineId);
        result.setSuccess(true);
        if (res.getBoolean("success")) {
            result.setData("取消纠纷成功。TradeOrderLineId:" + tradeOrderLineId);
            result.setMessage("取消纠纷成功。TradeOrderLineId:" + tradeOrderLineId);
        } else {
            result.setData(res.toJSONString());
            result.setMessage(res.toJSONString());
        }
        return result;
    }

    @Override
    public ResultDTO provideSolution(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        String operator = jsonObject.getString(Constant.SOLUTION_OWNER);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        Long sellerId = jsonObject.getLong(Constant.SELLER_ID);
        Long tradeOrderLineId = jsonObject.getLong(Constant.SUB_ORDER_ID);
        String returnOrRefund = jsonObject.getString(Constant.RETURN_OR_REFUND);
        JSONObject reverseOrderLine = HsfUtil.getReverseOrderLineByTradeOrderLineId(buyerId, tradeOrderLineId);
        if (reverseOrderLine == null || reverseOrderLine.getJSONObject("result") == null
                || reverseOrderLine.getJSONObject("result").getIntValue("reverseStatus") == 3) {
            result.setData("未找到在纠纷中的订单");
            result.setMessage("未找到在纠纷中的订单");
            return result;
        }
        Long reverseOrderLineId = reverseOrderLine.getJSONObject("result").getLong("reverseOrderLineId");
        JSONObject solutionRes = HsfUtil.getReverseSolution(reverseOrderLineId);
        JSONObject solution = reverseService.getActiveSolution(solutionRes);
        if (solution != null) {
            Long solutionId = solution.getLong("solutionId");
            int solutionOwner = solution.getIntValue("solutionOwner");
            if (solutionOwner == 1 && !operator.equals("卖家")) {
                result.setData("Illegal Operator. Current operator can not refuse this solution cause last solution created by seller."
                        + "Active Solution: " + solution.toJSONString());
                result.setMessage("Illegal Operator. Current operator can not refuse this solution cause last solution created by seller."
                        + "Active Solution: " + solution.toJSONString());
                return result;
            }
            if (solutionOwner == 2 && !operator.equals("买家")) {
                result.setData("Illegal Operator. Current operator can not refuse this solution cause last solution created by buyer."
                        + "Active Solution: " + solution.toJSONString());
                result.setMessage("Illegal Operator. Current operator can not refuse this solution cause last solution created by buyer."
                        + "Active Solution: " + solution.toJSONString());
                return result;
            }
            // 买家操作 即operator == 买家
            if (solutionOwner == 2) {
                switch (returnOrRefund) {
                    case "坚持我的方案": {
                        JSONObject refuseSolutionReq = new JSONObject();
                        refuseSolutionReq.put("reverseOrderLineId", reverseOrderLineId);
                        refuseSolutionReq.put("keepLastProposal", true);
                        refuseSolutionReq.put("solutionId", solutionId);
                        refuseSolutionReq.put("comment", reverseService.getComment());
                        refuseSolutionReq.put("operatorId", buyerId);
                        JSONObject refuseSolutionResult = HsfUtil.refuseSolutionByBuyer(refuseSolutionReq);
                        if (refuseSolutionResult.getBoolean("success")) {
                            result.setData("操作成功");
                            result.setMessage("操作成功");
                        } else {
                            result.setData("操作失败。 Res: " + refuseSolutionResult.toJSONString());
                            result.setMessage("操作失败。 Res: " + refuseSolutionResult.toJSONString());
                        }
                        break;
                    }
                    case "仅退款": {
                        JSONObject refuseSolutionReq = new JSONObject();
                        refuseSolutionReq.put("solutionId", solutionId);
                        refuseSolutionReq.put("solutionType", 1);
                        refuseSolutionReq.put("operatorId", buyerId);
                        refuseSolutionReq.put("comment", reverseService.getComment());
                        refuseSolutionReq.put("refundAmount", reverseService.getRefundAmount(jsonObject.getString(Constant.RETURN_AMT), jsonObject.getString(Constant.CURRENCY)));
                        JSONObject refuseSolutionResult = HsfUtil.refuseSolutionByBuyer(refuseSolutionReq);
                        if (refuseSolutionResult.getBoolean("success")) {
                            result.setData("操作成功");
                            result.setMessage("操作成功");
                        } else {
                            result.setData("操作失败。 Res: " + refuseSolutionResult.toJSONString());
                            result.setMessage("操作失败。 Res: " + refuseSolutionResult.toJSONString());
                        }
                        break;
                    }
                    case "退货退款": {
                        JSONObject refuseSolutionReq = new JSONObject();
                        refuseSolutionReq.put("reverseOrderLineId", reverseOrderLineId);
                        refuseSolutionReq.put("comment", reverseService.getComment());
                        refuseSolutionReq.put("solutionId", solutionId);
                        refuseSolutionReq.put("solutionType", 2);
                        refuseSolutionReq.put("operatorId", buyerId);
                        refuseSolutionReq.put("refundAmount", reverseService.getRefundAmount(jsonObject.getString(Constant.RETURN_AMT), jsonObject.getString(Constant.CURRENCY)));
                        JSONObject refuseSolutionResult = HsfUtil.refuseSolutionByBuyer(refuseSolutionReq);
                        if (refuseSolutionResult.getBoolean("success")) {
                            result.setData("操作成功");
                            result.setMessage("操作成功");
                        } else {
                            result.setData("操作失败。 Res: " + refuseSolutionResult.toJSONString());
                            result.setMessage("操作失败。 Res: " + refuseSolutionResult.toJSONString());
                        }
                        break;
                    }
                }
            } else
                // 卖家操作 即operator == 卖家
                if (solutionOwner == 1) {
                    switch (returnOrRefund) {
                        case "拒绝退款": {
                            JSONObject refuseSolutionReq = new JSONObject();
                            refuseSolutionReq.put("reverseOrderLineId", reverseOrderLineId);
                            refuseSolutionReq.put("comment", "test by dataBank");
                            refuseSolutionReq.put("solutionId", solutionId);
                            refuseSolutionReq.put("operatorId", sellerId);
                            refuseSolutionReq.put("solutionType", 3);
                            refuseSolutionReq.put("currencyCode", jsonObject.getString(Constant.CURRENCY));
                            JSONObject refuseSolutionResult = HsfUtil.refuseSolutionBySeller(refuseSolutionReq);
                            if (refuseSolutionResult.getBoolean("success")) {
                                result.setData("操作成功");
                                result.setMessage("操作成功");
                            } else {
                                result.setData("操作失败。 Res: " + refuseSolutionResult.toJSONString());
                                result.setMessage("操作失败。 Res: " + refuseSolutionResult.toJSONString());
                            }
                            break;
                        }
                        case "仅退款": {
                            JSONObject refuseSolutionReq = new JSONObject();
                            refuseSolutionReq.put("reverseOrderLineId", reverseOrderLineId);
                            refuseSolutionReq.put("comment", "test by data bank");
                            refuseSolutionReq.put("solutionId", solutionId);
                            refuseSolutionReq.put("solutionType", 1);
                            refuseSolutionReq.put("currencyCode", jsonObject.getString(Constant.CURRENCY));
                            refuseSolutionReq.put("operatorId", sellerId);
                            refuseSolutionReq.put("refundAmount", jsonObject.getString(Constant.RETURN_AMT));
                            refuseSolutionReq.put("file", "[{\"uid\":\"kqw3fvk7\",\"name\":\"截屏2021-07-09 下午4.47.43.png\",\"state\":\"done\",\"id\":\"H6b080eaa4f3c4649869cf39449fe04251.png\",\"url\":\"https://ae01.alicdn.com/kf/H6b080eaa4f3c4649869cf39449fe04251.png\",\"imgURL\":\"https://ae01.alicdn.com/kf/H6b080eaa4f3c4649869cf39449fe04251.png\",\"fileURL\":\"https://ae01.alicdn.com/kf/H6b080eaa4f3c4649869cf39449fe04251.png\",\"downloadURL\":\"https://ae01.alicdn.com/kf/H6b080eaa4f3c4649869cf39449fe04251.png\",\"fileType\":\"PICTURE\"}]");
                            JSONObject refuseSolutionResult = HsfUtil.refuseSolutionBySeller(refuseSolutionReq);
                            if (refuseSolutionResult.getBoolean("success")) {
                                result.setData("操作成功");
                                result.setMessage("操作成功");
                            } else {
                                result.setData("操作失败。 Res: " + refuseSolutionResult.toJSONString());
                                result.setMessage("操作失败。 Res: " + refuseSolutionResult.toJSONString());
                            }
                            break;
                        }
                        case "退货退款": {
                            JSONObject refuseSolutionReq = new JSONObject();
                            refuseSolutionReq.put("reverseOrderLineId", reverseOrderLineId);
                            refuseSolutionReq.put("comment", "test by data bank");
                            refuseSolutionReq.put("solutionId", solutionId);
                            refuseSolutionReq.put("solutionType", 2);
                            refuseSolutionReq.put("operatorId", sellerId);
                            refuseSolutionReq.put("refundAmount", jsonObject.getString(Constant.RETURN_AMT));
                            refuseSolutionReq.put("currencyCode", jsonObject.getString(Constant.CURRENCY));
                            String addressId = HsfUtil.getReturnAddress(sellerId).getJSONObject(0).getString("id");
                            refuseSolutionReq.put("addressId", addressId);
                            JSONObject refuseSolutionResult = HsfUtil.refuseSolutionBySeller(refuseSolutionReq);
                            if (refuseSolutionResult.getBoolean("success")) {
                                result.setData("操作成功");
                                result.setMessage("操作成功");
                            } else {
                                result.setData("操作失败。 Res: " + refuseSolutionResult.toJSONString());
                                result.setMessage("操作失败。 Res: " + refuseSolutionResult.toJSONString());
                            }
                            break;
                        }
                    }
                }
        }
        result.setSuccess(true);
        return result;
    }


    /**
     * solutionStatus:
     * 1 -- wait accept
     * 2 -- reached
     * 3 -- refuse
     * 4 -- canceled
     * solutionType：
     * 1 -- refund
     * 2 -- return
     * solutionOwner:
     * 1 -- buyer
     * 2 -- seller
     */
    @Override
    public ResultDTO acceptSolution(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        Long sellerId = jsonObject.getLong(Constant.SELLER_ID);
        Long tradeOrderLineId = jsonObject.getLong(Constant.SUB_ORDER_ID);
        JSONObject reverseOrderLine = HsfUtil.getReverseOrderLineByTradeOrderLineId(buyerId, tradeOrderLineId);
        Long reverseOrderLineId = reverseOrderLine.getJSONObject("result").getLong("reverseOrderLineId");
        JSONObject solutionRes = HsfUtil.getReverseSolution(reverseOrderLineId);
        JSONObject solution = reverseService.getActiveSolution(solutionRes);
        if (solution != null) {
            int solutionOwner = solution.getIntValue("solutionOwner");
            String addressId = "";
            Long solutionId = solution.getLong("solutionId");
            if (solution.getIntValue("solutionType") == 2 && solutionOwner == 1) {
                addressId = jsonObject.getString(Constant.RETURN_ADDRESS);
                if (addressId.isEmpty()) {
                    addressId = HsfUtil.getReturnAddress(sellerId).getJSONObject(0).getString("id");
                }
            }
            // 卖家同意买家方案
            if (solutionOwner == 1) {
                JSONObject acceptResult = HsfUtil.acceptSolutionBySeller(reverseOrderLineId, solutionId, sellerId, addressId);
                if (acceptResult.getBoolean("success")) {
                    result.setData("操作成功");
                    result.setMessage("操作成功");
                } else {
                    result.setData("操作失败。 Res: " + acceptResult.toJSONString());
                    result.setMessage("操作失败。 Res: " + acceptResult.toJSONString());
                }
            } else
                // 买家同意卖家方案
                if (solutionOwner == 2) {
                    JSONObject acceptResult = HsfUtil.acceptSolutionByBuyer(reverseOrderLineId, solutionId, buyerId, addressId);
                    if (acceptResult.getBoolean("success")) {
                        result.setData("操作成功");
                        result.setMessage("操作成功");
                    } else {
                        result.setData("操作失败。 Res: " + acceptResult.toJSONString());
                        result.setMessage("操作失败。 Res: " + acceptResult.toJSONString());
                    }
                }
        }
        result.setSuccess(true);
        return result;
    }

    @Override
    public ResultDTO getReturnAddress(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long sellerId = jsonObject.getLong(Constant.SELLER_ID);
        JSONArray returnAddress = HsfUtil.getReturnAddress(sellerId);
        Map<String, QueryResultUnit> data = QueryResultBuilder.buildQueryResult("退货地址信息", null, null, returnAddress);
        result.setSuccess(true);
        result.setData(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
        result.setMessage(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
        return result;
    }

    @Override
    public ResultDTO getIssueProtect(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long buyer = jsonObject.getLong(Constant.BUYER_ID);
        Long tradeOrderLineId = jsonObject.getLong(Constant.SUB_ORDER_ID);
        List<Long> tradeOrderLineIds = new ArrayList<>();
        if (tradeOrderLineId != null && tradeOrderLineId != 0L) {
            tradeOrderLineIds.add(tradeOrderLineId);
        } else {
            JSONObject tradeOrder = jsonObject.getJSONObject(Constant.ORDER_INFO);
            JSONArray tradeOrderLines = tradeOrder.getJSONObject("module").getJSONArray("orderLines");
            for (int i = 0; i < tradeOrderLines.size(); i++) {
                JSONObject tradeOrderLine = tradeOrderLines.getJSONObject(i);
                tradeOrderLineIds.add(tradeOrderLine.getLong("tradeOrderLineId"));
            }
        }
        JSONObject issueProtect = HsfUtil.getIssueProtect(buyer, tradeOrderLineIds);
        Map<String, QueryResultUnit> data = QueryResultBuilder.buildQueryResult("纠纷保护期信息 ", null, null, issueProtect.getJSONArray("data"));
        result.setSuccess(true);
        result.setData(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
        result.setMessage(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
        return result;
    }

    @Override
    public ResultDTO buyerReturnGoods(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        Long tradeOrderLineId = jsonObject.getLong(Constant.SUB_ORDER_ID);
        String logisticCompany = jsonObject.getString(Constant.LOGISTIC_COMPANY);
        String logisticCode = jsonObject.getString(Constant.LOGISTIC_CODE);
        String logisticNum = jsonObject.getString(Constant.LOGISTIC_NUM);
        JSONObject reverseOrderLine = HsfUtil.getReverseOrderLineByTradeOrderLineId(buyerId, tradeOrderLineId);
        String reverseOrderLineId = reverseOrderLine.getJSONObject("result").getString("reverseOrderLineId");
        JSONObject res = HsfUtil.returnGoods(Long.valueOf(reverseOrderLineId), buyerId, logisticCompany, logisticCode, logisticNum);
        if (res.getBoolean("success")) {
            result.setData("买家退货成功");
            result.setMessage("买家退货成功");
        } else {
            result.setData("买家退货失败。Res: " + res.toJSONString());
            result.setMessage("买家退货失败。Res: " + res.toJSONString());
            result.setSuccess(false);
        }
        result.setSuccess(true);
        return result;
    }

    @Override
    public ResultDTO confirmReturn(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        result.setSuccess(true);
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long seller = jsonObject.getLong(Constant.SELLER_ID);
        Long tradeOrderLineId = jsonObject.getLong(Constant.SUB_ORDER_ID);

        JSONObject reverseOrderLine = HsfUtil.getReverseOrderLineByTradeOrderLineId(seller, tradeOrderLineId);
        if (reverseOrderLine == null || reverseOrderLine.getJSONObject("result") == null
                || reverseOrderLine.getJSONObject("result").getIntValue("reverseStatus") == 3) {
            result.setData("未找到在纠纷中的订单");
            result.setMessage("未找到在纠纷中的订单");
            return result;
        }
        Long reverseOrderLineId = reverseOrderLine.getJSONObject("result").getLong("reverseOrderLineId");
        JSONObject res = HsfUtil.confirmReturn(reverseOrderLineId, seller);
        if (res.getBoolean("success")) {
            result.setData("操作成功");
            result.setMessage("操作成功");
        } else {
            result.setData("操作失败。Res: " + res.toJSONString());
            result.setMessage("操作失败。Res: " + res.toJSONString());
            result.setSuccess(false);
        }

        return result;
    }

    @Override
    public ResultDTO abandonGoods(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long seller = jsonObject.getLong(Constant.SELLER_ID);
        Long tradeOrderLineId = jsonObject.getLong(Constant.SUB_ORDER_ID);

        JSONObject reverseOrderLine = HsfUtil.getReverseOrderLineByTradeOrderLineId(seller, tradeOrderLineId);
        if (reverseOrderLine == null || reverseOrderLine.getJSONObject("result") == null
                || reverseOrderLine.getJSONObject("result").getIntValue("reverseStatus") == 3) {
            result.setData("未找到在纠纷中的订单");
            result.setMessage("未找到在纠纷中的订单");
            return result;
        }
        Long reverseOrderLineId = reverseOrderLine.getJSONObject("result").getLong("reverseOrderLineId");
        JSONObject res = HsfUtil.abandonGoods(reverseOrderLineId, seller);
        if (res.getBoolean("success")) {
            result.setData("操作成功");
            result.setMessage("操作成功");
        } else {
            result.setData("操作失败。Res: " + res.toJSONString());
            result.setMessage("操作失败。Res: " + res.toJSONString());
        }
        result.setSuccess(true);
        return result;
    }

    @Override
    public ResultDTO signFreeReturn(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long seller = jsonObject.getLong(Constant.SELLER_ID);
        JSONObject userInfo = HsfUtil.getUserByUserId(seller);
        Long havanaId = userInfo.getJSONObject("module").getLong("havanaId");
        JSONObject isSigned = HsfUtil.isSignFreeReturn(havanaId);
        if (isSigned.getBoolean("module")) {
            result.setData("该卖家已签约了无忧退");
            result.setMessage("该卖家已签约了无忧退");
        } else {
            JSONObject res = HsfUtil.syncFreeReturn(havanaId, 1);
            if (res.getBoolean("success")) {
                result.setData("无忧退已签约成功");
                result.setMessage("无忧退已签约成功");
            } else {
                result.setData("操作失败。 Res: " + res.toJSONString());
                result.setMessage("操作失败。 Res: " + res.toJSONString());
            }
        }
        result.setSuccess(true);
        return result;
    }

    @Override
    public ResultDTO quitFreeReturn(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long seller = jsonObject.getLong(Constant.SELLER_ID);
        JSONObject userInfo = HsfUtil.getUserByUserId(seller);
        Long havanaId = userInfo.getJSONObject("module").getLong("havanaId");
        JSONObject isSigned = HsfUtil.isSignFreeReturn(havanaId);
        if (!isSigned.getBoolean("module")) {
            result.setData("该卖家已退出了无忧退");
            result.setMessage("该卖家已退出了无忧退");
        } else {
            JSONObject res = HsfUtil.syncFreeReturn(havanaId, 0);
            if (res.getBoolean("success")) {
                result.setData("无忧退已退出成功");
                result.setMessage("无忧退已退出成功");
            } else {
                result.setData("操作失败。 Res: " + res.toJSONString());
                result.setMessage("操作失败。 Res: " + res.toJSONString());
            }
        }
        result.setSuccess(true);
        return result;
    }

    @Override
    public ResultDTO getServiceOrder(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long tradeOrderLineId = jsonObject.getLong(Constant.SUB_ORDER_ID);
        Long promiseTemplateId = jsonObject.getLong(Constant.PROMISE_TEMPLATE_ID);

        Map<String, QueryResultUnit> data = new LinkedHashMap<>();
        JSONObject serviceOrders = HsfUtil.getServiceOrder(tradeOrderLineId, promiseTemplateId);
        if (serviceOrders != null && serviceOrders.getJSONArray("module") != null) {
            for (int i = 0; i < serviceOrders.getJSONArray("module").size(); i++) {
                JSONObject serviceInfo = serviceOrders.getJSONArray("module").getJSONObject(i);
                Long serviceWorkOrderId = serviceInfo.getLong("id");
                String bizType = serviceInfo.getString("bizCode").contains("claim") ? "报案工单" : "保险工单";
                Map<String, QueryResultUnit> serviceOrder = QueryResultBuilder.buildQueryResult("服务工单信息 ==> 工单类型: " + bizType + " == 工单id: " + serviceWorkOrderId, null, null, serviceInfo);
                data.putAll(serviceOrder);
                JSONObject serviceOrderDetailInfo = HsfUtil.getServiceOrderDetail(serviceWorkOrderId);
                if (serviceOrderDetailInfo.getBoolean("success") && serviceInfo.getString("bizCode").contains("claim")) {
                    Map<String, QueryResultUnit> serviceOrderDetail = QueryResultBuilder.buildQueryResult("服务工单详情 ==> 工单类型: " + bizType + " == 工单id: " + serviceWorkOrderId, null, null, serviceOrderDetailInfo.getJSONObject("module").getJSONArray("insuranceCaseDTOList"));
                    data.putAll(serviceOrderDetail);
                } else {
                    Map<String, QueryResultUnit> serviceOrderDetail = QueryResultBuilder.buildQueryResult("服务工单详情 ==> 工单类型: " + bizType + " == 工单id: " + serviceWorkOrderId, null, null, serviceOrderDetailInfo.getJSONObject("module"));
                    data.putAll(serviceOrderDetail);
                }
            }
        } else {
            Map<String, QueryResultUnit> errorInfo = QueryResultBuilder.buildQueryResult("错误信息", null, null, serviceOrders);
            data.putAll(errorInfo);
        }
        result.setData(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
        result.setSuccess(true);
        return result;
    }

    @Override
    public ResultDTO placeReverseOrder(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        Long sellerId = jsonObject.getLong(Constant.SELLER_ID);
        String tradeOrderLineId = jsonObject.getString(Constant.SUB_ORDER_ID);
        String scenario = jsonObject.getString(Constant.ORDER_SCENARIO);
        String tradeOrderId = jsonObject.getString(Constant.ORDER_ID);
        Long reverseOrderLineId = reverseService.placeReverseOrderByScenario(scenario, buyerId, sellerId, tradeOrderId,
                tradeOrderLineId, jsonObject.getJSONObject(Constant.SUB_ORDER_INFO));
        result.setSuccess(true);
        result.setData("操纵成功。 ReverseOrderLineId: " + reverseOrderLineId);
        result.setMessage("操纵成功。 ReverseOrderLineId: " + reverseOrderLineId);
        return result;
    }

    @Override
    public ResultDTO isSignCjFreeReturn(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long buyerId = jsonObject.getLong(Constant.SELLER_ID);
        Long havanaId = jsonObject.getLong(Constant.HAVANA_ID);
        if (havanaId == null || havanaId == 0L) {
            JSONObject userInfo = HsfUtil.getUserByUserId(buyerId);
            if (!userInfo.getBoolean("success") || userInfo.getJSONObject("module") == null) {
                result.setData("Fail to get userInfo. Res: " + userInfo.toJSONString());
                result.setMessage("Fail to get userInfo. Res: " + userInfo.toJSONString());
            }
            havanaId = userInfo.getJSONObject("module").getLong("havanaId");
        }
        JSONObject isSigned = HsfUtil.isSignFreeReturn(havanaId);
        if (isSigned.getBoolean("module")) {
            result.setData("用户签约状态已同步");
            result.setMessage("用户签约状态已同步");
        } else {
            result.setData("AE侧用户签约状态：未签约");
            result.setMessage("AE侧用户签约状态：未签约");
        }
        result.setSuccess(true);
        return result;
    }

    @Override
    public ResultDTO getOfficialOverseaWarehouseMailNum(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long subOrderId = jsonObject.getLong(Constant.SUB_ORDER_ID);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        JSONObject lastReverseOrderByTradeOrderLineId = HsfUtil.getLastReverseOrderByTradeOrderLineId(buyerId, subOrderId);
        JSONObject lastReverseOrder = lastReverseOrderByTradeOrderLineId.getJSONObject("result");
        Long reverseOrderLineId = lastReverseOrder.getLong("reverseOrderLineId");
        JSONObject request = JSONObject.parseObject(Constant.MAIL_NUM_PARAM);
        JSONArray fulfillmentOrderItemStatusUpdatedList = request.getJSONArray("fulfillmentOrderItemStatusUpdatedList");
        fulfillmentOrderItemStatusUpdatedList.getJSONObject(0).put("reverseTradeOrderItemId", reverseOrderLineId);
        SendResult result = mqConfig.sendMessage(Constant.REVERSE_FULFILLMENT_TOPIC, Constant.REVERSE_FULFILLMENT_TAG, reverseOrderLineId.toString(), JSONObject.toJSONString(request));
        resultDTO.setSuccess(true);
        resultDTO.setData(JSONObject.toJSONString(result));
        resultDTO.setMessage(JSONObject.toJSONString(result));
        return resultDTO;
    }

    @Override
    public ResultDTO getOfficialOverseaWarehouseAScan(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long subOrderId = jsonObject.getLong(Constant.SUB_ORDER_ID);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        JSONObject lastReverseOrderByTradeOrderLineId = HsfUtil.getLastReverseOrderByTradeOrderLineId(buyerId, subOrderId);
        JSONObject lastReverseOrder = lastReverseOrderByTradeOrderLineId.getJSONObject("result");
        Long reverseOrderLineId = lastReverseOrder.getLong("reverseOrderLineId");
        JSONObject request = JSONObject.parseObject(Constant.A_SCAN_PARAM);
        JSONArray fulfillmentOrderItemStatusUpdatedList = request.getJSONArray("fulfillmentOrderItemStatusUpdatedList");
        fulfillmentOrderItemStatusUpdatedList.getJSONObject(0).put("reverseTradeOrderItemId", reverseOrderLineId);
        SendResult result = mqConfig.sendMessage(Constant.REVERSE_FULFILLMENT_TOPIC, Constant.REVERSE_FULFILLMENT_TAG, reverseOrderLineId.toString(), JSONObject.toJSONString(request));
        resultDTO.setSuccess(true);
        resultDTO.setData(JSONObject.toJSONString(result));
        resultDTO.setMessage(JSONObject.toJSONString(result));
        return resultDTO;

    }

    @Override
    public ResultDTO getOfficialOverseaWarehouseDScan(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long subOrderId = jsonObject.getLong(Constant.SUB_ORDER_ID);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        JSONObject lastReverseOrderByTradeOrderLineId = HsfUtil.getLastReverseOrderByTradeOrderLineId(buyerId, subOrderId);
        JSONObject lastReverseOrder = lastReverseOrderByTradeOrderLineId.getJSONObject("result");
        Long reverseOrderLineId = lastReverseOrder.getLong("reverseOrderLineId");
        JSONObject request = JSONObject.parseObject(Constant.D_SCAN_PARAM);
        JSONArray fulfillmentOrderItemStatusUpdatedList = request.getJSONArray("fulfillmentOrderItemStatusUpdatedList");
        fulfillmentOrderItemStatusUpdatedList.getJSONObject(0).put("reverseTradeOrderItemId", reverseOrderLineId);
        SendResult result = mqConfig.sendMessage(Constant.REVERSE_FULFILLMENT_TOPIC, Constant.REVERSE_FULFILLMENT_TAG, reverseOrderLineId.toString(), JSONObject.toJSONString(request));
        resultDTO.setSuccess(true);
        resultDTO.setData(JSONObject.toJSONString(result));
        resultDTO.setMessage(JSONObject.toJSONString(result));
        return resultDTO;
    }

    @Override
    public ResultDTO getReverseFulfillmentOrder(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long orderId = jsonObject.getLong(Constant.ORDER_ID);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        LandlordContext.setTenantSpec("AE_GLOBAL", -1);
        Map<String, QueryResultUnit> data = new LinkedHashMap<>();
        Response<List<FulfillmentOrderDTO>> fulfillmentReverseOrder = fulfillmentOrderQueryFacade.queryReverseOrderByTradeId(buyerId, orderId, null);
        if (fulfillmentReverseOrder.getModule().get(0) != null) {
            Map<String, QueryResultUnit> fulfillmentReverseOrderData = QueryResultBuilder.buildQueryResult("逆向履约记录", null, null, fulfillmentReverseOrder.getModule().get(0));
            data.putAll(fulfillmentReverseOrderData);
        }
        resultDTO.setData(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
        resultDTO.setMessage(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
        resultDTO.setSuccess(true);
        return resultDTO;
    }

    @Override
    public ResultDTO mockCjClaimApplyResult(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long tradeOrderLineId = jsonObject.getLong(Constant.SUB_ORDER_ID);
        Long workOrderId = jsonObject.getLong(Constant.WORK_ORDER_ID);
        String applyResult = jsonObject.getString(Constant.APPLY_RESULT);
        if (workOrderId == null || workOrderId == 0L) {
            JSONObject serviceOrders = HsfUtil.getServiceOrder(tradeOrderLineId, 17L);
            if (serviceOrders == null || serviceOrders.getJSONArray("module") == null) {
                resultDTO.setSuccess(true);
                resultDTO.setMessage("未找到服务工单");
                resultDTO.setData("未找到服务工单");
                return resultDTO;
            }
            JSONArray modules = serviceOrders.getJSONArray("module");
            workOrderId = getActiveClaimWorkOrderId(modules);
        }
        JSONObject messageBody = getMessageBody(applyResult);
        messageBody.put("echo", workOrderId);
        messageBody.put("tradeOrderLineId", tradeOrderLineId);
        messageBody.put("serviceProductId", 17);
        SendResult result = mqConfig.sendMessage(Constant.CJJR_TOPIC, "报案拒绝".equals(applyResult) ?
                        Constant.CLAIM_APPLY_REJECT_TAG : Constant.CLAIM_APPLY_ACCEPT_TAG
                , workOrderId.toString(), JSONObject.toJSONString(messageBody));
        resultDTO.setSuccess(true);
        resultDTO.setData(JSONObject.toJSONString(result));
        resultDTO.setMessage(JSONObject.toJSONString(result));
        return resultDTO;
    }

    private JSONObject getMessageBody(String applyResult) {
        switch (applyResult) {
            case "报案成功":
                return JSONObject.parseObject(Constant.CLAIM_APPLY_ACCEPT_MSG_BODY);
            case "报案拒绝":
                return JSONObject.parseObject(Constant.CLAIM_APPLY_REJECT_MSG_BODY);
            case "仅退款":
                return JSONObject.parseObject(Constant.CLAIM_APPLY_ONLY_REFUND);
            default:
                return null;
        }
    }

    @Override
    public ResultDTO mockCjClaimResult(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long tradeOrderLineId = jsonObject.getLong(Constant.SUB_ORDER_ID);
        Long workOrderId = jsonObject.getLong(Constant.WORK_ORDER_ID);
        String resultType = jsonObject.getString(Constant.CLAIM_RESULT);
        if (workOrderId == null || workOrderId == 0L) {
            JSONObject serviceOrders = HsfUtil.getServiceOrder(tradeOrderLineId, 17L);
            if (serviceOrders == null || serviceOrders.getJSONArray("module") == null) {
                resultDTO.setSuccess(true);
                resultDTO.setMessage("未找到服务工单");
                resultDTO.setData("未找到服务工单");
                return resultDTO;
            }
            JSONArray modules = serviceOrders.getJSONArray("module");
            workOrderId = getActiveClaimWorkOrderId(modules);
        }
        JSONObject messageBody = "同意理赔".equals(resultType) ? JSONObject.parseObject(Constant.CLAIM_RESULT_ACCEPT_MSG_BODY)
                : JSONObject.parseObject(Constant.CLAIM_RESULT_REJECT_MSG_BODY);
        messageBody.put("echo", workOrderId);
        SendResult result = mqConfig.sendMessage(Constant.CJJR_TOPIC, Constant.CLAIM_APPLY_RESULT_TAG, workOrderId.toString(), messageBody.toJSONString());
        resultDTO.setSuccess(true);
        resultDTO.setData(JSONObject.toJSONString(result));
        resultDTO.setMessage(JSONObject.toJSONString(result));
        return resultDTO;
    }

    @Override
    public ResultDTO getChargeBackRecordAndMessage(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long tradeOrderId = jsonObject.getLong(Constant.ORDER_ID);
        LandlordContext.setTenantSpec("AE_GLOBAL", -1);
        Map<String, QueryResultUnit> data = new LinkedHashMap<>();
        JSONObject chargebackRecords = HsfUtil.getChargebackRecords(tradeOrderId);
        JSONObject chargebackMessages = HsfUtil.getChargebackMessages(tradeOrderId);
        if (chargebackRecords != null && chargebackRecords.getJSONArray("result") != null) {
            Map<String, QueryResultUnit> chargebackRecordsData = QueryResultBuilder.buildQueryResult("拒付记录信息", null, null, chargebackRecords.getJSONArray("result"));
            data.putAll(chargebackRecordsData);
        }
        if (chargebackMessages != null && chargebackMessages.getJSONArray("result") != null) {
            Map<String, QueryResultUnit> chargebackMessagesData = QueryResultBuilder.buildQueryResult("拒付消息信息", null, null, chargebackMessages.getJSONArray("result"));
            data.putAll(chargebackMessagesData);
        }
        result.setData(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
        result.setMessage(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
        result.setSuccess(true);
        return result;

    }

    @Override
    public ResultDTO queryNrInterceptor(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        Map<String, QueryResultUnit> data = new LinkedHashMap<>();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long tradeOrderLineId = jsonObject.getLong(Constant.SUB_ORDER_ID);
        Long tradeOrderId = jsonObject.getLong(Constant.ORDER_ID);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        JSONObject nrCodeRes = HsfUtil.queryNrCode(buyerId, tradeOrderId, tradeOrderLineId);
        JSONObject packageInfo = HsfUtil.queryPackage(buyerId, String.valueOf(tradeOrderId), String.valueOf(tradeOrderLineId));

        if (nrCodeRes.getBoolean("success") && nrCodeRes.getJSONObject("module") != null) {
            Map<String, QueryResultUnit> module = QueryResultBuilder.buildQueryResult("NR Code Res", null, null, nrCodeRes.getJSONObject("module"));
            data.putAll(module);
        }
        JSONArray packages = packageInfo.getJSONArray("module");
        if (packageInfo.getBoolean("success") && packages.size() > 0) {
            Map<String, QueryResultUnit> firstPackage = QueryResultBuilder.buildQueryResult("Package Info: First Package", null, null, packages.get(0));
            data.putAll(firstPackage);
            JSONObject trackingInfoReq = getTrackingInfoReq(packages.getJSONObject(0));
            JSONObject trackingInfo = HsfUtil.queryTrackingInfo(trackingInfoReq);
            if (trackingInfo.getBoolean("success")) {
                Map<String, QueryResultUnit> firstPackageTracking = QueryResultBuilder.buildQueryResult("Tracking Info: First Package", null, null, trackingInfo.getJSONObject("data"));
                data.putAll(firstPackageTracking);
            }

            if (packages.size() > 1) {
                for (int i = 1; i < packages.size(); i++) {
                    Map<String, QueryResultUnit> packageItemInfo = QueryResultBuilder.buildQueryResult("Package Info: " + (i + 1) + " Package", null, null, packageInfo.get(String.valueOf(i)));
                    data.putAll(packageItemInfo);
                    JSONObject trackingInfoReqItem = getTrackingInfoReq(packages.getJSONObject(i));
                    JSONObject trackingInfoItem = HsfUtil.queryTrackingInfo(trackingInfoReqItem);
                    Map<String, QueryResultUnit> itemTracking = QueryResultBuilder.buildQueryResult("Tracking Info: " + (i + 1) + " Package", null, null, trackingInfoItem.getJSONObject("data"));
                    data.putAll(itemTracking);
                }
            }
        }

        result.setData(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
        result.setSuccess(true);
        return result;
    }

    @Override
    public ResultDTO reverseFlowValidate(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        Map<String, QueryResultUnit> data = new LinkedHashMap<>();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long tradeOrderLineId = jsonObject.getLong(Constant.SUB_ORDER_ID);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        JSONObject reverseOrderLines = HsfUtil.getLastReverseOrderByTradeOrderLineId(buyerId, tradeOrderLineId);
        JSONObject lastReverseOrder = reverseOrderLines.getJSONObject("result");
        Long reverseOrderLineId = lastReverseOrder.getLong("reverseOrderLineId");
        JSONObject flowInfo = HsfUtil.analyseReverseProduct(reverseOrderLineId).getJSONObject("result");
        JSONObject productType = new JSONObject();
        productType.put("Reverse Product Type", flowInfo.getString("reverseProductType"));
        Map<String, QueryResultUnit> reverseProductType = QueryResultBuilder.buildQueryResult("Reverse Product Type", null, null, productType);
        data.putAll(reverseProductType);
        JSONObject flowType = new JSONObject();
        flowType.put("Reverse Flow Type", flowInfo.getString("reverseFlowType"));
        Map<String, QueryResultUnit> reverseFlowType = QueryResultBuilder.buildQueryResult("Reverse Flow Type", null, null, flowType);
        data.putAll(reverseFlowType);
        JSONObject flowName = new JSONObject();
        flowName.put("Reverse Flow Name", flowInfo.getString("reverseFlowName"));
        Map<String, QueryResultUnit> reverseFlowName = QueryResultBuilder.buildQueryResult("Reverse Flow Name", null, null, flowName);
        data.putAll(reverseFlowName);
        JSONObject record = new JSONObject();
        Map<String, String> recordMap = new Gson().fromJson(flowInfo.getString("ruleValidateRecord"), new TypeToken<Map<String, String>>() {
        }.getType());
        recordMap.keySet().forEach(it -> record.put(it, recordMap.get(it)));
        Map<String, QueryResultUnit> reverseRecord = QueryResultBuilder.buildQueryResult("Reverse Record", null, null, record);
        data.putAll(reverseRecord);
        result.setSuccess(true);
        result.setData(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
        return result;
    }

    @Override
    public ResultDTO reverseLifeCycle(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long tradeOrderId = jsonObject.getLong(Constant.ORDER_ID);
        Long tradeOrderLineId = jsonObject.getLong(Constant.SUB_ORDER_ID);
        Map<String, QueryResultUnit> data = new LinkedHashMap<>();
        JSONObject res = HsfUtil.analyseReverseOrder(tradeOrderId, tradeOrderLineId);
        if (res.getBoolean("success")) {
            JSONArray jsonArray = res.getJSONArray("result");
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject reverseInfo = jsonArray.getJSONObject(i);
                Map<String, QueryResultUnit> map = QueryResultBuilder.buildQueryResult("逆向子单信息: 逆向子单id: " + reverseInfo.getLong("reverseOrderLineId") + "订单id：" + tradeOrderId + "子单id：" + reverseInfo.getLong("tradeOrderLineId"), null, null, reverseInfo);
                data.putAll(map);
            }
        }

        JSONObject cancelOrderRes = HsfUtil.analyseCancelOrderReverse(tradeOrderId);
        if (cancelOrderRes.getBoolean("success")) {
            JSONObject object = cancelOrderRes.getJSONObject("result");
            Map<String, QueryResultUnit> map = QueryResultBuilder.buildQueryResult("取消订单情况：", null, null, object);
            data.putAll(map);
        }

        result.setSuccess(true);
        result.setMessage(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
        result.setData(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
        return result;
    }

    @Override
    public ResultDTO mockCjReturnGoodsMessage(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long tradeOrderLineId = jsonObject.getLong(Constant.SUB_ORDER_ID);
        String workOrderId = jsonObject.getString(Constant.WORK_ORDER_ID);
        String claimReportId = jsonObject.getString(Constant.CLAIM_REPORT_NO);
        if (StringUtils.isBlank(workOrderId)) {
            JSONObject serviceOrders = HsfUtil.getServiceOrder(tradeOrderLineId, 17L);
            if (serviceOrders == null || serviceOrders.getJSONArray("module") == null) {
                resultDTO.setSuccess(true);
                resultDTO.setMessage("未找到服务工单");
                resultDTO.setData("未找到服务工单");
                return resultDTO;
            }
            JSONObject req = getReturnWorkOrderId(serviceOrders.getJSONArray("module"));
            workOrderId = req.getString(Constant.WORK_ORDER_ID);
            claimReportId = req.getString(Constant.CLAIM_REPORT_NO);
        }
        JSONObject messageBody = JSONObject.parseObject(Constant.LOGISTICS_RETURN_MSG_BODY);
        messageBody.put("claimReportNo", claimReportId);
        messageBody.put("echo", workOrderId);
        SendResult result = mqConfig.sendMessage(Constant.CJJR_TOPIC, Constant.LOGISTICS_RETURN_TAG, workOrderId, messageBody.toJSONString());
        resultDTO.setSuccess(true);
        resultDTO.setData(JSONObject.toJSONString(result));
        resultDTO.setMessage(JSONObject.toJSONString(result));
        return resultDTO;
    }

    @Override
    public ResultDTO mockCcoJudgement(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        Long sellerId = jsonObject.getLong(Constant.SELLER_ID);
        Long tradeOrderLineId = jsonObject.getLong(Constant.SUB_ORDER_ID);
        String judgeResult = getJudgementResult(jsonObject.getString(Constant.JUDGE_RESULT));
        String refundAmt = jsonObject.getString(Constant.RETURN_AMT);
        Boolean fake = jsonObject.getBoolean(Constant.FAKE_ITEM);
        Boolean arbitrationToSelfDrop = jsonObject.getBoolean(Constant.ARBITRATION_TO_SELFDROP);
        String responsibleParty = getResponsibleParty(jsonObject.getString(Constant.RESPONSIBLE_PARTY));
        JSONObject reverseOrderLines = HsfUtil.getLastReverseOrderByTradeOrderLineId(buyerId, tradeOrderLineId);
        JSONObject lastReverseOrder = reverseOrderLines.getJSONObject("result");
        if (StringUtils.isBlank(refundAmt)) {
            refundAmt = lastReverseOrder.getJSONObject("fundOrderDTO").getJSONObject("applyRefundAmt").getString("cent");
        }
        String currencyCode = lastReverseOrder.getJSONObject("fundOrderDTO").getJSONObject("applyRefundAmt").getString("currencyCode");
        Long reverseOrderLineId = lastReverseOrder.getLong("reverseOrderLineId");
        String arbitrationId = getCaseIdByReverseOrderLineId1(reverseOrderLineId);
        if (arbitrationId.isEmpty()) {
            resultDTO.setSuccess(true);
            resultDTO.setData("未找到仲裁id。逆向子单id：" + reverseOrderLineId);
            resultDTO.setMessage("未找到仲裁id。逆向子单id：" + reverseOrderLineId);
            return resultDTO;
        }
        String dpath = jsonObject.getString(Constant.DPATH_ENV);
        if (StringUtil.isNotBlank(dpath)) {
            EagleEye.putUserData("dpath_env", dpath);
        }
        // 泛化调用莫名报错了奇奇怪怪
//        JSONObject result = HsfUtil.updateArbitration(buyerId, sellerId, arbitrationId, judgeResult, reverseOrderLineId,
//                responsibleParty, Long.valueOf(refundAmt), 2, currencyCode, fake,arbitrationToSelfDrop);
        UpdateReverseArbitrationRequest updateReverseArbitrationRequest = getUpdateReverseArbitrationRequest(buyerId, sellerId, arbitrationId, judgeResult, reverseOrderLineId,
                responsibleParty, Long.valueOf(refundAmt), 2, currencyCode, fake, arbitrationToSelfDrop);
        com.alibaba.global.protocol.common.model.response.Response<Boolean> result = reverseArbitrationFacade.updateArbitration(updateReverseArbitrationRequest);
        resultDTO.setSuccess(true);
        resultDTO.setData("执行结果：" + JSONObject.toJSONString(result));
        resultDTO.setMessage("执行结果：" + JSONObject.toJSONString(result));

        return resultDTO;
    }

    private UpdateReverseArbitrationRequest getUpdateReverseArbitrationRequest(Long buyerId, Long sellerId,
                                                                               String arbitrationId, String judgeType,
                                                                               Long reverseOrderLineId, String sponsor,
                                                                               Long amount, int status, String currencyCode,
                                                                               Boolean fake, Boolean arbitrationToSelfDrop) {
        JSONObject request = new JSONObject();
        JSONObject invokeInfo = new JSONObject();
        invokeInfo.put("appName", "xcommerce-icbu-i18n");
        request.put("invokeInfo", invokeInfo);

        JSONObject routingInfo = new JSONObject();
        routingInfo.put("buyerId", buyerId);
        routingInfo.put("sellerId", sellerId);
        request.put("routingInfo", routingInfo);

        JSONObject operatorDTO = new JSONObject();
        operatorDTO.put("operatorType", "CUSTOMER_SERVICE");
        operatorDTO.put("operatorId", 0);
        request.put("operatorDTO", operatorDTO);

        JSONObject extendParam = new JSONObject();
        JSONObject extensionDTOMap = new JSONObject();
        extendParam.put("extensionDTOMap", extensionDTOMap);
        request.put("extendParam", extendParam);

        JSONArray lineReqs = new JSONArray();
        JSONObject lineReq = new JSONObject();
        JSONObject features = new JSONObject();
        JSONObject featureMap = new JSONObject();
        featureMap.put("responsibleParty", sponsor);
        if (fake) {
            featureMap.put("caseSubCategory", "fake_item@3rdIssueReason");
        }
        if (arbitrationToSelfDrop) {
            featureMap.put("arbitrationToSelfDrop", "1");
        }
        features.put("featureMap", featureMap);
        features.put("empty", false);

        lineReq.put("features", features);
        lineReq.put("arbitrationId", arbitrationId);
        lineReq.put("reverseOrderLineId", reverseOrderLineId);
        lineReq.put("targetStatus", String.valueOf(status));

        JSONObject arbitrationResult = new JSONObject();
        arbitrationResult.put("idempotentKey", String.valueOf(reverseOrderLineId));
        if (amount != 0L) {
            arbitrationResult.put("type", judgeType);
            JSONArray sponsors = new JSONArray();
            JSONObject sponsorJsonObject = new JSONObject();
            sponsorJsonObject.put("sponsorType", "SELLER");
            JSONObject amt = new JSONObject();
            amt.put("amount", amount);
            amt.put("currencyCode", currencyCode);
            sponsorJsonObject.put("amt", amt);
            sponsors.add(sponsorJsonObject);
            arbitrationResult.put("sponsors", sponsors);
            JSONObject refundAmt = new JSONObject();
            refundAmt.put("amount", amount);
            refundAmt.put("currencyCode", currencyCode);
            arbitrationResult.put("refundAmt", refundAmt);
        } else {
            arbitrationResult.put("type", "CLOSE_REVERSE");
        }
        lineReq.put("arbitrationResult", arbitrationResult);
        lineReqs.add(lineReq);
        request.put("lineReqs", lineReqs);
        log.info("request: " + request.toJSONString());
        return request.toJavaObject(UpdateReverseArbitrationRequest.class);
    }

    @Override
    public ResultDTO mockCcoJudgement1(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        Long sellerId = jsonObject.getLong(Constant.SELLER_ID);
        Long tradeOrderLineId = jsonObject.getLong(Constant.SUB_ORDER_ID);
        String refundAmt = jsonObject.getString(Constant.RETURN_AMT);
        Boolean fake = jsonObject.getBoolean(Constant.FAKE_ITEM);
        Boolean arbitrationToSelfDrop = jsonObject.getBoolean(Constant.ARBITRATION_TO_SELFDROP);
        /*String fastArbitrationResult = jsonObject.getString(Constant.FAST_ARBITRATION_RESULT);
        String processType = jsonObject.getString(Constant.PROCESS_TYPE);*/
        String responsibleParty = getResponsibleParty(jsonObject.getString(Constant.RESPONSIBLE_PARTY));
        com.alibaba.global.protocol.common.model.response.Response<AeReverseOrderLineDTO> reverseOrderLineDTOResponse = aeReverseOrderQueryFacade.queryLastReverseOrderLineByTradeOrderLineId(buyerId, tradeOrderLineId);
        AeReverseOrderLineDTO lastReverseOrder = reverseOrderLineDTOResponse.getResult();
        if (StringUtils.isBlank(refundAmt)) {
            refundAmt = String.valueOf(lastReverseOrder.getFundOrderDTO().getApplyRefundAmt().getCent());
        }
        String currencyCode = lastReverseOrder.getFundOrderDTO().getApplyRefundAmt().getCurrencyCode();
        Long reverseOrderLineId = lastReverseOrder.getReverseOrderLineId();
        String arbitrationId = getCaseIdByReverseOrderLineId1(reverseOrderLineId);
        if (arbitrationId.isEmpty()) {
            resultDTO.setSuccess(true);
            resultDTO.setData("未找到仲裁id。逆向子单id：" + reverseOrderLineId);
            resultDTO.setMessage("未找到仲裁id。逆向子单id：" + reverseOrderLineId);
            return resultDTO;
        }
        String dpath = jsonObject.getString(Constant.DPATH_ENV);
        if (StringUtil.isNotBlank(dpath)) {
            EagleEye.putUserData("dpath_env", dpath);
        }

        //仲裁入参构造
        UpdateReverseArbitrationRequest updateReverseArbitrationRequest = new UpdateReverseArbitrationRequest();
        UpdateReverseArbitrationLineReqDTO updateReverseArbitrationLineReqDTO = new UpdateReverseArbitrationLineReqDTO();
        updateReverseArbitrationLineReqDTO.setReverseOrderLineId(reverseOrderLineId);
        updateReverseArbitrationLineReqDTO.setArbitrationId(arbitrationId);
        updateReverseArbitrationLineReqDTO.setTargetStatus("2");
        Map<String, String> featureMap = new HashMap<>();
        featureMap.put("responsibleParty", responsibleParty);
        if (jsonObject.getBoolean(IS_FAST_ARBITRATION)) {
            featureMap.put("fastArbitrationResult", "Fast_check_Hard_to_define_product_issue_from_proof");
            featureMap.put("processType", "local_return_pic_audit");
        }
        if (fake) {
            featureMap.put("caseSubCategory", "fake_item@3rdIssueReason");
        }
        if (arbitrationToSelfDrop) {
            featureMap.put("arbitrationToSelfDrop", "1");
        }
        Features features = Features.of(featureMap);
        features.addFeature("empty", "false");

        updateReverseArbitrationLineReqDTO.setFeatures(features);

        ReverseArbitrationResultDTO arbitrationResult = new ReverseArbitrationResultDTO();
        arbitrationResult.setIdempotentKey(String.valueOf(reverseOrderLineId));
        ArbitrationJudgementType type = getJudgementType(jsonObject.getString(Constant.JUDGE_RESULT), Long.parseLong(refundAmt));
        arbitrationResult.setType(type);
        MonetaryAmount refundTotalMoney = MonetaryUtils.of(Long.parseLong(refundAmt), currencyCode);
        MoneyDTO moneyDTO = MoneyDTO.of(refundTotalMoney);
        arbitrationResult.setRefundAmt(moneyDTO);
        List<SponsorDTO> sponsors = new ArrayList<>();
        SponsorDTO sponsorDTO = new SponsorDTO();
        sponsorDTO.setAmt(moneyDTO);
        sponsorDTO.setSponsorType("SELLER");
        sponsors.add(sponsorDTO);
        arbitrationResult.setSponsors(sponsors);

        updateReverseArbitrationLineReqDTO.setArbitrationResult(arbitrationResult);

        List<UpdateReverseArbitrationLineReqDTO> updateReverseArbitrationLineReqDTOS = new ArrayList<>();
        updateReverseArbitrationLineReqDTOS.add(updateReverseArbitrationLineReqDTO);
        updateReverseArbitrationRequest.setLineReqs(updateReverseArbitrationLineReqDTOS);

        OperatorDTO operatorDTO = new OperatorDTO();
        operatorDTO.setOperatorId(0L);
        operatorDTO.setOperatorType("CUSTOMER_SERVICE");
        updateReverseArbitrationRequest.setOperatorDTO(operatorDTO);

        ExtendParam extendParam = new ExtendParam();
        updateReverseArbitrationRequest.setExtendParam(extendParam);

        InvokeInfoDTO invokeInfo = new InvokeInfoDTO();
        invokeInfo.setAppName("xcommerce-icbu-i18n");
        updateReverseArbitrationRequest.setInvokeInfo(invokeInfo);

        RoutingInfoDTO routingInfo = new RoutingInfoDTO();
        routingInfo.setBuyerId(buyerId);
        routingInfo.setSellerId(sellerId);
        updateReverseArbitrationRequest.setRoutingInfo(routingInfo);

        com.alibaba.global.protocol.common.model.response.Response<Boolean> booleanResponse = reverseArbitrationFacade.updateArbitration(updateReverseArbitrationRequest);

        if (booleanResponse.isSuccess()) {
            // mock仲裁成功后 取消仲裁单
            CancelDisputeParam param = new CancelDisputeParam();
            Source source = Source.of(reverseOrderLineId,
                    arbitrationId,
                    SourceEnum.AE_REFUND,
                    SourceEnum.AE_BIZ_ORDER);
            source.setChannel("ae_dispute");
            param.setSource(source);
            param.setOperator(User.buildSystemUser());
            param.setForceType(ForceTypeEnum.SYSTEM_CANCEL);
            Result<Void> result = disputeWriteService.cancel(param);
            if (null == result || result.isFailed()) {
                logger.info("仲裁单取消失败，子单id：" + tradeOrderLineId);
            }
        }


        resultDTO.setSuccess(booleanResponse.isSuccess());
        resultDTO.setData((booleanResponse.isSuccess() ? "执行成功: " : "执行失败: ") + JSONObject.toJSONString(booleanResponse.getResult()));
        resultDTO.setMessage((booleanResponse.isSuccess() ? "执行成功: " : "执行失败: ") + JSONObject.toJSONString(booleanResponse.getResult()));
        return resultDTO;
    }

    private String getCaseIdByReverseOrderLineId1(Long reverseOrderLineId) throws Exception {
        com.alibaba.global.protocol.common.model.response.Response<ConsoleReverseOrderLineInfoDO> consoleReverseOrderLineInfoDOResponse = aeReverseOrderQueryForConsoleFacade.queryReverseOrderLineDOByReverseOrderLineId(reverseOrderLineId);
        ConsoleReverseOrderLineInfoDO result = consoleReverseOrderLineInfoDOResponse.getResult();
        if (result != null && result.getReverseOrderLineConsoleDO() != null) {
            String disputeFeatures = result.getReverseOrderLineConsoleDO().getDisputeFeatures();
            logger.info("disputeFeatures: " + disputeFeatures);

            if (StringUtils.isBlank(disputeFeatures) || "null".equals(disputeFeatures)) {
                return "";
            }
            // 去掉最外层的引号
            disputeFeatures = disputeFeatures.substring(1, disputeFeatures.length() - 1);
            disputeFeatures = disputeFeatures.replace("\\\"", "\"").replace("\\\\", "\\");

            JSONArray jsonArray = JSON.parseArray(disputeFeatures);
            jsonArray.sort(Comparator.comparing(o -> ((JSONObject) o).getString("gmtCreate")).reversed());
            return jsonArray.getJSONObject(0).getString("outerId");
        } else {
            return "";
        }
    }

    private String getCaseIdByReverseOrderLineId(Long reverseOrderLineId) throws Exception {
        JSONObject reverseOrderLine = HsfUtil.getReverseInfoByReverseOrderLineId(reverseOrderLineId);
        JSONObject arbitrationInfo = reverseOrderLine.getJSONObject("result").getJSONObject("reverseOrderLine").getJSONObject("reverseOrderLine").getJSONObject("newestArbitration");
        if (arbitrationInfo != null) {
            return arbitrationInfo.getString("outerId");
        }
        return "";
    }

    private String getResponsibleParty(String sponsor) {
        switch (sponsor) {
            case "商家":
                return "seller";
            case "买家":
                return "buyer";
            case "平台":
                return "platform";
            case "物流商":
                return "logistics";
        }
        return "";
    }

    private String getJudgementResult(String judgement) {
        switch (judgement) {
            case "仅退款":
                return "AGREE_TO_REFUND";
            case "退货退款":
                return "AGREE_TO_RETURN";
        }
        return "";
    }


    private ArbitrationJudgementType getJudgementType(String judgement, Long amount) {
        if (amount == 0L) {
            return ArbitrationJudgementType.CLOSE_REVERSE;
        }
        switch (judgement) {
            case "仅退款":
                return ArbitrationJudgementType.AGREE_TO_REFUND;
            case "退货退款":
                return ArbitrationJudgementType.AGREE_TO_RETURN;
        }
        return null;
    }


    private JSONObject getReturnWorkOrderId(JSONArray modules) throws Exception {
        JSONObject jsonObject = new JSONObject();
        for (int i = 0; i < modules.size(); i++) {
            JSONObject workOrder = modules.getJSONObject(i);
            if ("ali.global.insurance.claim".equals(workOrder.getString("bizCode"))) {
                if (workOrder.getIntValue("status") == 10) {
                    JSONObject serviceOrderDetailInfo = HsfUtil.getServiceOrderDetail(workOrder.getLong("id"));
                    if (serviceOrderDetailInfo != null && serviceOrderDetailInfo.getBoolean("success") && serviceOrderDetailInfo.getJSONObject("module") != null) {
                        JSONArray serviceOrders = serviceOrderDetailInfo.getJSONObject("module").getJSONArray("insuranceCaseDTOList");
                        for (int j = 0; j < serviceOrders.size(); j++) {
                            if (serviceOrders.getJSONObject(j).getIntValue("status") == 29) {
                                jsonObject.put(Constant.WORK_ORDER_ID, workOrder.getLong("id"));
                                jsonObject.put("claimReportNo", serviceOrders.getJSONObject(j).getString("reportNumber"));
                            }
                        }

                    }
                }
            }
        }
        return jsonObject;
    }


    private JSONObject getTrackingInfoReq(JSONObject packageInfo) {
        JSONObject trackingInfoReq = new JSONObject();
        trackingInfoReq.put("mailNo", packageInfo.getString("trackingNumber"));
        trackingInfoReq.put("role", "CUST");
        trackingInfoReq.put("language", "en_US");
        return trackingInfoReq;
    }

    private Long getActiveClaimWorkOrderId(JSONArray modules) {
        Long workOrderId = 0L;
        for (int i = 0; i < modules.size(); i++) {
            JSONObject workOrder = modules.getJSONObject(i);
            if ("ali.global.insurance.claim".equals(workOrder.getString("bizCode"))) {
                if (workOrder.getIntValue("status") == 10) {
                    return workOrder.getLong("id");
                }
            }
        }
        return workOrderId;
    }

    private Map<Long, Set<Long>> getReverseOrderIds(JSONObject reverseOrderLines) {
        Map<Long, Set<Long>> reverseOrderIds = new ConcurrentHashMap<>();
        JSONArray results = reverseOrderLines.getJSONArray("result");
        for (int i = 0; i < results.size(); i++) {
            Long reverseOrderId = results.getJSONObject(i).getLong("reverseOrderId");
            if (reverseOrderIds.keySet().contains(reverseOrderId)) {
                reverseOrderIds.get(reverseOrderId).add(results.getJSONObject(i).getLong("reverseOrderLineId"));
            } else {
                Set<Long> reverseOrderLineIds = new HashSet<>();
                Long reverseOrderLineId = results.getJSONObject(i).getLong("reverseOrderLineId");
                reverseOrderLineIds.add(reverseOrderLineId);
                reverseOrderIds.put(reverseOrderId, reverseOrderLineIds);
            }
        }
        return reverseOrderIds;
    }


    private Map<Long, JSONObject> getReverseOrderLineIds(JSONObject reverseOrderLines) {
        Map<Long, JSONObject> reverseOrderLineIds = new ConcurrentHashMap<>();
        JSONArray results = reverseOrderLines.getJSONArray("result");
        for (int i = 0; i < results.size(); i++) {
            Long reverseOrderLineId = results.getJSONObject(i).getLong("reverseOrderLineId");
            reverseOrderLineIds.put(reverseOrderLineId, results.getJSONObject(i));
        }
        return reverseOrderLineIds;
    }

    /**
     * 拒付-拼接用户相关消息
     *
     * @return
     */
    private List<String> getCustomerMsgs(JSONObject jsonObject) {
        String customerMsg = jsonObject.getString(Constant.CUSTOMER_MSGS);
        List<String> customerMsgs = new ArrayList<>();
        if (StringUtils.isEmpty(customerMsg)) {
            customerMsgs.add("default msg");
            customerMsgs.add("default customer");
        } else {
            for (String tmp : customerMsg.split(",")) {
                customerMsgs.add(tmp);
            }
        }
        return customerMsgs;
    }

    /**
     * 拒付-拼接物流信息
     *
     * @return
     */
    private List<Map<String, String>> getReturnShippingInfos(JSONObject jsonObject) {
        String shippingCarrier = jsonObject.getString(Constant.SHIPPING_CARRIER);
        String shippingNumber = jsonObject.getString(Constant.SHIPPING_NUMBER);
        List<Map<String, String>> returnShippingInfos = new ArrayList<>();
        shippingCarrier = StringUtils.isEmpty(shippingCarrier) ? "DEFAULT" : shippingCarrier;
        shippingNumber = StringUtils.isEmpty(shippingNumber) ? "111" : shippingNumber;
        Map<String, String> map = new HashMap<>();
        map.put(Constant.SHIPPING_CARRIER, shippingCarrier);
        map.put(Constant.SHIPPING_NUMBER, shippingNumber);
        returnShippingInfos.add(map);
        return returnShippingInfos;
    }

    private ResultDTO sendChargeBackMessage(JSONObject request, Long orderId) {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject attributes = request.getJSONObject("attributes");
        JSONObject chargeBackEntity = attributes.getJSONObject("chargebackEntity");
        attributes.put("chargebackEntity", JSONObject.toJSONString(chargeBackEntity));
        try {
            SendResult result = mqConfig.sendMessage(Constant.PAYMENT_TOPIC, Constant.CB_TAG, orderId.toString(), JSONObject.toJSONString(request));
            resultDTO.setSuccess(true);
            resultDTO.setData(JSONObject.toJSONString(result) + "\n=====\n" + request.toJSONString());
            resultDTO.setMessage(JSONObject.toJSONString(result));
        } catch (Exception e) {
            resultDTO.setSuccess(false);
            resultDTO.setData("send message error: " + e.getMessage());
        }
        return resultDTO;
    }

    @Override
    public ResultDTO promiseTemplateDate(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long seller = jsonObject.getLong(Constant.SELLER_ID);

        JSONObject promiseToSellerDTOMap = null;
        try {
            promiseToSellerDTOMap = HsfUtil.getPromiseToSellerDTOMap(seller);
        } catch (Exception e) {
            throw new RuntimeException("promiseTemplateDate genericServiceInvoke failed, error:" + e.getMessage(), e);
        }

        JSONObject res = new JSONObject();
        // 迫不得已写另一个value存放，不然平台展示不了json；
        res.put("value", JSON.toJSONString(promiseToSellerDTOMap));
        Map<String, QueryResultUnit> jsonFeature = QueryResultBuilder.buildQueryResult("promise", null, null, res);
        Map<String, QueryResultUnit> orderLineFeature = QueryResultBuilder.buildQueryResult("feature", null, null, promiseToSellerDTOMap);
        Map<String, QueryResultUnit> data = new LinkedHashMap<>();
        data.putAll(jsonFeature);
        data.putAll(orderLineFeature);
        result.setData(JSONObject.toJSONString(data, SerializerFeature.PrettyFormat));
        result.setSuccess(true);
        return result;
    }

    @Override
    public ResultDTO updatePromiseRule(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);

        Long seller = jsonObject.getLong(Constant.SELLER_ID);
        Long promiseId = jsonObject.getLong(Constant.PROMISE_ID);
        String ruleDetail = jsonObject.getString(Constant.RULE_DETAIL);
        JSONObject promiseTemplateActionMap = null;
        try {
            promiseTemplateActionMap = HsfUtil.updatePromiseRule(seller, promiseId, ruleDetail);
        } catch (Exception e) {
            throw new RuntimeException("promiseTemplateDate genericServiceInvoke failed, error:" + e.getMessage(), e);
        }

        if (promiseTemplateActionMap != null) {
            result.setData(JSON.toJSONString(promiseTemplateActionMap));
        }
        result.setSuccess(true);
        return result;
    }

    @Override
    public ResultDTO promiseTemplateAction(String params, SystemDTO systemDTO) throws Exception {
        String empId = systemDTO.getOperator();
        ResultDTO result = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);

        Long seller = jsonObject.getLong(Constant.SELLER_ID);
        Long promiseId = jsonObject.getLong(Constant.PROMISE_ID);
        Byte actionId = "签约".equals(jsonObject.getString(Constant.ACTION_ID)) ? (byte) 0 : (byte) 1;
        JSONObject promiseTemplateActionMap = HsfUtil.sellerJoinOrExitPromise(seller, promiseId, actionId);
        if (promiseTemplateActionMap != null) {
            result.setData(JSON.toJSONString(promiseTemplateActionMap));
        }
        result.setSuccess(true);
        HsfUtil.measureAll("/reverse/jobId=" + systemDTO.getSite(), empId);
        return result;
    }


    @Override
    public ResultDTO mockFulfillmentStatus(String params, SystemDTO systemDTO) throws Exception {
        String empId = systemDTO.getOperator();
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        String fulfillmentType = jsonObject.getString(Constant.FULFILLMENT_TYPE);
        log.error("params:{}, mockFulfillmentStatus:{}", params, fulfillmentType);
        String dpath = jsonObject.getString(Constant.DPATH_ENV);
        if (StringUtil.isNotBlank(dpath)) {
            EagleEye.putUserData("dpath_env", dpath);
        }
        switch (fulfillmentType) {
            case "面单":
                resultDTO = ReverseFulfilmentUtil.mockFR2MailNo(buildFRFulfillmentReq(params));
                break;
            case "ascan":
                resultDTO = ReverseFulfilmentUtil.mockFRAscan(buildFRFulfillmentReq(params));
                break;
            case "dscan":
                resultDTO = ReverseFulfilmentUtil.mockFRDSCAN(buildFRFulfillmentReq(params));
                break;
            case "qc":
                resultDTO = ReverseFulfilmentUtil.mockQCCheck(buildFRFulfillmentReq(params));
                break;
            case "上门揽件":
                resultDTO = ReverseFulfilmentUtil.mockFR3PickUp(buildFRFulfillmentReq(params));
                break;
            default:
                resultDTO.setSuccess(false);
                resultDTO.setMessage("未匹配到type:" + fulfillmentType);
                break;
        }
        HsfUtil.measureAll("/reverse/jobId=" + systemDTO.getSite(), empId);
        return resultDTO;
    }

    @Override
    public ResultDTO mockFRFulfillmentStatus(String params, SystemDTO systemDTO) throws Exception {
        String empId = systemDTO.getOperator();
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        String fulfillmentType = jsonObject.getString(Constant.FULFILLMENT_TYPE);


        String dpath = jsonObject.getString(Constant.DPATH_ENV);
        if (StringUtil.isNotBlank(dpath)) {
            EagleEye.putUserData("dpath_env", dpath);
        }

        switch (fulfillmentType) {
            case "面单返回":
                resultDTO = ReverseFulfilmentUtil.mockFR2MailNo(buildFRFulfillmentReq(params));
                break;
            case "ascan成功":
                resultDTO = ReverseFulfilmentUtil.mockFRAscan(buildFRFulfillmentReq(params));
                break;
            case "仓库收到货(dscan)":
                resultDTO = ReverseFulfilmentUtil.mockFRDSCAN(buildFRFulfillmentReq(params));
                break;
            case "质检完成":
                resultDTO = ReverseFulfilmentUtil.mockQCCheck(buildFRFulfillmentReq(params));
                break;
            case "履约接单成功":
                resultDTO = ReverseFulfilmentUtil.mockFR3PickUp(buildFRFulfillmentReq(params));
                break;
            default:
                resultDTO.setSuccess(false);
                resultDTO.setMessage("未匹配到type:" + fulfillmentType);
                break;
        }
        HsfUtil.measureAll("/reverse/jobId=" + systemDTO.getSite(), empId);
        return resultDTO;
    }

    private MockFRFulfillmentReq buildFRFulfillmentReq(String params) {
        MockFRFulfillmentReq mockFRFulfillmentReq = new MockFRFulfillmentReq();
        JSONObject jsonObject = JSONObject.parseObject(params);
        mockFRFulfillmentReq.setBuyerId(jsonObject.getLong(Constant.BUYER_ID));
        mockFRFulfillmentReq.setTradeOrderLineId(jsonObject.getLong(Constant.TRADE_ORDER_LINE_ID));
        if (!Objects.isNull(jsonObject.getBoolean("islandPkg"))) {
            mockFRFulfillmentReq.setIslandPkg(jsonObject.getBoolean("islandPkg"));
        }

        return mockFRFulfillmentReq;
    }

    @Override
    public ResultDTO getAdvanceFundOrderInfo(String params, SystemDTO systemDTO) throws Exception {
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        Long tradeOrderLineId = jsonObject.getLong(Constant.TRADE_ORDER_LINE_ID);

        String dpath = jsonObject.getString(Constant.DPATH_ENV);
        if (StringUtil.isNotBlank(dpath)) {
            EagleEye.putUserData("dpath_env", dpath);
        }

        ResultDTO result = new ResultDTO();

        //根据子单id查询逆向单
        JSONObject reverseOrderLines = HsfUtil.getLastReverseOrderByTradeOrderLineId(buyerId, tradeOrderLineId);
        JSONObject lastReverseOrder = reverseOrderLines.getJSONObject("result");
        Long reverseOrderLineId = lastReverseOrder.getLong("reverseOrderLineId");

        try {
            JSONObject res = HsfUtil.getReverseInfoByReverseOrderLineId(reverseOrderLineId);
            JSONObject resultJSONObject = res.getJSONObject("result");
            if (resultJSONObject != null) {
                Map<String, QueryResultUnit> data = new LinkedHashMap<>();
                Map<String, QueryResultUnit> reverseOrderLine = QueryResultBuilder.buildQueryResult("逆向单信息", null, null, resultJSONObject.getJSONObject("reverseOrderLine"));
                Map<String, QueryResultUnit> advanceFundOrder = QueryResultBuilder.buildQueryResult("垫资单信息", null, null, resultJSONObject.getJSONObject("advanceFundOrder"));
                data.putAll(reverseOrderLine);
                data.putAll(advanceFundOrder);
                result.setData(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
                result.setMessage("操作成功");
            } else {
                result.setData("无垫资信息。Res: " + res.toJSONString());
                result.setMessage("无垫资信息。Res: " + res.toJSONString());
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.setSuccess(true);
            result.setData("系统异常，请重试，超过三次还是不行，就放弃吧");
            result.setMessage("系统异常，请重试，超过三次还是不行，就放弃吧");
        }
        return result;
    }

    @Override
    public ResultDTO mockUnreachableCloseOrder(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        JSONObject paramJsonObj = JSONObject.parseObject(params);
        String triggerType = paramJsonObj.getString(Constant.TRIGGER_TYPE);
        String dpath = paramJsonObj.getString(Constant.DPATH_ENV);
        String tradeOrderLineId = paramJsonObj.getString(Constant.TRADE_ORDER_LINE_ID);
        if (StringUtil.isNotBlank(dpath)) {
            EagleEye.putUserData("dpath_env", dpath);
        }
        if ("消息".equalsIgnoreCase(triggerType)) {
            // mock履约发送不可达消息
            String msg = getUnreachableMsg(paramJsonObj);
            SendResult sendResult = mqConfig.sendMessage(Constant.REVERSE_FULFILLMENT_TOPIC, Constant.FULFILLMENT_UNREACHABLE_TAG, tradeOrderLineId, msg);
            result.setSuccess(true);
            result.setData("触发成功: " + JSON.toJSONString(sendResult));
            result.setMessage("触发成功: " + JSON.toJSONString(sendResult));
        } else {
            // 接口触发履约发送不可达消息
            Long buyerId = paramJsonObj.getLong(Constant.BUYER_ID);
            String tradeOrderId = paramJsonObj.getString(Constant.ORDER_ID);
            String warehouseOrderId = paramJsonObj.getString(Constant.WH_ORDER);
            JSONObject warehouseOrders = HsfUtil.queryWarehouseOrdersByTradeOrderId(buyerId, tradeOrderId);
            if (warehouseOrders == null || !warehouseOrders.getBoolean("success")) {
                result.setSuccess(true);
                result.setData("获取仓单失败: " + JSON.toJSONString(warehouseOrders));
                result.setMessage("获取仓单失败: " + JSON.toJSONString(warehouseOrders));
            }
            if (warehouseOrders.getJSONArray("module") == null || warehouseOrders.getJSONArray("module").size() == 0) {
                result.setSuccess(true);
                result.setData("未查询到仓单信息: " + JSON.toJSONString(warehouseOrders));
                result.setMessage("未查询到仓单信息: " + JSON.toJSONString(warehouseOrders));
            }
            log.info("warehouseOrders: " + warehouseOrders.toJSONString());
            JSONObject warehouseOrder = getWarehouseOrder(warehouseOrderId, warehouseOrders.getJSONArray("module"));
            if (warehouseOrder == null) {
                result.setSuccess(true);
                result.setData("未查询到仓单信息. 仓单id：" + warehouseOrder + ". 履约返回仓信息: " + warehouseOrders.getJSONArray("module").toJSONString());
                result.setMessage("未查询到仓单信息. 仓单id：" + warehouseOrder + ". 履约返回仓信息: " + warehouseOrders.getJSONArray("module").toJSONString());
            }
            String lpOrder = warehouseOrder.getString("outBizId");
            String mailNo = warehouseOrder.getString("trackingNumbers");
            warehouseOrderId = warehouseOrder.getString("warehouseOrderId");
            JSONObject unreachableReq = ReverseFulfilmentUtil.getUnreachableReq(tradeOrderId, tradeOrderLineId, lpOrder, mailNo, warehouseOrderId);
            JSONObject res = HsfUtil.manualEventHandleMsg(unreachableReq);
            result.setSuccess(true);
            result.setMessage("执行成功: " + res.toJSONString());
            result.setData("执行成功: " + res.toJSONString());
        }
        return result;
    }

    private JSONObject getWarehouseOrder(String warehouseOrderId, JSONArray warehouseOrders) {
        if (StringUtils.isNotBlank(warehouseOrderId)) {
            for (int i = 0; i < warehouseOrders.size(); i++) {
                if (warehouseOrderId.equalsIgnoreCase(warehouseOrders.getJSONObject(i).getString("warehouseOrderId"))) {
                    return warehouseOrders.getJSONObject(i);
                }
            }
        } else {
            return warehouseOrders.getJSONObject(0);
        }
        return null;
    }

    private String getUnreachableMsg(JSONObject param) {
        JSONObject msg = JSONObject.parseObject(Constant.UNREACHABLE_FULFILLMENT_MSG);

        String tradeOrderId = param.getString(Constant.ORDER_ID);
        msg.put("tradeOrderId", tradeOrderId);

        String buyerId = param.getString(Constant.BUYER_ID);
        msg.put("buyerId", buyerId);

        String sellerId = param.getString(Constant.SELLER_ID);
        msg.put("sellerId", sellerId);

        String packageId = param.getString(Constant.PACKAGE_ID);


        String reverseType = param.getString(Constant.REVERSE_TYPE);

        if ("海外仓".equals(reverseType)) {
            JSONObject extendMap = msg.getJSONObject("extendMap");
            extendMap.put("fulfillmentType", "OVERSEAS_WAREHOUSE");
            extendMap.put("originEventCode", "LAST_MILE_RETURN");
        } else if ("半托管".equals(reverseType)) {
            JSONObject extendMap = msg.getJSONObject("extendMap");
            extendMap.put("bizCode", "SEMI_CHOICE_SELF");
            extendMap.put("fulfillmentType", "SELECTION_WAREHOUSE");
            extendMap.put("originEventCode", "SORTING_CENTER_UNREACHABLE_RETURN_CALLBACK");
        } else if ("POP".equals(reverseType)) {
            JSONObject extendMap = msg.getJSONObject("extendMap");
            extendMap.put("bizCode", "POP");
        }

        JSONArray fulfillmentOrderItemStatusUpdatedIncreaseList = new JSONArray();
        JSONObject fulfillmentOrderItemStatusUpdatedIncrease = msg.getJSONArray("fulfillmentOrderItemStatusUpdatedIncreaseList").getJSONObject(0);
        String tradeOrderLineId = param.getString(Constant.TRADE_ORDER_LINE_ID);
        fulfillmentOrderItemStatusUpdatedIncrease.put("tradeOrderItemId", tradeOrderLineId);
        fulfillmentOrderItemStatusUpdatedIncrease.put("sellerId", sellerId);
        fulfillmentOrderItemStatusUpdatedIncrease.put("outSubBizId", tradeOrderLineId);

        JSONObject extendMap = fulfillmentOrderItemStatusUpdatedIncrease.getJSONObject("extendMap");
        JSONArray itemList = new JSONArray();
        JSONObject unreachableItem = extendMap.getJSONArray("itemList").getJSONObject(0);
        if (StringUtils.isNotBlank(packageId)) {
            unreachableItem.put("packageId", packageId);
            msg.getJSONObject("extendMap").put("packageId", packageId);
        }
        Integer unReachableQty = param.getInteger(Constant.CANCEL_QUANTITY);
        unreachableItem.put("unReachableQty", unReachableQty);
        unreachableItem.put("unReachableReason", "海外仓".equals(reverseType) ? "末公里退货" : "出入库失败");
        itemList.add(unreachableItem);
        extendMap.put("itemList", itemList.toJSONString());

        fulfillmentOrderItemStatusUpdatedIncrease.put("extendMap", extendMap);
        fulfillmentOrderItemStatusUpdatedIncreaseList.add(fulfillmentOrderItemStatusUpdatedIncrease);
        msg.put("fulfillmentOrderItemStatusUpdatedIncreaseList", fulfillmentOrderItemStatusUpdatedIncreaseList);
        return msg.toJSONString();
    }


    @Override
    public ResultDTO localSeller2ES(String params, SystemDTO systemDTO) throws Exception {
        String empId = systemDTO.getOperator();
        ResultDTO result = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        Long tradeOrderLineId = jsonObject.getLong(Constant.TRADE_ORDER_LINE_ID);
        JSONObject latestReverseOrderLine = HsfUtil.getLastReverseOrderByTradeOrderLineId(buyerId, tradeOrderLineId);
        if (latestReverseOrderLine.getBoolean("success")
                && latestReverseOrderLine.getJSONObject("result") != null
                && 10 != latestReverseOrderLine.getJSONObject("result").getLong("reverseStatus")) {
            List<Long> reverseOrderLineIds = Lists.newArrayList();
            if (CollectionUtils.isNotEmpty(reverseOrderLineIds)) {
                reverseOrderLineIds = createReverse(buyerId, tradeOrderLineId);
            }
        } else {
            result.setSuccess(true);
            result.setData("该笔子单纠纷已完结.无法处理");
            result.setMessage("该笔子单纠纷已完结.无法处理");
        }
        HsfUtil.measureAll("/reverse/jobId=" + systemDTO.getSite(), empId);
        return result;
    }

    @Override
    public ResultDTO tagReverseFulfillmentOrderId(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        try {
            JSONObject jsonObject = JSONObject.parseObject(params);
            Long tradeOrderLineId = jsonObject.getLong(Constant.SUB_ORDER_ID);
            Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
            String reverseFoId = jsonObject.getString(Constant.REVERSE_FO_ID);
            JSONObject latestReverseOrderLine = HsfUtil.getLastReverseOrderByTradeOrderLineId(buyerId, tradeOrderLineId);
            if (latestReverseOrderLine.getBoolean("success")
                    && latestReverseOrderLine.getJSONObject("result") != null
                    && 10 != latestReverseOrderLine.getJSONObject("result").getLong("reverseStatus")) {
                Long reverseOrderLineId = latestReverseOrderLine.getJSONObject("result").getLong("reverseOrderLineId");
                String script = rebuildReverseFoId(reverseOrderLineId, reverseFoId);
                JSONObject res = HsfUtil.callReverseScript(String.valueOf(buyerId), script);
                if (res.getBoolean("success")) {
                    result.setData("操作成功");
                    result.setMessage("操作成功");
                } else {
                    result.setData("操作失败。Res: " + res.toJSONString());
                    result.setMessage("操作失败。Res: " + res.toJSONString());
                }
            } else {
                result.setSuccess(true);
                result.setData("该笔子单纠纷已完结.无法处理");
                result.setMessage("该笔子单纠纷已完结.无法处理");
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.setSuccess(false);
            result.setData("处理失败。traceId: " + EagleEye.getTraceId());
            result.setMessage("处理失败。traceId: " + EagleEye.getTraceId());
        }
        return result;
    }

    @Override
    public ResultDTO retryReverseSettlement(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        try {
            JSONObject jsonObject = JSONObject.parseObject(params);
            Long tradeOrderLineId = jsonObject.getLong(Constant.SUB_ORDER_ID);
            Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
            JSONObject latestReverseOrderLine = HsfUtil.getLastReverseOrderByTradeOrderLineId(buyerId, tradeOrderLineId);
            if (latestReverseOrderLine.getBoolean("success")
                    && latestReverseOrderLine.getJSONObject("result") != null
                    && 10 != latestReverseOrderLine.getJSONObject("result").getLong("reverseStatus")) {
                Long reverseOrderLineId = latestReverseOrderLine.getJSONObject("result").getLong("reverseOrderLineId");
                // todo 批量场景
                List<Long> reverseOrderLineIds = Arrays.asList(reverseOrderLineId);

                JSONObject res = HsfUtil.agreeRefundCheckPoint(buyerId, reverseOrderLineIds);
                if (res.getBoolean("success")) {
                    result.setData("操作成功");
                    result.setMessage("操作成功");
                } else {
                    result.setData("操作失败。Res: " + res.toJSONString());
                    result.setMessage("操作失败。Res: " + res.toJSONString());
                }
            } else {
                result.setSuccess(true);
                result.setData("该笔子单纠纷已完结.无法处理");
                result.setMessage("该笔子单纠纷已完结.无法处理");
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.setSuccess(false);
            result.setData("处理失败。traceId: " + EagleEye.getTraceId());
            result.setMessage("处理失败。traceId: " + EagleEye.getTraceId());
        }
        return result;
    }

    @Override
    public ResultDTO mockFRv3Fulfillment(String params, SystemDTO systemDTO) throws Exception {
        log.info("mockFRv3Fulfillment() ==== ");
        if (systemDTO != null) {
            String empId = systemDTO.getOperator();
            HsfUtil.measureAll("/reverse/jobId=" + systemDTO.getSite(), empId);
        }
        ResultDTO result = new ResultDTO();
        result.setSuccess(true);
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long tradeOrderLineId = jsonObject.getLong(Constant.SUB_ORDER_ID);
        Long tradeOrderId = jsonObject.getLong(Constant.ORDER_ID);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        Long sellerId = jsonObject.getLong(Constant.SELLER_ID);
        String warehouseType = jsonObject.getString(Constant.RETURN_ADDRESS);
        String scenario = jsonObject.getString(Constant.ORDER_SCENARIO);
        String serviceProviderName = jsonObject.getString(Constant.LOGISTICS_SERVICE_PROVIDER);
        String logisticsServiceName = jsonObject.getString(Constant.LOGISTICS_SERVICE_NAME);
        String occurTime = jsonObject.getString(Constant.TIMESTAMP);
        String dpath = jsonObject.getString(Constant.DPATH_ENV);
        Boolean verifiedFulfillment = "否".equals(jsonObject.getString(Constant.TYPE)) ? false : true;

        if (StringUtil.isNotBlank(dpath)) {
            EagleEye.putUserData("dpath_env", dpath);
        }
        try {
            JSONObject reverseOrderLineInfo = HsfUtil.getReverseOrderLinesByOrderId(buyerId, tradeOrderId);

            if (reverseOrderLineInfo == null || !reverseOrderLineInfo.getBoolean("success") || reverseOrderLineInfo.getJSONArray("result") == null) {
                result.setSuccess(true);
                result.setData("未找到有效的逆向单");
                result.setMessage("未找到有效的逆向单");
                return result;
            }
            JSONArray validReverseOrders = filterInvalidReverseInfo(reverseOrderLineInfo.getJSONArray("result"), buyerId, tradeOrderLineId);

            if (validReverseOrders.size() == 0) {
                result.setSuccess(true);
                result.setData("未找到有效的逆向单");
                result.setMessage("未找到有效的逆向单");
                return result;
            }

            Long reverseOrderId = validReverseOrders.getJSONObject(0).getLong("reverseOrderId");

            if (verifiedFulfillment) {
                String foId = validReverseOrders.getJSONObject(0).getJSONObject("features").getString("OuterId");

                if (StringUtils.isEmpty(foId)) {
                    result.setSuccess(true);
                    result.setData("创建履约单失败，请排查问题");
                    result.setMessage("创建履约单失败，请排查问题");
                    return result;
                }
                JSONObject fulfillmentInfo = HsfUtil.queryFulfillmentOrderByFoId(foId);
                if (fulfillmentInfo == null || !fulfillmentInfo.getBoolean("success") || fulfillmentInfo.getJSONObject("module") == null) {
                    result.setSuccess(true);
                    result.setData("查询履约失败，请重试");
                    result.setMessage("查询履约失败，请重试");
                    return result;
                }
                Map<Long, Long> reverseOrderLineId2TradeOrderLineIds = getReverseOrderLineId2TradeOrderLineIds(validReverseOrders);

                Map<Long, String> reverseOrderLineId2FulfillmentOrderItemIds = getReverseOrderLineId2FulfillmentOrderItemIds(fulfillmentInfo.getJSONObject("module").getJSONArray("fulfillmentOrderItemDTOList"));

                String msgBody = ReverseFulfilmentUtil.getFulfillmentMsg(buyerId, sellerId, tradeOrderId, reverseOrderId, foId, warehouseType, serviceProviderName, logisticsServiceName, scenario, reverseOrderLineId2TradeOrderLineIds, reverseOrderLineId2FulfillmentOrderItemIds, occurTime);

                SendResult sendResult = mqConfig.sendMessage(Constant.REVERSE_FULFILLMENT_TOPIC, Constant.REVERSE_FULFILLMENT_TAG, String.valueOf(tradeOrderLineId), msgBody);

                result.setData(JSONObject.toJSONString(sendResult));
                result.setMessage(JSONObject.toJSONString(sendResult));
            } else {
                Long reverseOrderLineId = validReverseOrders.getJSONObject(0).getLong("reverseOrderLineId");
                Map<Long, Long> reverseOrderLineId2TradeOrderLineIds = Maps.newConcurrentMap();
                reverseOrderLineId2TradeOrderLineIds.put(reverseOrderLineId, tradeOrderLineId);

                Map<Long, String> reverseOrderLineId2FulfillmentOrderItemIds = Maps.newConcurrentMap();
                reverseOrderLineId2FulfillmentOrderItemIds.put(reverseOrderLineId, "FO0810513484243006");

                String msgBody = ReverseFulfilmentUtil.getFulfillmentMsg(buyerId, sellerId, tradeOrderId, reverseOrderId, "FO0810512627459006", warehouseType, serviceProviderName, logisticsServiceName, scenario, reverseOrderLineId2TradeOrderLineIds, reverseOrderLineId2FulfillmentOrderItemIds, occurTime);

                SendResult sendResult = mqConfig.sendMessage(Constant.REVERSE_FULFILLMENT_TOPIC, Constant.REVERSE_FULFILLMENT_TAG, String.valueOf(tradeOrderLineId), msgBody);
                result.setData(JSONObject.toJSONString(sendResult));
                result.setMessage(JSONObject.toJSONString(sendResult));
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.setSuccess(false);
            result.setData("系统异常，请重试，超过三次还是不行，就放弃吧");
            result.setMessage("系统异常，请重试，超过三次还是不行，就放弃吧");
        }
        return result;
    }

    private Map<Long, String> getReverseOrderLineId2FulfillmentOrderItemIds(JSONArray fulfillmentOrderItemDTOList) {
        Map<Long, String> reverseOrderLineId2FulfillmentOrderItemIds = Maps.newConcurrentMap();
        for (int i = 0; i < fulfillmentOrderItemDTOList.size(); i++) {
            reverseOrderLineId2FulfillmentOrderItemIds.put(fulfillmentOrderItemDTOList.getJSONObject(i).getLong("outSubBizId")
                    , fulfillmentOrderItemDTOList.getJSONObject(i).getString("fulfillmentOrderItemId"));
        }
        return reverseOrderLineId2FulfillmentOrderItemIds;
    }

    private Map<Long, Long> getReverseOrderLineId2TradeOrderLineIds(JSONArray validReverseOrders) {
        Map<Long, Long> reverseOrderLineId2TradeOrderLineIds = Maps.newConcurrentMap();
        for (int i = 0; i < validReverseOrders.size(); i++) {
            reverseOrderLineId2TradeOrderLineIds.put(validReverseOrders.getJSONObject(i).getLong("reverseOrderLineId"), validReverseOrders.getJSONObject(i).getLong("tradeOrderLineId"));
        }
        return reverseOrderLineId2TradeOrderLineIds;
    }

    private JSONArray filterInvalidReverseInfo(JSONArray reverseOrders, Long buyerId, Long tradeOrderLineId) throws Exception {
        JSONArray batchReverse = new JSONArray();
        JSONArray validReverseInfo = new JSONArray();
        String foId = "";
        for (int i = 0; i < reverseOrders.size(); i++) {
            int reverseStatus = reverseOrders.getJSONObject(i).getIntValue("reverseStatus");
            if (reverseStatus != 2 && reverseStatus != 3 && reverseStatus != 10) {
                validReverseInfo.add(reverseOrders.getJSONObject(i));
                if (tradeOrderLineId.equals(reverseOrders.getJSONObject(i).getLong("tradeOrderLineId"))) {
                    foId = reverseOrders.getJSONObject(i).getJSONObject("features").getString("OuterId");
                }
            }
        }
        // sb逻辑，批量发起纠纷查不到foId
        if (StringUtils.isEmpty(foId)) {
            JSONObject reverseOrderLine = HsfUtil.queryReverseOrderLineById(buyerId, tradeOrderLineId);
            foId = reverseOrderLine.getJSONObject("result").getJSONObject("features").getString("OuterId");
        }

        if (StringUtils.isNotBlank(foId)) {
            for (int i = 0; i < validReverseInfo.size(); i++) {
                if (foId.equalsIgnoreCase(validReverseInfo.getJSONObject(i).getJSONObject("features").getString("OuterId"))) {
                    batchReverse.add(validReverseInfo.getJSONObject(i));
                }
            }
        }

        return batchReverse;
    }

    private String rebuildReverseFoId(Long reverseOrderLineId, String reverseFoId) {
        String script = Constant.TAG_REVERSE_FO_ID.replaceAll("8212951500772884", String.valueOf(reverseOrderLineId));
        if (StringUtils.isNotBlank(reverseFoId)) {
            script = script.replaceAll("FO0810512173388002", reverseFoId);
        }
        return script;
    }

    private String rebuildLogisticsServiceProvider(Long reverseOrderLineId, String f_l_p_t, String f_l_p_c) {
        String script = Constant.LOGISTICS_SERVICE_PROVIDER_SCRIPT.replaceAll("8212951500772884", String.valueOf(reverseOrderLineId));
        if (StringUtils.isNotBlank(f_l_p_t)) {
            script = script.replaceAll("4PL", f_l_p_t);
        }
        if (StringUtils.isNotBlank(f_l_p_c)) {
            script = script.replaceAll("CAINIAO", f_l_p_c);
        }
        return script;
    }

    private List<Long> createReverse(Long buyerId, Long tradeOrderLineId) {
        return Lists.newArrayList();
    }


    private void invoke(String hsfData) throws Exception {
        try {
            JSONObject jsonObject = JSONObject.parseObject(hsfData);
            HSFApiConsumerBean userDataTag = new HSFApiConsumerBean();
            userDataTag.setInterfaceName(jsonObject.getString("facade"));
            userDataTag.setVersion(jsonObject.getString("version"));
            userDataTag.setGroup(jsonObject.getString("group"));
            userDataTag.setGeneric("true");
            userDataTag.init(true);
            GenericService service = (GenericService) userDataTag.getObject();
            String[] paramTypes = jsonObject.getString("paramTypes").split(",");
            Object[] paramData = new Object[paramTypes.length];
            String[] tempDataList = jsonObject.getString("datas").split("~");
            for (int i = 0; i < tempDataList.length; i++) {
                String data = tempDataList[i];
                try {
                    JSONObject param = JSONObject.parseObject(data);
                    paramData[i] = param;
                } catch (Exception e) {
                    log.error("to JSONObject error:", e);
                }
                try {
                    JSONArray param = JSONObject.parseArray(data);
                    paramData[i] = param;
                } catch (Exception e) {
                    log.error("to JSONArray error:", e);
                }
                paramData[i] = data;
            }


            Object result = service.$invoke(
                    jsonObject.getString("method"),
                    jsonObject.getString("paramTypes").split(","),
                    paramData);
        } catch (Exception e) {
            throw e;
        }
    }

    @Override
    public ResultDTO reviseLogisticsServiceProvider(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        try {
            JSONObject jsonObject = JSONObject.parseObject(params);
            Long tradeOrderLineId = jsonObject.getLong(Constant.SUB_ORDER_ID);
            Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
            String serviceProviderName = jsonObject.getString(Constant.LOGISTICS_SERVICE_PROVIDER);
            LogisticsServiceProviderEnum logisticsServiceProvider = LogisticsServiceProviderEnum.getLogisticsServiceProviderByName(serviceProviderName);
            JSONObject latestReverseOrderLine = HsfUtil.getLastReverseOrderByTradeOrderLineId(buyerId, tradeOrderLineId);
            if (latestReverseOrderLine.getBoolean("success")
                    && latestReverseOrderLine.getJSONObject("result") != null
                    && 10 != latestReverseOrderLine.getJSONObject("result").getLong("reverseStatus")) {
                Long reverseOrderLineId = latestReverseOrderLine.getJSONObject("result").getLong("reverseOrderLineId");
                String script = rebuildLogisticsServiceProvider(reverseOrderLineId, logisticsServiceProvider.getLogisticsServiceProvider(), logisticsServiceProvider.getLogisticsServiceProviderCode());
                JSONObject res = HsfUtil.callReverseScript(String.valueOf(buyerId), script);
                if (res.getBoolean("success")) {
                    result.setData("操作成功");
                    result.setMessage("操作成功");
                } else {
                    result.setData("操作失败。Res: " + res.toJSONString());
                    result.setMessage("操作失败。Res: " + res.toJSONString());
                }
            } else {
                result.setSuccess(true);
                result.setData("该笔子单纠纷已完结.无法处理");
                result.setMessage("该笔子单纠纷已完结.无法处理");
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.setSuccess(false);
            result.setData("处理失败。traceId: " + EagleEye.getTraceId() + "Exception:" + e);
            result.setMessage("处理失败。traceId: " + EagleEye.getTraceId() + "Exception:" + e);
        }
        return result;
    }

    @Override
    public ResultDTO modifyAddress(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);

        String dpath = jsonObject.getString(Constant.DPATH_ENV);
        if (StringUtil.isNotBlank(dpath)) {
            EagleEye.putUserData("dpath_env", dpath);
        }

        JSONObject reqJson = new JSONObject();
        reqJson.put("buyerId", jsonObject.getLong(Constant.BUYER_ID));

        JSONObject features = new JSONObject();
        features.put("bizSource", "CANCEL_ORDER");
        reqJson.put("features", features);

        reqJson.put("modifyAddressFields", new JSONObject());

        List<Long> orderIdList = new ArrayList<>();
        orderIdList.add(jsonObject.getLong(Constant.ORDER_ID));
        reqJson.put("orderIdList", orderIdList);


        reqJson.put("paramOk", false);
        reqJson.put("scene", "verifyAddressPre");


        JSONObject source = new JSONObject();
        source.put("appClientVersionNumber", "".equals(jsonObject.getString("version")) ? "8.87.0" : jsonObject.getString("version"));
        source.put("clientType", jsonObject.getString("device"));
        source.put("deviceId", "".equals(jsonObject.getString("deviceId")) ? "Y9x7QLC199gDAPwSsK8OyFrd" : jsonObject.getString("deviceId"));
        source.put("paramOk", false);
        reqJson.put("source", source);

        JSONObject res = HsfUtil.modifyAddress(reqJson);


        if (res.getBoolean("success")) {
            result.setMessage("请求成功" + res.toJSONString());
            result.setData("请求成功" + res.toJSONString());
            result.setSuccess(true);
        } else {
            result.setMessage("请求失败" + res.toJSONString());
            result.setData("请求失败" + res.toJSONString());
            result.setSuccess(false);
        }
        return result;
    }

    @Override
    public ResultDTO mockRefundRecord(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long tradeOrderLineId = jsonObject.getLong(Constant.SUB_ORDER_ID);
        Long tradeOrderId = jsonObject.getLong(Constant.ORDER_ID);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        String scenario = jsonObject.getString(Constant.ORDER_SCENARIO);
        String refundChannel = jsonObject.getString(Constant.REFUND_CHANNEL);
        String outUniqueSeq = jsonObject.getString(Constant.UNIQUE_ID);
        String dpath = jsonObject.getString(Constant.DPATH_ENV);
        if (StringUtil.isNotBlank(dpath)) {
            EagleEye.putUserData("dpath_env", dpath);
        }
        String msg = "";
        // sb逻辑，record塞得乱码起早，主单退款查不到outUniqueSeq，且子单id填入为主单id
        if (StringUtils.isBlank(outUniqueSeq)) {
            outUniqueSeq = getOutUniqueSeq(buyerId, tradeOrderId, tradeOrderLineId);
        } else {
            tradeOrderLineId = tradeOrderId;
        }
        if ("支付受理退款".equals(scenario)) {
            msg = refundRecordService.sendInitOrSettleRecordMsg(buyerId, refundChannel, tradeOrderId, tradeOrderLineId, String.valueOf(System.currentTimeMillis()), "init", "");
        } else if ("二次退款".equals(scenario)) {
            msg = refundRecordService.sendSecondRefundMsg(buyerId, tradeOrderId, tradeOrderLineId, outUniqueSeq);
        } else if ("二次退款异步".equals(scenario)) {
            msg = refundRecordService.sendAsyncSecondRefundMsg(buyerId, tradeOrderId, tradeOrderLineId, outUniqueSeq);
        } else if ("退款完成".equals(scenario)) {
            msg = refundRecordService.sendInitOrSettleRecordMsg(buyerId, refundChannel, tradeOrderId, tradeOrderLineId, outUniqueSeq, "settle", "");
        } else if ("bonus转原路退-init".equals(scenario)) {
            msg = refundRecordService.sendInitOrSettleRecordMsg(buyerId, refundChannel, tradeOrderId, tradeOrderLineId, outUniqueSeq, "init", "bonus2origin");
        } else if ("bonus转原路退-send".equals(scenario)) {
            msg = refundRecordService.sendInitOrSettleRecordMsg(buyerId, refundChannel, tradeOrderId, tradeOrderLineId, outUniqueSeq, "send", "bonus2origin");
        } else if ("bonus转原路退-settle".equals(scenario)) {
            msg = refundRecordService.sendInitOrSettleRecordMsg(buyerId, refundChannel, tradeOrderId, tradeOrderLineId, outUniqueSeq, "settle", "bonus2origin");
        }
        result.setSuccess(true);
        result.setData(msg);
        result.setMessage(msg);
        return result;
    }

    private String getOutUniqueSeq(Long buyerId, Long tradeOrderId, Long tradeOrderLineId) throws Exception {
        JSONObject result = HsfUtil.refundRecordDetails(buyerId, tradeOrderId, tradeOrderLineId);
        if (!result.getBoolean("success") || result.getJSONObject("data") == null) {
            return "";
        }
        return result.getJSONObject("data").getString("outUniqueSeq");
    }

    @Override
    public ResultDTO triggerXdayAlarmTimeout(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long tradeOrderId = jsonObject.getLong(Constant.ORDER_ID);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        String dpath = jsonObject.getString(Constant.DPATH_ENV);
        if (StringUtil.isNotBlank(dpath)) {
            EagleEye.putUserData("dpath_env", dpath);
        }
        JSONObject res = HsfUtil.triggerXdayAlarmTimeout(buyerId, tradeOrderId);

        if (res.getBoolean("success")) {
            result.setMessage("请求成功" + res.toJSONString());
            result.setData("请求成功" + res.toJSONString());
            result.setSuccess(true);
        } else {
            result.setMessage("请求失败" + res.toJSONString());
            result.setData("请求失败" + res.toJSONString());
            result.setSuccess(false);
        }
        return result;
    }

    @Override
    public ResultDTO mockDeliveryFailed(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long tradeOrderId = jsonObject.getLong(Constant.ORDER_ID);
        Long sellerId = jsonObject.getLong(Constant.SELLER_ID);
        String tag = jsonObject.getString("tag");
        String dpath = jsonObject.getString(Constant.DPATH_ENV);
        if (StringUtil.isNotBlank(dpath)) {
            EagleEye.putUserData("dpath_env", dpath);
        }
        JSONObject msgBody = JSONObject.parseObject(Constant.MOCK_DELIVERY_FAILED_REQ);
        switch (tag) {
            case "NX包裹丢失":
                msgBody.put("tags", "rPkgLost;loacl;cb;exception");
                break;
            case "NX包裹破损":
                msgBody.put("tags", "rPkgDmg;loacl;cb;exception");
                break;
            case "NX退回":
                msgBody.put("tags", "rPkgReturn;loacl;cb;exception");
                break;
        }
        msgBody.getJSONObject("features").getJSONObject("featureMap").put("sellerId", sellerId);
        msgBody.getJSONObject("features").getJSONObject("featureMap").put("tradeOrderId", tradeOrderId);
        msgBody.getJSONArray("trackingUpdatedLines").getJSONObject(0).put("tradeOrderId", tradeOrderId);

        JSONObject res = HsfUtil.triggerLogisticTrajectoryMessage(msgBody.toJSONString());

        if (res.getBoolean("success")) {
            result.setMessage("请求成功" + res.toJSONString());
            result.setData("请求成功" + res.toJSONString());
            result.setSuccess(true);
        } else {
            result.setMessage("请求失败" + res.toJSONString());
            result.setData("请求失败" + res.toJSONString());
            result.setSuccess(false);
        }
        return result;
    }

    @Override
    public ResultDTO solutionReached(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long sellerId = jsonObject.getLong(Constant.SELLER_ID);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);

        String dpath = jsonObject.getString(Constant.DPATH_ENV);
        if (StringUtil.isNotBlank(dpath)) {
            EagleEye.putUserData("dpath_env", dpath);
        }
        //根据子单id查询逆向单
        JSONObject reverseOrderLine = HsfUtil.getLastReverseOrderByTradeOrderLineId(jsonObject.getLong("buyerId"), jsonObject.getLong("tradeOrderLineId"));
        JSONObject lastReverseOrder = reverseOrderLine.getJSONObject("result");
        if (lastReverseOrder == null) {
            result.setSuccess(true);
            result.setData("未找到有效的逆向单");
            result.setMessage("未找到有效的逆向单");
            return result;
        }
        Long reverseOrderLineId = lastReverseOrder.getLong("reverseOrderLineId");

        JSONObject solutionReached = new JSONObject();
        solutionReached.put("reverseOrderLineId", reverseOrderLineId);

        JSONObject routingInfo = new JSONObject();
        routingInfo.put("buyerId", buyerId);
        routingInfo.put("sellerId", sellerId);
        routingInfo.put("class", "com.alibaba.global.protocol.common.model.dto.RoutingInfoDTO");
        solutionReached.put("routingInfo", routingInfo);

        List<Long> reverseOrderLineIdList = new ArrayList<>();
        reverseOrderLineIdList.add(reverseOrderLineId);
        solutionReached.put("reverseOrderLineIdList", reverseOrderLineIdList);

        JSONObject operatorDTO = new JSONObject();
        operatorDTO.put("operatorId", buyerId);
        operatorDTO.put("operatorName", "x");
        operatorDTO.put("operatorType", "BUYER");
        operatorDTO.put("class", "com.alibaba.global.reverse.protocol.sdk.model.dto.OperatorDTO");
        solutionReached.put("operatorDTO", operatorDTO);
        solutionReached.put("solutionReached", "com.alibaba.reverse.platform.plus.server.api.request.SolutionReachedReq");


        logger.info("solutionReached=" + solutionReached.toJSONString());
        JSONObject solutionReachedResult = HsfUtil.solutionReached(solutionReached);
        if ("true".equalsIgnoreCase(solutionReachedResult.getString("success"))) {
            result.setSuccess(true);
            result.setData("执行成功");
            result.setMessage("执行成功");
        } else {
            result.setSuccess(false);
            result.setData(solutionReachedResult.getString("errorCode"));
            result.setMessage(solutionReachedResult.getString("errorCode"));
        }
        return result;
    }

    @Override
    public ResultDTO collectAgain(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        result.setSuccess(true);
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long tradeOrderLineId = jsonObject.getLong(Constant.SUB_ORDER_ID);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        JSONObject reverseOrderLine = HsfUtil.getReverseOrderLineByTradeOrderLineId(buyerId, tradeOrderLineId);
        Long reverseOrderLineId = reverseOrderLine.getJSONObject("result").getLong("reverseOrderLineId");
        JSONObject res = HsfUtil.collectAgain(buyerId, reverseOrderLineId);
        if (res.getBoolean("success")) {
            result.setData("二揽成功");
            result.setMessage("二揽成功");
        } else {
            result.setData("二揽失败。Res: " + res.toJSONString());
            result.setMessage("二揽失败。Res: " + res.toJSONString());
            result.setSuccess(false);
        }
        return result;
    }

    @Override
    public ResultDTO sellerUploadMailForFRv3(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        Long sellerId = jsonObject.getLong(Constant.SELLER_ID);
        List<Long> orderLineIds = jsonObject.getJSONArray(Constant.ORDER_LINE_IDS).toJavaList(Long.class);
        String dpath = jsonObject.getString(Constant.DPATH_ENV);
        if (StringUtil.isNotBlank(dpath)) {
            EagleEye.putUserData("dpath_env", dpath);
        }
        List<Long> reverseOrderLineIds = Lists.newArrayList();
        StringBuffer reverseOrderLineIdStr = new StringBuffer();
        for (Long orderLineId : orderLineIds) {
            JSONObject reverseOrderLine = HsfUtil.getReverseOrderLineByTradeOrderLineId(buyerId, orderLineId).getJSONObject("result");
            reverseOrderLineIds.add(reverseOrderLine.getLong("reverseOrderLineId"));
            reverseOrderLineIdStr.append(reverseOrderLine.getLong("reverseOrderLineId")).append(",");
        }
        JSONObject request = JSONObject.parseObject(Constant.SELLER_UPLOAD_REQUEST_BODY);
        request.put("routingId", buyerId);
        request.put("operatorId", sellerId);
        request.put("reverseOrderLineId", reverseOrderLineIds.get(0));
        request.put("reverseOrderLineIdList", reverseOrderLineIdStr.toString().substring(0, reverseOrderLineIdStr.length() - 1));
        JSONObject res = HsfUtil.uploadMail(request);
        result.setSuccess(true);
        result.setMessage(res.toJSONString());
        result.setData(res.toJSONString());
        return result;
    }

    @Override
    public ResultDTO queryNrInterceptorForFRv3(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        JSONObject order = jsonObject.getJSONObject(Constant.ORDER_INFO);
        String shipTo = order.getJSONObject("module").getJSONObject("deliveryAddress").getString("countryCode");
        JSONArray orderLines = jsonObject.getJSONArray(Constant.ORDER_LINES);
        List<Long> tradeOrderLineIds = Lists.newArrayList();
        Map<Long, String> nrRules = Maps.newHashMap();
        for (int i = 0; i < orderLines.size(); i++) {
            tradeOrderLineIds.add(orderLines.getJSONObject(i).getLong("tradeOrderLineId"));
        }
        for (Long tradeOrderLineId : tradeOrderLineIds) {
            JSONObject request = new JSONObject();
            request.put("mergeSteps", false);
            request.put("clientIp", "************");
            request.put("received", false);
            request.put("umidToken", "");
            request.put("sessionId", "4c874a98a04e0a36462a7983de5cada8");
            request.put("buyerId", buyerId);
            request.put("lang", "en_US");
            request.put("fromMtop", true);
            request.put("shipTo", shipTo);
            List<String> orderLineIdStr = Lists.newArrayList();
            orderLineIdStr.add(String.valueOf(tradeOrderLineId));
            request.put("orderLineIds", orderLineIdStr);
            JSONObject res = HsfUtil.initiateReverseRenderLocal(request);
            String nrRule = res.getJSONObject("module").getJSONArray("initiateReverseLineLocalVOS").getJSONObject(0).getJSONObject("nrInterceptRuleVo").getString("ruleCode");
            nrRules.put(tradeOrderLineId, nrRule);
        }
        Map<String, QueryResultUnit> data = QueryResultBuilder.buildQueryResult("NR rules", null, null, JSONObject.parseObject(JSONObject.toJSONString(nrRules)));
        result.setSuccess(true);
        result.setData(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
        result.setMessage(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
        return result;
    }

    @Override
    public ResultDTO reverseOrderLineRenderForSeller(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        result.setSuccess(true);
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        Long tradeOrderLineId = jsonObject.getLong(Constant.SUB_ORDER_ID);
        Long sellerId = jsonObject.getLong(Constant.SELLER_ID);

        //根据子单id查询逆向单
        JSONObject reverseOrderLines = HsfUtil.getLastReverseOrderByTradeOrderLineId(buyerId, tradeOrderLineId);
        JSONObject lastReverseOrder = reverseOrderLines.getJSONObject("result");
        Long reverseOrderLineId = lastReverseOrder.getLong("reverseOrderLineId");

        JSONObject request = new JSONObject();
        request.put("reverseOrderLineId", reverseOrderLineId);
        request.put("channelUserId", sellerId);
        request.put("userId", sellerId);

        JSONObject res = HsfUtil.reverseOrderLineRenderForSeller(request);

        if (res.getBoolean("success")) {
            result.setData(res.toJSONString());
            result.setMessage(res.toJSONString());
        } else {
            result.setData("获取商家页面渲染信息失败。Res: " + res.toJSONString());
            result.setMessage("获取商家页面渲染信息失败。Res: " + res.toJSONString());
            result.setSuccess(false);
        }
        return result;
    }

    @Override
    public ResultDTO sellerAcceptSolution(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        result.setSuccess(true);
        JSONObject solution = getSolution(params, systemDTO);
        if (!solution.isEmpty()) {
            solution.get("id").toString();
        }
        return result;
    }

    @Override
    public ResultDTO cancelOrderByReasonTest(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        Long tradeOrderId = jsonObject.getLong(Constant.ORDER_ID);
        String source = jsonObject.getString(Constant.SOURCE);
        String reason = jsonObject.getString(Constant.REASON);
        String refundMethod = "原路退".equals(jsonObject.getString(Constant.REFUND_CHANNEL)) ? "" : "BONUS";
        CancelOrderOperatorRequest cancelOrderOperatorRequest = getCancelOrderOperatorRequest(buyerId, tradeOrderId, source, reason, refundMethod);
        com.aliexpress.issue.common.result.PlainResult<IssueCancelOrderOperatorResult> response = cancelOrderWriteFacade.openCancelOrderIssue(buyerId, null, cancelOrderOperatorRequest);
        result.setSuccess(true);
        result.setData(JSONObject.toJSONString(response.getData()));
        result.setMessage(JSONObject.toJSONString(response.getData()));
        return result;
    }

    @Override
    public ResultDTO enableOrDisableAfterSalesStrategy(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long sellerId = jsonObject.getLong(Constant.SELLER_ID);
        String strategy = jsonObject.getString(Constant.ORDER_SCENARIO);
        String status = jsonObject.getString(Constant.STATUS);
        JSONObject request = getSwitchStrategyRequest(sellerId, strategy, status);
        JSONObject res = HsfUtil.switchStrategy(request);
        result.setSuccess(true);
        result.setMessage(res.toJSONString());
        result.setMessage(res.toJSONString());
        return result;
    }

    @Override
    public ResultDTO queryNrInterceptorForFRv2(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long tradeOrderId = jsonObject.getLong(Constant.ORDER_ID);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        JSONArray orderLines = jsonObject.getJSONArray(Constant.ORDER_LINES);
        List<Long> tradeOrderLineIds = Lists.newArrayList();
        for (int i = 0; i < orderLines.size(); i++) {
            tradeOrderLineIds.add(orderLines.getJSONObject(i).getLong("tradeOrderLineId"));
        }
        Map<Long, String> nrRules = Maps.newHashMap();
        for (Long tradeOrderLineId : tradeOrderLineIds) {
            JSONObject request = new JSONObject();
            request.put("language", "en_US");
            request.put("buyerId", buyerId);
            request.put("orderId", tradeOrderId);
            request.put("orderLineId", tradeOrderLineId);
            JSONObject res = HsfUtil.queryNrInterceptRule(request);
            String nrRule = res.getJSONObject("module").getString("ruleCode");
            nrRules.put(tradeOrderLineId, nrRule);
        }
        Map<String, QueryResultUnit> data = QueryResultBuilder.buildQueryResult("NR rules", null, null, JSONObject.parseObject(JSONObject.toJSONString(nrRules)));
        result.setSuccess(true);
        result.setData(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
        result.setMessage(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
        return result;
    }

    @Override
    public ResultDTO sellerReportMaliciousSeller(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long tradeOrderLineId = jsonObject.getLong(Constant.SUB_ORDER_ID);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        Long sellerId = jsonObject.getLong(Constant.SELLER_ID);
        JSONObject reverseOrderLines = HsfUtil.getLastReverseOrderByTradeOrderLineId(buyerId, tradeOrderLineId);
        JSONObject lastReverseOrder = reverseOrderLines.getJSONObject("result");
        Long reverseOrderLineId = lastReverseOrder.getLong("reverseOrderLineId");
        String dpath = jsonObject.getString(Constant.DPATH_ENV);
        if (StringUtil.isNotBlank(dpath)) {
            EagleEye.putUserData("dpath_env", dpath);
        }
        JSONObject msgBody = new JSONObject();
        msgBody.put("subBizId", reverseOrderLineId);
        msgBody.put("sceneFullName", "举报异常纠纷");
        msgBody.put("sceneName", "异常纠纷");
        msgBody.put("complainantUserNick", "测试商家");
        msgBody.put("sceneFullCode", "abnormal_dispute");
        msgBody.put("defendantUserNick", "testSeller");
        msgBody.put("complainantUserId", sellerId);
        msgBody.put("subBizType", 2);
        msgBody.put("defendantUserName", "testBuyer");
        msgBody.put("sceneCode", "abnormal_dispute");
        msgBody.put("caseId", 2503803979721520L);
        msgBody.put("complainantMainUserId", buyerId);
        msgBody.put("parentCaseId", 2503803979721519L);
        msgBody.put("complaintBizId", tradeOrderLineId);
        msgBody.put("complainantMainUserNick", "testSeller");
        msgBody.put("fromSource", "ISSUE_DETAIL");
        msgBody.put("defendantUserId", 408038117353L);
        msgBody.put("complaintBizType", 1);
        SendResult sendResult = mqConfig.sendMessage(Constant.XP_COMPLAINT, Constant.ITEM_CREATE, String.valueOf(tradeOrderLineId), msgBody.toJSONString());
        resultDTO.setSuccess(true);
        resultDTO.setMessage(JSONObject.toJSONString(sendResult));
        resultDTO.setData(JSONObject.toJSONString(sendResult));
        return resultDTO;
    }

    @Override
    public ResultDTO timeoutExecute(TimeoutReqDto timeoutReqDto) throws Exception {
        ResultDTO result = new ResultDTO();
        result.setSuccess(true);

        String timeoutType = TimeoutEnum.getValue(timeoutReqDto.getTimeoutType());
        String buyerId = EXTRA_PARAMS.get("buyerId");

        //根据子单id查询逆向单
        com.alibaba.global.protocol.common.model.response.Response<AeReverseOrderLineDTO> lastReverseOrder = aeReverseOrderQueryFacade.queryLastReverseOrderLineByTradeOrderLineId(Long.parseLong(buyerId), Long.parseLong(timeoutReqDto.getTradeOrderLineId()));
        if (!lastReverseOrder.isSuccess() && lastReverseOrder.getResult() == null) {
            result.setData("未找到有效的逆向单");
            result.setMessage("未找到有效的逆向单");
            return result;
        }

        Long reverseOrderLineId = lastReverseOrder.getResult().getReverseOrderLineId();
        com.alibaba.ae.reverse.response.Response<Void> timeoutResult = internalToolFacade.triggerReverseTimeout(reverseOrderLineId, timeoutType);
        result.setData(timeoutResult.isSuccess() ? "执行成功: " : "执行失败: " + JSONObject.toJSONString(timeoutResult));
        result.setMessage(timeoutResult.isSuccess() ? "执行成功: " : "执行失败: " + JSONObject.toJSONString(timeoutResult));
        return result;
    }

    @Override
    public ResultDTO mockCcoUpdateAppealStatus(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        Long tradeOrderId = jsonObject.getLong(Constant.ORDER_ID);
        Long tradeOrderLineId = jsonObject.getLong(Constant.SUB_ORDER_ID);
        String ticketId = jsonObject.getString(Constant.BREACH_ID);
        int status = getCcoTicketStatus(jsonObject.getString(Constant.STATUS));
        JSONObject reverseOrderLines = HsfUtil.getLastReverseOrderByTradeOrderLineId(buyerId, tradeOrderLineId);
        JSONObject lastReverseOrder = reverseOrderLines.getJSONObject("result");
        Long reverseOrderLineId = lastReverseOrder.getLong("reverseOrderLineId");
        Long reverseOrderId = lastReverseOrder.getLong("reverseOrderId");
        String dpath = jsonObject.getString(Constant.DPATH_ENV);
        if (StringUtil.isNotBlank(dpath)) {
            EagleEye.putUserData("dpath_env", dpath);
        }
        JSONObject res = HsfUtil.writeComplaintTicketsInfo(buyerId, tradeOrderId, ticketId, status, reverseOrderLineId, reverseOrderId);
        resultDTO.setSuccess(true);
        resultDTO.setMessage(res.toJSONString());
        resultDTO.setData(res.toJSONString());
        return resultDTO;
    }

    private int getCcoTicketStatus(String status) {
        switch (status) {
            case "已创建":
                return 1;
            case "已结束":
                return 10;
            case "已拒绝":
                return 11;
            case "已取消":
                return 12;
        }
        return 0;
    }

    private JSONObject getSwitchStrategyRequest(Long sellerId, String strategy, String status) {
        JSONObject request = new JSONObject();
        request.put("routingId", sellerId);
        request.put("sellerId", sellerId);
        request.put("scope", "AE_GLOBAL");
        request.put("strategyCode", getAfterSalesStrategyCode(strategy));
        request.put("status", "生效".equals(status) ? 1 : 0);
        return request;
    }

    private String getAfterSalesStrategyCode(String strategy) {
        switch (strategy) {
            case "部分退(不区分商家身份)":
                return "PARTIAL_REFUND";
            case "海外托管-退货方式":
                return "LOCAL_SERVICE_STORE_OWNED_LOGISTIC";
            case "本地pop-退货方式":
                return "LOCAL_POP_LOGISTIC";
            case "本地pop-低金额仅退款":
                return "LOCAL_POP_AUTO_REFUND";
        }
        return "";
    }

    //获取协商方案
    public JSONObject getSolution(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = reverseOrderLineRenderForSeller(params, systemDTO);
        if (resultDTO.getSuccess()) {
            String data = resultDTO.getData();
            JSONArray jsonArray = JSONObject.parseObject(data).getJSONObject("data").getJSONArray("solutions");
            if (jsonArray.size() != 0) {
                return jsonArray.getJSONObject(0);
            }
        }
        return null;
    }

    @Override
    public ResultDTO querySellerStrategiesFromDb(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        result.setSuccess(true);
        JSONObject jsonObject = JSONObject.parseObject(params);

        Long sellerId = jsonObject.getLong(Constant.SELLER_ID);
        String strategyCodeDescription = jsonObject.getString("strategyCodeDescription");
        Map<String, QueryResultUnit> data = new LinkedHashMap<>();
        JSONObject request = new JSONObject();
        request.put("sellerId", sellerId);
        ArrayList<String> strategyCodes = new ArrayList<>();
        String sellerStrategy = SellerStrategyEnum.getSellerStrategyByDescription(strategyCodeDescription);
        strategyCodes.add(sellerStrategy);
        request.put("strategyCodes", strategyCodes);

        JSONObject res = HsfUtil.querySellerStrategiesFromDb(request);
        if (res.getBoolean("success")) {
            JSONArray jsonArray = res.getJSONArray("result");
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject reverseInfo = jsonArray.getJSONObject(i);
                Map<String, QueryResultUnit> map = QueryResultBuilder.buildQueryResult("策略信息: " + reverseInfo.getString("strategyCode") + "-" + reverseInfo.getString("scope"), null, null, reverseInfo);
                data.putAll(map);
            }
        }
        result.setSuccess(true);
        result.setMessage(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
        result.setData(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
        return result;
    }

    @Override
    public ResultDTO querySellerStrategiesInfo(QuerySellerStrategiesDto reqDto) throws Exception {
        ResultDTO result = new ResultDTO();
        result.setSuccess(true);


        AeAfterSaleSellerStrategyQueryReq queryReq = new AeAfterSaleSellerStrategyQueryReq();
        queryReq.setSellerId(Long.valueOf(reqDto.getSellerId()));
        ArrayList<String> strategyCodes = new ArrayList<>();
        String sellerStrategy = SellerStrategyAIEnum.getSellerStrategyByDescription(reqDto.getStrategyCode());
        strategyCodes.add(sellerStrategy);
        queryReq.setStrategyCodes(strategyCodes);
        com.alibaba.global.protocol.common.model.response.Response<List<AeAfterSaleStrategyResult>> listResponse = aeAfterSaleStrategyFacade.queryStrategiesFromDb(queryReq);

        if (listResponse.isSuccess()) {
            result.setMessage(listResponse.getResult().toString());
            result.setData(listResponse.getResult().toString());
        }
        return result;
    }

    @Override
    public ResultDTO deleteSellerStrategy(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        result.setSuccess(true);
        JSONObject jsonObject = JSONObject.parseObject(params);

        String dpath = jsonObject.getString(Constant.DPATH_ENV);
        if (StringUtil.isNotBlank(dpath)) {
            EagleEye.putUserData("dpath_env", dpath);
        }

        Long sellerId = jsonObject.getLong(Constant.SELLER_ID);
        String strategyCodeDescription = jsonObject.getString("strategyCodeDescription");
        JSONObject request = new JSONObject();
        request.put("sellerId", sellerId);
        ArrayList<String> strategyCodes = new ArrayList<>();
        String sellerStrategy = SellerStrategyEnum.getSellerStrategyByDescription(strategyCodeDescription);
        strategyCodes.add(sellerStrategy);
        request.put("strategyCodes", strategyCodes);

        JSONObject SellerStrategiesRes = HsfUtil.querySellerStrategiesFromDb(request);

        ArrayList<Long> mainIds = new ArrayList<>();
        if (SellerStrategiesRes.getBoolean("success")) {
            JSONArray jsonArray = SellerStrategiesRes.getJSONArray("result");
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject reverseInfo = jsonArray.getJSONObject(i);
                Long id = reverseInfo.getLong("id");
                mainIds.add(id);
            }
        } else {
            result.setData("查询商家策略失败。Res: " + SellerStrategiesRes.toJSONString());
            result.setMessage("查询商家策略失败。Res: " + SellerStrategiesRes.toJSONString());
            result.setSuccess(false);
        }


        JSONObject deleteRequest = new JSONObject();
        deleteRequest.put("sellerId", sellerId);
        deleteRequest.put("ids", mainIds);
        JSONObject res = HsfUtil.deleteSellerStrategies(deleteRequest);

        if (res.getBoolean("success")) {
            result.setData(res.toJSONString());
            result.setMessage(res.toJSONString());
        } else {
            result.setData("删除商家策略失败。Res: " + res.toJSONString());
            result.setMessage("删除商家策略失败。Res: " + res.toJSONString());
            result.setSuccess(false);
        }
        return result;
    }

    @Override
    public ResultDTO modifySellerStrategy(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        result.setSuccess(true);
        JSONObject jsonObject = JSONObject.parseObject(params);

        String dpath = jsonObject.getString(Constant.DPATH_ENV);
        if (StringUtil.isNotBlank(dpath)) {
            EagleEye.putUserData("dpath_env", dpath);
        }

        Long sellerId = jsonObject.getLong(Constant.SELLER_ID);
        String countryName = jsonObject.getString("countryName");
        String country = CountryEnum.getCountryByCountryName(countryName);
        String minAmount = jsonObject.getString("partialRefundMinAmount");

        JSONObject request = new JSONObject();
        request.put("sellerId", sellerId);
        request.put("strategyCode", "PARTIAL_REFUND");
        List<String> scopes = new ArrayList<>();
        scopes.add(country);
        request.put("scopes", scopes);
        request.put("status", 1);

        request.put("modifiedDataStr", MODIFIED_DATA_STR.replaceAll("minAmountReplace", minAmount));

        JSONObject res = HsfUtil.modifySellerStrategy(request);

        if (res.getBoolean("success")) {
            result.setData(res.toJSONString());
            result.setMessage(res.toJSONString());
        } else {
            result.setData("修改商家策略失败。Res: " + res.toJSONString());
            result.setMessage("修改商家策略失败。Res: " + res.toJSONString());
            result.setSuccess(false);
        }
        return result;
    }

    @Override
    public ResultDTO aiResultBack(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        // 获取入参
        Long tradeOrderLineId = jsonObject.getLong(Constant.SUB_ORDER_ID);
        String discount = jsonObject.getString("discount");
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);

        //根据子单id查询逆向单
        JSONObject reverseOrderLines = HsfUtil.getLastReverseOrderByTradeOrderLineId(buyerId, tradeOrderLineId);
        JSONObject lastReverseOrder = reverseOrderLines.getJSONObject("result");
        Long reverseOrderLineId = lastReverseOrder.getLong("reverseOrderLineId");

        JSONObject reqJson = JSONObject.parseObject(AI_RESULT.replaceAll("6351621025600453", String.valueOf(reverseOrderLineId)).replaceAll("100", discount));


        //发送AI消息
        SendResult sendResult = mqConfig.sendMessage(AI_TOPIC, AI_TAG, String.valueOf(tradeOrderLineId), JSONObject.toJSONString(reqJson));

        result.setMessage("发送ai消息" + sendResult.getMsgId());
        result.setData("发送ai消息" + sendResult.getMsgId());
        result.setSuccess(true);
        return result;
    }
}
