package com.aliexpress.databank.hsf.provider;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.global.qa.dc.model.result.ResultDTO;
import com.aliexpress.databank.constant.Constant;
import com.aliexpress.databank.dataobject.MockFRFulfillmentReq;
import com.aliexpress.databank.dataobject.ReverseOrderLineDTO;
import com.aliexpress.databank.dataobject.ThubReqDto;
import com.aliexpress.databank.hsf.InsuranceService;
import com.aliexpress.databank.hsf.ReverseOrderService;
import com.aliexpress.databank.hsf.ThubAdaptationService;
import com.aliexpress.databank.utils.ReverseFulfilmentUtil;
import com.aliexpress.databank.utils.ThubAdaptationUtils;
import lombok.extern.log4j.Log4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;

@Log4j
@HSFProvider(serviceInterface = ThubAdaptationService.class, serviceGroup = "HSF")
public class ThubAdaptationServiceImpl implements ThubAdaptationService {
    private static final Logger logger = LoggerFactory.getLogger(ThubAdaptationServiceImpl.class);

    @Autowired
    ReverseOrderService reverseOrderService;

    @Autowired
    InsuranceService insuranceService;


    @Override
    public ResultDTO thubOpenV3Dispute(ThubReqDto thubReqDto) throws Exception {
        logger.info("thubOpenV3Dispute:" + JSONObject.toJSONString(thubReqDto));

        ResultDTO result = new ResultDTO();
        result.setSuccess(true);

        // 校验订单状态
        String buyerId = Constant.EXTRA_PARAMS.get("buyerId");
        try {
           /* ReverseOrderLineDTO reverseOrder = ThubAdaptationUtils.getLastReverseOrderByTradeOrderLineId(Long.parseLong(buyerId), Long.parseLong(thubReqDto.getTradeOrderLineId()));
            if (reverseOrder != null) {
                result.setMessage("创建订单成功");
                result.setData("创建订单成功");
                return result;
            }*/

            JSONObject jsonObject = new JSONObject();
            jsonObject.put("refundChannel", thubReqDto.getRefundChannel());
            jsonObject.put("returnReason", "买家原因");
            jsonObject.put("quantity", thubReqDto.getQuantity());
            jsonObject.put("tradeOrderLineId", thubReqDto.getTradeOrderLineId());

            String params = buildParam(Constant.EXTRA_PARAMS, jsonObject).toJSONString();
            logger.info("thubOpenV3Dispute参数：" + params);

            result = reverseOrderService.returnAndRefund3(params, null);
        } catch (Exception e) {
            e.printStackTrace();
            result.setSuccess(false);
            result.setData(e.getMessage());
            result.setMessage(e.getMessage());
        }
        return result;
    }

    private JSONObject buildParam(Map<String, String> extraParams, JSONObject jsonObject) {
        for (String key : extraParams.keySet()) {
            jsonObject.put(key, extraParams.get(key));
        }
        return jsonObject;
    }

    @Override
    public ResultDTO thubMockVCBuyerInfoError(ThubReqDto thubReqDto) throws Exception {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("scenario", "买家信息错误拒单");
        jsonObject.put(Constant.TYPE, "否");
        String buyerId = Constant.EXTRA_PARAMS.get("buyerId");
        jsonObject.put(Constant.LOGISTICS_SERVICE_PROVIDER, ThubAdaptationUtils.getLogisticsServiceProvider(Long.parseLong(buyerId), Long.parseLong(thubReqDto.getTradeOrderLineId())));
        String params = buildParam(Constant.EXTRA_PARAMS, jsonObject).toJSONString();
        ResultDTO result = new ResultDTO();
        String scenes = thubReqDto.getScenes();
        try {
            /*if (ThubAdaptationUtils.isReverseOrderFinished(Long.parseLong(buyerId), Long.parseLong(thubReqDto.getTradeOrderLineId()))) {
                result.setMessage("订单退款结束");
                result.setData("订单退款结束");
                return result;
            }
            if (ThubAdaptationUtils.isInvalid(Long.parseLong(buyerId), Long.parseLong(thubReqDto.getTradeOrderLineId()), scenes)) {
                result.setMessage("订单状态不合法");
                result.setData("订单状态不合法");
                result.setSuccess(false);
                return result;
            }
            if (!ThubAdaptationUtils.isVisitCollect(Long.parseLong(buyerId), Long.parseLong(thubReqDto.getTradeOrderLineId()))) {
                result.setMessage("非上门揽件订单");
                result.setData("非上门揽件订单");
                result.setSuccess(false);
                return result;
            }
            if (ThubAdaptationUtils.isSelfDropOff(Long.parseLong(buyerId), Long.parseLong(thubReqDto.getTradeOrderLineId()))) {
                result.setMessage("自寄订单跳过此节点");
                result.setData("自寄订单跳过此节点");
                return result;
            }*/
            result = reverseOrderService.mockFRv3Fulfillment(params, null);
        } catch (Exception e) {
            e.printStackTrace();
            result.setSuccess(false);
            result.setData(e.getMessage());
            result.setMessage(e.getMessage());
        }
        return result;
    }


    @Override
    public ResultDTO thubSelfDropOff(ThubReqDto thubReqDto) throws Exception {
        ResultDTO result = new ResultDTO();
        result.setSuccess(true);
        String buyerId = Constant.EXTRA_PARAMS.get("buyerId");
        String scenes = thubReqDto.getScenes();

        // 校验订单状态
        try {
           /* if (ThubAdaptationUtils.isReverseOrderFinished(Long.parseLong(buyerId), Long.parseLong(thubReqDto.getTradeOrderLineId()))) {
                result.setMessage("订单退款结束");
                result.setData("订单退款结束");
                return result;
            }
            if (ThubAdaptationUtils.isCreateArbitration(Long.parseLong(buyerId), Long.parseLong(thubReqDto.getTradeOrderLineId()))) {
                result.setMessage("上升仲裁跳过此节点");
                result.setData("上升仲裁跳过此节点");
                return result;
            }
            if (ThubAdaptationUtils.isInvalid(Long.parseLong(buyerId), Long.parseLong(thubReqDto.getTradeOrderLineId()), scenes)) {
                result.setMessage("订单状态不合法");
                result.setData("订单状态不合法");
                result.setSuccess(false);
                return result;
            }*/

            JSONObject jsonObject = new JSONObject();
            jsonObject.put(Constant.LOGISTIC_COMPANY, "ems");
            jsonObject.put(Constant.LOGISTIC_NUM, (int) (Math.random() * 100000));
            jsonObject.put(Constant.LOGISTIC_CODE, "ems");
            jsonObject.put("tradeOrderLineId", thubReqDto.getTradeOrderLineId());

            String params = buildParam(Constant.EXTRA_PARAMS, jsonObject).toJSONString();
            result = reverseOrderService.buyerReturnGoods(params, null);

        } catch (Exception e) {
            e.printStackTrace();
            result.setSuccess(false);
            result.setData(e.getMessage());
            result.setMessage(e.getMessage());
        }
        return result;
    }

    @Override
    public ResultDTO thubSellerConfirmGoods(ThubReqDto thubReqDto) throws Exception {
        ResultDTO result = new ResultDTO();
        result.setSuccess(true);
        String buyerId = Constant.EXTRA_PARAMS.get("buyerId");

        // 校验订单状态
        try {

            /*if (ThubAdaptationUtils.isReverseOrderFinished(Long.parseLong(buyerId), Long.parseLong(thubReqDto.getTradeOrderLineId()))) {
                result.setMessage("订单退款结束");
                result.setData("订单退款结束");
                return result;
            }

            if (ThubAdaptationUtils.isWarehouseReceived(Long.parseLong(buyerId), Long.parseLong(thubReqDto.getTradeOrderLineId()))) {
                result.setMessage("仓库未收到货");
                result.setData("仓库未收到货");
                result.setSuccess(false);
                return result;
            }*/

            JSONObject jsonObject = new JSONObject();
            jsonObject.put("tradeOrderLineId", thubReqDto.getTradeOrderLineId());
            String params = buildParam(Constant.EXTRA_PARAMS, jsonObject).toJSONString();
            result = reverseOrderService.confirmReturn(params, null);

        } catch (Exception e) {
            e.printStackTrace();
            result.setSuccess(false);
            result.setData(e.getMessage());
            result.setMessage(e.getMessage());
        }
        return result;
    }

    @Override
    public ResultDTO thubOrderInfo(ThubReqDto thubReqDto) throws Exception {
        ResultDTO result = new ResultDTO();
        String buyerId = Constant.EXTRA_PARAMS.get("buyerId");
        try {
            ReverseOrderLineDTO reverseOrderLine = ThubAdaptationUtils.getLastReverseOrderByTradeOrderLineId(Long.parseLong(buyerId), Long.parseLong(thubReqDto.getTradeOrderLineId()));
            Map<String, String> features = reverseOrderLine.getFeatures();
            String returnWay = features.get("return_way");
            JSONObject object = new JSONObject();
            object.put("returnWay", returnWay);
            object.put("scenes", thubReqDto.getScenes());
            object.put("addressType", ThubAdaptationUtils.addressType(Long.parseLong(buyerId), Long.parseLong(thubReqDto.getTradeOrderLineId())));
            object.put("ae_business_type", ThubAdaptationUtils.getAeBusinessType(Long.parseLong(buyerId), Long.parseLong(thubReqDto.getTradeOrderLineId())));
            object.put("reverseOrderLineId", reverseOrderLine.getReverseOrderLineId());

            result.setSuccess(true);
            result.setMessage("订单信息:" + JSONObject.toJSONString(object));
            result.setData("订单信息:" + JSONObject.toJSONString(object));
        } catch (Exception e) {
            e.printStackTrace();
            result.setSuccess(false);
            result.setData(e.getMessage());
            result.setMessage(e.getMessage());
        }
        return result;
    }

    @Override
    public ResultDTO thubMockCollectAgain(ThubReqDto thubReqDto) throws Exception {
        ResultDTO result = new ResultDTO();
        result.setSuccess(true);

        // 校验订单状态
        String buyerId = Constant.EXTRA_PARAMS.get("buyerId");
        try {

           /* if (ThubAdaptationUtils.isReverseOrderFinished(Long.parseLong(buyerId), Long.parseLong(thubReqDto.getTradeOrderLineId()))) {
                result.setMessage("订单退款结束");
                result.setData("订单退款结束");
                return result;
            }

            if (ThubAdaptationUtils.isCreateArbitration(Long.parseLong(buyerId), Long.parseLong(thubReqDto.getTradeOrderLineId()))) {
                result.setMessage("上升仲裁跳过此节点");
                result.setData("上升仲裁跳过此节点");
                return result;
            }

            if (!ThubAdaptationUtils.isVisitCollect(Long.parseLong(buyerId), Long.parseLong(thubReqDto.getTradeOrderLineId()))) {
                result.setMessage("非上门揽件订单");
                result.setData("非上门揽件订单");
                result.setSuccess(false);
                return result;
            }

            if (!ThubAdaptationUtils.isReCollectConfirm(Long.parseLong(buyerId), Long.parseLong(thubReqDto.getTradeOrderLineId()))) {
                result.setMessage("订单状态不合法");
                result.setData("订单状态不合法");
                result.setSuccess(false);
                return result;
            }*/
            JSONObject jsonObject = new JSONObject();
            String params = buildParam(Constant.EXTRA_PARAMS, jsonObject).toJSONString();
            return reverseOrderService.collectAgain(params, null);

        } catch (Exception e) {
            e.printStackTrace();
            result.setSuccess(false);
            result.setData(e.getMessage());
            result.setMessage(e.getMessage());
        }
        return result;
    }

    @Override
    public ResultDTO thubMockAcceptSuccess(ThubReqDto thubReqDto) throws Exception {
        ResultDTO result = new ResultDTO();
        result.setSuccess(true);

        // 校验订单状态
        String buyerId = Constant.EXTRA_PARAMS.get("buyerId");
        String scenes = thubReqDto.getScenes();
        try {
            /*if (ThubAdaptationUtils.isReverseOrderFinished(Long.parseLong(buyerId), Long.parseLong(thubReqDto.getTradeOrderLineId()))) {
                result.setMessage("订单退款结束");
                result.setData("订单退款结束");
                return result;
            }
            if (ThubAdaptationUtils.isInvalid(Long.parseLong(buyerId), Long.parseLong(thubReqDto.getTradeOrderLineId()), scenes)) {
                result.setMessage("订单状态不合法");
                result.setData("订单状态不合法");
                result.setSuccess(false);
                return result;
            }
            if (ThubAdaptationUtils.isSelfDropOff(Long.parseLong(buyerId), Long.parseLong(thubReqDto.getTradeOrderLineId()))) {
                result.setMessage("自寄订单跳过此节点");
                result.setData("自寄订单跳过此节点");
                return result;
            }*/

            JSONObject jsonObject = new JSONObject();
            if (ThubAdaptationUtils.isInsuranceReturn(Long.parseLong(buyerId), Long.parseLong(thubReqDto.getTradeOrderLineId()))) {
                jsonObject.put("scenario", "报案结果消息（面单返回）");
                String params = buildParam(Constant.EXTRA_PARAMS, jsonObject).toJSONString();
                result = insuranceService.mockServiceWorryFreeV2(params, null);
            } else if (ThubAdaptationUtils.isVisitCollect(Long.parseLong(buyerId), Long.parseLong(thubReqDto.getTradeOrderLineId()))) {
                String params = buildParam(Constant.EXTRA_PARAMS, jsonObject).toJSONString();
                result = ReverseFulfilmentUtil.mockFR3PickUp(buildFRFulfillmentReq(params));
            }
            else if (ThubAdaptationUtils.isInformSellerUploadMail(Long.parseLong(buyerId), Long.parseLong(thubReqDto.getTradeOrderLineId()))) {
                String params = buildParam(Constant.EXTRA_PARAMS, jsonObject).toJSONString();
                result = reverseOrderService.sellerUploadMailForFRv3(params,null);
            }
            else {
                String params = buildParam(Constant.EXTRA_PARAMS, jsonObject).toJSONString();
                result = ReverseFulfilmentUtil.mockFR2MailNo(buildFRFulfillmentReq(params));
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.setSuccess(false);
            result.setData(e.getMessage());
            result.setMessage(e.getMessage());
        }
        return result;
    }

    @Override
    public ResultDTO thubMockAcceptFailed(ThubReqDto thubReqDto) throws Exception {
        JSONObject jsonObject = new JSONObject();
        String buyerId = Constant.EXTRA_PARAMS.get("buyerId");
        if (ThubAdaptationUtils.isInsuranceReturn(Long.parseLong(buyerId), Long.parseLong(thubReqDto.getTradeOrderLineId()))) {
            jsonObject.put("scenario", "物流异常消息（二揽）");
            String params = buildParam(Constant.EXTRA_PARAMS, jsonObject).toJSONString();
            return insuranceService.mockServiceWorryFreeV2(params, null);
        }
        return thubMockVCBuyerInfoError(thubReqDto);
    }

    @Override
    public ResultDTO thubMockReceivedAscan(ThubReqDto thubReqDto) throws Exception {
        ResultDTO result = new ResultDTO();
        result.setSuccess(true);

        // 校验订单状态
        String buyerId = Constant.EXTRA_PARAMS.get("buyerId");
        String scenes = thubReqDto.getScenes();
        try {
            /*if (ThubAdaptationUtils.isReverseOrderFinished(Long.parseLong(buyerId), Long.parseLong(thubReqDto.getTradeOrderLineId()))) {
                result.setMessage("订单退款结束");
                result.setData("订单退款结束");
                return result;
            }
            if (ThubAdaptationUtils.isCreateArbitration(Long.parseLong(buyerId), Long.parseLong(thubReqDto.getTradeOrderLineId()))) {
                result.setMessage("上升仲裁跳过此节点");
                result.setData("上升仲裁跳过此节点");
                return result;
            }
            if (ThubAdaptationUtils.isInvalid(Long.parseLong(buyerId), Long.parseLong(thubReqDto.getTradeOrderLineId()), scenes)) {
                result.setMessage("订单状态不合法");
                result.setData("订单状态不合法");
                result.setSuccess(false);
                return result;
            }
            if (ThubAdaptationUtils.isSelfDropOff(Long.parseLong(buyerId), Long.parseLong(thubReqDto.getTradeOrderLineId()))) {
                // 调用买家自寄接口
                return thubSelfDropOff(thubReqDto);
            }*/

            JSONObject jsonObject = new JSONObject();
            if (ThubAdaptationUtils.isInsuranceReturn(Long.parseLong(buyerId), Long.parseLong(thubReqDto.getTradeOrderLineId()))) {
                jsonObject.put("scenario", "已揽件消息（ascan）");
                String params = buildParam(Constant.EXTRA_PARAMS, jsonObject).toJSONString();
                return insuranceService.mockServiceWorryFreeV2(params, null);
            } else {
                String params = buildParam(Constant.EXTRA_PARAMS, jsonObject).toJSONString();
                return ReverseFulfilmentUtil.mockFRAscan(buildFRFulfillmentReq(params));
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.setSuccess(false);
            result.setData(e.getMessage());
            result.setMessage(e.getMessage());
        }
        return result;
    }

    @Override
    public ResultDTO thubMockReceivedDscan(ThubReqDto thubReqDto) throws Exception {
        JSONObject jsonObject = new JSONObject();
        ResultDTO result = new ResultDTO();
        // 校验订单状态
        String buyerId = Constant.EXTRA_PARAMS.get("buyerId");
        String scenes = thubReqDto.getScenes();
        try {
            /*if (ThubAdaptationUtils.isReverseOrderFinished(Long.parseLong(buyerId), Long.parseLong(thubReqDto.getTradeOrderLineId()))) {
                result.setMessage("订单退款结束");
                result.setData("订单退款结束");
                return result;
            }
            if (ThubAdaptationUtils.isCreateArbitration(Long.parseLong(buyerId), Long.parseLong(thubReqDto.getTradeOrderLineId()))) {
                result.setMessage("上升仲裁跳过此节点");
                result.setData("上升仲裁跳过此节点");
                return result;
            }
            if (ThubAdaptationUtils.isInvalid(Long.parseLong(buyerId), Long.parseLong(thubReqDto.getTradeOrderLineId()), scenes)) {
                result.setMessage("订单状态不合法");
                result.setData("订单状态不合法");
                result.setSuccess(false);
                return result;
            }*/
            if (ThubAdaptationUtils.isInsuranceReturn(Long.parseLong(buyerId), Long.parseLong(thubReqDto.getTradeOrderLineId()))) {
                jsonObject.put("scenario", "已签收消息（dscan）");
                String params = buildParam(Constant.EXTRA_PARAMS, jsonObject).toJSONString();
                return insuranceService.mockServiceWorryFreeV2(params, null);
            } else {
                String params = buildParam(Constant.EXTRA_PARAMS, jsonObject).toJSONString();
                return ReverseFulfilmentUtil.mockFRDSCAN(buildFRFulfillmentReq(params));
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.setSuccess(false);
            result.setData(e.getMessage());
            result.setMessage(e.getMessage());
        }
        return result;
    }

    @Override
    public ResultDTO thubMockQualityChecked(ThubReqDto thubReqDto) throws Exception {
        ResultDTO result = new ResultDTO();
        result.setSuccess(true);

        // 校验订单状态
        String buyerId = Constant.EXTRA_PARAMS.get("buyerId");
        String scenes = thubReqDto.getScenes();
        try {
            /*if (ThubAdaptationUtils.isReverseOrderFinished(Long.parseLong(buyerId), Long.parseLong(thubReqDto.getTradeOrderLineId()))) {
                result.setMessage("订单退款结束");
                result.setData("订单退款结束");
                return result;
            }
            if (ThubAdaptationUtils.isInvalid(Long.parseLong(buyerId), Long.parseLong(thubReqDto.getTradeOrderLineId()), scenes)) {
                result.setMessage("订单状态不合法");
                result.setData("订单状态不合法");
                result.setSuccess(false);
                return result;
            }
            if (ThubAdaptationUtils.isNoQcFee(Long.parseLong(buyerId), Long.parseLong(thubReqDto.getTradeOrderLineId()))) {
                result.setMessage("免质检，跳过此节点");
                result.setData("免质检，跳过此节点");
                return result;
            }

            if (ThubAdaptationUtils.isCreateArbitration(Long.parseLong(buyerId), Long.parseLong(thubReqDto.getTradeOrderLineId()))) {
                result.setMessage("上升仲裁跳过此节点");
                result.setData("上升仲裁跳过此节点");
                return result;
            }

            if (ThubAdaptationUtils.isSellerAddress(Long.parseLong(buyerId), Long.parseLong(thubReqDto.getTradeOrderLineId()))) {
                //退商家仓调用商家确认收货接口
                return thubSellerConfirmGoods(thubReqDto);
            }*/


            JSONObject jsonObject = new JSONObject();
            if (ThubAdaptationUtils.isInsuranceReturn(Long.parseLong(buyerId), Long.parseLong(thubReqDto.getTradeOrderLineId()))) {
                jsonObject.put("scenario", "勘查结果消息（质检）");
                String params = buildParam(Constant.EXTRA_PARAMS, jsonObject).toJSONString();
                return insuranceService.mockServiceWorryFreeV2(params, null);
            } else {
                String params = buildParam(Constant.EXTRA_PARAMS, jsonObject).toJSONString();
                return ReverseFulfilmentUtil.mockQCCheck(buildFRFulfillmentReq(params));
            }
        } catch (Exception e) {
            e.printStackTrace();
            result.setSuccess(false);
            result.setData(e.getMessage());
            result.setMessage(e.getMessage());
        }
        return result;
    }

    @Override
    public ResultDTO thubMockCcoOnlyRefund(ThubReqDto thubReqDto) throws Exception {
        ResultDTO result = new ResultDTO();
        result.setSuccess(true);

        // 校验订单状态
        String buyerId = Constant.EXTRA_PARAMS.get("buyerId");
        try {
            if (ThubAdaptationUtils.isReverseOrderFinished(Long.parseLong(buyerId), Long.parseLong(thubReqDto.getTradeOrderLineId()))) {
                result.setMessage("订单退款结束");
                result.setData("订单退款结束");
                return result;
            }

            if (!ThubAdaptationUtils.isCreateArbitration(Long.parseLong(buyerId), Long.parseLong(thubReqDto.getTradeOrderLineId()))) {
                result.setMessage("订单未上升仲裁");
                result.setData("订单未上升仲裁");
                result.setSuccess(false);
                return result;
            }

            JSONObject jsonObject = new JSONObject();
            jsonObject.put(Constant.JUDGE_RESULT, "仅退款");
            jsonObject.put(Constant.RESPONSIBLE_PARTY, "买家");
            jsonObject.put(Constant.FAKE_ITEM, false);
            jsonObject.put("tradeOrderLineId", thubReqDto.getTradeOrderLineId());

            String params = buildParam(Constant.EXTRA_PARAMS, jsonObject).toJSONString();

            result = reverseOrderService.mockCcoJudgement(params, null);

        } catch (Exception e) {
            e.printStackTrace();
            result.setSuccess(false);
            result.setData(e.getMessage());
            result.setMessage(e.getMessage());
        }

        return result;
    }

    @Override
    public ResultDTO thubMockCcoNotRefund(ThubReqDto thubReqDto) throws Exception {
        ResultDTO result = new ResultDTO();
        result.setSuccess(true);

        // 校验订单状态
        String buyerId = Constant.EXTRA_PARAMS.get("buyerId");
        try {
            if (ThubAdaptationUtils.isReverseOrderFinished(Long.parseLong(buyerId), Long.parseLong(thubReqDto.getTradeOrderLineId()))) {
                result.setMessage("订单退款结束");
                result.setData("订单退款结束");
                return result;
            }

            if (!ThubAdaptationUtils.isCreateArbitration(Long.parseLong(buyerId), Long.parseLong(thubReqDto.getTradeOrderLineId()))) {
                result.setMessage("订单未上升仲裁");
                result.setData("订单未上升仲裁");
                result.setSuccess(false);
                return result;
            }

            JSONObject jsonObject = new JSONObject();
            jsonObject.put(Constant.JUDGE_RESULT, "仅退款");
            jsonObject.put(Constant.RESPONSIBLE_PARTY, "买家");
            jsonObject.put(Constant.FAKE_ITEM, false);
            jsonObject.put(Constant.RETURN_AMT, "0");
            jsonObject.put("tradeOrderLineId", thubReqDto.getTradeOrderLineId());

            String params = buildParam(Constant.EXTRA_PARAMS, jsonObject).toJSONString();

            result = reverseOrderService.mockCcoJudgement(params, null);

        } catch (Exception e) {
            e.printStackTrace();
            result.setSuccess(false);
            result.setData(e.getMessage());
            result.setMessage(e.getMessage());
        }

        return result;
    }

    @Override
    public ResultDTO thubMockCcoReturnAndRefund(ThubReqDto thubReqDto) throws Exception {
        ResultDTO result = new ResultDTO();
        result.setSuccess(true);

        // 校验订单状态
        String buyerId = Constant.EXTRA_PARAMS.get("buyerId");
        try {
            if (ThubAdaptationUtils.isReverseOrderFinished(Long.parseLong(buyerId), Long.parseLong(thubReqDto.getTradeOrderLineId()))) {
                result.setMessage("订单退款结束");
                result.setData("订单退款结束");
                return result;
            }

            if (!ThubAdaptationUtils.isCreateArbitration(Long.parseLong(buyerId), Long.parseLong(thubReqDto.getTradeOrderLineId()))) {
                result.setMessage("订单未上升仲裁");
                result.setData("订单未上升仲裁");
                result.setSuccess(false);
                return result;
            }

            JSONObject jsonObject = new JSONObject();
            jsonObject.put(Constant.JUDGE_RESULT, "退货退款");
            jsonObject.put(Constant.RESPONSIBLE_PARTY, "买家");
            jsonObject.put(Constant.FAKE_ITEM, false);
            jsonObject.put("tradeOrderLineId", thubReqDto.getTradeOrderLineId());

            String params = buildParam(Constant.EXTRA_PARAMS, jsonObject).toJSONString();

            result = reverseOrderService.mockCcoJudgement(params, null);

        } catch (Exception e) {
            e.printStackTrace();
            result.setSuccess(false);
            result.setData(e.getMessage());
            result.setMessage(e.getMessage());
        }
        return result;
    }

    private MockFRFulfillmentReq buildFRFulfillmentReq(String params) {
        MockFRFulfillmentReq mockFRFulfillmentReq = new MockFRFulfillmentReq();
        JSONObject jsonObject = JSONObject.parseObject(params);
        mockFRFulfillmentReq.setBuyerId(jsonObject.getLong(Constant.BUYER_ID));
        mockFRFulfillmentReq.setTradeOrderLineId(jsonObject.getLong(Constant.TRADE_ORDER_LINE_ID));
        return mockFRFulfillmentReq;
    }


}
