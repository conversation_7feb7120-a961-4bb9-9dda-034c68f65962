package com.aliexpress.databank.hsf;

import com.alibaba.global.qa.dc.model.result.ResultDTO;
import com.aliexpress.databank.dataobject.*;
import com.aliexpress.databank.dataobject.insurance.SolutionReachedDto;

public interface AIAdaptationService {
    /**
     * 取消订单LLM版本
     */
    NewResultDTO cancelOrder4LLM(CancelOrderDto cancelOrderDto) throws Exception;


    NewResultDTO cancelDispute4LLM(CancelDisputeDto cancelDisputeDto) throws Exception;

    NewResultDTO tagReverseFulfillmentOrderId4LLM(TagReverseFulfillmentOrderIdDto tagReverseFulfillmentOrderIdDto) throws Exception;
    /**
     * 一键下单LLM版本
     */
    ResultDTO placeOrder4LLM(PlaceOrderDto placeOrderDto) throws Exception;

    ResultDTO editPayPrice4LLM(EditPayPriceDto editPayPriceDto) throws Exception;
    /**
     * 加购LLM版本
     */
    ResultDTO addCartItems4LLM(AddCartItemsDto addCartItemsDto) throws Exception;
    /**
     * 创建地址LLM版本
     */
    ResultDTO createAddress4LLM(CreateAddressDto createAddressDto) throws Exception;
    /**
     * 创建推荐地址LLM版本
     */
    ResultDTO createRecommendAddress4LLM(CreateAddressDto createAddressDto) throws Exception;
    /**
     * 创建不合规地址LLM版本
     */
    ResultDTO insertUserAddress4LLM(InsertUserAddressDto insertUserAddressDto) throws Exception;
    /**
     * 确认收货LLM版本
     */
    ResultDTO confirmDelivery4LLM(ConfirmDeliveryDto confirmDeliveryDto) throws Exception;
    /**
     * 履约发货LLM版本
     */
    ResultDTO shipOrder4LLM(ShipOrderDto shipOrderDto) throws Exception;

    /**
     * mockCCO判责LLM版本
     */
    NewResultDTO mockCcoJudgement4LLM(MockCcoJudgementDto mockCcoJudgementDto) throws Exception;

    /**
     * solutionReached重推LLM版本
     */
    NewResultDTO solutionReached4LLM(SolutionReachedDto solutionReachedDto) throws Exception;

    /**
     * mock买家信息错误拒单4LLM版本
     */
    NewResultDTO mockVCBuyerInfoError4LLM(ReqDto reqDto) throws Exception;


    /**
     * mock买家自寄退货4LLM版本
     */
    NewResultDTO selfDropOff4LLM(ReqDto reqDto) throws Exception;

    /**
     * mock商家确认收货4LLM版本
     */
    NewResultDTO sellerConfirmGoods4LLM(ReqDto reqDto) throws Exception;


    /**
     * 触发二揽4LLM版本
     */
    NewResultDTO mockCollectAgain4LLM(ReqDto reqDto) throws Exception;

    /**
     * mock履约接单成功4LLM版本
     */
    NewResultDTO mockAcceptSuccess4LLM(ReqDto reqDto) throws Exception;

    /**
     * mock面单返回4LLM版本
     */
    NewResultDTO mockMailNoReturn4llM(ReqDto reqDto) throws Exception;

    /**
     * mock-收到ASCAN消息4LLM版本
     */
    NewResultDTO mockReceivedAscan4LLM(ReqDto reqDto) throws Exception;


    /**
     * mock-收到DSCAN消息4LLM版本
     */
    NewResultDTO mockReceivedDscan4LLM(ReqDto reqDto) throws Exception;

    /**
     * mock质检完成4LLM版本
     */
    NewResultDTO mockQualityChecked4LLM(ReqDto reqDto) throws Exception;


    /**
     * 商家响应方案4LLM版本
     */
    NewResultDTO acceptSolution4LLM(ReqDto reqDto) throws Exception;

    /**
     * 逆向单分析
     */
    NewResultDTO analyseReverseOrder4LLM(ReverseReqDto reqDto) throws Exception;


    /**
     * 创建纠纷
     */
    NewResultDTO openV3Dispute4LLM(OpenDisputeReq reqDto) throws Exception;

    /**
     * 查询商家策略信息
     */
    NewResultDTO querySellerStrategiesInfo4LLM(QuerySellerStrategiesDto reqDto) throws Exception;

    /**
     * 超时执行
     */
    NewResultDTO timeoutExecute4LLM(TimeoutReqDto timeoutReqDto) throws Exception;

    /**
     * 日志查询
     */
    NewResultDTO querySlsLog(SlsReqDto slsReqDto) throws Exception;

    NewResultDTO chargeBack(ChargeBackDto chargeBackDto) throws Exception;

    NewResultDTO chargeBackJudge(ChargeBackDto chargeBackDto) throws Exception;


    NewResultDTO queryBreachContracts(CompensationDto compensationDto) throws Exception;

    /**     * 履约发货LLM版本
     *
     * @param
     * @return
     * @throws Exception
     */
    NewResultDTO triggerOnTimeGuarantee4LLM(ReqDto reqDto) throws Exception;


    NewResultDTO isMaliciousBuyer4LLM(MaliciousReqDto reqDto) throws Exception;


}
