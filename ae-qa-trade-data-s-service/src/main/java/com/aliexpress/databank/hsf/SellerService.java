package com.aliexpress.databank.hsf;

import com.alibaba.global.qa.dc.model.param.SystemDTO;
import com.alibaba.global.qa.dc.model.result.ResultDTO;

public interface SellerService {

    /**
     *
     * @param orderId 订单id
     * @return
     */
    ResultDTO agreeCancelOrder(String params, SystemDTO systemDTO);

    /**
     *
     * @param orderId 订单id
     * @return
     */
    ResultDTO rejectCancelOrder(String params, SystemDTO systemDTO);

    ResultDTO orderChangeReportForPickUp(String params, SystemDTO systemDTO) throws Exception;
}
