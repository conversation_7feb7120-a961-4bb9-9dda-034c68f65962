package com.aliexpress.databank.hsf;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.global.qa.dc.model.param.SystemDTO;
import com.alibaba.global.qa.dc.model.result.ResultDTO;
import com.alibaba.rocketmq.client.exception.MQBrokerException;
import com.alibaba.rocketmq.client.exception.MQClientException;
import com.alibaba.rocketmq.remoting.exception.RemotingException;
import com.aliexpress.databank.dataobject.QuerySellerStrategiesDto;
import com.aliexpress.databank.dataobject.ReqDto;
import com.aliexpress.databank.dataobject.TimeoutReqDto;

public interface ReverseOrderService {

    ResultDTO cancelOrderLine(String params, SystemDTO systemDTO) throws Exception;

    /**
     * 拒付发生接口
     */
    ResultDTO applyChargeBack(String params, SystemDTO systemDTO) throws InterruptedException, RemotingException, MQClientException, MQBrokerException;

    ResultDTO chargeBack(String params, SystemDTO systemDTO) throws InterruptedException, RemotingException, MQClientException, <PERSON>QBrokerException;

    ResultDTO chargeBackRetry(String params, SystemDTO systemDTO) throws InterruptedException, RemotingException, MQClientException, MQBrokerException;

    /**
     * 拒付申诉接口
     */
    ResultDTO chargeBackAppeal(String params, SystemDTO systemDTO) throws InterruptedException, RemotingException, MQClientException, MQBrokerException;

    ResultDTO chargeBackJudge(String params, SystemDTO systemDTO) throws InterruptedException, RemotingException, MQClientException, MQBrokerException;

    ResultDTO mockAscan(String params, SystemDTO systemDTO) throws InterruptedException, RemotingException, MQClientException, MQBrokerException;


    /**
     * mock履约，直接发送消息
     */
    ResultDTO mockSendFulfillmentMsgByType(String params, SystemDTO systemDTO) throws InterruptedException, RemotingException, MQClientException, MQBrokerException;

    /**
     * hsf接口 履约，直接发送消息
     */
    ResultDTO hsfSendFulfillmentMsgByType(Long reverseOrderLineId, Long buyerId, Long sellerId, String type, String trackingNumber) throws InterruptedException, RemotingException, MQClientException, MQBrokerException;

    /**
     * hsf接口 履约，发送消息
     */
    ResultDTO mockSendFulfillmentMessageBody(Long reverseOrderLineId, String messageBody) throws InterruptedException, RemotingException, MQClientException, MQBrokerException;

    /**
     * 推动发货
     */
    JSONObject mockSendGoods(String foId, Long orderIdStr, Long buyerId);

    /**
     * 下发菜鸟
     */
    JSONObject mockCainiao(String foId, Long orderIdStr, Long buyerId);

    /**
     * 指定topic发消息
     */
    ResultDTO mockSendMessageByTopic(Long reverseOrderLineId, String topic, String tag, String messageBody) throws InterruptedException, RemotingException, MQClientException, MQBrokerException;

    ResultDTO endPayout(String params, SystemDTO systemDTO);

    ResultDTO getRefundChannel(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO migrateOldIssue(String params, SystemDTO systemDTO);

    ResultDTO getIssue(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO getReverseByOrderId(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO returnAndRefund(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO returnAndRefund3(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO sellerArbitration(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO cancelDispute(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO provideSolution(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO acceptSolution(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO getReturnAddress(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO getIssueProtect(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO buyerReturnGoods(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO confirmReturn(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO abandonGoods(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO signFreeReturn(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO quitFreeReturn(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO getServiceOrder(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO placeReverseOrder(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO isSignCjFreeReturn(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO getOfficialOverseaWarehouseMailNum(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO getOfficialOverseaWarehouseAScan(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO getOfficialOverseaWarehouseDScan(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO getReverseFulfillmentOrder(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO mockCjClaimApplyResult(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO mockCjClaimResult(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO getChargeBackRecordAndMessage(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO queryNrInterceptor(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO reverseFlowValidate(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO reverseLifeCycle(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO mockCjReturnGoodsMessage(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO mockCcoJudgement(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO mockCcoJudgement1(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO promiseTemplateDate(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO promiseTemplateAction(String params, SystemDTO systemDTO) throws Exception;


    ResultDTO mockFulfillmentStatus(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO mockFRFulfillmentStatus(String params, SystemDTO systemDTO) throws Exception;


    ResultDTO updatePromiseRule(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO localSeller2ES(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO tagReverseFulfillmentOrderId(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO retryReverseSettlement(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO mockFRv3Fulfillment(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO getAdvanceFundOrderInfo(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO mockUnreachableCloseOrder(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO reviseLogisticsServiceProvider(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO modifyAddress(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO mockRefundRecord(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO triggerXdayAlarmTimeout(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO mockDeliveryFailed(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO solutionReached(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO collectAgain(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO sellerUploadMailForFRv3(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO queryNrInterceptorForFRv3(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO reverseOrderLineRenderForSeller(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO sellerAcceptSolution(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO querySellerStrategiesFromDb(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO querySellerStrategiesInfo(QuerySellerStrategiesDto reqDto) throws Exception;

    ResultDTO deleteSellerStrategy(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO modifySellerStrategy(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO aiResultBack(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO cancelOrderByReasonTest(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO enableOrDisableAfterSalesStrategy(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO queryNrInterceptorForFRv2(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO sellerReportMaliciousSeller(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO timeoutExecute(TimeoutReqDto timeoutReqDto) throws Exception;

    ResultDTO mockCcoUpdateAppealStatus(String params, SystemDTO systemDTO) throws Exception;

}
