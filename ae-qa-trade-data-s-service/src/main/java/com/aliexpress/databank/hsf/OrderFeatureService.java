package com.aliexpress.databank.hsf;

import com.alibaba.global.qa.dc.model.param.SystemDTO;
import com.alibaba.global.qa.dc.model.result.ResultDTO;

public interface OrderFeatureService {


    /**
     * 查询不同业务标
     * @param params : orderId
     * @param systemDTO
     * @return
     * @throws Exception
     */
    ResultDTO queryOrderFeature(String params, SystemDTO systemDTO) throws Exception;

    }
