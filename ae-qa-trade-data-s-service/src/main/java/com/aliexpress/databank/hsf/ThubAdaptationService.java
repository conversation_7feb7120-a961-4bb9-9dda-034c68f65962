package com.aliexpress.databank.hsf;

import com.alibaba.global.qa.dc.model.param.SystemDTO;
import com.alibaba.global.qa.dc.model.result.ResultDTO;
import com.aliexpress.databank.dataobject.ThubReqDto;

public interface ThubAdaptationService {
    /**
     * 3.0开纠纷
     */
    ResultDTO thubOpenV3Dispute(ThubReqDto thubReqDto) throws Exception;


    /**
     * mock买家信息错误拒单
     */
    ResultDTO thubMockVCBuyerInfoError(ThubReqDto thubReqDto) throws Exception;


    /**
     * mock买家自寄退货
     */
    ResultDTO thubSelfDropOff(ThubReqDto thubReqDto) throws Exception;

    /**
     * mock商家确认收货
     */
    ResultDTO thubSellerConfirmGoods(ThubReqDto thubReqDto) throws Exception;

    /**
     * 获取订单信息
     */
    ResultDTO thubOrderInfo(ThubReqDto thubReqDto) throws Exception;

    /**
     * 触发二揽
     */
    ResultDTO thubMockCollectAgain(ThubReqDto thubReqDto) throws Exception;

    /**
     * mock履约接单成功
     */
    ResultDTO thubMockAcceptSuccess(ThubReqDto thubReqDto) throws Exception;

    /**
     * mock履约接单失败
     */

    ResultDTO thubMockAcceptFailed(ThubReqDto thubReqDto) throws Exception;

    /**
     * mock-收到ASCAN消息
     */
    ResultDTO thubMockReceivedAscan(ThubReqDto thubReqDto) throws Exception;


    /**
     * mock-收到DSCAN消息
     */
    ResultDTO thubMockReceivedDscan(ThubReqDto thubReqDto) throws Exception;


    /**
     * mock质检完成
     */
    ResultDTO thubMockQualityChecked(ThubReqDto thubReqDto) throws Exception;


    /**
     * mockCCO-仅退款
     */
    ResultDTO thubMockCcoOnlyRefund(ThubReqDto thubReqDto) throws Exception;

    /**
     * mockCCO-0退款
     */
    ResultDTO thubMockCcoNotRefund(ThubReqDto thubReqDto) throws Exception;

    /**
     * mockCCO-退货退款
     */
    ResultDTO thubMockCcoReturnAndRefund(ThubReqDto thubReqDto) throws Exception;


}
