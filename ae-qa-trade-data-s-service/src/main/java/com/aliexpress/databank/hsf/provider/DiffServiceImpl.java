package com.aliexpress.databank.hsf.provider;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.global.qa.dc.model.builder.QueryResultBuilder;
import com.alibaba.global.qa.dc.model.param.SystemDTO;
import com.alibaba.global.qa.dc.model.result.QueryResultUnit;
import com.alibaba.global.qa.dc.model.result.ResultDTO;
import com.aliexpress.databank.constant.Constant;
import com.aliexpress.databank.hsf.DiffService;
import com.aliexpress.databank.utils.HttpClientUtil;
import lombok.extern.slf4j.Slf4j;
import org.javers.core.Javers;
import org.javers.core.JaversBuilder;
import org.javers.core.diff.Diff;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@HSFProvider(serviceInterface = DiffService.class, serviceGroup = "HSF", serviceVersion = "1.0.0", clientTimeout = 20000)
public class DiffServiceImpl implements DiffService {

    @Override
    public ResultDTO getDiffFromPre2Online(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        boolean isSame = jsonObject.getBoolean(Constant.PRE_IS_THE_SAME_2_ONLINE);

        String preIp = jsonObject.getString(Constant.PRE_IP);
        String onlineIp = jsonObject.getString(Constant.ONLINE_IP);
        String preHsfRef = jsonObject.getString(Constant.PRE_HSF);
        String preHsfVersion = jsonObject.getString(Constant.PRE_HSF_VERSION);
        String preHsfMethod = jsonObject.getString(Constant.PRE_HSF_METHOD);

        if (!Constant.DIFF_HSF.contains(preHsfMethod)) {
            resultDTO.setSuccess(true);
            Map<String, QueryResultUnit> data = QueryResultBuilder.buildQueryResult("ERROR",
                    null, null, "Illegal Method. Only support READ facade. If is read facade, need to add it to whitelist");
            resultDTO.setData(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
            return resultDTO;
        }

        String preSubFix = ":12220/" + preHsfRef + "/" + preHsfVersion + "/" + preHsfMethod;
        String preUrl = "http://" + preIp + preSubFix;
        String onlineUrl = isSame ? "http://" + onlineIp + preSubFix : "http://" + onlineIp + getOnlineUrl(jsonObject);

        List<Object> preRequestBody = formatReqBody(jsonObject.getString(Constant.PRE_REQ_BODY));
        String preReqTypes = jsonObject.getString(Constant.PRE_REQ_TYPE);

        List<String> preArgsTypes = getArgsType(preReqTypes);
        Map<String, Object> preHttpParams = new ConcurrentHashMap<>();
        preHttpParams.put("argsTypes", preArgsTypes);
        preHttpParams.put("argsObjs", preRequestBody);
        String preData = JSON.toJSONString(preHttpParams);

        JSONObject preRes = HttpClientUtil.doPost4HsfHttp(preUrl, preData);
        JSONObject onlineRes = isSame ? HttpClientUtil.doPost4HsfHttp(onlineUrl, preData) :
                HttpClientUtil.doPost4HsfHttp(onlineUrl, getOnlineData(jsonObject));

        Map<String, QueryResultUnit> data = new LinkedHashMap<>();
        Map<String, QueryResultUnit> preResMap = QueryResultBuilder.buildQueryResult("预发返回结果", null, null, preRes);
        Map<String, QueryResultUnit> onlineResMap = QueryResultBuilder.buildQueryResult("线上返回结果", null, null, onlineRes);
        Map<String, QueryResultUnit> diff = QueryResultBuilder.buildQueryResult("线上与预发返回结果diff", null, null, getDiff(preRes, onlineRes));

        data.putAll(diff);
        data.putAll(preResMap);
        data.putAll(onlineResMap);

        resultDTO.setData(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
        resultDTO.setSuccess(true);
        return resultDTO;
    }

    private List<Object> formatReqBody(String reqBody) {
        return new ArrayList<>(Arrays.asList(reqBody.split(",")));
    }

    private String getDiff(JSONObject preRes, JSONObject onlineRes) {
        Javers javers = JaversBuilder.javers().build();
        Diff diff = javers.compare(preRes, onlineRes);
        return javers.getJsonConverter().toJson(diff);
    }

    private String getOnlineData(JSONObject jsonObject) {
        List<Object> onlineRequestBody = formatReqBody(jsonObject.getString(Constant.ONLINE_REQ_BODY));
        String onlineReqTypes = jsonObject.getString(Constant.ONLINE_REQ_TYPE);
        List<String> preArgsTypes = getArgsType(onlineReqTypes);
        Map<String, Object> onlineHttpParams = new ConcurrentHashMap<>();
        onlineHttpParams.put("argsTypes", preArgsTypes);
        onlineHttpParams.put("argsObjs", onlineRequestBody);
        return JSON.toJSONString(onlineHttpParams);
    }

    private List<String> getArgsType(String reqTypes) {
        return new ArrayList<>(Arrays.asList(reqTypes.split(",")));
    }

    private String getOnlineUrl(JSONObject jsonObject) {
        String onlineHsfRef = jsonObject.getString(Constant.ONLINE_HSF);
        String onlineHsfVersion = jsonObject.getString(Constant.ONLINE_HSF_VERSION);
        String onlineHsfMethod = jsonObject.getString(Constant.ONLINE_HSF_METHOD);
        return ":12220/" + onlineHsfRef + "/" + onlineHsfVersion + "/" + onlineHsfMethod;
    }
}
