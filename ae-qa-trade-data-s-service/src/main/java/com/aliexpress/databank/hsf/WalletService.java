package com.aliexpress.databank.hsf;

import com.alibaba.global.qa.dc.model.param.SystemDTO;
import com.alibaba.global.qa.dc.model.result.ResultDTO;



public interface WalletService {


    ResultDTO mockBonus(String params, SystemDTO systemDTO) throws Exception;
    ResultDTO deleteWalletUser(String params, SystemDTO systemDTO) throws Exception;


    ResultDTO addRiskWhitelist(String params, SystemDTO systemDTO) throws Exception;


	ResultDTO arrivalRepaid(String params, SystemDTO systemDTO);

	ResultDTO arrivalRepaidPeriod(String params, SystemDTO systemDTO);

	ResultDTO carryForwardRefundStatus(String params, SystemDTO systemDTO);

	ResultDTO getWalletInfo(String params, SystemDTO systemDTO) throws Exception;

	ResultDTO getPADAmount(String params, SystemDTO systemDTO) throws Exception;

	ResultDTO setPADWhiteList(String params, SystemDTO systemDTO) throws Exception;

	Long getMemberIdByUserId(String buyerId);

	ResultDTO getPadInfo(String params, SystemDTO systemDTO) throws Exception;

	ResultDTO bindCardAssetOffline(String params, SystemDTO systemDTO);

	ResultDTO queryBindCard(String params, SystemDTO systemDTO);
}

