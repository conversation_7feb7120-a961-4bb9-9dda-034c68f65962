package com.aliexpress.databank.hsf;

import com.alibaba.global.qa.dc.model.param.SystemDTO;
import com.alibaba.global.qa.dc.model.result.ResultDTO;
import com.alibaba.rocketmq.client.exception.MQBrokerException;
import com.alibaba.rocketmq.client.exception.MQClientException;
import com.alibaba.rocketmq.remoting.exception.RemotingException;

public interface InsuranceService {

    ResultDTO mockInsured(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO mockApproved(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO mockCreateClaim(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO mockAgreeBuyerSendGoods(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO inquireInsuranceOrderInfo(String params, SystemDTO systemDTO) ;

    ResultDTO mockServiceWorryFreeV2(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO worryFreeV2TimeOut(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO worryFreeV2FastInsure(String params, SystemDTO systemDTO) ;

    ResultDTO mockICBUInformation(String params, SystemDTO systemDTO) throws Exception;
}
