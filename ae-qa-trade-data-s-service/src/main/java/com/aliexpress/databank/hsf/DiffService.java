package com.aliexpress.databank.hsf;

import com.alibaba.global.qa.dc.model.param.SystemDTO;
import com.alibaba.global.qa.dc.model.result.ResultDTO;

public interface DiffService {

    /**
     *
     * @param params:
       isSame: 预发和线上的hsf服务名称，版本，group，方法的出入参数是否一致
       preIp: 预发ip
       onlineIp: 线上ip
       preHsfRef: 预发hsf服务
       preHsfVersion: 预发hsf服务版本号
       preHsfMethod: 预发hsf的方法
       preReqBody: 预发hsf的请求参数
       preReqType: 预发hsf的请求参数类型
       onlineHsfRef: 线上hsf服务
       onlineHsfVersion: 线上hsf服务版本号
       onlineHsfMethod: 线上hsf的方法
       onlineReqBody: 线上hsf的请求参数
       onlineReqType: 线上hsf的请求参数类型
     * @param systemDTO
     * @return
     */
    ResultDTO getDiffFromPre2Online(String params, SystemDTO systemDTO) throws Exception;

}
