package com.aliexpress.databank.hsf.provider;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.ecommerce.module.Response;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.global.address.api.facade.UserAddressReadFacade;
import com.alibaba.global.address.api.model.GlobalUserAddressDTO;
import com.alibaba.global.order.management.api.model.TradeOrderLineDTO;
import com.alibaba.global.qa.dc.model.builder.QueryResultBuilder;
import com.alibaba.global.qa.dc.model.param.SystemDTO;
import com.alibaba.global.qa.dc.model.result.QueryResultUnit;
import com.alibaba.global.qa.dc.model.result.ResultDTO;
import com.alibaba.global.satellite.proxy.guava.SatelliteRemovalListener;
import com.alibaba.intl.ae.logistics.address.dto.WLMailingAddressSnapshotCombineDTO;
import com.alibaba.intl.ae.logistics.address.dto.WlMailingAddressDTO;
import com.alibaba.intl.ae.logistics.address.dto.WlMailingAddressResultDTO;
import com.alibaba.intl.ae.logistics.address.open.remote.WlMailingAddressRemoteService;
import com.aliexpress.databank.constant.Constant;
import com.aliexpress.databank.hsf.AddressService;
import com.aliexpress.databank.utils.HsfUtil;
import com.aliexpress.qa.platform.api.util.MeasureLogger;
import com.google.common.base.Charsets;
import com.google.common.collect.Maps;
import com.google.common.io.Files;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.File;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@HSFProvider(serviceInterface = AddressService.class)
public class AddressServiceImpl implements AddressService {

    @Autowired
    private UserAddressReadFacade userAddressReadFacade;

    @Autowired
    private WlMailingAddressRemoteService wlMailingAddressRemoteService;


    @Override
    public ResultDTO getUserAddressByUserId(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        Response<List<GlobalUserAddressDTO>> userAddressOld = userAddressReadFacade.listUserAddressByUserId(buyerId);
        resultDTO.setSuccess(userAddressOld.isSuccess());
        Map<String, QueryResultUnit> data = new LinkedHashMap<>();
        if (userAddressOld.isSuccess()) {
            String json = JSON.toJSONString(userAddressOld.getModule(), SerializerFeature.DisableCircularReferenceDetect);
            List<GlobalUserAddressDTO> userAddresses = JSON.parseArray(json, GlobalUserAddressDTO.class);
            Map<String, QueryResultUnit> addressData = QueryResultBuilder.buildQueryResult("地址信息", null, null, userAddresses);

            List<Long> snapshotAddressIds = new ArrayList<>();
            userAddresses.forEach(it -> snapshotAddressIds.add(it.getSnapshotId()));
            List<WLMailingAddressSnapshotCombineDTO> wlMailingAddressSnapshotCombineDTOS = new ArrayList<>();
            snapshotAddressIds.forEach(snapshotAddressId -> {
                WLMailingAddressSnapshotCombineDTO wlMailingAddressSnapshotCombineDTO = wlMailingAddressRemoteService.findCombineSnapshotAddressByOwnerSeqAndSnapshotId(buyerId, snapshotAddressId);
                wlMailingAddressSnapshotCombineDTOS.add(wlMailingAddressSnapshotCombineDTO);
            });
            Map<String, QueryResultUnit> snapshotAddress = QueryResultBuilder.buildQueryResult("地址snapshot信息", null, null, wlMailingAddressSnapshotCombineDTOS);

            data.putAll(addressData);
            if (CollectionUtils.isNotEmpty(Collections.singleton(snapshotAddress))) {
                data.putAll(snapshotAddress);
            }
            resultDTO.setData(JSON.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
        } else {
            resultDTO.setData(JSON.toJSONString(userAddressOld));
        }
        return resultDTO;
    }

    @Override
    public ResultDTO createAddress(String params, SystemDTO systemDTO) throws Exception {
        MeasureLogger measureLogger = MeasureLogger.start("ACCESS_LOG", "PVUV", MeasureLogger.Level.INFO);
        measureLogger.setInvokeStartTime(System.currentTimeMillis());
        ResultDTO resultDTO = new ResultDTO();
       JSONObject jsonObject = JSONObject.parseObject(params);
        long buyerId = Long.parseLong(jsonObject.getString(Constant.BUYER_ID));
        String countryCode = jsonObject.getString(Constant.COUNTRY_CODE);
        addAddress(buyerId,countryCode);
        resultDTO.setData("buyerId:" + buyerId + ";" + "country:"+countryCode + ";" + "createAddress success");
        resultDTO.setMessage("buyerId:" + buyerId + ";" + "country:"+countryCode + ";" + "createAddress success");
        measureLogger.setSuccess(true)
                .setInvokeEndTime(System.currentTimeMillis())
                .setEmpId(systemDTO.getOperator())
                .setContent("/trade/jobId="+systemDTO.getSite())
                .end();
        return resultDTO;
    }

    @Override
    public ResultDTO createRecommendAddress(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        long buyerId = Long.parseLong(jsonObject.getString(Constant.BUYER_ID));
        String countryCode = jsonObject.getString(Constant.COUNTRY_CODE);
        addAddress(buyerId,countryCode+"Recommend");
        resultDTO.setData("buyerId:" + buyerId + ";" + "country:"+countryCode + ";" + "createRecommendAddress success");
        resultDTO.setMessage("buyerId:" + buyerId + ";" + "country:"+countryCode + ";" + "createRecommendAddress success");
        return resultDTO;
    }

    @Override
    public ResultDTO createBrCpfAddress(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        log.info("addresscreate params {}", params);
        long buyerId = Long.parseLong(jsonObject.getString(Constant.BUYER_ID));
        String addressType = jsonObject.getString(Constant.ADDRESS_TYPE);
        String country = jsonObject.getString(Constant.SHIP_TO_COUNTRY);
        String cpfStatus = jsonObject.getString(Constant.CPF_STATUS);
        WlMailingAddressDTO wlMailingAddressDTO = new WlMailingAddressDTO();
        wlMailingAddressDTO.setBuyerAddressType("residential");
        wlMailingAddressDTO.setPlatform("PC");
        wlMailingAddressDTO.setCountry("BR");
        wlMailingAddressDTO.setOwnerSeq(buyerId);
        if (cpfStatus.equals("200")||cpfStatus.isEmpty()) {
            wlMailingAddressDTO.setCountryName("Brazil");
            wlMailingAddressDTO.setContactPerson("Alex Gustavo Kuttner");
            wlMailingAddressDTO.setMobileNo("1111111111");
            wlMailingAddressDTO.setPhoneCountry("+55");
            wlMailingAddressDTO.setZip("13165-000");

            wlMailingAddressDTO.setProvince("Acre");
            wlMailingAddressDTO.setProvinceCode("917465680000000000");
            wlMailingAddressDTO.setCity("Acrelandia");
            wlMailingAddressDTO.setCityCode("917465689977000000");
            wlMailingAddressDTO.setAddress("vaild address");
            wlMailingAddressDTO.setAddress2("vaild address");
            wlMailingAddressDTO.setCpf("09625587969");
            wlMailingAddressDTO.setDistrict("12312");
            WlMailingAddressResultDTO wlMailingAddressResultDTO = wlMailingAddressRemoteService.createMailingAddress(wlMailingAddressDTO, buyerId);
            resultDTO.setMessage(wlMailingAddressDTO.getContactPerson() + " address build success");

        }
        if (cpfStatus.equals("401")||cpfStatus.isEmpty()) {
            wlMailingAddressDTO.setCountryName("Brazil");
            wlMailingAddressDTO.setContactPerson("BR NOCPF");
            wlMailingAddressDTO.setMobileNo("1111111111");
            wlMailingAddressDTO.setPhoneCountry("+55");
            wlMailingAddressDTO.setZip("13165-000");

            wlMailingAddressDTO.setProvince("Acre");
            wlMailingAddressDTO.setProvinceCode("917465680000000000");
            wlMailingAddressDTO.setCity("Acrelandia");
            wlMailingAddressDTO.setCityCode("917465689977000000");
            wlMailingAddressDTO.setAddress("invaild 401");
            wlMailingAddressDTO.setAddress2("invaild 401");
            wlMailingAddressDTO.setDistrict("12312");
            wlMailingAddressDTO.setCpf("09625587969");
            WlMailingAddressResultDTO wlMailingAddressResultDTO = wlMailingAddressRemoteService.createMailingAddress(wlMailingAddressDTO, buyerId);
            HsfUtil.putOrDelFeature(buyerId,wlMailingAddressResultDTO.getId(),"401");
            resultDTO.setMessage(wlMailingAddressDTO.getContactPerson() + " address build success");
        }
        if (cpfStatus.equals("402")||cpfStatus.isEmpty()) {
            wlMailingAddressDTO.setCountryName("Brazil");
            wlMailingAddressDTO.setContactPerson("BR NOCPF");
            wlMailingAddressDTO.setMobileNo("1111111111");
            wlMailingAddressDTO.setPhoneCountry("+55");
            wlMailingAddressDTO.setZip("13165-000");

            wlMailingAddressDTO.setProvince("Acre");
            wlMailingAddressDTO.setProvinceCode("917465680000000000");
            wlMailingAddressDTO.setCity("Acrelandia");
            wlMailingAddressDTO.setCityCode("917465689977000000");
            wlMailingAddressDTO.setAddress("invaild 402");
            wlMailingAddressDTO.setAddress2("invaild 402");
            wlMailingAddressDTO.setDistrict("12312");
            wlMailingAddressDTO.setCpf("09625587969");
            WlMailingAddressResultDTO wlMailingAddressResultDTO = wlMailingAddressRemoteService.createMailingAddress(wlMailingAddressDTO, buyerId);
            HsfUtil.putOrDelFeature(buyerId,wlMailingAddressResultDTO.getId(),"401");
            resultDTO.setMessage(wlMailingAddressDTO.getContactPerson() + " address build success");
        }

        return resultDTO;
    }

    @Override
    public ResultDTO insertUserAddress(String params, SystemDTO systemDTO) {

        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        log.info("addresscreate params {}", params);
        long buyerId = Long.parseLong(jsonObject.getString(Constant.BUYER_ID));
        String addressType = jsonObject.getString(Constant.ADDRESS_TYPE);
        String country = jsonObject.getString(Constant.SHIP_TO_COUNTRY);
        WlMailingAddressDTO wlMailingAddressDTO = new WlMailingAddressDTO();
        wlMailingAddressDTO.setBuyerAddressType("residential");
        wlMailingAddressDTO.setPlatform("PC");
        wlMailingAddressDTO.setAddress("auto create");
        wlMailingAddressDTO.setAddress2("auto create");
        wlMailingAddressDTO.setCountry(jsonObject.getString(Constant.SHIP_TO_COUNTRY));
        wlMailingAddressDTO.setOwnerSeq(buyerId);


        //构建巴西缺少CPF地址
        if (country.equals("BR")) {
            wlMailingAddressDTO.setCountryName("Brazil");
            wlMailingAddressDTO.setContactPerson("BR NOCPF");
            wlMailingAddressDTO.setMobileNo("1111111111");
            wlMailingAddressDTO.setPhoneCountry("+55");
            wlMailingAddressDTO.setZip("13165-000");

            wlMailingAddressDTO.setProvince("Acre");
            wlMailingAddressDTO.setProvinceCode("917465680000000000");
            wlMailingAddressDTO.setCity("Acrelandia");
            wlMailingAddressDTO.setCityCode("917465689977000000");
        }
        //构建土耳其缺少passportNo地址
        if (country.equals("TR")) {
            wlMailingAddressDTO.setCountryName("Turkey");
            wlMailingAddressDTO.setContactPerson("TR NO idnumber");
            wlMailingAddressDTO.setMobileNo("1111111111");
            wlMailingAddressDTO.setPhoneCountry("+34");
            wlMailingAddressDTO.setZip("12332");

            wlMailingAddressDTO.setProvince("Adana");
            wlMailingAddressDTO.setProvinceCode("921800560000000000");
            wlMailingAddressDTO.setCity("Ceyhan");
            wlMailingAddressDTO.setCityCode("921800560002000000");
        }
        //构建智利缺少rutNo地址
        if (country.equals("CL")) {
            wlMailingAddressDTO.setCountryName("Chile");
            wlMailingAddressDTO.setContactPerson("chile no rut");
            wlMailingAddressDTO.setMobileNo("12323");
            wlMailingAddressDTO.setPhoneCountry("+56");
            wlMailingAddressDTO.setZip("7234567");

            wlMailingAddressDTO.setProvince("Arica y Parinacota");
            wlMailingAddressDTO.setProvinceCode("904365800000000000");
            wlMailingAddressDTO.setCity("Arica");
            wlMailingAddressDTO.setCityCode("904365806582001000");
        }
        //构建韩国没有清关信息地址
        try {
            if (country.equals("KR")) {
                wlMailingAddressDTO.setAddress("한국어");
                wlMailingAddressDTO.setAddress2("한국어");
                if (addressType.equals("MissingCustoms")){
                    wlMailingAddressDTO.setCountryName("Korea");
                    wlMailingAddressDTO.setContactPerson("한국어");
                    wlMailingAddressDTO.setMobileNo("01123456789");
                    wlMailingAddressDTO.setPhoneCountry("+82");
                    wlMailingAddressDTO.setZip("12345");

                    wlMailingAddressDTO.setProvince("Gangwon-do");
                    wlMailingAddressDTO.setProvinceCode("919800010000000000");
                    wlMailingAddressDTO.setCity("Goseong-gun");
                    wlMailingAddressDTO.setCityCode("919800010002000000");
                }
                if (addressType.equals("HaveCustoms")){
                    wlMailingAddressDTO.setCountryName("Korea");
                    wlMailingAddressDTO.setContactPerson("KR Have Customs");
                    wlMailingAddressDTO.setMobileNo("01123456789");
                    wlMailingAddressDTO.setPhoneCountry("+82");
                    wlMailingAddressDTO.setZip("12345");

                    wlMailingAddressDTO.setProvince("Gangwon-do");
                    wlMailingAddressDTO.setProvinceCode("919800010000000000");
                    wlMailingAddressDTO.setCity("Goseong-gun");
                    wlMailingAddressDTO.setCityCode("919800010002000000");
                    wlMailingAddressDTO.setPassportNo("P682151173203");
                }
            }
        }catch (Exception e){
            e.printStackTrace();
        }

        //构建日本强切地址，省份城市关联关系不相符
        if (country.equals("JP")) {
            wlMailingAddressDTO.setCountryName("Japan");
            wlMailingAddressDTO.setContactPerson("Japan old");
            wlMailingAddressDTO.setMobileNo("1234567890");
            wlMailingAddressDTO.setPhoneCountry("+81");
            wlMailingAddressDTO.setZip("1234567");

            wlMailingAddressDTO.setProvince("AAAAA");
//            wlMailingAddressDTO.setProvinceCode("919800010000000000");
            wlMailingAddressDTO.setCity("aaaaa");
//            wlMailingAddressDTO.setCityCode("919800010002000000");
        }
        //构建法国省份城市与邮编不符地址，出纠偏卡片
        if (country.equals("FR")) {
            wlMailingAddressDTO.setCountryName("France");
            wlMailingAddressDTO.setContactPerson("Fr test");
            wlMailingAddressDTO.setMobileNo("1234567890");
            wlMailingAddressDTO.setPhoneCountry("+33");
            wlMailingAddressDTO.setZip("01250");

            wlMailingAddressDTO.setProvince("Ain");
            wlMailingAddressDTO.setProvinceCode("907202900001000000");
            wlMailingAddressDTO.setCity("Ambleon");
            wlMailingAddressDTO.setCityCode("907202900001009000");
            wlMailingAddressDTO.setAddress("140 RUE DU FOUR");
            wlMailingAddressDTO.setAddress2("Suite");
        }


        WlMailingAddressResultDTO wlMailingAddressResultDTO = wlMailingAddressRemoteService.createMailingAddress(wlMailingAddressDTO, buyerId);
        resultDTO.setMessage(wlMailingAddressDTO.getContactPerson() + " address build success");
        return resultDTO;
    }


    @Override
    public ResultDTO checkAddress(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        long buyerId = Long.parseLong(jsonObject.getString(Constant.BUYER_ID));
        JSONObject checkAddressResult = checkAddressExist(buyerId, jsonObject.getString(Constant.SHIP_TO_COUNTRY));
        resultDTO.setData(checkAddressResult.toString());
        if (checkAddressResult.getBoolean("success")){
            resultDTO.setSuccess(true);
        } else {
            resultDTO.setSuccess(false);
        }
        return resultDTO;
    }

    @Override
    public ResultDTO delectAllAddress(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        JSONObject response = HsfUtil.getMailingAddressInfoV2(buyerId);
        JSONArray userAddressList = null;
        if (response!=null) {
            userAddressList = response.getJSONArray("addressList");
        }
            for(int i=0;i<userAddressList.size();i++){
                Long addressId = userAddressList.getJSONObject(i).getLong("id");
                WlMailingAddressDTO wlMailingAddressDTO = new WlMailingAddressDTO();
                wlMailingAddressDTO.setId(addressId);
                WlMailingAddressResultDTO wlMailingAddressResultDTO = wlMailingAddressRemoteService.deleteMailingAddress(wlMailingAddressDTO,buyerId);
                if (!wlMailingAddressResultDTO.isSuccess()){
                    resultDTO.setMessage(buyerId.toString() +"删除地址失败："+ addressId.toString());
                    log.info(buyerId.toString() +"删除地址失败："+ addressId.toString());
                } else {

                }
            }
        resultDTO.setMessage(buyerId.toString()+"删除地址成功");
        return resultDTO;
    }


    @Override
    public ResultDTO mockRecommendAddress(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        String tradeOrderId = jsonObject.getString(Constant.ORDER_ID);;
        JSONObject tradeOrderResponse = jsonObject.getJSONObject(Constant.ORDER_INFO).getJSONObject("module");
        List<TradeOrderLineDTO> tradeOrderLineDTOs = tradeOrderResponse.getJSONArray("orderLines").toJavaList(TradeOrderLineDTO.class);
        String packageId = tradeOrderLineDTOs.get(0).getOutDeliveryId();
        String regex = "^FO.*$";
        Pattern p = Pattern.compile(regex);
        Matcher m = p.matcher(packageId);
        if (m.matches()){
            HsfUtil.mockRecommendAddress(tradeOrderId, packageId);
            resultDTO.setMessage("触发成功,履约单号为："+packageId);

        }else{
            resultDTO.setMessage("此订单没有履约单号");

        }

        return resultDTO;
    }

    public JSONObject checkAddressExist(Long buyerId,String countryList ){
        List<Object> succList= new ArrayList<>();
        List<Object> failList = new ArrayList<>();
        List<Object> addAddressResult = new ArrayList<>();
        List<Object>  notMatchCountry = new ArrayList<>();
        List countryCodeList = Arrays.asList(countryList.split(","));
        JSONArray addressArray = JSONArray.parseArray(Constant.ADDRESS_DATA);
        Response<List<GlobalUserAddressDTO>> userAddressList = userAddressReadFacade.listUserAddressByUserId(buyerId);
        if (userAddressList.isSuccess()) {
            String json = JSON.toJSONString(userAddressList.getModule(), SerializerFeature.DisableCircularReferenceDetect);
            List<GlobalUserAddressDTO> userAddresses = JSON.parseArray(json, GlobalUserAddressDTO.class);
            List<String> userCountryCodeList = new ArrayList<>();
            for(GlobalUserAddressDTO address:userAddresses) {
                String userCountryCode = address.getAddressLocationTree().getCountryIsoCode();
                userCountryCodeList.add(userCountryCode);
            }
            for (Object countryCode : countryCodeList) {
                String supportCountry = "BR,ES,US,FR,RU,DE,PT,UK";
//                List lis = Arrays.asList(supportCountry.split(","));

                    if (supportCountry.contains(countryCode.toString())) {
                        if (userCountryCodeList.contains(countryCode)) {
                            if (!succList.contains(countryCode)) {
                                succList.add(countryCode);
                            }
                            } else if (!failList.contains(countryCode)) {
                            failList.add(countryCode);
                            //检测失败的地址进行新增
                            WlMailingAddressDTO wlMailingAddressDTO = new WlMailingAddressDTO();
                            for (int j = 0; j < addressArray.size(); j++) {
                                JSONObject addressObj = addressArray.getJSONObject(j);
                                if (addressObj.get("country").equals(countryCode)) {
                                    JSONObject data = JSONObject.parseObject(addressObj.getString("data"));
                                    wlMailingAddressDTO.setCountry(data.getString("country"));
                                    wlMailingAddressDTO.setCountryName(data.getString("countryName"));
                                    wlMailingAddressDTO.setContactPerson(data.getString("contactPerson"));
                                    wlMailingAddressDTO.setMobileNo(data.getString("mobileNo"));
                                    wlMailingAddressDTO.setPhoneCountry(data.getString("phoneCountry"));
                                    wlMailingAddressDTO.setZip(data.getString("zip"));
                                    wlMailingAddressDTO.setProvince(data.getString("province"));
                                    wlMailingAddressDTO.setProvinceCode(data.getString("provinceCode"));
                                    wlMailingAddressDTO.setCity(data.getString("city"));
                                    wlMailingAddressDTO.setCityCode(data.getString("cityCode"));
                                    wlMailingAddressDTO.setAddress(data.getString("address"));
                                    wlMailingAddressDTO.setAddress2(data.getString("address2"));
                                    wlMailingAddressDTO.setBuyerAddressType("residential");
                                    wlMailingAddressDTO.setPlatform("PC");
                                    wlMailingAddressDTO.setOwnerSeq(buyerId);
                                    WlMailingAddressResultDTO wlMailingAddressResultDTO = wlMailingAddressRemoteService.createMailingAddress(wlMailingAddressDTO, buyerId);
                                    if (!addAddressResult.contains(countryCode)) {
                                        addAddressResult.add(countryCode);
                                    }
                                }
                            }
                            }
                        }


                        else{
                            notMatchCountry.add(countryCode.toString());
                        }

         }
        }

        JSONObject resultJson = new JSONObject();
        resultJson.put("checkAddressPass", succList);
        resultJson.put("checkAddressFail",failList);
        resultJson.put("AddAddressSuccess",addAddressResult);
        resultJson.put("notMatchCountry",notMatchCountry);
        if(notMatchCountry.isEmpty()) {
            resultJson.put("success",true);
        } else {
            resultJson.put("success",false);
        }
        log.info("checkAddressResult:"+resultJson);
       return resultJson;
    }

    //数据池检测使用
    public JSONObject checkAddressExistV2(Long buyerId,String countryList ) throws Exception {

        List<Object> succList= new ArrayList<>();
        List<Object> failList = new ArrayList<>();
        List<Object> addAddressResult = new ArrayList<>();
        List<Object>  notMatchCountry = new ArrayList<>();
        List countryCodeList = Arrays.asList(countryList.split(","));
        JSONArray addressArray = JSONArray.parseArray(Constant.ADDRESS_DATA);
        JSONObject response = HsfUtil.getMailingAddressInfoV2(buyerId);
        JSONArray userAddressList = null;
        if (response!=null) {
            userAddressList = response.getJSONArray("addressList");
        }
        String supportCountry = "BR,ES,US,FR,RU,DE,PT,UK,KR";

        //传入国家标未none时，删除该用户所有地址
        if("None".equals(countryList) && response != null){
            for(int i=0;i<userAddressList.size();i++){
                Long addressId = userAddressList.getJSONObject(i).getLong("id");
                WlMailingAddressDTO wlMailingAddressDTO = new WlMailingAddressDTO();
                wlMailingAddressDTO.setId(addressId);
                WlMailingAddressResultDTO wlMailingAddressResultDTO = wlMailingAddressRemoteService.deleteMailingAddress(wlMailingAddressDTO,buyerId);
                if (!wlMailingAddressResultDTO.isSuccess()){
                    log.info(buyerId.toString() +"删除地址失败："+ addressId.toString());
                }
            }
        }
        //该用户有地址
        else if (response != null && response.getBoolean("success")) {
            List<String> userCountryCodeList = new ArrayList<>();
            for(int i = 0;i <userAddressList.size();i++) {
                JSONObject address =userAddressList.getJSONObject(i);
                String userCountryCode = address.getString("country");
                userCountryCodeList.add(userCountryCode);
            }
            for (Object countryCode : countryCodeList) {
//                String supportCountry = "BR,ES,US,FR,RU,DE,PT,UK";
                if (supportCountry.contains(countryCode.toString())) {
                    if (userCountryCodeList.contains(countryCode)) {
                        if (!succList.contains(countryCode)) {
                            succList.add(countryCode);
                        }
                    } else if (!failList.contains(countryCode)) {
                        failList.add(countryCode);
                        addAddress(buyerId,countryCode);

                    }
                }

                else{
                    notMatchCountry.add(countryCode.toString());
                }

            }
        }
        //该用户地址为null，新增传入地址code对应的地址
        else if (response == null) {
            for (Object countryCode:countryCodeList) {
                addAddress(buyerId,countryCode);
            }
        }

        JSONObject resultJson = new JSONObject();
        resultJson.put("checkAddressPass", succList);
        resultJson.put("checkAddressFail",failList);
        resultJson.put("AddAddressSuccess",addAddressResult);
        resultJson.put("notMatchCountry",notMatchCountry);
        if(notMatchCountry.isEmpty()) {
            resultJson.put("success",true);
        } else {
            resultJson.put("success",false);
        }
        log.info("checkAddressResult:"+resultJson);
        return resultJson;
    }


    @Override
    public ResultDTO createAndMockWrongAddress (String params, SystemDTO systemDTO) throws Exception{
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        long buyerId = Long.parseLong(jsonObject.getString(Constant.BUYER_ID));
        String countryCode = jsonObject.getString(Constant.COUNTRY_CODE);
        String addressType = jsonObject.getString(Constant.SUGGEST_ADDRESS_TYPE);
        WlMailingAddressResultDTO wlMailingAddressResultDTO =addAddress(buyerId,countryCode);
        long snapshotId = wlMailingAddressResultDTO.getDataObject().getSnapshotId();
        JSONObject suggestAddress = buildSuggestAddressObj(countryCode,addressType);
        log.warn("suggestAddress result:{}",  JSON.toJSONString(suggestAddress));
        JSONObject response = HsfUtil.cainiaoSuggestAddress(buyerId,snapshotId,suggestAddress);
        resultDTO.setSuccess(true);
        resultDTO.setData(response.toJSONString());
        resultDTO.setMessage(response.toJSONString());
        return resultDTO;
    }

    public JSONObject buildSuggestAddressObj(String countryCode,String addressType){
        JSONObject obj= new JSONObject();
        switch (addressType) {
            //报错类型是division
            case "省份/大区不在标准区划中":
                obj.put("addressErrorType","invalid_province");
                break;
            case "城市不在标准区划中":
                obj.put("addressErrorType","invalid_city");
                break;
            case "区划父子级关系不匹配":
                obj.put("addressErrorType","parent_division_mismatch");
                break;
            //报错类型是postcode
            case "邮编和区划不匹配":
                obj.put("addressErrorType","zip_division_mismatch");
                break;
            case "邮编不在标准邮编中":
                obj.put("addressErrorType","invalid_zip_code");
                break;
            //报错类型是unknow
            case "工单":
                obj.put("addressErrorType","from_ticket");
                break;
            case "CP回传错误地址":
                obj.put("addressErrorType","cp_error_address");
                break;
            case "CP改写正确地址":
                obj.put("addressErrorType","cp_right_address");
                break;
        }
        obj.put("detailAddress","this is suggested detail address");
        obj.put("postCode","74883");
        JSONArray addressArray = JSONArray.parseArray(Constant.SUGGEST_ADDRESS);
        log.warn("addressArray result:{}",  JSON.toJSONString(addressArray));
        for (int i =0;i<=addressArray.size();i++){
            JSONObject addressObj = addressArray.getJSONObject(i);
            if (addressObj.getString("country").equals(countryCode)) {
                JSONArray divisions = addressObj.getJSONArray("divisions");
                obj.put("divisions", divisions);
                break;
            }
        }
        log.warn("buildSuggestAddressObj result:{}",  JSON.toJSONString(obj));
        return obj;
    }

    public WlMailingAddressResultDTO addAddress(Long buyerId, Object countryCode) {

        JSONArray addressArray = JSONArray.parseArray(Constant.ADDRESS_DATA);
        WlMailingAddressDTO wlMailingAddressDTO = new WlMailingAddressDTO();
        WlMailingAddressResultDTO wlMailingAddressResultDTO = new WlMailingAddressResultDTO();
        for (int j = 0; j < addressArray.size(); j++) {
            JSONObject addressObj = addressArray.getJSONObject(j);
            if (addressObj.get("country").equals(countryCode)) {
                JSONObject data = JSONObject.parseObject(addressObj.getString("data"));
                wlMailingAddressDTO.setCountry(data.getString("country"));
                wlMailingAddressDTO.setCountryName(data.getString("countryName"));
                wlMailingAddressDTO.setMobileNo(data.getString("mobileNo"));
                wlMailingAddressDTO.setPhoneCountry(data.getString("phoneCountry"));
                wlMailingAddressDTO.setZip(data.getString("zip"));
                wlMailingAddressDTO.setProvince(data.getString("province"));
                wlMailingAddressDTO.setProvinceCode(data.getString("provinceCode"));
                wlMailingAddressDTO.setCity(data.getString("city"));
                wlMailingAddressDTO.setCityCode(data.getString("cityCode"));
                wlMailingAddressDTO.setBuyerAddressType("residential");
                wlMailingAddressDTO.setPlatform("PC");
                wlMailingAddressDTO.setOwnerSeq(buyerId);
                if (addressObj.get("country").equals("KR")){
                    wlMailingAddressDTO.setContactPerson("한국어");
                    wlMailingAddressDTO.setAddress("한국어");
                    wlMailingAddressDTO.setAddress2("한국어");
                } else {
                    wlMailingAddressDTO.setContactPerson(data.getString("contactPerson"));
                    wlMailingAddressDTO.setAddress(data.getString("address"));
                    wlMailingAddressDTO.setAddress2(data.getString("address2"));
                }
                if(data.containsKey("cpf")){
                    wlMailingAddressDTO.setCpf(data.getString("cpf"));
                }
                if(data.containsKey("firstName")) {
                    wlMailingAddressDTO.setFirstName(data.getString("firstName"));
                }
                if(data.containsKey("lastName")) {
                    wlMailingAddressDTO.setLastName(data.getString("lastName"));
                }
                if(data.containsKey("birthday")) {
                    wlMailingAddressDTO.setBirthday(data.getString("birthday"));
                }
                if (data.containsKey("passportNo")){
                    wlMailingAddressDTO.setPassportNo(data.getString("passportNo"));
                }
                if(data.containsKey("fullName")){
                    wlMailingAddressDTO.setFullName(data.getString("fullName"));
                }
                if(data.containsKey("passportNoDate")){
                    wlMailingAddressDTO.setPassportNoDate(data.getString("passportNoDate"));
                }
                if(data.containsKey("district")){
                    wlMailingAddressDTO.setDistrict(data.getString("district"));
                }
                wlMailingAddressResultDTO = wlMailingAddressRemoteService.createMailingAddress(wlMailingAddressDTO, buyerId);

            }
        }
    return wlMailingAddressResultDTO;

    }
    @Override
    public WlMailingAddressResultDTO addDefaultAddress(Long buyerId, String countryCode) {
        JSONArray addressArray = JSONArray.parseArray(Constant.ADDRESS_DATA);
        WlMailingAddressDTO wlMailingAddressDTO = new WlMailingAddressDTO();
        WlMailingAddressResultDTO wlMailingAddressResultDTO = new WlMailingAddressResultDTO();
        for (int j = 0; j < addressArray.size(); j++) {
            JSONObject addressObj = addressArray.getJSONObject(j);
            if (addressObj.get("country").equals(countryCode)) {
                JSONObject data = JSONObject.parseObject(addressObj.getString("data"));
                wlMailingAddressDTO.setCountry(data.getString("country"));
                wlMailingAddressDTO.setCountryName(data.getString("countryName"));
                wlMailingAddressDTO.setMobileNo(data.getString("mobileNo"));
                wlMailingAddressDTO.setPhoneCountry(data.getString("phoneCountry"));
                wlMailingAddressDTO.setZip(data.getString("zip"));
                wlMailingAddressDTO.setProvince(data.getString("province"));
                wlMailingAddressDTO.setProvinceCode(data.getString("provinceCode"));
                wlMailingAddressDTO.setCity(data.getString("city"));
                wlMailingAddressDTO.setCityCode(data.getString("cityCode"));
                wlMailingAddressDTO.setBuyerAddressType("residential");
                wlMailingAddressDTO.setPlatform("PC");
                wlMailingAddressDTO.setOwnerSeq(buyerId);
                wlMailingAddressDTO.setIsDefault(true);
                wlMailingAddressDTO.setDefaultShipping(true);
                if (addressObj.get("country").equals("KR")){
                    wlMailingAddressDTO.setContactPerson("한국어");
                    wlMailingAddressDTO.setAddress("한국어");
                    wlMailingAddressDTO.setAddress2("한국어");
                } else {
                    wlMailingAddressDTO.setContactPerson(data.getString("contactPerson"));
                    wlMailingAddressDTO.setAddress(data.getString("address"));
                    wlMailingAddressDTO.setAddress2(data.getString("address2"));
                }
                if(data.containsKey("cpf")){
                    wlMailingAddressDTO.setCpf(data.getString("cpf"));
                }
                if(data.containsKey("firstName")) {
                    wlMailingAddressDTO.setFirstName(data.getString("firstName"));
                }
                if(data.containsKey("lastName")) {
                    wlMailingAddressDTO.setLastName(data.getString("lastName"));
                }
                if(data.containsKey("birthday")) {
                    wlMailingAddressDTO.setBirthday(data.getString("birthday"));
                }
                if (data.containsKey("passportNo")){
                    wlMailingAddressDTO.setPassportNo(data.getString("passportNo"));
                }
                if(data.containsKey("fullName")){
                    wlMailingAddressDTO.setFullName(data.getString("fullName"));
                }
                if(data.containsKey("passportNoDate")){
                    wlMailingAddressDTO.setPassportNoDate(data.getString("passportNoDate"));
                }
                if(data.containsKey("district")){
                    wlMailingAddressDTO.setDistrict(data.getString("district"));
                }

                wlMailingAddressResultDTO = wlMailingAddressRemoteService.createMailingAddress(wlMailingAddressDTO, buyerId);

            }
        }
        return wlMailingAddressResultDTO;

    }
    @Override
    public ResultDTO delectAddress(Long buyerId, Long addressId) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        WlMailingAddressDTO wlMailingAddressDTO = new WlMailingAddressDTO();
        wlMailingAddressDTO.setId(addressId);
        WlMailingAddressResultDTO wlMailingAddressResultDTO = wlMailingAddressRemoteService.deleteMailingAddress(wlMailingAddressDTO,buyerId);
        if (!wlMailingAddressResultDTO.isSuccess()){
            resultDTO.setMessage(buyerId.toString() +"删除地址失败："+ addressId.toString());
            log.info(buyerId.toString() +"删除地址失败："+ addressId.toString());
        } else {

        }
        resultDTO.setMessage(buyerId.toString()+"删除地址成功");
        return resultDTO;
    }

    public static JSONArray fileToJson(String fileName) {
        JSONArray array = null;
        try
        {
            String path = Thread.currentThread().getContextClassLoader().getResource("").getPath() + fileName;
            log.info("~~~path~~~~"+path);

            List<String> lines = Files.readLines(new File(path), Charsets.UTF_8);
            StringBuilder builder = new StringBuilder();
            for (String line : lines) {
                builder.append(line);
            }
            System.out.println(builder.toString());
            array = JSONObject.parseArray(builder.toString());
        } catch (Exception e) {
            System.out.println(fileName + "文件读取异常" + e);
        }
        return array;
    }
}


