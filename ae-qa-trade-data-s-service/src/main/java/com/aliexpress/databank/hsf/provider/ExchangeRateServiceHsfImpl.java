package com.aliexpress.databank.hsf.provider;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.ecommerce.exchange.dataobject.*;

import com.alibaba.ecommerce.exchange.facade.GlobalExchangeFacade;
import com.alibaba.ecommerce.exchange.request.ExchangePriceRequest;
import com.alibaba.ecommerce.module.Response;
import com.alibaba.global.money.Money;
import com.aliexpress.databank.constant.Constant;
import com.taobao.diamond.client.Diamond;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import com.aliexpress.databank.hsf.ExchangeRateServiceHsf;

import javax.money.MonetaryAmount;
import javax.money.MonetaryContext;
import javax.money.MonetaryContextBuilder;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.text.NumberFormat;



@HSFProvider(serviceInterface = ExchangeRateServiceHsf.class, serviceGroup = "HSF", clientTimeout = 20000)
public class ExchangeRateServiceHsfImpl implements ExchangeRateServiceHsf{


    @Autowired
    private GlobalExchangeFacade globalExchangeFacade;

//    @Override
//    public org.javamoney.moneta.Money getExchangePrice(MonetaryAmount monetaryAmount, String curCurrency, String targetCurrency) {
//        // 预发金额展示时，需要调用汇率二方包将预发金额转换成钱包的币种展示
//        ExchangePriceRequest request = getExchangePriceRequest(monetaryAmount, curCurrency, targetCurrency);
//        Response<GlobalPriceDTO> exchangePrice = globalExchangeFacade.getExchangePrice(request);
//        return getResponse(curCurrency, exchangePrice);
//    }
    /**
     * 组装请求汇率二方包服务request
     *
     * @param monetaryAmount {@link MonetaryAmount}
     * @param curCurrency    当前金额币种
     * @param targetCurrency 目标金额币种
     * @return {@link ExchangePriceRequest}
     */
    private ExchangePriceRequest getExchangePriceRequest(MonetaryAmount monetaryAmount, String curCurrency, String targetCurrency) {
        ExchangePriceRequest request = ExchangePriceRequest.builder()
                .baseAmount(com.alibaba.global.money.Money.of(org.javamoney.moneta.Money.of(monetaryAmount.getNumber(), curCurrency)))
                .quoteCur(targetCurrency)
                .build();
        return request;
    }
    /**
     * @param curCurrency   当前金额币种
     * @param exchangePrice {@link GlobalPriceDTO}
     * @return {@link org.javamoney.moneta.Money}
     */
    @NotNull
    private org.javamoney.moneta.Money getResponse(String curCurrency, Response<GlobalPriceDTO> exchangePrice) {
        if (!exchangePrice.isSuccess()) {
//            logger.error(
//                    "|DOMAIN|WalletAssetServiceFacade|getBonusBalance|handled pendingBalance failed," +
//                            " curCurrency is:{},errorCode is:{} ", curCurrency, exchangePrice.getErrorCode());
//            throw new AssetException(ErrorCode.BONUS_EXCHANGE_RATE_ERROR,
//                    "could not exchange rate, response code is" + exchangePrice.getErrorCode());
        }
        GlobalPriceDTO globalPriceDTO = exchangePrice.getModule();
        com.alibaba.global.money.Money money = globalPriceDTO.getQuotePrice();
        GlobalRateInfo rateInfo = globalPriceDTO.getRateInfo();
        final MonetaryContext context =
                MonetaryContextBuilder.of(org.javamoney.moneta.Money.class)
                        // 转换时汇率
                        .set("exchangeRate", rateInfo.getRate().toString())
                        // 汇率生效时间
                        .set("validTime", rateInfo.getValidTime() != null ? rateInfo.getValidTime().getTime() : null)
                        .build();
        return org.javamoney.moneta.Money.of(money.getNumber(), money.getCurrency(), context);
    }

    @Override
    public  Map<String, Response<CurrencyType>> getCurrencyDiff() throws Exception {

        Map<String, Response<CurrencyType>> data = new LinkedHashMap<>();
        Map<String, List<String>> symbolMap = getSymbolData();

        Set<String> timeKeys = symbolMap.keySet();

        Iterator<String> iterator = timeKeys.iterator();
        while (iterator.hasNext()) {
            String currString = iterator.next();

            Response<CurrencyType> currencyType1 = globalExchangeFacade.getCurrency(currString);
            List<String> format = symbolMap.get(currString);
            data.put(format.get(0),currencyType1);
        }
        return data;
    }
    @Override
    public  Map<String,List<String>> getExchangePriceDiff(Money baseAmount,com.alibaba.intl.commons.framework.type.Money baseAmount1,Language language) throws Exception{

        Map<String, List<String>> symbolData = getSymbolData();
        Set<String> currKeys = symbolData.keySet();
        Iterator<String> iterator = currKeys.iterator();

        NumberFormat numberFormat = NumberFormat.getNumberInstance();
        numberFormat.setMinimumFractionDigits(2);

        Map<String,List<String>> data = new HashMap<String, List<String>>();

        while (iterator.hasNext()) {
            String currString = iterator.next();

            List<String> eachCurryInfo = symbolData.get(currString);
            String symbol = getCurrency(currString).getModule().getCurrencySymbol();
            String format = eachCurryInfo.get(1);

            String expectResult =null;

            List<String> eachCurrCalulate = new ArrayList<>();

            GlobalPriceDTO quotePriceForNew = getExchangePrice(baseAmount,currString,language).getModule();

            GlobalPriceForOldAeDTO quotePriceForOld = getExchangePrice(baseAmount1,currString,language).getModule();
            String amount = numberFormat.format(quotePriceForNew.getQuotePrice().getAmount().setScale(2,BigDecimal.ROUND_HALF_EVEN));

            if(format != null && format.equals("{symbol} {amount}")){
                expectResult = symbol+" "+amount;
            }
            if(format != null && format.equals("{amount} {symbol}")){
                expectResult = amount+" "+symbol;
            }
            eachCurrCalulate.add(expectResult);
            eachCurrCalulate.add(quotePriceForNew.getFormatPrice());
            eachCurrCalulate.add(quotePriceForOld.getFormatPrice());

            data.put(currString,eachCurrCalulate);
        }
        return data;

    }

    @Override
    public  Map<String,Map<String,List<String>>> getExchangeLanguageDiff(Money baseAmount,com.alibaba.intl.commons.framework.type.Money baseAmount1) throws Exception{

        Map<String,Map<String,List<String>>> data= new  HashMap<String,Map<String,List<String>>>();

        Map<String, List<String>> symbolData = getSymbolData();
        Set<String> currKeys = symbolData.keySet();
        Iterator<String> iterator = currKeys.iterator();

        //List<String> specialCurrency = Arrays.asList("JYP","CLP","KRW","BIF","BYR","DJF","GNF","ISK","KMF","PYG","RWF","UGX","VUV","XAF","XOF","XPF");

        List<String> lang = Arrays.asList("EN","JA","FR","DE","NL","IT","ES","KO","PT","DA","FI","NB","SV","RU","PL","TR","UK","AR","HR","CS","EL","HE","RO","SK","CA","HU");

        NumberFormat numberFormat = NumberFormat.getNumberInstance();

        while (iterator.hasNext()) {
            String currString = iterator.next();

            List<String> eachCurryInfo = symbolData.get(currString);
            String symbol = eachCurryInfo.get(0);
            String formatIn = eachCurryInfo.get(1);
            String formatDeci = eachCurryInfo.get(2);

            Map<String,List<String>> formatData = new  HashMap<String, List<String>>();

            for(String eachLang:lang){
                Language language = Language.valueOf(eachLang);
                GlobalPriceDTO quotePrice = globalExchangeFacade.getExchangePrice(baseAmount,currString,language).getModule();
                GlobalPriceForOldAeDTO quotePriceForOld = globalExchangeFacade.getExchangePrice(baseAmount1,currString,language).getModule();

                String amount =null;

                if(formatDeci!= null && formatDeci.equals("#,###")){
                    numberFormat.setMinimumFractionDigits(0);
                    amount = numberFormat.format(quotePrice.getQuotePrice().getAmount().toBigInteger());

                }else {
                    numberFormat.setMinimumFractionDigits(2);
                    amount = numberFormat.format(quotePrice.getQuotePrice().getAmount().setScale(2,BigDecimal.ROUND_HALF_EVEN));
                }

                String actFormatNew = quotePrice.getFormatPrice();
                String actFormatOld = quotePriceForOld.getFormatPrice();


                String expectFormat = null;

                if(currString.equals("EUR")){
                    if(eachLang.equals("FR")||eachLang.equals("ES")||eachLang.equals("PL")||eachLang.equals("DE")){
                        expectFormat = amount+" "+symbol;
                    }else {
                        expectFormat = symbol+" "+amount;
                    }
                }else {
                    if(formatIn != null && formatIn.equals("{symbol} {amount}")){
                        expectFormat = symbol+" "+ amount;
                    }
                    if(formatIn != null && formatIn.equals("{amount} {symbol}")){
                        expectFormat = amount+" "+symbol;
                    }

                }

                if((expectFormat!=null && !expectFormat.equals(actFormatNew)) || (expectFormat!=null && !expectFormat.equals(actFormatOld)) ){
                    List<String> format = new ArrayList<>();
                    format.add(expectFormat);
                    format.add(actFormatNew);
                    format.add(actFormatOld);
                    formatData.put(eachLang,format);
                }


            }
            data.put(currString,formatData);

        }

        return data;
    }


    @Override
    public Response<CurrencyType> getCurrency(String currencyType) throws Exception{
        Response<CurrencyType> getCurry= globalExchangeFacade.getCurrency(currencyType);
        return getCurry;

    }


    @Override
    public Map<String,List<String>> getSymbolData(){

        Map<String, List<String>> data = new HashMap<String,List<String>>();

        try {
            String configInfo = Diamond
                    .getConfig("com.alibaba.ae.qa.databank.exchageRate", "ae-qa-databank", ********);

            Constant.EXCHANGE_RATE = configInfo;


            String[] formatData = configInfo.split("\n");
            if (formatData.length!=0){
                for (String linData:formatData ){

                    String[] tmp = linData.split("\"");

                    List<String> formatInfo=new ArrayList<>();

                    formatInfo.add(tmp[11]);
                    formatInfo.add(tmp[15]);
                    formatInfo.add(tmp[13]);

                    data.put(tmp[1],formatInfo);

                }

            }
            return data;

        }catch (Exception e){
            return data;
        }
    }

    @Override
    public  Response<GlobalRateInfo> getExchangeRate(String baseCur, String quoteCur,boolean isBackup) throws Exception{
        Response<GlobalRateInfo>  rareInfo= globalExchangeFacade.getExchangeRate(baseCur,quoteCur,isBackup);
        return rareInfo;
    }


    @Override
    public  List<Response<GlobalRateInfo>> getExchangeRateForBackCheck(boolean isBackup) throws Exception{
        List<Response<GlobalRateInfo>> data = new ArrayList<>();
        Response<GlobalRateInfo>  rateInNoBack= globalExchangeFacade.getExchangeRate(null,null);
        Response<GlobalRateInfo>  rateInfoBack= globalExchangeFacade.getExchangeRate(null,null,isBackup);

        data.add(rateInNoBack);
        data.add(rateInfoBack);

        return data;
    }

    @Override
    public Response<GlobalRateInfo> getExchangePreSaleRate(String baseCur, String quoteCur)throws Exception {
        return globalExchangeFacade.getExchangePreSaleRate(baseCur,quoteCur);
    }

    @Override
    public  Response<GlobalRateInfo> getExchangeRate(String baseCur, String quoteCur) throws Exception{
        Response<GlobalRateInfo>  rareInfo= globalExchangeFacade.getExchangeRate(baseCur,quoteCur);
        return rareInfo;
    }


    @Override
    public Response<List<GlobalRateInfo>> getExchangeRateList(String baseCur, List<String> quoteCurList)throws Exception{
        Response<List<GlobalRateInfo>> rateList = globalExchangeFacade.getExchangeRateList(baseCur,quoteCurList);
        return rateList;

    }

    @Override
    public  Response<GlobalPriceDTO> getExchangePrice(Money baseAmount, String quoteCur, Language language)throws Exception{

        Response<GlobalPriceDTO> getPrice = globalExchangeFacade.getExchangePrice(baseAmount,quoteCur,language);
        return getPrice;

    }

    @Override
    public Response<GlobalRangePriceDTO> getExchangeRangePrice(Money minBaseAmount,
                                                               Money maxBaseAmount,
                                                               String quoteCur,
                                                               String separator,
                                                               Language language)  throws Exception{

        Response<GlobalRangePriceDTO> getRangPrice = globalExchangeFacade.getExchangeRangePrice(minBaseAmount,maxBaseAmount,quoteCur,separator,language);
        return getRangPrice;

    }

    @Override
    public Response<GlobalPriceForOldAeDTO> getExchangePrice(com.alibaba.intl.commons.framework.type.Money baseAmount, String quoteCur)throws Exception {
        return globalExchangeFacade.getExchangePrice(baseAmount, quoteCur, null);
    }

    @Override
    public Response<GlobalPriceDTO> getExchangePrice(Money baseAmount, String quoteCur)throws Exception {
        return globalExchangeFacade.getExchangePrice(baseAmount,quoteCur);
    }




    @Override
    public String formatPrice(Money price, Language language)throws Exception {
        String formate = globalExchangeFacade.formatPrice(price,language);
        return formate;
    }

    @Override
    public String formatPrice(Money price)throws Exception {
        String formate = globalExchangeFacade.formatPrice(price);
        return formate;
    }

    @Override
    public String formatPrice(com.alibaba.intl.commons.framework.type.Money price, Language language)throws Exception {
        String formate = globalExchangeFacade.formatPrice(price,language);
        return formate;
    }

    @Override
    public String formatPrice(com.alibaba.intl.commons.framework.type.Money price) throws Exception{
        String formate = globalExchangeFacade.formatPrice(price);
        return formate;
    }

    @Override
    public String formatRangePrice(Money leftPrice, Money rightPrice, Language language)throws Exception {
        String formate = globalExchangeFacade.formatRangePrice(leftPrice,rightPrice,language);
        return formate;
    }

    @Override
    public String formatRangePrice(com.alibaba.intl.commons.framework.type.Money leftPrice,
                                   com.alibaba.intl.commons.framework.type.Money rightPrice,
                                   Language language)throws Exception {

        String formate = globalExchangeFacade.formatRangePrice(leftPrice,rightPrice,language);
        return formate;

    }
    @Override
    public Response<List<GlobalPriceDTO>> batchGetExchangePrice(List<Money> baseAmountList, List<String> quoteCurList) throws Exception{
        Response<List<GlobalPriceDTO>> batchPrice = globalExchangeFacade.batchGetExchangePrice(baseAmountList,quoteCurList);
        return batchPrice;
    }

    @Override
    public Money calculateExchangePrice(Money baseAmount, String quoteCur, Double rate, RoundingMode roundingMode) throws Exception {
        BigDecimal rat= new BigDecimal(rate);

        Money price = globalExchangeFacade.calculateExchangePrice(baseAmount,quoteCur,rat,roundingMode);
        return price;

    }

    @Override
    public Response<GlobalPriceForOldAeDTO> getExchangePrice(com.alibaba.intl.commons.framework.type.Money baseAmount, String quoteCur, Language language) throws Exception {

        Response<GlobalPriceForOldAeDTO> getExchangePrice = globalExchangeFacade.getExchangePrice(baseAmount,quoteCur,language);
        return getExchangePrice;
    }

    @Override
    public Response<GlobalRangePriceOldDTO> getExchangeRangePrice(com.alibaba.intl.commons.framework.type.Money minBaseMoney,
                                                               com.alibaba.intl.commons.framework.type.Money maxBaseMoney,
                                                               String quoteCur,
                                                               String separator,
                                                               Language language) throws Exception{
        Response<GlobalRangePriceOldDTO> getRangePrice = globalExchangeFacade.getExchangeRangePrice(minBaseMoney,maxBaseMoney,quoteCur,separator,language);
        return getRangePrice;

    }


    @Override
    public Response<GlobalPriceForOldAeDTO> getExchangePreSalePrice(com.alibaba.intl.commons.framework.type.Money baseAmount, String quoteCur) throws Exception{

        Response<GlobalPriceForOldAeDTO> getPreSalePrice = globalExchangeFacade.getExchangePreSalePrice(baseAmount,quoteCur);
        return getPreSalePrice;
    }

    @Override
    public Response<GlobalPriceDTO> getExchangePreSalePrice(Money baseAmount, String quoteCur) throws Exception {
        Response<GlobalPriceDTO> getPreSalePrice = globalExchangeFacade.getExchangePreSalePrice(baseAmount,quoteCur);
        return getPreSalePrice;
    }


    @Override
    public Response<GlobalRangePriceOldDTO> getExchangeRangePrice(com.alibaba.intl.commons.framework.type.Money minBaseAmount, com.alibaba.intl.commons.framework.type.Money maxBaseAmount, String quoteCur) throws Exception{
        return globalExchangeFacade.getExchangeRangePrice(minBaseAmount, maxBaseAmount, quoteCur, null,null);
    }

    @Override
    public Response<GlobalRangePriceDTO> getExchangeRangePrice(Money minBaseAmount, Money maxBaseAmount, String quoteCur) throws Exception {
        return globalExchangeFacade.getExchangeRangePrice(minBaseAmount, maxBaseAmount, quoteCur, null, null);
    }
}
