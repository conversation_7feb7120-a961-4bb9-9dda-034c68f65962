package com.aliexpress.databank.hsf.provider;

import com.alibaba.boot.hsf.annotation.HSFProvider;

import com.alibaba.common.lang.StringUtil;
import com.alibaba.ecommerce.module.Response;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.global.landlord.LandlordContext;
import com.alibaba.global.order.management.api.facade.OrderQueryForBuyerFacade;
import com.alibaba.global.order.management.api.model.TradeOrderDTO;
import com.alibaba.global.qa.dc.model.builder.QueryResultBuilder;
import com.alibaba.global.qa.dc.model.param.SystemDTO;
import com.alibaba.global.qa.dc.model.result.QueryResultUnit;
import com.alibaba.global.qa.dc.model.result.ResultDTO;

import com.aliexpress.databank.constant.TradeScenario;
import com.aliexpress.databank.dataobject.OrderDTO;
import com.aliexpress.databank.hsf.OrderPrepareService;


import com.taobao.eagleeye.EagleEye;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

@Slf4j
@HSFProvider(serviceInterface = OrderPrepareService.class, serviceGroup = "HSF", serviceVersion = "1.0.0", clientTimeout = 20000)
public class OrderPrepareServiceImpl implements OrderPrepareService {
//
//      private Gson gson = new Gson();
//    private Type type = new TypeToken<HashMap<String, Set<String>>>() {
//    }.getType();


    @Autowired
    private OrderQueryForBuyerFacade orderQueryForBuyerFacade;


    private Map<String, List<OrderDTO>> getOrders() throws Exception {
        String allScenariosURL = "https://pre-simulator.alibaba-inc.com/api/dataDriven/queryOrderIdsAndTags?bizCode=international|aliExpress";
        CloseableHttpClient httpclient = HttpClients.createDefault();
        HttpGet request = new HttpGet(allScenariosURL);
        HttpResponse response = httpclient.execute(request);
        JSONObject jsonObject = JSONObject.parseObject(EntityUtils.toString(response.getEntity(), "utf-8"));
        httpclient.close();

//        String jsonStr = "{\"caseResultId1\":{\"1000155540110181\":{\"orderId\":\"1000155540110181\",\"buyerEmail\":\"<EMAIL>\",\"buyerId\":\"1858900181\",\"orderTime\":\"2020-11-04 10:12:33\",\"linkType\":\"base\",\"linkTags\":[\"USD报价\",\"订单创建-单子订单\",\"成功请求\",\"订单创建-多子订单\",\"订单创建\",\"订单渲染-来源：BUY_NOW\"]},\"1000159670030181\":{\"orderId\":\"1000159670030181\",\"buyerEmail\":\"<EMAIL>\",\"buyerId\":\"1858900181\",\"orderTime\":\"2020-11-04 10:13:33\",\"linkType\":\"test\",\"linkTags\":[\"EUR报价 \",\"订单创建-美国征税\",\"成功请求\",\"订单创建-多子订单\",\"订单创建\",\"订单渲染-来源：BUY_NOW\"]}},\"caseResultId2\":{\"1000157060030181\":{\"orderId\":\"1000157060030181\",\"buyerEmail\":\"<EMAIL>\",\"buyerId\":\"1858900181\",\"orderTime\":\"2020-11-04 10:12:33\",\"linkType\":\"base\",\"linkTags\":[\"USD报价\",\"订单创建-DDP\",\"成功请求\",\"订单创建-多子订单\",\"订单创建\",\"订单渲染-来源：BUY_NOW\"]}}}";
//                JSONObject jsonObject = JSONObject.parseObject(jsonStr);

        Map<String, List<OrderDTO>> maps = new HashMap<String, List<OrderDTO>>();
        List<OrderDTO> oneson = new ArrayList<>();
        List<OrderDTO> severalson = new ArrayList<>();
        List<OrderDTO> threeC = new ArrayList<>();
        List<OrderDTO> ustax = new ArrayList<>();
        List<OrderDTO> autax = new ArrayList<>();
        List<OrderDTO> ddp = new ArrayList<>();
        List<OrderDTO> sellerdiscount = new ArrayList<>();
        List<OrderDTO> uspost = new ArrayList<>();
        List<OrderDTO> eurpost = new ArrayList<>();
        List<OrderDTO> rupost = new ArrayList<>();
        List<OrderDTO> selfpickup = new ArrayList<>();
        List<OrderDTO> freeshipping = new ArrayList<>();

        List<OrderDTO> fullpiece = new ArrayList<>();
        List<OrderDTO> peMemberDiscount = new ArrayList<>();


        for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
            JSONObject value = JSONObject.parseObject(entry.getValue().toString());


            for (Map.Entry<String, Object> entryorder : value.entrySet()) {
                JSONObject orderjson = JSONObject.parseObject(entryorder.getValue().toString());
                OrderDTO orderDTO = new OrderDTO();
                String orderId = orderjson.getString("orderId");
                String buyerId = orderjson.getString("buyerId");

                orderDTO.setOrderId(orderId);
                orderDTO.setBuyerEmail(orderjson.getString("buyerEmail"));
                orderDTO.setLinkTags(orderjson.getJSONArray("linkTags"));
                orderDTO.setBuyId(buyerId);
                //从订单查询中查到订单状态
                //查询区域化接口
//                String staging=null;
                LandlordContext.setTenantSpec("AE_GLOBAL", -1);
//                if (StringUtil.isNotBlank(staging)) {
//                    EagleEye.putUserData("scm_project", staging);//临时处理
//
//                }
                Response<TradeOrderDTO> orderresult = orderQueryForBuyerFacade.queryTradeOrderById(Long.parseLong(buyerId), Long.parseLong(orderId));
                orderDTO.setPayStatus(orderresult.getModule().getPayStatus().toString());
                orderDTO.setDeliveryStatus(orderresult.getModule().getDeliverStatus().toString());


                if (orderDTO.getLinkTags().contains("订单创建-单子订单")) {
                    oneson.add(orderDTO);
                }
                //需把标签匹配上业务
                if (orderDTO.getLinkTags().contains("订单创建-多子订单")) {
                    severalson.add(orderDTO);
                }
                if (orderDTO.getLinkTags().contains("订单创建-3C服务保修")) {
                    threeC.add(orderDTO);
                }
                if (orderDTO.getLinkTags().contains("订单创建-美国征税")) {
                    ustax.add(orderDTO);
                }
                if (orderDTO.getLinkTags().contains("订单创建-澳洲征税")) {
                    autax.add(orderDTO);
                }
                if (orderDTO.getLinkTags().contains("订单创建-DDP")) {
                    ddp.add(orderDTO);
                }
                if (orderDTO.getLinkTags().contains("订单创建-店铺优惠：满立减")) {
                    sellerdiscount.add(orderDTO);
                }
                if (orderDTO.getLinkTags().contains("订单创建-报价币种-USD")) {
                    uspost.add(orderDTO);
                }
                if (orderDTO.getLinkTags().contains("订单创建-报价币种-EUR")) {
                    eurpost.add(orderDTO);
                }
                if (orderDTO.getLinkTags().contains("订单创建-报价币种-RUB")) {
                    rupost.add(orderDTO);
                }
                if (orderDTO.getLinkTags().contains("订单创建-单子订单")) {
                    selfpickup.add(orderDTO);
                }
                //需在页面新增这个参数识别
                if (orderDTO.getLinkTags().contains("订单创建-店铺优惠：店铺满包邮")) {
                    freeshipping.add(orderDTO);
                }
                //需在页面新增这个参数识别
                if (orderDTO.getLinkTags().contains("订单创建-店铺优惠：满件折")) {
                    fullpiece.add(orderDTO);
                }
                //需在页面新增这个参数识别
                if (orderDTO.getLinkTags().contains("订单创建-单品折扣：peMemberDiscount")) {
                    peMemberDiscount.add(orderDTO);
                }

//                orderList.add(orderDTO);

            }

        }
        maps.put(TradeScenario.ONE_SON, oneson);
        maps.put(TradeScenario.AU_TAX, autax);

        maps.put(TradeScenario.DDP, ddp);
        maps.put(TradeScenario.EUR_POST, eurpost);
        maps.put(TradeScenario.RU_POST, rupost);
        maps.put(TradeScenario.SELF_PICKUP, selfpickup);
        maps.put(TradeScenario.SELLER_DISCOUNT, sellerdiscount);
        maps.put(TradeScenario.SEVERAL_SON, severalson);
        maps.put(TradeScenario.THREE_C, threeC);
        maps.put(TradeScenario.US_POST, uspost);
        maps.put(TradeScenario.US_TAX, ustax);
        maps.put(TradeScenario.FREE_SHIPPING, freeshipping);
        maps.put(TradeScenario.FULL_PIECE, fullpiece);
        maps.put(TradeScenario.PE_MEMBER_DISCOUNT, peMemberDiscount);


        return maps;

    }

    @Override
    public ResultDTO queryCustomOrder(String params, SystemDTO systemDTO) throws Exception {


        List<OrderDTO> result = new ArrayList<>();
        JSONObject jsonObject = JSONObject.parseObject(params);
        String tradescenario = jsonObject.getString("tradescenario");
        Map<String, List<OrderDTO>> ordermaps = new HashMap<String, List<OrderDTO>>();
        ordermaps = getOrders();


        if (tradescenario.equals(TradeScenario.ONE_SON)) {
            result = ordermaps.get(TradeScenario.ONE_SON);

        } else if (tradescenario.equals(TradeScenario.AU_TAX)) {
            result = ordermaps.get(TradeScenario.AU_TAX);

        } else if (tradescenario.equals(TradeScenario.DDP)) {
            result = ordermaps.get(TradeScenario.DDP);

        } else if (tradescenario.equals(TradeScenario.EUR_POST)) {
            result = ordermaps.get(TradeScenario.EUR_POST);

        } else if (tradescenario.equals(TradeScenario.RU_POST)) {
            result = ordermaps.get(TradeScenario.RU_POST);

        } else if (tradescenario.equals(TradeScenario.SELF_PICKUP)) {
            result = ordermaps.get(TradeScenario.SELF_PICKUP);

        } else if (tradescenario.equals(TradeScenario.SEVERAL_SON)) {
            result = ordermaps.get(TradeScenario.SEVERAL_SON);

        } else if (tradescenario.equals(TradeScenario.THREE_C)) {
            result = ordermaps.get(TradeScenario.THREE_C);

        } else if (tradescenario.equals(TradeScenario.US_POST)) {
            result = ordermaps.get(TradeScenario.US_POST);

        } else if (tradescenario.equals(TradeScenario.US_TAX)) {
            result = ordermaps.get(TradeScenario.US_TAX);

        } else if (tradescenario.equals(TradeScenario.FREE_SHIPPING)) {
            result = ordermaps.get(TradeScenario.FREE_SHIPPING);

        } else if (tradescenario.equals(TradeScenario.FULL_PIECE)) {
            result = ordermaps.get(TradeScenario.FULL_PIECE);

        } else if (tradescenario.equals(TradeScenario.PE_MEMBER_DISCOUNT)) {
            result = ordermaps.get(TradeScenario.PE_MEMBER_DISCOUNT);

        } else {
            result = null;

        }

        Map<String, QueryResultUnit> data = new LinkedHashMap<>();
        ResultDTO resultDTO = new ResultDTO();


        Map<String, QueryResultUnit> orderLineFeature = QueryResultBuilder.buildQueryResult("订单查询", null, null, result);
        data.putAll(orderLineFeature);

        resultDTO.setData(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
        resultDTO.setSuccess(true);
        return resultDTO;


//    if ()


    }
}

