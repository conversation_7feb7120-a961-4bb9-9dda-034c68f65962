package com.aliexpress.databank.hsf.provider;

import com.ali.unit.rule.util.StringUtils;
import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.cobar.parser.ast.expression.primary.function.string.Y;
import com.alibaba.common.lang.StringUtil;
import com.alibaba.ecommerce.module.Response;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.global.landlord.LandlordContext;
import com.alibaba.global.money.Money;
import com.alibaba.global.order.management.api.facade.OrderQueryForBuyerFacade;
import com.alibaba.global.order.management.api.model.*;
import com.alibaba.global.order.management.constants.AttributeConstants;
import com.alibaba.global.qa.dc.model.builder.QueryResultBuilder;
import com.alibaba.global.qa.dc.model.param.SystemDTO;
import com.alibaba.global.qa.dc.model.result.QueryResultUnit;
import com.alibaba.global.qa.dc.model.result.ResultDTO;
import com.aliexpress.databank.constant.Constant;
import com.aliexpress.databank.dataobject.*;
import com.aliexpress.databank.hsf.OrderPriceService;
import com.aliexpress.databank.utils.FundCalculator;
import com.aliexpress.databank.utils.GetPrices;
import com.aliexpress.databank.utils.HsfUtil;
import com.aliexpress.databank.utils.MoneyConvertUtil;
import com.taobao.eagleeye.EagleEye;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.map.HashedMap;
import org.springframework.beans.factory.annotation.Autowired;
import javax.money.MonetaryAmount;
import java.math.BigDecimal;
import java.util.*;


@Slf4j
@HSFProvider(serviceInterface = OrderPriceService.class, serviceGroup = "HSF", serviceVersion = "1.0.0", clientTimeout = 20000)
public class OrderPriceServiceImpl implements OrderPriceService {

    @Autowired
    private OrderQueryForBuyerFacade orderQueryForBuyerFacade;


    @Override
    public ResultDTO getOrderDpPriceById(String params, SystemDTO systemDTO) throws Exception {
        String empId = systemDTO.getOperator();
        ResultDTO result = new ResultDTO();
        log.info(systemDTO.toString(),systemDTO.getEnv());
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long orderId = jsonObject.getLong(Constant.ORDER_ID);
        String staging = jsonObject.getString(Constant.STAGING);
        Map<String, QueryResultUnit> data = new LinkedHashMap<>();
        Response<TradeOrderDTO> tradeOrderDTOResponse = null;

        //查询区域化接口
        LandlordContext.setTenantSpec("AE_GLOBAL", -1);//debug时注释
        if (StringUtil.isNotBlank(staging)) {
            EagleEye.putUserData("dpath_env", staging);//隔离环境：scm_project

        }

        try {
            //Response<TradeOrderDTO> tradeOrderDTOResponse = orderQueryForBuyerFacade.queryTradeOrderById(jsonObject.getLong(Constant.BUYER_ID), orderId);
            tradeOrderDTOResponse = orderQueryForBuyerFacade.queryTradeOrderById(orderId);

        } catch (Exception e) {
            log.info(e.getMessage());
            result.setSuccess(false);
            result.setData("queryTradeOrderById() query npe." + EagleEye.getTraceId());
            log.info("TraceId:" + EagleEye.getTraceId());
            return result;
        }

        if (tradeOrderDTOResponse.isNotSuccess()) {
            log.info("queryTradeOrderById() query failed");
            result.setSuccess(false);
            result.setData("queryTradeOrderById() is false." + EagleEye.getTraceId());
            log.info("TraceId:" + EagleEye.getTraceId());
            return result;
        }



        String s = JSON.toJSONString(tradeOrderDTOResponse.getModule().getOrderLines(), SerializerFeature.DisableCircularReferenceDetect);
        List<TradeOrderLineDTO> tradeOrderLines = JSON.parseArray(s, TradeOrderLineDTO.class);

        List<Map<String, String>> orderLineFeatures = new ArrayList<>();
        for (TradeOrderLineDTO tradeOrderLineDTO : tradeOrderLines) {
            Map<String, String> orderLineFeature = tradeOrderLineDTO.getFeatures().getFeatureMap();
            orderLineFeatures.add(orderLineFeature);

        }

        TradePriceDTO tradePriceDTO = GetPrices.getTradePrice(tradeOrderDTOResponse.getModule());//价格获取

        Map<String, QueryResultUnit> orderPrice = QueryResultBuilder.buildQueryResult("交易主订单金额", null, GetPrices.getOrdertotalPricekey(), GetPrices.getTradePrice(tradeOrderDTOResponse.getModule()));
        Map<String, QueryResultUnit> orderLinePrice = QueryResultBuilder.buildQueryResult("交易子订单金额", null, GetPrices.getOrderPricekey(), GetPrices.getTradeLinePriceList(tradeOrderLines));
        Map<String, QueryResultUnit> orderLinefeaturePrice = QueryResultBuilder.buildQueryResult("交易子订单feature", null, GetPrices.getPdfPricekey(), orderLineFeatures);
//        Map<String, QueryResultUnit> intentionPrice = QueryResultBuilder.buildQueryResult("交易子订单支付币种金额", null, null, GetPrices.getIntentionPrice(tradePriceDTO));


        List<TradeLineDynamicDTO> dynamicPrices = new ArrayList<>();
        Map<String, QueryResultUnit> dynamicPrice = new HashMap<>();

        try {

            for (TradeLinePriceDTO tradeLinePriceDTO :tradePriceDTO.getTradeLinePriceDTO()){
                TradeLineDynamicDTO e =tradeLinePriceDTO.getTradeLineDynamicDTO();
                dynamicPrices.add(e);
            }

            if(dynamicPrices!=null){
                dynamicPrice = QueryResultBuilder.buildQueryResult("子订单量价", null, null, dynamicPrices);

            }

        } catch (Exception e) {
            log.info("getTradePrice failed");

        }

//           List<VolumePriceDTO> valumePrices = new ArrayList<>();
//        for (TradeOrderLineDTO tradeOrderLineDTO : tradeOrderLines) {
//            VolumePriceDTO volumePriceDTO = tradeOrderLineDTO.getVolumePrice();
//            if (volumePriceDTO != null) {
//                valumePrices.add(volumePriceDTO);
//            }
//
//        }
//
//        Map<String, QueryResultUnit> valumePrice = QueryResultBuilder.buildQueryResult("子订单量价valumePrice", null, null, valumePrices);


//        //买家视角
//        List<ShowPriceDTO> buyerPricevalue = new ArrayList<ShowPriceDTO>();
//
//        try {
//            buyerPricevalue = getBuyerPrice(tradeOrderDTOResponse.getModule());
//        } catch (Exception e) {
//            result.setSuccess(false);
//            result.setMessage(e.getMessage());
//            result.setData(e.getMessage());
//
//        }
//        Map<String, QueryResultUnit> buyerPrice = QueryResultBuilder.buildQueryResult("买家订单详情", null, getShowPriceKey(), buyerPricevalue);
//
//        //卖家视角
//        List<ShowPriceDTO> sellerPricevalue = new ArrayList<ShowPriceDTO>();
//
//        try {
//            sellerPricevalue = getSellerPrice(tradeOrderDTOResponse.getModule());
//        } catch (Exception e) {
//            result.setSuccess(false);
//            result.setMessage(e.getMessage());
//            result.setData(e.getMessage());
//
//        }
//        Map<String, QueryResultUnit> sellerPrice = QueryResultBuilder.buildQueryResult("卖家订单详情", null, getShowPriceKey(), sellerPricevalue);

        //订单金额检查
        List<CheckPriceDTO> checkPricevalue = new ArrayList<CheckPriceDTO>();

        try {
            checkPricevalue = checkPrices(tradeOrderDTOResponse.getModule());
        } catch (Exception e) {
            result.setSuccess(false);
            result.setMessage(e.getMessage());
            result.setData(e.getMessage());

        }
        Map<String, QueryResultUnit> checkPrice = QueryResultBuilder.buildQueryResult("订单金额检测结果", null, getCheckPriceKey(), checkPricevalue);

//        data.putAll(buyerPrice);
//        data.putAll(sellerPrice);
        data.putAll(orderPrice);
        data.putAll(orderLinePrice);
        data.putAll(orderLinefeaturePrice);
//        data.putAll(intentionPrice);
        data.putAll(dynamicPrice);
//        data.putAll(valumePrice);
        data.putAll(checkPrice);


        //量价差值
        List<CheckPriceDTO> dpGap = new ArrayList<>();
        if (dynamicPrices != null) {
            try {
                dpGap = checkDpGaps(tradePriceDTO);

            }catch(Exception e){
                result.setSuccess(false);
                result.setMessage(e.getMessage());
                result.setData(e.getMessage());
            }
            Map<String, QueryResultUnit> checkDpGap = QueryResultBuilder.buildQueryResult("量价差值核对", null, null, dpGap);
            data.putAll(checkDpGap);

        }

        result.setData(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat,SerializerFeature.DisableCircularReferenceDetect));
        result.setSuccess(true);
        HsfUtil.measureAll("/trade/jobId="+systemDTO.getSite(),empId);
        return result;
    }


    private List<ShowPriceDTO> getSellerPrice(TradeOrderDTO tradeOrderDTO) throws Exception{
        List<ShowPriceDTO> sellerPrices = new ArrayList<>();
        MonetaryAmount adjustFee = Money.zero(tradeOrderDTO.getSaleOriginalFee().getCurrency());
        ShowPriceDTO sellerPrice = new ShowPriceDTO();
        MonetaryAmount nonGoldStandardDiscountFee = Money.zero(tradeOrderDTO.getSaleOriginalFee().getCurrency());
        MonetaryAmount goldStandardDiscountFee = Money.zero(tradeOrderDTO.getSaleOriginalFee().getCurrency());
        Map<String, String> discount = new HashedMap<>();

        MonetaryAmount cbImport = Money.zero(tradeOrderDTO.getSaleOriginalFee().getCurrency());

        for (TradeOrderLineDTO tradeOrderLineDTO : tradeOrderDTO.getOrderLines()) {

            //原价
            ShowPriceDTO t = new ShowPriceDTO();

            t.setTradeOrderId(tradeOrderLineDTO.getTradeOrderId());
            t.setTradeOrderLineId(tradeOrderLineDTO.getTradeOrderLineId());
            t.setUnitPrice(tradeOrderLineDTO.getUnitFee().toString());
            t.setQuantity(tradeOrderLineDTO.getQuantity().toString());

            MonetaryAmount priceTemp = tradeOrderLineDTO.getUnitFee().multiply(tradeOrderLineDTO.getQuantity());

            t.setProductPrice(priceTemp.toString());

            if(tradeOrderLineDTO.getPromotionOrderDTO()!=null){
                for(PromotionOrderDTO promotionOrderDTO :tradeOrderLineDTO.getPromotionOrderDTO()){

                    discount.put(promotionOrderDTO.getProductCode(),promotionOrderDTO.getPriceTypes().get(0).getMonetaryAmount().toString());

                }

            }

            adjustFee = adjustFee.add(tradeOrderLineDTO.getAdjustFee());

            if (tradeOrderLineDTO.getOriginalSaleDiscountInfo() != null) {
                for (PromotionFeeInfoDTO promotionFeeInfoDTO : tradeOrderLineDTO.getOriginalSaleDiscountInfo()) {
                    nonGoldStandardDiscountFee = nonGoldStandardDiscountFee.add(promotionFeeInfoDTO.getNonGoldStandardDiscountFee());
                    goldStandardDiscountFee = goldStandardDiscountFee.add(promotionFeeInfoDTO.getGoldStandardDiscountFee());
                }
            }

            sellerPrices.add(t);

            if(tradeOrderLineDTO.getFeatures().getFeatureMap().get("cbImport")!=null){
                cbImport = cbImport.add(Money.of(tradeOrderLineDTO.getFeatures().getFeatureMap().get("cbImport")));
            }
        }
        sellerPrice.setTradeOrderId(tradeOrderDTO.getTradeOrderId());
        sellerPrice.setProductPrices(tradeOrderDTO.getSaleOriginalFee().toString());
        sellerPrice.setShippingCost(tradeOrderDTO.getShippingActualFee().toString());
        sellerPrice.setAdjustFee(adjustFee.toString());
        sellerPrice.setDiscountInfo(discount);
        sellerPrice.setNonGoldStandardDiscountFee(nonGoldStandardDiscountFee.toString());
        sellerPrice.setTotalAmount(tradeOrderDTO.getOrderAmount().add(adjustFee).toString());


        if(tradeOrderDTO.getPayOrderDTO().getActualPaidFee()!=null){
            sellerPrice.setActualPaidFee(tradeOrderDTO.getPayOrderDTO().getActualPaidFee().toString());
        }

        if(tradeOrderDTO.getPayOrderDTO().getPaymentCostFee()!=null){
            sellerPrice.setPaymentCostFee(tradeOrderDTO.getPayOrderDTO().getPaymentCostFee().toString());
        }

        if(tradeOrderDTO.getPayOrderDTO().getPaymentPromotionFee()!=null){
            sellerPrice.setPaymentPromotionFee(tradeOrderDTO.getPayOrderDTO().getPaymentPromotionFee().toString());
        }

        sellerPrice.setTax("显性税卖家不展示税，含税则展示含税价");
        if(cbImport!=null) {
            sellerPrice.setDuty(cbImport.toString());
        }


        sellerPrices.add(sellerPrice);

        return sellerPrices;

    }

    private List<String> getShowPriceKey() {
        List<String> showPriceKey = new ArrayList<>();
        showPriceKey.add("tradeOrderId");
        showPriceKey.add("tradeOrderLineId");
        showPriceKey.add("unitPrice");
        showPriceKey.add("quantity");
        showPriceKey.add("productPrice");
        showPriceKey.add("productPrices");
        showPriceKey.add("shippingCost");
        showPriceKey.add("adjustFee");
        showPriceKey.add("tax");
        showPriceKey.add("duty");
        showPriceKey.add("discountInfo");
        showPriceKey.add("nonGoldStandardDiscountFee");
        showPriceKey.add("totalAmount");
        showPriceKey.add("actualPaidFee");
        showPriceKey.add("goldStandardDiscountFee");
        showPriceKey.add("paymentPromotionFee");
        showPriceKey.add("paymentCostFee");

        return showPriceKey;
    }

    private List<String> getCheckPriceKey() {

        List<String> checkPriceKey = new ArrayList<>();
        checkPriceKey.add("tradeOrderId");
        checkPriceKey.add("tradeOrderLineId");
        checkPriceKey.add("filed");
        checkPriceKey.add("calRule");
        checkPriceKey.add("calFormula");
        checkPriceKey.add("calResult");
        return checkPriceKey;


    }


    private List<ShowPriceDTO> getBuyerPrice(TradeOrderDTO tradeOrderDTO) throws Exception{

        List<ShowPriceDTO> buyerPrices = new ArrayList<>();
        ShowPriceDTO buyerPrice = new ShowPriceDTO();
        BigDecimal exchangeRate = BigDecimal.valueOf(1);
        String currency = tradeOrderDTO.getActualFeeOfPurposeCurrency().getCurrency().toString();
        MonetaryAmount adjustFee = Money.zero(tradeOrderDTO.getSaleOriginalFee().getCurrency());
        MonetaryAmount payableFee = Money.zero(tradeOrderDTO.getSaleOriginalFee().getCurrency());
        MonetaryAmount nonGoldStandardDiscountFee = Money.zero(tradeOrderDTO.getSaleOriginalFee().getCurrency());
        MonetaryAmount goldStandardDiscountFee = Money.zero(tradeOrderDTO.getSaleOriginalFee().getCurrency());
        MonetaryAmount payItemNonGoldStandardFee = Money.zero(tradeOrderDTO.getActualFeeOfPurposeCurrency().getCurrency());
        MonetaryAmount payItemGoldStandardFee = Money.zero(tradeOrderDTO.getActualFeeOfPurposeCurrency().getCurrency());
        Map<String, String> discount = new HashedMap<>();
        Money taxFee = Money.zero(tradeOrderDTO.getSaleOriginalFee().getCurrency());
        MonetaryAmount cbImport = Money.zero(tradeOrderDTO.getSaleOriginalFee().getCurrency());


        if (tradeOrderDTO.getOrderLines().get(0).getFeatures().getFeatureMap().get("d_p") != null && tradeOrderDTO.getOrderLines().get(0).getFeatures().getFeatureMap().get("d_p").equals("1")) {

            MonetaryAmount originalFee = Money.zero(tradeOrderDTO.getSaleOriginalFee().getCurrency());
            MonetaryAmount itemSingleDiscountFee = Money.zero(tradeOrderDTO.getSaleOriginalFee().getCurrency());
            MonetaryAmount sumOriginalFee =Money.zero(tradeOrderDTO.getSaleOriginalFee().getCurrency());


            for (TradeOrderLineDTO tradeOrderLineDTO : tradeOrderDTO.getOrderLines()) {

                exchangeRate = tradeOrderLineDTO.getExchangeInfo().getExchangeRate();
                ShowPriceDTO t = new ShowPriceDTO();
                JSONObject _dynamic_price = null;

                if (StringUtil.isNotBlank(tradeOrderLineDTO.getFeatures().getFeatureMap().get("_dynamic_price"))) {

                    _dynamic_price = JSONObject.parseObject(tradeOrderLineDTO.getFeatures().getFeatureMap().get("_dynamic_price"));

                    if (_dynamic_price.getJSONObject("payCurrDiscountFee")!=null) {

                        JSONObject payCurrDiscountFee = _dynamic_price.getJSONObject("payCurrDiscountFee");
                        payItemNonGoldStandardFee = payItemNonGoldStandardFee.add(Money.ofMinorUnit(payCurrDiscountFee.getJSONObject("itemNonGoldStandardFee").getString("currencyCode"), payCurrDiscountFee.getJSONObject("itemNonGoldStandardFee").getLongValue("amount")));
                        payItemGoldStandardFee = payItemGoldStandardFee.add(Money.ofMinorUnit(payCurrDiscountFee.getJSONObject("itemGoldStandardFee").getString("currencyCode"), payCurrDiscountFee.getJSONObject("itemGoldStandardFee").getLongValue("amount")));
                    }

                    originalFee = JSONObject.parseObject(_dynamic_price.getJSONObject("originalFee").toJSONString(), MonetaryAmount.class);
                    payableFee = payableFee.add(JSONObject.parseObject(_dynamic_price.getJSONObject("payableFee").toJSONString(), MonetaryAmount.class));
                    adjustFee = adjustFee.add(JSONObject.parseObject(_dynamic_price.getJSONObject("adjustFee").toJSONString(), MonetaryAmount.class));

                    t.setTradeOrderId(tradeOrderLineDTO.getTradeOrderId());
                    t.setTradeOrderLineId(tradeOrderLineDTO.getTradeOrderLineId());


                    if (StringUtil.isNotBlank(_dynamic_price.getJSONObject("discountFee").toJSONString())) {
                        JSONObject discountFee = _dynamic_price.getJSONObject("discountFee");
                        itemSingleDiscountFee = Money.ofMinorUnit(discountFee.getJSONObject("itemSingleDiscountFee").getString("currencyCode"), discountFee.getJSONObject("itemSingleDiscountFee").getLongValue("amount"));

                    }

                }

                t.setUnitPrice(FundCalculator.calculateAmountByExchange(exchangeRate, originalFee.subtract(itemSingleDiscountFee), currency, Boolean.TRUE).toString());
                t.setQuantity(tradeOrderLineDTO.getQuantity().toString());

                if (!"1".equals(tradeOrderLineDTO.getFeatures().getFeatureMap().get("include_tax"))&&tradeOrderDTO.getTaxActualFee() != null && !tradeOrderDTO.getTaxActualFee().isZero()) {
                    taxFee = MoneyConvertUtil.calSplitAmount(tradeOrderLineDTO.getSaleOriginalFee(),
                            tradeOrderDTO.getTaxFee(),
                            tradeOrderDTO.getSaleOriginalFee().add(tradeOrderDTO.getShippingActualFee()));

                }
                t.setTax(FundCalculator.calculateAmountByExchange(exchangeRate,taxFee, currency, Boolean.TRUE).toString());

                MonetaryAmount priceTemp = originalFee.subtract(itemSingleDiscountFee).multiply(tradeOrderLineDTO.getQuantity()).add(taxFee);
                sumOriginalFee = sumOriginalFee.add(originalFee.subtract(itemSingleDiscountFee).multiply(tradeOrderLineDTO.getQuantity()));

                t.setProductPrice(FundCalculator.calculateAmountByExchange(exchangeRate, priceTemp, currency, Boolean.TRUE).toString());
                buyerPrices.add(t);

                if(tradeOrderLineDTO.getFeatures().getFeatureMap().get("cbImport")!=null){
                    cbImport = cbImport.add(Money.of(tradeOrderLineDTO.getFeatures().getFeatureMap().get("cbImport")));
                }


                if (StringUtil.isNotBlank(tradeOrderLineDTO.getFeatures().getFeatureMap().get("_dp_pcpdf"))) {
                    List<JSONObject> _dp_pcpdfs = JSONObject.parseArray(tradeOrderLineDTO.getFeatures().getFeatureMap().get("_dp_pcpdf"), JSONObject.class);


                    for (JSONObject _dp_pcpdf:_dp_pcpdfs) {
                        String promotionId = _dp_pcpdf.get("promotionId").toString();

                        MonetaryAmount p = Money.ofMinorUnit(_dp_pcpdf.getJSONObject("discountFee").getString("currencyCode"), _dp_pcpdf.getJSONObject("discountFee").getLongValue("amount"));
                        MonetaryAmount discountPrice = p;
                        if (discount.get(promotionId)!=null){

                            discountPrice = p.add(Money.of(discount.get(promotionId)));

                        }
                        discount.put(promotionId, discountPrice.toString());

                    }

                }

            }

            buyerPrice.setTradeOrderId(tradeOrderDTO.getTradeOrderId());
            buyerPrice.setProductPrices(FundCalculator.calculateAmountByExchange(exchangeRate, sumOriginalFee, currency, Boolean.TRUE).toString());
            buyerPrice.setShippingCost(FundCalculator.calculateAmountByExchange(exchangeRate, tradeOrderDTO.getShippingActualFee(), currency, Boolean.TRUE).toString());
            buyerPrice.setAdjustFee(FundCalculator.calculateAmountByExchange(exchangeRate, adjustFee, currency, Boolean.TRUE).toString());
            buyerPrice.setDiscountInfo(discount);
            if(!"1".equals(tradeOrderDTO.getOrderLines().get(0).getFeatures().getFeatureMap().get("include_tax"))){
                buyerPrice.setTax(FundCalculator.calculateAmountByExchange(exchangeRate, tradeOrderDTO.getTaxActualFee(), currency, Boolean.TRUE).toString());

            }
            buyerPrice.setTotalAmount(FundCalculator.calculateAmountByExchange(exchangeRate, payableFee, currency, Boolean.TRUE).toString());

            buyerPrice.setNonGoldStandardDiscountFee(payItemNonGoldStandardFee.toString());
            buyerPrice.setGoldStandardDiscountFee(payItemGoldStandardFee.toString());
            buyerPrice.setDuty(FundCalculator.calculateAmountByExchange(exchangeRate, cbImport, currency, Boolean.TRUE).toString());


            if (tradeOrderDTO.getPayOrderDTO().getActualPaidFee() != null) {
                buyerPrice.setActualPaidFee(tradeOrderDTO.getPayOrderDTO().getActualPaidFee().toString());
            }

            if (tradeOrderDTO.getPayOrderDTO().getPaymentCostFee() != null) {
                buyerPrice.setPaymentCostFee(tradeOrderDTO.getPayOrderDTO().getPaymentCostFee().toString());
            }

            if (tradeOrderDTO.getPayOrderDTO().getPaymentPromotionFee() != null) {
                buyerPrice.setPaymentPromotionFee(tradeOrderDTO.getPayOrderDTO().getPaymentPromotionFee().toString());
            }

            buyerPrices.add(buyerPrice);


        } else {
            for (TradeOrderLineDTO tradeOrderLineDTO : tradeOrderDTO.getOrderLines()) {
                exchangeRate = tradeOrderLineDTO.getExchangeInfo().getExchangeRate();
                ShowPriceDTO t = new ShowPriceDTO();

                //原价

                t.setTradeOrderId(tradeOrderLineDTO.getTradeOrderId());
                t.setTradeOrderLineId(tradeOrderLineDTO.getTradeOrderLineId());
                t.setUnitPrice(FundCalculator.calculateAmountByExchange(exchangeRate, tradeOrderLineDTO.getUnitFee(), currency, Boolean.TRUE).toString());
                t.setQuantity(tradeOrderLineDTO.getQuantity().toString());

                if (!"1".equals(tradeOrderLineDTO.getFeatures().getFeatureMap().get("include_tax"))
                && tradeOrderDTO.getTaxActualFee() != null && !tradeOrderDTO.getTaxActualFee().isZero()) {
                    taxFee = MoneyConvertUtil.calSplitAmount(tradeOrderLineDTO.getSaleOriginalFee(),
                            tradeOrderDTO.getTaxFee(),
                            tradeOrderDTO.getSaleOriginalFee().add(tradeOrderDTO.getShippingActualFee()));

                }
                t.setTax(FundCalculator.calculateAmountByExchange(exchangeRate,taxFee, currency, Boolean.TRUE).toString());

                MonetaryAmount priceTemp = tradeOrderLineDTO.getUnitFee().multiply(tradeOrderLineDTO.getQuantity()).add(taxFee);
                t.setProductPrice(FundCalculator.calculateAmountByExchange(exchangeRate, priceTemp, currency, Boolean.TRUE).toString());
                buyerPrices.add(t);

                adjustFee = adjustFee.add(tradeOrderLineDTO.getAdjustFee());
                
                if (tradeOrderLineDTO.getOriginalSaleDiscountInfo() != null) {
                    for (PromotionFeeInfoDTO promotionFeeInfoDTO : tradeOrderLineDTO.getOriginalSaleDiscountInfo()) {
                        nonGoldStandardDiscountFee = nonGoldStandardDiscountFee.add(promotionFeeInfoDTO.getNonGoldStandardDiscountFee());
                        goldStandardDiscountFee = goldStandardDiscountFee.add(promotionFeeInfoDTO.getGoldStandardDiscountFee());
                    }
                }

                if(tradeOrderLineDTO.getFeatures().getFeatureMap().get("cbImport")!=null){
                    cbImport = cbImport.add(Money.of(tradeOrderLineDTO.getFeatures().getFeatureMap().get("cbImport")));
                }
            }

            buyerPrice.setTradeOrderId(tradeOrderDTO.getTradeOrderId());
            buyerPrice.setProductPrices(FundCalculator.calculateAmountByExchange(exchangeRate, tradeOrderDTO.getSaleOriginalFee(), currency, Boolean.TRUE).toString());
            buyerPrice.setShippingCost(FundCalculator.calculateAmountByExchange(exchangeRate, tradeOrderDTO.getShippingActualFee(), currency, Boolean.TRUE).toString());
            buyerPrice.setAdjustFee(FundCalculator.calculateAmountByExchange(exchangeRate, adjustFee, currency, Boolean.TRUE).toString());
            if (!"1".equals(tradeOrderDTO.getOrderLines().get(0).getFeatures().getFeatureMap().get("include_tax"))){
                buyerPrice.setTax(FundCalculator.calculateAmountByExchange(exchangeRate, tradeOrderDTO.getTaxActualFee(), currency, Boolean.TRUE).toString());

            }
            buyerPrice.setNonGoldStandardDiscountFee(FundCalculator.calculateAmountByExchange(exchangeRate, nonGoldStandardDiscountFee, currency, Boolean.TRUE).toString());

            if ("1".equals(tradeOrderDTO.getOrderLines().get(0).getFeatures().getFeatureMap().get("include_tax"))){
                buyerPrice.setTotalAmount(FundCalculator.calculateAmountByExchange(exchangeRate, tradeOrderDTO.getOrderAmount().add(adjustFee), currency, Boolean.TRUE).toString());

            }else{
                buyerPrice.setTotalAmount(FundCalculator.calculateAmountByExchange(exchangeRate, tradeOrderDTO.getOrderAmount().add(adjustFee).add(tradeOrderDTO.getTaxActualFee()), currency, Boolean.TRUE).toString());

            }
            buyerPrice.setDuty(FundCalculator.calculateAmountByExchange(exchangeRate, cbImport, currency, Boolean.TRUE).toString());

            if (tradeOrderDTO.getOrderLines().get(0).getPromotionOrderDTO() != null) {

              List<PromotionOrderDTO> promotionOrderDTOs= tradeOrderDTO.getOrderLines().get(0).getPromotionOrderDTO();

              for(PromotionOrderDTO promotionOrderDTO:promotionOrderDTOs) {

                  MonetaryAmount discountPrice = FundCalculator.calculateAmountByExchange(exchangeRate, promotionOrderDTO.getPriceTypes().get(0).getMonetaryAmount(), currency, Boolean.TRUE);

                  if (discount.get(promotionOrderDTO.getProductCode()) == null) {
                      discount.put(promotionOrderDTO.getProductCode(), discountPrice.toString());

                  }
              }
                buyerPrice.setDiscountInfo(discount);

            }


            if(tradeOrderDTO.getPayOrderDTO().getActualPaidFee()!=null){
                buyerPrice.setActualPaidFee(tradeOrderDTO.getPayOrderDTO().getActualPaidFee().toString());
            }

            if(tradeOrderDTO.getPayOrderDTO().getPaymentCostFee()!=null){
                buyerPrice.setPaymentCostFee(tradeOrderDTO.getPayOrderDTO().getPaymentCostFee().toString());
            }

            if(tradeOrderDTO.getPayOrderDTO().getPaymentPromotionFee()!=null){
                buyerPrice.setPaymentPromotionFee(tradeOrderDTO.getPayOrderDTO().getPaymentPromotionFee().toString());
            }

            buyerPrices.add(buyerPrice);

        }

        return buyerPrices;

    }




    public List<CheckPriceDTO> checkPrices (TradeOrderDTO tradeOrderDTO) throws Exception {
        List<CheckPriceDTO> result = new ArrayList<>();

        List<TradeOrderLineDTO> tradeOrderLineDTOs = tradeOrderDTO.getOrderLines();
        MonetaryAmount totalNonGoldStandardDiscountFee = Money.zero(tradeOrderDTO.getSaleOriginalFee().getCurrency());
        MonetaryAmount totalGoldStandardDiscountFee = Money.zero(tradeOrderDTO.getSaleOriginalFee().getCurrency());
        MonetaryAmount adjustFee = Money.zero(tradeOrderDTO.getSaleOriginalFee().getCurrency());
        MonetaryAmount totalSaleDiscountFee = Money.zero(tradeOrderDTO.getSaleOriginalFee().getCurrency());


        try {

            for (TradeOrderLineDTO tradeOrderLineDTO : tradeOrderLineDTOs) {
                MonetaryAmount nonGoldStandardDiscountFee = Money.zero(tradeOrderDTO.getSaleOriginalFee().getCurrency());
                MonetaryAmount goldStandardDiscountFee = Money.zero(tradeOrderDTO.getSaleOriginalFee().getCurrency());

                MonetaryAmount payNonGoldStandardDiscountFee = Money.zero(tradeOrderDTO.getActualFeeOfPurposeCurrency().getCurrency());
                MonetaryAmount payGoldStandardDiscountFee = Money.zero(tradeOrderDTO.getActualFeeOfPurposeCurrency().getCurrency());
                MonetaryAmount payShippingNonGoldStandardDiscountFee = Money.zero(tradeOrderDTO.getActualFeeOfPurposeCurrency().getCurrency());
                MonetaryAmount payShippingGoldStandardDiscountFee = Money.zero(tradeOrderDTO.getActualFeeOfPurposeCurrency().getCurrency());

                if (tradeOrderLineDTO.getOriginalSaleDiscountInfo() != null) {
                    for (PromotionFeeInfoDTO promotionFeeInfoDTO : tradeOrderLineDTO.getOriginalSaleDiscountInfo()) {
                        totalNonGoldStandardDiscountFee = totalNonGoldStandardDiscountFee.add(promotionFeeInfoDTO.getNonGoldStandardDiscountFee());
                        nonGoldStandardDiscountFee = nonGoldStandardDiscountFee.add(promotionFeeInfoDTO.getNonGoldStandardDiscountFee());

                        totalGoldStandardDiscountFee = totalGoldStandardDiscountFee.add(promotionFeeInfoDTO.getGoldStandardDiscountFee());
                        goldStandardDiscountFee=goldStandardDiscountFee.add(promotionFeeInfoDTO.getGoldStandardDiscountFee());
                    }
                }


                if (tradeOrderLineDTO.getSaleDiscountFee()!=null){
                    totalSaleDiscountFee = totalSaleDiscountFee.add(tradeOrderLineDTO.getSaleDiscountFee());
                    result.add(checkSaleDiscountInfo(tradeOrderLineDTO,nonGoldStandardDiscountFee,goldStandardDiscountFee));

                }

                result.add(checkPayableFee(tradeOrderLineDTO,nonGoldStandardDiscountFee,goldStandardDiscountFee));


                if (tradeOrderLineDTO.getSaleDiscountInfo() != null) {

                    result.add(checkSaleDiscountInfo(tradeOrderLineDTO));

                    for (PromotionFeeInfoDTO promotionFeeInfoDTO : tradeOrderLineDTO.getSaleDiscountInfo()) {

                        if (promotionFeeInfoDTO.getNonGoldStandardDiscountFee() != null) {
                            payNonGoldStandardDiscountFee = payNonGoldStandardDiscountFee.add(promotionFeeInfoDTO.getNonGoldStandardDiscountFee());
                        }

                        if (promotionFeeInfoDTO.getGoldStandardDiscountFee() != null) {
                            payGoldStandardDiscountFee = payGoldStandardDiscountFee.add(promotionFeeInfoDTO.getGoldStandardDiscountFee());

                        }

                    }

                }
                if (tradeOrderLineDTO.getShippingDiscountInfo() != null) {

                    for (PromotionFeeInfoDTO promotionFeeInfoDTO : tradeOrderLineDTO.getShippingDiscountInfo()) {

                        if (promotionFeeInfoDTO.getNonGoldStandardDiscountFee() != null) {
                            payShippingNonGoldStandardDiscountFee = payShippingNonGoldStandardDiscountFee.add(promotionFeeInfoDTO.getNonGoldStandardDiscountFee());
                        }

                        if (promotionFeeInfoDTO.getGoldStandardDiscountFee() != null) {
                            payShippingGoldStandardDiscountFee = payShippingGoldStandardDiscountFee.add(promotionFeeInfoDTO.getGoldStandardDiscountFee());
                        }

                    }

                }

                /*pdf,pcpdf*/

                if (StringUtil.isNotBlank(tradeOrderLineDTO.getFeatures().getFeatureMap().get("pdf"))) {

                    result.add(checkPdf(tradeOrderLineDTO,payNonGoldStandardDiscountFee,payGoldStandardDiscountFee,payShippingNonGoldStandardDiscountFee,payShippingGoldStandardDiscountFee));

                }

                if (StringUtil.isNotBlank(tradeOrderLineDTO.getFeatures().getFeatureMap().get("pcpdf"))) {
                    if(tradeOrderLineDTO.getFeatures().getFeatureMap().get("d_p")!=null && tradeOrderLineDTO.getFeatures().getFeatureMap().get("d_p").equals("1")){
                        result.add(checkPcpdf(tradeOrderLineDTO,payGoldStandardDiscountFee,payShippingGoldStandardDiscountFee));
                    }else{
                        result.add(checkPcpdf(tradeOrderLineDTO,payGoldStandardDiscountFee));
                    }


                }

                /*adjustFee*/
                adjustFee = adjustFee.add(tradeOrderLineDTO.getAdjustFee());

                if (StringUtil.isNotBlank(tradeOrderLineDTO.getFeatures().getFeatureMap().get("gaf")) && StringUtil.isNotBlank(tradeOrderLineDTO.getFeatures().getFeatureMap().get("paf"))) {

                    result.add(checkAdjustCal(tradeOrderLineDTO));
                }

                //check _dynamic_price
                if(tradeOrderLineDTO.getFeatures().getFeatureMap().get("d_p")!=null && tradeOrderLineDTO.getFeatures().getFeatureMap().get("d_p").equals("1")){
                    result = checkDynamicPrice(result,tradeOrderLineDTO);

                }
                //check tax
                if(tradeOrderLineDTO.getTaxFee()!=null){
                    result.add(checkSonTaxActualFee(tradeOrderLineDTO)) ;
                    result.add(checkTaxDetail(tradeOrderLineDTO));

                }

                if("1".equals(tradeOrderLineDTO.getFeatures().getFeatureMap().get("include_tax"))){
                    result.add(checkTaxDetails(tradeOrderLineDTO));

                }


            }

            /*tradeOrder-主单维度核对*/
            /*original orderAmount*/
            result.add(checkOrderAmount(tradeOrderDTO,totalNonGoldStandardDiscountFee));


            /*originalSaleDiscountInfo*/
            if(tradeOrderDTO.getSaleDiscountFee()!=null) {
                result.add(checkTotalSaleDiscountInfo(tradeOrderDTO.getTradeOrderId(), tradeOrderDTO.getSaleDiscountFee(), totalSaleDiscountFee, totalGoldStandardDiscountFee, totalNonGoldStandardDiscountFee));
            }

            if(tradeOrderDTO.getTaxFee()!=null && tradeOrderDTO.getTaxActualFee()!=null && tradeOrderDTO.getTaxRebateFee()!=null){
                result.add(checkTaxActualFee(tradeOrderDTO));
            }

        } catch (Exception e) {

            throw new Exception(e);
        }

        return result;
    }

    private CheckPriceDTO checkSaleDiscountInfo(TradeOrderLineDTO tradeOrderDTO) {

        CheckPriceDTO r = new CheckPriceDTO();
        r.setTradeOrderId(tradeOrderDTO.getTradeOrderId());
        r.setTradeOrderLineId(tradeOrderDTO.getTradeOrderLineId());
        r.setFiled("SaleDiscountInfo明细");
        r.setCalRule("discountFee等于goldStandardDiscountFee和nonGoldStandardDiscountFee之和");
        r.setCalResult(true);

        for (PromotionFeeInfoDTO promotionFeeInfoDTO:tradeOrderDTO.getSaleDiscountInfo()){

            MonetaryAmount goldStandardDiscountFee = Money.zero(tradeOrderDTO.getExchangeInfo().getQuoteCurrency());
            MonetaryAmount nonGoldStandardDiscountFee = Money.zero(tradeOrderDTO.getExchangeInfo().getQuoteCurrency());

            MonetaryAmount discountFee = promotionFeeInfoDTO.getDiscountFee();
            if (promotionFeeInfoDTO.getGoldStandardDiscountFee() != null) {
                goldStandardDiscountFee = promotionFeeInfoDTO.getGoldStandardDiscountFee();
            }

            if(promotionFeeInfoDTO.getNonGoldStandardDiscountFee() !=null){
                nonGoldStandardDiscountFee = promotionFeeInfoDTO.getNonGoldStandardDiscountFee();
            }

            if(discountFee.isEqualTo(goldStandardDiscountFee.add(nonGoldStandardDiscountFee))){
                r.setCalFormula(r.getCalFormula() +";" + promotionFeeInfoDTO.getPromotionId() + ":" + goldStandardDiscountFee +"+" + nonGoldStandardDiscountFee + "=" + discountFee);

            }else{
                r.setCalFormula(r.getCalFormula() +";"+ promotionFeeInfoDTO.getPromotionId() + ":" + goldStandardDiscountFee + "+" + nonGoldStandardDiscountFee + "!=" + discountFee);
                r.setCalResult(false);            }

        }

        return r;

    }

    private CheckPriceDTO checkTotalSaleDiscountInfo(Long tradeOrderId,MonetaryAmount OrderSaleDiscountFee,MonetaryAmount totalSaleDiscountFee, MonetaryAmount goldStandardDiscountFee, MonetaryAmount nonGoldStandardDiscountFee) {

        CheckPriceDTO r = new CheckPriceDTO();
        r.setTradeOrderId(tradeOrderId);
        r.setFiled("original total SaleDiscountFee总和");
        r.setCalRule("SaleDiscountFee=nonGoldStandardDiscountFee+goldStandardDiscountFee");//优惠总和与明细-全球卖下营销金本位优惠由于保汇和实时汇率的不一致会导致不对等

        if (totalSaleDiscountFee.isEqualTo(OrderSaleDiscountFee)&&totalSaleDiscountFee.isEqualTo(
                goldStandardDiscountFee.add(
                        nonGoldStandardDiscountFee))) {
            r.setCalFormula(goldStandardDiscountFee + "+" + nonGoldStandardDiscountFee + "=" + totalSaleDiscountFee);
            r.setCalResult(true);


        } else {
            r.setCalFormula(goldStandardDiscountFee + "+" + nonGoldStandardDiscountFee + "!=" + totalSaleDiscountFee);
            r.setCalResult(false);
        }
        return r;
    }

    private CheckPriceDTO checkSaleDiscountInfo(TradeOrderLineDTO tradeOrderLineDTO,MonetaryAmount goldStandardDiscountFee, MonetaryAmount nonGoldStandardDiscountFee) {

        CheckPriceDTO r = new CheckPriceDTO();
        r.setTradeOrderId(tradeOrderLineDTO.getTradeOrderId());
        r.setTradeOrderLineId(tradeOrderLineDTO.getTradeOrderLineId());
        r.setFiled("original saleDiscountFee compare to originalSaleDiscountInfo");
        r.setCalRule("saleDiscountFee=nonGoldStandardDiscountFee+goldStandardDiscountFee");//优惠总和与明细-全球卖下营销金本位优惠由于保汇和实时汇率的不一致会导致不对等

        if (tradeOrderLineDTO.getSaleDiscountFee().isEqualTo(
                goldStandardDiscountFee.add(
                        nonGoldStandardDiscountFee))) {
            r.setCalFormula(goldStandardDiscountFee + "+" + nonGoldStandardDiscountFee + "=" + tradeOrderLineDTO.getSaleDiscountFee());
            r.setCalResult(true);


        } else {
            r.setCalFormula(goldStandardDiscountFee + "+" + nonGoldStandardDiscountFee + "!=" + tradeOrderLineDTO.getSaleDiscountFee());
            r.setCalResult(false);
        }
        return r;
    }

    private CheckPriceDTO checkOrderAmount(TradeOrderDTO tradeOrderDTO,MonetaryAmount nonGoldStandardDiscountFee) {
        CheckPriceDTO r = new CheckPriceDTO();
        r.setTradeOrderId(tradeOrderDTO.getTradeOrderId());
        r.setFiled("original orderAmount");
        r.setCalRule("orderAmount=商品折后价*数量总和+运费总和-非金本位优惠总和");

        if (tradeOrderDTO.getSaleOriginalFee().add(tradeOrderDTO.getShippingActualFee()).subtract(nonGoldStandardDiscountFee).subtract(tradeOrderDTO.getTaxRebateFee()).isEqualTo(tradeOrderDTO.getOrderAmount())) {

            r.setCalFormula(tradeOrderDTO.getOrderAmount()+ "=" + tradeOrderDTO.getSaleOriginalFee() + "+" + tradeOrderDTO.getShippingActualFee() + "-" + nonGoldStandardDiscountFee + "-"+tradeOrderDTO.getTaxRebateFee());
            r.setCalResult(true);
        } else {

            r.setCalFormula(tradeOrderDTO.getOrderAmount()+ "!=" + tradeOrderDTO.getSaleOriginalFee() + "+" + tradeOrderDTO.getShippingActualFee() + "-" + nonGoldStandardDiscountFee + "-"+tradeOrderDTO.getTaxRebateFee());
            r.setCalResult(false);
        }
        return r;

    }

    private CheckPriceDTO checkAdjustCal(TradeOrderLineDTO tradeOrderLineDTO) {
        CheckPriceDTO r = new CheckPriceDTO();
        r.setTradeOrderId(tradeOrderLineDTO.getTradeOrderId());
        r.setTradeOrderLineId(tradeOrderLineDTO.getTradeOrderLineId());
        r.setFiled("original gaf&paf");
        r.setCalRule("compare gaf+paf to adjustFee");

        try {

            MonetaryAmount gaf = Money.of(tradeOrderLineDTO.getFeatures().getFeatureMap().get("gaf"));
            MonetaryAmount paf = Money.of(tradeOrderLineDTO.getFeatures().getFeatureMap().get("paf"));
            if (gaf.add(paf).isEqualTo(tradeOrderLineDTO.getAdjustFee())) {
                r.setCalFormula(gaf + "+" + paf + "=" + tradeOrderLineDTO.getAdjustFee());
                r.setCalResult(true);

            } else {
                r.setCalFormula(gaf + "+" + paf + "!=" + tradeOrderLineDTO.getAdjustFee());
                r.setCalResult(false);
            }
        }catch (Exception e){

            log.error(e.getMessage());

        }

        return r;
    }

    private CheckPriceDTO checkPcpdf(TradeOrderLineDTO tradeOrderLineDTO, MonetaryAmount payGoldStandardDiscountFee,MonetaryAmount payShippingGoldStandardDiscountFee) {
        CheckPriceDTO r = new CheckPriceDTO();

        MonetaryAmount pcpdf_itemGoldStandardFee = Money.zero(tradeOrderLineDTO.getExchangeInfo().getQuoteCurrency());

        List<JSONObject> pcpdfs = JSONObject.parseArray(tradeOrderLineDTO.getFeatures().getFeatureMap().get("pcpdf"), JSONObject.class);

        r.setTradeOrderId(tradeOrderLineDTO.getTradeOrderId());
        r.setTradeOrderLineId(tradeOrderLineDTO.getTradeOrderLineId());
        r.setFiled("dymanic pcpdf");
        r.setCalRule("compare pcpdf to SaleDiscountInfo + ShippingDiscountInfo");

        for (JSONObject pcpdf : pcpdfs) {
            if (pcpdf.getJSONObject("goldStandardDiscountFee") != null) {
                MonetaryAmount s1 = Money.ofMinorUnit(pcpdf.getJSONObject("goldStandardDiscountFee").getString("currencyCode"), pcpdf.getJSONObject("goldStandardDiscountFee").getLongValue("amount"));

                pcpdf_itemGoldStandardFee = pcpdf_itemGoldStandardFee.add(s1);

            }

        }

        if (pcpdf_itemGoldStandardFee.isEqualTo(payGoldStandardDiscountFee.add(payShippingGoldStandardDiscountFee))) {
            r.setCalFormula("goldStandardDiscountFee is" + pcpdf_itemGoldStandardFee + "=" + payGoldStandardDiscountFee + "+" + payShippingGoldStandardDiscountFee);
            r.setCalResult(true);


        } else {
            r.setCalFormula("goldStandardDiscountFee is" + pcpdf_itemGoldStandardFee + "!=" + payGoldStandardDiscountFee + "+" + payShippingGoldStandardDiscountFee);
            r.setCalResult(false);
        }

        return r;

    }

    private CheckPriceDTO checkPcpdf(TradeOrderLineDTO tradeOrderLineDTO, MonetaryAmount payGoldStandardDiscountFee) {
        CheckPriceDTO r = new CheckPriceDTO();

        MonetaryAmount pcpdf_itemGoldStandardFee = Money.zero(tradeOrderLineDTO.getExchangeInfo().getQuoteCurrency());

        List<JSONObject> pcpdfs = JSONObject.parseArray(tradeOrderLineDTO.getFeatures().getFeatureMap().get("pcpdf"), JSONObject.class);

        r.setTradeOrderId(tradeOrderLineDTO.getTradeOrderId());
        r.setTradeOrderLineId(tradeOrderLineDTO.getTradeOrderLineId());
        r.setFiled("original pcpdf");
        r.setCalRule("compare pcpdf to SaleDiscountInfo");

        for (JSONObject pcpdf : pcpdfs) {
            if (pcpdf.getJSONObject("goldStandardDiscountFee") != null) {
                MonetaryAmount s1 = Money.ofMinorUnit(pcpdf.getJSONObject("goldStandardDiscountFee").getString("currencyCode"), pcpdf.getJSONObject("goldStandardDiscountFee").getLongValue("amount"));

                pcpdf_itemGoldStandardFee = pcpdf_itemGoldStandardFee.add(s1);

            }

        }

        if (pcpdf_itemGoldStandardFee.isEqualTo(payGoldStandardDiscountFee)) {
            r.setCalFormula("goldStandardDiscountFee is" + pcpdf_itemGoldStandardFee + "=" + payGoldStandardDiscountFee);
            r.setCalResult(true);


        } else {
            r.setCalFormula("goldStandardDiscountFee is" + pcpdf_itemGoldStandardFee + "!=" + payGoldStandardDiscountFee);
            r.setCalResult(false);
        }

        return r;

    }

    private CheckPriceDTO checkPdf(TradeOrderLineDTO tradeOrderLineDTO, MonetaryAmount payNonGoldStandardDiscountFee, MonetaryAmount payGoldStandardDiscountFee,MonetaryAmount payShippingNonGoldStandardDiscountFee,MonetaryAmount payShippingGoldStandardDiscountFee) {

        CheckPriceDTO r = new CheckPriceDTO();
        r.setTradeOrderId(tradeOrderLineDTO.getTradeOrderId());
        r.setTradeOrderLineId(tradeOrderLineDTO.getTradeOrderLineId());
        r.setFiled("original pdf");
        r.setCalRule("compare pdf to SaleDiscountInfo + ShippingDiscountInfo");

        JSONObject pdf = JSONObject.parseObject(tradeOrderLineDTO.getFeatures().getFeatureMap().get("pdf"));

        JSONObject pdf_itemGoldStandardFee = pdf.getJSONObject("itemGoldStandardFee");
        JSONObject pdf_itemNonGoldStandardFee = pdf.getJSONObject("itemNonGoldStandardFee");
        JSONObject pdf_shippingGoldStandardFee = pdf.getJSONObject("shippingGoldStandardFee");
        JSONObject pdf_shippingNonGoldStandardFee = pdf.getJSONObject("shippingNonGoldStandardFee");
        //优惠是分为单位

        MonetaryAmount s1 = Money.ofMinorUnit(pdf_itemNonGoldStandardFee.getString("currencyCode"), pdf_itemNonGoldStandardFee.getLongValue("amount"));

        MonetaryAmount s2 = Money.ofMinorUnit(pdf_itemGoldStandardFee.getString("currencyCode"), pdf_itemGoldStandardFee.getLongValue("amount"));

        MonetaryAmount s3 = Money.ofMinorUnit(pdf_shippingNonGoldStandardFee.getString("currencyCode"), pdf_shippingNonGoldStandardFee.getLongValue("amount"));

        MonetaryAmount s4 = Money.ofMinorUnit(pdf_shippingGoldStandardFee.getString("currencyCode"), pdf_shippingGoldStandardFee.getLongValue("amount"));
        String re= "itemNonGoldStandardFee is";
        r.setCalResult(true);
        if(s1.isEqualTo(payNonGoldStandardDiscountFee)){
            re=re+s1 + "=" + payNonGoldStandardDiscountFee;
        }
        else{
            re=re+s1 + "!=" + payNonGoldStandardDiscountFee;
            r.setCalResult(false);
        }
        if(s2.isEqualTo(payGoldStandardDiscountFee)){
            re=re+ ";itemGoldStandardFee is"+s2 + "=" + payGoldStandardDiscountFee;
        }
        else{
            re=re+ ";itemGoldStandardFee is"+s2 + "!=" + payGoldStandardDiscountFee;
            r.setCalResult(false);
        }
        if(s3.isEqualTo(payShippingNonGoldStandardDiscountFee)){
            re=re+ ";shippingNonGoldStandardFee is"+s3 + "=" + payShippingNonGoldStandardDiscountFee;
        }
        else{
            re=re+ ";shippingNonGoldStandardFee is"+s3 + "!=" + payShippingNonGoldStandardDiscountFee;
            r.setCalResult(false);
        }
        if(s4.isEqualTo(payShippingGoldStandardDiscountFee)){
            re=re+ ";shippingGoldStandardFee is"+s4 + "=" + payShippingGoldStandardDiscountFee;
        }
        else{
            re=re+ ";shippingGoldStandardFee is"+s4 + "!=" + payShippingGoldStandardDiscountFee;
            r.setCalResult(false);
        }
        r.setCalFormula(re);

        return r;
    }


    public CheckPriceDTO checkPayableFee(TradeOrderLineDTO tradeOrderLineDTO,MonetaryAmount nonGoldStandardDiscountFee,MonetaryAmount goldStandardDiscountFee){
        CheckPriceDTO result = new CheckPriceDTO();
        /*payableFee*/
        result.setTradeOrderId(tradeOrderLineDTO.getTradeOrderId());
        result.setTradeOrderLineId(tradeOrderLineDTO.getTradeOrderLineId());
        result.setFiled("original son payableFee" );
        result.setCalRule("payableFee=unitFee*quantity+shippingFee+taxFee(rdf)+adjustFee-nonGoldStandardDiscountFee-goldStandardDiscountFee  (saleDiscountFee=nonGoldStandardDiscountFee+goldStandardDiscountFee)");
        MonetaryAmount adjustFee = Money.zero(tradeOrderLineDTO.getPayableFee().getCurrency());
        MonetaryAmount cbImport = Money.zero(tradeOrderLineDTO.getPayableFee().getCurrency());
        MonetaryAmount tax = Money.zero(tradeOrderLineDTO.getPayableFee().getCurrency());
        MonetaryAmount otherTaxFee = Money.zero(tradeOrderLineDTO.getPayableFee().getCurrency());
        List<JSONObject> otherTaxDetail=null;
        MonetaryAmount t_tax_fee_ev = Money.zero(tradeOrderLineDTO.getPayableFee().getCurrency());


        if(tradeOrderLineDTO.getAdjustFee()!=null){
            adjustFee=tradeOrderLineDTO.getAdjustFee();
        }

        if(tradeOrderLineDTO.getFeatures().getFeatureMap().get("cbImport")!=null){
            cbImport = Money.of(tradeOrderLineDTO.getFeatures().getFeatureMap().get("cbImport"));
        }

        if(!"1".equals(tradeOrderLineDTO.getFeatures().getFeatureMap().get("include_tax"))){
            tax = tradeOrderLineDTO.getTaxActualFee();

        }
        if(StringUtil.isNotBlank(tradeOrderLineDTO.getFeatures().getFeatureMap().get("otherTaxDetail"))){
            otherTaxDetail=JSONObject.parseArray(tradeOrderLineDTO.getFeatures().getFeatureMap().get("otherTaxDetail"),JSONObject.class);
            for (JSONObject otd:otherTaxDetail){
                if(otd.getJSONObject("taxFee")!=null){
                    MonetaryAmount otd1= Money.ofMinorUnit(otd.getJSONObject("taxFee").getString("currencyCode"), otd.getJSONObject("taxFee").getLongValue("cent"));
                    otherTaxFee=otherTaxFee.add(otd1);
                }
            }
        }

        if(StringUtils.isNotBlank(tradeOrderLineDTO.getFeatures().getFeatureMap().get("t_tax_fee_ev"))){
            t_tax_fee_ev = Money.of(tradeOrderLineDTO.getFeatures().getFeatureMap().get("t_tax_fee_ev"),tradeOrderLineDTO.getPayableFee().getCurrency());
        }

        if (tradeOrderLineDTO.getPayableFee().isEqualTo(
                tradeOrderLineDTO.getUnitFee().multiply(
                        tradeOrderLineDTO.getQuantity()).add(
                        tradeOrderLineDTO.getShippingActualFee().add(tax.add(otherTaxFee)).subtract(
                                tradeOrderLineDTO.getTaxRebateFee()).add(cbImport).add(t_tax_fee_ev).add(
                                        adjustFee).subtract(
                                nonGoldStandardDiscountFee).subtract(
                                goldStandardDiscountFee)))) {
            result.setCalFormula(tradeOrderLineDTO.getPayableFee()+ "=" + tradeOrderLineDTO.getUnitFee() + "*" + tradeOrderLineDTO.getQuantity() + "+" + tradeOrderLineDTO.getShippingActualFee() + "+" + tax.add(otherTaxFee) +"-"+ tradeOrderLineDTO.getTaxRebateFee()  + "+" + adjustFee + "-" + nonGoldStandardDiscountFee + "-" + goldStandardDiscountFee);
            result.setCalResult(true);

        } else {
            result.setCalFormula(tradeOrderLineDTO.getPayableFee()+ "!=" + tradeOrderLineDTO.getUnitFee() + "*" + tradeOrderLineDTO.getQuantity() + "+" + tradeOrderLineDTO.getShippingActualFee() + "+" + tax.add(otherTaxFee) +"-"+ tradeOrderLineDTO.getTaxRebateFee()  + "+" + adjustFee + "-" +  nonGoldStandardDiscountFee + "-" + goldStandardDiscountFee);
            result.setCalResult(false);

        }

        return result;

    }




    public List<CheckPriceDTO> checkDynamicPrice (List<CheckPriceDTO> result,TradeOrderLineDTO tradeOrderLineDTO) throws Exception {
        JSONObject _dynamic_price = null;
        List<JSONObject> _dp_pcpdf = null;
        JSONArray promotionFeeDetails = null;

        MonetaryAmount payItemGoldStandardFee = Money.zero(tradeOrderLineDTO.getActualFeeOfPurposeCurrency().getCurrency());
        MonetaryAmount payShippingGoldStandardFee = Money.zero(tradeOrderLineDTO.getActualFeeOfPurposeCurrency().getCurrency());

        if (StringUtil.isNotBlank(tradeOrderLineDTO.getFeatures().getFeatureMap().get("_dynamic_price"))) {
            _dynamic_price = JSONObject.parseObject(tradeOrderLineDTO.getFeatures().getFeatureMap().get("_dynamic_price"));

            if (_dynamic_price.getJSONObject("payCurrDiscountFee")!=null && StringUtil.isNotBlank(_dynamic_price.getJSONObject("payCurrDiscountFee").toJSONString())) {
                JSONObject payCurrDiscountFee = _dynamic_price.getJSONObject("payCurrDiscountFee");
                payItemGoldStandardFee = Money.ofMinorUnit(payCurrDiscountFee.getJSONObject("itemGoldStandardFee").getString("currencyCode"), payCurrDiscountFee.getJSONObject("itemGoldStandardFee").getLongValue("amount"));
                payShippingGoldStandardFee = Money.ofMinorUnit(payCurrDiscountFee.getJSONObject("shippingGoldStandardFee").getString("currencyCode"), payCurrDiscountFee.getJSONObject("shippingGoldStandardFee").getLongValue("amount"));

            }
            MonetaryAmount itemSingleDiscountFee =  Money.zero(tradeOrderLineDTO.getPayableFee().getCurrency());
            MonetaryAmount itemNonGoldStandardFee =  Money.zero(tradeOrderLineDTO.getPayableFee().getCurrency());
            MonetaryAmount itemGoldStandardFee =  Money.zero(tradeOrderLineDTO.getPayableFee().getCurrency());
            MonetaryAmount shippingNonGoldStandardFee =  Money.zero(tradeOrderLineDTO.getPayableFee().getCurrency());
            MonetaryAmount shippingGoldStandardFee =  Money.zero(tradeOrderLineDTO.getPayableFee().getCurrency());

            MonetaryAmount shippingDiscountFee = JSONObject.parseObject(_dynamic_price.getJSONObject("shippingDiscountFee").toJSONString(), MonetaryAmount.class);

            MonetaryAmount originalFee = JSONObject.parseObject(_dynamic_price.getJSONObject("originalFee").toJSONString(), MonetaryAmount.class);
            MonetaryAmount payableFee = JSONObject.parseObject(_dynamic_price.getJSONObject("payableFee").toJSONString(),  MonetaryAmount.class);
            MonetaryAmount adjustFee = JSONObject.parseObject(_dynamic_price.getJSONObject("adjustFee").toJSONString(),  MonetaryAmount.class);

            //discountFee

            if(StringUtil.isNotBlank(_dynamic_price.getJSONObject("discountFee").toJSONString())){
                JSONObject discountFee = _dynamic_price.getJSONObject("discountFee");
                itemSingleDiscountFee = Money.ofMinorUnit(discountFee.getJSONObject("itemSingleDiscountFee").getString("currencyCode"), discountFee.getJSONObject("itemSingleDiscountFee").getLongValue("amount"));
                itemNonGoldStandardFee = Money.ofMinorUnit(discountFee.getJSONObject("itemNonGoldStandardFee").getString("currencyCode"), discountFee.getJSONObject("itemNonGoldStandardFee").getLongValue("amount"));
                itemGoldStandardFee = Money.ofMinorUnit(discountFee.getJSONObject("itemGoldStandardFee").getString("currencyCode"), discountFee.getJSONObject("itemGoldStandardFee").getLongValue("amount"));
                shippingNonGoldStandardFee = Money.ofMinorUnit(discountFee.getJSONObject("shippingNonGoldStandardFee").getString("currencyCode"), discountFee.getJSONObject("shippingNonGoldStandardFee").getLongValue("amount"));
                shippingGoldStandardFee = Money.ofMinorUnit(discountFee.getJSONObject("shippingGoldStandardFee").getString("currencyCode"), discountFee.getJSONObject("shippingGoldStandardFee").getLongValue("amount"));

            }

            MonetaryAmount cbImport = Money.zero(tradeOrderLineDTO.getPayableFee().getCurrency());
            MonetaryAmount tax = Money.zero(tradeOrderLineDTO.getPayableFee().getCurrency());


            if(tradeOrderLineDTO.getFeatures().getFeatureMap().get("cbImport")!=null){
                cbImport = Money.of(tradeOrderLineDTO.getFeatures().getFeatureMap().get("cbImport"));
            }


            if(!"1".equals(tradeOrderLineDTO.getFeatures().getFeatureMap().get("include_tax"))){
                tax = tradeOrderLineDTO.getTaxActualFee();

            }

            //payableFee
            CheckPriceDTO checkDpPayableFee = new CheckPriceDTO();
            checkDpPayableFee.setTradeOrderId(tradeOrderLineDTO.getTradeOrderId());
            checkDpPayableFee.setTradeOrderLineId(tradeOrderLineDTO.getTradeOrderLineId());
            checkDpPayableFee.setFiled("dynamic payableFee");
            checkDpPayableFee.setCalRule("payableFee=unitFee*quantity+shippingFee+taxFee+adjustFee+ddp-saleDiscountFee");

            if (originalFee.subtract(itemSingleDiscountFee).multiply(
                    tradeOrderLineDTO.getQuantity()).add(
                    tradeOrderLineDTO.getShippingFee()).subtract(
                    itemNonGoldStandardFee).subtract(
                    itemGoldStandardFee).subtract(shippingDiscountFee).add(tax).subtract(tradeOrderLineDTO.getTaxRebateFee()).add(
                    adjustFee).add(cbImport).isEqualTo(payableFee)) {

                checkDpPayableFee.setCalFormula(payableFee+ "=" + originalFee + "-" + itemSingleDiscountFee + "-" + itemNonGoldStandardFee + "-" + itemGoldStandardFee + "+" + tradeOrderLineDTO.getShippingActualFee() + "+" + tax +"-"+ tradeOrderLineDTO.getTaxRebateFee() + "+" + adjustFee);
                checkDpPayableFee.setCalResult(true);

            } else {
                checkDpPayableFee.setCalFormula(payableFee+ "!=" + originalFee + "-" + itemSingleDiscountFee + "-" + itemNonGoldStandardFee + "-" + itemGoldStandardFee + "+" + tradeOrderLineDTO.getShippingActualFee() + "+" + tax +"-"+ tradeOrderLineDTO.getTaxRebateFee() + "+" + adjustFee);
                checkDpPayableFee.setCalResult(false);
            }
            result.add(checkDpPayableFee);

            //promotionFeeDetails
            if (StringUtil.isNotBlank(_dynamic_price.getJSONArray("promotionFeeDetails").toJSONString())) {

                promotionFeeDetails = _dynamic_price.getJSONArray("promotionFeeDetails");
                MonetaryAmount nonGoldStandardDiscountFee = Money.zero(payableFee.getCurrency());
                MonetaryAmount goldStandardDiscountFee = Money.zero(payableFee.getCurrency());

                for (int i = 0; i < promotionFeeDetails.size(); i++) {
                    if (promotionFeeDetails.getJSONObject(i).get("nonGoldStandardDiscountFee")!=null) {
                        // 改成分
                        MonetaryAmount n = Money.ofMinorUnit(promotionFeeDetails.getJSONObject(i).getJSONObject("nonGoldStandardDiscountFee").getString("currencyCode"),promotionFeeDetails.getJSONObject(i).getJSONObject("nonGoldStandardDiscountFee").getLongValue("amount"));

                        nonGoldStandardDiscountFee = nonGoldStandardDiscountFee.add(n);
                    }

                    if (promotionFeeDetails.getJSONObject(i).get("goldStandardDiscountFee")!= null) {
                        MonetaryAmount t = Money.ofMinorUnit(promotionFeeDetails.getJSONObject(i).getJSONObject("goldStandardDiscountFee").getString("currencyCode"),promotionFeeDetails.getJSONObject(i).getJSONObject("goldStandardDiscountFee").getLongValue("amount"));

                        goldStandardDiscountFee = goldStandardDiscountFee.add(t);
                    }
                }

                CheckPriceDTO r = new CheckPriceDTO();
                r.setTradeOrderId(tradeOrderLineDTO.getTradeOrderId());
                r.setTradeOrderLineId(tradeOrderLineDTO.getTradeOrderLineId());
                r.setFiled("dynamic discountFee");
                r.setCalRule("compare discountFee & promotionFeeDetails(非金本位优惠)");

                if(nonGoldStandardDiscountFee.isEqualTo(itemNonGoldStandardFee.add(itemSingleDiscountFee).add(shippingNonGoldStandardFee))){

                    r.setCalFormula(nonGoldStandardDiscountFee + "=" + itemNonGoldStandardFee+"+"+itemSingleDiscountFee+"+"+shippingNonGoldStandardFee);
                    r.setCalResult(true);

                }else{
                    r.setCalFormula(nonGoldStandardDiscountFee + "!=" + itemNonGoldStandardFee+"+"+itemSingleDiscountFee+"+"+shippingNonGoldStandardFee);
                    r.setCalResult(false);
                }
                result.add(r);


                CheckPriceDTO r1 = new CheckPriceDTO();
                r1.setTradeOrderId(tradeOrderLineDTO.getTradeOrderId());
                r1.setTradeOrderLineId(tradeOrderLineDTO.getTradeOrderLineId());
                r1.setFiled("dynamic discountFee");
                r1.setCalRule("compare discountFee & promotionFeeDetails(金本位优惠)");

                if(goldStandardDiscountFee.isEqualTo(itemGoldStandardFee.add(shippingGoldStandardFee))){
                    r1.setCalFormula(goldStandardDiscountFee + "=" + itemGoldStandardFee + "+" +shippingGoldStandardFee);
                    r1.setCalResult(true);
                }else{
                    r1.setCalFormula(goldStandardDiscountFee + "!=" + itemGoldStandardFee+ "+" +shippingGoldStandardFee);
                    r1.setCalResult(false);

                }
                result.add(r1);

            }

        }

        if (StringUtil.isNotBlank(tradeOrderLineDTO.getFeatures().getFeatureMap().get("_dp_pcpdf"))) {
            _dp_pcpdf = JSONObject.parseArray(tradeOrderLineDTO.getFeatures().getFeatureMap().get("_dp_pcpdf"), JSONObject.class);
            MonetaryAmount dp_pcpdf_itemGoldStandardFee = Money.zero(tradeOrderLineDTO.getActualFeeOfPurposeCurrency().getCurrency());
            CheckPriceDTO r2 = new CheckPriceDTO();
            r2.setTradeOrderId(tradeOrderLineDTO.getTradeOrderId());
            r2.setTradeOrderLineId(tradeOrderLineDTO.getTradeOrderLineId());
            r2.setFiled("dynamic _dp_pcpdf");
            r2.setCalRule("compare _dp_pcpdf & payCurrDiscountFee");

            for (JSONObject dp_pcpdf : _dp_pcpdf) {
                if (dp_pcpdf.getJSONObject("goldStandardDiscountFee") != null) {
                    MonetaryAmount s1 = Money.ofMinorUnit(dp_pcpdf.getJSONObject("goldStandardDiscountFee").getString("currencyCode"), dp_pcpdf.getJSONObject("goldStandardDiscountFee").getLongValue("amount"));
                    dp_pcpdf_itemGoldStandardFee = dp_pcpdf_itemGoldStandardFee.add(s1);
                }

            }

            if (dp_pcpdf_itemGoldStandardFee.isEqualTo(payItemGoldStandardFee.add(payShippingGoldStandardFee))) {
                r2.setCalFormula(dp_pcpdf_itemGoldStandardFee + "=" + payItemGoldStandardFee + "+" +payShippingGoldStandardFee);
                r2.setCalResult(true);

            } else {
                r2.setCalFormula(dp_pcpdf_itemGoldStandardFee + "!=" + payItemGoldStandardFee+ "+" +payShippingGoldStandardFee);
                r2.setCalResult(false);

            }
            result.add(r2);

        }


        return result;
    }



    private CheckPriceDTO checkSonTaxActualFee(TradeOrderLineDTO tradeOrderLineDTO) {
        CheckPriceDTO r = new CheckPriceDTO();
        r.setTradeOrderId(tradeOrderLineDTO.getTradeOrderId());
        r.setTradeOrderLineId(tradeOrderLineDTO.getTradeOrderLineId());
        r.setFiled("original son taxActualFee");
        r.setCalRule("taxActualFee=taxFee-taxRebateFee");

        if (tradeOrderLineDTO.getTaxFee().subtract(tradeOrderLineDTO.getTaxRebateFee()).isEqualTo(tradeOrderLineDTO.getTaxActualFee())) {

            r.setCalFormula(tradeOrderLineDTO.getTaxRebateFee() + "+" + tradeOrderLineDTO.getTaxActualFee() + "=" + tradeOrderLineDTO.getTaxFee());
            r.setCalResult(true);
        } else {

            r.setCalFormula(tradeOrderLineDTO.getTaxRebateFee() + "+" + tradeOrderLineDTO.getTaxActualFee() + "!=" + tradeOrderLineDTO.getTaxFee());
            r.setCalResult(false);
        }
        return r;

    }


    private CheckPriceDTO checkTaxActualFee(TradeOrderDTO tradeOrderDTO) {
        CheckPriceDTO r = new CheckPriceDTO();
        r.setTradeOrderId(tradeOrderDTO.getTradeOrderId());
        r.setFiled("original taxActualFee");
        r.setCalRule("taxActualFee=taxFee-taxRebateFee");

        if (tradeOrderDTO.getTaxFee().subtract(tradeOrderDTO.getTaxRebateFee()).isEqualTo(tradeOrderDTO.getTaxActualFee())) {

            r.setCalFormula(tradeOrderDTO.getTaxActualFee() + "=" + tradeOrderDTO.getTaxFee()+ "-" +tradeOrderDTO.getTaxRebateFee());
            r.setCalResult(true);
        } else {

            r.setCalFormula(tradeOrderDTO.getTaxActualFee() + "!=" + tradeOrderDTO.getTaxFee()+ "-" +tradeOrderDTO.getTaxRebateFee());
            r.setCalResult(false);
        }
        return r;

    }

    private CheckPriceDTO checkTaxDetail(TradeOrderLineDTO tradeOrderLineDTO) {
        CheckPriceDTO r = new CheckPriceDTO();
        r.setTradeOrderId(tradeOrderLineDTO.getTradeOrderId());
        r.setTradeOrderLineId(tradeOrderLineDTO.getTradeOrderLineId());
        r.setFiled("original gt & pft");
        r.setCalRule("compare gt + pft to taxFee");

        try {

            MonetaryAmount gt = Money.of(tradeOrderLineDTO.getFeatures().getFeatureMap().get("gt"));
            MonetaryAmount pft = Money.of(tradeOrderLineDTO.getFeatures().getFeatureMap().get("pft"));
            if (gt.add(pft).isEqualTo(tradeOrderLineDTO.getTaxFee())) {
                r.setCalFormula(gt + "+" + pft + "=" + tradeOrderLineDTO.getTaxFee());
                r.setCalResult(true);

            } else {
                r.setCalFormula(gt + "+" + pft + "!=" + tradeOrderLineDTO.getTaxFee());
                r.setCalResult(false);
            }
        }catch (Exception e){

            log.error(e.getMessage(),e);

        }

        return r;
    }

    private CheckPriceDTO checkTaxDetails(TradeOrderLineDTO tradeOrderLineDTO) {

            CheckPriceDTO r = new CheckPriceDTO();
            r.setTradeOrderId(tradeOrderLineDTO.getTradeOrderId());
            r.setTradeOrderLineId(tradeOrderLineDTO.getTradeOrderLineId());
            r.setFiled("original tax_detail");
            r.setCalRule("compare gt&pft to tax_detail");


            try {

                MonetaryAmount good = Money.zero(tradeOrderLineDTO.getPayableFee().getCurrency());
                MonetaryAmount SHIPPING =Money.zero(tradeOrderLineDTO.getPayableFee().getCurrency());

                List<JSONObject> taxDetails = JSONObject.parseArray(tradeOrderLineDTO.getFeatures().getFeatureMap().get("tax_detail"), JSONObject.class);

                if(taxDetails==null){
                    log.error("taxDetails is null");
                }

                for (JSONObject taxDetail:taxDetails) {
                    String taxType = taxDetail.get("taxType").toString();

                    if(taxType.equals("PRODUCT")){

                        good = Money.ofMinorUnit(taxDetail.getJSONObject("taxFee").getString("currencyCode"), taxDetail.getJSONObject("taxFee").getLongValue("cent"));

                    }

                    if (taxType.equals("SHIPPING")){
                        SHIPPING = Money.ofMinorUnit(taxDetail.getJSONObject("taxFee").getString("currencyCode"), taxDetail.getJSONObject("taxFee").getLongValue("cent"));

                    }

                }

                MonetaryAmount gt = Money.of(tradeOrderLineDTO.getFeatures().getFeatureMap().get("gt"));
                MonetaryAmount pft = Money.of(tradeOrderLineDTO.getFeatures().getFeatureMap().get("pft"));

                if (gt.isEqualTo(good)&&pft.isEqualTo(SHIPPING)) {
                    r.setCalFormula(gt + "=" + good +" and " + pft +  "=" + SHIPPING);
                    r.setCalResult(true);

                } else {
                    r.setCalFormula(gt + "!=" + good +" or " + pft +  "!=" + SHIPPING);
                    r.setCalResult(false);
                }
            }catch (Exception e){

                log.error(e.getMessage(),e);

            }

              return r;

        }


    private List<CheckPriceDTO> checkDpGaps(TradePriceDTO tradePriceDTO) {
        List<CheckPriceDTO> result = new ArrayList<>();

        for (TradeLinePriceDTO tradeLinePriceDTO : tradePriceDTO.getTradeLinePriceDTO()) {
//      量价差值
            Map<String,String> featureMaps = tradeLinePriceDTO.getVolumePrice().getFeatures().getFeatureMap();

            BigDecimal exchangeRate =  tradeLinePriceDTO.getTradeLineFeatureDTO().getIntent_pay_rate();
            String currency =  tradeLinePriceDTO.getTradeLineFeatureDTO().getIntent_pay_cur();

            for(String key:featureMaps.keySet()) {
                String value = featureMaps.get(key);

                if(key.equals(AttributeConstants.DYNAMIC_PRICE_ORIGINAL_FEE_DIFF)){
                    result.add(check_d_p_ori_fee_df(key,tradeLinePriceDTO,value));
                }else if(key.equals(AttributeConstants.DYNAMIC_PRICE_ORIGINAL_FEE_PAY_DIFF)){
                    result.add(check_d_p_ori_fee_pay_df(key,tradeLinePriceDTO,value,currency,exchangeRate));

                }else if(key.equals(AttributeConstants.DYNAMIC_PRICE_PRODUCT_GOLD_FEE)){
                    result.add(check_d_p_product_g_fee(key,tradeLinePriceDTO,value));


                }else if(key.equals(AttributeConstants.DYNAMIC_PRICE_PRODUCT_GOLD_FEE_PAY_CURR)){
                    result.add(check_d_p_product_g_fee_pay(key,tradeLinePriceDTO,value,currency));

                }else if(key.equals(AttributeConstants.DYNAMIC_PRICE_PRODUCT_NO_GOLD_FEE)){

                    result.add(check_d_p_product_ng_fee(key,tradeLinePriceDTO,value));

                }else if(key.equals(AttributeConstants.DYNAMIC_PRICE_PRODUCT_NO_GOLD_FEE_PAY_CURR)){

                    result.add(check_d_p_product_ng_fee_pay(key,tradeLinePriceDTO,value,currency));


                }else {

                }



            }

        }

        return result ;

    }

    private CheckPriceDTO check_d_p_product_ng_fee_pay(String key, TradeLinePriceDTO tradeLinePriceDTO, String value,String currency) {
        CheckPriceDTO r= new CheckPriceDTO();
        r.setTradeOrderId(tradeLinePriceDTO.getTradeOrderId());
        r.setTradeOrderLineId(tradeLinePriceDTO.getTradeOrderLineId());
        r.setFiled(key);
        r.setCalRule("子订单支付币种：量价非金本位优惠=payCurrDiscountFee.itemNonGoldStandardFee+payCurrDiscountFee.itemSingleDiscountFee");
        MonetaryAmount pay_itemNonGoldStandardFee = Money.zero(currency);
        MonetaryAmount pay_itemSingleDiscountFee = Money.zero(currency);

        if (tradeLinePriceDTO.getTradeLineDynamicDTO().getPay_itemNonGoldStandardFee()!=null){
            pay_itemNonGoldStandardFee =tradeLinePriceDTO.getTradeLineDynamicDTO().getPay_itemNonGoldStandardFee();
        }
        if (tradeLinePriceDTO.getTradeLineDynamicDTO().getPay_itemSingleDiscountFee()!=null){
            pay_itemSingleDiscountFee =tradeLinePriceDTO.getTradeLineDynamicDTO().getPay_itemSingleDiscountFee();
        }

        if ((pay_itemNonGoldStandardFee.add(pay_itemSingleDiscountFee)).isEqualTo(Money.of(value))){
            r.setCalFormula(value + "=" + pay_itemNonGoldStandardFee + "+" + pay_itemSingleDiscountFee);
            r.setCalResult(true);
        }else{
            r.setCalFormula(value + "!=" + pay_itemNonGoldStandardFee + "+" + pay_itemSingleDiscountFee);
            r.setCalResult(false);
        }

        return r;

    }

    private CheckPriceDTO check_d_p_product_ng_fee(String key, TradeLinePriceDTO tradeLinePriceDTO, String value) {
        CheckPriceDTO r= new CheckPriceDTO();
        r.setTradeOrderId(tradeLinePriceDTO.getTradeOrderId());
        r.setTradeOrderLineId(tradeLinePriceDTO.getTradeOrderLineId());
        r.setFiled(key);
        r.setCalRule("子订单报价币种：量价非金本位优惠=discountFee.itemNonGoldStandardFee+discountFee.itemSingleDiscountFee");
        MonetaryAmount itemNonGoldStandardFee = Money.zero(tradeLinePriceDTO.getExchangeInfo().getBaseCurrency());
        MonetaryAmount itemSingleDiscountFee = Money.zero(tradeLinePriceDTO.getExchangeInfo().getBaseCurrency());

        if (tradeLinePriceDTO.getTradeLineDynamicDTO().getItemNonGoldStandardFee()!=null){
            itemNonGoldStandardFee =tradeLinePriceDTO.getTradeLineDynamicDTO().getItemNonGoldStandardFee();
        }
        if (tradeLinePriceDTO.getTradeLineDynamicDTO().getItemSingleDiscountFee()!=null){
            itemSingleDiscountFee =tradeLinePriceDTO.getTradeLineDynamicDTO().getItemSingleDiscountFee();
        }

        if ((itemNonGoldStandardFee.add(itemSingleDiscountFee)).isEqualTo(Money.of(value))){
            r.setCalFormula(value + "=" + itemNonGoldStandardFee + "+" + itemSingleDiscountFee);
            r.setCalResult(true);
        }else{
            r.setCalFormula(value + "!=" +  itemNonGoldStandardFee + "+" + itemSingleDiscountFee);
            r.setCalResult(false);
        }

        return r;

    }

    private CheckPriceDTO check_d_p_product_g_fee_pay(String key, TradeLinePriceDTO tradeLinePriceDTO, String value,String currency) {
        CheckPriceDTO r= new CheckPriceDTO();
        r.setTradeOrderId(tradeLinePriceDTO.getTradeOrderId());
        r.setTradeOrderLineId(tradeLinePriceDTO.getTradeOrderLineId());
        r.setFiled(key);
        r.setCalRule("子订单支付币种：量价金本位优惠=payCurrDiscountFee.itemGoldStandardFee");

        MonetaryAmount pay_itemGoldStandardFee = Money.zero(currency);

        if (tradeLinePriceDTO.getTradeLineDynamicDTO().getPay_itemGoldStandardFee()!=null){
            pay_itemGoldStandardFee =tradeLinePriceDTO.getTradeLineDynamicDTO().getPay_itemGoldStandardFee();
        }

        if (pay_itemGoldStandardFee.isEqualTo(Money.of(value))){
            r.setCalFormula(value + "=" + pay_itemGoldStandardFee);
            r.setCalResult(true);
        }else{
            r.setCalFormula(value + "!=" + pay_itemGoldStandardFee);
            r.setCalResult(false);
        }

        return r;

    }

    private CheckPriceDTO check_d_p_product_g_fee(String key, TradeLinePriceDTO tradeLinePriceDTO, String value) {
        CheckPriceDTO r= new CheckPriceDTO();
        r.setTradeOrderId(tradeLinePriceDTO.getTradeOrderId());
        r.setTradeOrderLineId(tradeLinePriceDTO.getTradeOrderLineId());
        r.setFiled(key);
        r.setCalRule("子订单报价币种：量价金本位优惠=discountFee.itemGoldStandardFee");
        MonetaryAmount itemGoldStandardFee = Money.zero(tradeLinePriceDTO.getExchangeInfo().getBaseCurrency());

        if (tradeLinePriceDTO.getTradeLineDynamicDTO().getItemGoldStandardFee()!=null){
            itemGoldStandardFee =tradeLinePriceDTO.getTradeLineDynamicDTO().getItemGoldStandardFee();
        }

        if (itemGoldStandardFee.isEqualTo(Money.of(value))){
            r.setCalFormula(value + "=" + itemGoldStandardFee);
            r.setCalResult(true);
        }else{
            r.setCalFormula(value + "!=" + itemGoldStandardFee);
            r.setCalResult(false);
        }

        return r;
    }

    private CheckPriceDTO check_d_p_ori_fee_pay_df(String key,TradeLinePriceDTO tradeLinePriceDTO, String price,String currency,BigDecimal exchangeRate) {
        CheckPriceDTO r= new CheckPriceDTO();
        r.setTradeOrderId(tradeLinePriceDTO.getTradeOrderId());
        r.setTradeOrderLineId(tradeLinePriceDTO.getTradeOrderLineId());
        r.setFiled(key);
        r.setCalRule("子订单支付币种:商品原价差值=originalFee*rate - proRetailPrice*rate");

        MonetaryAmount originalFee = tradeLinePriceDTO.getTradeLineDynamicDTO().getOriginalFee();
        MonetaryAmount proRetailPrice  = tradeLinePriceDTO.getTradeLineFeatureDTO().getProRetailPrice();

        MonetaryAmount originalFeeIntentionCurrency = FundCalculator.calculateAmountByExchange(exchangeRate, originalFee, currency, Boolean.TRUE);

        MonetaryAmount proRetailPriceIntentionCurrency = FundCalculator.calculateAmountByExchange(exchangeRate, proRetailPrice, currency, Boolean.TRUE);

        if (originalFeeIntentionCurrency.subtract(proRetailPriceIntentionCurrency).isEqualTo(Money.of(price))){
            r.setCalFormula(price + "=" + originalFeeIntentionCurrency + "-" + proRetailPriceIntentionCurrency);
            r.setCalResult(true);
        }else{
            r.setCalFormula(price + "!=" + originalFeeIntentionCurrency + "-" + proRetailPriceIntentionCurrency);
            r.setCalResult(false);
        }

        return  r;
    }

    private CheckPriceDTO check_d_p_ori_fee_df(String key,TradeLinePriceDTO tradeLinePriceDTO,String price) {

        CheckPriceDTO r= new CheckPriceDTO();
        r.setTradeOrderId(tradeLinePriceDTO.getTradeOrderId());
        r.setTradeOrderLineId(tradeLinePriceDTO.getTradeOrderLineId());
        r.setFiled(key);
        r.setCalRule("子订单报价币种,商品原价差值=originalFee - proRetailPrice");

        if (tradeLinePriceDTO.getTradeLineDynamicDTO().getOriginalFee().subtract(tradeLinePriceDTO.getTradeLineFeatureDTO().getProRetailPrice()).isEqualTo(Money.of(price))) {

            r.setCalFormula(price + "=" + tradeLinePriceDTO.getTradeLineDynamicDTO().getOriginalFee() + "-" + tradeLinePriceDTO.getTradeLineFeatureDTO().getProRetailPrice());
            r.setCalResult(true);
        } else {

            r.setCalFormula(price + "!=" + tradeLinePriceDTO.getTradeLineDynamicDTO().getOriginalFee() + "-" + tradeLinePriceDTO.getTradeLineFeatureDTO().getProRetailPrice());
            r.setCalResult(false);
        }
        return  r;

    }
}




