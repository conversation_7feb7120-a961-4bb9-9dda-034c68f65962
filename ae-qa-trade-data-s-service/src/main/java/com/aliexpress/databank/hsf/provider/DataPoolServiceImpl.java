package com.aliexpress.databank.hsf.provider;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.common.lang.StringUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.global.ic.constant.AuditStatusConstants;
import com.alibaba.global.ic.constant.ProductStatusConstants;
import com.alibaba.global.qa.dc.model.param.SystemDTO;
import com.aliexpress.databank.dataobject.ProductPool;
import com.aliexpress.databank.dataobject.SkuData;
import com.aliexpress.databank.dataobject.UserPool;
import com.aliexpress.databank.hsf.DataPoolService;
import com.aliexpress.databank.hsf.MemberService;
import com.aliexpress.databank.hsf.ProductService;
import com.aliexpress.databank.hsf.WalletService;
import com.aliexpress.databank.mapper.ProductPoolMapper;
import com.aliexpress.databank.mapper.UserPoolMapper;
import com.aliexpress.databank.utils.HsfUtil;
import com.aliexpress.uic.open.tag.domain.BoolTag;
import com.aliexpress.uic.open.tag.domain.dataobject.BoolTagDo;
import com.lazada.imptest.api.domain.ic.ProductResponseDTO;
import com.lazada.imptest.api.domain.ic.SkuResponseDTO;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.*;

import static com.aliexpress.databank.constant.Constant.UserTagList;

@Slf4j
@HSFProvider(serviceInterface = DataPoolService.class, serviceGroup = "HSF", serviceVersion = "1.0.0", clientTimeout = 20000)
public class DataPoolServiceImpl implements DataPoolService {

    private static final Logger logger = LoggerFactory.getLogger(DataPoolServiceImpl.class);

    @Resource
    UserPoolMapper userPoolMapper;

    @Resource
    ProductPoolMapper productPoolMapper;

    @Autowired
    ProductService  productService;

    @Autowired
    MemberService memberService;

    @Autowired
    WalletService walletService;

    //检测是否是测试账号
    @Override
    public Boolean checkTestAccount(Long id){
        //logger.info("服务查询："+res);
        Map<BoolTag, Boolean> boolTagBooleanMap = getUserTag(id);
        //测试账号
        if (boolTagBooleanMap != null && boolTagBooleanMap.get(BoolTag.AE_TEST_ACCOUNT)){
            return true;
        }
        else{
            logger.info("非测试账号："+ id.toString());
            return false;
        }

    }

    /**
     * 检测是否开通，钱包类型
     * @param accountId accountId
     * @return String
     */
    @Override
    public String checkUserTag(Long accountId){
        try {
            List<String> list = new ArrayList<>();
            JSONObject data = HsfUtil.queryWalletStatus(accountId);
            if (data != null && data.getJSONObject("module").getBoolean("opened")){
                list.add("OPEN_WALLET");
            }
            if (data != null && data.getJSONObject("module").containsKey("walletCountryCode") && StringUtil.isNotEmpty(data.getJSONObject("module").getString("walletCountryCode"))){
                list.add(data.getJSONObject("module").getString("walletCountryCode") + "_WALLET");
            }
            else if(data != null && !data.getJSONObject("module").getBoolean("opened")){
                list.add("NOT_WALLET");
            }
            if (data != null && data.getJSONObject("module").getBoolean("openedBalance")){
                list.add("OPEN_BALANCE");
            }

            return String.join(",",list);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 获取所有会员标签状态
     * @param accountId accountId
     * @return Map<BoolTag, Boolean>
     */
    private Map<BoolTag, Boolean> getUserTag(Long accountId){

        try {
            List<BoolTag> boolTags = new ArrayList<>(Arrays.asList(BoolTag.values()));
            JSONObject jsonObject = HsfUtil.getBoolTagByAccountId(accountId);
            BoolTagDo boolTagDo = jsonObject.toJavaObject(BoolTagDo.class);
            Map<BoolTag, Boolean> map = new HashMap<>();
            for (BoolTag boolTag : boolTags) {
                long tagValue = boolTag.getTagColumn().getMyValue(boolTagDo);
                int seat = boolTag.getSeat();
                boolean seatValue = ((1L & (tagValue >> seat)) == 1);
                map.put(boolTag, seatValue);
            }
            return map;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 获取会员实际标签
     * @param boolTagResult boolTagResult
     * @return  List<String>
     */
    private List<String> getUserRealTag(Map<BoolTag, Boolean> boolTagResult){
        List<String> userTagList = new ArrayList<>();
        try {
            if (!boolTagResult.isEmpty()){
                for(BoolTag boolTag : boolTagResult.keySet()){
                    if (boolTagResult.get(boolTag) && UserTagList.contains(boolTag.name())){
                        userTagList.add(boolTag.name());
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        logger.info("会员标签：" + userTagList.toString());
        return userTagList;
    }


    @Override
    public List<String> insertUser(String userParams, String passwordParams, String addressParams,SystemDTO systemDTO){
        List<String> result = new ArrayList<>();
        List<UserPool> userPoolList = new ArrayList<>();
        if (!StringUtil.isEmpty(userParams)){
            String[] userList = userParams.split(" ");
            String[] addressList = addressParams.split(" ");
            String[] passwordList = passwordParams.split(" ");
            if (userList.length!= addressList.length || userList.length != passwordList.length){
                result.add("用户个数和地址个数不一致");
                return result;
            }
            try {
                for(int i=0;i<userList.length;i++){
                    UserPool userPool0 = new UserPool();
                    userPool0.setUserName(userList[i]);
                    List<UserPool> poolList = userPoolMapper.userList(userPool0,1);
                    if (null != poolList && !poolList.isEmpty()){
                        result.add(userList[i] + "：原因-此用户已存在");
                        continue;
                    }
                    JSONObject data = memberService.getAccountByStr(userList[i]);
                    logger.info("查询用户信息："+data.toJSONString());
                    if (!data.isEmpty() && data.containsKey("module")){
                        Map<BoolTag, Boolean> boolTagBooleanMap = getUserTag(data.getJSONObject("module").getLong("userId"));
                        logger.info("boolTagBooleanMap:" + boolTagBooleanMap.toString());
                        //测试账号
                        if (!boolTagBooleanMap.get(BoolTag.AE_TEST_ACCOUNT)){
                            logger.info("非测试账号：" + userList[i]);
                            result.add(userList[i] + ":原因-非测试账号");
                            continue;
                        }
                        Long userId = data.getJSONObject("module").getLong("userId");
                        UserPool userPool = new UserPool();
                        userPool.setAccountId(userId);
                        JSONObject arData = HsfUtil.getRouting(userId);
                        logger.info("买家机房："+arData.toJSONString());
                        JSONObject location = new JSONObject();
                        location.put("preEnv",arData.getJSONObject("unitRegionNo").getString("csEnv"));
                        location.put("Env",arData.getJSONObject("unitRegionNo").getString("unitEnv"));
                        userPool.setBuyerLocation(location.toJSONString());

                        userPool.setAddressTag(addressList[i]);
                        userPool.setUserName(data.getJSONObject("module").getString("email"));
                        userPool.setPassword(passwordList[i]);
                        userPool.setUserTag(checkUserTag(userId));
                        userPool.setScene("ui");
                        userPool.setMemberId(walletService.getMemberIdByUserId(userId.toString()));
                        userPoolList.add(userPool);
                    }
                }
                logger.info("新增数据为："+userPoolList.toString());
                if (!userPoolList.isEmpty()){
                    if (!userPoolMapper.insertUserList(userPoolList)){
                        result.add("执行插入数据失败");
                    }
                }
            } catch (Exception e) {
                result.add("代码抛异常");
                e.printStackTrace();
            }
        }
        return result;
    }


    @Override
    public List<String> insertProduct(String productParams , SystemDTO systemDTO){
        List<String> result = new ArrayList<>();
        List<ProductPool> productPoolList = new ArrayList<>();
        try{
            if (!StringUtil.isEmpty(productParams)){
                String[] productList = productParams.split(" ");
                for(int i=0;i<productList.length;i++){
                    try {
                        ProductPool productPool0 = new ProductPool();
                        productPool0.setProductId(Long.parseLong(productList[i]));
                        List<ProductPool> poolList = productPoolMapper.productList(productPool0,1,0L,0L);
                        if (null != poolList && !poolList.isEmpty()){
                            result.add(productList[i] + "：原因-此商品已存在");
                            continue;
                        }
                        JSONObject pro = HsfUtil.getProductByProductId(Long.parseLong(productList[i]));
                        if (null != pro && !pro.getBoolean("success")){
                            result.add(productList[i] + "：原因-获取商品信息异常");
                            logger.info("获取商品信息异常："+ pro);
                            continue;
                        }
                        ProductResponseDTO res =  productService.getProductInfo(Long.parseLong(productList[i]),0L);
                        //logger.info("服务查询："+res);
                        Map<BoolTag, Boolean> boolTagBooleanMap = getUserTag(res.getSellerId());
                        //测试账号
                        if (boolTagBooleanMap != null && !boolTagBooleanMap.get(BoolTag.AE_TEST_ACCOUNT)){
                            result.add(productList[i] + "：原因-非测试卖家商品");
                            logger.info("非测试卖家商品");
                            continue;
                        }
                        //不可用不插入
                        if (!checkProductStatus(res.getSellerId(), Long.parseLong(productList[i]), res.getStatus(), res.getAuditStatus())){
                            result.add(productList[i] + "：原因-不可用状态不可插入");
                            logger.info("不可用状态不可插入");
                            continue;
                        }
                        SkuData skuData = getSkuData(res.getSkuIdAndSkuMap());
                        //库存不足10件不插入
                        if (skuData.getTotalStock()<10){
                            result.add(productList[i] + "：原因-库存不足10件不插入");
                            logger.info("库存不足10件不插入");
                            continue;
                        }
                        ProductPool productPool = new ProductPool();
                        productPool.setCategoryId(res.getCategoryId());
                        productPool.setProductId(Long.parseLong(productList[i]));
                        productPool.setSellerId(res.getSellerId());
                        productPool.setStockNum(skuData.getTotalStock());
                        productPool.setPrice(new Double(skuData.getMinSkuPrice()*100).longValue());
                        // 报价币种存在特征字段
                        productPool.setFeatures(res.getOriginalCurrencyCode());
                        productPoolList.add(productPool);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
                logger.info("新增商品数据："+productPoolList);
                if (!productPoolList.isEmpty()){
                    if (!productPoolMapper.insertProductList(productPoolList)){
                        result.add("执行插入数据失败");
                    }
                }
            }
        }catch (Exception e){
            result.add("代码抛异常");
            e.printStackTrace();
        }
        return result;
    }

    @Override
    public Boolean updateUser(UserPool userPool){
        return userPoolMapper.updateUser(userPool);
    }

    @Override
    public Boolean deleteUser(UserPool userPool){
        return userPoolMapper.deleteUser(userPool);
    }


    @Override
    public List<UserPool> queryUserInfo(UserPool userPool, Integer num){
        return userPoolMapper.userList(userPool, num);
    }


    @Override
    public Boolean updateProduct(ProductPool productPool){
        return productPoolMapper.updateProduct(productPool);
    }

    @Override
    public Boolean deleteProduct(ProductPool productPool){
        return productPoolMapper.deleteProduct(productPool);
    }


    @Override
    public List<ProductPool> queryProductInfo(ProductPool productPool, Integer num, Long lessPrice, Long largePrice){
        return productPoolMapper.productList(productPool, num, lessPrice,largePrice);
    }

    @Override
    public int userCount(Integer isDel, Integer enableType){
        return userPoolMapper.userCount(isDel,enableType);
    }

    @Override
    public JSONObject userCountById(Long accountId){
        JSONObject res = new JSONObject();
        res.put("num",userPoolMapper.userCountById(accountId));
        return res;
    }

    @Override
    public int productCount(Integer isDel, Integer enableType){
        return productPoolMapper.productCount(isDel,enableType);
    }

    @Override
    public JSONObject productCountById(Long productId){
        JSONObject res = new JSONObject();
        res.put("num",productPoolMapper.productCountById(productId));
        return res;
    }

    @Override
    public Boolean deleteUserOrProduct(String userName, String productId){
        Boolean res = false;
        if (StringUtil.isNotBlank(productId)){
            res = productPoolMapper.deleteProductByProductId(Long.parseLong(productId));
        }
        if(StringUtil.isNotBlank(userName)){
            res = userPoolMapper.deleteUserByUserName(userName);
        }
        return res;
    }

    @Override
    public Boolean checkProductStatus(Long sellerId, Long productId, Integer status, Integer auditStatus){
        if (status == ProductStatusConstants.NORMAL.intValue()
                && auditStatus.equals(AuditStatusConstants.PASS.getValue())) {
            return true;
        }
        else if(status == ProductStatusConstants.INSTOCK.intValue() ||
                status == ProductStatusConstants.INSTOCK_BY_ADMIN.intValue() ){
            logger.info("商品被下架");
            //上架操作--恢复接口
            return productService.recoverProduct(sellerId, productId);
        }
        return false;
    }

    @Override
    public SkuData getSkuData(Map<Long, SkuResponseDTO> skuIdAndSkuMap){
        SkuData skuData = new SkuData();
        Long stock = 0L;
        double minSkuPrice = 0L;
        Long minSkuStock = 0L;
        try{
            if (!skuIdAndSkuMap.isEmpty()) {
                for(Long key : skuIdAndSkuMap.keySet()){
                    SkuResponseDTO skuResponseDTO = skuIdAndSkuMap.get(key);
                    stock += skuResponseDTO.getStock();
                    if (skuResponseDTO.getAuditStatus() != 1 || skuResponseDTO.getStatus() != 1){
                        if (skuData.getSkuStatus() != null){
                            skuData.setSkuStatus(true);
                        }
                    }
                    //取最低sku库存
                    long skuStock = skuResponseDTO.getStock();
                    if (minSkuStock == 0 || skuStock < minSkuStock){
                        minSkuStock = skuStock;
                    }
                    double skuPrice = skuResponseDTO.getOriginalPrice().getNumber().doubleValue();
                    //取最低sku价格
                    if (minSkuPrice == 0 || skuPrice < minSkuPrice){
                        minSkuPrice = skuPrice;
                    }
                }
            }
            skuData.setMinSkuStock(minSkuStock);
            skuData.setMinSkuPrice(minSkuPrice);
            skuData.setTotalStock(stock);
        }catch (Exception e){
            e.printStackTrace();
        }
        return skuData;
    }

    @Override
    public JSONObject queryUser(String userName, Long accountId, String address, String userTag, Integer enableType, Integer num, Integer isUsed){
        JSONObject result = new JSONObject();
        try{
            UserPool userPool = new UserPool();
            if (StringUtil.isNotBlank(userName)){
                userPool.setUserName(userName);
            }
            if (null != accountId && accountId != 0){
                userPool.setAccountId(accountId);
            }
            if (StringUtil.isNotBlank(address)){
                userPool.setAddressTag(address);
            }
            if (StringUtil.isNotBlank(userTag)){
                userPool.setUserTag(userTag);
            }
            if (null != enableType){
                userPool.setEnableType(enableType);
            }
            if (null != isUsed){
                userPool.setIsUsed(isUsed);
            }
            int tempNum = 1000;
            if (null != num){
                tempNum = num;
            }
            userPool.setScene("ui");
            List<UserPool> userPoolList = queryUserInfo(userPool,tempNum);
            if (!userPoolList.isEmpty()){
                result.put("data",userPoolList);
                result.put("success",true);
            }
        }catch (Exception e) {
            result.put("data","查询异常");
            result.put("success",false);
        }
        return result;
    }

    @Override
    public JSONObject queryProduct(Long categoryId, Long productId, Integer enableType, Integer num, Long lessPrice, Long largePrice){
        JSONObject result = new JSONObject();
        try{
            ProductPool productPool = new ProductPool();
            if (null != productId && categoryId != 0){
                productPool.setProductId(productId);
            }
            if (null != categoryId && categoryId != 0){
                productPool.setCategoryId(categoryId);
            }
            else {
                productPool.setCategoryId(200000367L);
            }
            if (null != enableType){
                productPool.setEnableType(enableType);
            }
            int tempNum = 1000;
            if (null != num){
                tempNum = num;
            }
            List<ProductPool> productPoolList = queryProductInfo(productPool, tempNum, lessPrice, largePrice);
            if (!productPoolList.isEmpty()){
                result.put("data",productPoolList);
                result.put("success",true);
            }
        }catch (Exception e) {
            result.put("data","查询异常");
            result.put("success",false);
        }
        return result;
    }

    @Override
    public JSONObject updateUserUsed(UserPool userPool){
        JSONObject result = new JSONObject();
        if (userPoolMapper.updateUser(userPool)){
            result.put("success",true);
        }
        else {
            result.put("success",false);
        }
        return result;
    }

    @Override
    public Boolean updateUsed(){
        return userPoolMapper.updateUsed();
    }


    @Override
    public JSONObject checkAccount(Long id) {
        JSONObject result = new JSONObject();
        Map<BoolTag, Boolean> boolTagBooleanMap = getUserTag(id);
        //测试账号
        if (boolTagBooleanMap != null && boolTagBooleanMap.get(BoolTag.AE_TEST_ACCOUNT)){
            result.put("result",true);
        }
        else{
            logger.info("非测试账号："+ id.toString());
            result.put("result",false);
        }
        return result;

    }
}
