package com.aliexpress.databank.hsf;

import com.alibaba.global.qa.dc.model.param.SystemDTO;
import com.alibaba.global.qa.dc.model.result.ResultDTO;

import javax.money.MonetaryAmount;
import java.util.List;

public interface OrderService {

    /**
     * 获取订单详情，包含主订单信息，子订单信息，超时信息(支付/发货/确认收货)
     *
     * @param params    : orderId
     * @param systemDTO
     * @return
     * @throws Exception
     */
    ResultDTO getOrderByOrderIdAndBuyerId(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO formatOrderFeature(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO cancelOrder(Long buyerId, String refundChannel, Long orderId) throws Exception;

    /**
     * 取消订单
     *
     * @param params    : orderId
     * @param params    : cancelEvent
     * @param systemDTO
     * @return
     * @throws Exception
     */
    ResultDTO cancelOrder(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO cancelOrder4LLM(Long orderId,String cancelEvent,String dpath,String refundChannel) throws Exception;
    ResultDTO customPlaceOrder(String params, SystemDTO systemDTO) throws Exception;

    /**
     * 商品量价
     */
//    ResultDTO dynamicPrice(String params, SystemDTO systemDTO);

    /**
     * 商品查询
     */

    ResultDTO queryProduct(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO getOrderPriceById(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO freezeTestTradeOrder(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO unfreezeTestTradeOrder(String params, SystemDTO systemDTO) throws Exception;

    /**
     * 下试用单
     *
     * @param params
     * @param systemDTO
     * @return
     */
    ResultDTO placeTrialOrder(String params, SystemDTO systemDTO);

    /**
     * 发货
     *
     * @param params
     * @param systemDTO
     * @return
     */
    ResultDTO shipOrder(String params, SystemDTO systemDTO) throws Exception;
    ResultDTO shipOrder4LLM(String orderId, String shipType,String dpath) throws Exception;

    /**
     * 线上发货
     *
     * @param params
     * @param systemDTO
     * @return
     */
    ResultDTO shipOrderOnline(String params, SystemDTO systemDTO) throws Exception;

    /**
     * 查询不同业务订单
     * @param params : buyerId
     * @param systemDTO
     * @return
     * @throws Exception
     */
//    ResultDTO queryCustomOrder(String params, SystemDTO systemDTO) throws Exception;

    /**
     * 支付风控关单
     * ipay发起的关单，消息交互
     *
     * @param params : orderId
     */
    ResultDTO paymentRiskCancelOrder(String params, SystemDTO systemDTO) throws Exception;


    /**
     * 根据loginId获取订单详情
     *
     * @param params    : loginId
     * @param systemDTO
     * @return
     * @throws Exception
     */
    ResultDTO getOrderByLoginIdOrBuyerId(String params, SystemDTO systemDTO) throws Exception;

    /**
     * 一键下单
     */
    ResultDTO placeOrder(String params, SystemDTO systemDTO) throws Exception;
    /**
     * 一键下单LLM版本
     */
    ResultDTO placeOrder4LLM(Long buyerId,String countryCode,String deliveryOption,String itemIDS,String skuId,Integer quantity,String cur,String staging,String shareGroup,String shareGroupCode) throws Exception;
    ResultDTO editPayPrice4LLM(Long sellerId, Long orderId, Long buyerId, String adjustReason, String oldPrice, String newPrice, String currencyCode) throws Exception;
    ResultDTO placeOrderForFreeReturn(String params, SystemDTO systemDTO) throws Exception;

    /**
     * 克隆订单-未支付
     */
    ResultDTO cloneOrder(String params, SystemDTO systemDTO) throws Exception;

    /**
     * 确认收货
     */
    ResultDTO confirmDelivery(String params, SystemDTO systemDTO) throws Exception;
    ResultDTO confirmDelivery4LLM(Long orderId,Long subOrderId) throws Exception;

    List<Long> getOrderIdsByScenarioIndexId(String scenario, String scenarioIndex, int size) throws Exception;

    List<Long> getOrderIdsByScenario(String scenario, int size) throws Exception;

    ResultDTO shippingWarehouseOrder(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO shippingUnWarehouseOrder(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO createMiniAppOrder(String params, SystemDTO systemDTO) throws Exception;

    Long getPayerIdByOrderId(String orderId);

    ResultDTO add2CartByOrder(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO qdOrderConfirm(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO getOrderPrice(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO addOrderFeaturesTag(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO addOrderLineFeaturesTag(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO mockInbound(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO resumeCancelOrder(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO updateOrderFeatures(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO updateOrdersFeatures(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO sendMsg(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO sendLandloardMsg(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO codPrePaymentOrderFail(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO mockCodDelivered(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO manualCreateSnapshot(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO mockRaffleOrder(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO shippingUnWarehouseOrderByOnEventByRegion(String params, SystemDTO systemDTO) throws Exception;


}
