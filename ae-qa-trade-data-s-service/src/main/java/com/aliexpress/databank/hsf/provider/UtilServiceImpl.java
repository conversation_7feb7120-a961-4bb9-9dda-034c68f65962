package com.aliexpress.databank.hsf.provider;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.global.qa.dc.model.param.SystemDTO;
import com.alibaba.global.qa.dc.model.result.ResultDTO;
import com.alibaba.rocketmq.client.exception.MQBrokerException;
import com.alibaba.rocketmq.client.exception.MQClientException;
import com.alibaba.rocketmq.client.producer.SendResult;
import com.alibaba.rocketmq.remoting.exception.RemotingException;
import com.aliexpress.databank.config.MqConfig;
import com.aliexpress.databank.constant.Constant;
import com.aliexpress.databank.constant.TimeoutEnum;
import com.aliexpress.databank.dataobject.ResultVo;
import com.aliexpress.databank.diamond.ShippingMethodDiamond;
import com.aliexpress.databank.hsf.UtilService;
import com.aliexpress.databank.utils.DecodeUtil;
import com.aliexpress.databank.utils.HsfUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Objects;

@Slf4j
@HSFProvider(serviceInterface = UtilService.class, serviceGroup = "HSF", serviceVersion = "1.0.0", clientTimeout = 20000)
public class UtilServiceImpl implements UtilService {

    @Autowired
    private MqConfig mqConfig;

    @Override
    public ResultDTO decodeParams(String params, SystemDTO systemDTO) {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        String input = jsonObject.getString(Constant.PARAMS);
        resultDTO.setData(DecodeUtil.deCompress(input));
        resultDTO.setSuccess(true);
        return resultDTO;
    }

    @Override
    public ResultDTO callHsf(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        String input = "[  {    \"sortByCreateTime\": 1,    \"globalBizCode\": null,    \"deliveryStatuses\": null,    \"bizCode\": null,    \"payStatuses\": null,    \"tradeOrderId\": null,    \"pageSize\": 5,    \"buyerId\": 738306127,    \"stepBarLanguageKey\": \"LOCAL_DEFAULT_LANGUAGE\",    \"sellerId\": null,    \"needAmendable\": false,    \"scenario\": null,    \"frozenType\": null,    \"entrance\": null,    \"class\": \"com.alibaba.global.order.management.api.request.QueryTradeOrdersRequest\",    \"usingDynamicPrice\": null,    \"globalProductCode\": null,    \"gmtCreateEndTime\": {      \"dateTime\": {        \"date\": {          \"month\": 11,          \"year\": 2022,          \"class\": \"com.tmall.doom.client.extend.serialize.jdk8time.LocalDateHandle\",          \"day\": 1        },        \"time\": {          \"hour\": 2,          \"nano\": 0,          \"class\": \"com.tmall.doom.client.extend.serialize.jdk8time.LocalTimeHandle\",          \"minute\": 0,          \"second\": 0        },        \"class\": \"com.tmall.doom.client.extend.serialize.jdk8time.LocalDateTimeHandle\"      },      \"offset\": {        \"seconds\": -25200,        \"class\": \"com.tmall.doom.client.extend.serialize.jdk8time.ZoneOffsetHandle\"      },      \"zoneId\": \"America/Los_Angeles\",      \"class\": \"com.tmall.doom.client.extend.serialize.jdk8time.ZonedDateTimeHandle\"    },    \"orderSearchStatus\": null,    \"gmtCreateStartTime\": {      \"dateTime\": {        \"date\": {          \"month\": 10,          \"year\": 2022,          \"class\": \"com.tmall.doom.client.extend.serialize.jdk8time.LocalDateHandle\",          \"day\": 29        },        \"time\": {          \"hour\": 0,          \"nano\": 0,          \"class\": \"com.tmall.doom.client.extend.serialize.jdk8time.LocalTimeHandle\",          \"minute\": 0,          \"second\": 0        },        \"class\": \"com.tmall.doom.client.extend.serialize.jdk8time.LocalDateTimeHandle\"      },      \"offset\": {        \"seconds\": -25200,        \"class\": \"com.tmall.doom.client.extend.serialize.jdk8time.ZoneOffsetHandle\"      },      \"zoneId\": \"America/Los_Angeles\",      \"class\": \"com.tmall.doom.client.extend.serialize.jdk8time.ZonedDateTimeHandle\"    },    \"paystatus\": {      \"name\": \"PAID\",      \"class\": \"com.alibaba.global.order.management.api.model.PayStatus\"    },    \"page\": 1,    \"enableStatus\": 1,    \"deliveryStatus\": null,    \"needStepBar\": false  }]";
        String targetIp = jsonObject.getString(Constant.TARGET_IP);
        String serviceName = "com.alibaba.global.order.management.api.facade.OrderQueryForBuyerFacade";
        String serviceVersion = "1.0.0";
        String methodName = "queryTradeOrders";
        String groupId = "HSF";
        String[] parameterType = {"com.alibaba.global.order.management.api.request.QueryTradeOrdersRequest"};
        JSONArray paramList = JSONArray.parseArray(input);
        JSONObject result = HsfUtil.genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, targetIp, parameterType, paramList.toArray());
        resultDTO.setSuccess(true);
        resultDTO.setData(result.toJSONString());
        return resultDTO;
    }

    @Override
    public ResultDTO sendMsg(String params, SystemDTO systemDTO) throws InterruptedException, RemotingException, MQClientException, MQBrokerException {
        ResultDTO resultDTO = new ResultDTO();
        String topic = "GLOBAL-TRADE-ORDER-TOPIC";
        // OrderShipped  OrderPartialShipped
        String tag = "OrderPartialShipped";
        String key = "8168683090013223";
        String str = "{\"tradeOrderId\":8168683090013223,\"buyer\":{\"buyerId\":1859103223,\"buyerFullName\":\"test hz005\",\"buyerEmail\":\"<EMAIL>\",\"buyerPhone\":\"18689493231\",\"userLevel\":0},\"createTime\":1684846447.*********,\"actualFee\":{\"amount\":115.51,\"currency\":\"USD\"},\"saleOriginalFee\":{\"amount\":47.97,\"currency\":\"USD\"},\"saleDiscountFee\":{\"amount\":21.49,\"currency\":\"USD\"},\"dpSaleDiscountFee\":{\"amount\":0.00,\"currency\":\"USD\"},\"shippingActualFee\":{\"amount\":68.98,\"currency\":\"USD\"},\"shippingFee\":{\"amount\":68.98,\"currency\":\"USD\"},\"shippingDiscountFee\":{\"amount\":0.00,\"currency\":\"USD\"},\"taxActualFee\":{\"amount\":20.05,\"currency\":\"USD\"},\"taxFee\":{\"amount\":20.05,\"currency\":\"USD\"},\"siteId\":\"GLOBAL\",\"features\":{\"featureMap\":{\"cardCountry\":\"CN\",\"cardIndexNo\":\"2019121911027100188626211352575\",\"cashbackAmount\":\"{\\\"orderAmount\\\":{\\\"amount\\\":115.50,\\\"cent\\\":11550,\\\"centFactor\\\":100,\\\"currency\\\":\\\"USD\\\",\\\"currencyCode\\\":\\\"USD\\\",\\\"zero\\\":false},\\\"payAmount\\\":{\\\"amount\\\":115.50,\\\"cent\\\":11550,\\\"centFactor\\\":100,\\\"currency\\\":\\\"USD\\\",\\\"currencyCode\\\":\\\"USD\\\",\\\"zero\\\":false}}\",\"dcm\":\"true\",\"dcmm\":\"active\",\"ip\":\"***********\",\"local_order\":null,\"machineType\":\"OTHER.PC\",\"new\":\"1\",\"payBrand\":\"MASTERCARD\",\"sri\":\"8168683090013223\",\"tax_snapshot_id\":\"100016799756503223\",\"userTags\":null}},\"codOrder\":false,\"bizCodes\":[\"ali.global.ae.general\",\"ali.global.ae.general\",\"ali.global.ae.general\",\"ali.global.ae.general\",\"ali.global.ae.general\",\"ali.global.ae.general\",\"ali.global.ae.general\",\"ali.global.ae.general\",\"ali.global.ae.general\",\"ali.global.ae.general\",\"ali.global.ae.general\",\"ali.global.ae.general\",\"ali.global.ae.general\",\"ali.global.ae.general\"],\"paymentChannels\":[\"MIXEDCARD\"],\"orderLineMsgDTOS\":[{\"tradeOrderLineId\":8168683090083223,\"bizCode\":\"ali.global.ae.general\",\"tradeOrderId\":8168683090013223,\"buyer\":{\"buyerId\":1859103223,\"buyerFullName\":\"test hz005\",\"buyerEmail\":\"<EMAIL>\",\"buyerPhone\":\"18689493231\",\"userLevel\":0},\"seller\":{\"sellerId\":*********,\"sellerFullName\":\"Shop402172 Store\",\"sellerEmail\":\"<EMAIL>\",\"sellerPhone\":\"15927549709\"},\"quantity\":1,\"paymentStatus\":2,\"deliveryStatus\":6,\"outDeliveryId\":\"FO2551511951910002\",\"outPaymentId\":\"12990591202230523833800283223\",\"unitFee\":{\"amount\":1.00,\"currency\":\"USD\"},\"actualFee\":{\"amount\":15.56,\"currency\":\"USD\"},\"saleDiscountFee\":{\"amount\":0.44,\"currency\":\"USD\"},\"shippingActualFee\":{\"amount\":12.30,\"currency\":\"USD\"},\"shippingFee\":{\"amount\":12.30,\"currency\":\"USD\"},\"shippingDiscountFee\":{\"amount\":0.00,\"currency\":\"USD\"},\"saleDiscountInfo\":[{\"doSplit\":true,\"discountFee\":{\"amount\":0.41,\"currency\":\"USD\"},\"goldStandardDiscountFee\":{\"amount\":0.00,\"currency\":\"USD\"},\"nonGoldStandardDiscountFee\":{\"amount\":0.41,\"currency\":\"USD\"},\"promotionId\":\"6000000439033525\"},{\"doSplit\":true,\"discountFee\":{\"amount\":0.03,\"currency\":\"USD\"},\"goldStandardDiscountFee\":{\"amount\":0.00,\"currency\":\"USD\"},\"nonGoldStandardDiscountFee\":{\"amount\":0.03,\"currency\":\"USD\"},\"promotionId\":\"5000020552297225\"}],\"taxActualFee\":{\"amount\":2.70,\"currency\":\"USD\"},\"siteId\":\"GLOBAL\",\"wareHouseInfo\":{\"wareHouseCode\":\"ae_marketplace\",\"wareHouseType\":\"ae_marketplace\",\"inventoryType\":1000},\"itemMsgDTO\":{\"itemId\":\"1005005400370905\",\"itemTitle\":\"125244\",\"itemPicUrl\":\"S3077a43570354738a99eab827b3f1d4dG.jpg\",\"skuId\":\"12000032909836820\",\"sellerSkuCode\":\"1005005400370905_12000032909836820\",\"shopSkuCode\":\"1005005400370905_AE-12000032909836820\",\"itemPrice\":{\"amount\":1.00,\"currency\":\"USD\"},\"categoryId\":\"*********\",\"skuInfo\":\"[{\\\"valueId\\\":366,\\\"valueText\\\":\\\"Yellow\\\",\\\"propertyName\\\":\\\"Color\\\",\\\"isCustom\\\":true,\\\"propertyId\\\":14,\\\"order\\\":1},{\\\"valueId\\\":*********,\\\"valueText\\\":\\\"60*185cm\\\",\\\"propertyName\\\":\\\"Size\\\",\\\"isCustom\\\":true,\\\"propertyId\\\":5,\\\"valueAliasText\\\":\\\"200x210cm\\\",\\\"order\\\":2}]\",\"skuFeatures\":{\"cp_outer_id\":\"131144568537-1995261792985\",\"feature_cc\":\"7\",\"gmt_create_time\":\"1680230127506\",\"gmt_modified_time\":\"1680587632043\",\"ic_price_lmt\":\"1680512172716\",\"inv_code\":\"5:*********;14:366\",\"visibility_reason\":\"others#customizationResult\"}},\"promotionMsgDTOS\":[{\"promotionId\":\"6000000439033525\",\"productCode\":\"shopCoupon\",\"promotionRole\":\"SELLER\",\"discountFeeForItem\":{\"amount\":0.41,\"currency\":\"USD\"}},{\"promotionId\":\"5000020552297225\",\"productCode\":\"flexiCoin\",\"promotionRole\":\"SELLER\",\"discountFeeForItem\":{\"amount\":0.03,\"currency\":\"USD\"}}],\"logicDeliveryOrderMsgDTO\":{\"logicDeliveryId\":\"FO2551511951910002\",\"transportMethodDTO\":{\"leadingTime\":{\"shippingProviderCode\":\"CAINIAO_STANDARD\",\"shippingProviderName\":\"AliExpress Standard Shipping\"},\"deliveryType\":\"CAINIAO_STANDARD\"},\"transportMethodLineDTO\":{\"tradeOrderLineId\":8168683090083223,\"leadingTime\":{\"shippingProviderCode\":\"CAINIAO_STANDARD\",\"shippingProviderName\":\"AliExpress Standard Shipping\"},\"deliveryType\":\"CAINIAO_STANDARD\"}},\"features\":{\"featureMap\":{\"LOGISTICS_ALL_TAGS\":\"{\\\"maxTimestamp\\\":\\\"1686261600000\\\",\\\"provider\\\":\\\"cainiao\\\",\\\"etaTraceId\\\":\\\"PAY-f-1-919986676578000000-**********-**********-1686315243355-1686315243355-CAINIAO_STANDARD-DOOR_DELIVERY-OPERATING-DEF|SELLER_STOCK_UP-CALENDAR_DAY--RESOURCE_CODE#DEF-432000000-432000000---OPERATING-@DELIVERY_FULFIL-CALENDAR_DAY--DIVISION_ID#919986676578011000-**********-**********---OPERATING-|1684846443355\\\",\\\"minTimestamp\\\":\\\"1686261600000\\\"}\",\"dcm\":\"true\",\"dcma\":\"1\",\"dcmc\":\"USD\",\"dcmm\":\"active\",\"dcmt\":\"1\",\"etk\":null,\"freightCommitDay\":\"90\",\"goodsPrepareTime\":\"4\",\"guaranteedDeliveryTime\":\"60\",\"hcd\":\"1\",\"hp\":\"1\",\"ical\":null,\"in_rs\":\"place_order_withhold\",\"in_st\":\"0^1_1^12000032909836820_2^4000000004351495976_3^4000000007609325966\",\"isRetail\":\"false\",\"local_order\":null,\"new\":\"1\",\"tci\":\"18\",\"uopPromiseTime\":\"1686315461735\",\"wc\":\"ae_marketplace\",\"wt\":\"ae_marketplace\"}},\"deliveryAddress\":{\"addressId\":4100097956830,\"receiver\":{\"phone\":\"1234568906\",\"phonePrefixCode\":\"+34\",\"phoneCountry\":\"+34\",\"mobileNo\":\"1234568906\",\"mobilePrefixCode\":\"+34\"},\"countryId\":0,\"addressType\":\"HOME\",\"countryName\":\"Spain\",\"postCode\":\"12345\",\"city\":\"Agost\",\"state\":\"Alacant\",\"thirdLevelAddressName\":\"\",\"locationTreeAddressId\":\"919986676578000000-919986676578011000\",\"locationTreeAddressName\":\"Alacant,Agost\",\"detailAddress\":\"rgbihbko\",\"languageCode\":\"en\",\"countryCode\":\"ES\",\"features\":{\"featureMap\":{}}},\"createTime\":1684846447.*********,\"createOutDeliveryTime\":1684846662.147000000,\"bizTenant\":1,\"volumePriceExtraMap\":{}},{\"tradeOrderLineId\":8168683090133223,\"bizCode\":\"ali.global.ae.general\",\"tradeOrderId\":8168683090013223,\"buyer\":{\"buyerId\":1859103223,\"buyerFullName\":\"test hz005\",\"buyerEmail\":\"<EMAIL>\",\"buyerPhone\":\"18689493231\",\"userLevel\":0},\"seller\":{\"sellerId\":*********,\"sellerFullName\":\"Shop402172 Store\",\"sellerEmail\":\"<EMAIL>\",\"sellerPhone\":\"15927549709\"},\"quantity\":1,\"paymentStatus\":2,\"deliveryStatus\":6,\"outDeliveryId\":\"FO2551511951910002\",\"outPaymentId\":\"12990591202230523833800283223\",\"unitFee\":{\"amount\":0.01,\"currency\":\"USD\"},\"actualFee\":{\"amount\":0.00,\"currency\":\"USD\"},\"saleDiscountFee\":{\"amount\":0.01,\"currency\":\"USD\"},\"shippingActualFee\":{\"amount\":0.00,\"currency\":\"USD\"},\"shippingFee\":{\"amount\":0.00,\"currency\":\"USD\"},\"shippingDiscountFee\":{\"amount\":0.00,\"currency\":\"USD\"},\"saleDiscountInfo\":[{\"doSplit\":true,\"discountFee\":{\"amount\":0.00,\"currency\":\"USD\"},\"goldStandardDiscountFee\":{\"amount\":0.00,\"currency\":\"USD\"},\"nonGoldStandardDiscountFee\":{\"amount\":0.00,\"currency\":\"USD\"},\"promotionId\":\"6000000439033525\"},{\"doSplit\":true,\"discountFee\":{\"amount\":0.01,\"currency\":\"USD\"},\"goldStandardDiscountFee\":{\"amount\":0.00,\"currency\":\"USD\"},\"nonGoldStandardDiscountFee\":{\"amount\":0.01,\"currency\":\"USD\"},\"promotionId\":\"5000020552297225\"}],\"taxActualFee\":{\"amount\":0.00,\"currency\":\"USD\"},\"siteId\":\"GLOBAL\",\"wareHouseInfo\":{\"wareHouseCode\":\"ae_marketplace\",\"wareHouseType\":\"ae_marketplace\",\"inventoryType\":1000},\"itemMsgDTO\":{\"itemId\":\"1005005536676592\",\"itemTitle\":\"fanny test for oldman coudan not edit\",\"itemPicUrl\":\"S82a9acc5169c4722bca5666f7d1bfaads.jpg\",\"skuId\":\"12000033452968370\",\"sellerSkuCode\":\"1005005536676592_12000033452968370\",\"shopSkuCode\":\"1005005536676592_AE-12000033452968370\",\"itemPrice\":{\"amount\":0.01,\"currency\":\"USD\"},\"categoryId\":\"*********\",\"skuInfo\":\"[{\\\"valueId\\\":*********,\\\"valueText\\\":\\\"Burgundy\\\",\\\"propertyName\\\":\\\"Color\\\",\\\"isCustom\\\":false,\\\"propertyId\\\":14,\\\"valueAliasText\\\":\\\"For Apple iPhone 14\\\",\\\"order\\\":2},{\\\"valueId\\\":*********,\\\"valueText\\\":\\\"China\\\",\\\"propertyName\\\":\\\"Ships From\\\",\\\"isCustom\\\":false,\\\"propertyId\\\":*********,\\\"order\\\":1}]\",\"skuFeatures\":{\"feature_cc\":\"8\",\"gmt_create_time\":\"1683341514000\",\"gmt_modified_time\":\"1684779448204\",\"ic_price_lmt\":\"1684779448204\",\"inv_code\":\"14:*********;*********:*********\",\"seller_sku\":\"522\",\"visibility_reason\":\"others#customizationResult\"}},\"promotionMsgDTOS\":[{\"promotionId\":\"6000000439033525\",\"productCode\":\"shopCoupon\",\"promotionRole\":\"SELLER\",\"discountFeeForItem\":{\"amount\":0.00,\"currency\":\"USD\"}},{\"promotionId\":\"5000020552297225\",\"productCode\":\"flexiCoin\",\"promotionRole\":\"SELLER\",\"discountFeeForItem\":{\"amount\":0.01,\"currency\":\"USD\"}}],\"logicDeliveryOrderMsgDTO\":{\"logicDeliveryId\":\"FO2551511951910002\",\"transportMethodDTO\":{\"leadingTime\":{\"shippingProviderCode\":\"CAINIAO_STANDARD\",\"shippingProviderName\":\"AliExpress Standard Shipping\"},\"deliveryType\":\"CAINIAO_STANDARD\"},\"transportMethodLineDTO\":{\"tradeOrderLineId\":8168683090133223,\"leadingTime\":{\"shippingProviderCode\":\"CAINIAO_STANDARD\",\"shippingProviderName\":\"AliExpress Standard Shipping\"},\"deliveryType\":\"CAINIAO_STANDARD\"}},\"features\":{\"featureMap\":{\"LOGISTICS_ALL_TAGS\":\"{\\\"maxTimestamp\\\":\\\"1686261600000\\\",\\\"provider\\\":\\\"cainiao\\\",\\\"etaTraceId\\\":\\\"PAY-f-1-919986676578000000-**********-**********-1686315243355-1686315243355-CAINIAO_STANDARD-DOOR_DELIVERY-OPERATING-DEF|SELLER_STOCK_UP-CALENDAR_DAY--RESOURCE_CODE#DEF-432000000-432000000---OPERATING-@DELIVERY_FULFIL-CALENDAR_DAY--DIVISION_ID#919986676578011000-**********-**********---OPERATING-|1684846443355\\\",\\\"minTimestamp\\\":\\\"1686261600000\\\"}\",\"dcm\":\"true\",\"dcma\":\"1\",\"dcmc\":\"USD\",\"dcmm\":\"passive\",\"dcmt\":\"1\",\"etk\":null,\"freightCommitDay\":\"90\",\"goodsPrepareTime\":\"4\",\"guaranteedDeliveryTime\":\"60\",\"hcd\":\"1\",\"hp\":\"1\",\"ical\":null,\"in_rs\":\"place_order_withhold\",\"in_st\":\"0^1_1^12000033452968370_2^4000000004749752972_3^4000000009361953243\",\"isRetail\":\"false\",\"local_order\":null,\"new\":\"1\",\"tci\":\"21\",\"uopPromiseTime\":\"1686315461779\",\"wc\":\"ae_marketplace\",\"wt\":\"ae_marketplace\"}},\"deliveryAddress\":{\"addressId\":4100097956830,\"receiver\":{\"phone\":\"1234568906\",\"phonePrefixCode\":\"+34\",\"phoneCountry\":\"+34\",\"mobileNo\":\"1234568906\",\"mobilePrefixCode\":\"+34\"},\"countryId\":0,\"addressType\":\"HOME\",\"countryName\":\"Spain\",\"postCode\":\"12345\",\"city\":\"Agost\",\"state\":\"Alacant\",\"thirdLevelAddressName\":\"\",\"locationTreeAddressId\":\"919986676578000000-919986676578011000\",\"locationTreeAddressName\":\"Alacant,Agost\",\"detailAddress\":\"rgbihbko\",\"languageCode\":\"en\",\"countryCode\":\"ES\",\"features\":{\"featureMap\":{}}},\"createTime\":1684846447.*********,\"createOutDeliveryTime\":1684846662.148000000,\"bizTenant\":1,\"volumePriceExtraMap\":{}}],\"sourceDTO\":{\"from\":\"SELECTIVE_CARTS\",\"sourceDevice\":{},\"sourceNetwork\":{\"clientIp\":\"*************\",\"sessionId\":\"8nJdTCPHb4LJkQeT99PHy5g1\"},\"sourceSite\":{\"locale\":\"en_US\"},\"sourceSystem\":{\"platformType\":\"DESKTOP\"},\"extraParams\":{\"MOBILE_ALIPAY_UMID_TOKEN\":null,\"MOBILE_API_NAME\":\"mtop.aliexpress.checkout.renderorder\",\"MOBILE_API_VERSION\":\"1.0\",\"MOBILE_MINI_WUA\":null,\"MOBILE_SERVER_ENV_RAW\":null,\"MOBILE_UMID_TOKEN\":\"T2gAL6kerUBbImp4-ie-gnXZmji5VTdd5_WmrQA6BQKjIWzTALL5wUxHURE8Jv1Yb-k=\",\"Referer\":\"https://www.aliexpress.com/p/trade/confirm.html?availableProductShopcartIds=11000026500001,11000026500002,11000026500003,11000026500004,11000026500005,11000026500006,11000026500007,11000026500009,11000026500010,11000026500011,11000026500012,81015248129003,81015248129004,81015248129005&aeOrderFrom=main_shopcart&curPageLogUid=1684846267945_Gzy2H5&spm=a2g0o.cart.0.0&_mtopPrev_=use-pre-acs\",\"_adid\":null,\"_anony_id\":null,\"_cna\":\"RHbzHG874RQCASp4SvbHquyK\",\"_utdid\":null,\"application\":\"CHECKOUT\",\"astore\":\"1\",\"cna\":\"RHbzHG874RQCASp4SvbHquyK\",\"entrance\":null,\"new\":\"new\",\"page_id\":\"2103272c16848462690241598ee123\",\"phase\":\"CREATE\",\"priceCenterRenderTotal\":\"USD 0.01\",\"renderPayPromotionFee\":\"USD 115.50\",\"subPlatform\":null}},\"freezeStatus\":\"NO_FROZEN\",\"promotionSnapshotId\":\"500022813420293223\",\"payTime\":1684846456.151000000,\"deliveryAddress\":{\"addressId\":4100097956830,\"receiver\":{\"phone\":\"1234568906\",\"phonePrefixCode\":\"+34\",\"phoneCountry\":\"+34\",\"mobileNo\":\"1234568906\",\"mobilePrefixCode\":\"+34\"},\"countryId\":0,\"addressType\":\"HOME\",\"countryName\":\"Spain\",\"postCode\":\"12345\",\"city\":\"Agost\",\"state\":\"Alacant\",\"thirdLevelAddressName\":\"\",\"locationTreeAddressId\":\"919986676578000000-919986676578011000\",\"locationTreeAddressName\":\"Alacant,Agost\",\"detailAddress\":\"rgbihbko\",\"languageCode\":\"en\",\"countryCode\":\"ES\",\"features\":{\"featureMap\":{}}}}";
        SendResult sendResult = mqConfig.sendMessage(topic, tag, key, str);
        resultDTO.setData(JSONObject.toJSONString(sendResult));
        resultDTO.setMessage(JSONObject.toJSONString(sendResult));
        resultDTO.setSuccess(true);
        return resultDTO;
    }

    @Override
    public ResultDTO getIntentionCurrencyCodes() {
        ResultDTO resultDTO = new ResultDTO();
        resultDTO.setData(JSONArray.toJSONString(Constant.INTENTION_CURRENCY));
        resultDTO.setSuccess(true);
        return resultDTO;
    }

    @Override
    public ResultDTO getCountryCodes() {
        ResultDTO resultDTO = new ResultDTO();
        resultDTO.setData(JSONArray.toJSONString(Constant.COUNTRY_CODES));
        resultDTO.setSuccess(true);
        return resultDTO;
    }

    @Override
    public ResultVo getTocJobs() {
        return ResultVo.ofSuccess(TimeoutEnum.getNames());
    }

    @Override
    public ResultDTO getShippingMethodList(String params, SystemDTO systemDTO) {
        JSONObject jsonObject = JSONObject.parseObject(params);
        String shippingScenario = jsonObject.getString("scenario");
        ResultDTO resultDTO = new ResultDTO();
        log.info("getShippingMethodList,shippingScenario:{}", shippingScenario);
        List<String> shippingMethods = ShippingMethodDiamond.getShippingMethods(shippingScenario);
        resultDTO.setData(shippingMethods != null ? String.join(",", ShippingMethodDiamond.getShippingMethods(shippingScenario)) : "");
        resultDTO.setMessage("success");
        resultDTO.setSuccess(true);
        log.info("getShippingMethodList,ResultVo:{}", JSON.toJSONString(resultDTO));
        return resultDTO;
    }

}
