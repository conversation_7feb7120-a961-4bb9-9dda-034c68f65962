package com.aliexpress.databank.hsf.provider;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.common.lang.StringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.global.landlord.LandlordContext;
import com.alibaba.global.qa.dc.model.builder.QueryResultBuilder;
import com.alibaba.global.qa.dc.model.param.SystemDTO;
import com.alibaba.global.qa.dc.model.result.QueryResultUnit;
import com.alibaba.global.qa.dc.model.result.ResultDTO;
import com.aliexpress.databank.dataobject.*;
import com.aliexpress.databank.hsf.DataApiService;
import com.aliexpress.databank.hsf.DataPoolService;
import com.aliexpress.databank.hsf.ProductService;
import com.aliexpress.databank.hsf.WalletService;
import com.aliexpress.databank.mapper.UserPoolMapper;
import com.aliexpress.databank.utils.HsfUtil;
import com.lazada.imptest.api.domain.ic.ProductResponseDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;


@Slf4j
@HSFProvider(serviceInterface = DataApiService.class, serviceGroup = "HSF", serviceVersion = "1.0.0", clientTimeout = 20000)
public class DataApiServiceImpl implements DataApiService {

	@Autowired
	DataPoolService dataPoolService;

	@Autowired
	ProductService productService;

	@Autowired
	AddressServiceImpl addressService;

	@Autowired
	MemberServiceImpl memberService;

	@Autowired
	WalletService walletService;

	@Resource
	UserPoolMapper userPoolMapper;

	/**
	 * gdc请求新增用户接口
	 * @param params params
	 * @param systemDTO systemDTO
	 */
	@Override
	public ResultDTO insertUserData(String params, SystemDTO systemDTO) throws Exception{
		ResultDTO result = new ResultDTO();
		Map<String, QueryResultUnit> data = new LinkedHashMap<>();
		LandlordContext.setTenantSpec("AE_GLOBAL", -1);
		JSONObject jsonObject = JSONObject.parseObject(params);
		String userParams = jsonObject.getString("userParams");
		String passwordParams = jsonObject.getString("passwordParams");
		String addressParams = jsonObject.getString("addressParams");

		try {
			List<String> res = dataPoolService.insertUser(userParams,passwordParams,addressParams,systemDTO);
			if(res.isEmpty()){
				result.setData("插入成功");
			}
			else {
				result.setData("插入失败: "+ res.toString());
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.setData("插入用户异常：" + e.getLocalizedMessage());
		}
		result.setSuccess(true);
		return result;
	}

	/**
	 * gdc请求新增商品接口
	 * @param params params
	 * @param systemDTO systemDTO
	 */
	@Override
	public ResultDTO insertProductData(String params, SystemDTO systemDTO) throws Exception{
		ResultDTO result = new ResultDTO();
		Map<String, QueryResultUnit> data = new LinkedHashMap<>();
		LandlordContext.setTenantSpec("AE_GLOBAL", -1);
		JSONObject jsonObject = JSONObject.parseObject(params);
		String productParams = jsonObject.getString("productParams");

		try {
			List<String> res = dataPoolService.insertProduct(productParams, systemDTO);
			if (res.isEmpty()){
				result.setData("插入成功");
			}
			else {
				result.setData("插入失败："+ res.toString());
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.setData("插入商品异常：" + e.getLocalizedMessage());
		}
		result.setSuccess(true);
		return result;
	}

	@Override
	public ResultDTO deleteInfo(String params, SystemDTO systemDTO){
		ResultDTO result = new ResultDTO();
		Map<String, QueryResultUnit> data = new LinkedHashMap<>();
		LandlordContext.setTenantSpec("AE_GLOBAL", -1);
		JSONObject jsonObject = JSONObject.parseObject(params);
		String productId = jsonObject.getString("productId");
		String userName = jsonObject.getString("userName");
		String str = "";
		try {
			if (StringUtil.isBlank(productId) && StringUtil.isBlank(userName)){
				str = "请输入参数";
			}
			else {
				Boolean res = dataPoolService.deleteUserOrProduct(userName,productId);
				if (res){
					str = "删除成功";
				}
				else {
					str = "删除失败";
				}
			}
			result.setData(str);

		}catch (Exception e) {
			result.setData("删除信息异常：" + e.getLocalizedMessage());
			e.printStackTrace();
		}
		result.setSuccess(true);
		return result;

	}


	@Override
	public ResultDTO checkUserPool(String params, SystemDTO systemDTO){
		ResultDTO result = new ResultDTO();
		Map<String, QueryResultUnit> data = new LinkedHashMap<>();
		LandlordContext.setTenantSpec("AE_GLOBAL", -1);
		JSONObject jsonObject = JSONObject.parseObject(params);
		String isAll = jsonObject.getString("isAll");
		String userName = jsonObject.getString("userName");
		List<UserPool> userPoolList = new ArrayList<>();
		UserPool userPool = new UserPool();
		try {
			result.setSuccess(true);
			if (StringUtil.isNotEmpty(userName)){
				userPool.setUserName(userName);
				userPoolList.add(userPool);
				userPoolList = dataPoolService.queryUserInfo(userPool,1);
				if (userPoolList.isEmpty()){
					result.setData("校验结果：池中无此用户，请先插入新增" );
					return result;
				}
			}
			else if (StringUtil.isNotBlank(isAll) && "是".equals(isAll)){
				userPool.setIsDel(0);
				userPoolList = dataPoolService.queryUserInfo(userPool,1000);
			}
			DataDisableDTO dataDisableDTO = checkUser(userPoolList);
			result.setData("校验结果：" + JSON.toJSONString(dataDisableDTO));
		}catch (Exception e) {
			e.printStackTrace();
			result.setData("检测结果异常：" + e.getLocalizedMessage());
		}
		return result;
	}

	@Override
	public ResultDTO checkProductPool(String params, SystemDTO systemDTO){
		ResultDTO result = new ResultDTO();
		Map<String, QueryResultUnit> data = new LinkedHashMap<>();
		LandlordContext.setTenantSpec("AE_GLOBAL", -1);
		JSONObject jsonObject = JSONObject.parseObject(params);
		String isAll = jsonObject.getString("isAll");
		String productId = jsonObject.getString("productId");
		List<ProductPool> productPoolList = new ArrayList<>();
		ProductPool productPool = new ProductPool();
		try {
			result.setSuccess(true);
			if (StringUtil.isNotEmpty(productId)){
				productPool.setProductId(Long.parseLong(productId));
				productPoolList.add(productPool);
				productPoolList = dataPoolService.queryProductInfo(productPool,1, 0L , 0L);
				if (productPoolList.isEmpty()){
					result.setData("校验结果：池中无此商品，请先插入新增" );
					return result;
				}
			}
			else if (StringUtil.isNotBlank(isAll) && "是".equals(isAll)){
				productPool.setIsDel(0);
				productPoolList = dataPoolService.queryProductInfo(productPool,1000, 0L, 0L);
			}
			DataDisableDTO dataDisableDTO = checkProduct(productPoolList);
			result.setData("校验结果：" + JSON.toJSONString(dataDisableDTO));
		}catch (Exception e) {
			result.setData("检测结果异常：" + e.getLocalizedMessage());
		}
		return result;

	}


	/**
	 * 检测用户可用性--已废弃、方法不可使用
	 * @param userPoolList 用户列表
	 * @return DataDisableDTO
	 */
	@Override
	public DataDisableDTO checkUser(List<UserPool> userPoolList) throws Exception {
		DataDisableDTO dataDisableDTO = new DataDisableDTO();
		List<String> noExistList = new ArrayList<>();
		List<String> riskList = new ArrayList<>();
		List<String> needConfirmList = new ArrayList<>();
		List<String> addressNotMatch = new ArrayList<>();
		if (!userPoolList.isEmpty()){
			for(UserPool userPool: userPoolList){
				UserPool userPoolRes = new UserPool();
				userPoolRes.setUserName(userPool.getUserName());
				JSONObject userInfo = HsfUtil.findByAccountId(userPool.getAccountId());
				//检测用户是否可用
				if (userInfo == null) {
					noExistList.add(userPool.getUserName());
					userPoolRes.setEnableType(0);
					dataPoolService.updateUser(userPoolRes);
					continue;
				}

				Long havanaId = memberService.getAccountByStr(userPool.getAccountId().toString()).getJSONObject("module").getLong("havanaId");
				log.info("~~~~~havanaId~~~~~~~"+havanaId);

				JSONObject userInfobuc = HsfUtil.findAccount(havanaId);//方法已注释
				//检测用户是否风控中
				if (!userInfo.getBoolean("enabled")){
					riskList.add(userPool.getUserName());
					userPoolRes.setEnableType(0);
					dataPoolService.updateUser(userPoolRes);
					continue;
				}//email且手机号码都未验证
				else if (userInfobuc != null && (userInfobuc.getJSONObject("returnValue").getInteger("tag") == 16 )){
					needConfirmList.add(userPool.getUserName());
					userPoolRes.setEnableType(0);
					dataPoolService.updateUser(userPoolRes);
					continue;
				}
				if (userInfobuc != null){
					log.info("~~~~~~~tag~~~~~~:" + userInfobuc.getJSONObject("returnValue").getInteger("tag"));
				}else{
					log.error("~~~~~~~tag~~~~~~:" + userInfobuc.getJSONObject("returnValue").getInteger("tag"));
				}
				//检测地址是否可用
				JSONObject addressResult = addressService.checkAddressExistV2(userPool.getAccountId(),userPool.getAddressTag());
				if(!addressResult.getBoolean("success")){
					addressNotMatch.add(userPool.getUserName());
					userPoolRes.setEnableType(0);
					dataPoolService.updateUser(userPoolRes);
				}
				else {
					userPoolRes.setEnableType(1);
					userPoolRes.setUserTag(dataPoolService.checkUserTag(userPool.getAccountId()));
					dataPoolService.updateUser(userPoolRes);
				}
			}
			dataDisableDTO.setUserNoExist(noExistList);
			dataDisableDTO.setUserRiskControl(riskList);
			dataDisableDTO.setAddressNotMatch(addressNotMatch);
			dataDisableDTO.setNeedConfirm(needConfirmList);
		}
		return dataDisableDTO;
	}

	/**
	 * 检测商品可用性
	 * @param productPoolList productId
	 * @return DataDisableDTO
	 */
	@Override
	public DataDisableDTO checkProduct(List<ProductPool> productPoolList){
		DataDisableDTO dataDisableDTO = new DataDisableDTO();
		try {
			List<String> noExistList = new ArrayList<>();
			List<String> statusErrorList = new ArrayList<>();
			List<String> notEnoughList = new ArrayList<>();
			List<String> stockWarnList = new ArrayList<>();
			if (!productPoolList.isEmpty()){
				for(ProductPool proList: productPoolList){
					JSONObject pro = HsfUtil.getProductByProductId(proList.getProductId());
					if (pro != null && !pro.getBoolean("success")) {
						ProductPool productPool = new ProductPool();
						productPool.setProductId(proList.getProductId());
						productPool.setEnableType(0);
						productPool.setIsDel(0);
						dataPoolService.updateProduct(productPool);
						noExistList.add(proList.getProductId().toString());
						continue;
					}
					ProductResponseDTO response =  productService.getProductInfo(proList.getProductId(),null);

					if(null != response){
						//非测试账号
						if (!dataPoolService.checkTestAccount(response.getSellerId())){
							dataPoolService.deleteUserOrProduct("",proList.getProductId().toString());
							continue;
						}
						Boolean res = dataPoolService.checkProductStatus(response.getSellerId(), proList.getProductId(),response.getStatus(),response.getAuditStatus());
						SkuData skuData = dataPoolService.getSkuData(response.getSkuIdAndSkuMap());
						ProductPool productPool = new ProductPool();
						productPool.setProductId(proList.getProductId());
						if (!res){
							productPool.setEnableType(0);
							statusErrorList.add(proList.getProductId().toString());
						}
						else if (skuData.getTotalStock() < 1){
							productPool.setEnableType(0);
							notEnoughList.add(proList.getProductId().toString());
						}
						else if (skuData.getTotalStock() < 10) {
							stockWarnList.add(proList.getProductId().toString());
							productPool.setStockNum(skuData.getTotalStock());
							productPool.setPrice(new Double(skuData.getMinSkuPrice()*100).longValue());
							// 报价币种存在特征字段
							productPool.setFeatures(response.getOriginalCurrencyCode());
						}
						else{
							productPool.setEnableType(1);
							productPool.setStockNum(skuData.getTotalStock());
							productPool.setPrice(new Double(skuData.getMinSkuPrice()*100).longValue());
							// 报价币种存在特征字段
							productPool.setFeatures(response.getOriginalCurrencyCode());
						}
						dataPoolService.updateProduct(productPool);
					}
				}
				dataDisableDTO.setProductNoExist(noExistList);
				dataDisableDTO.setProductNotEnough(notEnoughList);
				dataDisableDTO.setProductStatusError(statusErrorList);
				dataDisableDTO.setProductStockWarn(stockWarnList);
			}
		}catch (Exception e){
			e.printStackTrace();
		}
		return dataDisableDTO;
	}


	@Override
	public ResultDTO queryUserInfo(String params, SystemDTO systemDTO){
		ResultDTO result = new ResultDTO();
		try{
			LandlordContext.setTenantSpec("AE_GLOBAL", -1);
			JSONObject jsonObject = JSONObject.parseObject(params);
			String address = jsonObject.getString("address");
			String num = jsonObject.getString("num");
			String accountId = jsonObject.getString("accountId");
			String enableType = jsonObject.getString("enableType");
			String userName = jsonObject.getString("userName");
			String userTag = jsonObject.getString("userTag");
			UserPool userPool = new UserPool();
			if (StringUtil.isNotBlank(address)){
				userPool.setAddressTag(address);
			}
			if (StringUtil.isNotBlank(accountId)){
				userPool.setAccountId(Long.parseLong(accountId));
			}
			if (StringUtil.isNotBlank(enableType)){
				switch (enableType){
					case "不可用":
						userPool.setEnableType(0);
						break;
					case "可用":
						userPool.setEnableType(1);
						break;
					default:
						break;
				}
			}
			if (StringUtil.isNotBlank(userName)){
				userPool.setUserName(userName);
			}
			if (StringUtil.isNotBlank(userTag)){
				userPool.setUserTag(userTag);
			}
			int tempNum = 1000;
			if (StringUtil.isNotBlank(num)){
				tempNum = Integer.parseInt(num);
			}
			List<UserPool> userPoolList = dataPoolService.queryUserInfo(userPool,tempNum);
			if (!userPoolList.isEmpty()){
				Map<String, QueryResultUnit> data = QueryResultBuilder.buildQueryResult("查询用户", null, null, getUserList(userPoolList));
				result.setSuccess(true);
				result.setData(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
			}
		}catch (Exception e) {
			result.setData("查询异常: " + e.getLocalizedMessage());
			result.setSuccess(false);
		}
		return result;
	}

	@Override
	public ResultDTO queryProductInfo(String params, SystemDTO systemDTO){
		ResultDTO result = new ResultDTO();
		try{
			LandlordContext.setTenantSpec("AE_GLOBAL", -1);
			JSONObject jsonObject = JSONObject.parseObject(params);
			String num = jsonObject.getString("num");
			String categoryId = jsonObject.getString("categoryId");
			String enableType = jsonObject.getString("enableType");
			String productId = jsonObject.getString("productId");
			ProductPool productPool = new ProductPool();
			if (StringUtil.isNotBlank(categoryId)){
				productPool.setCategoryId(Long.parseLong(categoryId));
			}
			if (StringUtil.isNotBlank(enableType)){
				switch (enableType){
					case "不可用":
						productPool.setEnableType(0);
						break;
					case "可用":
						productPool.setEnableType(1);
						break;
					default:
						break;
				}
			}
			if (StringUtil.isNotBlank(productId)){
				productPool.setProductId(Long.parseLong(productId));
			}
			int tempNum = 1000;
			if (StringUtil.isNotBlank(num)){
				tempNum = Integer.parseInt(num);
			}
			List<ProductPool> productPoolList = dataPoolService.queryProductInfo(productPool,tempNum,0L, 0L);
			if (!productPoolList.isEmpty()){
				Map<String, QueryResultUnit> data = QueryResultBuilder.buildQueryResult("查询商品", null, null, getProductList(productPoolList));
				result.setSuccess(true);
				result.setData(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
			}
		}catch (Exception e) {
			result.setData("查询异常: " + e.getLocalizedMessage());
			result.setSuccess(false);
		}
		return result;
	}

	private List<UserPoolDTO> getUserList(List<UserPool> list){
		List<UserPoolDTO> userPoolDTOS = new ArrayList<>();
		if (null != list && !list.isEmpty()){
			for(UserPool userPool: list){
				UserPoolDTO userPoolDTO = new UserPoolDTO();
				userPoolDTO.setAccountId(userPool.getAccountId());
				userPoolDTO.setAddressTag(userPool.getAddressTag());
				userPoolDTO.setBuyerLocation(userPool.getBuyerLocation());
				userPoolDTO.setEnableType(userPool.getEnableType());
				userPoolDTO.setFeatures(userPool.getFeatures());
				userPoolDTO.setId(userPool.getId());
				userPoolDTO.setPassword(userPool.getPassword());
				userPoolDTO.setUserName(userPool.getUserName());
				userPoolDTO.setUserTag(userPool.getUserTag());
				userPoolDTOS.add(userPoolDTO);
			}
		}
		return userPoolDTOS;
	}

	private List<ProductPoolDTO> getProductList(List<ProductPool> list){
		List<ProductPoolDTO> productPoolDTOS = new ArrayList<>();
		if (null != list && !list.isEmpty()){
			for(ProductPool productPool: list){
				ProductPoolDTO productPoolDTO = new ProductPoolDTO();
				productPoolDTO.setCategoryId(productPool.getCategoryId());
				productPoolDTO.setProductId(productPool.getProductId());
				productPoolDTO.setStockNum(productPool.getStockNum());
				productPoolDTO.setEnableType(productPool.getEnableType());
				productPoolDTO.setFeatures(productPool.getFeatures());
				productPoolDTO.setId(productPool.getId());
				productPoolDTO.setSellerId(productPool.getSellerId());
				productPoolDTO.setPrice(Double.toString(productPool.getPrice().doubleValue()/100));
				productPoolDTOS.add(productPoolDTO);
			}
		}
		return productPoolDTOS;
	}


	@Override
	public ResultDTO deleteProduct(String params, SystemDTO systemDTO){
		ResultDTO result = new ResultDTO();
		try{
			LandlordContext.setTenantSpec("AE_GLOBAL", -1);
			JSONObject jsonObject = JSONObject.parseObject(params);
			String isUsed = jsonObject.getString("isUsed");
			String categoryId = jsonObject.getString("categoryId");
			String enableType = jsonObject.getString("enableType");
			String productId = jsonObject.getString("productId");
			ProductPool productPool = new ProductPool();
			if (StringUtil.isNotBlank(categoryId)){
				productPool.setCategoryId(Long.parseLong(categoryId));
			}
			if (StringUtil.isNotBlank(enableType)){
				productPool.setEnableType(Integer.parseInt(enableType));
			}
			if (StringUtil.isNotBlank(productId)){
				productPool.setProductId(Long.parseLong(productId));
			}
			if (StringUtil.isNotBlank(enableType)){
				productPool.setIsUsed(Integer.parseInt(isUsed));
			}
			Boolean res = dataPoolService.deleteProduct(productPool);
			if (res){
				result.setSuccess(true);
				result.setData("删除成功");
			}
		}catch (Exception e) {
			result.setData("删除异常: " + e.getLocalizedMessage());
			result.setSuccess(true);
		}
		return result;
	}

	@Override
	public ResultDTO deleteUser(String params, SystemDTO systemDTO){
		ResultDTO result = new ResultDTO();
		try{
			LandlordContext.setTenantSpec("AE_GLOBAL", -1);
			JSONObject jsonObject = JSONObject.parseObject(params);
			String address = jsonObject.getString("address");
			String isUsed = jsonObject.getString("isUsed");
			String accountId = jsonObject.getString("accountId");
			String enableType = jsonObject.getString("enableType");
			String userName = jsonObject.getString("userName");
			UserPool userPool = new UserPool();
			if (StringUtil.isNotBlank(address)){
				userPool.setAddressTag(address);
			}
			if (StringUtil.isNotBlank(isUsed)){
				userPool.setIsUsed(Integer.parseInt(isUsed));
			}
			if (StringUtil.isNotBlank(accountId)){
				userPool.setAccountId(Long.parseLong(accountId));
			}
			if (StringUtil.isNotBlank(enableType)){
				userPool.setEnableType(Integer.parseInt(enableType));
			}
			if (StringUtil.isNotBlank(userName)){
				userPool.setUserName(userName);
			}
			Boolean res = dataPoolService.deleteUser(userPool);
			if (res){
				result.setSuccess(true);
				result.setData("删除成功");
			}
		}catch (Exception e) {
			result.setData("删除异常: " + e.getLocalizedMessage());
			result.setSuccess(true);
		}
		return result;
	}

	@Override
	public ResultDTO updateUserStatus(String params, SystemDTO systemDTO){
		ResultDTO result = new ResultDTO();
		try{
			LandlordContext.setTenantSpec("AE_GLOBAL", -1);
			JSONObject jsonObject = JSONObject.parseObject(params);
			String buyerId = jsonObject.getString("buyerId");
			String status = jsonObject.getString("status");
			Boolean userFlag = dataPoolService.checkTestAccount(Long.parseLong(buyerId));
			result.setSuccess(true);
			if (!userFlag){
				result.setData("非测试账号不能操作");
				return result;
			}
			JSONObject res = HsfUtil.updateUserStatus(Long.parseLong(buyerId), Integer.parseInt(status));
			if (res != null) {
				result.setData("结果：" + res.toJSONString());
			}
		}catch (Exception e) {
			result.setData("删除异常: " + e.getLocalizedMessage());
			result.setSuccess(true);
		}
		return result;
	}

	@Override
	public ResultDTO insertTestAccount(String params, SystemDTO systemDTO){
		ResultDTO result = new ResultDTO();
		Map<String, QueryResultUnit> data = new LinkedHashMap<>();
		LandlordContext.setTenantSpec("AE_GLOBAL", -1);

		JSONObject jsonObject = JSONObject.parseObject(params);
		String userStr = jsonObject.getString("userStr");
		Long days = jsonObject.getLong("days");
		JSONObject  json = null;
		try {
			json = memberService.getAccountByStr(userStr);
			if (json == null || json.isEmpty()){
				result.setData("查询用户信息异常");
				result.setSuccess(false);
				return result;
			}

			String userId = json.getJSONObject("module").getString("userId");
			String email = json.getJSONObject("module").getString("email");
			Boolean isTest = dataPoolService.checkTestAccount(Long.parseLong(userId));
			if (!isTest){
				result.setData("非测试账号不允许插入");
			}
			if (StringUtil.isNotBlank(userId)){
				UserPool userPool0 = new UserPool();
				userPool0.setAccountId(Long.parseLong(userId));
				List<UserPool> poolList = userPoolMapper.userList(userPool0,1);
				//更新
				if (null != poolList && !poolList.isEmpty()){
					UserPool userPool = new UserPool();
					userPool.setScene("PAD_CRO");
					userPool.setMemberId(walletService.getMemberIdByUserId(userId));
					userPool.setGmtDead(System.currentTimeMillis()/1000 + days*24*60*60);
					userPool.setUserName(email);
					if (userPoolMapper.updateUser(userPool)){
						result.setData("用户已存在，更新有效时间成功");
					}
					else {
						result.setData("更新用户失败");
					}
				} //新增
				else {
					List<UserPool> userPoolList = new ArrayList<>();
					UserPool userPool = new UserPool();
					userPool.setScene("PAD_CRO");
					userPool.setMemberId(walletService.getMemberIdByUserId(userId));
					userPool.setUserTag("");
					userPool.setAccountId(Long.parseLong(userId));
					userPool.setPassword("");
					userPool.setUserName(email);
					userPool.setIsUsed(0);
					userPool.setEnableType(1);
					userPool.setBuyerLocation("");
					userPool.setAddressTag("");
					userPool.setFeatures("");
					userPool.setGmtDead(System.currentTimeMillis()/1000 + days*24*60*60);
					userPoolList.add(userPool);
					if (userPoolMapper.insertUserList(userPoolList)){
						result.setData("插入测试用户成功");
					}
					else {
						result.setData("插入测试用户失败");
					}
				}

			}
			else {
				result.setData("插入测试用户失败");
			}
			result.setSuccess(true);
		} catch (Exception e) {
			e.printStackTrace();
			throw new RuntimeException(e);
		}
		return result;
	}
}
