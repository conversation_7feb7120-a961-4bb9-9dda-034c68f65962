package com.aliexpress.databank.hsf.provider;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.common.lang.StringUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.global.qa.dc.model.builder.QueryResultBuilder;
import com.alibaba.global.qa.dc.model.param.SystemDTO;
import com.alibaba.global.qa.dc.model.result.QueryResultUnit;
import com.alibaba.global.qa.dc.model.result.ResultDTO;
import com.aliexpress.databank.hsf.DataModelQueryService;
import com.aliexpress.shadowdata.share.object.OrderRequestContion;
import com.aliexpress.shadowdata.share.remote.response.OrderResponseDTO;
import com.aliexpress.shadowdata.share.remote.service.interfaces.OrderDataModeRemoteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
@HSFProvider(serviceInterface = DataModelQueryService.class, serviceVersion = "1.0.0", serviceGroup = "HSF",clientTimeout = 20000)
public class DataModelQueryServiceImpl implements DataModelQueryService {

    @Autowired
    private OrderDataModeRemoteService orderDataModeRemoteService;

    @Override
    public ResultDTO getOrdersByCondition(String params, SystemDTO systemDTO) {
        {

            ResultDTO resultDTO = new ResultDTO();
            Map<String, QueryResultUnit> resultMap = new LinkedHashMap<>();
            JSONObject jsonObj = JSONObject.parseObject(params);

            String orderBaseTag = jsonObj.getString("orderBaseTag");
            String tradeFlowTag = jsonObj.getString("tradeFlowTag");
            String orderTypeTag = jsonObj.getString("orderTypeTag");//muti
            String promotionTag = jsonObj.getString("promotionTag");//muti
            Boolean isTester = jsonObj.getBoolean("isTester");

            OrderRequestContion dataRequestContion = new OrderRequestContion();
            List<String> orderBaseTags = new ArrayList<>();
            List<String> orderTypeTags = new ArrayList<>();
            List<String> promotionTags = new ArrayList<>();
            List<String> tradeFlowTags = new ArrayList<>();

            if(StringUtil.isNotBlank(orderBaseTag)  && !"null".equalsIgnoreCase(orderBaseTag)){
                orderBaseTags.add(orderBaseTag);
            }

            if(StringUtil.isNotBlank(tradeFlowTag)  && !"null".equalsIgnoreCase(tradeFlowTag)){
                tradeFlowTags.add(tradeFlowTag);
            }

            if(StringUtil.isNotBlank(orderTypeTag) && !"null".equalsIgnoreCase(orderTypeTag)){
                String[] str = orderTypeTag.split(",");
                orderTypeTags.addAll(Arrays.asList(str));
            }
            if(StringUtil.isNotBlank(promotionTag) && !"null".equalsIgnoreCase(promotionTag)){
                String[] str = promotionTag.split(",");
                promotionTags.addAll(Arrays.asList(str));
            }

            dataRequestContion.setOrderStatusTag(orderBaseTags);
            dataRequestContion.setTradeProcessTag(tradeFlowTags);
            dataRequestContion.setOrderTypeTags(orderTypeTags);
            dataRequestContion.setUmpDataTags(promotionTags);

            List<OrderResponseDTO> onlineDataResponseDTOs = orderDataModeRemoteService.getOrderByCondition(dataRequestContion,isTester);
            List<OrderResponseDTO> resultList = new ArrayList<>();
            if(onlineDataResponseDTOs.size() > 100){
                resultList = onlineDataResponseDTOs.subList(0,99);
            }else{
                resultList = onlineDataResponseDTOs;
            }

            try{
                resultMap.putAll(QueryResultBuilder.buildQueryResult("订单列表",
                        (List)null, (List)null, resultList));
                resultDTO.setSuccess(true);
                resultDTO.setData(JSONObject.toJSONString(resultMap,SerializerFeature.WriteDateUseDateFormat));
            }catch(Exception e){
                e.printStackTrace();
            }

            return resultDTO;
        }
    }
}
