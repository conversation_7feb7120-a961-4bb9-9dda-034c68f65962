package com.aliexpress.databank.hsf;

import com.alibaba.global.order.management.api.model.TradeOrderDTO;
import com.alibaba.global.qa.dc.model.param.SystemDTO;
import com.alibaba.global.qa.dc.model.result.ResultDTO;
import com.aliexpress.databank.dataobject.CheckPriceDTO;

import java.util.List;

public interface OrderPriceService {

    ResultDTO getOrderDpPriceById(String params, SystemDTO systemDTO) throws Exception;

    List<CheckPriceDTO> checkPrices (TradeOrderDTO tradeOrderDTO) throws Exception;

    }
