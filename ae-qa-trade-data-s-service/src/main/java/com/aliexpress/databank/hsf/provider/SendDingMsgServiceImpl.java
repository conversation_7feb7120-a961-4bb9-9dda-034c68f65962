package com.aliexpress.databank.hsf.provider;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.common.lang.StringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.fpark.bings.executor.StandardThreadExecutor;
import com.alibaba.global.landlord.LandlordContext;
import com.alibaba.global.qa.dc.model.builder.QueryResultBuilder;
import com.alibaba.global.qa.dc.model.param.SystemDTO;
import com.alibaba.global.qa.dc.model.result.QueryResultUnit;
import com.alibaba.global.qa.dc.model.result.ResultDTO;
import com.alibaba.rocketmq.client.producer.SendResult;
import com.alibaba.rocketmq.client.producer.SendStatus;
import com.alibaba.rocketmq.common.message.Message;
import com.alibaba.xxpt.gateway.shared.client.http.ExecutableClient;
import com.alibaba.xxpt.gateway.shared.client.http.PostClient;
import com.aliexpress.databank.diamond.hpy.ChargeBackResultMsgTemplate;
import com.aliexpress.databank.diamond.hpy.ChargeBackResultRuleList;
import com.aliexpress.databank.hsf.DataPoolService;
import com.aliexpress.databank.hsf.SendDingMsgService;
import com.aliexpress.databank.hsf.multiTask.NewMultiExecutorGroup;
import com.aliexpress.databank.utils.HsfUtil;
import com.aliexpress.databank.utils.HttpUtils;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiRobotSendRequest;
import com.dingtalk.api.response.OapiRobotSendResponse;
import com.google.common.collect.HashMultimap;
import com.google.common.collect.Maps;
import com.google.common.collect.Multimap;
import com.taobao.api.ApiException;
import com.taobao.diamond.client.impl.DiamondUnitSite;
import com.taobao.eagleeye.EagleEye;
import com.taobao.metaq.client.MetaProducer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.Charset;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.Future;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Created by limeng on 2021/12/1.
 */

@HSFProvider(serviceInterface = SendDingMsgService.class, serviceGroup = "HSF", serviceVersion = "1.0.0", clientTimeout = 20000)
@Slf4j
public class SendDingMsgServiceImpl implements SendDingMsgService {
//    @Autowired
//    private SatelliteMetaProducer metaProducer;

//    @Autowired
//    private PaymentBopsToolFacade paymentBopsToolFacade;

    @Autowired
    private DataPoolService dataPoolService;


    private static final String DING_API_URL = "https://oapi.dingtalk.com/topapi/message/corpconversation/asyncsend_v2?access_token=";

    private static final String CORP_ACCESS_TOKEN_API = "/token/corpAccessToken.json";

    private static Logger logger = LoggerFactory.getLogger(SendDingMsgServiceImpl.class);

    @Override
    public String sendMetaq(String topic, String messageBody, String messageTag, String envType, String ip, String buyerId, String keys, String dPathEnv, String aepay) {
        MetaProducer metaProducer=new MetaProducer("lmtest");
        metaProducer.setClientIP(ip);
        String enableOnlineMetaq = chargeBackResultMsgTemplate.getEnableOnlineMetaq();
        if (!Boolean.parseBoolean(enableOnlineMetaq) && org.apache.commons.lang3.StringUtils.equalsIgnoreCase("online", envType)) {
            log.error("enableOnlineMetaqNotSupport|"+ EagleEye.getTraceId() + "|" + enableOnlineMetaq + "|" + envType);
            return "not support online metaq";
        }

        switch (envType) {
            case "线上":
                metaProducer.setUnitName("aliyun-region-vpc-ap-southeast-1");
                metaProducer.setInstanceName("aliyun-region-vpc-ap-southeast-1");
                break;
            case "德国预发":
                metaProducer.setUnitName("aliyun-vpc-de-pre");
                metaProducer.setInstanceName("aliyun-vpc-de-pre");
                break;
            case "美东预发":
                metaProducer.setUnitName("rg-us-east-pre");
                metaProducer.setInstanceName("rg-us-east-pre");
                break;
            case "俄罗斯预发":
                metaProducer.setUnitName("rg-ru-pre");
                metaProducer.setInstanceName("rg-ru-pre");
                break;
            case "新加坡预发":
                metaProducer.setUnitName("aliyun-region-vpc-ap-southeast-1-pre");
                metaProducer.setInstanceName("aliyun-region-vpc-ap-southeast-1-pre");
                break;
            case "国际化新加坡预发":
                metaProducer.setUnitName("rg-sg-pre");
                metaProducer.setInstanceName("rg-sg-pre");
            default:
                metaProducer.setUnitName("rg-us-east-pre");
                metaProducer.setInstanceName("rg-us-east-pre");
        }
        boolean status=false;
        JSONObject res = new JSONObject();
        Boolean testFlag = dataPoolService.checkTestAccount(Long.parseLong(buyerId));
        if (!Boolean.parseBoolean(enableOnlineMetaq)) {
            if (!messageBody.contains(":" + buyerId + ",") && !messageBody.contains("\"" + buyerId + "\"") & !testFlag) {
                return "发送消息只支持测试账号";
            }
        }

        try {
            if (StringUtil.isNotBlank(aepay)){
                EagleEye.putUserData("voyager_env_flag","release_pre");
            }
            if (StringUtil.isNotBlank(dPathEnv)){
                EagleEye.putUserData("dpath_env",dPathEnv);
            }
            metaProducer.start();
            //如果key为null，补充一个key，buyerId+时间戳
            if (StringUtil.isBlank(keys)) {
                keys = buyerId + "_" + System.currentTimeMillis();
            }
            Message msg = new Message(topic, // topic
                    messageTag, // tag
                    keys, // key，消息的Key字段是为了唯一标识消息的，方便运维排查问题。如果不设置Key，则无法定位消息丢失原因。
                    messageBody.getBytes(Charset.forName("UTF-8")));// body
            SendResult sendResult = metaProducer.send(msg);
             status = sendResult != null && SendStatus.SEND_OK.name().equals(sendResult.getSendStatus().name());

//    System.err.println("send message: " + messageBody);
//    System.err.println("send status: " + sendResult.getSendStatus());
        } catch (Exception e) {
            res.put("errorMsg",e.getMessage());
            log.error(e.getMessage());
            System.out.println("send message failed, messageTopic:" + topic + "; key:" + keys + e.getMessage());
        }
        metaProducer.shutdown();
        res.put("status",status);
        res.put("traceId",EagleEye.getTraceId());
        res.put("messageKey",keys);
        return JSON.toJSONString(res);

    }

//   @Override
//   public String sendMessage(String topic, String messageBody, String messageTag, String keys) {
//           try {
//               Message msg = new Message(topic, messageTag, messageBody.getBytes(Charset.forName("UTF-8")));
////            msg.setKeys(keys);
//               SendResult sendResult = metaProducer.send(msg);
//
////            log(messageBody, topic, messageTag, keys, sendResult, true);
//               boolean status=sendResult != null && SendStatus.SEND_OK.name().equals(sendResult.getSendStatus().name());
//
//               return status + EagleEye.getTraceId();
//
//           } catch (Exception e) {
////            log(messageBody, topic, messageTag, keys, null, false);
//               System.out.println("send message failed, messageTopic:" + topic + "; key:" + keys+e.getMessage());
//               return "false"+ EagleEye.getTraceId() ;
//           }
//       }


    @Override
    public ResultDTO SendMQMsg(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();

        Map<String, QueryResultUnit> data = new LinkedHashMap<>();
        LandlordContext.setTenantSpec("AE_GLOBAL", -1);


        JSONObject jsonObject = JSONObject.parseObject(params);
        String empId = systemDTO.getOperator();
        String topic = jsonObject.getString("topic");
        String messageTag = jsonObject.getString("messageTag");
        String messageBody = jsonObject.getString("messageBody");
        String envType =  jsonObject.getString("envType");
        String ip =  jsonObject.getString("ip");
        String buyerId = jsonObject.getString("buyerId");
        String dpath =  jsonObject.getString("dpath");
        String aepay = jsonObject.getString("aepay");
        String res = null;
        if (StringUtil.isNotBlank(buyerId)){
            try {
                if (!"pre".equals(aepay)){
                    aepay = "";
                }
                res = sendMetaq(topic, messageBody, messageTag, envType, ip, buyerId,null,dpath,aepay);
                result.setData("发送消息结果：" + res);
                result.setSuccess(true);
                HsfUtil.measureAll("/payment/jobId="+systemDTO.getSite(),empId);
            } catch (Exception e) {
                result.setSuccess(false);
                result.setData("发送消息结果异常：" + res);
            }
        }
        else{
            result.setSuccess(false);
            result.setData("发送消息只支持测试账号");
        }
        return result;

    }


    @Override
    public ResultDTO TestSendDingMsg(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();

        Map<String, QueryResultUnit> data = new LinkedHashMap<>();
        LandlordContext.setTenantSpec("AE_GLOBAL", -1);


        JSONObject jsonObject = JSONObject.parseObject(params);
        String userNo = jsonObject.getString("userNo");
        List<String> userList = new ArrayList<String>();
        userList.add(userNo);
        Boolean respon = false;
        try {
            respon = sendDingMessage("UI testcase", "testcase runSuccess,clickme" + new Date(), "http://awt.alibaba-inc.com/ui/task/list", userList);
            Map<String, QueryResultUnit> sendResult = QueryResultBuilder.buildQueryResult("发钉钉消息", null, null, respon);
            data.putAll(sendResult);

            sendDingTalkGroup("UI testcase test");

            result.setData(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
            result.setSuccess(true);
        } catch (Exception e) {
            result.setSuccess(false);
        }


        return result;

    }


    public static String getCorpAccessToken(ExecutableClient executableClient) throws Exception {

        executableClient.setAccessKey("ae-qa-automation-b-Ejg9xRfiMzq");
        executableClient.setDomainName("s-api.alibaba-inc.com");
        executableClient.setProtocal("https");
        executableClient.setSecretKey("pCouxjK7960Ja1o3N6ki2481thGLabY5biZ2H7Jz");

        executableClient.init();

        PostClient postClient = executableClient.newPostClient(CORP_ACCESS_TOKEN_API);
        postClient.addParameter("appCode", "passport");
        postClient.addParameter("corpId", "dingd8e1123006514592");

        String res = postClient.post();

        JSONObject jsonObject = JSON.parseObject(res);

        return jsonObject.getString("content");
    }

    @Override

    public Boolean sendDingMessage(String appName, String content, String url, List<String> receiverList) {
        String token = null;
        try {
            token = getCorpAccessToken(ExecutableClient.getInstance());
        } catch (Exception e) {
            logger.error("getCorpAccessToken exception, e: " + e.toString());
            return false;
        }

        Map<String, String> postMap = new HashMap<>();
        Map<String, Object> msgMap = new HashMap<>();

        postMap.put("agent_id", "*********");
        postMap.put("userid_list", StringUtils.join(receiverList, ","));

        Map<String, String> linkMap = new HashMap<>();
        Date dt = new Date();
        linkMap.put("title", "lm." + appName);
        linkMap.put("picUrl", "lm." + appName);
        linkMap.put("text", content + dt.toString());
        linkMap.put("messageUrl", url);

        msgMap.put("link", linkMap);
        msgMap.put("msgtype", "link");

        postMap.put("msg", JSON.toJSONString(msgMap));

        String res = HttpUtils.doPost(DING_API_URL + token, postMap);
        logger.info(res);
        return true;
    }

    @Override
    public Boolean sendDingMessage(String msg, List<String> receiverList) {
        String token = null;
        try {
            token = getCorpAccessToken(ExecutableClient.getInstance());
        } catch (Exception e) {
            logger.error("getCorpAccessToken exception, e: " + e.toString());
            return false;
        }

        if (StringUtils.isBlank(msg) || receiverList.isEmpty()) {
            return false;
        }

        Map<String, String> postMap = new HashMap<>();

        postMap.put("agent_id", "*********");
        postMap.put("userid_list", StringUtils.join(receiverList, ","));

        postMap.put("msg", msg);

        String res = HttpUtils.doPost(DING_API_URL + token, postMap);
        logger.info(res);
        return true;
    }

    @Override
    public OapiRobotSendResponse sendDingTalkGroup(String msg) {

        logger.info("send dingTalkMessage:{}", msg);
        Long timestamp = System.currentTimeMillis();
        String robertUrl
                = "https://oapi.dingtalk.com/robot/send?access_token=048ab530257d828c055368f11391754118ea9d08ea96d0f2922f4f9f1b733409";

        DingTalkClient client = null;
        try {
            client = new DefaultDingTalkClient(
                    robertUrl + "&timestamp=" + timestamp + "&sign=" + getDingTalkSign(timestamp));
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }

        OapiRobotSendRequest req = new OapiRobotSendRequest();
        req.setMsgtype("markdown");
        OapiRobotSendRequest.Markdown markdown = new OapiRobotSendRequest.Markdown();
        markdown.setTitle("异常告警");
        markdown.setText(msg);
        req.setMarkdown(markdown);
        OapiRobotSendResponse rsp = null;
        try {
            rsp = client.execute(req);
        } catch (ApiException e) {
            logger.error("error when send DingTalk:{},rsp:{}", e.getErrMsg(), rsp);
        }
        return rsp;
    }

    public static String getDingTalkSign(Long timestamp) throws Exception {

        String secret = "SEC7982a4ae3dbcff46e800809d90353daa51be97a9a944a2afbc5352f1db50ecc1";

        String stringToSign = timestamp + "\n" + secret;
        Mac mac = Mac.getInstance("HmacSHA256");
        mac.init(new SecretKeySpec(secret.getBytes("UTF-8"), "HmacSHA256"));
        byte[] signData = mac.doFinal(stringToSign.getBytes("UTF-8"));
        return URLEncoder.encode(new String(Base64.encodeBase64(signData)), "UTF-8");
    }

    @Autowired
    private ChargeBackResultMsgTemplate chargeBackResultMsgTemplate;

    @Autowired
    private ChargeBackResultRuleList chargeBackResultRuleList;

    @Autowired
    private NewMultiExecutorGroup multiExecutorGroup;
    /**
     * 多线程处理
     */
    private List<?> threadHandler(List<String> ruleList){
        List<List<String>> lists = splitList(ruleList, 15);

        AtomicLong successCnt = new AtomicLong();

        List<Long> result = new CopyOnWriteArrayList<>();

        List<Long> bizOrderIdsFailedTotal =  new CopyOnWriteArrayList<>();
        List<Long> bizOrderIdsSuccessTotal =  new CopyOnWriteArrayList<>();
        List<Long> bizOrderIdsPaidTotal =  new CopyOnWriteArrayList<>();
        Map<Long, String> bizOrderId2InstructionNoTotal = Maps.newConcurrentMap();

        Map<Long, JSONObject> bizOrderId2DisputeAmountTotal = Maps.newConcurrentMap();

        StandardThreadExecutor busyPoolExecutor = multiExecutorGroup.getBusyStandardPool();

        List<Future<Map<String, Object>>> futureList = new CopyOnWriteArrayList<>();

        try{
            for (List<String> list : lists) {
                futureList.add(busyPoolExecutor.submit(new Callable<Map<String, Object>>() {
                    @Override
                    public Map<String, Object> call() {
                        Map<String, Object> resultMap = Maps.newHashMap();
                        List<Long> bizOrderIdsFailed = Lists.newArrayList();
                        List<Long> bizOrderIdsSuccess = Lists.newArrayList();
                        List<Long> bizOrderIdsPaid = Lists.newArrayList();
                        Map<Long, JSONObject> bizOrderId2DisputeAmount = Maps.newConcurrentMap();
                        Map<Long, String> bizOrderId2InstructionNo = Maps.newConcurrentMap();

                        long cntSuccess = 0;
                        Map<String, Long> totalMoneySuccess = Maps.newHashMap();

                        try {
                            for (String each: list) {
                                try {
                                    String[] split = each.split("&&&");
                                    JSONObject queryPaymentDetailResponse = HsfUtil.queryPaymentDetail(split[0]);

                                    String tradeOrderNo = queryPaymentDetailResponse.getString("tradeOrderNo");
                                    bizOrderId2InstructionNo.put(Long.valueOf(tradeOrderNo), split[0]);

                                    // 是否追偿成功；
                                    String payerId = queryPaymentDetailResponse.getString("payerId");


                                    JSONArray payOrderInfoList = queryPaymentDetailResponse.getJSONArray("payOrderInfoList");
                                    Iterator<Object> iterator = payOrderInfoList.stream().iterator();
                                    boolean hasPaid = false;
                                    while (iterator.hasNext()) {
                                        JSONObject next = (JSONObject)iterator.next();
                                        String status = next.getString("status");
                                        log.error("PayOrderInfoVOStatus|" + EagleEye.getTraceId() + "|" + tradeOrderNo + "|" + split[0] + "|" + status);
                                        if (null != status && status.equalsIgnoreCase("paid")){
                                            hasPaid = true;
                                        }
                                    }

                                    if (hasPaid) {
                                        bizOrderIdsPaid.add(Long.valueOf(tradeOrderNo));
                                    }


                                    String s = split[1];
                                    JSONObject jsonObject = JSON.parseObject(s);
                                    JSONObject disputeAmount = (JSONObject) jsonObject.get("disputeAmount");
                                    String value = disputeAmount.getString("value");
                                    String currency = disputeAmount.getString("currency");
                                    String disputeId = jsonObject.getString("disputeId");

                                    if ( null != bizOrderId2DisputeAmount.get(Long.valueOf(tradeOrderNo))){
                                        log.error("duplicatedError|" + EagleEye.getTraceId() + "|" + tradeOrderNo + "|" + split[0] + "|" + payerId);
                                    }

                                    bizOrderId2DisputeAmount.put(Long.valueOf(tradeOrderNo), disputeAmount);

                                    JSONObject chargeBackRecoverSellerResponse = HsfUtil.chargeBackRecoverSeller(Long.valueOf(payerId.trim())
                                            , tradeOrderNo
                                            , disputeId
                                            , Long.valueOf(value)
                                            , currency);

                                    if (Objects.isNull(chargeBackRecoverSellerResponse) ||
                                            (Objects.nonNull(chargeBackRecoverSellerResponse) && chargeBackRecoverSellerResponse.getBoolean("succeeded"))) {
                                        cntSuccess++;
                                        bizOrderIdsSuccess.add(Long.valueOf(tradeOrderNo));
                                        if(null != totalMoneySuccess.get(currency)){
                                            Long aLong = totalMoneySuccess.get(currency);
                                            totalMoneySuccess.put(currency, aLong + Long.valueOf(value));
                                        } else {
                                            totalMoneySuccess.put(currency, Long.valueOf(value));
                                        }
                                    } else {
                                        if (org.apache.commons.lang3.StringUtils.isNotBlank(tradeOrderNo)) {
                                            bizOrderIdsFailed.add(Long.valueOf(tradeOrderNo));
                                        }
                                    }

                                } catch (Throwable t) {
                                    log.error("queryPaymentDetailError|" + EagleEye.getTraceId() + "|" + each, t);
                                }
                            }
                        } catch (Throwable t) {
                            log.error("chargeBackRecoverSellererror|" + EagleEye.getTraceId() + "|", t);
                        }

                        resultMap.put("bizOrderId2InstructionNo", bizOrderId2InstructionNo);
                        resultMap.put("bizOrderIdsPaid", bizOrderIdsPaid);
                        resultMap.put("bizOrderIdsFailed", bizOrderIdsFailed);
                        resultMap.put("totalMoneySuccess", totalMoneySuccess);
                        resultMap.put("cntSuccess", cntSuccess);
                        resultMap.put("bizOrderIdsSuccess", bizOrderIdsSuccess);
                        resultMap.put("bizOrderId2DisputeAmount",bizOrderId2DisputeAmount);

                        log.error("currentGroup|" + EagleEye.getTraceId() + "|" + JSONObject.toJSONString(resultMap));
                        return resultMap;
                    }
                }));
            }


            log.info(EagleEye.getTraceId() + "查询数据个数|" + futureList.size());
            futureList.remove(null);

            Map<String, Long> totalMoneySuccessTongji = Maps.newConcurrentMap();

            futureList.forEach(future -> {
                try {
                    log.info(EagleEye.getTraceId() +"|messg:" + future.get());
                    if ((future.get() != null)){
                        log.info(EagleEye.getTraceId() +"|线程消息future："+  + System.currentTimeMillis());

                        Map<Long, String> bizOrderId2InstructionNo = Optional.ofNullable(future.get()).map(i -> (Map<Long, String>)i.get("bizOrderId2InstructionNo")).orElse(Maps.newHashMap());
                        bizOrderId2InstructionNoTotal.putAll(bizOrderId2InstructionNo);

                        List<Long> bizOrderIdsPaid = Optional.ofNullable(future.get()).map(i -> (List<Long>)i.get("bizOrderIdsPaid")).orElse(Lists.newArrayList());
                        bizOrderIdsPaidTotal.addAll(bizOrderIdsPaid);


                        List<Long> bizOrderIdsFailed = Optional.ofNullable(future.get()).map(i -> (List<Long>)i.get("bizOrderIdsFailed")).orElse(Lists.newArrayList());
                        bizOrderIdsFailedTotal.addAll(bizOrderIdsFailed);
                        List<Long> bizOrderIdsSuccess = Optional.ofNullable(future.get()).map(i -> (List<Long>)i.get("bizOrderIdsSuccess")).orElse(Lists.newArrayList());
                        bizOrderIdsSuccessTotal.addAll(bizOrderIdsSuccess);

                        Map<Long, JSONObject> bizOrderId2DisputeAmount = Optional.ofNullable(future.get()).map(i -> (Map<Long, JSONObject>)i.get("bizOrderId2DisputeAmount")).orElse(Maps.newHashMap());

                        bizOrderId2DisputeAmountTotal.putAll(bizOrderId2DisputeAmount);

                        long cntSuccess = Optional.ofNullable(future.get()).map(i -> (long)i.get("cntSuccess")).orElse(0L);
                        successCnt.addAndGet(cntSuccess);
                        Map<String, Long> totalMoneySuccess = Optional.ofNullable(future.get()).map(i -> (Map<String, Long>)i.get("totalMoneySuccess")).orElse(Maps.newHashMap());

                        for (Map.Entry<String, Long> stringLongEntry : totalMoneySuccess.entrySet()) {
                            String currency = stringLongEntry.getKey();
                            long value = stringLongEntry.getValue();

                            if(null != totalMoneySuccessTongji.get(currency)){
                                AtomicLong atomicLong = new AtomicLong(totalMoneySuccessTongji.get(currency));
                                totalMoneySuccessTongji.put(currency, atomicLong.addAndGet(Long.valueOf(value)));
                            } else {
                                totalMoneySuccessTongji.put(currency, value);
                            }
                        }

                    }
                } catch (Exception e) {
                    log.error("futuregetError|" + EagleEye.getTraceId(), e);
                }
            });

            log.error("orderIds_hpy|" + EagleEye.getTraceId() + "|" +
                    JSON.toJSONString(result) + "|" + successCnt + "|" + totalMoneySuccessTongji + "|bizOrderIdsFailedTotal|" +
                    bizOrderIdsFailedTotal+ "|bizOrderIdsSuccessTotal|" + bizOrderIdsSuccessTotal + "|bizOrderId2DisputeAmountTotal|"
                    + JSON.toJSONString(bizOrderId2DisputeAmountTotal) + "|bizOrderIdPaidTotal|" + bizOrderIdsPaidTotal +
                    "|bizOrderId2InstructionNoTotal|" + JSON.toJSONString(bizOrderId2InstructionNoTotal)
            );

            return result;
        }catch (Throwable t) {
            log.error("chargeBackRecoverSellererror11|" + EagleEye.getTraceId() + "|", t);
        }
        return null;
    }

    private List<List<String>> splitList(List<String> list , int groupSize){
        int length = list.size();
        // 计算可以分成多少组
        int num = ( length + groupSize - 1 )/groupSize ; // TODO
        List<List<String>> newList = new ArrayList<>(num);
        for (int i = 0; i < num; i++) {
            // 开始位置
            int fromIndex = i * groupSize;
            // 结束位置
            int toIndex = (i+1) * groupSize < length ? ( i+1 ) * groupSize : length ;
            newList.add(list.subList(fromIndex,toIndex)) ;
        }
        return  newList ;
    }

    @Override
    public ResultDTO SendMQMsghpy(String params) throws Exception {
        log.error("sendChargeBackResultMetaq#start|" + EagleEye.getTraceId() + "|" +params);

        if (params.equalsIgnoreCase("newProtocolopen")) {
            Map<String, String> loseField = Maps.newHashMap();

            List<String> ruleList = ChargeBackResultRuleList.getChargeOpenList();
            // 本次要发送的订单；

            final String templateMsg = chargeBackResultMsgTemplate.getChargeOpenTemplate();
            log.error("sendChargeBackResultMetaq#getChargeOpenTemplate|" + EagleEye.getTraceId() + "|" +templateMsg);

            List<Long> bizOrderIds = Lists.newArrayList();
            Multimap<String, String> disputeId2Text = HashMultimap.<String, String>create();

            for (String each : ruleList) {
                try {
                    JSONObject originalJsonObject = JSON.parseObject(each);
                    String disputeId = originalJsonObject.getString("disputeId");
                    disputeId2Text.put(disputeId, each);
                } catch (Throwable t){
                    log.error("getChargeOpenListError|" + EagleEye.getTraceId(), t);
                }
            }

            log.error("getChargeOpenListResult|" + EagleEye.getTraceId()+ "|"+JSON.toJSONString(disputeId2Text.keys()));


            //each 表示，要追偿的 DisputeId
            for (String zhuichangDisputeId : disputeId2Text.keySet()) {
                try {
                    if (org.apache.commons.lang3.StringUtils.isNotBlank(zhuichangDisputeId) && zhuichangDisputeId.contains("\r")) {
                        log.error("zhuichangDisputeId|" + EagleEye.getTraceId(), zhuichangDisputeId);
                        zhuichangDisputeId = zhuichangDisputeId.replace("\r", "");
                    }

                    // DisputeId 对应的多个报文
                    Collection<String> originalText = disputeId2Text.get(zhuichangDisputeId);
                    while (originalText.iterator().hasNext()) {
                        String each = originalText.iterator().next();
                        // 1，从 ipay 的excel 里面能拿到的字段：
                        JSONObject originalJsonObject = JSON.parseObject(each);
                        JSONObject pgRtMsg = originalJsonObject.getJSONObject("pgRtMsg");
                        JSONObject disputeAmountAsCent = originalJsonObject.getJSONObject("disputeAmountAsCent");
                        String disputeAmountAsYuan = originalJsonObject.getString("disputeAmountAsYuan");
                        String disputeId = originalJsonObject.getString("disputeId");
                        String disputeSource = originalJsonObject.getString("disputeSource");
                        String disputeRawCode = originalJsonObject.getString("disputeRawCode");
                        String disputeTime = originalJsonObject.getString("disputeInquiryTime");
                        String disputeReasonCode = originalJsonObject.getString("disputeReasonCode");
                        String disputeReasonMsg = originalJsonObject.getString("disputeReasonMsg");

                        String payInstructionNo = originalJsonObject.getString("payInstrcutionNo");

                        // 2，ipay excel 没有提供的字段，只能通过调用支付的hsf接口，拿字段；
                        JSONObject queryPaymentDetailResponse = HsfUtil.queryPaymentDetail(payInstructionNo);
                        log.error("queryPaymentDetailResponse|" + EagleEye.getTraceId() + "|input="+ payInstructionNo+"|result=" + JSON.toJSONString(queryPaymentDetailResponse));
                        String checkoutOrderId = queryPaymentDetailResponse.getString("checkoutOrderNo");
                        String payerId = queryPaymentDetailResponse.getString("payerId");
                        String bizOrderId = queryPaymentDetailResponse.getString("tradeOrderNo");
                        if (org.apache.commons.lang3.StringUtils.isNotBlank(bizOrderId)) {
                            bizOrderIds.add(Long.valueOf(bizOrderId));
                        } else {
                        }
                        JSONArray payOrderInfoList = queryPaymentDetailResponse.getJSONArray("payOrderInfoList");
                        String payOptionCode = "";
                        if (null != payOrderInfoList && payOrderInfoList.size()>0) {
                            for (int i = 0; i < payOrderInfoList.size(); i++) {
                                JSONObject o = (JSONObject) payOrderInfoList.get(i);
                                if (Objects.nonNull(o)) {
                                    String payInstructionNoFromAePayment = o.getString("payInstructionNo");
                                    if (StringUtils.equalsIgnoreCase(payInstructionNoFromAePayment, payInstructionNo)) {
                                        payOptionCode = o.getString("payOptionCode");
                                        break;
                                    }
                                }
                            }
                        }



                        // 3, 用上述准备好的所有的参数，去替换 模板消息里面的占位符
                        String s1 = templateMsg.replace("${disputeAmountAsYuan}", disputeAmountAsYuan);
                        String centValue = disputeAmountAsCent.getString("value");
                        String currency = disputeAmountAsCent.getString("currency");
                        s1 = s1.replace("${disputeAmountAsCent}", centValue);
                        s1 = s1.replace("${currencyCode}", currency);
                        s1 = s1.replace("${disputeId}", disputeId);
                        s1 = s1.replace("${disputeSource}", disputeSource);
                        s1 = s1.replace("${payInstructionNo}", payInstructionNo);
                        s1 = s1.replace("${payerId}", payerId);
                        s1 = s1.replace("${bizOrderId}", bizOrderId);
                        s1 = s1.replace("${payOptionCode}", payOptionCode);
                        s1 = s1.replace("${checkoutOrderId}", checkoutOrderId);
                        s1 = s1.replace("${disputeReasonCode}", disputeReasonCode);
                        s1 = s1.replace("${disputeRawCode}", disputeRawCode);
                        s1 = s1.replace("${disputeReasonCode}", disputeReasonCode);
                        s1 = s1.replace("${disputeReasonMsg}", disputeReasonMsg);

                        if (org.apache.commons.lang3.StringUtils.isBlank(disputeAmountAsYuan) ||
                                org.apache.commons.lang3.StringUtils.isBlank(centValue) ||
                                org.apache.commons.lang3.StringUtils.isBlank(currency) ||
                                org.apache.commons.lang3.StringUtils.isBlank(disputeId) ||
                                org.apache.commons.lang3.StringUtils.isBlank(disputeSource) ||
                                org.apache.commons.lang3.StringUtils.isBlank(payInstructionNo) ||
                                org.apache.commons.lang3.StringUtils.isBlank(payerId) ||
                                org.apache.commons.lang3.StringUtils.isBlank(bizOrderId) ||
                                org.apache.commons.lang3.StringUtils.isBlank(payOptionCode) ||
                                org.apache.commons.lang3.StringUtils.isBlank(checkoutOrderId)
                        ) {
                            log.error("msgbodyLostField|"+ EagleEye.getTraceId() + "|" + s1);
                            loseField.put(payInstructionNo, s1);
                            continue;
                        }

                        // 4, 发送消息，topic: global_payment_event_topic, tag: chargeback,
                        doSendGeneral(s1, "global_payment_event_topic", "chargeback");
                    }

                } catch (Throwable t) {
                    log.error("newProtocolError|" + EagleEye.getTraceId() + "|" + zhuichangDisputeId, t);
                }
            }

            log.error("bizOrderIdchargebackopen|" + EagleEye.getTraceId() + "|" + JSON.toJSONString(bizOrderIds));
            return null;
        }

        if (params.equalsIgnoreCase("newProtocol")) {
            Map<String, String> loseField = Maps.newHashMap();

            Map<String, JSONObject> zhuichangFailed = Maps.newHashMap();
            List<String> ruleList = chargeBackResultRuleList.getRuleList();
            // 本次要发送的订单；
            List<String> targetDisputeIdList = chargeBackResultRuleList.getTargetDisputeIdList();
            log.error("sendChargeBackResultMetaq#targetDisputeIdList|" + EagleEye.getTraceId() + "|" +targetDisputeIdList);

            final String templateMsg = chargeBackResultMsgTemplate.getTemplateMsg();
            log.error("sendChargeBackResultMetaq#templateMsg|" + EagleEye.getTraceId() + "|" +templateMsg);

            List<Long> bizOrderIds = Lists.newArrayList();
            Map<String, Long> totalMoneySuccess = Maps.newHashMap();
            long cntSuccess = 0L;

            Map<String, String> disputeId2Text = Maps.newHashMap();
            for (String each : ruleList) {
                try {
                    JSONObject originalJsonObject = JSON.parseObject(each);
                    String disputeId = originalJsonObject.getString("disputeId");
                    disputeId2Text.put(disputeId, each);
                } catch (Throwable t){
                    log.error("readRuleListError|" + EagleEye.getTraceId(), t);
                }
            }

            List<String> needZhuiChangDisputeIdbutnotInIpayExcel = Lists.newArrayList();
            //each 表示，要追偿的 DisputeId
            for (String zhuichangDisputeId : targetDisputeIdList) {
                try {
                    log.error("zhuichangDisputeIdcurr1|" + EagleEye.getTraceId() + "|" + zhuichangDisputeId);

                    if(org.apache.commons.lang3.StringUtils.isNotBlank(zhuichangDisputeId) &&
                            org.apache.commons.lang3.StringUtils.contains(zhuichangDisputeId, "\r")) {
                        int idx = zhuichangDisputeId.indexOf("\r");
                        zhuichangDisputeId = zhuichangDisputeId.substring(0, idx);
                        log.error("zhuichangDisputeIdcurr2|" + EagleEye.getTraceId() + "|" + zhuichangDisputeId);
                    }

                    String each = disputeId2Text.get(zhuichangDisputeId);
                    // 如果从 ipay 的 excel 里面找不到想发送的拒付消息的 DisputeId，那么需要打印日志，找 ipay 同学要到消息体。
                    if (org.apache.commons.lang3.StringUtils.isBlank(each)){
                        needZhuiChangDisputeIdbutnotInIpayExcel.add(zhuichangDisputeId);
                        continue;
                    }
                    // 1，从 ipay 的excel 里面能拿到的字段：
                    JSONObject originalJsonObject = JSON.parseObject(each);
                    JSONObject pgRtMsg = originalJsonObject.getJSONObject("pgRtMsg");
                    JSONObject disputeAmountAsCent = originalJsonObject.getJSONObject("disputeAmountAsCent");
                    String disputeAmountAsYuan = originalJsonObject.getString("disputeAmountAsYuan");
                    String disputeId = originalJsonObject.getString("disputeId");
                    String disputeSource = originalJsonObject.getString("disputeSource");
                    String payInstructionNo = originalJsonObject.getString("payInstrcutionNo");

                    // 2，ipay excel 没有提供的字段，只能通过调用支付的hsf接口，拿字段；
                    JSONObject queryPaymentDetailResponse = HsfUtil.queryPaymentDetail(payInstructionNo);
                    log.error("queryPaymentDetailResponse|" + EagleEye.getTraceId() + "|input="+ payInstructionNo+"|result=" + JSON.toJSONString(queryPaymentDetailResponse));
                    String checkoutOrderId = queryPaymentDetailResponse.getString("checkoutOrderNo");
                    String payerId = queryPaymentDetailResponse.getString("payerId");
                    String bizOrderId = queryPaymentDetailResponse.getString("tradeOrderNo");
                    if (org.apache.commons.lang3.StringUtils.isNotBlank(bizOrderId)) {
                        bizOrderIds.add(Long.valueOf(bizOrderId));
                    } else {
                    }
                    JSONArray payOrderInfoList = queryPaymentDetailResponse.getJSONArray("payOrderInfoList");
                    String payOptionCode = "";
                    if (null != payOrderInfoList && payOrderInfoList.size()>0) {
                        for (int i = 0; i < payOrderInfoList.size(); i++) {
                            JSONObject o = (JSONObject) payOrderInfoList.get(i);
                            if (Objects.nonNull(o)) {
                                String payInstructionNoFromAePayment = o.getString("payInstructionNo");
                                if (StringUtils.equalsIgnoreCase(payInstructionNoFromAePayment, payInstructionNo)) {
                                    payOptionCode = o.getString("payOptionCode");
                                    break;
                                }
                            }
                        }
                    }



                    // 3, 用上述准备好的所有的参数，去替换 模板消息里面的占位符
                    String s1 = templateMsg.replace("${disputeAmountAsYuan}", disputeAmountAsYuan);
                    String centValue = disputeAmountAsCent.getString("value");
                    String currency = disputeAmountAsCent.getString("currency");
                    s1 = s1.replace("${disputeAmountAsCent}", centValue);
                    s1 = s1.replace("${currencyCode}", currency);
                    s1 = s1.replace("${disputeId}", disputeId);
                    s1 = s1.replace("${disputeSource}", disputeSource);
                    s1 = s1.replace("${payInstructionNo}", payInstructionNo);
                    s1 = s1.replace("${payerId}", payerId);
                    s1 = s1.replace("${bizOrderId}", bizOrderId);
                    s1 = s1.replace("${payOptionCode}", payOptionCode);
                    s1 = s1.replace("${checkoutOrderId}", checkoutOrderId);

                    if (org.apache.commons.lang3.StringUtils.isBlank(disputeAmountAsYuan) ||
                            org.apache.commons.lang3.StringUtils.isBlank(centValue) ||
                            org.apache.commons.lang3.StringUtils.isBlank(currency) ||
                            org.apache.commons.lang3.StringUtils.isBlank(disputeId) ||
                            org.apache.commons.lang3.StringUtils.isBlank(disputeSource) ||
                            org.apache.commons.lang3.StringUtils.isBlank(payInstructionNo) ||
                            org.apache.commons.lang3.StringUtils.isBlank(payerId) ||
                            org.apache.commons.lang3.StringUtils.isBlank(bizOrderId) ||
                            org.apache.commons.lang3.StringUtils.isBlank(payOptionCode) ||
                            org.apache.commons.lang3.StringUtils.isBlank(checkoutOrderId)
                    ) {
                        log.error("msgbodyLostField|"+ EagleEye.getTraceId() + "|" + s1);
                        loseField.put(payInstructionNo, s1);
                        continue;
                    }

                    // 4, 发送消息，topic: global_payment_event_topic, tag: chargeback,
                    doSendGeneral(s1, "global_payment_event_topic", "chargeback");

                    // 5，消息发完，查询结算，看看有没有追偿成功
                    JSONObject chargeBackRecoverSellerResponse = HsfUtil.chargeBackRecoverSeller(Long.valueOf(payerId.trim()), bizOrderId
                            , disputeId, Long.valueOf(centValue), currency);
                    log.error("chargeBackRecoverSellerResponse|" + EagleEye.getTraceId() + "|" + JSON.toJSONString(chargeBackRecoverSellerResponse));

                    if (Objects.isNull(chargeBackRecoverSellerResponse) ||
                            (Objects.nonNull(chargeBackRecoverSellerResponse) && !chargeBackRecoverSellerResponse.getBoolean("succeeded"))) {
                        zhuichangFailed.put(each, chargeBackRecoverSellerResponse);
                    }

                    if (Objects.isNull(chargeBackRecoverSellerResponse) ||
                            (Objects.nonNull(chargeBackRecoverSellerResponse) && chargeBackRecoverSellerResponse.getBoolean("succeeded"))) {
                        cntSuccess++;
                        if(null != totalMoneySuccess.get(currency)){
                            Long aLong = totalMoneySuccess.get(currency);
                            totalMoneySuccess.put(currency, aLong + Long.valueOf(centValue));
                        } else {
                            totalMoneySuccess.put(currency, 0L);
                        }
                    }

                } catch (Throwable t) {
                    log.error("newProtocolError|" + EagleEye.getTraceId() + "|" + zhuichangDisputeId, t);
                }
            }

            log.error("bizOrderIdshehe|" + EagleEye.getTraceId() + "|" + JSON.toJSONString(bizOrderIds));
            log.error("zhuichangFailed|" + EagleEye.getTraceId() + "|" + JSON.toJSONString(zhuichangFailed));
            log.error("totalMoneySuccess|" + EagleEye.getTraceId() + "|" + JSON.toJSONString(totalMoneySuccess) + "|cntSuccess|" + cntSuccess);
            log.error("needZhuiChangDisputeIdbutnotInIpayExcel|" + EagleEye.getTraceId() + "|" + JSON.toJSONString(needZhuiChangDisputeIdbutnotInIpayExcel));

            return null;
        }


        if (params.equalsIgnoreCase("chargeBackRecoverSellerMultiTask")) {
            Map<String, JSONObject> zhuichangFailed = Maps.newHashMap();
            List<String> ruleList = Optional.ofNullable(chargeBackResultRuleList.getRuleList()).orElse(Lists.newArrayList());
            log.error("SendMQMsghpyStart2|" + EagleEye.getTraceId() + "|" + ruleList.size());

            Map<String, Long> totalMoneySuccess = Maps.newHashMap();
            long cntSuccess = 0L;

            List<Long> bizOrderIds = Lists.newArrayList();

            log.error("threadHandlerStart|" + EagleEye.getTraceId() + "|");

            threadHandler(ruleList);

            log.error("threadHandlerEnd|" + EagleEye.getTraceId() + "|");

            log.error("bizOrderIdshehe|" + EagleEye.getTraceId() + "|" + JSON.toJSONString(bizOrderIds));
            log.error("zhuichangFailed|" + EagleEye.getTraceId() + "|" + JSON.toJSONString(zhuichangFailed));
            log.error("totalMoneySuccess|" + EagleEye.getTraceId() + "|" + JSON.toJSONString(totalMoneySuccess) + "|cntSuccess|" + cntSuccess);
            return null;
        }




        if (params.equalsIgnoreCase("chargeBackRecoverSeller")) {
            Map<String, JSONObject> zhuichangFailed = Maps.newHashMap();
            List<String> ruleList = chargeBackResultRuleList.getRuleList();
            List<Long> bizOrderIds = Lists.newArrayList();
            Map<String, Long> totalMoneySuccess = Maps.newHashMap();
            long cntSuccess = 0L;
            for (String each : ruleList) {
                try {
                    String[] split = each.split("&&&");
                    // instructionNo ~ 报文
                    String s = split[1];
                    JSONObject jsonObject = JSON.parseObject(s);
                    JSONObject disputeAmount = (JSONObject) jsonObject.get("disputeAmount");
                    String value = disputeAmount.getString("value");
                    String currency = disputeAmount.getString("currency");

                    String disputeId = jsonObject.getString("disputeId");
                    JSONObject queryPaymentDetailResponse = HsfUtil.queryPaymentDetail(split[0]);
                    log.error("queryPaymentDetailResponse|" + EagleEye.getTraceId() + "|" + JSON.toJSONString(queryPaymentDetailResponse));

                    String tradeOrderNo = queryPaymentDetailResponse.getString("tradeOrderNo");
                    if (org.apache.commons.lang3.StringUtils.isNotBlank(tradeOrderNo)) {
                        bizOrderIds.add(Long.valueOf(tradeOrderNo));
                    } else {
                    }

                    String payerId = queryPaymentDetailResponse.getString("payerId");

                    JSONObject chargeBackRecoverSellerResponse = HsfUtil.chargeBackRecoverSeller(Long.valueOf(payerId.trim())
                            , tradeOrderNo
                            , disputeId
                            , Long.valueOf(value)
                            , currency);
                    log.error("chargeBackRecoverSellerResponse|" + EagleEye.getTraceId() + "|" + JSON.toJSONString(chargeBackRecoverSellerResponse));

                    if (Objects.isNull(chargeBackRecoverSellerResponse) ||
                            (Objects.nonNull(chargeBackRecoverSellerResponse) && !chargeBackRecoverSellerResponse.getBoolean("succeeded"))) {
                        zhuichangFailed.put(each, chargeBackRecoverSellerResponse);
                    }

                    if (Objects.isNull(chargeBackRecoverSellerResponse) ||
                            (Objects.nonNull(chargeBackRecoverSellerResponse) && chargeBackRecoverSellerResponse.getBoolean("succeeded"))) {
                        cntSuccess++;
                        if(null != totalMoneySuccess.get(currency)){
                            Long aLong = totalMoneySuccess.get(currency);
                            totalMoneySuccess.put(currency, aLong + Long.valueOf(value));
                        } else {
                            totalMoneySuccess.put(currency, 0L);
                        }
                    }

                } catch (Throwable t) {
                    log.error("chargeBackRecoverSellererror|" + EagleEye.getTraceId() + "|" + each, t);
                }
            }
            log.error("bizOrderIdshehe|" + EagleEye.getTraceId() + "|" + JSON.toJSONString(bizOrderIds));
            log.error("zhuichangFailed|" + EagleEye.getTraceId() + "|" + JSON.toJSONString(zhuichangFailed));
            log.error("totalMoneySuccess|" + EagleEye.getTraceId() + "|" + JSON.toJSONString(totalMoneySuccess) + "|cntSuccess|" + cntSuccess);

            return null;
        }


        Map<String, QueryResultUnit> data = new LinkedHashMap<>();
        LandlordContext.setTenantSpec("AE_GLOBAL", -1);

        List<String> ruleList = chargeBackResultRuleList.getRuleList();

        Map<String, List<String>> map = Maps.newHashMap();
        for (String each : ruleList) {
            String[] split = each.split("&&&");
            // instructionNo ~ 报文
            if (map.get(split[0]) == null) {
                map.put(split[0], Lists.newArrayList());
            }
            map.get(split[0]).add(split[2]);
        }

        Map<String, List<String>> tongji = tongji(map);
        final int end = 40000;
        for (Map.Entry<String, List<String>> entry : tongji.entrySet()) {
            try {
                List<String> i = entry.getValue();
                log.error("currentEntry1|" + EagleEye.getTraceId() + "|" + entry.getKey() + "|" + entry.getValue().size());

                for (int j = 0; j < (Math.min(i.size(), end)); j++) {
                    log.error("currentListelement|" + EagleEye.getTraceId() + "|" + j + "|" + i.size());
                    String context = i.get(j);
                    boolean sendResult = doSend(context);
                    // 每发送成功一条数据，就从 diamond 里面将它删除
                    if (sendResult) {
                        log.error("removeDiamond11|" + EagleEye.getTraceId() + "|" + ruleList.size());

                        for (int k = 0; k < ruleList.size(); k++) {
                            if (ruleList.get(k).contains(context)) {
                                ruleList.remove(k);
                                break;
                            }
                        }
                        log.error("removeDiamond12|" + EagleEye.getTraceId() + "|" + ruleList.size());

                        StringBuilder sb = new StringBuilder("");
                        for (int k = 0; k < ruleList.size(); k++) {
                            sb.append(ruleList.get(k));
                            if (k != ruleList.size() - 1) {
                                sb.append("\n");
                            }
                        }
                        log.error("removeDiamond13|" + EagleEye.getTraceId() + "|" + ruleList.size());

                        if (ruleList.size() == 0) {
                            publishSingle("DONE");
                        } else {
                            log.error("removeDiamond14|" + EagleEye.getTraceId() + "|" + ruleList.size());
                            publishSingle(sb.toString());
                            log.error("removeDiamond15|" + EagleEye.getTraceId() + "|" + ruleList.size());
                        }
                    }
                }
            } catch (Throwable t){
                log.error("forEntryFail|" + EagleEye.getTraceId() + "|", t);

            }
        }


//        for (String each : map.values()) {
//            doSend(each);
//        }

        return null;
    }

    private Map<String, List<String>> tongji(Map<String, List<String>> map) {
        Map<String, List<String>> sellerId2Msg = Maps.newHashMap();
        for (Map.Entry<String, List<String>> eachEle : map.entrySet()) {
            List<String> value = eachEle.getValue();
            for (String each : value) {
                try {
                    JSONObject jsonObject = HsfUtil.queryPaymentDetail(eachEle.getKey());
//                log.error("queryPaymentDetailReuslt|"+ EagleEye.getTraceId() +"|" + jsonObject);

                    String payeeId = jsonObject.getString("payeeId");

                    if (org.apache.commons.lang3.StringUtils.isBlank(payeeId) || "0".equalsIgnoreCase(payeeId)) {
                        log.error("payeeIdIsNullHehe|" + EagleEye.getTraceId() + "|" + eachEle.getKey());
                    }

                    if (sellerId2Msg.get(payeeId) == null) {
                        sellerId2Msg.put(payeeId, Lists.newArrayList());
                    }

                    sellerId2Msg.get(payeeId).add(each);

                } catch (Throwable t) {
                    log.error("queryPaymentDetailErrorrrrr|" + EagleEye.getTraceId(), t);
                }
            }
        }

//        sellerId2Msg = sellerId2Msg.entrySet().stream()
//                .filter(i -> i.getValue().size() <= 3)
//                .collect(Collectors.toMap(i -> i.getKey(), i -> i.getValue(),  (a, b) -> a));
        log.error("sellerIdmultiMsg1|" + EagleEye.getTraceId() + "|" + sellerId2Msg.size());
        for (Map.Entry<String, List<String>> entry: sellerId2Msg.entrySet()){
            log.error("sellerIdmultiMsg2|" + EagleEye.getTraceId() + "|" + entry.getKey() +"|" + entry.getValue());
        }
        return sellerId2Msg;
    }


    private boolean doSend(String context) {
        ResultDTO result = new ResultDTO();

        String topic = "pg_rt1_pg_msg";
        String messageTag = "DISPUTE_RESULT";

        String messageBody = context;

        String envType = "美东预发";
        String ip = "";
        String buyerId = "2603852102";
        String res = null;
        long cnt = 0;
        // 最多重试300次
        for (; cnt < 300; cnt++) {
            try {
                res = sendMetaq(topic, messageBody, messageTag, envType, ip, buyerId, null,null,null);
                log.error("ssssendmsgresult|" + EagleEye.getTraceId() + "|" + messageBody + "|" + res);
                Thread.sleep(3000 + cnt * 1000);
                // {"traceId":"212cd8dd16678745942472641e1f43","status":true}
                if (StringUtils.isNotBlank(res) && res.contains("true")) {
                    return true;
                }
            } catch (Exception e) {
                result.setSuccess(false);
                result.setData("发送消息结果异常：" + res);
                log.error("heheError999" + EagleEye.getTraceId(), e);
            }
        }
        return false;
    }


    private boolean doSendGeneral(String context, String topic, String messageTag) {
        ResultDTO result = new ResultDTO();

        String messageBody = context;

        String envType = "online";
        String ip = "";
        String buyerId = "2603852102";
        String res = null;
        long cnt = 0;
        // 最多重试300次
        for (; cnt < 300; cnt++) {
            try {
                res = sendMetaq(topic, messageBody, messageTag, envType, ip, buyerId, null, null,null);
                log.error("doSendGeneralResult|" + EagleEye.getTraceId() + "|" + messageBody + "|" + res);
                Thread.sleep(3000 + cnt * 1000);
                // {"traceId":"212cd8dd16678745942472641e1f43","status":true}
                if (StringUtils.isNotBlank(res) && res.contains("true")) {
                    return true;
                }
            } catch (Exception e) {
                result.setSuccess(false);
                result.setData("发送消息结果异常：" + res);
                log.error("doSendGeneralError999" + EagleEye.getTraceId(), e);
            }
        }
        return false;
    }

    public static boolean publishSingle(Object object) {
        try {
            if (object instanceof String) {
                DiamondUnitSite.publishToAllUnit(ChargeBackResultRuleList.DATA_ID, ChargeBackResultRuleList.GROUP_ID, ChargeBackResultRuleList.GROUP_ID, (String)object);
            } else {
                DiamondUnitSite.publishToAllUnit(ChargeBackResultRuleList.DATA_ID, ChargeBackResultRuleList.GROUP_ID, ChargeBackResultRuleList.GROUP_ID, JSON.toJSONString(object));
            }
            return true;
        } catch (IOException e) {
            log.error(EagleEye.getTraceId()+"|diamondPushFail", e);
            return false;
        }
    }
}
