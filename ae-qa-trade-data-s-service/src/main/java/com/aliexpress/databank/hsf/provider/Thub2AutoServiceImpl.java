package com.aliexpress.databank.hsf.provider;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.global.qa.dc.model.builder.QueryResultBuilder;
import com.alibaba.global.qa.dc.model.param.SystemDTO;
import com.alibaba.global.qa.dc.model.result.QueryResultUnit;
import com.alibaba.global.qa.dc.model.result.ResultDTO;
import com.aliexpress.databank.constant.Constant;
import com.aliexpress.databank.constant.ThubConstant;
import com.aliexpress.databank.constant.ThubScenesEnum;
import com.aliexpress.databank.constant.ThubUrl;
import com.aliexpress.databank.dataobject.*;
import com.aliexpress.databank.hsf.Thub2AutoService;
import com.aliexpress.databank.utils.ThubAdaptationUtils;
import com.aliexpress.databank.utils.ThubHttpUtil;
import lombok.extern.log4j.Log4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import static com.aliexpress.databank.constant.ThubConstant.THUB_PROCESS_RESULT_URL;

@Log4j
@HSFProvider(serviceInterface = Thub2AutoService.class, serviceGroup = "HSF")
public class Thub2AutoServiceImpl implements Thub2AutoService {

    private static final Logger logger = LoggerFactory.getLogger(Thub2AutoServiceImpl.class);

    @Override
    public ResultDTO triggerProcessExecute(String params, SystemDTO systemDTO) {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        String envType = jsonObject.getString(Constant.EVN_TYPE);
        switch (envType) {
            case "线上":
                envType = "ONLINE";
                break;
            case "预发":
                envType = "PREONLINE";
                break;
            default:
                envType = "DAILY";
        }
        String processCode = jsonObject.getString(Constant.THUB_PROCESS_CODE);
        String param = jsonObject.getString(Constant.THUB_PARAM);
        JSONObject requestBody = new JSONObject(new TreeMap<>());
        requestBody.put("env", envType);
        requestBody.put("processCode", processCode);
        if (StringUtils.isNotBlank(param)) {
            requestBody.put("runtimeParam", param);
        }
        String res = ThubHttpUtil.queryThubTestcaseInstance(envType, requestBody, ThubUrl.EXECUTE_PROCESS.getUrl());
        resultDTO.setSuccess(true);
        resultDTO.setData(res);
        resultDTO.setMessage(res);
        return resultDTO;
    }

    @Override
    public ResultDTO queryProcessExecuteResult(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        String envType = jsonObject.getString(Constant.EVN_TYPE);
        switch (envType) {
            case "线上":
                envType = "ONLINE";
                break;
            case "预发":
                envType = "PREONLINE";
                break;
            default:
                envType = "DAILY";
        }
        String rowKey = jsonObject.getString(Constant.THUB_ROW_KEY);
        JSONObject requestBody = new JSONObject();
        requestBody.put("env", envType);
        requestBody.put("rowkey", rowKey);
        String res = ThubHttpUtil.queryThubTestcaseInstance(envType, requestBody, ThubUrl.QUERY_PROCESS_RESULT.getUrl());
        Map<String, QueryResultUnit> data = QueryResultBuilder.buildQueryResult("纠纷保护期信息 ", null, null, res);
        resultDTO.setSuccess(true);
        resultDTO.setData(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
        resultDTO.setMessage(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
        return resultDTO;
    }

    @Override
    public ResultDTO disputeProcessExecute(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();

        String res = ThubHttpUtil.disputeExecuteProcess(buildExecuteProcessReq(params), ThubUrl.EXECUTE_PROCESS.getUrl());
        logger.info("Thub出参" + res);
        String rowKey = JSONObject.parseObject(res).getJSONObject("result").getJSONObject("data").getJSONObject("CreateNew")
                .getJSONObject("result").getString("rowKey");
        String processResultUrl = THUB_PROCESS_RESULT_URL.replace("RK41A6FA18DB1D7CF3D65CEF95105644C6000248F710699BCE", rowKey);
        //  JSONObject jsonObject = JSONObject.parseObject(res);
        //  JSONObject result = new JSONObject();
        //  result.put("thubProcessResultUrl", processResultUrl);
        resultDTO.setSuccess(true);
        resultDTO.setData(processResultUrl);
        resultDTO.setMessage(processResultUrl);
        return resultDTO;
    }


    private ExecuteProcessReq buildExecuteProcessReq(String params) throws Exception {
        JSONObject jsonObject = JSONObject.parseObject(params);
        String envType = jsonObject.getString(Constant.EVN_TYPE);
        String scenes = jsonObject.getString("scenes");
        String fromStageUniqueCode = jsonObject.getString("fromStageUniqueCode");
        String tradeOrderLineId = jsonObject.getString("tradeOrderLineId");
        Integer quantity = jsonObject.getInteger("quantity");
        String refundChannel = jsonObject.getString("refundChannel");
        Long buyerId = jsonObject.getLong("buyerId");

        ExecuteProcessReq.ExecuteProcessBody executeProcessBody = new ExecuteProcessReq.ExecuteProcessBody();


        ReverseOrderLineDTO reverseOrderLine = ThubAdaptationUtils.getLastReverseOrderByTradeOrderLineId(buyerId, Long.parseLong(tradeOrderLineId));
        if (StringUtils.isEmpty(fromStageUniqueCode)) {
            // 开始节点
            if (reverseOrderLine != null) {
                Map<String, String> features = reverseOrderLine.getFeatures();
                String FulfilmentStatus = features.get("f_o_s");
                String activity = features.get("activity");
                if (StringUtils.isEmpty(FulfilmentStatus)) {
                    executeProcessBody.setFromStageUniqueCode(null);
                } else if ("CreateArbitration".equals(activity) && StringUtils.isNotBlank(FulfilmentStatus)) {
                    if (StringUtils.isEmpty(FulfilmentStatus) || "2000340".equals(FulfilmentStatus)) {
                        executeProcessBody.setFromStageUniqueCode(ThubScenesEnum.THUB_MOCK_ACCEPT_SUCCESS.getThubServiceId());
                    } else {
                        executeProcessBody.setFromStageUniqueCode(ThubScenesEnum.THUB_MOCK_QUALITY_CHECKED.getThubServiceId());
                    }
                } else if ("ReCollectConfirm".equals(activity)) {
                    executeProcessBody.setFromStageUniqueCode(ThubScenesEnum.THUB_MOCK_VC_COLLECT_AGAIN.getThubServiceId());
                } else if ("SubmitReturnItem".equals(activity)) {
                    executeProcessBody.setFromStageUniqueCode(ThubScenesEnum.THUB_MOCK_RECEIVED_DSCAN.getThubServiceId());
                } else {
                    String thubServiceId = ThubScenesEnum.getThubServiceIdByFulfillmentStatus(FulfilmentStatus, "");
                    if (thubServiceId == null) {
                        throw new RuntimeException("非法场景FulfilmentStatus=" + FulfilmentStatus);
                    }
                    executeProcessBody.setFromStageUniqueCode(thubServiceId);
                }
            } else {
                executeProcessBody.setFromStageUniqueCode(null);
            }
        }
        if (StringUtils.isNotBlank(scenes)) {
            // 终止节点
            String thubServiceId = ThubScenesEnum.getThubServiceIdByScenes(scenes);
            if (StringUtils.isEmpty(thubServiceId)) {
                throw new RuntimeException("场景不合法：" + scenes);
            }
            executeProcessBody.setTargetStageUniqueCode(thubServiceId);
        }

        String envStrategyCode = "";
        switch (envType) {
            case "线上":
                envType = "ONLINE";
                break;
            case "预发":
                envType = "PREONLINE";
                break;
            case "新加坡预发":
                envType = "PREONLINE";
                envStrategyCode = "PREONLINE_SG";
                break;
            case "美东预发":
                envType = "PREONLINE";
                envStrategyCode = "PREONLINE_USA_PRE";
                break;
            default:
                envType = "DAILY";
        }
        executeProcessBody.setEnv(envType);
        executeProcessBody.setEnvStrategyCode(envStrategyCode);
        executeProcessBody.setProcessCode(ThubConstant.PROCESS_CODE);
        //   String processCode = jsonObject.getString(Constant.THUB_PROCESS_CODE);
        //   executeProcessBody.setProcessCode(processCode);

        executeProcessBody.setAppName(Constant.THUB_INFO.getString("appName"));
        executeProcessBody.setTenantId(Constant.THUB_INFO.getString("tenantId"));

        // 流程入参
        ThubRuntimeParam thubRuntimeParam = new ThubRuntimeParam();
        thubRuntimeParam.setTradeOrderLineId(tradeOrderLineId);
        thubRuntimeParam.setQuantity(quantity);
        thubRuntimeParam.setRefundChannel(refundChannel);
        executeProcessBody.setRuntimeParam(JSONObject.toJSONString(thubRuntimeParam));

        // 全局变量
        List<ThubGlobalParam> globalParamList = new ArrayList<>();
        ThubGlobalParam globalParam1 = new ThubGlobalParam();
        globalParam1.setKey(ThubConstant.GLOBAL_PARAM_TRADE_ORDER_LINE_ID);
        globalParam1.setValue(tradeOrderLineId);
        globalParamList.add(globalParam1);
        ThubGlobalParam globalParam2 = new ThubGlobalParam();
        globalParam2.setKey(ThubConstant.GLOBAL_PARAM_SCENES);
        globalParam2.setValue(scenes);
        globalParamList.add(globalParam2);
        ThubExecuteContextDto executeContextDto = new ThubExecuteContextDto();
        executeContextDto.setGlobalParam(globalParamList);
        executeProcessBody.setExecuteContext(JSONObject.toJSONString(executeContextDto));

        ExecuteProcessReq executeProcessReq = new ExecuteProcessReq();
        executeProcessReq.setEnv(envType);
        executeProcessReq.setVariables(executeProcessBody);

        return executeProcessReq;
    }
}
