package com.aliexpress.databank.hsf;

import com.alibaba.ecommerce.exchange.dataobject.CurrencyType;
import com.alibaba.ecommerce.exchange.dataobject.GlobalRateInfo;
import com.alibaba.ecommerce.exchange.dataobject.Language;
import com.alibaba.ecommerce.exchange.request.ExchangePriceRequest;
import com.alibaba.ecommerce.module.Response;
import com.alibaba.global.money.Money;
import com.alibaba.global.qa.dc.model.param.SystemDTO;
import com.alibaba.global.qa.dc.model.result.ResultDTO;
import com.alibaba.ecommerce.exchange.dataobject.*;

import javax.money.MonetaryAmount;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Map;

public interface ExchangeRateServiceHsf {

//   org.javamoney.moneta.Money getExchangePrice(MonetaryAmount monetaryAmount, String curCurrency, String targetCurrency) throws Exception;

    Map<String, Response<CurrencyType>> getCurrencyDiff() throws Exception;

    Response<CurrencyType> getCurrency(String currencyType) throws Exception;

    Response<GlobalRateInfo> getExchangeRate(String baseCur, String quoteCur) throws Exception;

    Response<List<GlobalRateInfo>> getExchangeRateList(String baseCur, List<String> quoteCurList) throws Exception;

    Response<GlobalPriceDTO> getExchangePrice(Money baseAmount, String quoteCur, Language language) throws Exception;

    Response<GlobalRangePriceDTO> getExchangeRangePrice(Money minBaseAmount,
                                                        Money maxBaseAmount,
                                                        String quoteCur,
                                                        String separator,
                                                        Language language) throws Exception;

    String formatPrice(Money price, Language language) throws Exception;

    String formatPrice(Money price) throws Exception;

    String formatPrice(com.alibaba.intl.commons.framework.type.Money price, Language language) throws Exception;

    String formatPrice(com.alibaba.intl.commons.framework.type.Money price) throws Exception;

    String formatRangePrice(Money leftPrice, Money rightPrice, Language language) throws Exception;

    String formatRangePrice(com.alibaba.intl.commons.framework.type.Money leftPrice, com.alibaba.intl.commons.framework.type.Money rightPrice, Language language) throws Exception;

    Response<List<GlobalPriceDTO>> batchGetExchangePrice(List<Money> baseAmountList, List<String> quoteCurList) throws Exception;

    Money calculateExchangePrice(Money baseAmount, String quoteCur, Double rate, RoundingMode roundingMode) throws Exception;

    Response<GlobalPriceForOldAeDTO> getExchangePrice(com.alibaba.intl.commons.framework.type.Money baseAmount, String quoteCur, Language language) throws Exception;


    Response<GlobalRangePriceOldDTO> getExchangeRangePrice(com.alibaba.intl.commons.framework.type.Money minBaseMoney,
                                                        com.alibaba.intl.commons.framework.type.Money maxBaseMoney,
                                                        String quoteCur,
                                                        String separator,
                                                        Language language) throws Exception;




    Response<GlobalPriceForOldAeDTO> getExchangePreSalePrice(com.alibaba.intl.commons.framework.type.Money baseAmount, String quoteCur) throws Exception;

    Response<GlobalPriceDTO> getExchangePreSalePrice(Money baseAmount, String quoteCur) throws Exception;

    Map<String,List<String>> getSymbolData();

    Response<GlobalRateInfo> getExchangeRate(String baseCur, String quoteCur, boolean isBackup) throws Exception;

    Response<GlobalPriceForOldAeDTO> getExchangePrice(com.alibaba.intl.commons.framework.type.Money baseAmount, String quoteCur) throws Exception;

    Response<GlobalPriceDTO> getExchangePrice(Money baseAmount, String quoteCur) throws Exception;

    Response<GlobalRangePriceOldDTO> getExchangeRangePrice(com.alibaba.intl.commons.framework.type.Money minBaseAmount, com.alibaba.intl.commons.framework.type.Money maxBaseAmount, String quoteCur) throws Exception;

    Response<GlobalRangePriceDTO> getExchangeRangePrice(Money minBaseAmount, Money maxBaseAmount, String quoteCur) throws Exception;

    Map<String, List<String>> getExchangePriceDiff(Money baseAmount, com.alibaba.intl.commons.framework.type.Money baseAmount1, Language language) throws Exception;

    List<Response<GlobalRateInfo>> getExchangeRateForBackCheck(boolean isBackup) throws Exception;

    Response<GlobalRateInfo> getExchangePreSaleRate(String baseCur, String quoteCur)throws Exception;

    Map<String,Map<String,List<String>>> getExchangeLanguageDiff(Money baseAmount,com.alibaba.intl.commons.framework.type.Money baseAmount1) throws Exception;

}

