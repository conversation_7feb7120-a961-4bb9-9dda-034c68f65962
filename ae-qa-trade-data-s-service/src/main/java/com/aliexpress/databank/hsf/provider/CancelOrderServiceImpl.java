package com.aliexpress.databank.hsf.provider;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.global.qa.dc.model.param.SystemDTO;
import com.alibaba.global.qa.dc.model.result.ResultDTO;
import com.aliexpress.databank.constant.Constant;
import com.aliexpress.databank.hsf.CancelOrderService;
import com.aliexpress.databank.utils.HsfUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@HSFProvider(serviceInterface = CancelOrderService.class, serviceGroup = "HSF", serviceVersion = "1.0.0", clientTimeout = 20000)
public class CancelOrderServiceImpl implements CancelOrderService {

    @Override
    public ResultDTO croCloseMainOrder(String params, SystemDTO systemDTO) throws Exception{
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        Long orderId = jsonObject.getLong(Constant.ORDER_ID);
        JSONObject res = HsfUtil.croCloseMainOrder(orderId, buyerId);
        ResultDTO result = new ResultDTO();
        if (res.getBoolean("success")) {
            result.setMessage("风控主单关单成功" + res.toJSONString());
            result.setData("风控主单关单成功" + res.toJSONString());
            result.setSuccess(true);
        }else {
            result.setMessage("风控主单关单失败" + res.toJSONString());
            result.setData("风控主单关单失败" + res.toJSONString());
            result.setSuccess(false);
        }
        return result;
    }

    @Override
    public ResultDTO croCloseSubOrder(String params, SystemDTO systemDTO) throws Exception{
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        Long orderId = jsonObject.getLong(Constant.ORDER_ID);
        String subOrderIds = jsonObject.getString(Constant.NEED_CANCEL_ORDER_LINE_DATA);
        JSONObject res = HsfUtil.croCloseSubOrder(orderId, subOrderIds, buyerId);
        ResultDTO result = new ResultDTO();
        if (res.getBoolean("success")) {
            result.setMessage("风控子单关单成功" + res.toJSONString());
            result.setData("风控子单关单成功" + res.toJSONString());
            result.setSuccess(true);
        }else {
            result.setMessage("风控子单关单失败" + res.toJSONString());
            result.setData("风控子单关单失败" + res.toJSONString());
            result.setSuccess(false);
        }
        return result;
    }

}
