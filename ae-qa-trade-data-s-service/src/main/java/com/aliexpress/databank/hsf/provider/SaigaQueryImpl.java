package com.aliexpress.databank.hsf.provider;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.saiga.base.model.ApiResponse;
import com.alibaba.saiga.sdk.replay.service.ReplayService;
import com.aliexpress.databank.hsf.SaigaQueryFacade;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/2/7
 */
@Slf4j
@Component
@HSFProvider(serviceInterface = SaigaQueryFacade.class, serviceVersion = "1.0.0", clientTimeout = 20000)
public class SaigaQueryImpl implements SaigaQueryFacade {
    @Autowired
    ReplayService replayService;

    @Override
    public ApiResponse<Map<String, Object>> getCollectAndReplayResult(String var1) {
        ApiResponse<Map<String, Object>> resp = replayService.getCollectAndReplayResult(var1);
        return resp;
    }
}
