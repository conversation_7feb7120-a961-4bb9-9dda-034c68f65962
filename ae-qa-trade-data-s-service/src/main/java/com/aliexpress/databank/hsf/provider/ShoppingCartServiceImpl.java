package com.aliexpress.databank.hsf.provider;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.ecommerce.module.Response;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.global.carts.api.facade.CartFacade;
import com.alibaba.global.carts.api.request.CartAddRequest;
import com.alibaba.global.carts.api.request.CartQueryRequest;
import com.alibaba.global.carts.api.request.dto.CartAddItemDTO;
import com.alibaba.global.carts.api.request.dto.CartPageOptionDTO;
import com.alibaba.global.carts.api.request.dto.CartShipToDTO;
import com.alibaba.global.carts.api.response.dto.CartDetailResult;
import com.alibaba.global.landlord.LandlordContext;
import com.alibaba.global.qa.dc.model.param.SystemDTO;
import com.alibaba.global.qa.dc.model.result.ResultDTO;
import com.aliexpress.databank.constant.Constant;
import com.aliexpress.databank.dataobject.TradeDTO;
import com.aliexpress.databank.hsf.ShoppingCartService;
import com.aliexpress.databank.utils.ConvertParam;
import com.aliexpress.databank.utils.QueryProduct;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@HSFProvider(serviceInterface = ShoppingCartService.class, serviceGroup = "HSF")
public class ShoppingCartServiceImpl implements ShoppingCartService {

    @Autowired
    private CartFacade cartFacade;

    @Autowired
    private QueryProduct queryProduct;


    @Override
    public ResultDTO add2ShoppingCart(String params, SystemDTO systemDTO) {
        ResultDTO result = new ResultDTO();
        TradeDTO inputParam = ConvertParam.convertParamToTradeDTO(params);

        LandlordContext.setTenantSpec("AE_GLOBAL", -1);

        CartAddRequest cartAddRequest = new CartAddRequest();

        Map<String, String> httpHeaders = getHttpHeaders();
        cartAddRequest.setHttpHeaders(httpHeaders);
        cartAddRequest.setDebugInfo(false);
        cartAddRequest.setUserId(inputParam.buyerId);
        cartAddRequest.setTestUserId(inputParam.buyerId);
        List<CartAddItemDTO> cartAddItemDTOS = new ArrayList<>();
        for (Long productId : inputParam.productidlist) {
            CartAddItemDTO cartAddItemDTO = new CartAddItemDTO();
            cartAddItemDTO.setItemId(productId);
            cartAddItemDTO.setQuantity(1);
            Long sku = queryProduct.queryItemSku(productId);
            cartAddItemDTO.setSkuId(sku);
            cartAddItemDTOS.add(cartAddItemDTO);
        }
        cartAddRequest.setAddItems(cartAddItemDTOS);
        cartAddRequest.setCurrency(inputParam.currency);
        Response response = cartFacade.add(cartAddRequest);
        if (response.isSuccess()) {
            result.setSuccess(true);
            result.setData("add to cart successfully");
        } else {
            result.setSuccess(true);
            result.setData(response.getErrorCode().getLogMessage() + response.getErrorCode().getDisplayMessage());
        }

        return result;

    }

    private Map<String, String> getHttpHeaders() {
        Map<String, String> httpHeaders = new HashMap<>();
        httpHeaders.put("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_13_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/73.0.3683.103 Safari/537.36");
        httpHeaders.put("Host", "localhost:7003");
        return httpHeaders;
    }

    @Override
    public ResultDTO getShopCartInfoByBuyerIdAndProductId(String params, SystemDTO systemDTO) {
        ResultDTO result = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        CartQueryRequest cartQueryRequest = getCartQueryRequest(jsonObject);
        Response<CartDetailResult> response =  cartFacade.query(cartQueryRequest);
        if (!response.isSuccess() ){
            result.setSuccess(false);
            result.setMessage(JSONObject.toJSONString(response.getErrorCode()));
            return result;
        }
        if (response.getModule() == null){
            result.setSuccess(true);
            result.setMessage("Empty Cart.");
            return result;
        }
        return result;
    }

    private CartQueryRequest getCartQueryRequest(JSONObject jsonObject) {
        CartQueryRequest cartQueryRequest = new CartQueryRequest();
        cartQueryRequest.setUserId(Long.parseLong(jsonObject.getString(Constant.BUYER_ID)));
        cartQueryRequest.setExtParams(getExtParams(jsonObject));
        cartQueryRequest.setHttpCookies(getHttpHeaders());
        cartQueryRequest.setPageOption(getPageOption());
        cartQueryRequest.setCartShipToDTO(getCartShopToDTO(jsonObject));
        return cartQueryRequest;
    }

    private Map<String, Object> getExtParams(JSONObject jsonObject) {
        Map<String, Object> extParams = new HashMap<>();
        extParams.put(Constant.INTENTIONAL_CURRENCY, jsonObject.getString(Constant.CURRENCY));
        return extParams;
    }

    private CartPageOptionDTO getPageOption() {
        CartPageOptionDTO cartPageOptionDTO = new CartPageOptionDTO();
        cartPageOptionDTO.setPage(1);
        cartPageOptionDTO.setPageSize(100);
        return cartPageOptionDTO;
    }

    private CartShipToDTO getCartShopToDTO(JSONObject jsonObject) {
        CartShipToDTO cartShipToDTO = new CartShipToDTO();
        cartShipToDTO.setShipToCountry(jsonObject.getString(Constant.SHIP_TO_COUNTRY));
        return cartShipToDTO;
    }

}
