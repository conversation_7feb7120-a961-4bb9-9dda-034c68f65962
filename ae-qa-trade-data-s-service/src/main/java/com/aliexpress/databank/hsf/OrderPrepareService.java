package com.aliexpress.databank.hsf;

import com.alibaba.global.qa.dc.model.param.SystemDTO;
import com.alibaba.global.qa.dc.model.result.ResultDTO;

public interface OrderPrepareService {


    /**
     * 查询不同业务订单,brp全链路自动化订单聚合
     * @param params : buyerId
     * @param systemDTO
     * @return
     * @throws Exception
     */
    ResultDTO queryCustomOrder(String params, SystemDTO systemDTO) throws Exception;

    }
