package com.aliexpress.databank.hsf.provider;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.ecommerce.module.Response;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.global.landlord.LandlordContext;
import com.alibaba.global.order.management.api.facade.OrderQueryForBuyerFacade;
import com.alibaba.global.order.management.api.model.Features;
import com.alibaba.global.order.management.api.model.TradeOrderDTO;
import com.alibaba.global.order.management.api.model.TradeOrderLineDTO;
import com.alibaba.global.qa.dc.model.builder.QueryResultBuilder;
import com.alibaba.global.qa.dc.model.param.SystemDTO;
import com.alibaba.global.qa.dc.model.result.QueryResultUnit;
import com.alibaba.global.qa.dc.model.result.ResultDTO;

import com.aliexpress.databank.constant.Constant;
import com.aliexpress.databank.hsf.OrderFeatureService;
import com.taobao.eagleeye.EagleEye;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

@Slf4j
@HSFProvider(serviceInterface = OrderFeatureService.class, serviceGroup = "HSF", serviceVersion = "1.0.0", clientTimeout = 20000)
public class OrderFeatureServiceImpl implements OrderFeatureService {



    @Autowired
    private OrderQueryForBuyerFacade orderQueryForBuyerFacade;


    @Override
    public ResultDTO queryOrderFeature(String params, SystemDTO systemDTO) throws Exception {

        ResultDTO result = new ResultDTO();

        JSONObject jsonObject = JSONObject.parseObject(params);
        String orderId =jsonObject.getString("orderIdStr");
        //查询区域化接口，多租户标
        LandlordContext.setTenantSpec("AE_GLOBAL", -1);


        Response<TradeOrderDTO> tradeOrderDTOResponse = orderQueryForBuyerFacade.queryTradeOrderById(jsonObject.getLong(Constant.BUYER_ID), Long.parseLong(orderId));

        if (tradeOrderDTOResponse.isNotSuccess()) {
            log.error("queryTradeOrderById() query failed");
            result.setSuccess(false);
            result.setMessage("queryTradeOrderById() query failed");
            return result;
        }

        String orderline = JSON.toJSONString(tradeOrderDTOResponse.getModule().getOrderLines(), SerializerFeature.DisableCircularReferenceDetect);
        List<TradeOrderLineDTO> tradeOrderLines = JSON.parseArray(orderline, TradeOrderLineDTO.class);


        List<Map<String ,String>> tradelinelist=new ArrayList<>();


        //查标,子订单
        for(int i=0;i<tradeOrderLines.size();i++){
            Map<String ,String> orderfeature=new HashMap<String ,String>();


            TradeOrderLineDTO tradeOrderLineDTO=new TradeOrderLineDTO();
            tradeOrderLineDTO=tradeOrderLines.get(i);

            Features featrue=tradeOrderLineDTO.getFeatures();
            if(featrue.getFeature("leadTimeTag")!=null){
                orderfeature.put("leadTimeTag","x日达订单");
            }if(featrue.getFeature("cbImport")!=null){
                orderfeature.put("cbImport","DDP订单");
            }if(featrue.getFeature("wc")!=null&&featrue.getFeature("wc").equals("HUZ204")||featrue.getFeature("wc").equals("AET001")){
                orderfeature.put("wc","尖货订单");
            }if(featrue.getFeature("in_rs")!=null){
                orderfeature.put("in_rs，库存扣减策略",featrue.getFeature("in_rs"));
            }if(featrue.getFeature("logisticsHBATag")!=null){
                orderfeature.put("logisticsHBATag","石油订单");
            }if(featrue.getFeature("sku_custom_attr")!=null){
                orderfeature.put("sku_custom_attr","定制订单（婚纱、眼镜，根据类目区分）");
            }
            //保修的怎么写？？？
            if(featrue.getFeature("promiseTemplate").contains("\"id\":10")){
                orderfeature.put("promiseTemplate","保修标");
            }

            orderfeature.put("tradeOrderLineId",tradeOrderLineDTO.getTradeOrderLineId().toString());

            tradelinelist.add(orderfeature);


        }
        Map<String, QueryResultUnit> orderLineFeature = QueryResultBuilder.buildQueryResult("子订单标", null, null, tradelinelist);

        //查标,主订单
        Map<String,String> mainorderfeature=new HashMap<>();

       TradeOrderDTO tradeOrderDTO= tradeOrderDTOResponse.getModule();
        if(tradeOrderDTO.getFeatures().getFeature("splitOrderType")!=null){
            mainorderfeature.put("splitOrderType","尖货拆单订单");

        }if(tradeOrderDTO.getBizCode().equals("ali.global.ae.topup")){
            mainorderfeature.put("ali.global.ae.topup","充值订单");

        }if(tradeOrderDTO.getBizCode().equals("ali.global.ae.eticket")){
            mainorderfeature.put("ali.global.ae.eticket","Eticket订单");

        }
        Map<String, QueryResultUnit> orderFeature = QueryResultBuilder.buildQueryResult("主订单标", null, null, mainorderfeature);




        Map<String, QueryResultUnit> data = new LinkedHashMap<>();

        data.putAll(orderLineFeature);
        data.putAll(orderFeature);


        result.setData(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
        result.setSuccess(true);
        return result;



    }
}

