package com.aliexpress.databank.hsf.provider;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.common.lang.StringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.global.qa.dc.model.param.SystemDTO;
import com.alibaba.global.qa.dc.model.result.ResultDTO;
import com.aliexpress.databank.constant.Constant;
import com.aliexpress.databank.hsf.CartService;
import com.aliexpress.databank.hsf.TaskTimeOutService;
import com.aliexpress.databank.utils.HsfUtil;
import com.taobao.eagleeye.EagleEye;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@HSFProvider(serviceInterface = TaskTimeOutService.class, serviceGroup = "HSF",serviceVersion = "1.0.0", clientTimeout = 20000)
public class TaskTimeOutServiceImpl implements TaskTimeOutService {
    @SneakyThrows
    @Override
    public ResultDTO shipTaskTimeOut(String params, SystemDTO systemDTO) {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        String dpath = jsonObject.getString(Constant.DPATH_ENV);
        //String orderId = jsonObject.getString(Constant.ORDER_ID);
        String orderId =jsonObject.getString("orderIdStr");

        if (StringUtil.isNotBlank(dpath)) {
            EagleEye.putUserData("dpath_env", dpath);
        }

        if (orderId.isEmpty()) {
            resultDTO.setSuccess(false);
            resultDTO.setData("orderId is empty");
            resultDTO.setMessage("orderId is empty");
            return resultDTO;
        }

        JSONObject response = null;
        try {
            response = HsfUtil.timeOutFacadeTest(orderId);
        } catch (Exception e) {
            log.error("failed={}", orderId, e);
        }
        if (!response.getBoolean("success")) {
            resultDTO.setSuccess(false);
            resultDTO.setData("Failed "+JSON.toJSONString(response)+" traceId: " + EagleEye.getTraceId());
            resultDTO.setMessage("Failed "+JSON.toJSONString(response)+" traceId: " + EagleEye.getTraceId());
        }else{
            resultDTO.setSuccess(true);
            resultDTO.setData("success "+JSON.toJSONString(response)+" traceId: " + EagleEye.getTraceId());
        }
        return resultDTO;
    }
}
