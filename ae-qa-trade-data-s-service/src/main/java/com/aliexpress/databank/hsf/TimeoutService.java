package com.aliexpress.databank.hsf;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.global.qa.dc.model.param.SystemDTO;
import com.alibaba.global.qa.dc.model.result.QueryResultUnit;
import com.alibaba.global.qa.dc.model.result.ResultDTO;
import com.aliexpress.databank.dataobject.ResultVo;

import java.util.Map;

public interface TimeoutService {
    /**
     * 获取超时时间详情
     *
     * @param params    : alarmName ; orderId
     * @param systemDTO
     * @return
     */
    ResultDTO getTimeoutByOrderIdAndEventType(String params, SystemDTO systemDTO);

    /**
     * 获取逆向超时时间详情
     *
     * @param params    : alarmName ; orderId
     * @param systemDTO
     * @return
     */
    ResultDTO getReverseTimeoutByOrderIdAndEventType(String params, SystemDTO systemDTO);

    /**
     * 更新超时时间
     *
     * @param params    : alarmName ; orderId
     * @param systemDTO
     * @return
     */
    ResultDTO updateTimeout(String params, SystemDTO systemDTO) throws Exception;

    /**
     * 根据alarmName和orderId返回展示层Vo
     *
     * @param alarmName
     * @param orderId
     * @return
     * @throws Exception
     */
    Map<String, QueryResultUnit> getAlarmTimeoutVo(String alarmName, String orderId) throws Exception;

    Map<String, QueryResultUnit> getReverseTimeouts(String alarmName, JSONArray results);

    Map<String, QueryResultUnit> getReverseTimeouts(String alarmName, JSONObject result);

    ResultDTO updateShareGroupTimeout(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO updateReverseTimeout(String params, SystemDTO systemDTO) throws Exception;

    ResultVo getTimeoutsByParams(String params, SystemDTO systemDTO) throws Exception;

    ResultVo updateTimeoutsByParams(String params, SystemDTO systemDTO);

    ResultDTO breachTocCallback(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO logisticsServicesTimeOut(String params, SystemDTO systemDTO) throws Exception;
    /**
     * 获取拼团超时时间详情
     *
     * @param params    : alarmName ; businessId
     * @param systemDTO
     * @return
     */
    ResultDTO getTimeoutByBusinessIdAndEventType(String params, SystemDTO systemDTO);


    ResultDTO updateNewReverseTimeout (String params, SystemDTO systemDTO) throws Exception;

    ResultDTO triggerPunishTimeout (String params, SystemDTO systemDTO) throws Exception;


}
