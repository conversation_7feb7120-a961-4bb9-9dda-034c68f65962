package com.aliexpress.databank.hsf.provider;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.cobar.parser.ast.expression.primary.function.json.JsonObject;
import com.alibaba.common.lang.StringUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.global.qa.dc.model.param.SystemDTO;
import com.alibaba.global.qa.dc.model.result.ResultDTO;
import com.aliexpress.databank.constant.Constant;
import com.aliexpress.databank.hsf.AddressService;
import com.aliexpress.databank.hsf.CodService;
import com.aliexpress.databank.utils.HsfUtil;
import com.taobao.eagleeye.EagleEye;
import com.taobao.hsf.util.RequestCtxUtil;
import lombok.extern.slf4j.Slf4j;

import javax.xml.transform.Result;

@Slf4j
@HSFProvider(serviceInterface = CodService.class)
public class CodServiceImpl implements CodService {
    @Override
    public ResultDTO triggerAudit(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long orderId = jsonObject.getLong(Constant.ORDER_ID);
        //发货审核 1，缺货审核2
        String codAuditType = jsonObject.getString(Constant.COD_AUDIT_TYPE);
        String codAuditResult = jsonObject.getString(Constant.COD_AUDIT_RESULT);
        String dpath = jsonObject.getString(Constant.DPATH_ENV);
        if (StringUtil.isNotBlank(dpath)) {
            EagleEye.putUserData("dpath_env", dpath);
        }
        if(codAuditResult.equalsIgnoreCase("1")){
            JSONObject response = HsfUtil.triggerAudit(orderId, true ,codAuditType);
            resultDTO.setSuccess(true);
            resultDTO.setData(response.toJSONString());
            resultDTO.setMessage(response.toJSONString());
            log.warn("triggerAudit response ~~~~~",response.toJSONString());
        }
        if (codAuditResult.equalsIgnoreCase("0")){
            JSONObject response = HsfUtil.triggerAudit(orderId, false ,codAuditType);
            resultDTO.setSuccess(true);
            resultDTO.setData(response.toJSONString());
            resultDTO.setMessage(response.toJSONString());
        }
        return resultDTO;
    }
}
