package com.aliexpress.databank.hsf;

import com.alibaba.global.qa.dc.model.param.SystemDTO;
import com.alibaba.global.qa.dc.model.result.ResultDTO;

public interface ShoppingCartService {

    /**
     * 加购
     * @param params
     * @param systemDTO
     * @return
     */
    ResultDTO add2ShoppingCart(String params, SystemDTO systemDTO);


    /**
     * 根据买家id和product id查询商品
     * @param params buyerId ; productId ; shipTo ; currency
     * @param systemDTO
     * @return
     */
    ResultDTO getShopCartInfoByBuyerIdAndProductId(String params, SystemDTO systemDTO);

}
