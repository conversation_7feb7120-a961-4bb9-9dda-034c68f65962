package com.aliexpress.databank.hsf;

import com.alibaba.global.qa.dc.model.param.SystemDTO;
import com.alibaba.global.qa.dc.model.result.ResultDTO;
import com.alibaba.rocketmq.client.exception.MQBrokerException;
import com.alibaba.rocketmq.client.exception.MQClientException;
import com.alibaba.rocketmq.remoting.exception.RemotingException;
import com.aliexpress.databank.dataobject.ResultVo;

public interface UtilService {

    ResultDTO decodeParams(String params, SystemDTO systemDTO);

    ResultDTO callHsf(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO sendMsg(String params, SystemDTO systemDTO) throws <PERSON>ruptedEx<PERSON>, RemotingException, MQClientException, MQBrokerException;

    ResultDTO getIntentionCurrencyCodes();

    ResultDTO getCountryCodes();

    ResultVo getTocJobs();

    ResultDTO getShippingMethodList(String params, SystemDTO systemDTO);
}
