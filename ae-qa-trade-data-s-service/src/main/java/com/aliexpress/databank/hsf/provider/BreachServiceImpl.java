package com.aliexpress.databank.hsf.provider;

import com.alibaba.ae.service.open.facade.TimeoutCalculateFacade;
import com.alibaba.ae.service.open.param.QualifiedTimeRequest;
import com.alibaba.ae.service.open.param.QualifiedTimeResponse;
import com.alibaba.ae.service.open.response.Response;
import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.common.lang.StringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.global.order.management.api.facade.OrderQueryForBuyerFacade;
import com.alibaba.global.qa.dc.model.builder.QueryResultBuilder;
import com.alibaba.global.qa.dc.model.param.SystemDTO;
import com.alibaba.global.qa.dc.model.result.QueryResultUnit;
import com.alibaba.global.qa.dc.model.result.ResultDTO;
import com.alibaba.rocketmq.client.exception.MQBrokerException;
import com.alibaba.rocketmq.client.exception.MQClientException;
import com.alibaba.rocketmq.client.producer.SendResult;
import com.alibaba.rocketmq.remoting.exception.RemotingException;
import com.aliexpress.databank.config.MqConfig;
import com.aliexpress.databank.constant.Constant;
import com.aliexpress.databank.hsf.BreachService;
import com.aliexpress.databank.hsf.DataPoolService;
import com.aliexpress.databank.utils.HsfUtil;
import com.beust.jcommander.internal.Lists;
import com.google.common.collect.Maps;
import com.taobao.eagleeye.EagleEye;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;


@Slf4j
@HSFProvider(serviceInterface = BreachService.class)
public class BreachServiceImpl implements BreachService {
    public static final String EXPENSIVE_COUPONS_REQ = "{\"breachContractId\":**************,\"gmtModified\":*************,\"initiatorId\":\"**********\",\"initiatorType\":\"expensive_compensate\",\"msgType\":\"FinalJudge\",\"responsibilityCode\":\"EXPENSIVE-COMPENSATE\",\"targetId\":\"****************\",\"targetType\":\"AE_ORDER\"}";

    private static final Logger logger = LoggerFactory.getLogger(BreachServiceImpl.class);

    @Autowired
    private MqConfig mqConfig;

    @Autowired
    private OrderQueryForBuyerFacade orderQueryForBuyerFacade;

    @Autowired
    private DataPoolService dataPoolService;

    @Autowired
    private TimeoutCalculateFacade timeoutCalculateFacade;

    @Override
    public ResultDTO createWarehouseBreachOrder(String params, SystemDTO systemDTO) throws InterruptedException, RemotingException, MQClientException, MQBrokerException {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long sellerId = jsonObject.getLong(Constant.SELLER_ID);
        String breachScenario = jsonObject.getString(Constant.BREACH_SCENARIO);
        String poOrder = jsonObject.getString(Constant.PO_ORDER);
        String bhOrder = jsonObject.getString(Constant.BH_ORDER);
        Long shipMethod = getShipMethod(jsonObject.getString(Constant.SHIP_METHOD));
        String coOrders = jsonObject.getString(Constant.CO_ORDERS);
        String coStatus = jsonObject.getString(Constant.CO_STATUS);
        String scItemId = jsonObject.getString(Constant.SC_ITEM_ID);
        String msgBody = getWarehouseBreachReq(sellerId, breachScenario, shipMethod, coStatus, poOrder, bhOrder, coOrders, scItemId);
        SendResult sendResult = mqConfig.sendMessage(Constant.BREACH_WAREHOUSE_TOPIC, Constant.BREACH_WAREHOUSE_TAG, sellerId + breachScenario + coStatus, msgBody);
        resultDTO.setSuccess(true);
        resultDTO.setData(JSON.toJSONString(sendResult));
        resultDTO.setMessage(JSON.toJSONString(sendResult));
        return resultDTO;
    }

    @Override
    public ResultDTO queryBreach(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long breachId = jsonObject.getLong(Constant.BREACH_ID);
        JSONObject breachContract = HsfUtil.queryBreachContractDO(breachId);
        JSONArray breachContractPenalties = HsfUtil.queryBreachContractPenaltyDO(breachId);
        JSONArray breachContractBills = HsfUtil.queryBreachContractFundBillDO(breachId);
        JSONArray breachContractRecords = HsfUtil.queryBreachContractRecordDO(breachId);
        Map<String, QueryResultUnit> data = new LinkedHashMap<>();
        if (breachContract == null) {
            resultDTO.setSuccess(true);
            JSONObject msg = new JSONObject();
            msg.put("错误信息", "未找到违约单: " + breachId);
            Map<String, QueryResultUnit> noResult = QueryResultBuilder.buildQueryResult("违约单信息", null, null, msg);
            data.putAll(noResult);
            resultDTO.setData(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
        } else {
            // 违约单
            Map<String, QueryResultUnit> breachContractMap = QueryResultBuilder.buildQueryResult("违约单信息", null, null, breachContract);
            data.putAll(breachContractMap);

            // 处罚单
            if (breachContractPenalties != null) {
                for (int i = 0; i < breachContractPenalties.size(); i++) {
                    Map<String, QueryResultUnit> breachContractPenalty = QueryResultBuilder.buildQueryResult("处罚单信息 - " + breachContractPenalties.getJSONObject(i).getLong("id"), null, null, breachContractPenalties.getJSONObject(i));
                    data.putAll(breachContractPenalty);
                }
            }

            // 违约资金单
            if (breachContractBills != null) {
                for (int i = 0; i < breachContractBills.size(); i++) {
                    Map<String, QueryResultUnit> breachContractBill = QueryResultBuilder.buildQueryResult("违约资金单信息 - " + breachContractBills.getJSONObject(i).getLong("id"), null, null, breachContractBills.getJSONObject(i));
                    data.putAll(breachContractBill);
                }
            }

            // 记录表
            if (breachContractRecords != null) {
                for (int i = 0; i < breachContractRecords.size(); i++) {
                    Map<String, QueryResultUnit> breachContractRecord = QueryResultBuilder.buildQueryResult("处罚单记录信息 - " + breachContractRecords.getJSONObject(i).getLong("id"), null, null, breachContractRecords.getJSONObject(i));
                    data.putAll(breachContractRecord);
                }
            }
        }
        resultDTO.setSuccess(true);
        resultDTO.setData(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
        return resultDTO;
    }

    @Override
    public ResultDTO createSemiChoiceJitBreach(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        String poStatus = jsonObject.getString(Constant.PO_STATUS);
        Long shipMethod = getShipMethod(jsonObject.getString(Constant.SHIP_METHOD));
        String breachType = jsonObject.getString(Constant.BREACH_TYPE);
        JSONObject createSemiChoiceJitBreachReq = generateSemiChoiceJitBreachReq(poStatus, shipMethod, breachType);
        JSONObject result = HsfUtil.batchInitBreachContractData(createSemiChoiceJitBreachReq);
        resultDTO.setSuccess(true);
        resultDTO.setMessage(result.toJSONString());
        resultDTO.setData(result.toJSONString());
        return resultDTO;
    }

    @Override
    public ResultDTO queryJitPurchaseOrder(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        String po = jsonObject.getString(Constant.PO_ORDER);
        JSONObject result = HsfUtil.queryJitPurchaseOrder(po);
        Map<String, QueryResultUnit> data = QueryResultBuilder.buildQueryResult("入库单信息", null, null, result.getJSONObject("data"));
        resultDTO.setSuccess(true);
        resultDTO.setData(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
        resultDTO.setMessage(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
        return resultDTO;
    }

    @Override
    public ResultDTO createJitWeightScaleBreachOrder(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        String sellerId = jsonObject.getString(Constant.SELLER_ID);
        String sellerWeight = jsonObject.getString(Constant.SELLER_WEIGHT);
        String diffRatio = jsonObject.getString(Constant.DIFF_RATIO);
        String diffType = getWeightDiffType(jsonObject.getString(Constant.WEIGHT_DIFF_TYPE));
        String lbx = jsonObject.getString(Constant.LBX);
        String orderType = jsonObject.getString(Constant.ORDER_TYPE);
        JSONObject subOrderInfo = jsonObject.getJSONObject(Constant.SUB_ORDER_INFO).getJSONObject("module");
        String scItemId = getScItemIdFromOrder(subOrderInfo);
        String warehouseCode = getWarehouseCodeFromOrder(subOrderInfo);
        String productId = getProductIdFromOrder(subOrderInfo);
        String skuId = getSkuIdFromOrder(subOrderInfo);
        JSONObject request = getCreateJitWeightScaleBreachRequest(sellerId, sellerWeight, diffRatio, diffType, lbx,
                scItemId, warehouseCode, productId, skuId, orderType);
        JSONObject result = HsfUtil.applyBreach(request);
        resultDTO.setSuccess(true);
        resultDTO.setData(result.toJSONString());
        resultDTO.setMessage(result.toJSONString());
        return resultDTO;
    }


    private String getScItemIdFromOrder(JSONObject subOrderInfo) {
        JSONObject supplyInfo = subOrderInfo.getJSONObject("features").getJSONObject("featureMap").getJSONObject("sfts");
        return supplyInfo.getString("sc_item_id");
    }

    private String getWarehouseCodeFromOrder(JSONObject subOrderInfo) {
        return subOrderInfo.getJSONObject("wareHouseDTO").getString("wareHouseCode").split("-")[0];
    }

    private String getProductIdFromOrder(JSONObject subOrderInfo) {
        return subOrderInfo.getJSONObject("product").getString("itemId");
    }

    private String getSkuIdFromOrder(JSONObject subOrderInfo) {
        return subOrderInfo.getJSONObject("product").getJSONObject("sku").getString("skuId");
    }

    private String getWeightDiffType(String weightType) {
        return "仓>商家重量".equalsIgnoreCase(weightType) ? "1" : "2";
    }

    private JSONObject getCreateJitWeightScaleBreachRequest(String sellerId, String sellerWeight, String radio,
                                                            String diffType, String lbx, String scItemId,
                                                            String warehouseCode, String productId, String skuId, String orderType) {
        JSONObject request = new JSONObject();
        request.put("breachLevel", "BREACHED");
        request.put("initiatorCode", "sc_item");
        if (orderType == "全托管") {
            request.put("responsibilityCode", "JIT-WEIGHT-SCALE");
            request.put("penaltyType", "JIT_WEIGHT_SCALE_PUNISH");
        } else if (orderType == "半托管") {
            request.put("responsibilityCode", "SEMI_WEIGHT_SCALE_PUNISH");
            request.put("penaltyType", "SEMI-WEIGHT-SCALE");
        }
        request.put("targetId", scItemId);
        request.put("targetType", "AE_SC_ITEM");
        request.put("initiatorId", lbx);

        JSONObject penaltyUser = new JSONObject();
        penaltyUser.put("userId", sellerId);
        request.put("penaltyUser", penaltyUser);

        String warehouseWeight = getWarehouseWeight(sellerWeight, radio, diffType);
        JSONObject memoFeatures = new JSONObject();
        memoFeatures.put("bizId", "12345678");
        memoFeatures.put("merchantWeight", sellerWeight);
        memoFeatures.put("warehouseWeight", warehouseWeight);
        memoFeatures.put("abnormalType", diffType);
        memoFeatures.put("overloadWeight", getOverloadWeight(sellerWeight, warehouseWeight));
        memoFeatures.put("overloadRatio", radio);
        memoFeatures.put("warehouseCode", warehouseCode);
        memoFeatures.put("productId", productId);
        memoFeatures.put("inboundOrderCreateTime", String.valueOf(System.currentTimeMillis()));
        memoFeatures.put("skuId", skuId);
        request.put("memoFeatures", memoFeatures);

        request.put("class", "com.alibaba.ae.service.open.param.ApplyBreachContractParam");
        System.err.println(request);
        return request;
    }

    private Object getOverloadWeight(String sellerWeight, String warehouseWeight) {
        Long diff = Long.parseLong(sellerWeight) - Long.parseLong(warehouseWeight);
        return String.valueOf(Math.abs(diff));
    }

    private String getWarehouseWeight(String sellerWeight, String radio, String diffType) {
        if ("1".equalsIgnoreCase(diffType)) {
            Double warehouseWeight = Double.parseDouble(sellerWeight) / (1 - Double.parseDouble(radio));
            return String.valueOf(Math.round(warehouseWeight));
        } else {
            Double warehouseWeight = Double.parseDouble(sellerWeight) / (1 + Double.parseDouble(radio));
            return String.valueOf(Math.round(warehouseWeight));
        }
    }

    private JSONObject generateSemiChoiceJitBreachReq(String poStatus, Long shipMethod, String breachType) {
        JSONObject req = new JSONObject();
        Long current = System.currentTimeMillis();
        req.put("semiJit", true);
        req.put("tradeOrderId", 8175284250036440L);
        req.put("fulfillmentOrderNo", "FO2405512185717498");
        req.put("inboardOrderNo", "PON2309152000371801");
        req.put("shippingMode", shipMethod);
        req.put("gmtPoCreate", current);
        req.put("gmtArrivalRegister", current);
        req.put("gmtPoCancel", current);
        req.put("gmtPickup", current);
//        req.put("sellerId", "244195061");
        req.put("useInboardOrderId", false);
        req.put("receivedNormalQuantity", 1L);
        if ("待接单".equalsIgnoreCase(poStatus)) {
            req.put("poStatus", -1);
            req.remove("gmtPoCancel");
        } else if ("已取消".equalsIgnoreCase(poStatus)) {
            req.put("poStatus", -99);
            if ("罚一".equalsIgnoreCase(breachType)) {
                req.put("gmtPoCreate", current - 172900000);  // > 48h
            } else if ("罚二".equalsIgnoreCase(breachType)) {
                req.put("gmtPoCreate", current - 345700000);  // > 96h
            } else if ("免责".equalsIgnoreCase(breachType)) {
                req.put("gmtPoCreate", current - 3600000);  // < 48h
            }
        } else if ("已接单未上架".equalsIgnoreCase(poStatus)) {
            req.put("poStatus", 40);
            req.put("receivedNormalQuantity", 0);
        } else if ("已接单已上架".equalsIgnoreCase(poStatus)) {
            req.put("poStatus", 40);
            req.remove("gmtPoCancel");
            if ("罚一".equalsIgnoreCase(breachType)) {
                if (shipMethod == 1) {
                    req.put("gmtPoCreate", current - 172900000);  // > 48h
                } else {
                    LocalDateTime currentTime = LocalDateTime.ofEpochSecond(current, 0, ZoneOffset.UTC);
                    if (currentTime.getHour() > 14) {
                        req.put("gmtPoCreate", current - 345600000);  // -4天
                    } else {
                        req.put("gmtPoCreate", current - 129600000);  // -1.5天
                    }
                }
            } else if ("罚二".equalsIgnoreCase(breachType)) {
                if (shipMethod == 1) {
                    req.put("gmtPoCreate", current - 345700000);  // > 48h
                } else {
                    LocalDateTime currentTime = LocalDateTime.ofEpochSecond(current, 0, ZoneOffset.UTC);
                    if (currentTime.getHour() > 14) {
                        req.put("gmtPoCreate", current - 518400000);  // -6天
                    } else {
                        req.put("gmtPoCreate", current - 302400000);  // -3.5天
                    }
                }
            } else if ("免责".equalsIgnoreCase(breachType)) {
                req.put("gmtPoCreate", current - 3600000);  // < 48h
            }
        }
        return req;
    }

    private Long getShipMethod(String param) {
        if ("自寄发货".equalsIgnoreCase(param)) return 1L;
        if ("未选择发货模式".equalsIgnoreCase(param)) return -1L;
        return 2L;//揽收
    }

    private String getWarehouseBreachReq(Long sellerId, String breachScenario, Long shipMethod, String coStatus,
                                         String poOrder, String bhOrder, String coOrders, String scItemId) {
        String breachReqMsg = getBreachMsgBody(breachScenario, coStatus);
        JSONObject req = JSONObject.parseObject(breachReqMsg);
        req.put("sellerId", sellerId);
        req.put("gmtCreateTime", System.currentTimeMillis());
        if (StringUtils.isNotBlank(poOrder)) {
            req.put("inboundOrderId", poOrder);
        }
        if (StringUtils.isNotBlank(bhOrder)) {
            req.put("replenishmentOrderId", bhOrder);
        } else {
            req.put("replenishmentOrderId", "BH" + System.currentTimeMillis());
        }
        if (StringUtils.isNotBlank(coOrders)) {
            String[] coOrderList = coOrders.split(",");
            JSONObject conOrderDemo = req.getJSONArray("consignOrderList").getJSONObject(0);
            JSONArray consignOrders = new JSONArray();
            for (String conOrder : coOrderList) {
                JSONObject consignOrder = JSONObject.parseObject(conOrderDemo.toJSONString());
                consignOrder.put("consign_order_no", conOrder);
                consignOrder.put("ae_shipping_mode", shipMethod);
                if (StringUtils.isNotBlank(scItemId)) {
                    consignOrder.put("sc_item_id", scItemId);
                }
                // todo 通过co单查询并回填信息

                consignOrders.add(consignOrder);
            }
            req.put("consignOrderList", consignOrders);
        } else if (StringUtils.isNotBlank(req.getJSONArray("consignOrderList").getJSONObject(0).getString("ae_shipping_mode"))) {
            req.getJSONArray("consignOrderList").getJSONObject(0).put("ae_shipping_mode", shipMethod);
            if (StringUtils.isNotBlank(scItemId)) {
                req.getJSONArray("consignOrderList").getJSONObject(0).put("sc_item_id", scItemId);
            }
        }
        if (StringUtils.isNotBlank(scItemId)) {
            req.put("scItemId", scItemId);
        }
        return req.toString();
    }

    private String getBreachMsgBody(String breachScenario, String coStatus) {
        switch (breachScenario) {
            case "未到仓处罚":
                if ("无效发货单".equalsIgnoreCase(coStatus)) return Constant.BREACH_WAREHOUSE_TIMEOUT_CO_INVALID_MSG_BODY;
                if ("无发货单".equalsIgnoreCase(coStatus)) return Constant.BREACH_WAREHOUSE_TIMEOUT_WITHOUT_CO_MSG_BODY;
                return Constant.BREACH_WAREHOUSE_TIMEOUT_CO_NOT_ENOUGH_MSG_BODY;
            case "少到仓处罚":
                return Constant.BREACH_WAREHOUSE_LESS_QUANTITY_MSG_BODY;
        }
        return "";
    }


    @Override
    public ResultDTO mockInitLogisticOnlineOrderDTO(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);

        JSONArray onlineOrderLineDTOList = new JSONArray();

        JSONArray orderLines = jsonObject.getJSONArray(Constant.ORDER_LINES);
        JSONObject orderInfo = jsonObject.getJSONObject(Constant.ORDER_INFO);

        String dpath = jsonObject.getString(Constant.DPATH_ENV);
        if (StringUtil.isNotBlank(dpath)) {
            EagleEye.putUserData("dpath_env", dpath);
        }

        String logisticNums = jsonObject.getString("logisticNums");
        String[] logisticNumList = new String[]{};
        if (StringUtil.isNotEmpty(logisticNums)) {
            logisticNumList = logisticNums.split(",");
        }

        for (Object object : orderLines) {
            JSONObject o = (JSONObject) JSON.toJSON(object);
            JSONObject onlineOrderLine = new JSONObject();
            onlineOrderLine.put("isCaiNiaoLogistic", jsonObject.getString("isCaiNiaoLogistic"));
            onlineOrderLine.put("firstCategoryId", "");
            onlineOrderLine.put("paymentAuthorizedTime", 1702127748000L);
            onlineOrderLine.put("productPunishTime", 1661959672000L);
            onlineOrderLine.put("logisticOnlineTime", 1702458011000L);
            onlineOrderLine.put("logisticOnlineTimeoutOf48", false);
            onlineOrderLine.put("logisticOnlineTimeoutOf72", false);
            onlineOrderLine.put("logisticOnlineTimeoutOf96", false);
            switch (jsonObject.getString("logisticOnlineTimeout")) {
                case "48小时":
                    onlineOrderLine.put("logisticOnlineTimeoutOf48", true);
                    break;
                case "72小时":
                    onlineOrderLine.put("logisticOnlineTimeoutOf72", true);
                    break;
                case "96小时":
                    onlineOrderLine.put("logisticOnlineTimeoutOf96", true);
                    break;
            }
            onlineOrderLine.put("class", "com.alibaba.ae.service.domain.dto.LogisticOnlineOrderLineDTO");
            onlineOrderLine.put("secondCategoryId", "123");
            onlineOrderLine.put("tradeOrderLineId", o.getString("tradeOrderLineId"));
            onlineOrderLine.put("categoryId", o.getJSONObject("product").getString("categoryId"));
            onlineOrderLine.put("logisticNums", logisticNumList);
            onlineOrderLine.put("logisticOnlineControl", jsonObject.getString("logisticOnlineControl"));
            onlineOrderLine.put("buyerCountry", jsonObject.getString("buyerCountry"));
            onlineOrderLineDTOList.add(onlineOrderLine);
        }

        JSONObject reqJson = new JSONObject();
        reqJson.put("quotationCurrency", orderInfo.getString("actualFeeOfPurposeCurrency"));
        reqJson.put("sellerId", jsonObject.getString(Constant.SELLER_ID));
        reqJson.put("payAmount", orderInfo.getString("orderAmount"));
        reqJson.put("extraMap", "");
        reqJson.put("tradeOrderId", jsonObject.getString(Constant.ORDER_ID));
        reqJson.put("onlineOrderLineDTOList", onlineOrderLineDTOList);
        reqJson.put("payCurrency", orderInfo.getString("actualFeeOfPurposeCurrency"));
        reqJson.put("quotationCurrency", orderInfo.getString("actualFeeOfPurposeCurrency"));
        reqJson.put("buyerId", jsonObject.getString(Constant.BUYER_ID));

        // JSONObject res = HsfUtil.mockInitLogisticOnlineOrderDTO(reqJson);

        //发送处罚消息
        SendResult sendResult = mqConfig.sendMessage(Constant.BREACH_WAREHOUSE_TOPIC, Constant.LOGISTICS_ONLINE_ORDER, jsonObject.getString(Constant.ORDER_ID), JSONObject.toJSONString(reqJson));

        result.setMessage("发送处罚消息" + sendResult.getMsgId());
        result.setData("发送处罚消息" + sendResult.getMsgId());
        result.setSuccess(true);
        return result;

    }

    @Override
    public ResultDTO mockImBreachSendCoupon(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        String orderId = jsonObject.getString(Constant.ORDER_ID);
        String buyerId = jsonObject.getString(Constant.BUYER_ID);
        String sellerId = jsonObject.getString(Constant.SELLER_ID);
        String payoutReason = getAppealAbnormalCode(jsonObject.getString(Constant.PAYOUT_REASON));
        if (StringUtils.isEmpty(payoutReason)) {
            result.setSuccess(true);
            result.setData("未找到投诉类型");
            result.setMessage("未找到投诉类型");
            return result;
        }
        JSONObject request = getSendCouponRequest(orderId, buyerId, sellerId, payoutReason);
        JSONObject data = HsfUtil.applyBreach(request);
        result.setSuccess(true);
        result.setData(data.toJSONString());
        result.setMessage(data.toJSONString());
        return result;
    }

    private JSONObject getSendCouponRequest(String orderId, String buyerId, String sellerId, String payoutReason) {
        JSONObject request = new JSONObject();
        request.put("breachLevel", "BREACHED");

        JSONObject memoFeatures = new JSONObject();
        memoFeatures.put("sellerId", sellerId);
        memoFeatures.put("appealAbnormalCode", payoutReason);
        request.put("memoFeatures", memoFeatures);

        request.put("targetId", orderId);
        request.put("penaltyType", "BUYER_PROTECTION_COMPENSATE");
        request.put("targetType", "AE_MAIN_ORDER");
        request.put("initiatorId", String.valueOf(System.currentTimeMillis()));
        request.put("buyerId", buyerId);
        request.put("initiatorCode", "cco");
        request.put("responsibilityCode", "BUYER-PROTECTION-COMPENSATE-SERVICE");

        JSONObject penaltyUser = new JSONObject();
        penaltyUser.put("role", "Buyer");
        penaltyUser.put("userId", buyerId);
        request.put("penaltyUser", penaltyUser);

        request.put("identity", "cco");
        return request;
    }

    private String getAppealAbnormalCode(String payoutReason) {
        switch (payoutReason) {
            case "商家降级发货":
                return "1";
            case "商家虚假发货":
                return "2";
            case "商家要求补运费":
                return "3";
            case "商家引导修改纠纷理由或取消纠纷":
                return "4";
            case "商家一般辱骂/威胁消费者":
                return "5";
            case "商家严重辱骂/威胁消费者":
                return "6";
            case "商家特别严重辱骂/威胁消费者":
                return "7";
            case "商家一般违背售后承诺":
                return "8";
            case "商家严重违背售后承诺":
                return "9";
            case "商家特别严重违背售后承诺":
                return "10";
        }
        return "";
    }

    @Override
    public ResultDTO mockImBreachSeller(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        String orderId = jsonObject.getString(Constant.ORDER_ID);
        String buyerId = jsonObject.getString(Constant.BUYER_ID);
        String sellerId = jsonObject.getString(Constant.SELLER_ID);
        String payoutReason = getAppealAbnormalCode(jsonObject.getString(Constant.PAYOUT_REASON));
        if (StringUtils.isEmpty(payoutReason)) {
            result.setSuccess(true);
            result.setData("未找到投诉类型");
            result.setMessage("未找到投诉类型");
            return result;
        }
        JSONObject request = getBreachSellerRequest(orderId, buyerId, sellerId, payoutReason);
        JSONObject data = HsfUtil.applyBreach(request);
        result.setSuccess(true);
        result.setData(data.toJSONString());
        result.setMessage(data.toJSONString());
        return result;
    }

    private JSONObject getBreachSellerRequest(String orderId, String buyerId, String sellerId, String payoutReason) {
        JSONObject request = new JSONObject();
        request.put("breachLevel", "BREACHED");

        JSONObject memoFeatures = new JSONObject();
        memoFeatures.put("sellerId", sellerId);
        memoFeatures.put("appealAbnormalCode", payoutReason);
        request.put("memoFeatures", memoFeatures);

        request.put("targetId", orderId);
        request.put("penaltyType", "BUYER_PROTECTION_COMPENSATE");
        request.put("targetType", "AE_MAIN_ORDER");
        request.put("initiatorId", String.valueOf(System.currentTimeMillis()));
        request.put("buyerId", buyerId);
        request.put("initiatorCode", "cco");
        request.put("responsibilityCode", "BUYER-PROTECTION-COMPENSATE-SERVICE");

        JSONObject penaltyUser = new JSONObject();
        penaltyUser.put("role", "Seller");
        penaltyUser.put("userId", sellerId);
        request.put("penaltyUser", penaltyUser);

        request.put("identity", "cco");
        return request;
    }

    @Override
    public ResultDTO expensiveCoupons(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long breachId = jsonObject.getLong(Constant.BREACH_ID);
        JSONObject breachContract = HsfUtil.queryBreachContractDO(breachId);

        JSONObject req = JSONObject.parseObject(EXPENSIVE_COUPONS_REQ);
        req.put("breachContractId", breachId);
        req.put("initiatorId", breachContract.getString("initiatorId"));
        req.put("targetId", breachContract.getString("targetId"));

        //发送贵必赔发券消息
        SendResult sendResult = mqConfig.sendMessage(Constant.EXPENSIVE_COUPONS_TOPIC, Constant.EXPENSIVE_COUPONS_TAG, jsonObject.getString(Constant.ORDER_ID), JSONObject.toJSONString(req));
        resultDTO.setSuccess(true);
        resultDTO.setMessage(sendResult.getMsgId());
        resultDTO.setData(sendResult.getMsgId());
        return resultDTO;
    }

    @Override
    public ResultDTO mockLogisticOnlineBreach(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        Long sellerId = jsonObject.getLong(Constant.SELLER_ID);
        Long tradeOrderId = jsonObject.getLong(Constant.ORDER_ID);
        Long logisticOnlineTime = jsonObject.getLong(Constant.LOGISTIC_ONLINE_TIME);
        Long paymentAuthorizedTime = jsonObject.getLong(Constant.PAYMENT_AUTH_TIME);
        String fulfillmentCreateTime = jsonObject.getString(Constant.FULFILLMENT_CREATE_TIME);
        Long productPunishTime = jsonObject.getLong(Constant.PRODUCT_PUBLISH_TIME);
        String orderType = jsonObject.getString(Constant.ORDER_TYPE);
        Boolean caiNiaoLogistic = jsonObject.getBoolean(Constant.CAINIAO_LOGISTIC);
        JSONObject msgBody = new JSONObject();
        msgBody.put("buyerId", buyerId);
        msgBody.put("needExamine", false);
        msgBody.put("payAmount", 1221);
        msgBody.put("payCurrency", "USD");
        msgBody.put("sellerId", sellerId);
        msgBody.put("tradeOrderId", tradeOrderId);
        JSONArray onlineOrderLineDTOList = new JSONArray();
        JSONObject onlineOrderLineDTO = new JSONObject();
        onlineOrderLineDTO.put("caiNiaoLogistic", caiNiaoLogistic);
        onlineOrderLineDTO.put("categoryId", "201531501");
        onlineOrderLineDTO.put("secondCategoryId", "200000778");
        JSONArray logisticNums = new JSONArray();
        logisticNums.add("QA_TEST_ABABA" + System.currentTimeMillis());
        onlineOrderLineDTO.put("logisticNums", logisticNums);
        onlineOrderLineDTO.put("logisticOnlineTime", logisticOnlineTime);
        if (StringUtils.isNotBlank(fulfillmentCreateTime)) {
            onlineOrderLineDTO.put("fulfillmentOrderCreateTime", Long.valueOf(fulfillmentCreateTime));
        }
        // logisticOnlineTimeoutOf48 不再使用，逆向自己计算
        onlineOrderLineDTO.put("logisticOnlineTimeoutOf48", false);
        onlineOrderLineDTO.put("logisticOnlineTimeoutOf72", false);
        onlineOrderLineDTO.put("logisticOnlineTimeoutOf96", false);
        onlineOrderLineDTO.put("paymentAuthorizedTime", paymentAuthorizedTime);
        onlineOrderLineDTO.put("productPunishTime", productPunishTime);
        onlineOrderLineDTO.put("logisticOnlineControl", "1");
        onlineOrderLineDTO.put("buyerCountry", "US");
        onlineOrderLineDTO.put("tradeOrderLineId", tradeOrderId + 10000);
        onlineOrderLineDTO.put("orderType", orderType);
        onlineOrderLineDTOList.add(onlineOrderLineDTO);
        msgBody.put("onlineOrderLineDTOList", onlineOrderLineDTOList);
        SendResult sendResult = mqConfig.sendMessage(Constant.BREACH_WAREHOUSE_TOPIC, Constant.LOGISTICS_ONLINE_ORDER, String.valueOf(tradeOrderId), msgBody.toJSONString());
        resultDTO.setSuccess(true);
        resultDTO.setData(JSONObject.toJSONString(sendResult));
        resultDTO.setMessage(JSONObject.toJSONString(sendResult));
        return resultDTO;
    }

    @Override
    public ResultDTO exemptBreach(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long breachContractId = jsonObject.getLong(Constant.BREACH_ID);
        Long sellerId = jsonObject.getLong(Constant.SELLER_ID);
        String breachContractType = jsonObject.getString(Constant.TYPE);
        String userRate = jsonObject.getString(Constant.USER_RATE);
        String systemRate = jsonObject.getString(Constant.SYSTEM_RATE);
        StringBuffer sb = new StringBuffer();
        sb.append(sellerId).append("#").append(breachContractId).append("#").append(breachContractType);
        String tag = Constant.SYSTEM_EXEMPTION;
        if (StringUtils.isNotEmpty(userRate)) {
            sb.append("#").append(userRate);
            sb.append("#").append(systemRate);
            tag = Constant.SELLER_PERFORMANCE_EXEMPTION;
        }
        SendResult sendResult = mqConfig.sendMessage(Constant.SERVICE_PLATFORM_UNFINISHED_CONTRACT_TOPIC, tag, String.valueOf(breachContractId), sb.toString());
        resultDTO.setSuccess(true);
        resultDTO.setData(JSONObject.toJSONString(sendResult));
        resultDTO.setMessage(JSONObject.toJSONString(sendResult));
        return resultDTO;
    }

    @Override
    public ResultDTO priceProtectionCompensate(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long tradeOrderId = jsonObject.getLong(Constant.ORDER_ID);
        Long tradeOrderLineId = jsonObject.getLong(Constant.SUB_ORDER_ID);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        Long sellerId = jsonObject.getLong(Constant.SELLER_ID);
        String currencyCode = jsonObject.getString(Constant.CURRENCY);
        JSONObject param = getCreatePriceProtectionCompensateRequest(tradeOrderId, tradeOrderLineId, buyerId, sellerId, currencyCode);
        JSONObject result = HsfUtil.createPriceProtectionCompensate(param);
        resultDTO.setSuccess(true);
        resultDTO.setData(result.toJSONString());
        resultDTO.setMessage(result.toJSONString());
        return resultDTO;
    }

    private JSONObject getCreatePriceProtectionCompensateRequest(Long tradeOrderId, Long tradeOrderLineId, Long buyerId,
                                                                 Long sellerId, String currencyCode) {
        JSONObject param = new JSONObject();
        param.put("sellerId", sellerId);
        param.put("buyerId", buyerId);
        param.put("tradeOrderId", tradeOrderId);
        param.put("priceProtectionId", tradeOrderLineId);
        param.put("tradeOrderLineId", tradeOrderLineId);
        JSONObject refundAmount = new JSONObject();
        refundAmount.put("cent", 1);
        refundAmount.put("currencyCode", StringUtils.isNoneBlank("currencyCode") ? currencyCode : "USD");
        param.put("refundAmount", refundAmount);
        return param;
    }

    @Override
    public ResultDTO sendJITTimeoutMessage(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        String orderId = jsonObject.getString(Constant.ORDER_ID);
        String poOrder = jsonObject.getString(Constant.PO_ORDER);
        String orderMessageType = getOrderMessageType(jsonObject.getString(Constant.JITOrder_Type));
        String msgBody = poOrder + "#" + orderId + "#" + jsonObject.getString(Constant.SELLER_ID) + "#" + jsonObject.getString(Constant.SELLER_ID) + "#" + "store_code";
        SendResult sendResult = mqConfig.sendMessage(orderMessageType, poOrder, poOrder, msgBody);
        resultDTO.setSuccess(true);
        resultDTO.setData(JSON.toJSONString(sendResult));
        resultDTO.setMessage(JSON.toJSONString(sendResult));
        return resultDTO;
    }

    @Override
    public ResultDTO triggerQtgQualityPunish(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long tradeOrderLineId = jsonObject.getLong(Constant.TRADE_ORDER_LINE_ID);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        JSONObject reverseOrderLine = HsfUtil.getReverseOrderLineByTradeOrderLineId(buyerId, tradeOrderLineId).getJSONObject("result");
        Long reverseOrderLineId = reverseOrderLine.getLong("reverseOrderLineId");
        JSONObject Responses = HsfUtil.triggerQtgQualityPunish(reverseOrderLineId);
        if (Responses.getBoolean("success")) {
            result.setData("操作成功" + Responses.toJSONString());
            result.setMessage("操作成功" + Responses.toJSONString());
        } else {
            result.setData("操作失败。Res: " + Responses.toJSONString());
            result.setMessage("操作失败。Res: " + Responses.toJSONString());
        }
        return result;
    }

    @Override
    public ResultDTO triggerOnTimeGuarantee(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long orderId = jsonObject.getLong(Constant.ORDER_ID);
        Long orderLineId = jsonObject.getLong(Constant.TRADE_ORDER_LINE_ID);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        Long sellerId = jsonObject.getLong(Constant.SELLER_ID);
        Map<String, String> ext = new HashMap<>();
        ext.put("", "");
        JSONObject Responses = HsfUtil.triggerOnTimeGuarantee(buyerId, sellerId, orderId, orderLineId, ext);
        if (Responses.getBoolean("success")) {
            result.setData("操作成功" + Responses.toJSONString());
            result.setMessage("操作成功" + Responses.toJSONString());
        } else {
            result.setData("操作失败。Res: " + Responses.toJSONString());
            result.setMessage("操作失败。Res: " + Responses.toJSONString());
        }
        return result;
    }

    @Override
    public ResultDTO applyCompensate(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        String orderId = jsonObject.getString(Constant.ORDER_ID);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        JSONObject ApplyBreachCompensateParam = new JSONObject();
        ApplyBreachCompensateParam.put("promiseId", "33");
        ApplyBreachCompensateParam.put("targetId", orderId);
        ApplyBreachCompensateParam.put("initiatorId", orderId);
        JSONObject penaltyUser = new JSONObject();
        penaltyUser.put("role", "Buyer");
        penaltyUser.put("userId", buyerId);
        ApplyBreachCompensateParam.put("penaltyUser", penaltyUser);
        JSONObject Responses = HsfUtil.applyCompensate(ApplyBreachCompensateParam);
        if (Responses.getBoolean("success")) {
            result.setData("操作成功" + Responses.toJSONString());
            result.setMessage("操作成功" + Responses.toJSONString());
        } else {
            result.setData("操作失败。Res: " + Responses.toJSONString());
            result.setMessage("操作失败。Res: " + Responses.toJSONString());
        }
        return result;
    }

    @Override
    public ResultDTO mockChoiceRefundRateBreach(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);

        String tradeOrderId = jsonObject.getString(Constant.ORDER_ID);
        String tradeOrderLineId = jsonObject.getString(Constant.SUB_ORDER_ID);
        String sellerId = jsonObject.getString(Constant.SELLER_ID);
        String refundRate = StringUtils.isNoneBlank(jsonObject.getString(Constant.REFUND_RATE)) ? jsonObject.getString(Constant.REFUND_RATE) : "0.81";
        String refundRateStandard = StringUtils.isNoneBlank(jsonObject.getString(Constant.REFUND_RATE_STANDARD)) ? jsonObject.getString(Constant.REFUND_RATE_STANDARD) : "0.8";
        String itemQty = StringUtils.isNoneBlank(jsonObject.getString(Constant.QUANTITY)) ? jsonObject.getString(Constant.QUANTITY) : "10";
        String checkMonth = StringUtils.isNoneBlank(jsonObject.getString(Constant.TIMESTAMP)) ? jsonObject.getString(Constant.TIMESTAMP) : getLast2CheckMonth();
        String refundRateMessageTag = getRefundRateMessageTag(jsonObject.getString(Constant.MSG_TAG));


        StringBuilder sb = new StringBuilder();
        //商家id#一级类目id#二级类目i#一级类目英文名称#二级类目英文名称d#一级类目名称#二级类目名称#商家货品退款率#考核月份#逆向子单id#纠纷结束时间#交易主单id#交易子单id#发货时间#货品退货率门槛#交易子单数量#
        sb.append(sellerId).append("#200000345#349#3C electric#phone#3C电子balabalabala商品类目辣么辣么辣么辣么长长长长长长长长长长长#手机balabalabala商品类目辣么辣么辣么辣么长长长长长长长长长长长长长长长长长长#")
                .append(refundRate).append("#").append(checkMonth).append("#6347968100252202#")
                .append(System.currentTimeMillis() - 2592000000L).append("#").append(tradeOrderId)
                .append("#").append(tradeOrderLineId).append("#").append(System.currentTimeMillis() - 2764800000L)
                .append("#").append(refundRateStandard).append("#").append(itemQty).append("#");

        String dpath = jsonObject.getString(Constant.DPATH_ENV);
        if (StringUtil.isNotBlank(dpath)) {
            EagleEye.putUserData("dpath_env", dpath);
        }

        SendResult sendResult = mqConfig.sendMessage(Constant.REFUND_RATE_MESSAGE_TOPIC, refundRateMessageTag, tradeOrderLineId, sb.toString());

        resultDTO.setSuccess(true);
        resultDTO.setData(JSON.toJSONString(sendResult));
        resultDTO.setMessage(JSON.toJSONString(sendResult));
        return resultDTO;

    }

    private String getLast2CheckMonth() {
        StringBuilder sb = new StringBuilder();
        LocalDate now = LocalDate.now();
        LocalDate lastMonth = now.minusMonths(1);
        LocalDate last2Month = now.minusMonths(2);
        int lastYear = lastMonth.getYear();
        int lastMonthValue = lastMonth.getMonthValue();
        int last2MonthYear = last2Month.getYear();
        int last2MontyValue = last2Month.getMonthValue();

        sb.append(lastYear);
        if (lastMonthValue < 10) {
            sb.append("0");
        }
        sb.append(lastMonthValue).append("-").append(last2MonthYear);
        if (last2MontyValue < 10) {
            sb.append("0");
        }
        sb.append(last2MontyValue);
        return sb.toString();
    }

    @Override
    public ResultDTO sendOverseaConsignPunishMessage(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        String tradeOrderId = jsonObject.getString(Constant.ORDER_ID);
        String buyerId = jsonObject.getString(Constant.BUYER_ID);
        String orderType = jsonObject.getString(Constant.ORDER_TYPE);
        String dpath = jsonObject.getString(Constant.DPATH_ENV);
        if (StringUtil.isNotBlank(dpath)) {
            EagleEye.putUserData("dpath_env", dpath);
        }
        String orderTypeString = getOrderType(orderType);
        String msgBody = tradeOrderId + "#" + buyerId + "#" + orderTypeString;
        SendResult sendResult = mqConfig.sendMessage(Constant.OVERSEA_CONSIGN_PUNISH_TOPIC, "consign_timeout", tradeOrderId, msgBody);
        resultDTO.setSuccess(true);
        resultDTO.setData(JSON.toJSONString(sendResult));
        resultDTO.setMessage(JSON.toJSONString(sendResult));
        return resultDTO;
    }

    @Override
    public ResultDTO sendWeightScaleRecoverMessage(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        String orderType = jsonObject.getString(Constant.ORDER_TYPE);
        String ds = StringUtils.isNoneBlank(jsonObject.getString(Constant.DS)) ? jsonObject.getString(Constant.DS) : "20241101";


        String orderTypeValue = getdWeightScaleRecoverOrderType(orderType);
        String dpath = jsonObject.getString(Constant.DPATH_ENV);
        if (StringUtil.isNotBlank(dpath)) {
            EagleEye.putUserData("dpath_env", dpath);
        }
        String sellerId = "";
        String scItemId = "";
        String scItemName = "";
        String lbxNo = "";


        if (orderTypeValue == "AEFF") {
            sellerId = StringUtils.isNoneBlank(jsonObject.getString(Constant.SELLER_ID)) ? jsonObject.getString(Constant.SELLER_ID) : "200042360";
            scItemId = StringUtils.isNoneBlank(jsonObject.getString(Constant.SC_ITEM_ID)) ? jsonObject.getString(Constant.SC_ITEM_ID) : "825407340184";
            scItemName = StringUtils.isNoneBlank(jsonObject.getString(Constant.SC_ITEM_NAME)) ? jsonObject.getString(Constant.SC_ITEM_NAME) : "rsn test quality center";
            lbxNo = StringUtils.isNoneBlank(jsonObject.getString(Constant.LBX_NO)) ? jsonObject.getString(Constant.LBX_NO) : "LBX02254417646104018";
        } else {
            sellerId = StringUtils.isNoneBlank(jsonObject.getString(Constant.SELLER_ID)) ? jsonObject.getString(Constant.SELLER_ID) : "2671514005";
            scItemId = StringUtils.isNoneBlank(jsonObject.getString(Constant.SC_ITEM_ID)) ? jsonObject.getString(Constant.SC_ITEM_ID) : "797243160440";
            scItemName = StringUtils.isNoneBlank(jsonObject.getString(Constant.SC_ITEM_NAME)) ? jsonObject.getString(Constant.SC_ITEM_NAME) : "test price 0518";
            lbxNo = StringUtils.isNoneBlank(jsonObject.getString(Constant.LBX_NO)) ? jsonObject.getString(Constant.LBX_NO) : "LBX02284517671160698";

        }

        String buyerId = StringUtils.isNoneBlank(jsonObject.getString(Constant.BUYER_ID)) ? jsonObject.getString(Constant.BUYER_ID) : "**********";

        Boolean testBuyerFlag = dataPoolService.checkTestAccount(Long.parseLong(buyerId));
        Boolean testSellerFlag = dataPoolService.checkTestAccount(Long.parseLong(sellerId));
        if (testBuyerFlag || testSellerFlag) {
            resultDTO.setSuccess(false);
            resultDTO.setData(JSON.toJSONString("发送消息只支持测试账号"));
        }


        String latestCertificationTime = StringUtils.isNoneBlank(jsonObject.getString(Constant.LATEST_CERTIFICATION_TIME)) ? jsonObject.getString(Constant.LATEST_CERTIFICATION_TIME) : String.valueOf(System.currentTimeMillis());
        String lastCertificationTime = StringUtils.isNoneBlank(jsonObject.getString(Constant.LAST_CERTIFICATION_TIME)) ? jsonObject.getString(Constant.LAST_CERTIFICATION_TIME) : "*************";
        String latestWeight = StringUtils.isNoneBlank(jsonObject.getString(Constant.LATEST_WEIGHT)) ? jsonObject.getString(Constant.LATEST_WEIGHT) : "250";
        String lastRoundWeight = StringUtils.isNoneBlank(jsonObject.getString(Constant.LAST_ROUND_WEIGHT)) ? jsonObject.getString(Constant.LAST_ROUND_WEIGHT) : "220";
        String itemCount = StringUtils.isNoneBlank(jsonObject.getString(Constant.ITEM_COUNT)) ? jsonObject.getString(Constant.ITEM_COUNT) : "15";
        String weightInterval = StringUtils.isNoneBlank(jsonObject.getString(Constant.WEIGHT_INTERVAL)) ? jsonObject.getString(Constant.WEIGHT_INTERVAL) : "30";
        String weightRecoverStandard = StringUtils.isNoneBlank(jsonObject.getString(Constant.WEIGHT_RECOVER_STANDARD)) ? jsonObject.getString(Constant.WEIGHT_RECOVER_STANDARD) : "50g && >10%";
        String overloadWeightRatio = StringUtils.isNoneBlank(jsonObject.getString(Constant.OVERLOAD_WEIGHT_RATIO)) ? jsonObject.getString(Constant.OVERLOAD_WEIGHT_RATIO) : "0.136";
        String overloadWeight = StringUtils.isNoneBlank(jsonObject.getString(Constant.OVERLOAD_WEIGHT)) ? jsonObject.getString(Constant.OVERLOAD_WEIGHT) : "30";

        String msgBody = sellerId + "#" + scItemId + "#" + scItemName + "#" + lbxNo
                + "#" + latestCertificationTime
                + "#" + lastCertificationTime
                + "#" + latestWeight
                + "#" + lastRoundWeight
                + "#" + itemCount
                + "#" + weightInterval
                + "#" + weightRecoverStandard
                + "#" + overloadWeightRatio
                + "#" + overloadWeight
                + "#" + orderTypeValue
                + "#" + ds;
        String key = scItemId + "_" + latestCertificationTime;

        SendResult sendResult = mqConfig.sendMessage(Constant.AE_SERVICE_PLATFORM_WEIGHT_SCALE_RECOVER_TOPIC, Constant.AE_SERVICE_PLATFORM_WEIGHT_SCALE_RECOVER_TAG, key, msgBody);
        resultDTO.setSuccess(true);
        resultDTO.setData(JSON.toJSONString(sendResult));
        resultDTO.setMessage(JSON.toJSONString(sendResult));
        return resultDTO;
    }

    @Override
    public ResultDTO handleBreachContract(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        return resultDTO;
    }

    @Override
    public ResultDTO getChoiceRefundRateBreachInfo(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        String tradeOrderLineId = jsonObject.getString(Constant.SUB_ORDER_ID);
        Long sellerId = jsonObject.getLong(Constant.SELLER_ID);
        JSONObject result = HsfUtil.queryBreachContractDetail(tradeOrderLineId, sellerId, "CHOICE-SNAD-REFUND-RATE");
        resultDTO.setMessage(result.toJSONString());
        resultDTO.setData(result.toJSONString());
        resultDTO.setSuccess(true);
        return resultDTO;
    }

    @Override
    public ResultDTO mockCroComplianceBreach(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long sellerId = jsonObject.getLong(Constant.SELLER_ID);
        Long amount = StringUtils.isNotBlank(jsonObject.getString(Constant.RETURN_AMT)) ? jsonObject.getLong(Constant.RETURN_AMT) : 1;
        String currencyCode = StringUtils.isNotBlank(jsonObject.getString(Constant.CURRENCY)) ? jsonObject.getString(Constant.CURRENCY) : "CNY";
        String type = ("POP").equals(jsonObject.getString(Constant.TYPE)) ? "POP" : "CHOICE";
        JSONObject request = getCroComplianceBreachRequest(sellerId, amount, currencyCode, type);
        JSONObject result = HsfUtil.applyBreach(request);
        resultDTO.setMessage("违约单id：" + result.getLong("data"));
        resultDTO.setData("违约单id：" + result.getLong("data"));
        resultDTO.setSuccess(true);
        return resultDTO;
    }

    @Override
    public ResultDTO mockSettlementTransferMsg(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long breachContractId = jsonObject.getLong(Constant.BREACH_ID);
        Long sellerId = jsonObject.getLong(Constant.SELLER_ID);
        Long amount = jsonObject.getLong(Constant.RETURN_AMT);
        String msgTag = jsonObject.getString(Constant.MSG_TAG);
        String tag = "部分扣款".equals(msgTag) ? Constant.PARTIAL_TRANSFER_TAG : Constant.TOTAL_TRANSFER_TAG;
        String dpath = jsonObject.getString(Constant.DPATH_ENV);
        if (StringUtil.isNotBlank(dpath)) {
            EagleEye.putUserData("dpath_env", dpath);
        }

        JSONArray fundBillRes = HsfUtil.queryBreachContractFundBillDO(breachContractId);
        if (fundBillRes == null || fundBillRes.size() == 0 || fundBillRes.getJSONObject(0) == null) {
            resultDTO.setMessage("Fail to find breach fund bill. BreachContractId: " + breachContractId);
            resultDTO.setData("Fail to find breach fund bill. BreachContractId: " + breachContractId);
            resultDTO.setSuccess(true);
            return resultDTO;
        }

        List<String> result = Lists.newArrayList();
        for (int i = 0; i < fundBillRes.size(); i++) {
            JSONObject funBill = fundBillRes.getJSONObject(i);
            String billCode = funBill.getString("billCode");
            Map<String, String> features = splitFeatures(fundBillRes.getJSONObject(i).getString("features"));
            String bizCode = features.get("settle_biz_code");
            JSONObject msgBody = getSettlementTransferMessage(breachContractId, sellerId, amount, msgTag, bizCode, billCode);
            SendResult sendResult = mqConfig.sendMessage(Constant.FUND_TRANSFER_CENTER_TOPIC, tag, String.valueOf(breachContractId), msgBody.toJSONString());
            result.add("Bill code: " + billCode + ". bizCode: " + bizCode + ". Send msg result : " + JSONObject.toJSONString(sendResult));
        }

        resultDTO.setMessage(JSONObject.toJSONString(result));
        resultDTO.setData(JSONObject.toJSONString(result));
        resultDTO.setSuccess(true);
        return resultDTO;
    }

    private Map<String, String> splitFeatures(String features) {
        // demo: ;actual_amount:0;settle_biz_code:one.stop.outbreak.punish;
        Map<String, String> featureMap = Maps.newConcurrentMap();
        for (String key : features.split(";")) {
            String[] values = key.split(":");
            if (values.length == 2) {
                featureMap.put(values[0], values[1]);
            }
        }
        return featureMap;
    }

    @Override
    public ResultDTO mockCroCashOutPunish(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        String tradeOrderLineId = jsonObject.getString(Constant.SUB_ORDER_ID);
        Long sellerId = jsonObject.getLong(Constant.SELLER_ID);
        Long amount = StringUtils.isNotBlank(jsonObject.getString(Constant.RETURN_AMT)) ? jsonObject.getLong(Constant.RETURN_AMT) : 1;
        String currencyCode = StringUtils.isNotBlank(jsonObject.getString(Constant.CURRENCY)) ? jsonObject.getString(Constant.CURRENCY) : "CNY";
        JSONObject request = getCroCashOutPunish(tradeOrderLineId, sellerId, amount, currencyCode);
        JSONObject result = HsfUtil.applyBreach(request);
        resultDTO.setMessage("违约单id：" + result.getLong("data"));
        resultDTO.setData("违约单id：" + result.getLong("data"));
        resultDTO.setSuccess(true);
        return resultDTO;
    }

    @Override
    public ResultDTO mockWarehouseBreachV2(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        String orderType = jsonObject.getString(Constant.ORDER_TYPE);
        String sellerId = jsonObject.getString(Constant.SELLER_ID);
        String pon = jsonObject.getString(Constant.PO_ORDER);
        String shipTime = jsonObject.getString(Constant.TIMESTAMP);
        String messageTag = "真实链路".equals(jsonObject.getString(Constant.MSG_TAG)) ? Constant.WAREHOUSE : "WAREHOUSE_TEST_PATH";
        StringBuilder sb = new StringBuilder();
        if (orderType.equals("全托管")) {
            sb.append("CSAECHOICE#");
        } else {
            sb.append("CSAEFF#");
        }
        sb.append(pon).append("#");
        sb.append(sellerId).append("#");
        sb.append(shipTime);
        SendResult sendResult = mqConfig.sendMessage(Constant.BREACH_WAREHOUSE_TOPIC, messageTag, pon, sb.toString());
        result.setSuccess(true);
        result.setData(JSONObject.toJSONString(sendResult));
        result.setMessage(JSONObject.toJSONString(sendResult));
        return result;
    }

    @Override
    public ResultDTO mockCCOJudge(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long breachContractId = jsonObject.getLong(Constant.BREACH_ID);
        String responsibleParty = jsonObject.getString(Constant.RESPONSIBLE_PARTY);
        String type = jsonObject.getString(Constant.TYPE);
        JSONObject request = getCCOJudgeRequest(breachContractId, responsibleParty, type);
        JSONObject res = HsfUtil.judgeBreachContract(request);
        result.setSuccess(true);
        result.setData(res.toJSONString());
        result.setMessage(res.toJSONString());
        return result;
    }

    @Override
    public ResultDTO mockJItPurchasePunish(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        String environment = jsonObject.getString("environment");
        String poOrder = jsonObject.getString("poOrder");
        String businessPickUpTime = StringUtils.isNotBlank(jsonObject.getString("pickUpTime")) ? jsonObject.getString("pickUpTime") : "nullformat";
        String sellerId = jsonObject.getString("sellerId");
        String orderType = getTenantByOrderType(jsonObject.getString("orderType"));
        String messageContent = poOrder.concat("#").concat(sellerId).concat("#").concat(businessPickUpTime).concat("#").concat(orderType);

        SendResult sendResult = new SendResult();
        //发送jit超时处罚消息
        if ("正式处罚".equals(environment)) {
            sendResult = mqConfig.sendMessage(Constant.JIT_PURCHASE_PUNISH_TOPIC, Constant.JIT_PUNISH_TAG, poOrder, messageContent);
        } else if ("试跑链路".equals(environment)) {
            sendResult = mqConfig.sendMessage(Constant.JIT_PURCHASE_PUNISH_TOPIC, Constant.JIT_PUNISH_TEST_PATH, poOrder, messageContent);
        }

        result.setMessage("发送jit超时处罚消息" + sendResult.getMsgId());
        result.setData("发送jit超时处罚消息" + sendResult.getMsgId());
        result.setSuccess(true);
        return result;
    }

    @Override
    public ResultDTO testJitTimeoutBreach(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        String type = jsonObject.getString(Constant.ORDER_TYPE);
        String poStatus = jsonObject.getString(Constant.PO_STATUS);
        String deliveryType = jsonObject.getString(Constant.TYPE);
        Long poGmtCreate = jsonObject.getLong(Constant.TIMESTAMP);
        Long aimTimeStamp = jsonObject.getLong(Constant.AIM_TIMESTAMP);
        JSONObject request = getJitCalculatePenaltyType(type, poStatus, deliveryType, poGmtCreate, aimTimeStamp);
        JSONObject res = HsfUtil.calculatePenaltyType(request);
        result.setSuccess(true);
        result.setData(res.toJSONString());
        result.setMessage(res.toJSONString());
        return result;
    }

    @Override
    public ResultDTO mockDeliveryTimeoutBreach(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        Long orderId = jsonObject.getLong(Constant.ORDER_ID);
        JSONArray orderLines = jsonObject.getJSONArray(Constant.ORDER_LINES);
        Double deliveryRate = jsonObject.getDouble(Constant.SYSTEM_RATE);
        Long orderAmount = jsonObject.getLong(Constant.QUANTITY);
        LocalDate now = LocalDate.now();
        LocalDate lastMonth = now.minusMonths(1);
        int year = lastMonth.getYear();
        int month = lastMonth.getMonthValue();
        StringBuilder sb = new StringBuilder();
        sb.append(buyerId).append("#").append(orderId).append("#").append(System.currentTimeMillis())
                .append("#").append(year);
        if (month > 9) {
            sb.append(month);
        } else {
            sb.append(0).append(month);
        }
        sb.append("#").append(deliveryRate).append("#").append(orderAmount);
        JSONArray deliveryOrderLines = new JSONArray();
        for (int i = 0; i < orderLines.size(); i++) {
            Long orderLineId = orderLines.getJSONObject(i).getLong("tradeOrderLineId");
            JSONObject deliveryOrderLine = new JSONObject();
            deliveryOrderLine.put("tradeOrderLineId", String.valueOf(orderLineId));
            deliveryOrderLine.put("expectDeliveredTime", String.valueOf(System.currentTimeMillis()));
            deliveryOrderLine.put("actualDeliveredTime", String.valueOf(System.currentTimeMillis() + 86400000));
            deliveryOrderLines.add(deliveryOrderLine);
        }
        sb.append("#").append(deliveryOrderLines.toJSONString());
        SendResult sendResult = mqConfig.sendMessage(Constant.OVERSEA_CONSIGN_PUNISH_TOPIC, "delivery_timeout", String.valueOf(orderId), sb.toString());
        result.setSuccess(true);
        result.setData(JSONObject.toJSONString(sendResult));
        result.setMessage(JSONObject.toJSONString(sendResult));
        return result;
    }

    @Override
    public ResultDTO mockTallyOrderBreach(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        String channelType = "全托管".equals(jsonObject.getString(Constant.ORDER_TYPE)) ? "ONE_STOP_SERVICE" : "POP_CHOICE";
        String sellerId = jsonObject.getString(Constant.SELLER_ID);
        String con = jsonObject.getString(Constant.CONSIGN_ORDER_NO);
        String productId = jsonObject.getString(Constant.PRODUCT_IDS);
        JSONObject request = new JSONObject();
        request.put("channelType", channelType);
        request.put("defaultingPartyId", sellerId);
        JSONArray initiators = new JSONArray();
        JSONObject initiator = new JSONObject();
        initiator.put("initiator", productId);
        initiator.put("abnormalType", "LBX_STUCK");
        JSONObject evidence = new JSONObject();
        evidence.put("memo", "test case");
        JSONArray files = new JSONArray();
        JSONObject file = new JSONObject();
        file.put("fileType", "PICTURE");
        file.put("url", "https://ae01.alicdn.com/kf/S75aca424fa3a4cb99781d3f4b6edb8d7N.jpg");
        file.put("fileKey", "S75aca424fa3a4cb99781d3f4b6edb8d7N.jpg");
        file.put("fileName", "微信图片_202501241135534.jpg");
        files.add(file);
        evidence.put("files", files);
        initiator.put("evidence", evidence);
        initiators.add(initiator);
        request.put("initiators", initiators.toJSONString());
        request.put("responsibilityCode", "TALLY-ORDER-APPEAL");
        request.put("targetId", con);
        request.put("targetType", "CONSIGN_ORDER");
        JSONObject res = HsfUtil.applyBreachContracts(request);
        result.setSuccess(true);
        result.setData(res.toJSONString());
        result.setMessage(res.toJSONString());
        return result;
    }

    @Override
    public ResultDTO mockWeightScaleV2Breach(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long sellerId = jsonObject.getLong(Constant.SELLER_ID);
        String orderType = "全托管".equals(jsonObject.getString(Constant.ORDER_TYPE)) ? "AECHOICE" : "AEFF";
        return result;
    }

    @Override
    public ResultDTO mockStarLinkBreach(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        String tagType = jsonObject.getString(Constant.MSG_TAG);
        Long sellerId = jsonObject.getLong(Constant.SELLER_ID);
        Long bidQuantity = jsonObject.getLong(Constant.BID_QTY) == null ? 20L : jsonObject.getLong(Constant.BID_QTY);
        Long saleQty = jsonObject.getLong(Constant.QUANTITY) == null ? 10L : jsonObject.getLong(Constant.QUANTITY);
        Long lossQty = jsonObject.getLong(Constant.LOSS_QTY) == null ? 5 : jsonObject.getLong(Constant.LOSS_QTY);
        String itemId = StringUtils.isEmpty(jsonObject.getString(Constant.PARAM_ITEM_ID)) ? "1005008465051194" : jsonObject.getString(Constant.PARAM_ITEM_ID);
        String skuId = StringUtils.isEmpty(jsonObject.getString(Constant.SKU_ID)) ? "12000045249782986" : jsonObject.getString(Constant.SKU_ID);
        String rate = StringUtils.isEmpty(jsonObject.getString(Constant.SYSTEM_RATE)) ? "0.8" : jsonObject.getString(Constant.SYSTEM_RATE);

        StringBuilder sb = new StringBuilder();
        // 活动id#货品id#爆发货品id#爆发skuId#商品id#skuId#中标数量#活动销量#中标开始时间#中标结束时间#销售损失#商家id#仓名称#仓code#备货期内下单数量#实际送达数量#有货日平均每天出库数量#总出库数量#送满率#缺货天数#备货开始时间#备货结束时间
        sb.append(System.currentTimeMillis()).append("#************#1005007476827021#12000040909703007#").append(itemId).append("#").append(skuId).append("#")
                .append(bidQuantity).append("#").append(saleQty).append("#1737273126000#1737359526000#")
                .append(lossQty).append("#").append(sellerId)
                .append("#东莞测试001仓#DUG001#20#10#2#29#").append(rate)
                .append("#3#*************#*************");
        SendResult sendResult = mqConfig.sendMessage(Constant.STARLINK_BREACH_TOPIC, "试跑链路".equals(tagType) ? Constant.TEST_PATH : "STARLINK_BREACH", sellerId + "_" + itemId, sb.toString());
        result.setSuccess(true);
        result.setData(JSONObject.toJSONString(sendResult));
        result.setMessage(JSONObject.toJSONString(sendResult));
        return result;
    }

    @Override
    public ResultDTO mockSettlementAccountPeriodAggregationMsg(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        String breachId = jsonObject.getString(Constant.BREACH_ID);
        Long sellerId = jsonObject.getLong(Constant.SELLER_ID);
        String country = jsonObject.getString(Constant.COUNTRY_CODE);
        JSONObject msg = new JSONObject();
        msg.put("bizScene", country + "_LOCAL_PERIOD_SETTLE");
        msg.put("bizTime", System.currentTimeMillis());
        JSONObject extendInfo = new JSONObject();
        JSONObject tradePeriodTransparentInfo = new JSONObject();
        tradePeriodTransparentInfo.put("NEW_PERIOD_SETTLE", "true");
        tradePeriodTransparentInfo.put("outBizId", breachId);
        tradePeriodTransparentInfo.put("TRANSFER_MODEL", "WITHHOLD");
        tradePeriodTransparentInfo.put("NEED_CROSS_CURRENCY", "false");
        tradePeriodTransparentInfo.put("PERIOD_SELLER_ID", String.valueOf(sellerId));
        tradePeriodTransparentInfo.put("processByAefce", "true");
        tradePeriodTransparentInfo.put("outUniqueSeq", "OVERSEA_CONSIGN_PUNISH_" + breachId);
        tradePeriodTransparentInfo.put("MIGRATE_USER_ID", String.valueOf(sellerId));
        tradePeriodTransparentInfo.put("outFlowId", "OVERSEA_CONSIGN_PUNISH");
        tradePeriodTransparentInfo.put("PERIOD_SETTLE_SCENE", country + "_LOCAL_PERIOD_SETTLE");
        tradePeriodTransparentInfo.put("ACCOUNT_FLOW_BIZ_TYPE", country + "_LOCAL_DELIVERY_PUNISH");
        tradePeriodTransparentInfo.put("SETTLEMENT_BIZ_OUT_BIZ_CODE", getSettlementBizOutBizCode(country));
        JSONObject memo = new JSONObject();
        memo.put("bizId", "*********");
        memo.put("showCopy", "处罚账期扣款测试");
        tradePeriodTransparentInfo.put("MEMO", memo);
        extendInfo.put("TRADE_PERIOD_TRANSPARENT_INFO", tradePeriodTransparentInfo);
        msg.put("extendInfo", extendInfo);

        JSONArray settleBillInfoList = new JSONArray();
        JSONObject settleBillInfo = new JSONObject();
        JSONObject settleInfo = new JSONObject();
        JSONObject fundManageTransferReq = new JSONObject();
        fundManageTransferReq.put("bizSceneType", country + "_LOCAL_DELIVERY_PUNISH");
        fundManageTransferReq.put("fundManageFunctionType", "WITHHOLD");
        fundManageTransferReq.put("outBizId", breachId);
        fundManageTransferReq.put("outSubBizId", "0");
        fundManageTransferReq.put("routeId", sellerId);
        settleInfo.put("FUND_MANAGE_TRANSFER_REQUEST", fundManageTransferReq);
        settleBillInfo.put("settleInfo", settleInfo);
        settleBillInfo.put("totalNum", 1);
        settleBillInfo.put("tradeId", EagleEye.getTraceId());
        settleBillInfoList.add(settleBillInfo);
        msg.put("settleBillInfoList", settleBillInfoList);
        msg.put("userId", sellerId);
        SendResult sendResult = mqConfig.sendMessage("ae_settle_center_topic", "settle_bill_accum_complete_tag", breachId, msg.toJSONString());
        resultDTO.setSuccess(true);
        resultDTO.setData(JSONObject.toJSONString(sendResult));
        resultDTO.setMessage(JSONObject.toJSONString(sendResult));
        return resultDTO;
    }

    @Override
    public ResultDTO batchQueryQualifiedTime(String params, SystemDTO systemDTO) throws Exception {

        ResultDTO result = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Boolean customizeInfoFlag = jsonObject.getBoolean("customizeInfoFlag");
        Long itemId = jsonObject.getLong("itemId");
        String orderType = getOrderType(jsonObject.getString("orderType"));
        Long skuId = jsonObject.getLong("skuId");
        Long startTime = jsonObject.getLong("startTime");
        Long tradeOrderId = jsonObject.getLong(Constant.ORDER_ID);
        JSONObject request = new JSONObject();
        Map<String, String> extras = new HashMap<>();
        if(customizeInfoFlag){
            extras.put("customizeInfoFlag", "1");
            extras.put("customizeInfoId", "800003079150677");
        }else {
            extras.put("customizeInfoFlag", "0");
        }
        /*request.put("extras", extras);
        request.put("orderType", orderType);
        request.put("itemId", itemId);
        request.put("skuId", skuId);
        request.put("startTime", startTime);
        request.put("tradeOrderId", tradeOrderId);
        JSONObject res = HsfUtil.batchQueryQualifiedTime(request);*/
        QualifiedTimeRequest qualifiedTimeRequest = new QualifiedTimeRequest();
        qualifiedTimeRequest.setExtras(extras);
        qualifiedTimeRequest.setStartTime(startTime);
        qualifiedTimeRequest.setItemId(itemId);
        qualifiedTimeRequest.setOrderType(orderType);
        qualifiedTimeRequest.setSkuId(skuId);
        qualifiedTimeRequest.setTradeOrderId(tradeOrderId);
        List<QualifiedTimeRequest> qualifiedTimeRequests = new ArrayList<>();
        qualifiedTimeRequests.add(qualifiedTimeRequest);
        Response<List<QualifiedTimeResponse>> listResponse = timeoutCalculateFacade.batchQueryQualifiedTime(qualifiedTimeRequests);
        if(listResponse.isSuccess()){
            result.setSuccess(true);
            result.setData(listResponse.getData().toString());
            result.setMessage(listResponse.getData().toString());
            return result;
        }
        result.setSuccess(false);
        result.setData(listResponse.getErrorMessage());
        result.setMessage(listResponse.getErrorMessage());
        return result;
    }

    private String getSettlementBizOutBizCode(String country) {
        switch (country) {
            case "US":
                return "ali.global.oversea.delivery.punish.uslocal";
            case "MX":
                return "ali.global.oversea.delivery.punish.mxlocal";
            case "UK":
                return "ali.global.oversea.delivery.punish.gblocal";
        }
        return "";
    }

    private JSONObject getJitCalculatePenaltyType(String type, String poStatus, String deliveryType, Long poGmtCreate, Long aimTimeStamp) {
        JSONObject request = new JSONObject();
        request.put("shippingMode", 1);
        request.put("gmtPoCancel", 0);
        request.put("gmtPickup", 0);
        request.put("gmtArrivalRegister", 0);
        request.put("tradeOrderId", 0);
        request.put("fulfillmentOrderNo", "");
        request.put("sellerId", "");
        request.put("inboardOrderNo", "");
        request.put("useInboardOrderId", true);
        request.put("receivedNormalQuantity", 0);
        request.put("gmtPoCreate", poGmtCreate);
        if ("待上架".equals(poStatus)) {
            request.put("skuStatus", "JIT_OFF_SHELF");
        } else {
            request.put("skuStatus", "JIT_ON_SHELF");
        }
        switch (poStatus) {
            case "已取消":
                request.put("poStatus", -99);
                request.put("gmtPoCancel", aimTimeStamp);
                break;
            case "待接单":
                request.put("poStatus", -1);
                break;
            default:
                request.put("poStatus", 40);
        }
        if ("自寄".equals(deliveryType)) {
            request.put("shippingMode", 1);
            request.put("gmtArrivalRegister", aimTimeStamp);
        } else if ("揽收".equals(deliveryType)) {
            request.put("shippingMode", 2);
            request.put("gmtPickup", aimTimeStamp);
        }
        if ("全托管".equals(type)) {
            request.put("jitWarehouseType", "");
            request.put("semiJit", false);
        } else {
            request.put("jitWarehouseType", "semi_choice_not_wh");
            request.put("semiJit", true);
        }
        return request;
    }

    private JSONObject getCCOJudgeRequest(Long breachContractId, String responsibleParty, String type) {
        JSONObject request = new JSONObject();
        request.put("breachContractId", breachContractId);
        JSONObject defaultingParty = new JSONObject();
        defaultingParty.put("role", "商家责".equals(responsibleParty) ? "Seller" : "Relief");
        defaultingParty.put("shouldHeldResponsible", true);
        request.put("defaultingParty", defaultingParty);
        request.put("idempotentKey", "486bd834825f5f5275c16d9e15cb37fd");
        request.put("judgeMemo", "qa test");
        request.put("judgeSolution", "终判".equals(type) ? "FinalJudge" : "PreJudge");
        return request;
    }

    private JSONObject getCroCashOutPunish(String tradeOrderLineId, Long sellerId, Long amount, String currencyCode) {
        JSONObject request = new JSONObject();
        String bizId = String.valueOf(System.currentTimeMillis());
        request.put("breachLevel", "BREACHED");
        JSONObject memoFeatures = new JSONObject();
        memoFeatures.put("bizId", bizId);
        request.put("memoFeatures", memoFeatures);
        request.put("initiatorCode", "cro");
        request.put("initiatorId", bizId);
        request.put("responsibilityCode", "CRO-CASHOUT-PUNISH");
        JSONObject penaltyUser = new JSONObject();
        penaltyUser.put("userId", sellerId);
        request.put("penaltyUser", penaltyUser);
        request.put("identity", "punishcenter");
        request.put("penaltyType", "CRO_PUNISH");
        JSONArray createPenaltyParams = new JSONArray();
        JSONObject createPenaltyParam = new JSONObject();
        createPenaltyParam.put("penaltyCode", "CRO_PUNISH");
        createPenaltyParam.put("penaltyCostType", 1);
        createPenaltyParam.put("penaltyCost", amount);
        createPenaltyParam.put("currencyCode", currencyCode);
        createPenaltyParam.put("memo", "qa test");
        createPenaltyParams.add(createPenaltyParam);
        request.put("createPenaltyParams", createPenaltyParams);
        request.put("targetType", "AE_ORDER");
        request.put("targetId", tradeOrderLineId);
        return request;
    }

    private JSONObject getSettlementTransferMessage(Long breachContractId, Long sellerId, Long amount, String msgTag,
                                                    String bizCode, String billCode) {
        JSONObject msg = new JSONObject();
        Long now = System.currentTimeMillis();
        String bizScene = getBizScene(bizCode);
        msg.put("bizScene", bizScene);
        msg.put("bizTime", now);
        msg.put("currency", "USD");
        if ("全部转账".equals(msgTag)) {
            msg.put("finishTime", now);
        }
        msg.put("outBizId", "5846580143032360");
        msg.put("outUniqueSeq", "5846580143032360");
        msg.put("siteId", "AEG");
        msg.put("status", "部分扣款".equals(msgTag) ? "WAIT" : "SUCCESS");
        msg.put("userId", sellerId);
        msg.put("transferredAmount", amount);
        msg.put("amount", amount);
        JSONObject extendInfo = new JSONObject();
        extendInfo.put("NEW_PERIOD_SETTLE", "false");
        extendInfo.put("outBizId", "************");
        extendInfo.put("MIGRATE_USER_ID", String.valueOf(sellerId));
        extendInfo.put("outFlowId", billCode);
        extendInfo.put("TRANSFER_MODEL", "WITHHOLD");
        extendInfo.put("ACCOUNT_FLOW_BIZ_TYPE", bizScene);
        extendInfo.put("SETTLEMENT_BIZ_OUT_BIZ_CODE", bizCode);
        extendInfo.put("NEED_CROSS_CURRENCY", "false");
        extendInfo.put("processByAefce", "true");
        extendInfo.put("outUniqueSeq", billCode + "_" + breachContractId);
        JSONObject memo = new JSONObject();
        memo.put("bizId", "违规编号-" + breachContractId);
        extendInfo.put("MEMO", memo);
        msg.put("extendInfo", extendInfo);
        return msg;
    }


    private String getBizScene(String bizScene) {
        if (StringUtils.isEmpty(bizScene)) {
            return "xx.xx.xx.xx";
        }
        switch (bizScene) {
            case "pop.cro.compliance.punish":
                return "POP_CRO_COMPLIANCE_PUNISH";
            case "os.cro.compliance.punish":
                return "OS_CRO_COMPLIANCE_PUNISH";
            case "one.stop.outbreak.punish":
                return "one.stop.outbreak.punish";
        }
        return "xx.xx.xx.xx";
    }

    private JSONObject getCroComplianceBreachRequest(Long sellerId, Long amount, String currency, String type) {
        JSONObject request = new JSONObject();
        String now = String.valueOf(System.currentTimeMillis());
        request.put("breachLevel", "BREACHED");
        request.put("identity", "punishcenter");
        request.put("responsibilityCode", "CRO-COMPLIANCE-PUNISH");
        request.put("penaltyType", "CRO_COMPLIANCE_PUNISH");
        request.put("initiatorCode", "cro");
        request.put("initiatorId", now);
        request.put("targetType", "CRO_ORDER");
        request.put("targetId", now);
        JSONObject memoFeatures = new JSONObject();
        memoFeatures.put("bizId", Long.valueOf(now));
        memoFeatures.put("sellerType", type);
        request.put("memoFeatures", memoFeatures);
        JSONObject penaltyUser = new JSONObject();
        penaltyUser.put("userId", sellerId);
        request.put("penaltyUser", penaltyUser);
        JSONArray createPenaltyParams = new JSONArray();
        JSONObject createPenaltyParam = new JSONObject();
        createPenaltyParam.put("penaltyCostType", 1);
        createPenaltyParam.put("penaltyCost", amount);
        createPenaltyParam.put("penaltyCode", "CRO_COMPLIANCE_PUNISH");
        createPenaltyParam.put("currencyCode", currency);
        createPenaltyParams.add(createPenaltyParam);
        request.put("createPenaltyParams", createPenaltyParams);
        return request;
    }


    private String getdWeightScaleRecoverOrderType(String orderType) {
        switch (orderType) {
            case "全托管":
                return "AECHOICE";
            case "半托管":
                return "AEFF";
        }
        return "";
    }

    //jit全半托租户类型
    private String getTenantByOrderType(String orderType) {
        switch (orderType) {
            case "全托管":
                return "CSAECHOICE";
            case "半托管":
                return "CSAEFF";
        }
        return "";
    }


    private String getOrderType(String orderType) {
        switch (orderType) {
            case "POP本地":
                return "POP_LOCAL";
            case "POP跨境海外仓":
                return "POP_OVERSEA_CONSIGN";
            case "跨境海外托管":
                return "LOCAL_SERVICE_CROSS";
            case "本地海外托管（海外本地托管）":
                return "LOCAL_SERVICE_LOCAL";
            case "全托管":
                return "CHOICE";
            case "半托管":
                return "SEMI_CHOICE";
        }
        return "";
    }

    private String getRefundRateMessageTag(String type) {
        switch (type) {
            case "试跑链路":
                return Constant.TEST_PATH;
            case "真实链路":
                return "REFUND_RATE";
        }
        return "";
    }


    @Override
    public ResultDTO mockPopOnlinePunishExempt(String params, SystemDTO systemDTO) throws Exception {
        return null;
    }

    private String getOrderMessageType(String jitOrderType) {
        switch (jitOrderType) {
            case "全托管":
                return Constant.BREACH_FTP_JIT_TIMEOUT_TOPIC;
            case "半托管":
                return Constant.BREACH_STP_JIT_TIMEOUT_TOPIC;
        }
        return "";
    }


}
