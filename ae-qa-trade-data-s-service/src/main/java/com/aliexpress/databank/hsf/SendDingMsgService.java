package com.aliexpress.databank.hsf;

import com.alibaba.global.qa.dc.model.param.SystemDTO;
import com.alibaba.global.qa.dc.model.result.ResultDTO;
import com.dingtalk.api.response.OapiRobotSendResponse;

import java.util.List;

/**
 * Created by limeng on 2021/12/1.
 */
public interface SendDingMsgService {

    Boolean sendDingMessage(String appName, String content, String url, List<String> reciverList);

    Boolean sendDingMessage(String msg, List<String> reciverList);

    ResultDTO TestSendDingMsg(String params, SystemDTO systemDTO) throws Exception;

    OapiRobotSendResponse sendDingTalkGroup(String msg);

	String sendMetaq(String topic, String messageBody, String messageTag, String envType, String ip, String buyerId, String keys, String dPathEnv, String aepay);


    ResultDTO SendMQMsg(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO SendMQMsghpy(String params) throws Exception;
    }

