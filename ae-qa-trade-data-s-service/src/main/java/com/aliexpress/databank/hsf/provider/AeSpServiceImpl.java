package com.aliexpress.databank.hsf.provider;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.ecommerce.module.Response;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.global.carts.api.request.CartQueryRequest;
import com.alibaba.global.carts.api.response.dto.CartDetailResult;
import com.alibaba.global.qa.dc.model.param.SystemDTO;
import com.alibaba.global.qa.dc.model.result.ResultDTO;
import com.alibaba.intl.ae.biz.promisetemplate.share.dto.result.SpResult;
import com.aliexpress.databank.constant.Constant;
import com.aliexpress.databank.hsf.AeSpService;
import com.taobao.hsf.app.api.util.HSFApiConsumerBean;
import com.taobao.hsf.remoting.service.GenericService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

//import com.alibaba.intl.ae.biz.promisetemplate.service.PromiseToolsService;
import javax.annotation.PostConstruct;
import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * <AUTHOR>
 * @date 2025/2/10
 */

@Slf4j
@HSFProvider(serviceInterface = AeSpService.class,serviceGroup = "HSF", serviceVersion = "1.0.0", clientTimeout = 20000)
public class AeSpServiceImpl implements AeSpService {
    private static GenericService promiseToolsService;

    //泛化调用PromiseToolsService
    @PostConstruct
    public void init() throws Exception {
        HSFApiConsumerBean hsfApiConsumerBean = new HSFApiConsumerBean();
        hsfApiConsumerBean.setInterfaceName("com.alibaba.intl.ae.biz.promisetemplate.service.PromiseToolsService");
        hsfApiConsumerBean.setVersion("1.0.0");
        hsfApiConsumerBean.setGroup("HSF");
        hsfApiConsumerBean.setGeneric("true");
        hsfApiConsumerBean.init(true);
        promiseToolsService = (GenericService) hsfApiConsumerBean.getObject();
    }

    @Override
    public ResultDTO queryPromiseBySellerId(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        log.warn("queryPromiseBySellerId params {}", params);
        JSONObject jsonObject = JSONObject.parseObject(params);
        long sellerId = jsonObject.getLong(Constant.SELLER_ID);
        String methodName = "queryPromiseToSeller";
        String[] parameterType = {"java.lang.Long"};
        SpResult<Object> result = (SpResult<Object>) promiseToolsService.$invoke(methodName, parameterType, new Object[]{sellerId});
        log.error("queryPromiseToSeller:" + JSONObject.toJSONString(result));
        resultDTO.setSuccess(result.isSuccess());
        log.warn("promiseToolsService's data", result.getData().toString());
        resultDTO.setData(result.getData().toString());
        return resultDTO;
    }
}
