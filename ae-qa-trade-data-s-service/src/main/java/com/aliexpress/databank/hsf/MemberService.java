package com.aliexpress.databank.hsf;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.global.qa.dc.model.param.SystemDTO;
import com.alibaba.global.qa.dc.model.result.ResultDTO;

public interface MemberService {

    ResultDTO getUserByHavanaId(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO findByAccountId(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO findAccount(String params, SystemDTO systemDTO) throws Exception;

    JSONObject getAccountByStr(String str) throws Exception;

    ResultDTO checkUser(String params, SystemDTO systemDTO) throws  Exception;

    ResultDTO updateUserDataTag(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO deleteUserDataTag(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO getSid(String params, SystemDTO systemDTO) throws Exception;
}