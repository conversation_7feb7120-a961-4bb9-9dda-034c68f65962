package com.aliexpress.databank.hsf.multiTask;

import com.alibaba.fpark.bings.executor.RejectHandler;
import com.alibaba.fpark.bings.executor.ScheduleHandler;
import com.alibaba.fpark.bings.executor.StandardThreadExecutor;
import com.alibaba.fpark.bings.executor.StandardThreadFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import java.util.concurrent.Callable;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class NewMultiExecutorGroup implements InitializingBean {

    public static final int BUSY_CORE_SIZE = 5;
    public static final int BUSY_MAX_SIZE = 500;
    public static final int BUSY_KEEP_ALIVE_SECONDS = 20;
    public static final int BUSY_QUEUE_CAPACITY = 2000;

    public static final int DEFAULT_CORE_SIZE = 5;
    public static final int DEFAULT_MAX_SIZE = 500;
    public static final int DEFAULT_KEEP_ALIVE_SECONDS = 20;
    public static final int DEFAULT_QUEUE_CAPACITY = 500;

    /**
     * 核心线程池，用于主链路
     */
    private static StandardThreadExecutor threadPoolExecutor;

    /**
     * 用于旁路、job，如：异步写日志，听q进行io任务，定时刷新缓存等
     */
    private static StandardThreadExecutor busyPoolExecutor;

    @Override
    public void afterPropertiesSet() throws Exception {
        initialize();
    }

    public <V> V call(Callable<V> callable) throws Exception {
        Future<V> future = threadPoolExecutor.submit(callable);
        return future.get();
    }

    public <V> V call(Callable<V> callable, long timeout, TimeUnit unit) throws Exception {
        Future<V> future = threadPoolExecutor.submit(callable);
        return future.get(timeout, unit);
    }

    public void enqueue(Runnable task) {
        threadPoolExecutor.submit(task);
        log.info("; MultiExecutorGroup; registerStatisticCallback; threadPoolExecutor线程池：{}", threadPoolExecutor.toString());
    }

    public <V> V callBusy(Callable<V> callable) throws Exception {
        Future<V> future = busyPoolExecutor.submit(callable);
        return future.get();
    }

    public <V> V callBusy(Callable<V> callable, long timeout, TimeUnit unit) throws Exception {
        Future<V> future = busyPoolExecutor.submit(callable);
        return future.get(timeout, unit);
    }

    public void enBusyQueue(Runnable task) {
        busyPoolExecutor.submit(task);
        log.info("; MultiExecutorGroup; registerStatisticCallback; busyPoolExecutor线程池：{}", busyPoolExecutor.toString());
    }

    public StandardThreadExecutor getStandardPool() {
        return threadPoolExecutor;
    }

    public StandardThreadExecutor getBusyStandardPool() {
        return busyPoolExecutor;
    }

    private void registerStatisticCallback() {
        ScheduleHandler.registerReactorCallback(() -> {
            try {
                log.info("; MultiExecutorGroup; registerStatisticCallback; threadPoolExecutor线程池：{}", threadPoolExecutor.toString());
                log.info("; MultiExecutorGroup; registerStatisticCallback; busyPoolExecutor线程池：{}", busyPoolExecutor.toString());
            } catch (Exception e) {
                log.error("; MultiExecutorGroup; 定时统计线程池信息时出现异常, 忽略; ", e);
            }
        });
    }

    private void initialize() {
        threadPoolExecutor = new StandardThreadExecutor(DEFAULT_CORE_SIZE,
                DEFAULT_MAX_SIZE, DEFAULT_KEEP_ALIVE_SECONDS,
                TimeUnit.SECONDS, DEFAULT_QUEUE_CAPACITY,
                new StandardThreadFactory(NewMultiExecutorGroup.class.getSimpleName() + "-default"),
                new RejectHandler());
        busyPoolExecutor = new StandardThreadExecutor(BUSY_CORE_SIZE,
                BUSY_MAX_SIZE, BUSY_KEEP_ALIVE_SECONDS,
                TimeUnit.SECONDS, BUSY_QUEUE_CAPACITY,
                new StandardThreadFactory(NewMultiExecutorGroup.class.getSimpleName() + "-busy"),
                new RejectHandler());

        threadPoolExecutor.prestartAllCoreThreads();
        busyPoolExecutor.prestartAllCoreThreads();

//		registerStatisticCallback();

        addShutdownHook();
    }



    private void addShutdownHook() {
        Runtime.getRuntime().addShutdownHook(new Thread() {
            @Override
            public void run() {
                shutdown();
            }
        });
    }

    private void shutdown() {
        log.info("; MultiExecutorGroup; JVM正在进行关闭; MultiExecutorGroup详情：default={}, busy={}", threadPoolExecutor, busyPoolExecutor);
        try {
            Thread.sleep(2000);//wait for hsf unregister
            threadPoolExecutor.shutdown();
            busyPoolExecutor.shutdown();
        } catch (InterruptedException e) {
            log.error("; MultiExecutorGroup; 关闭线程池时出现异常; ", e);
        } finally {
            log.info("; MultiExecutorGroup; 线程池已经关闭, 即将关闭JVM; ");
        }
    }

}