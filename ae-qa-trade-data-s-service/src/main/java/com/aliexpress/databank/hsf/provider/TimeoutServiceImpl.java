package com.aliexpress.databank.hsf.provider;

import com.alibaba.ae.service.open.facade.InternalToolsFacade;
import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.common.lang.StringUtil;
import com.alibaba.ecommerce.module.Response;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.global.landlord.LandlordContext;
import com.alibaba.global.order.management.api.facade.OrderQueryForBuyerFacade;
import com.alibaba.global.order.management.api.model.TradeOrderDTO;
import com.alibaba.global.qa.dc.model.builder.QueryResultBuilder;
import com.alibaba.global.qa.dc.model.param.SystemDTO;
import com.alibaba.global.qa.dc.model.result.QueryResultUnit;
import com.alibaba.global.qa.dc.model.result.ResultDTO;
import com.alibaba.global.reverse.api.model.ReverseOrderLineDTO;
import com.alibaba.global.timeout.dto.AlarmTaskDTO;
import com.alibaba.global.timeout.dto.AlarmUpdateTaskDTO;
import com.alibaba.global.timeout.facade.IAlarmTaskReadFacade;
import com.alibaba.global.timeout.facade.IAlarmTaskWriteFacade;
import com.alibaba.global.timeout.service.enums.AlarmTaskStatusEnum;
import com.aliexpress.databank.constant.Constant;
import com.aliexpress.databank.constant.ReverseAlarmNameOfToc;
import com.aliexpress.databank.dataobject.ResultVo;
import com.aliexpress.databank.hsf.DataPoolService;
import com.aliexpress.databank.hsf.TimeoutService;
import com.aliexpress.databank.service.ReverseService;
import com.aliexpress.databank.utils.HsfUtil;
import com.aliexpress.databank.utils.ReverseTimeoutUtil;
import com.taobao.eagleeye.EagleEye;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@HSFProvider(serviceInterface = TimeoutService.class, serviceGroup = "HSF", clientTimeout = 20000)
public class TimeoutServiceImpl implements TimeoutService {

    @Autowired
    private IAlarmTaskReadFacade alarmTaskReadFacade;

    @Autowired
    private IAlarmTaskWriteFacade alarmTaskWriteFacade;

    @Autowired
    private ReverseService reverseService;


    @Autowired
    private DataPoolService dataPoolService;


    @Autowired
    private OrderQueryForBuyerFacade orderQueryForBuyerFacade;

    @Autowired
    private InternalToolsFacade internalToolsFacade;

    private static final Logger logger = LoggerFactory.getLogger(BreachServiceImpl.class);



    @Override
    public ResultDTO getTimeoutByOrderIdAndEventType(String params, SystemDTO systemDTO) {
        ResultDTO resultDTO = new ResultDTO();
        try {
            JSONObject jsonObject = JSONObject.parseObject(params);
            String orderId = jsonObject.getString(Constant.ORDER_ID);
            Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
            String alarmName = jsonObject.getString(Constant.TIMEOUT_ALARM_NAME);
            //查询多租户接口
            LandlordContext.setTenantSpec("AE_GLOBAL", -1);

            if (StringUtil.isNotBlank(alarmName) && !alarmName.trim().equalsIgnoreCase("null")) {
                if (Constant.WAIT_SELLER_HANDLE_CANCEL_TIMEOUT.equalsIgnoreCase(alarmName)) {
                    Map<String, QueryResultUnit> data = getCancelOrderAlarmTimeoutVo(alarmName, orderId, buyerId);
                    resultDTO.setSuccess(true);
                    resultDTO.setData(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
                } else {
                    Response<AlarmTaskDTO> alarmTaskResponse = alarmTaskReadFacade.getAlarmTaskDTO(alarmName, orderId);
                    Map<String, QueryResultUnit> data = QueryResultBuilder.buildQueryResult(alarmName + "超时信息", null, null, alarmTaskResponse.getModule());
                    resultDTO.setSuccess(alarmTaskResponse.isSuccess());
                    resultDTO.setData(alarmTaskResponse.isSuccess() ? JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat)
                            : JSON.toJSONString(JSON.toJSONString(alarmTaskResponse.getErrorCode())));
                }
            } else {
                Map<String, QueryResultUnit> data = new LinkedHashMap<>();

                // 支付超时
                Map<String, QueryResultUnit> payTimeout = getAlarmTimeoutVo(Constant.PAY_TIMEOUT_TYPE, orderId);
                // 发货超时
                Map<String, QueryResultUnit> shipTimeout = getAlarmTimeoutVo(Constant.SHIP_TIMEOUT_TYPE, orderId);
                // 确认收货超时
                Map<String, QueryResultUnit> succeedTimeout = getAlarmTimeoutVo(Constant.SUCCEED_TIMEOUT_TYPE, orderId);

                // 支付超时提醒 - 1天
                Map<String, QueryResultUnit> reminderPay1DayTimeout = getAlarmTimeoutVo(Constant.PAY_REMINDER_TIMEOUT_TYPE, orderId.trim() + "_86400");
                // 支付超时提醒 - 3天
                Map<String, QueryResultUnit> reminderPay3DaysTimeout = getAlarmTimeoutVo(Constant.PAY_REMINDER_TIMEOUT_TYPE, orderId.trim() + "_259200");

                Map<String, QueryResultUnit> reminderPay5DaysTimeout = getAlarmTimeoutVo(Constant.PAY_REMINDER_TIMEOUT_TYPE, orderId.trim() + "_432000");

                // 发货超时提醒 - 1天
                Map<String, QueryResultUnit> reminderShip1DayTimeout = getAlarmTimeoutVo(Constant.SHIP_REMINDER_TIMEOUT_TYPE, orderId.trim() + "_86400");
                // 发货超时提醒 - 3天
                Map<String, QueryResultUnit> reminderShip3DaysTimeout = getAlarmTimeoutVo(Constant.SHIP_REMINDER_TIMEOUT_TYPE, orderId.trim() + "_259200");
                // 发货超时提醒 - 5天
                Map<String, QueryResultUnit> reminderShip5DayTimeout = getAlarmTimeoutVo(Constant.SHIP_REMINDER_TIMEOUT_TYPE, orderId.trim() + "_432000");

                // 收货超时提醒 - 1天
                Map<String, QueryResultUnit> reminderSucceed1DayTimeout = getAlarmTimeoutVo(Constant.SUCCEED_REMINDER_TIMEOUT_TYPE, orderId.trim() + "_86400");
                // 收货超时提醒 - 3天
                Map<String, QueryResultUnit> reminderSucceed3DaysTimeout = getAlarmTimeoutVo(Constant.SUCCEED_REMINDER_TIMEOUT_TYPE, orderId.trim() + "_259200");
                // 收货超时提醒 - 5天
                Map<String, QueryResultUnit> reminderSucceed5DayTimeout = getAlarmTimeoutVo(Constant.SUCCEED_REMINDER_TIMEOUT_TYPE, orderId.trim() + "_432000");


                // 卖家同意/拒绝取消订单超时
                Map<String, QueryResultUnit> reminderSellerCancelOrderTimeouts = getCancelOrderAlarmTimeoutVo(Constant.SELLER_CANCEL_ORDER_TIMEOUT_TYPE, orderId.trim(), buyerId);

                data.putAll(payTimeout);
                data.putAll(reminderPay1DayTimeout);
                data.putAll(reminderPay3DaysTimeout);
                data.putAll(reminderPay5DaysTimeout);

                data.putAll(shipTimeout);
                data.putAll(reminderShip1DayTimeout);
                data.putAll(reminderShip3DaysTimeout);
                data.putAll(reminderShip5DayTimeout);

                data.putAll(succeedTimeout);
                data.putAll(reminderSucceed1DayTimeout);
                data.putAll(reminderSucceed3DaysTimeout);
                data.putAll(reminderSucceed5DayTimeout);
                data.putAll(reminderSellerCancelOrderTimeouts);

                resultDTO.setSuccess(true);
                resultDTO.setData(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));

            }

        } catch (IllegalArgumentException e) {
            resultDTO.setSuccess(false);
            resultDTO.setMessage(e.getMessage());
            return resultDTO;
        } catch (Exception e) {
            e.printStackTrace();
            resultDTO.setSuccess(false);
            resultDTO.setMessage(e.getMessage());
            return resultDTO;
        }

        return resultDTO;
    }

    @Override
    public ResultDTO getReverseTimeoutByOrderIdAndEventType(String params, SystemDTO systemDTO) {
        ResultDTO resultDTO = new ResultDTO();
        try {
            JSONObject jsonObject = JSONObject.parseObject(params);
            String orderId = jsonObject.getString(Constant.ORDER_ID);
            Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
            String alarmName = getTimeoutAlarmName(jsonObject.getString(Constant.TIMEOUT_ALARM_NAME));
            if (alarmName.isEmpty()) {
                resultDTO.setSuccess(true);
                JSONObject data = new JSONObject();
                data.put("Error Message", "未找到对应的超时任务");
                resultDTO.setData(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
            }
            Map<String, QueryResultUnit> data = new LinkedHashMap<>();
            if (Constant.WAIT_SELLER_HANDLE_CANCEL_TIMEOUT.equalsIgnoreCase(alarmName)) {
                Map<String, QueryResultUnit> cancelOrderAlarmTimeoutVo = getCancelOrderAlarmTimeoutVo(alarmName, orderId, buyerId);
                data.putAll(cancelOrderAlarmTimeoutVo);
            } else if (Constant.DELIVERY_ON_TIME_GUARANTEE_TIMEOUT_TYPE.equalsIgnoreCase(alarmName) || Constant.X_DAY_ON_TIME_GUARANTEE_TIMEOUT_TYPE.equalsIgnoreCase(alarmName) || Constant.SELLER_SUBMIT_EVIDENCE_TIMEOUT_TYPE.equalsIgnoreCase(alarmName)) {
                // 子订单id 获取
                JSONArray orderLines = jsonObject.getJSONArray(Constant.ORDER_LINES);
                List<Long> tradeOrderLineIds = Lists.newArrayList();
                for (int i = 0; i < orderLines.size(); i++) {
                    JSONObject orderLine = orderLines.getJSONObject(i);
                    tradeOrderLineIds.add(orderLine.getLong("tradeOrderLineId"));
                }
                tradeOrderLineIds.forEach(tradeOrderLineId -> {
                    LandlordContext.setTenantSpec("AE_GLOBAL", -1);
                    Response<AlarmTaskDTO> alarmTaskDTOResponse = alarmTaskReadFacade.getAlarmTaskDTO(alarmName, String.valueOf(tradeOrderLineId));
                    if (alarmTaskDTOResponse.isSuccess() && alarmTaskDTOResponse.getModule() != null) {
                        try {
                            Map<String, QueryResultUnit> response = getAlarmTimeoutVo(alarmName, String.valueOf(tradeOrderLineId));
                            data.putAll(response);
                        } catch (Exception e) {
                            e.printStackTrace();
                            log.error("Exception in getting alarm. alarmName: " + alarmName + ". subOrderId: " + tradeOrderLineId);
                        }
                    } else {
                        if (alarmTaskDTOResponse.isNotSuccess()) {
                            log.error("Fail to get alarm. alarmName: " + alarmName + ". subOrderId: " + tradeOrderLineId);
                            log.error(JSONObject.toJSONString(alarmTaskDTOResponse));
                        } else if (alarmTaskDTOResponse.getModule() == null) {
                            log.error("task is not exist. pls check shipping env");
                        }
                    }
                });
            } else {
                JSONObject reverseOrderLinesResponses = HsfUtil.getReverseOrderLinesByOrderId(buyerId, Long.parseLong(orderId));
                JSONArray results = reverseOrderLinesResponses.getJSONArray("result");
                data.putAll(getReverseTimeouts(alarmName, results));
            }
            resultDTO.setSuccess(true);
            resultDTO.setData(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
        } catch (IllegalArgumentException e) {
            resultDTO.setSuccess(false);
            resultDTO.setMessage(e.getMessage());
            return resultDTO;
        } catch (Exception e) {
            e.printStackTrace();
            resultDTO.setSuccess(false);
            resultDTO.setMessage(e.getMessage());
            return resultDTO;
        }
        return resultDTO;
    }

    private String getTimeoutAlarmName(String alarmName) {
        switch (alarmName) {
            case "普通协商超时":
                return "WAIT_SOLUTION_TIMEOUT";
            case "卖家填写退货地址超时":
                return "WAIT_SELLER_FILL_ADDRESS_TIMEOUT";
            case "买家退货超时提醒":
                return "WAIT_BUYER_RETURN_ITEM_WILL_TIMEOUT";
            case "买家退货超时":
                return "WAIT_BUYER_RETURN_ITEM_TIMEOUT";
            case "卖家确认收货超时提醒":
                return "WAIT_RECEIVE_ITEM_WILL_TIMEOUT";
            case "卖家确认收货超时":
                return "WAIT_RECEIVE_ITEM_TIMEOUT";
            case "X日达晚必赔超时":
                return Constant.X_DAY_ON_TIME_GUARANTEE_TIMEOUT_TYPE;
            case "线路晚必赔超时":
                return Constant.DELIVERY_ON_TIME_GUARANTEE_TIMEOUT_TYPE;
        }
        return "";
    }

    @Override
    public Map<String, QueryResultUnit> getReverseTimeouts(String alarmName, JSONArray results) {
        Map<String, QueryResultUnit> data = new LinkedHashMap<>();
        for (int i = 0; i < results.size(); i++) {
            data.putAll(getReverseTimeouts(alarmName, results.getJSONObject(i)));
        }
        return data;
    }

    @Override
    public Map<String, QueryResultUnit> getReverseTimeouts(String alarmNameOfAim, JSONObject result) {
        Map<String, QueryResultUnit> data = new LinkedHashMap<>();
        if (result.getJSONObject("features") != null && result.getJSONObject("features").getJSONArray("timeouts") != null) {
            JSONArray timeouts = result.getJSONObject("features").getJSONArray("timeouts");
            for (int j = 0; j < timeouts.size(); j++) {
                JSONObject timeout = timeouts.getJSONObject(j);
                String bizId = combineAftersalesToBizId(result.getLong("reverseOrderLineId"), timeout);
                String alarmName = timeout.getString("timeoutType");
                if (StringUtil.isNotBlank(alarmNameOfAim)) {
                    if (!alarmName.equals(alarmNameOfAim)) {
                        continue;
                    }
                }
                Response<AlarmTaskDTO> alarmTaskResponse = alarmTaskReadFacade.getAlarmTaskDTO("CATHEDRAL_GLOBAL_REVERSE_TIMEOUT", bizId);
                if (alarmTaskResponse.isSuccess() && alarmTaskResponse.getModule() != null) {
                    try {
                        Map<String, QueryResultUnit> timeoutRes = QueryResultBuilder.buildQueryResult(ReverseAlarmNameOfToc.getReverseAlarmNameOfToc(alarmName).getName() + " -- " + result.getString("tradeOrderLineId") + ":" + bizId, null, null, alarmTaskResponse.getModule());
                        data.putAll(timeoutRes);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
        }
        return data;
    }

    private String combineAftersalesToBizId(Long reverseOrderLineId, JSONObject timeout) {
        StringBuilder sb = new StringBuilder();
        sb.append(reverseOrderLineId).append("-").append(timeout.getString("timeoutType")).append("-").append(timeout.getString("id"));
        return sb.toString();
    }

    private Map<String, QueryResultUnit> getCancelOrderAlarmTimeoutVo(String alarmName, String orderId, Long buyerId) throws Exception {
        List<Long> reverseOrderLineIds = getReverseOrderLineIds(buyerId, orderId);
        List<AlarmTaskDTO> alarmTaskResponses = new ArrayList<>();
        for (Long reverseOrderLineId : reverseOrderLineIds) {
            LandlordContext.setTenantSpec("AE_GLOBAL", -1);
            Response<AlarmTaskDTO> alarmTaskResponse = alarmTaskReadFacade.getAlarmTaskDTO(alarmName, String.valueOf(reverseOrderLineId));
            alarmTaskResponses.add(alarmTaskResponse.getModule());
        }
        return QueryResultBuilder.buildQueryResult(alarmName + orderId, null, null, alarmTaskResponses);
    }

    private List<Long> getReverseOrderLineIds(Long buyerId, String orderId) {
        List<Long> reverseOrderLineIds = new ArrayList<>();
        Response<List<ReverseOrderLineDTO>> reverseOrderLinesResponse = reverseService.getBuyerReverseOrder(buyerId, Long.valueOf(orderId));
        reverseOrderLinesResponse.getModule().forEach(it -> reverseOrderLineIds.add(it.getReverseOrderLineId()));
        return reverseOrderLineIds;
    }

    @Override
    public ResultDTO updateTimeout(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        try {
            JSONObject jsonObject = JSONObject.parseObject(params);
            String orderId = jsonObject.getString(Constant.ORDER_ID);
            Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
            String alarmName = getAlarmName(jsonObject.getString(Constant.TIMEOUT_ALARM_NAME));
            List<Long> reverseOrderLineIds = new ArrayList<>();
            //查询多租户接口
            LandlordContext.setTenantSpec("AE_GLOBAL", -1);

            // 逆向单给到超时中心是reverseOrderLineId，需要单独处理，且多子场景，要多次触发超时任务
            if (Constant.WAIT_SELLER_HANDLE_CANCEL_TIMEOUT.equalsIgnoreCase(alarmName)) {
                Response<List<ReverseOrderLineDTO>> reverseOrderLinesResponse = reverseService.getBuyerReverseOrder(buyerId, Long.valueOf(orderId));
                reverseOrderLinesResponse.getModule().forEach(it -> reverseOrderLineIds.add(it.getReverseOrderLineId()));
                List<AlarmTaskDTO> alarmTaskResponses = new ArrayList<>();
                for (Long reverseOrderLineId : reverseOrderLineIds) {
                    // 先查询超时是否存在
                    Response<AlarmTaskDTO> alarmTaskResponse = alarmTaskReadFacade.getAlarmTaskDTO(alarmName, String.valueOf(reverseOrderLineId));
                    if (alarmTaskResponse.isNotSuccess()) {
                        resultDTO.setSuccess(false);
                        resultDTO.setMessage(JSON.toJSONString(alarmTaskResponse));
                        continue;
                    }
                    if (alarmTaskResponse.getModule() == null || (!StringUtil.isEmpty(alarmTaskResponse.getModule().toString())
                            && alarmTaskResponse.getModule().getStatus() != AlarmTaskStatusEnum.PENDING)) {
                        resultDTO.setSuccess(false);
                        resultDTO.setMessage("task is not exist. pls check shipping env");
                        continue;
                    }
                    alarmTaskResponses.add(alarmTaskResponse.getModule());
                }

                if (alarmTaskResponses.isEmpty()) {
                    resultDTO.setSuccess(true);
                    resultDTO.setMessage("Fail to find task. pls check env");
                    return resultDTO;
                }

                alarmTaskResponses.forEach(alarmTaskResponse -> {
                    //修改超时时间为当前时间
                    AlarmUpdateTaskDTO alarmUpdateTask = getAlarmUpdateTask(alarmTaskResponse, alarmName);
                    Response<Void> updateAlarmTaskResponse = alarmTaskWriteFacade.updateAlarmTask(alarmUpdateTask);
                    resultDTO.setSuccess(updateAlarmTaskResponse.isSuccess());
                    resultDTO.setMessage(updateAlarmTaskResponse.isSuccess() ?
                            "Update Timeout Successfully. Please wait the timer work in 1 min" : JSON.toJSONString(updateAlarmTaskResponse.getErrorCode()));
                });
            } else if (ReverseAlarmNameOfToc.getReverseAlarmNameOfToc(alarmName) != null) {
                JSONObject reverseOrderLinesResponses = HsfUtil.getReverseOrderLinesByOrderId(buyerId, Long.parseLong(orderId));
                JSONArray results = reverseOrderLinesResponses.getJSONArray("result");
                for (int i = 0; i < results.size(); i++) {
                    JSONObject result = results.getJSONObject(i);
                    if (result.getJSONObject("features") != null && result.getJSONObject("features").getJSONArray("timeouts") != null) {
                        JSONArray timeouts = result.getJSONObject("features").getJSONArray("timeouts");
                        for (int j = 0; j < timeouts.size(); j++) {
                            JSONObject timeout = timeouts.getJSONObject(j);
                            if (!timeout.getString("timeoutType").equals(alarmName)) {
                                continue;
                            }
                            String bizId = combineAftersalesToBizId(result.getLong("reverseOrderLineId"), timeout);
                            Response<AlarmTaskDTO> alarmTaskResponse = alarmTaskReadFacade.getAlarmTaskDTO("CATHEDRAL_GLOBAL_REVERSE_TIMEOUT", bizId);
                            if (alarmTaskResponse.isNotSuccess()) {
                                resultDTO.setSuccess(false);
                                resultDTO.setMessage(JSON.toJSONString(alarmTaskResponse));
                                return resultDTO;
                            }
                            if (alarmTaskResponse.getModule() == null || StringUtil.isEmpty(alarmTaskResponse.getModule().toString())) {
                                resultDTO.setSuccess(false);
                                resultDTO.setMessage("task is not exist. pls check shipping env");
                                return resultDTO;
                            }
                            try {
                                AlarmUpdateTaskDTO alarmUpdateTask = getAlarmUpdateTask(alarmTaskResponse.getModule(), "CATHEDRAL_GLOBAL_REVERSE_TIMEOUT");
                                Response<Void> updateAlarmTaskResponse = alarmTaskWriteFacade.updateAlarmTask(alarmUpdateTask);
                                resultDTO.setSuccess(updateAlarmTaskResponse.isSuccess());
                                resultDTO.setData(updateAlarmTaskResponse.isSuccess() ?
                                        "Update Timeout Successfully. Please wait the timer work in 1 min" : JSON.toJSONString(updateAlarmTaskResponse.getErrorCode()));
                                resultDTO.setMessage(updateAlarmTaskResponse.isSuccess() ?
                                        "Update Timeout Successfully. Please wait the timer work in 1 min" : JSON.toJSONString(updateAlarmTaskResponse.getErrorCode()));
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        }
                    }
                }
            } else {
                // 先查询超时是否存在
                Response<AlarmTaskDTO> alarmTaskResponse = alarmTaskReadFacade.getAlarmTaskDTO(alarmName, orderId);
                if (alarmTaskResponse.isNotSuccess()) {
                    resultDTO.setSuccess(false);
                    resultDTO.setMessage(JSON.toJSONString(alarmTaskResponse));
                    return resultDTO;
                }
                if (alarmTaskResponse.getModule() == null) {
                    resultDTO.setSuccess(false);
                    resultDTO.setMessage("task is not exist. pls check shipping env");
                    return resultDTO;
                }
                //修改超时时间为当前时间
                AlarmUpdateTaskDTO alarmUpdateTask = getAlarmUpdateTask(alarmTaskResponse.getModule(), alarmName);
                Response<Void> updateAlarmTaskResponse = alarmTaskWriteFacade.updateAlarmTask(alarmUpdateTask);
                resultDTO.setSuccess(updateAlarmTaskResponse.isSuccess());
                resultDTO.setData(updateAlarmTaskResponse.isSuccess() ?
                        "Update Timeout Successfully. Please wait the timer work in 1 min" : JSON.toJSONString(updateAlarmTaskResponse.getErrorCode()));
                resultDTO.setMessage(updateAlarmTaskResponse.isSuccess() ?
                        "Update Timeout Successfully. Please wait the timer work in 1 min" : JSON.toJSONString(updateAlarmTaskResponse.getErrorCode()));
            }
        } catch (IllegalArgumentException e) {
            resultDTO.setSuccess(false);
            resultDTO.setMessage(e.getMessage());
            return resultDTO;
        }
        return resultDTO;
    }


    @Override
    public ResultDTO updateShareGroupTimeout(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        try {
            JSONObject jsonObject = JSONObject.parseObject(params);
            Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
            String groupId = jsonObject.getString("groupId");
            String orderId = jsonObject.getString(Constant.ORDER_ID);
            String alarmName = jsonObject.getString(Constant.TIMEOUT_ALARM_NAME);
            String staging = jsonObject.getString(Constant.STAGING);
            String busId = null;

            //查询多租户接口
            LandlordContext.setTenantSpec("AE_GLOBAL", -1);
            if (StringUtil.isNotBlank(staging)) {
                EagleEye.putUserData("scm_project", staging);//临时处理

            }
            if (StringUtil.isNotBlank(orderId)) {
                busId = orderId;
            }
            if (StringUtil.isNotBlank(groupId) && buyerId != null) {
                busId = groupId;
            }

            // 先查询超时是否存在
            Response<AlarmTaskDTO> alarmTaskResponse = alarmTaskReadFacade.getAlarmTaskDTO(alarmName, busId);
            if (alarmTaskResponse.isNotSuccess()) {
                resultDTO.setSuccess(false);
                resultDTO.setMessage(JSON.toJSONString(alarmTaskResponse));
                return resultDTO;
            }
            if (alarmTaskResponse.getModule() == null) {
                resultDTO.setSuccess(false);
                resultDTO.setMessage("task is not exist. pls check shipping env");
                return resultDTO;
            }
            //修改超时时间为当前时间
            AlarmUpdateTaskDTO alarmUpdateTask = getAlarmUpdateTask(alarmTaskResponse.getModule(), alarmName);
            Response<Void> updateAlarmTaskResponse = alarmTaskWriteFacade.updateAlarmTask(alarmUpdateTask);
            resultDTO.setSuccess(updateAlarmTaskResponse.isSuccess());
            resultDTO.setData(updateAlarmTaskResponse.isSuccess() ?
                    "Update Timeout Successfully. Please wait the timer work in 1 min" : JSON.toJSONString(updateAlarmTaskResponse.getErrorCode()));
            resultDTO.setMessage(updateAlarmTaskResponse.isSuccess() ?
                    "Update Timeout Successfully. Please wait the timer work in 1 min" : JSON.toJSONString(updateAlarmTaskResponse.getErrorCode()));
        } catch (IllegalArgumentException e) {
            resultDTO.setSuccess(false);
            resultDTO.setMessage(e.getMessage());
            return resultDTO;
        }
        return resultDTO;
    }

    @Override
    public ResultDTO updateReverseTimeout(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        Long tradeOrderLineId = jsonObject.getLong(Constant.SUB_ORDER_ID);
        String timeoutType = jsonObject.getString(Constant.TIMEOUT_ALARM_NAME);
        String dpath = jsonObject.getString(Constant.DPATH_ENV);
        String alarmName = getAlarmName(timeoutType);
        JSONObject res;
        if (StringUtil.isNotBlank(dpath)) {
            EagleEye.putUserData("dpath_env", dpath);
        }
        if (Constant.X_DAY_ON_TIME_GUARANTEE_TIMEOUT_TYPE.equalsIgnoreCase(alarmName) || Constant.DELIVERY_ON_TIME_GUARANTEE_TIMEOUT_TYPE.equalsIgnoreCase(alarmName)) {
            LandlordContext.setTenantSpec("AE_GLOBAL", -1);
            Response<AlarmTaskDTO> alarmTaskResponse = alarmTaskReadFacade.getAlarmTaskDTO(alarmName, String.valueOf(tradeOrderLineId));
            if (alarmTaskResponse.isNotSuccess()) {
                result.setSuccess(false);
                result.setMessage(JSON.toJSONString(alarmTaskResponse));
                return result;
            }
            if (alarmTaskResponse.getModule() == null || StringUtil.isEmpty(alarmTaskResponse.getModule().toString())) {
                result.setSuccess(false);
                result.setMessage("task is not exist. pls check shipping env");
                return result;
            }
            //修改超时时间为当前时间
            AlarmUpdateTaskDTO alarmUpdateTask = getAlarmUpdateTask(alarmTaskResponse.getModule(), alarmName);
            Response<Void> updateAlarmTaskResponse = alarmTaskWriteFacade.updateAlarmTask(alarmUpdateTask);
            result.setSuccess(updateAlarmTaskResponse.isSuccess());
            result.setData(updateAlarmTaskResponse.isSuccess() ?
                    "Update Timeout Successfully. Please wait the timer work in 1 min" : JSON.toJSONString(updateAlarmTaskResponse.getErrorCode()));
            result.setMessage(updateAlarmTaskResponse.isSuccess() ?
                    "Update Timeout Successfully. Please wait the timer work in 1 min" : JSON.toJSONString(updateAlarmTaskResponse.getErrorCode()));
        } else {
            JSONObject reverseOrderLine = HsfUtil.getReverseOrderLineByTradeOrderLineId(buyerId, tradeOrderLineId).getJSONObject("result");
            Long reverseOrderLineId = reverseOrderLine.getLong("reverseOrderLineId");
            JSONArray timeouts = reverseOrderLine.getJSONObject("features").getJSONArray("timeouts");
            int timeoutId = getTimeoutId(timeouts, alarmName);
            String script = ReverseTimeoutUtil.getScript(timeoutType, reverseOrderLineId, timeoutId);
            res = HsfUtil.callReverseScript(String.valueOf(buyerId), script);
            if (res.getBoolean("success")) {
                result.setData("操作成功");
                result.setMessage("操作成功");
            } else {
                result.setData("操作失败。Res: " + res.toJSONString());
                result.setMessage("操作失败。Res: " + res.toJSONString());
            }
        }
        return result;
    }

    @Override
    public ResultVo getTimeoutsByParams(String params, SystemDTO systemDTO) throws Exception {
        log.info("getTimeoutsByParams(): in");
        List<AlarmTaskDTO> alarmTaskResponses = new ArrayList<>();
        JSONObject jsonObject = JSONObject.parseObject(params);
        String orderId = jsonObject.getString(Constant.ORDER_ID);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        String alarmName = jsonObject.getString(Constant.TIMEOUT_ALARM_NAME);

        LandlordContext.setTenantSpec("AE_GLOBAL", -1);
        if (StringUtils.isBlank(alarmName)) {
            // 支付超时
            Response<AlarmTaskDTO> payTimeout = alarmTaskReadFacade.getAlarmTaskDTO(Constant.PAY_TIMEOUT_TYPE, orderId);
            if (payTimeout.getModule() != null) {
                payTimeout.getModule().setAlarmName(Constant.PAY_TIMEOUT_TYPE);
                alarmTaskResponses.add(payTimeout.getModule());
            }

            // 发货超时
            Response<AlarmTaskDTO> shipTimeout = alarmTaskReadFacade.getAlarmTaskDTO(Constant.SHIP_TIMEOUT_TYPE, orderId);
            if (shipTimeout.getModule() != null) {
                shipTimeout.getModule().setAlarmName(Constant.SHIP_TIMEOUT_TYPE);
                alarmTaskResponses.add(shipTimeout.getModule());
            }

            // 确认收货超时
            Response<AlarmTaskDTO> succeedTimeout = alarmTaskReadFacade.getAlarmTaskDTO(Constant.SUCCEED_TIMEOUT_TYPE, orderId);
            if (succeedTimeout.getModule() != null) {
                succeedTimeout.getModule().setAlarmName(Constant.SUCCEED_TIMEOUT_TYPE);
                alarmTaskResponses.add(succeedTimeout.getModule());
            }

            // 支付超时提醒 - 1天
            Response<AlarmTaskDTO> reminderPay1DayTimeout = alarmTaskReadFacade.getAlarmTaskDTO(Constant.PAY_REMINDER_TIMEOUT_TYPE, orderId.trim() + "_86400");
            if (reminderPay1DayTimeout.getModule() != null) {
                reminderPay1DayTimeout.getModule().setAlarmName(Constant.PAY_REMINDER_TIMEOUT_TYPE);
                reminderPay1DayTimeout.getModule().getFeature().put("支付超时提醒", "剩余1天");
                alarmTaskResponses.add(reminderPay1DayTimeout.getModule());
            }

            // 支付超时提醒 - 3天
            Response<AlarmTaskDTO> reminderPay3DaysTimeout = alarmTaskReadFacade.getAlarmTaskDTO(Constant.PAY_REMINDER_TIMEOUT_TYPE, orderId.trim() + "_259200");
            if (reminderPay3DaysTimeout.getModule() != null) {
                reminderPay3DaysTimeout.getModule().setAlarmName(Constant.PAY_REMINDER_TIMEOUT_TYPE);
                reminderPay3DaysTimeout.getModule().getFeature().put("支付超时提醒", "剩余3天");
                alarmTaskResponses.add(reminderPay3DaysTimeout.getModule());
            }

            // 支付超时提醒 - 5天
            Response<AlarmTaskDTO> reminderPay5DaysTimeout = alarmTaskReadFacade.getAlarmTaskDTO(Constant.PAY_REMINDER_TIMEOUT_TYPE, orderId.trim() + "_432000");
            if (reminderPay5DaysTimeout.getModule() != null) {
                reminderPay5DaysTimeout.getModule().setAlarmName(Constant.PAY_REMINDER_TIMEOUT_TYPE);
                reminderPay5DaysTimeout.getModule().getFeature().put("支付超时提醒", "剩余5天");
                alarmTaskResponses.add(reminderPay5DaysTimeout.getModule());
            }

            // 发货超时提醒 - 1天
            Response<AlarmTaskDTO> reminderShip1DayTimeout = alarmTaskReadFacade.getAlarmTaskDTO(Constant.SHIP_REMINDER_TIMEOUT_TYPE, orderId.trim() + "_86400");
            if (reminderShip1DayTimeout.getModule() != null) {
                reminderShip1DayTimeout.getModule().setAlarmName(Constant.SHIP_REMINDER_TIMEOUT_TYPE);
                reminderShip1DayTimeout.getModule().getFeature().put("发货超时提醒", "剩余1天");
                alarmTaskResponses.add(reminderShip1DayTimeout.getModule());
            }

            // 发货超时提醒 - 3天
            Response<AlarmTaskDTO> reminderShip3DaysTimeout = alarmTaskReadFacade.getAlarmTaskDTO(Constant.SHIP_REMINDER_TIMEOUT_TYPE, orderId.trim() + "_259200");
            if (reminderShip3DaysTimeout.getModule() != null) {
                reminderShip3DaysTimeout.getModule().setAlarmName(Constant.SHIP_REMINDER_TIMEOUT_TYPE);
                reminderShip3DaysTimeout.getModule().getFeature().put("发货超时提醒", "剩余3天");
                alarmTaskResponses.add(reminderShip3DaysTimeout.getModule());
            }

            // 发货超时提醒 - 5天
            Response<AlarmTaskDTO> reminderShip5DayTimeout = alarmTaskReadFacade.getAlarmTaskDTO(Constant.SHIP_REMINDER_TIMEOUT_TYPE, orderId.trim() + "_432000");
            if (reminderShip5DayTimeout.getModule() != null) {
                reminderShip5DayTimeout.getModule().setAlarmName(Constant.SHIP_REMINDER_TIMEOUT_TYPE);
                reminderShip5DayTimeout.getModule().getFeature().put("发货超时提醒", "剩余5天");
                alarmTaskResponses.add(reminderShip5DayTimeout.getModule());
            }

            // 收货超时提醒 - 1天
            Response<AlarmTaskDTO> reminderSucceed1DayTimeout = alarmTaskReadFacade.getAlarmTaskDTO(Constant.SUCCEED_REMINDER_TIMEOUT_TYPE, orderId.trim() + "_86400");
            if (reminderSucceed1DayTimeout.getModule() != null) {
                reminderSucceed1DayTimeout.getModule().setAlarmName(Constant.SUCCEED_REMINDER_TIMEOUT_TYPE);
                reminderSucceed1DayTimeout.getModule().getFeature().put("确认收获超时提醒", "剩余1天");
                alarmTaskResponses.add(reminderSucceed1DayTimeout.getModule());
            }

            // 收货超时提醒 - 3天
            Response<AlarmTaskDTO> reminderSucceed3DaysTimeout = alarmTaskReadFacade.getAlarmTaskDTO(Constant.SUCCEED_REMINDER_TIMEOUT_TYPE, orderId.trim() + "_259200");
            if (reminderSucceed3DaysTimeout.getModule() != null) {
                reminderSucceed3DaysTimeout.getModule().setAlarmName(Constant.SUCCEED_REMINDER_TIMEOUT_TYPE);
                reminderSucceed3DaysTimeout.getModule().getFeature().put("确认收获超时提醒", "剩余3天");
                alarmTaskResponses.add(reminderSucceed3DaysTimeout.getModule());
            }

            // 收货超时提醒 - 5天
            Response<AlarmTaskDTO> reminderSucceed5DayTimeout = alarmTaskReadFacade.getAlarmTaskDTO(Constant.SUCCEED_REMINDER_TIMEOUT_TYPE, orderId.trim() + "_432000");
            if (reminderSucceed5DayTimeout.getModule() != null) {
                reminderSucceed5DayTimeout.getModule().setAlarmName(Constant.SUCCEED_REMINDER_TIMEOUT_TYPE);
                reminderSucceed5DayTimeout.getModule().getFeature().put("确认收获超时提醒", "剩余5天");
                alarmTaskResponses.add(reminderSucceed5DayTimeout.getModule());
            }

            // 卖家同意/拒绝取消订单超时
//            List<Long> reverseOrderLineIds = getReverseOrderLineIds(buyerId, orderId);
//            for (Long reverseOrderLineId : reverseOrderLineIds) {
//                LandlordContext.setTenantSpec("AE_GLOBAL", -1);
//                Response<AlarmTaskDTO> waitSellerCancelOrderTimeout = alarmTaskReadFacade.getAlarmTaskDTO(Constant.WAIT_SELLER_HANDLE_CANCEL_TIMEOUT, String.valueOf(reverseOrderLineId));
//                alarmTaskResponses.add(waitSellerCancelOrderTimeout.getModule());
//            }
        } else {
            if (Constant.WAIT_SELLER_HANDLE_CANCEL_TIMEOUT.equalsIgnoreCase(alarmName)) {
                List<Long> reverseOrderLineIds = getReverseOrderLineIds(buyerId, orderId);
                for (Long reverseOrderLineId : reverseOrderLineIds) {
                    LandlordContext.setTenantSpec("AE_GLOBAL", -1);
                    Response<AlarmTaskDTO> alarmTaskResponse = alarmTaskReadFacade.getAlarmTaskDTO(alarmName, String.valueOf(reverseOrderLineId));
                    alarmTaskResponse.getModule().setAlarmName(alarmName);
                    alarmTaskResponses.add(alarmTaskResponse.getModule());
                }
            } else {
                Response<AlarmTaskDTO> alarmTaskResponse = alarmTaskReadFacade.getAlarmTaskDTO(alarmName, orderId);
                if (alarmTaskResponse.getModule() != null) {
                    alarmTaskResponse.getModule().setAlarmName(alarmName);
                    alarmTaskResponses.add(alarmTaskResponse.getModule());
                }
            }
        }
        return ResultVo.ofSuccess(alarmTaskResponses);
    }

    @Override
    public ResultVo updateTimeoutsByParams(String params, SystemDTO systemDTO) {
        JSONObject jsonObject = JSONObject.parseObject(params);
        String alarmBizOrderId = jsonObject.getString("alarmBizOrderId");
        String alarmName = getTimeoutAlarmName(jsonObject.getString(Constant.TIMEOUT_ALARM_NAME));
        Response<AlarmTaskDTO> getAlarmTaskResponse = alarmTaskReadFacade.getAlarmTaskDTO(alarmName, alarmBizOrderId);
        AlarmUpdateTaskDTO alarmUpdateTask = getAlarmUpdateTask(getAlarmTaskResponse.getModule(), alarmName);
        Response<Void> updateAlarmTaskResponse = alarmTaskWriteFacade.updateAlarmTask(alarmUpdateTask);
        return updateAlarmTaskResponse.isSuccess() ? ResultVo.ofSuccess("Update Successfully, pls check")
                : ResultVo.ofFail("", JSON.toJSONString(updateAlarmTaskResponse.getErrorCode()));
    }

    @Override
    public ResultDTO breachTocCallback(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long breachId = jsonObject.getLong(Constant.BREACH_ID);
      //  JSONObject res = HsfUtil.triggerBreachTimeout(breachId);
        com.alibaba.ae.service.open.response.Response<Void> voidResponse = internalToolsFacade.triggerTimeout(breachId);
        result.setSuccess(true);
        if(voidResponse.isSuccess()){
            result.setData("更新超时成功. Res: " + voidResponse.getData());
            result.setMessage("更新超时成功. Res: " +voidResponse.getData());
        }else {
            result.setData("更新超时失败. Res: " + voidResponse.getErrorMessage());
            result.setMessage("更新超时失败. Res: " + voidResponse.getErrorMessage());
        }
        return result;
    }

    private int getTimeoutId(JSONArray timeouts, String alarmNameOfAim) {
        for (int j = 0; j < timeouts.size(); j++) {
            JSONObject timeout = timeouts.getJSONObject(j);
            String alarmName = timeout.getString("timeoutType");
            if (StringUtil.isNotBlank(alarmNameOfAim)) {
                if (alarmName.equalsIgnoreCase(alarmNameOfAim)) {
                    return timeout.getIntValue("id");
                }
            }
        }
        return -1;
    }

    private String getAlarmName(String alarmName) throws Exception {
        switch (alarmName) {
            case "支付超时":
                return "trade-pay-time-out";
            case "发货超时":
                return "trade-ship-time-out";
            case "确认收货超时":
                return "trade-succeed-time-out";
            case "卖家响应买家取消订单超时":
                return "WAIT_SELLER_HANDLE_CANCEL_TIMEOUT";
            case "普通协商超时":
            case "售后宝协商超时":
                return "WAIT_SOLUTION_TIMEOUT";
            case "理赔报案重试":
                return "WAIT_CREATE_CLAIMS_TIMEOUT";
            case "买家退货超时-自寄":
            case "买家退货超时-免费退":
            case "买家退货超时-海外仓免费退":
            case "买家退货超时-免费退-退货检查期":
            case "FR3.0-买家退货超时-平台仓-面单":// ES本地商家 平安仓
            case "FR3.0-买家退货超时-商家仓-自寄": // ES本地商家
                return "WAIT_BUYER_RETURN_ITEM_TIMEOUT";
            case "买家退货超时提醒-自寄":
            case "买家退货超时提醒-免费退":
                return "WAIT_BUYER_RETURN_ITEM_WILL_TIMEOUT";
            case "卖家收货超时":
            case "仓库收货超时":
            case "FR3.0-平台仓-自寄-卖家确认收货超时":
                return "WAIT_RECEIVE_ITEM_TIMEOUT";
            case "卖家收货超时提醒":
            case "卖家确认收货超时提醒":
            case "仓库收货超时提醒":
                return "WAIT_RECEIVE_ITEM_WILL_TIMEOUT";
            case "卖家填写退货地址超时":
                return "WAIT_SELLER_FILL_ADDRESS_TIMEOUT";
            case "X日达晚必赔超时":
                return Constant.X_DAY_ON_TIME_GUARANTEE_TIMEOUT_TYPE;
            case "FR3.0-DSCAN超时-商家仓-面单":
                return Constant.WAIT_DSCAN_RESULT_TIMEOUT;
            case "FR3.0-平台仓-自寄-质检超时":
                return "WAIT_QUALITY_RESULT_TIMEOUT";
            case "FR3.0-履约接单超时":
                return "WAIT_FULFILLMENT_ACCEPT_TIMEOUT";
            case "线路晚必赔超时":
                return "DELIVERY_ON_TIME_GUARANTEE_TIMEOUT_TYPE";
            case "二揽确认超时":
                return "WAIT_BUYER_RECOLLECT_CONFIRM";
            case "仓后仲裁超时":
                return "WAIT_AFTER_WAREHOUSE_ARBITRATION_TIMEOUT";
            case "":
                return "WAIT_AFTER_WAREHOUSE_ARBITRATION_TIMEOUT";
        }
        throw new Exception("illegal alarm name");
    }

    // alarmTask.alarmName will not return in the res, so put it by manually
    private AlarmUpdateTaskDTO getAlarmUpdateTask(AlarmTaskDTO alarmTask, String alarmName) {
        AlarmUpdateTaskDTO alarmUpdateTask = new AlarmUpdateTaskDTO();
        alarmUpdateTask.setAlarmName(alarmName);
        alarmUpdateTask.setAlarmBizOrderId(alarmTask.getAlarmBizOrderId());
        alarmUpdateTask.setEpochMills(alarmTask.getEpochMills());
        alarmUpdateTask.setStatus(AlarmTaskStatusEnum.PENDING);
        Long gmtStart = System.currentTimeMillis() - alarmTask.getEpochMills();
        alarmUpdateTask.setGmtStart(gmtStart);
        return alarmUpdateTask;
    }

    @Override
    public Map<String, QueryResultUnit> getAlarmTimeoutVo(String alarmName, String orderId) throws Exception {
        Response<AlarmTaskDTO> alarmTaskResponse = alarmTaskReadFacade.getAlarmTaskDTO(alarmName, orderId);
        if (!alarmTaskResponse.isSuccess() || alarmTaskResponse.getModule() == null) {
            log.error(JSON.toJSONString(alarmTaskResponse.getErrorCode()));
            return new ConcurrentHashMap<>();
        }
        Map<String, QueryResultUnit> data = QueryResultBuilder.buildQueryResult(alarmName + "——" + orderId, null, null, alarmTaskResponse.getModule());
        return data;
    }

    @Override
    public ResultDTO logisticsServicesTimeOut(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);

        String dpath = jsonObject.getString(Constant.DPATH_ENV);
        if (StringUtil.isNotBlank(dpath)) {
            EagleEye.putUserData("dpath_env", dpath);
        }
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        Long sellerId = jsonObject.getLong(Constant.SELLER_ID);
        Long orderId = jsonObject.getLong(Constant.ORDER_ID);
        Long orderLineId = jsonObject.getLong(Constant.SUB_ORDER_ID);
        Long timestamp = jsonObject.getLong(Constant.TIMESTAMP);
        if (timestamp == 0L) {
            timestamp = System.currentTimeMillis();
        }

        //   JSONArray orderLines = jsonObject.getJSONArray(Constant.ORDER_LINES);
        List<Long> orderLineIds = new ArrayList<>();
       /* for (Object object : orderLines) {
            JSONObject o = (JSONObject) JSON.toJSON(object);
            orderLineIds.add(o.getLong("tradeOrderLineId"));
        }*/
        orderLineIds.add(orderLineId);

        String eventType = jsonObject.getString("eventType");
        JSONObject Responses = new JSONObject();
        if ("晚必赔超时发券".equals(eventType)) {
            Map<String, String> ext = new HashMap<>();
            ext.put("", "");
            Responses = HsfUtil.triggerOnTimeGuarantee(buyerId, sellerId, orderId, orderLineId, ext);
        } else if ("Xday超时自动退款".equals(eventType)) {
            Responses = HsfUtil.triggerXdayReverse(buyerId, orderId, orderLineIds, timestamp);
        } else if ("Xday超时开放入口".equals(eventType)) {
            Responses = HsfUtil.triggerXdayCanApply(buyerId, orderId, orderLineIds, timestamp);
        } else if ("监听发货创建超时任务".equals(eventType)) {
            Responses = HsfUtil.triggerShipMsg(orderId, buyerId);
        }
        if (Responses.getBoolean("success")) {
            result.setData("操作成功" + Responses.toJSONString());
            result.setMessage("操作成功" + Responses.toJSONString());
        } else {
            result.setData("操作失败。Res: " + Responses.toJSONString());
            result.setMessage("操作失败。Res: " + Responses.toJSONString());
        }

        return result;
    }
    @Override
    public ResultDTO getTimeoutByBusinessIdAndEventType(String params, SystemDTO systemDTO) {
        ResultDTO resultDTO = new ResultDTO();
        try {
            JSONObject jsonObject = JSONObject.parseObject(params);
            String orderId = jsonObject.getString(Constant.ORDER_ID);
            Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
            String alarmName = jsonObject.getString(Constant.TIMEOUT_ALARM_NAME);
            //查询多租户接口
            LandlordContext.setTenantSpec("AE_GLOBAL", -1);

            Map<String, QueryResultUnit> data= getAlarmTimeoutVo(alarmName, orderId);

            resultDTO.setSuccess(true);
            resultDTO.setData(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));


        } catch (IllegalArgumentException e) {
            resultDTO.setSuccess(false);
            resultDTO.setMessage(e.getMessage());
            return resultDTO;
        } catch (Exception e) {
            e.printStackTrace();
            resultDTO.setSuccess(false);
            resultDTO.setMessage(e.getMessage());
            return resultDTO;
        }

        return resultDTO;
    }

    @Override
    public ResultDTO updateNewReverseTimeout(String params, SystemDTO systemDTO) throws Exception{
        ResultDTO result = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long tradeOrderLineId = jsonObject.getLong(Constant.SUB_ORDER_ID);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        String alarmName = getNewAlarmName(jsonObject.getString(Constant.REVERSE_NEW_TIMEOUT_ALARM_NAME));
        String dpath = jsonObject.getString(Constant.DPATH_ENV);
        JSONObject res;
        if (StringUtil.isNotBlank(dpath)) {
            EagleEye.putUserData("dpath_env", dpath);
        }
        JSONObject reverseOrderLine = HsfUtil.getReverseOrderLineByTradeOrderLineId(buyerId, tradeOrderLineId).getJSONObject("result");
        if (reverseOrderLine == null || reverseOrderLine.isEmpty()){
            result.setData("当前订单不存在逆向单，去请更换订单");
            result.setMessage("当前订单不存在逆向单，去请更换订单");
        }else {
            Long reverseOrderLineId = reverseOrderLine.getLong("reverseOrderLineId");
            JSONArray timeouts = reverseOrderLine.getJSONObject("features").getJSONArray("timeouts");
            if (timeouts.isEmpty()){
                result.setData("当前纠纷timeouts为空，请检查纠纷状态");
                result.setMessage("当前纠纷timeouts为空，请检查纠纷状态");
            }else {
                Map<String, String> reverseOrderLineTimeoutTypesAndCallbacks = getReverseOrderLineTimeoutTypesAndCallbacks(timeouts);
                String alarmNameCallback = reverseOrderLineTimeoutTypesAndCallbacks.get(alarmName);
                if (StringUtil.isBlank(alarmNameCallback)){
                    result.setData("当前纠纷无对应超时任务，请重新选择");
                    result.setMessage("当前纠纷无对应超时任务，请重新选择");
                }else{
                    String script = ReverseTimeoutUtil.buildReverseTimeoutScript(reverseOrderLineId,alarmName,alarmNameCallback);
                    res = HsfUtil.callReverseScript(String.valueOf(buyerId), script);
                    if (res.getBoolean("success")) {
                        result.setData("操作成功");
                        result.setMessage("操作成功");
                    } else {
                        result.setData("操作失败。Res: " + res.toJSONString());
                        result.setMessage("操作失败。Res: " + res.toJSONString());
                    }
                }
            }
        }
        return result;
    }

    @Override
    public ResultDTO triggerPunishTimeout(String params, SystemDTO systemDTO) throws Exception {

        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long breachId = jsonObject.getLong(Constant.BREACH_ID);
        String dpath = jsonObject.getString(Constant.DPATH_ENV);
        if (StringUtil.isNotBlank(dpath)) {
            EagleEye.putUserData("dpath_env", dpath);
        }
        String timeoutType = jsonObject.getString(Constant.PUNISH_TIMEOUT_TYPE);

        JSONObject contractDetail = HsfUtil.queryBreachContractDetail(breachId);
        Long tradeOrderId = contractDetail.getJSONObject("data").getJSONObject("tradeInfo").getLong("tradeOrderId");

        Response<TradeOrderDTO> tradeOrderDTOResponse = orderQueryForBuyerFacade.queryTradeOrderById(tradeOrderId);
        if (tradeOrderDTOResponse.isNotSuccess()) {
            resultDTO.setSuccess(false);
            resultDTO.setData(JSON.toJSONString("Fail to get response from trade-center"));

        }

        String buyerIdByOrder = tradeOrderDTOResponse.getModule().getBuyer().getBuyerId().toString();
        String sellerIdByOrder = tradeOrderDTOResponse.getModule().getOrderLines().get(0).getSeller().getSellerId().toString();

        Boolean testBuyerFlag = dataPoolService.checkTestAccount(Long.parseLong(buyerIdByOrder));
        Boolean testSellerFlag = dataPoolService.checkTestAccount(Long.parseLong(sellerIdByOrder));
        if ( testBuyerFlag || testSellerFlag) {
            resultDTO.setSuccess(false);
            resultDTO.setData(JSON.toJSONString("此功能只支持测试账号"));
        }

        String triggerTimeoutType = getTriggerTimeoutType(timeoutType);
        if (triggerTimeoutType == "triggerBreachTimeout"){
            JSONObject res = HsfUtil.triggerBreachTimeout(breachId);
            resultDTO.setSuccess(true);
            if (res != null && res.getBoolean("success")) {
                resultDTO.setData("更新商家同意认责超时成功. Res: " + res.toJSONString());
                resultDTO.setMessage("更新商家同意认责超时成功. Res: " + res.toJSONString());
            } else {
                resultDTO.setData("更新商家同意认责超时失败. Res: " + res.toJSONString());
                resultDTO.setMessage("更新商家同意认责超时失败. Res: " + res.toJSONString());
            }

        }else {
            JSONObject res = HsfUtil.extraDeduct(breachId);
            resultDTO.setSuccess(true);
            if (res != null && res.getBoolean("success")) {
                resultDTO.setData("更新欧盟7天退款超时成功. Res: " + res.toJSONString());
                resultDTO.setMessage("更新欧盟7天退款超时成功. Res: " + res.toJSONString());
            } else {
                resultDTO.setData("更新欧盟7天退款超时失败. Res: " + res.toJSONString());
                resultDTO.setMessage("更新欧盟7天退款超时失败. Res: " + res.toJSONString());
            }

        }

        return resultDTO;

    }

    private String getTriggerTimeoutType(String timeoutType) {
        switch (timeoutType){
            case "商家同意认责超时":
                return "triggerTimeout";
            case "欧盟7天退款超时":
                return "extraDeduct";
        }
        return "";
    }

    private String getNewAlarmName(String alarmName) throws Exception {
            switch (alarmName) {
                case "纠纷协商超时":
                    return "WAIT_SOLUTION_TIMEOUT";
                case "取消订单商家同意超时":
                    return "WAIT_SELLER_RESPONSE_TIMEOUT";
                case "创建报案单超时":
                    return "WAIT_CREATE_CLAIMS_TIMEOUT";
                case "保险接单超时":
                    return "INSURANCE_ACCEPT_TIMEOUT";
                case "卖家补充退货地址超时":
                    return "WAIT_SELLER_FILL_ADDRESS_TIMEOUT";
                case "创建履约单超时":
                    return "WAIT_CREATE_FULFILLMENT_ORDER_TIMEOUT";
                case "履约接单超时":
                    return "WAIT_FULFILLMENT_ACCEPT_TIMEOUT";
                case "履约接单超时提醒":
                    return "WAIT_FULFILLMENT_ACCEPT_WILL_TIMEOUT";
                case "买家退货超时":
                    return "WAIT_BUYER_RETURN_ITEM_TIMEOUT";
                case "买家退货超时提醒":
                    return "WAIT_BUYER_RETURN_ITEM_WILL_TIMEOUT";
                case "二揽确认超时":
                    return "WAIT_BUYER_RECOLLECT_CONFIRM";
                case "DSCAN超时":
                    return "WAIT_DSCAN_RESULT_TIMEOUT";
                case "质检超时":
                    return "WAIT_QUALITY_RESULT_TIMEOUT";
                case "卖家确认收货超时":
                    return "WAIT_RECEIVE_ITEM_TIMEOUT";
                case "卖家确认收货超时提醒":
                    return "WAIT_RECEIVE_ITEM_WILL_TIMEOUT";
                case "仓后仲裁超时":
                    return "WAIT_AFTER_WAREHOUSE_ARBITRATION_TIMEOUT";
            }
            throw new Exception("illegal alarm name");
    }


    private Map<String,String> getReverseOrderLineTimeoutTypesAndCallbacks(JSONArray timeoutsArray) {
        Map<String,String> timeoutTypesAndCallbacksList = new LinkedHashMap<>();
        for (int i = 0; i < timeoutsArray.size(); i++) {
            JSONObject timeout = timeoutsArray.getJSONObject(i);
            String callbackSting = timeout.getString("callback");
            String[] callbackStingSplit = callbackSting.split("timeout.");
            String callback = callbackStingSplit[callbackStingSplit.length - 1];
            String timeoutType = timeout.getString("timeoutType");
            timeoutTypesAndCallbacksList.put(timeoutType,callback);
        }
        return timeoutTypesAndCallbacksList;
    }
}

