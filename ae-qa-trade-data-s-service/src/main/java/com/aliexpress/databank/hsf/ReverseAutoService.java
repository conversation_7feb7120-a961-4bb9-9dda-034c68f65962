package com.aliexpress.databank.hsf;

import com.alibaba.fastjson.JSONObject;

public interface ReverseAutoService {

    JSONObject getCreateReverseOrderReceivedRefund(String scenario, String scenarioIndex);

    JSONObject getCreateReverseOrderReceivedReturnSelfDropOff(String scenario, String scenarioIndex);

    JSONObject getSellerAgreeSolution(Long reverseOrderLineId) throws Exception;

    JSONObject getBuyerAgreeSolution(Long reverseOrderLineId) throws Exception;

    JSONObject getSellerProvideSolutionRefund(Long reverseOrderLineId) throws Exception;

    JSONObject getSellerProvideSolutionNoRefund(Long reverseOrderLineId) throws Exception;

    JSONObject getSellerProvideSolutionReturn(Long reverseOrderLineId) throws Exception;

    JSONObject getBuyProvideSolutionKeep(Long reverseOrderLineId) throws Exception;

    JSONObject getBuyProvideSolutionRefund(Long reverseOrderLineId) throws Exception;

    JSONObject getBuyProvideSolutionReturn(Long reverseOrderLineId) throws Exception;
}
