package com.aliexpress.databank.hsf.provider;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.fastjson.JSONObject;
import com.aliexpress.databank.hsf.SimulatorService;
import com.aliexpress.databank.price.utils.SimulatorDataUtils;


@HSFProvider(serviceInterface = SimulatorService.class)
public class SimulatorServiceImpl implements SimulatorService {

    @Override
    public JSONObject getInvokeByTraceIdAndHsfAndAppName(String traceId, String hsf, String appNmae) {
        return SimulatorDataUtils.getSimulatorResult(traceId, hsf, appNmae);
    }
}
