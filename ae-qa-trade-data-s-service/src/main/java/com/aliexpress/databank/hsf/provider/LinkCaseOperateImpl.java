package com.aliexpress.databank.hsf.provider;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.saiga.api.SaigaCaseOperateFacade;
import com.alibaba.saiga.api.model.request.LinkCaseDataQueryRequest;
import com.alibaba.saiga.api.model.request.LinkCaseOperateRequest;
import com.alibaba.saiga.api.model.response.LinkCaseOperateResponse;
import com.alibaba.saiga.base.model.ApiResponse;
import com.aliexpress.databank.hsf.LinkCaseOperateFacade;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/12/14
 */

@Slf4j
@Component
@HSFProvider(serviceInterface = LinkCaseOperateFacade.class, serviceVersion = "1.0.0", clientTimeout = 20000)
public class LinkCaseOperateImpl implements LinkCaseOperateFacade {

    @Autowired
    SaigaCaseOperateFacade saigaCaseOperateFacade;


    @Override
    public ApiResponse<String> queryLinkCaseData(LinkCaseDataQueryRequest var1) {
        ApiResponse<String> stringApiResponse =saigaCaseOperateFacade.queryLinkCaseData(var1);
        return stringApiResponse;
    }

    @Override
    public ApiResponse<LinkCaseOperateResponse> operate(LinkCaseOperateRequest linkCaseOperateRequest){
        ApiResponse<LinkCaseOperateResponse> linkCaseOperateResponseApiResponse =saigaCaseOperateFacade.operate(linkCaseOperateRequest);
        return linkCaseOperateResponseApiResponse;
    }
}