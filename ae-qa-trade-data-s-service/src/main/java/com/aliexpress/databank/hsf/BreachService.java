package com.aliexpress.databank.hsf;

import com.alibaba.global.qa.dc.model.param.SystemDTO;
import com.alibaba.global.qa.dc.model.result.ResultDTO;
import com.alibaba.rocketmq.client.exception.MQBrokerException;
import com.alibaba.rocketmq.client.exception.MQClientException;
import com.alibaba.rocketmq.remoting.exception.RemotingException;

public interface BreachService {

    ResultDTO createWarehouseBreachOrder(String params, SystemDTO systemDTO) throws InterruptedException, RemotingException, MQClientException, MQBrokerException;

    ResultDTO queryBreach(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO createSemiChoiceJitBreach(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO queryJitPurchaseOrder(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO createJitWeightScaleBreachOrder(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO mockInitLogisticOnlineOrderDTO(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO mockImBreachSendCoupon(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO mockImBreachSeller(String params, SystemDTO systemDTO) throws Exception;

    //贵必赔发券
    ResultDTO expensiveCoupons(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO mockLogisticOnlineBreach(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO exemptBreach(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO priceProtectionCompensate(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO sendJITTimeoutMessage(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO triggerQtgQualityPunish(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO triggerOnTimeGuarantee(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO mockPopOnlinePunishExempt(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO applyCompensate(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO mockChoiceRefundRateBreach(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO sendOverseaConsignPunishMessage(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO sendWeightScaleRecoverMessage(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO handleBreachContract(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO getChoiceRefundRateBreachInfo(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO mockCroComplianceBreach(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO mockSettlementTransferMsg(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO mockCroCashOutPunish(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO mockWarehouseBreachV2(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO mockCCOJudge(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO mockJItPurchasePunish(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO testJitTimeoutBreach(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO mockDeliveryTimeoutBreach(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO mockTallyOrderBreach(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO mockWeightScaleV2Breach(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO mockStarLinkBreach(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO mockSettlementAccountPeriodAggregationMsg(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO batchQueryQualifiedTime(String params, SystemDTO systemDTO) throws Exception;

}


