package com.aliexpress.databank.hsf;

import com.alibaba.global.qa.dc.model.param.SystemDTO;
import com.alibaba.global.qa.dc.model.result.ResultDTO;
import com.aliexpress.databank.dataobject.AddressValidateDTO;

public interface AddressValidateService {

    /**
     *
     * @param validateDTO
     * @return
     * @throws Exception
     * 校验地址数据是否符合规则
     */
    AddressValidateDTO validateAddress(AddressValidateDTO validateDTO) throws Exception;

    /**
     *
     * @param params
     * @param systemDTO
     * @return
     * @throws Exception
     */
    ResultDTO validateAddressByGDC(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO fileDownload(String params, SystemDTO systemDTO);

    ResultDTO ossUploader(String params, SystemDTO systemDTO);


}
