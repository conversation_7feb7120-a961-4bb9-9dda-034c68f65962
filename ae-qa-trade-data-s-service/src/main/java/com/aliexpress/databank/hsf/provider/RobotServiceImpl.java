package com.aliexpress.databank.hsf.provider;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.ecommerce.module.Response;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.global.order.management.api.facade.OrderQueryForBuyerFacade;
import com.alibaba.global.order.management.api.model.TradeOrderDTO;
import com.alibaba.global.order.management.api.model.TradeOrderLineDTO;
import com.aliexpress.databank.constant.Constant;
import com.aliexpress.databank.dataobject.RobotRes;
import com.aliexpress.databank.hsf.RobotService;
import com.aliexpress.databank.service.ReverseService;
import com.aliexpress.databank.utils.HsfUtil;
import com.aliexpress.issue.api.AeIssueCancelOrderWriteFacade;
import com.aliexpress.issue.common.result.PlainResult;
import com.aliexpress.issue.dispute.pojo.common.CancelOrderOperatorRequest;
import com.aliexpress.issue.dispute.pojo.common.IssueCancelOrderOperatorResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.Objects;

@Slf4j
@HSFProvider(serviceInterface = RobotService.class, serviceGroup = "HSF", serviceVersion = "1.0.0", clientTimeout = 20000)
public class RobotServiceImpl implements RobotService {

    @Autowired
    private OrderQueryForBuyerFacade orderQueryForBuyerFacade;

    @Autowired
    private AeIssueCancelOrderWriteFacade cancelOrderWriteFacade;

    @Autowired
    private ReverseService reverseService;


    @Override
    public RobotRes refund(String orderId) throws Exception {
        RobotRes robotRes = new RobotRes();
        robotRes.setSuccess(true);
        Response<TradeOrderDTO> tradeOrderDTOResponse = orderQueryForBuyerFacade.queryTradeOrderById(Long.parseLong(orderId));

        if (tradeOrderDTOResponse.isNotSuccess() || tradeOrderDTOResponse.getModule() == null) {
            robotRes.setSuccess(false);
            robotRes.setErrorInfo("没获取到订单信息哦-。-目前只支持主单id维度的退款哦。子单退款马上会支持哒");
            return robotRes;
        }

        Long buyerId = Objects.requireNonNull(tradeOrderDTOResponse.getModule()).getBuyer().getBuyerId();
        Long sellerId = tradeOrderDTOResponse.getModule().getOrderLines().get(0).getSeller().getSellerId();

        boolean isTestUser = isTestUser(buyerId, sellerId);
        if (!isTestUser) {
            robotRes.setSuccess(false);
            robotRes.setErrorInfo("危险操作！买家账号或卖家账号不在测试白名单中哦～更新白名单请联系交易的测试同学哦^_^");
            return robotRes;
        }

        List<TradeOrderLineDTO> tradeOrderLines = tradeOrderDTOResponse.getModule().getOrderLines();

        // 未支付
        if (isPaid(tradeOrderLines)) {
            robotRes.setSuccess(false);
            robotRes.setErrorInfo("调皮，你没付款啊，还想退钱-.-");
            return robotRes;
        }

        // 未发货
        if (!isDelivery(tradeOrderLines)) {
            // cancel order
            CancelOrderOperatorRequest cancelOrderOperatorRequest = reverseService.getCancelOrderRequest(buyerId, Long.valueOf(orderId), "bops", tradeOrderLines, "");
            PlainResult<IssueCancelOrderOperatorResult> cancelOrderRes = cancelOrderWriteFacade.openCancelOrderIssue(buyerId, "", cancelOrderOperatorRequest);
            robotRes.setSuccess(cancelOrderRes.isSuccess());
            if (robotRes.isSuccess()) {
                robotRes.setMessage("退款成功");
            } else {
                robotRes.setErrorInfo("系统报错。请联系逆向测试同学：于畅。错误信息：" + JSON.toJSONString(cancelOrderRes));
            }
            return robotRes;
        }

        // 纠纷
        if (finishDelivery(tradeOrderLines)) {
            for (TradeOrderLineDTO tradeOrderLineDTO : tradeOrderLines) {
                int quantity = tradeOrderLineDTO.getQuantity();
                JSONObject queryRefundLimit = HsfUtil.queryRefundLimit(tradeOrderLineDTO.getTradeOrderId().toString(),
                        tradeOrderLineDTO.getTradeOrderLineId().toString(), tradeOrderLineDTO.getBuyer().getBuyerId());
                if (queryRefundLimit.getJSONObject("data") != null
                        && queryRefundLimit.getJSONObject("data").getJSONObject("refundMax") != null) {
                    JSONObject refundMax = queryRefundLimit.getJSONObject("data").getJSONObject("refundMax");
                    String amount = refundMax.getString("cent");
                    String currency = refundMax.getJSONObject("currency").getString("currencyCode");
                    JSONObject refundRequest = reverseService.getCreateReverseReq(tradeOrderLineDTO.getTradeOrderLineId().toString(),
                            tradeOrderLineDTO.getTradeOrderId().toString(), "", buyerId, true,
                            "ONLY_REFUND", amount, currency, "", "买家原因", quantity, false);
                    HsfUtil.createReverse(refundRequest);
                }
            }
        }
        return robotRes;
    }

    private boolean isPaid(List<TradeOrderLineDTO> tradeOrderLines) {
        return tradeOrderLines.stream().noneMatch(it -> it.getPayStatus() > 1);
    }

    private boolean isDelivery(List<TradeOrderLineDTO> tradeOrderLines) {
        return tradeOrderLines.stream().noneMatch(it -> it.getDeliveryStatus() > 4);
    }

    private boolean finishDelivery(List<TradeOrderLineDTO> tradeOrderLines) {
        return tradeOrderLines.stream().noneMatch(it -> it.getDeliveryStatus() != 7);
    }


    private boolean isTestUser(Long buyerId, Long sellerId) {
        return Constant.USER_WHITE_LIST.contains(buyerId) && Constant.USER_WHITE_LIST.contains(sellerId);
    }


}
