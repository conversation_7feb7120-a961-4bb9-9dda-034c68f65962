package com.aliexpress.databank.hsf.provider;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.global.qa.dc.model.param.SystemDTO;
import com.alibaba.global.qa.dc.model.result.ResultDTO;
import com.aliexpress.databank.constant.Constant;
import com.aliexpress.databank.hsf.WishlistService;
import com.aliexpress.databank.utils.HsfUtil;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@HSFProvider(serviceInterface = WishlistService.class, serviceGroup = "HSF",serviceVersion = "1.0.0", clientTimeout = 20000)
public class WishlistServiceImpl implements WishlistService{

    //收藏夹加藏接口测试
    @Override
    public ResultDTO addWishlistItems(String params, SystemDTO systemDTO){
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        long itemId = jsonObject.getLong(Constant.PARAM_ITEM_ID);
        JSONObject addResult = null;
        try {
            addResult = HsfUtil.addWishlistitem(buyerId,itemId);
        }catch (Exception e){
            log.error("{}",buyerId, itemId, e);
        }
        log.error(JSON.toJSONString(addResult));
        if (!addResult.getBoolean("success")) {
            resultDTO.setSuccess(false);
            resultDTO.setData("Fail to add to wishList");
            resultDTO.setMessage("Fail to add to wishList");
            return resultDTO;
        }else{
            resultDTO.setSuccess(true);
            resultDTO.setData("Add to wishList successfully");
            resultDTO.setMessage("Add to wishList successfully");
            return resultDTO;
        }
    }
}
