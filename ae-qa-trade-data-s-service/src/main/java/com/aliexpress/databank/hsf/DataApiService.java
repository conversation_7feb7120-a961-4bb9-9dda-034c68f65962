package com.aliexpress.databank.hsf;

import com.alibaba.global.qa.dc.model.param.SystemDTO;
import com.alibaba.global.qa.dc.model.result.ResultDTO;
import com.aliexpress.databank.dataobject.DataDisableDTO;
import com.aliexpress.databank.dataobject.ProductPool;
import com.aliexpress.databank.dataobject.UserPool;

import java.util.List;

public interface DataApiService {

	ResultDTO insertUserData(String params, SystemDTO systemDTO) throws Exception;

	ResultDTO insertProductData(String params, SystemDTO systemDTO) throws Exception;

	ResultDTO deleteInfo(String params, SystemDTO systemDTO);

	ResultDTO checkUserPool(String params, SystemDTO systemDTO);

	ResultDTO checkProductPool(String params, SystemDTO systemDTO);

	ResultDTO queryUserInfo(String params, SystemDTO systemDTO);

	ResultDTO queryProductInfo(String params, SystemDTO systemDTO);

	DataDisableDTO checkUser(List<UserPool> userPoolList) throws Exception;

	DataDisableDTO checkProduct(List<ProductPool> productPoolList);

	ResultDTO deleteProduct(String params, SystemDTO systemDTO);

	ResultDTO deleteUser(String params, SystemDTO systemDTO);

    ResultDTO updateUserStatus(String params, SystemDTO systemDTO);

	ResultDTO insertTestAccount(String params, SystemDTO systemDTO);
}
