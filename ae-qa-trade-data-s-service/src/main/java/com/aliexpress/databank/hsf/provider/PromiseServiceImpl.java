package com.aliexpress.databank.hsf.provider;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.global.qa.dc.model.builder.QueryResultBuilder;
import com.alibaba.global.qa.dc.model.param.SystemDTO;
import com.alibaba.global.qa.dc.model.result.QueryResultUnit;
import com.alibaba.global.qa.dc.model.result.ResultDTO;
import com.aliexpress.databank.constant.Constant;
import com.aliexpress.databank.diamond.DiamondUtils;
import com.aliexpress.databank.hsf.PromiseService;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

@Slf4j
@HSFProvider(serviceInterface = PromiseService.class, serviceGroup = "HSF", serviceVersion = "1.0.0", clientTimeout = 20000)
public class PromiseServiceImpl implements PromiseService {

    public static final String PROMISE_DATA_ID_PREFIX = "promiseId-";

    public static final String PROMISE_GROUP_ID = "spserver-promiseinstance";

    @Override
    public ResultDTO getPromiseConfig(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long promiseTemplateId = jsonObject.getLong(Constant.PROMISE_ID);
        String config = DiamondUtils.getDiamond(PROMISE_DATA_ID_PREFIX + promiseTemplateId, PROMISE_GROUP_ID);
        Map<String, QueryResultUnit> data = QueryResultBuilder.buildQueryResult("服务模版-" + promiseTemplateId, null, null, JSONArray.parse(config));
        resultDTO.setSuccess(true);
        resultDTO.setData(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
        resultDTO.setMessage(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
        return resultDTO;
    }
}
