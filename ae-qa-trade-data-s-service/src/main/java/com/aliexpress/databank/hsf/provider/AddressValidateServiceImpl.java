package com.aliexpress.databank.hsf.provider;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.cobar.parser.ast.expression.primary.function.json.JsonArray;
import com.alibaba.cobar.parser.ast.expression.primary.function.json.JsonObject;
import com.alibaba.ecommerce.module.Response;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.global.address.api.facade.GlobalAddressValidateFacade;
import com.alibaba.global.address.api.model.GlobalAddressFieldValidateConfigDTO;
import com.alibaba.global.address.api.model.GlobalAddressValidatorConfigDTO;
import com.alibaba.global.address.api.request.GlobalAddressValidateRequest;
import com.alibaba.global.qa.dc.model.param.SystemDTO;
import com.alibaba.global.qa.dc.model.result.ResultDTO;
import com.aliexpress.databank.constant.Constant;
import com.aliexpress.databank.dataobject.AddressValidateDTO;
import com.aliexpress.databank.hsf.AddressValidateService;
import com.aliexpress.databank.utils.OssUploader;
import com.lazada.imptest.api.facade.ic.ProductOpenService;
import com.taobao.hsf.util.RequestCtxUtil;
import com.taobao.tair.json.Json;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.poi.hpsf.Date;
import org.apache.poi.openxml4j.opc.OPCPackage;
import org.apache.poi.ss.usermodel.DataFormatter;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.actuate.metrics.integration.SpringIntegrationMetricReader;
import com.aliexpress.databank.utils.HsfUtil;
import org.springframework.core.io.ClassPathResource;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;


@Slf4j
@HSFProvider(serviceInterface = AddressValidateService.class)
public class AddressValidateServiceImpl implements AddressValidateService {


    @Autowired
    @Qualifier("GlobalAddressValidateFacade")
    private GlobalAddressValidateFacade globalAddressValidateFacade;

    @Override
    public ResultDTO validateAddressByGDC(String params, SystemDTO systemDTO) throws Exception {
//        BufferedReader reader = null;
//        reader = new BufferedReader(new InputStreamReader(AddressValidateServiceImpl.class.getResourceAsStream("address/addressData.xlsx"), StandardCharsets.UTF_8));
        List<AddressValidateDTO> res = new ArrayList<>();
        InputStream inputStream = this.getClass().getResourceAsStream("/address/addressData.xlsx");
        ResultDTO resultDTO = new ResultDTO();
        List<AddressValidateDTO> addressValidateDTOS = getAllFromExcel(inputStream);
        FileOutputStream fileOutputStream = new FileOutputStream("addressResult.xlsx");

        for (AddressValidateDTO addressValidateDTO:addressValidateDTOS){
            AddressValidateDTO validateRes = validateAddress(addressValidateDTO);
            res.add(validateRes);
            System.out.println("validateRes" + validateRes);
        }
        writeExcel(res, fileOutputStream);

        //上传到oss
        OssUploader.publish(
                "addressResult.xlsx",
                "/home/<USER>/ae-qa-trade-data-s/target/addressResult.xlsx");

        log.info("validate result"+ res);
        log.info("upload success");
        resultDTO.setSuccess(true);
        resultDTO.setMessage("upload success，please check result on oss");
        return resultDTO;
    }



    @Override
    public AddressValidateDTO validateAddress(AddressValidateDTO validateDTO) {

        try {
            RequestCtxUtil.setTargetCluster("rg-us-east-pre");
            GlobalAddressValidateRequest globalAddressValidateRequest = new GlobalAddressValidateRequest();
            globalAddressValidateRequest.setCountryCode(validateDTO.getCountry());

            GlobalAddressFieldValidateConfigDTO globalAddressFieldValidateConfigDTO = globalAddressValidateFacade.
                    getAllFieldValidateConfigByFieldName(globalAddressValidateRequest).getModule().get(validateDTO.getFieldName());
            List<GlobalAddressValidatorConfigDTO> validatorConfigList = globalAddressFieldValidateConfigDTO.getValidatorConfigs();

            for (int i = 0; i<validatorConfigList.size(); i++){
                GlobalAddressValidatorConfigDTO validator = validatorConfigList.get(i);
                if (validator.getName().equals(Constant.REQUIRED)&validateDTO.getRule().equals(Constant.REQUIRED)) {
                    if (validateDTO.getExcept().contains("true")){
                        validateDTO.setResult("pass");
                    }else {
                        validateDTO.setResult("fail");
                }
                } else if(validator.getName().equals(Constant.REGEXP)&validateDTO.getRule().equals(Constant.REGEXP)){
                    boolean isMatch = Pattern.matches(validator.getPattern(), validateDTO.getAddressData());
                    if((isMatch & validateDTO.getExcept().contains("true"))||(!isMatch&&validateDTO.getExcept().contains("false"))) {
                        validateDTO.setResult("pass");
                    }
                    else {
                        validateDTO.setResult("fail");
                    }

                } else if(validator.getName().equals(Constant.LENGTH)){
                    String[] length = validateDTO.getAddressData().split(",");
                    int min = Integer.parseInt(length[0]);
                    int max = Integer.parseInt(length[1]);
                    if (min == validator.getMinLength() && max == validator.getMaxLength()) {
                        validateDTO.setResult("pass");
                    } else {
                        validateDTO.setResult("fail");
                    }
                }
            }
        } catch (Exception e){
            log.error("validateAddress fail", e);
        }
        return validateDTO;
    }



    /**
     * 从Excel中读数据
     *
     * @return
     */
    public List<AddressValidateDTO> getAllFromExcel(InputStream fis) {
        List<AddressValidateDTO> addressValidateDTOList = new ArrayList<>();
        try {
//            FileInputStream fis = new FileInputStream(excelFile);
            XSSFWorkbook xssfWorkbook = new XSSFWorkbook(fis);
            DataFormatter dataFormatter = new DataFormatter();

            for(int numsheet =0; numsheet<xssfWorkbook.getNumberOfSheets(); numsheet++) {
                //获取每个表
                XSSFSheet xSheet = xssfWorkbook.getSheetAt(numsheet);

                if(xSheet == null) {
                    continue;
                }
//                log.info("获取到表单的最后一行的行数:"+xSheet.getLastRowNum());
                for(int numRow = 1; numRow < xSheet.getLastRowNum();numRow++) {
                    XSSFRow xRow = xSheet.getRow(numRow);
                    if (xRow != null){
                        AddressValidateDTO addressValidateDTO = new AddressValidateDTO();
                        XSSFCell caseId = xRow.getCell(0);
                        XSSFCell country = xRow.getCell(1);
                        XSSFCell fieldName = xRow.getCell(2);
                        XSSFCell rule = xRow.getCell(3);
                        XSSFCell caseName = xRow.getCell(4);
                        XSSFCell addressData = xRow.getCell(5);
                        XSSFCell except = xRow.getCell(6);

                        String test = dataFormatter.formatCellValue(fieldName);
                        String test2 = dataFormatter.formatCellValue(addressData);

                        addressValidateDTO.setFieldName(dataFormatter.formatCellValue(fieldName));
                        addressValidateDTO.setCaseId(Long.parseLong(dataFormatter.formatCellValue(caseId)));
                        addressValidateDTO.setCountry(dataFormatter.formatCellValue(country));
                        addressValidateDTO.setCaseName(dataFormatter.formatCellValue(caseName));
                        addressValidateDTO.setRule(dataFormatter.formatCellValue(rule));
                        if (addressData != null) {
                            addressValidateDTO.setAddressData(dataFormatter.formatCellValue(addressData));
                        }
                        if(except != null){
                            addressValidateDTO.setExcept(dataFormatter.formatCellValue(except));
                        }
                        addressValidateDTOList.add(addressValidateDTO);
                        System.out.println("获取到的表单地址数据"+addressValidateDTOList);
                    }
                }
            }
        } catch (Throwable e) {
            log.error("getAllFromExcel fail", e);
        }
        System.out.println("最后读到的表单地址数据"+addressValidateDTOList);
        JSONArray addressValidateDTOs = JSONArray.parseArray(JSONArray.toJSONString(addressValidateDTOList));
        return addressValidateDTOList;
    }

    /**
     * 把内容写入Excel
     * @param list 传入要写的内容，此处以一个List内容为例，先把要写的内容放到一个list中
     * @param outputStream 把输出流怼到要写入的Excel上，准备往里面写数据
     */
    public void writeExcel(List<AddressValidateDTO> list, OutputStream outputStream) throws IOException {
        //创建工作簿
        XSSFWorkbook xssfWorkbook = null;
        xssfWorkbook = new XSSFWorkbook();

        //创建工作表
        XSSFSheet xssfSheet;
        xssfSheet = xssfWorkbook.createSheet();

        //创建行
        XSSFRow xssfRow;

        //创建列，即单元格Cell
        XSSFCell xssfCell;

        //把List里面的数据写到excel中
        for (int i=0;i<list.size();i++) {
            //从第一行开始写入
            xssfRow = xssfSheet.createRow(i);
            //创建每个单元格Cell，即列的数据
            AddressValidateDTO addressValidateDTO =list.get(i);
            xssfRow.createCell(0).setCellValue(addressValidateDTO.getCaseId());
            xssfRow.createCell(1).setCellValue(addressValidateDTO.getCountry());
            xssfRow.createCell(2).setCellValue(addressValidateDTO.getFieldName());
            xssfRow.createCell(3).setCellValue(addressValidateDTO.getRule());
            xssfRow.createCell(4).setCellValue(addressValidateDTO.getCaseName());
            xssfRow.createCell(5).setCellValue(addressValidateDTO.getAddressData());
            xssfRow.createCell(6).setCellValue(addressValidateDTO.getExcept());
            xssfRow.createCell(7).setCellValue(addressValidateDTO.getResult());
        }

        //用输出流写到excel
        try {
            xssfWorkbook.write(outputStream);
            outputStream.flush();
            outputStream.close();
        }catch (IOException e) {
            e.printStackTrace();
        }

    }



//    public static void main(String[] args) throws IOException {
//        InputStream inputStream = AddressValidateServiceImpl.class.getResourceAsStream("/address/addressData.xlsx");
//        AddressValidateServiceImpl addressValidateService = new AddressValidateServiceImpl();
//        addressValidateService.getAllFromExcel(inputStream);

//        List<AddressValidateDTO> list = new ArrayList<>();
//        AddressValidateDTO addressValidateDTO = new AddressValidateDTO();
//        addressValidateDTO.setCaseName("test");
//        addressValidateDTO.setResult("pass");
//        addressValidateDTO.setExcept("excpect");
//        addressValidateDTO.setAddressData("Test");
//        addressValidateDTO.setFieldName("test");
//        addressValidateDTO.setCountry("JP");
//        addressValidateDTO.setRule("Test");
//        addressValidateDTO.setCaseId(1L);
//        list.add(addressValidateDTO);
//        FileOutputStream fileOutputStream = new FileOutputStream("a.xlsx");
//        writeExcel(list, fileOutputStream);


//    }


    @Override
    public ResultDTO fileDownload(String params, SystemDTO systemDTO)  {
        ResultDTO resultDTO = new ResultDTO();
        String msg = "";
        // 用来检测程序运行时间
        long startTime = System.currentTimeMillis();
        String filename = "/home/<USER>/ae-qa-trade-data-s/target/addressResult.xlsx";
        File file = new File(filename);
        byte[] data = getFileByteArray(filename);
//        org.apache.poi.hpsf.Date date = new Date();
//        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
//        String now = sdf.format(date);
        try {
            if (data == null) {
                throw new FileNotFoundException("文件没有找到！");
            }
         String filename2 = "eclopppppp.zip";
//            response.setContentType("application/binary;charset=UTF-8");
//            response.setHeader("Content-Disposition", "attachment;fileName=" + URLEncoder.encode(filename2, "UTF-8"));
//            response.setContentLength(data.length);
//            response.getOutputStream().write(data);
//            response.getOutputStream().flush();
//            response.getOutputStream().close();
            msg = "download success!!!";
        } catch (FileNotFoundException e) {
            e.printStackTrace();
            msg = "download fail!!!";
        }

        long endTime = System.currentTimeMillis();
        System.out.println("下载的运行时间：" + String.valueOf(endTime - startTime) + "ms");
//        return msg;
        return resultDTO;
    }

    /**
     * 写入本地磁盘
     *
     * @param filename
     * @param data
     */
    public void createNewFile(String filename, byte[] data) {
        File file = new File(filename);
        OutputStream output = null;
        try {
            if (!file.exists())
                file.createNewFile();
            output = new FileOutputStream(file);
            IOUtils.write(data, output);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (null != output) {
                try {
                    output.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
                output = null;
            }
        }

    }

    /**
     * 下载文件
     *
     * @param filename
     * @return
     */
    public byte[] getFileByteArray(String filename) {
        File file = new File(filename);
        InputStream input = null;
        byte[] data = null;
        try {
            if (!file.exists())
                return null;

            input = new FileInputStream(file);
            data = IOUtils.toByteArray(input);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (null != input) {
                try {
                    input.close();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
                input = null;
            }
        }
        return data;
    }



    @Override
    public ResultDTO ossUploader(String params, SystemDTO systemDTO) {
        ResultDTO resultDTO = new ResultDTO();
        OssUploader.publish(
                "addressResult.xlsx",
                "/home/<USER>/ae-qa-trade-data-s/target/addressResult.xlsx");

        return resultDTO;
    }

}
