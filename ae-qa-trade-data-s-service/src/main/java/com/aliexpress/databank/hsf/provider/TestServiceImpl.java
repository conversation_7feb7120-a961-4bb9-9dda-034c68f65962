package com.aliexpress.databank.hsf.provider;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.global.landlord.LandlordContext;
import com.alibaba.global.qa.dc.model.param.SystemDTO;
import com.alibaba.global.qa.dc.model.result.QueryResultUnit;
import com.alibaba.global.qa.dc.model.result.ResultDTO;
import com.aliexpress.databank.dataobject.ReverseOrder;
import com.aliexpress.databank.hsf.TestService;
import com.aliexpress.databank.mapper.ReverseOrderMapper;
import com.aliexpress.databank.service.ConfirmOrderService;
import com.aliexpress.databank.service.PlaceOrderTemplate;
import com.aliexpress.databank.service.ShipOrderService;
import com.aliexpress.databank.service.impl.NegotiationPlaceOrder;
import com.aliexpress.databank.utils.HsfUtil;
import lombok.extern.log4j.Log4j;
import org.javers.common.collections.Lists;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.LinkedHashMap;
import java.util.Map;

@Log4j
@HSFProvider(serviceInterface = TestService.class, serviceGroup = "HSF")
public class TestServiceImpl implements TestService {

    @Autowired
    private NegotiationPlaceOrder negotiationPlaceOrder;

    @Autowired
    private PlaceOrderTemplate placeOrderTemplate;

    @Autowired
    private ConfirmOrderService confirmOrderService;

    @Autowired
    private ShipOrderService shipOrderService;

    @Autowired
    private ReverseOrderMapper reverseOrderMapper;

    @Override
    public void testPlaceOrder(Long buyerId) {
        placeOrderTemplate.placeOrder(buyerId, negotiationPlaceOrder);
    }

    @Override
    public void testInsert(Long buyerId, Long orderId) {
        ReverseOrder reverseOrder = negotiationPlaceOrder.placeOrderResConvert2ReverseOrder(orderId, negotiationPlaceOrder.getScenario().getName(), 1);
        if (reverseOrder != null) {
            reverseOrderMapper.insertReverseOrders(Lists.asList(reverseOrder));
        }
    }

    @Override
    public ResultDTO consult(String params, SystemDTO systemDTO) throws Exception{
        ResultDTO result = new ResultDTO();
        Map<String, QueryResultUnit> data = new LinkedHashMap<>();
        LandlordContext.setTenantSpec("AE_GLOBAL", -1);

        JSONObject jsonObject = JSONObject.parseObject(params);
        String orderId = jsonObject.getString("orderStr");
        String checkoutOrderId = jsonObject.getString("checkoutOrderId");
        String buyerId = jsonObject.getString("buyerId");
        try{
            JSONObject res = HsfUtil.consultModifyBuyerAddress(buyerId,orderId,checkoutOrderId);
            if (res != null && !res.isEmpty()){
                result.setData(JSON.toJSONString(res));
                result.setSuccess(true);
                return result;
            }
        }catch (Exception e){
            result.setMessage(e.toString());
            result.setSuccess(false);
            return result;
        }
        return null;
    }

    @Override
    public void testShipOrder() {
        shipOrderService.shipOrders();
    }

    @Override
    public void testConfirmOrder() {
        confirmOrderService.confirmOrders();
    }
}
