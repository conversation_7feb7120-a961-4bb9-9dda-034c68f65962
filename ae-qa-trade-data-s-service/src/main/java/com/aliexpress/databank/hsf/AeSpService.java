package com.aliexpress.databank.hsf;

import com.alibaba.global.qa.dc.model.param.SystemDTO;
import com.alibaba.global.qa.dc.model.result.ResultDTO;

/**
 * <AUTHOR>
 * @date 2025/2/10
 */
public interface AeSpService {
    /**
     * 通过用户id查询服务签约表ae_promise_to_seller数据
     *
     * @param params
     * @param systemDTO
     * @return
     * @throws Exception
     */
    ResultDTO queryPromiseBySellerId(String params, SystemDTO systemDTO) throws Exception;

}
