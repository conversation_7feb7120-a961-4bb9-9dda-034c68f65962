package com.aliexpress.databank.hsf;

import com.alibaba.global.qa.dc.model.param.SystemDTO;
import com.alibaba.global.qa.dc.model.result.ResultDTO;

public interface Thub2AutoService {

    ResultDTO triggerProcessExecute(String params, SystemDTO systemDTO);

    ResultDTO queryProcessExecuteResult(String params, SystemDTO systemDTO) throws Exception;

    ResultDTO disputeProcessExecute(String params, SystemDTO systemDTO) throws Exception;
}
