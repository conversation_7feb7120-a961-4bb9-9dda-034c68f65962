package com.aliexpress.databank.hsf.provider;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.common.lang.StringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.global.qa.dc.model.builder.QueryResultBuilder;
import com.alibaba.global.qa.dc.model.param.SystemDTO;
import com.alibaba.global.qa.dc.model.result.QueryResultUnit;
import com.alibaba.global.qa.dc.model.result.ResultDTO;
import com.alibaba.rocketmq.client.producer.SendResult;
import com.alibaba.rocketmq.client.producer.SendStatus;
import com.aliexpress.databank.config.MqConfig;
import com.aliexpress.databank.constant.*;
import com.aliexpress.databank.dataobject.insurance.InsuranceOrderDTO;
import com.aliexpress.databank.dataobject.insurance.MockWFRV2Req;
import com.aliexpress.databank.hsf.InsuranceService;
import com.aliexpress.databank.utils.HsfUtil;
import com.aliexpress.databank.utils.WorryFreeRetreatUtil;
import com.taobao.eagleeye.EagleEye;
import com.taobao.top.messaging.ReceivedMessageDO;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigInteger;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;


@Slf4j
@HSFProvider(serviceInterface = InsuranceService.class, serviceGroup = "HSF", serviceVersion = "1.0.0", clientTimeout = 20000)
public class InsuranceServiceImpl implements InsuranceService {
    private static final Logger logger = LoggerFactory.getLogger(InsuranceServiceImpl.class);

    @Autowired
    private MqConfig mqConfig;

    public static final String POLICY_ISSUE_STATUS = "{\"message_type\":\"withhold_success\",\"biz_data\":\"{\\\"biz_id\\\":\\\"1000163540520181_41000102020181\\\",\\\"policy_no\\\":\\\"21010574522347210903\\\",\\\"sp_no\\\":\\\"6284\\\"}\",\"biz_order_id\":\"1000163540520181\"}";

    public static final String WITHHOLD_SUCCESS = "{\"message_type\":\"withhold_success\",\"biz_data\":\"{\\\"biz_id\\\":\\\"1000163540520181_41000102020181\\\",\\\"policy_no\\\":\\\"21010574522347210903\\\",\\\"sp_no\\\":\\\"6284\\\"}\",\"biz_order_id\":\"1000163540520181\"}";

    public static final String CLAIM_REPORT_CREATED = "{\"message_type\":\"claim_report_created\",\"biz_data\":\"{\\\"claim_report_no\\\":\\\"202101051100300604910660817973\\\",\\\"sp_no\\\":\\\"6284\\\",\\\"biz_id\\\":\\\"6200000132090181_41000096046181\\\"}\",\"biz_order_id\":\"1000163540520181\"}";

    public static final String AGREE_BUYER_RETURN_GOODS = "{\"message_type\":\"agree_buyer_return_goods\",\"biz_data\":\"{\\\"claim_report_no\\\":\\\"202101051100300604910660817973\\\",\\\"sp_no\\\":\\\"6284\\\",\\\"biz_id\\\":\\\"6200000132090181_41000096046181\\\"}\",\"biz_order_id\":\"1000163540520181\"}";

    @Override
    public ResultDTO mockInsured(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        String orderId = jsonObject.getString(Constant.SUB_ORDER_ID);
        String claimId = jsonObject.getString(Constant.CLAIM_ID);
        String policyIssueStatus = replaceOrderIdAndBizId(POLICY_ISSUE_STATUS, orderId, claimId);
        ReceivedMessageDO receivedMessageDO = new ReceivedMessageDO();
        receivedMessageDO.setContent(policyIssueStatus);
        SendResult sendResult = mqConfig.sendMessageViaTop(Constant.ANT_INSURANCE_TOPIC, Constant.ANT_INSURANCE_TAG, orderId.toString(), receivedMessageDO);
        log.info("mockInsured(): POLICY_ISSUE_STATUS: " + JSON.toJSONString(sendResult));
        if (sendResult.getSendStatus() != SendStatus.SEND_OK) {
            resultDTO.setSuccess(false);
            resultDTO.setData(JSON.toJSONString(sendResult));
            resultDTO.setMessage(JSON.toJSONString(sendResult));
            return resultDTO;
        }
        String withHold = replaceOrderIdAndBizId(WITHHOLD_SUCCESS, orderId, claimId);
        receivedMessageDO.setContent(withHold);
        sendResult = mqConfig.sendMessageViaTop(Constant.ANT_INSURANCE_TOPIC, Constant.ANT_INSURANCE_TAG, orderId.toString(), receivedMessageDO);
        resultDTO.setSuccess(true);
        resultDTO.setData(JSON.toJSONString(sendResult));
        resultDTO.setMessage(JSON.toJSONString(sendResult));
        return resultDTO;
    }

    @Override
    public ResultDTO mockApproved(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        String orderId = jsonObject.getString(Constant.SUB_ORDER_ID);
        String issueId = jsonObject.getString(Constant.ISSUE_ID);
        String claimId = jsonObject.getString(Constant.CLAIM_ID);
        String claimReportCreate = replaceOrderIdAndBizId(CLAIM_REPORT_CREATED, orderId, issueId, claimId);
        ReceivedMessageDO receivedMessageDO = new ReceivedMessageDO();
        receivedMessageDO.setContent(claimReportCreate);
        SendResult sendResult = mqConfig.sendMessageViaTop(Constant.ANT_INSURANCE_TOPIC, Constant.ANT_INSURANCE_TAG, orderId, receivedMessageDO);
        log.info("mockAgreeBuyerSendGoods(): CLAIM_REPORT_CREATED: " + JSON.toJSONString(sendResult));
        if (sendResult.getSendStatus() != SendStatus.SEND_OK) {
            resultDTO.setSuccess(false);
            resultDTO.setData(JSON.toJSONString(sendResult));
            resultDTO.setMessage(JSON.toJSONString(sendResult));
            return resultDTO;
        }
        String agreeBuyerReturnGoods = replaceOrderIdAndBizId(AGREE_BUYER_RETURN_GOODS, orderId, issueId, claimId);
        receivedMessageDO.setContent(agreeBuyerReturnGoods);
        sendResult = mqConfig.sendMessageViaTop(Constant.ANT_INSURANCE_TOPIC, Constant.ANT_INSURANCE_TAG, orderId.toString(), receivedMessageDO);
        resultDTO.setSuccess(true);
        resultDTO.setData(JSON.toJSONString(sendResult));
        resultDTO.setMessage(JSON.toJSONString(sendResult));
        return resultDTO;
    }

    @Override
    public ResultDTO mockCreateClaim(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        String orderId = jsonObject.getString(Constant.SUB_ORDER_ID);
        String issueId = jsonObject.getString(Constant.ISSUE_ID);
        String claimId = jsonObject.getString(Constant.CLAIM_ID);
        String claimReportNo = jsonObject.getString(Constant.CLAIM_REPORT_NO);
        String claimReportCreate = replaceOrderIdAndBizId(CLAIM_REPORT_CREATED, orderId, issueId, claimId);
        claimReportCreate = replaceReportNo(claimReportCreate, claimReportNo);
        ReceivedMessageDO receivedMessageDO = new ReceivedMessageDO();
        receivedMessageDO.setContent(claimReportCreate);
        SendResult sendResult = mqConfig.sendMessageViaTop(Constant.ANT_INSURANCE_TOPIC, Constant.ANT_INSURANCE_TAG, orderId, receivedMessageDO);
        resultDTO.setSuccess(true);
        resultDTO.setData(JSON.toJSONString(sendResult));
        resultDTO.setMessage(JSON.toJSONString(sendResult));
        return resultDTO;
    }

    private String replaceReportNo(String message, String claimReportNo) {
        JSONObject jsonObject = JSONObject.parseObject(message);
        JSONObject bizData = jsonObject.getJSONObject("biz_data");
        bizData.put("claim_report_no", claimReportNo);
        jsonObject.put("biz_data", bizData.toJSONString());
        return jsonObject.toJSONString();
    }

    @Override
    public ResultDTO mockAgreeBuyerSendGoods(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        String orderId = jsonObject.getString(Constant.SUB_ORDER_ID);
        String issueId = jsonObject.getString(Constant.ISSUE_ID);
        String claimId = jsonObject.getString(Constant.CLAIM_ID);
        String claimReportNo = jsonObject.getString(Constant.CLAIM_REPORT_NO);
        ReceivedMessageDO receivedMessageDO = new ReceivedMessageDO();
        String agreeBuyerReturnGoods = replaceOrderIdAndBizId(AGREE_BUYER_RETURN_GOODS, orderId, issueId, claimId);
        agreeBuyerReturnGoods = replaceReportNo(agreeBuyerReturnGoods, claimReportNo);
        receivedMessageDO.setContent(agreeBuyerReturnGoods);
        SendResult sendResult = mqConfig.sendMessageViaTop(Constant.ANT_INSURANCE_TOPIC, Constant.ANT_INSURANCE_TAG, orderId.toString(), receivedMessageDO);
        resultDTO.setSuccess(true);
        resultDTO.setData(JSON.toJSONString(sendResult));
        resultDTO.setMessage(JSON.toJSONString(sendResult));
        return resultDTO;
    }

    private String replaceOrderIdAndBizId(String messageBody, String orderId, String issueId, String claimId) {
        JSONObject jsonObject = JSONObject.parseObject(messageBody);
        jsonObject.put("biz_order_id", orderId);

        JSONObject bizData = jsonObject.getJSONObject("biz_data");
        String bizId = bizData.getString("biz_id");
        bizId = bizId.replace(bizId.split("_")[0], issueId);
        bizId = bizId.replace(bizId.split("_")[1], claimId);
        bizData.put("biz_id", bizId);
        jsonObject.put("biz_data", bizData.toJSONString());
        return jsonObject.toJSONString();
    }

    private String replaceOrderIdAndBizId(String messageBody, String orderId, String claimId) {
        JSONObject jsonObject = JSONObject.parseObject(messageBody);
        jsonObject.put("biz_order_id", orderId);

        JSONObject bizData = jsonObject.getJSONObject("biz_data");
        String bizId = bizData.getString("biz_id");
        bizId = bizId.replace(bizId.split("_")[0], orderId);
        bizId = bizId.replace(bizId.split("_")[1], claimId);
        bizData.put("biz_id", bizId);
        jsonObject.put("biz_data", bizData.toJSONString());
        return jsonObject.toJSONString();
    }

    @Override
    public ResultDTO mockServiceWorryFreeV2(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        String scenario = jsonObject.getString(Constant.ORDER_SCENARIO);
        log.error("params:{}, scenario:{}", params, scenario);

        String dpath = jsonObject.getString(Constant.DPATH_ENV);
        if (StringUtil.isNotBlank(dpath)) {
            EagleEye.putUserData("dpath_env", dpath);
        }

        MockWFRV2Req mockWFRV2Req = new MockWFRV2Req();
        mockWFRV2Req.setTradeOrderId(jsonObject.getString("tradeOrderId"));
        mockWFRV2Req.setTradeOrderLineId(jsonObject.getString("tradeOrderLineId"));
        mockWFRV2Req.setBuyerId(jsonObject.getLong("buyerId"));
        mockWFRV2Req.setSellerId(jsonObject.getLong("sellerId"));
        mockWFRV2Req.setScenario(scenario);

        ReverseWorryFreeV2Status reverseWorryFreeV2StatusByScenario = ReverseWorryFreeV2Status.getReverseWorryFreeV2StatusByScenario(scenario);

        mockWFRV2Req.setCurrentLogisticsStatus(reverseWorryFreeV2StatusByScenario.getCurrentLogisticsStatus());

        JSONObject msgBody = new JSONObject();
        InsuranceOrderDTO insuranceOrderInfo = WorryFreeRetreatUtil.getInsuranceOrderInfo(mockWFRV2Req);

        switch (scenario) {
            case "报案结果消息（面单返回）":
            case "已揽件消息（ascan）":
            case "已签收消息（dscan）":
            case "物流异常消息（二揽）":
                msgBody.put("insuranceOrder", insuranceOrderInfo);
                msgBody.put("insuranceCaseOrder", WorryFreeRetreatUtil.getInsuranceCaseInfo(insuranceOrderInfo.getInsuranceId(),insuranceOrderInfo.getBuyer().getId()));
                msgBody.put("logistics", WorryFreeRetreatUtil.rebuildInsuranceLogistics(mockWFRV2Req));
                break;
            case "勘查结果消息（质检）":
                msgBody.put("insuranceOrder", insuranceOrderInfo);
                msgBody.put("insuranceCaseOrder", WorryFreeRetreatUtil.getInsuranceCaseInfo(insuranceOrderInfo.getInsuranceId(),insuranceOrderInfo.getBuyer().getId()));
                msgBody.put("logistics", WorryFreeRetreatUtil.rebuildInsuranceLogistics(mockWFRV2Req));
                msgBody.put("checkResult", WorryFreeRetreatUtil.rebuildCheckResult());
                break;
            default:
                result.setSuccess(false);
                result.setMessage("未匹配到type:" + scenario);
                break;
        }

        logger.info("发送无忧退mock服务消息" + reverseWorryFreeV2StatusByScenario.getScenario() + ":" + msgBody.toJSONString());
        SendResult sendResult = mqConfig.sendMessage(reverseWorryFreeV2StatusByScenario.getTopic(), reverseWorryFreeV2StatusByScenario.getTag(), String.valueOf(mockWFRV2Req.getTradeOrderLineId()), msgBody.toJSONString());
        result.setMessage("发送" + reverseWorryFreeV2StatusByScenario.getScenario() + "消息" + sendResult.getMsgId());
        result.setData("发送" + reverseWorryFreeV2StatusByScenario.getScenario() + "消息" + sendResult.getMsgId());
        result.setSuccess(true);
        return result;
    }

    @Override
    public ResultDTO inquireInsuranceOrderInfo(String params, SystemDTO systemDTO) {
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        Long tradeOrderId = jsonObject.getLong(Constant.ORDER_ID);
        JSONArray orderLines = jsonObject.getJSONArray(Constant.ORDER_LINES);

        String dpath = jsonObject.getString(Constant.DPATH_ENV);
        if (StringUtil.isNotBlank(dpath)) {
            EagleEye.putUserData("dpath_env", dpath);
        }

        ResultDTO result = new ResultDTO();
        try {
            List<Long> tradeOrderLineIds = new ArrayList<>();
            for (Object orderLine : orderLines) {
                JSONObject o = (JSONObject) JSON.toJSON(orderLine);
                tradeOrderLineIds.add(o.getLong("tradeOrderLineId"));
            }
            JSONObject insuranceOrderQuery = new JSONObject();
            insuranceOrderQuery.put("buyerId", buyerId);
            insuranceOrderQuery.put("productCode", "AE_FREE_RETURN_LOCAL");
            insuranceOrderQuery.put("productPlatform", "ICBU");
            insuranceOrderQuery.put("tradeOrderId", tradeOrderId);
            insuranceOrderQuery.put("tradeOrderLineIds", tradeOrderLineIds);
            JSONObject queryInsuranceOrders4Trade = HsfUtil.queryInsuranceOrders4Trade(insuranceOrderQuery);
            Map<String, QueryResultUnit> data = new LinkedHashMap<>();
            if (queryInsuranceOrders4Trade != null) {
                logger.info("无忧退保单信息查询结果" + queryInsuranceOrders4Trade.getJSONArray("module"));
                JSONArray module = queryInsuranceOrders4Trade.getJSONArray("module");
                if (module != null && module.size() > 0) {
                    for (int i = 0; i < module.size(); i++) {
                        Map<String, QueryResultUnit> insuranceOrder = QueryResultBuilder.buildQueryResult("保单信息 - " + module.getJSONObject(i).getLong("insuranceId"), null, null, module.getJSONObject(i));
                        data.putAll(insuranceOrder);
                    }
                } else {
                    Map<String, QueryResultUnit> insuranceOrder = QueryResultBuilder.buildQueryResult("无保单信息 - " + null, null, null, null);
                    data.putAll(insuranceOrder);
                }
            }
            result.setData(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
            result.setMessage("操作成功");
            result.setSuccess(true);
        } catch (Exception e) {
            e.printStackTrace();
            result.setData("系统异常，请重试，超过三次还是不行，就放弃吧");
            result.setMessage("系统异常，请重试，超过三次还是不行，就放弃吧");
        }
        return result;
    }


    @Override
    public ResultDTO worryFreeV2TimeOut(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        String scenario = jsonObject.getString(Constant.ORDER_SCENARIO);

        String dpath = jsonObject.getString(Constant.DPATH_ENV);
        if (StringUtil.isNotBlank(dpath)) {
            EagleEye.putUserData("dpath_env", dpath);
        }

        String timeoutType = TimeoutEnum.getValue(scenario);

        //根据子单id查询逆向单
        JSONObject reverseOrderLine = HsfUtil.getLastReverseOrderByTradeOrderLineId(jsonObject.getLong("buyerId"), jsonObject.getLong("tradeOrderLineId"));
        JSONObject lastReverseOrder = reverseOrderLine.getJSONObject("result");
        if (lastReverseOrder == null) {
            result.setSuccess(true);
            result.setData("未找到有效的逆向单");
            result.setMessage("未找到有效的逆向单");
            return result;
        }

        Long reverseOrderLineId = lastReverseOrder.getLong("reverseOrderLineId");
        JSONObject timeoutResult = HsfUtil.triggerReverseTimeout(reverseOrderLineId, timeoutType);
        result.setSuccess(true);
        result.setData(timeoutResult.toJSONString());
        result.setMessage(timeoutResult.toJSONString());

        return result;
    }

    @Override
    public ResultDTO worryFreeV2FastInsure(String params, SystemDTO systemDTO) {
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long tradeOrderId = jsonObject.getLong(Constant.ORDER_ID);
        Long buyerId = jsonObject.getLong("buyerId");

        String dpath = jsonObject.getString(Constant.DPATH_ENV);
        if (StringUtil.isNotBlank(dpath)) {
            EagleEye.putUserData("dpath_env", dpath);
        }

        ResultDTO result = new ResultDTO();
        result.setSuccess(true);
        try {
            JSONObject timeoutResult = HsfUtil.fastInsure(tradeOrderId, buyerId);
            result.setData(timeoutResult.toJSONString());
            result.setMessage(timeoutResult.toJSONString());
        } catch (Exception e) {
            e.printStackTrace();
            result.setData("系统异常，请重试，超过三次还是不行，就放弃吧");
            result.setMessage("系统异常，请重试，超过三次还是不行，就放弃吧");
        }
        return result;
    }

    @Override
    public ResultDTO mockICBUInformation(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        Long tradeOrderId = jsonObject.getLong(Constant.ORDER_ID);
//        JSONArray orderLines = jsonObject.getJSONArray(Constant.ORDER_LINES);
        JSONArray tradeOrderLineId = jsonObject.getJSONArray(Constant.TRADE_ORDER_LINE_ID);
        String scenario = jsonObject.getString(Constant.ORDER_SCENARIO);
        log.error("params:{}, scenario:{}", params, scenario);

        String dpath = jsonObject.getString(Constant.DPATH_ENV);
        if (StringUtil.isNotBlank(dpath)) {
            EagleEye.putUserData("dpath_env", dpath);
        }

        MockWFRV2Req mockWFRV2Req = new MockWFRV2Req();
        mockWFRV2Req.setTradeOrderLineId(jsonObject.getString("tradeOrderLineId"));
        mockWFRV2Req.setScenario(scenario);

        FreeNoWorryInsurance reverseWorryFreeV2StatusByScenario = FreeNoWorryInsurance.getReverseWorryFreeV2StatusByScenario(scenario);

//        mockWFRV2Req.setCurrentLogisticsStatus(reverseWorryFreeV2StatusByScenario.getCurrentLogisticsStatus());

        JSONObject msgBody = new JSONObject();
//        InsuranceOrderDTO insuranceOrderInfo = WorryFreeRetreatUtil.getInsuranceOrderInfo(mockWFRV2Req);
        InsuranceData insuranceData = new InsuranceData();


        String insuranceNo = "01960100001180724071800000017001";
        BigInteger insuranceId = new BigInteger("80190014400");

        //构造查询保险单入参
        JSONObject insuranceOrderQuery = new JSONObject();
        insuranceOrderQuery.put("buyerId", buyerId);
        insuranceOrderQuery.put("productCode", "AE_FREE_RETURN_LOCAL");
        insuranceOrderQuery.put("productPlatform", "ICBU");
        insuranceOrderQuery.put("tradeOrderId", tradeOrderId);
        insuranceOrderQuery.put("tradeOrderLineIds", tradeOrderLineId);
        JSONObject queryInsuranceOrders4Trade = HsfUtil.queryInsuranceOrders4Trade(insuranceOrderQuery);
        if (queryInsuranceOrders4Trade != null) {
            logger.info("无忧退保单信息查询结果" + queryInsuranceOrders4Trade.getJSONArray("module"));
            JSONArray module = queryInsuranceOrders4Trade.getJSONArray("module");
            if (module != null && module.size() > 0) {
                for (int i = 0; i < module.size(); i++) {
//                    long insuranceId = module.getJSONObject(i).getLong("insuranceId");
                    insuranceId = new BigInteger(String.valueOf(module.getJSONObject(i).getLong("insuranceId")));
                    insuranceNo = module.getJSONObject(i).getString("policyNo");
                }
            }
        }

        switch (scenario) {
            case "投保成功":
                msgBody = insuranceData.getMsgBody();
                //替换insuranceNo
                msgBody.put("insuranceNo", insuranceNo);
                //替换insuranceID
                msgBody.put("insuranceId", insuranceId);
                logger.info("投保入参" + msgBody);
                break;
            default:
                result.setSuccess(false);
                result.setMessage("未匹配到type:" + scenario);
                break;
        }

        logger.info("发送无忧退mockICBU消息" + reverseWorryFreeV2StatusByScenario.getScenario() + ":" + msgBody.toJSONString());
        SendResult sendResult = mqConfig.sendMessage(reverseWorryFreeV2StatusByScenario.getTopic(), reverseWorryFreeV2StatusByScenario.getTag(), String.valueOf(mockWFRV2Req.getTradeOrderLineId()), msgBody.toJSONString());
        result.setMessage("发送" + reverseWorryFreeV2StatusByScenario.getScenario() + "消息" + sendResult.getMsgId());
        result.setData("发送" + reverseWorryFreeV2StatusByScenario.getScenario() + "消息" + sendResult.getMsgId());
        result.setSuccess(true);
        return result;
    }
}
