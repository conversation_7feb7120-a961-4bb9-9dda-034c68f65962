package com.aliexpress.databank.hsf.provider;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.common.lang.StringUtil;
import com.alibaba.ecommerce.module.Response;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.global.landlord.LandlordContext;
import com.alibaba.global.order.management.api.facade.OrderQueryForBuyerFacade;
import com.alibaba.global.order.management.api.model.TradeOrderLineDTO;
import com.alibaba.global.qa.dc.model.param.SystemDTO;
import com.alibaba.global.qa.dc.model.result.ResultDTO;
import com.alibaba.global.reverse.api.model.ReverseOrderLineDTO;
import com.aliexpress.databank.constant.Constant;
import com.aliexpress.databank.hsf.SellerService;
import com.aliexpress.databank.service.ReverseService;
import com.aliexpress.databank.utils.ConvertParam;
import com.aliexpress.databank.utils.HsfUtil;
import com.aliexpress.issue.api.AeIssueCancelOrderWriteFacade;
import com.aliexpress.issue.common.result.PlainResult;
import com.aliexpress.issue.dispute.pojo.common.CancelOrderOperatorRequest;
import com.aliexpress.issue.dispute.pojo.common.IssueCancelOrderOperatorResult;
import com.taobao.eagleeye.EagleEye;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

@HSFProvider(serviceInterface = SellerService.class, serviceGroup = "HSF", clientTimeout = 20000)
public class SellerServiceImpl implements SellerService {

    @Autowired
    private AeIssueCancelOrderWriteFacade cancelOrderWriteFacade;

    @Autowired
    private ReverseService reverseService;

    @Autowired
    private OrderQueryForBuyerFacade orderQueryForBuyerFacade;

    @Override
    public ResultDTO agreeCancelOrder(String params, SystemDTO systemDTO) {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long orderId = jsonObject.getLong(Constant.ORDER_ID);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        Long sellerId = jsonObject.getLong(Constant.SELLER_ID);
        String dpath = jsonObject.getString(Constant.DPATH_ENV);
        JSONObject tradeOrderResponse = jsonObject.getJSONObject(Constant.ORDER_INFO).getJSONObject("module");
        List<TradeOrderLineDTO> tradeOrderLineDTOs = tradeOrderResponse.getJSONArray("orderLines").toJavaList(TradeOrderLineDTO.class);
        List<Long> tradeOrderLineIds = getTradeOrderLineIds(tradeOrderLineDTOs);
        LandlordContext.setTenantSpec("AE_GLOBAL", -1);
        if (StringUtil.isNotBlank(dpath)) {
            EagleEye.putUserData("dpath_env", dpath);
        }
        Response<List<ReverseOrderLineDTO>> reverseOrderLinesResponse = reverseService.getBuyerReverseOrder(buyerId, orderId);
        if (reverseOrderLinesResponse.isNotSuccess()) {
            resultDTO.setSuccess(false);
            resultDTO.setData("未找到逆向单。 买家当前未发起过取消订单操作");
            resultDTO.setMessage("未找到逆向单。 买家当前未发起过取消订单操作");
            return resultDTO;
        }
        if (reverseOrderLinesResponse.getModule() != null && reverseOrderLinesResponse.getModule().size() != 0) {
            String applyReason = reverseOrderLinesResponse.getModule().get(0).getApplyReason().getText();
            CancelOrderOperatorRequest cancelOrderOperatorRequest = ConvertParam.getCancelOrderOperatorRequestBySellerSide(sellerId, orderId, applyReason, tradeOrderLineIds);
            PlainResult<IssueCancelOrderOperatorResult> agreeCancelOrderResponse = cancelOrderWriteFacade.approveCancelOrderIssue(buyerId, "", cancelOrderOperatorRequest);
            resultDTO.setSuccess(agreeCancelOrderResponse.isSuccess());
            if (agreeCancelOrderResponse.isSuccess()) {
                resultDTO.setMessage("取消订单成功。 Res: " + JSON.toJSONString(agreeCancelOrderResponse));
                resultDTO.setData("取消订单成功。 Res: " + JSON.toJSONString(agreeCancelOrderResponse));
            } else {
                resultDTO.setMessage("取消订单失败。 Res: " + JSON.toJSONString(agreeCancelOrderResponse));
                resultDTO.setData("取消订单失败。 Res: " + JSON.toJSONString(agreeCancelOrderResponse));
            }
        }
        return resultDTO;
    }

    private List<Long> getTradeOrderLineIds(List<TradeOrderLineDTO> orderLines) {
        List<Long> tradeOrderLineIds = new ArrayList<>();
        orderLines.forEach(it -> tradeOrderLineIds.add(it.getTradeOrderId()));
        return tradeOrderLineIds;
    }

    @Override
    public ResultDTO rejectCancelOrder(String params, SystemDTO systemDTO) {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long orderId = jsonObject.getLong(Constant.ORDER_ID);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        Long sellerId = jsonObject.getLong(Constant.SELLER_ID);
        String dpath = jsonObject.getString(Constant.DPATH_ENV);
        JSONObject tradeOrderResponse = jsonObject.getJSONObject(Constant.ORDER_INFO).getJSONObject("module");
        List<TradeOrderLineDTO> tradeOrderLineDTOs = tradeOrderResponse.getJSONArray("orderLines").toJavaList(TradeOrderLineDTO.class);
        List<Long> tradeOrderLineIds = getTradeOrderLineIds(tradeOrderLineDTOs);
        LandlordContext.setTenantSpec("AE_GLOBAL", -1);
        if (StringUtil.isNotBlank(dpath)) {
            EagleEye.putUserData("dpath_env", dpath);
        }
        Response<List<ReverseOrderLineDTO>> reverseOrderLinesResponse = reverseService.getBuyerReverseOrder(buyerId, orderId);
        if (reverseOrderLinesResponse.isNotSuccess()) {
            resultDTO.setSuccess(false);
            resultDTO.setData("未找到逆向单。 买家当前未发起过取消订单操作");
            resultDTO.setMessage("未找到逆向单。 买家当前未发起过取消订单操作");
            return resultDTO;
        }
        if (reverseOrderLinesResponse.getModule() != null && reverseOrderLinesResponse.getModule().size() != 0) {
            String applyReason = reverseOrderLinesResponse.getModule().get(0).getApplyReason().getText();
            CancelOrderOperatorRequest cancelOrderOperatorRequest = ConvertParam.getCancelOrderOperatorRequestBySellerSide(sellerId, orderId, applyReason, tradeOrderLineIds);
            PlainResult<IssueCancelOrderOperatorResult> rejectCancelOrderResponse = cancelOrderWriteFacade.denyCancelOrderIssue(buyerId, "", cancelOrderOperatorRequest);
            resultDTO.setSuccess(true);
            if (rejectCancelOrderResponse.isSuccess()) {
                resultDTO.setMessage("拒绝买家请求成功。 Res: " + JSON.toJSONString(rejectCancelOrderResponse));
                resultDTO.setData("拒绝买家请求成功。 Res: " + JSON.toJSONString(rejectCancelOrderResponse));
            } else {
                resultDTO.setMessage("拒绝卖家请求失败。 Res: " + JSON.toJSONString(rejectCancelOrderResponse));
                resultDTO.setData("拒绝卖家请求失败。 Res: " + JSON.toJSONString(rejectCancelOrderResponse));
            }
        }
        return resultDTO;
    }

    @Override
    public ResultDTO orderChangeReportForPickUp(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        String pickUpOrderCode = jsonObject.getString(Constant.PICK_UP_ORDER);
        Long pickUpTime = jsonObject.getLong(Constant.TIMESTAMP);
        Long pickUpQty = jsonObject.getLong(Constant.QUANTITY);
        JSONObject request = new JSONObject();
        request.put("actualPackageCount", pickUpQty);
        request.put("actualPickUpTime", pickUpTime);
        request.put("fulfillPickUpOrderCode", "LZDPUO231120000041003");
        request.put("operateTime", System.currentTimeMillis());
        request.put("pickUpOrderCode", pickUpOrderCode);
        request.put("pickUpOrderStatus", "PICKED_UP");
        request.put("timeZone", "+0800");
        JSONObject truckInfo = new JSONObject();
        truckInfo.put("carNumber", "豫P6305F");
        truckInfo.put("driverName", "zqy");
        truckInfo.put("driverPhone", "13787654356");
        request.put("truckInfo", truckInfo);
        JSONObject res = HsfUtil.orderChangeReport(request);
        resultDTO.setSuccess(true);
        resultDTO.setData(res.toJSONString());
        resultDTO.setMessage(res.toJSONString());
        return resultDTO;
    }

}
