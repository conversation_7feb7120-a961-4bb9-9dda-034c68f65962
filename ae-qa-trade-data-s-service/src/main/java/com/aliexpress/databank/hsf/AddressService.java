package com.aliexpress.databank.hsf;

import com.alibaba.global.qa.dc.model.param.SystemDTO;
import com.alibaba.global.qa.dc.model.result.ResultDTO;
import com.alibaba.intl.ae.logistics.address.dto.WlMailingAddressResultDTO;

public interface AddressService {

    /**
     * 通过用户id查询用户地址
     * @param params
     * @param systemDTO
     * @return
     * @throws Exception
     */
    ResultDTO getUserAddressByUserId(String params, SystemDTO systemDTO) throws Exception;

    /**
     * 一键造地址
     * @param params
     * @param systemDTO
     * @return
     * @throws Exception
     */
    ResultDTO createAddress(String params, SystemDTO systemDTO)throws Exception;
    /**
     * 一键造默认地址
     * @param buyerId
     * @param countryCode
     * @return
     * @throws Exception
     */
    WlMailingAddressResultDTO addDefaultAddress(Long buyerId, String countryCode)throws Exception;
    /**
     * 一键删除地址
     * @param buyerId
     * @param addressId
     * @return
     * @throws Exception
     */
    ResultDTO delectAddress(Long buyerId, Long addressId)throws Exception;

    /**
     * 一键造带有推荐地址的地址
     * @param params
     * @param systemDTO
     * @return
     * @throws Exception
     */

    ResultDTO createRecommendAddress(String params, SystemDTO systemDTO)throws Exception;

    /**
     * 构造巴西cpf符合校验和不符合校验的地址
     * @param params
     * @param systemDTO
     * @return
     * @throws Exception
     */
    ResultDTO createBrCpfAddress(String params, SystemDTO systemDTO)throws Exception;

    /**
     * 构建信息不全的地址数据
     * @param params
     * @param systemDTO
     * @return
     * @throws Exception
     */
    ResultDTO insertUserAddress(String params, SystemDTO systemDTO) throws Exception;

    /**
     * 检测用户现存地址是否包含预期的国家
     * 预期国家在diamond进行配置
     * @param params
     * @param systemDTO
     * @return
     * @throws Exception
     */
    ResultDTO checkAddress(String params, SystemDTO systemDTO) throws Exception;


    /**
     * 删除用户所有地址
     * @param params
     * @param systemDTO
     * @return
     * @throws Exception
     */
    ResultDTO delectAllAddress(String params, SystemDTO systemDTO) throws Exception;


    /**
     * 履约小二push推荐地址
     * @param params
     * @param systemDTO
     * @return
     * @throws Exception
     */
    ResultDTO mockRecommendAddress(String params, SystemDTO systemDTO) throws Exception;


    ResultDTO createAndMockWrongAddress(String params, SystemDTO systemDTO) throws Exception;
}
