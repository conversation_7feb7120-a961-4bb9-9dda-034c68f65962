package com.aliexpress.databank.hsf.provider;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.global.ic.api.CustomerProductServiceFacade;
import com.alibaba.global.ic.dto.scenario.query.ProductQueryRequest;
import com.alibaba.global.ic.dto.scenario.query.ProductQueryResponse;
import com.alibaba.global.ic.dto.scenario.query.SingleProductQueryCondition;
import com.aliexpress.databank.constant.Constant;
import com.aliexpress.databank.hsf.DataPoolService;
import com.aliexpress.databank.hsf.ProductService;
import com.lazada.imptest.api.domain.DataResultDO;
import com.lazada.imptest.api.domain.ic.ProductResponseDTO;
import com.lazada.imptest.api.facade.ic.ProductOpenService;
import com.lazada.imptest.api.facade.ic.ProductToolService;
import com.taobao.hsf.util.RequestCtxUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

@HSFProvider(serviceInterface = ProductService.class, serviceGroup = "HSF", serviceVersion = "1.0.0", clientTimeout = 20000)
public class ProductServiceImpl implements com.aliexpress.databank.hsf.ProductService {
    private static final Logger logger = LoggerFactory.getLogger(DataPoolServiceImpl.class);

    @Autowired
    private CustomerProductServiceFacade customerProductServiceFacade;

    @Autowired
    private DataPoolService dataPoolService;

    @Autowired
    @Qualifier("ProductToolService")
    private ProductToolService productToolService;

    @Autowired
    @Qualifier("ProductOpenService")
    private ProductOpenService productOpenService;

    @Override
    public ProductQueryResponse getProductDetails(Long itemId) {
        SingleProductQueryCondition.QueryByProductIdBuilder queryByProductIdBuilder = SingleProductQueryCondition.queryByProductIdBuilder(itemId);
        ProductQueryRequest productQueryRequest = ProductQueryRequest.builder().addQueryCondition(queryByProductIdBuilder.build()).build();
        ProductQueryResponse queryResult = customerProductServiceFacade.queryProduct(productQueryRequest);
        return queryResult;
    }


    @Override
    public Boolean recoverProduct(Long sellerId, Long itemId) {
       DataResultDO<Boolean> queryResult = null;
        try {
            if (!dataPoolService.checkTestAccount(sellerId)){
                return false;
            }
            RequestCtxUtil.setTargetCluster("pre");
            queryResult = productOpenService.recoverProduct(itemId, "AE_GLOBAL");
            if (queryResult.isSuccess()){
                return true;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public ProductResponseDTO getProductInfo(Long itemId, Long skuId) {
        int times = 0;
        while (times < Constant.MAX_TIMES) {
            try {
                RequestCtxUtil.setTargetCluster("pre");
                logger.info("times:" + times);
                DataResultDO<ProductResponseDTO> res =  productToolService.queryProduct(itemId,skuId,false,false,"AE_GLOBAL");
                logger.info("queryProduct:" + res);
                if (res.isSuccess()){
                    return res.getData();
                }
            } catch (Exception e) {
                times++;
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException interruptedException) {
                    interruptedException.printStackTrace();
                }
                if(times >= Constant.MAX_TIMES) {
                    throw new RuntimeException(e);
                }
            }
        }
        return null;
    }


}
