package com.aliexpress.databank.hsf.consumer;

import com.alibaba.ae.extend.buy.AEMultiterminalCheckoutFacade;
import com.alibaba.ae.trade.open.bops.share.service.arbitration.TpbopsArbitrationTaskService;
import com.alibaba.boot.hsf.annotation.HSFConsumer;
import com.alibaba.global.address.api.facade.UserAddressReadFacade;
import com.alibaba.global.buy.api.facade.CheckoutMultiterminalFacade;
import com.alibaba.global.buy.api.facade.RegionCheckoutFacade;
import com.alibaba.global.buy.api.facade.wireless.WirelessCheckoutFacade;
import com.alibaba.global.carts.api.facade.CartFacade;
import com.alibaba.global.ic.api.CustomerProductServiceFacade;
import com.alibaba.global.order.management.api.facade.OrderManagementRegionFacade;
import com.alibaba.global.order.management.api.facade.OrderQueryForBuyerFacade;
import com.alibaba.global.order.management.api.facade.OrderQueryForSellerFacade;
import com.alibaba.global.order.management.api.facade.OrderViewFacade;
import com.alibaba.global.payment.api.facade.GlobalPaymentCashierMtopFacade;
import com.alibaba.global.payment.api.facade.GlobalPaymentCashierServiceFacade;
import com.alibaba.global.payment.api.facade.PaymentBopsToolFacade;
import com.alibaba.global.payment.api.facade.PaymentFacade;
import com.alibaba.global.payment.api.facade.admin.PaymentOpsFacade;
import com.alibaba.global.qa.dc.remote.DcSecurityService;
import com.alibaba.global.reverse.api.facade.GlobalReverseOrderQueryForBuyerFacade;
import com.alibaba.global.reverse.api.facade.GlobalReverseOrderQueryForSellerFacade;
import com.alibaba.global.timeout.facade.IAlarmTaskReadFacade;
import com.alibaba.global.timeout.facade.IAlarmTaskWriteFacade;
import com.alibaba.global.uop.api.FulfillmentOrderQueryFacade;
import com.alibaba.global.uop.api.FulfillmentOrderQueryNoRoutingFacade;
import com.alibaba.global.uop.api.FulfillmentOrderStatusUpdateFacade;
import com.alibaba.intl.ae.biz.promisetemplate.share.service.interfaces.PromiseServiceForMtop;
import com.alibaba.intl.ae.biz.promisetemplate.share.service.interfaces.PromiseTemplateInternalService;
import com.alibaba.intl.ae.logistics.address.open.remote.WlMailingAddressRemoteService;
import com.alibaba.saiga.api.SaigaCaseOperateFacade;
import com.alibaba.saiga.sdk.replay.service.ReplayService;
import com.aliexpress.databank.hsf.ExchangeRateServiceHsf;
import com.aliexpress.issue.api.AeIssueCancelOrderWriteFacade;
import com.aliexpress.issue.dispute.service.AeIssueDisputeQueryRemoteService;
import com.aliexpress.issue.dispute.service.DataMigration4TestService;
import com.aliexpress.issue.service.issue.IssueInstantRefundService;
import com.aliexpress.shadowdata.share.remote.service.interfaces.OrderDataModeRemoteService;
import com.aliexpress.wallet.api.asset.facade.ICreditAssetServiceFacade;
import com.aliexpress.wallet.api.asset.facade.IMockToolsFacade;
import com.aliexpress.wallet.api.asset.facade.IWalletAssetServiceFacade;
import com.aliexpress.wallet.api.member.facade.IWalletMemberServiceFacade;
import com.aliexpress.wallet.api.member.facade.IWalletMemberTagServiceFacade;
import com.taobao.payment.boot.techsdk.facade.PaymentBootBizQueryFacade;
import com.taobao.payment.boot.techsdk.facade.PaymentBootOpsFacade;
import org.springframework.context.annotation.Configuration;

/**
 * hsf服务的统一个Config类，在其它需要使用的地方，直接@Autowired注入即可。详情见
 * http://gitlab.alibaba-inc.com/middleware-container/pandora-boot/wikis/spring-boot-hsf
 *
 * <AUTHOR>
 */
@Configuration
public class HsfConfig {

    @HSFConsumer(serviceVersion = "1.0.0.sirius", serviceGroup = "HSF", clientTimeout = 20000)
    private CartFacade cartFacade;

    @HSFConsumer(serviceVersion = "1.0.0", serviceGroup = "HSF", clientTimeout = 20000)
    private CustomerProductServiceFacade customerProductServiceFacade;

    @HSFConsumer(serviceVersion = "1.0.0", serviceGroup = "HSF", clientTimeout = 20000)
    private OrderQueryForBuyerFacade orderQueryForBuyerFacade;

    @HSFConsumer(serviceVersion = "1.0.0", serviceGroup = "HSF", clientTimeout = 20000)
    private OrderQueryForSellerFacade orderQueryForSellerFacade;

    // global-timeout-center
    @HSFConsumer(serviceVersion = "1.0.0", serviceGroup = "HSF", clientTimeout = 20000)
    private IAlarmTaskReadFacade alarmTaskReadFacade;

    @HSFConsumer(serviceVersion = "1.0.0", serviceGroup = "HSF", clientTimeout = 20000)
    private IAlarmTaskWriteFacade alarmTaskWriteFacade;

    @HSFConsumer(serviceVersion = "2.0.0", serviceGroup = "HSF", clientTimeout = 20000)
    private AeIssueCancelOrderWriteFacade aeIssueCancelOrderWriteFacade;

    // ae-qa
    @HSFConsumer(serviceVersion = "1.0.0", serviceGroup = "HSF", clientTimeout = 20000)
    private DcSecurityService securityService;

    @HSFConsumer(serviceVersion = "1.0.0", serviceGroup = "HSF", clientTimeout = 20000)
    private CheckoutMultiterminalFacade checkoutMultiterminalFacade;

    @HSFConsumer(serviceVersion = "1.0.0", serviceGroup = "HSF", clientTimeout = 20000)
    private AEMultiterminalCheckoutFacade aeMultiterminalCheckoutFacade;

    @HSFConsumer(serviceVersion = "1.0.0", serviceGroup = "HSF", clientTimeout = 20000)
    private WirelessCheckoutFacade wirelessCheckoutFacade;

    @HSFConsumer(serviceVersion = "1.0.0", serviceGroup = "HSF", clientTimeout = 20000)
    private RegionCheckoutFacade regionCheckoutFacade;

    @HSFConsumer(serviceVersion = "1.0.0.global", serviceGroup = "HSF", clientTimeout = 30000)
    private GlobalReverseOrderQueryForBuyerFacade reverseOrderQueryForBuyerFacade;


    @HSFConsumer(serviceVersion = "1.0.0", serviceGroup = "HSF", clientTimeout = 30000)
    private IWalletMemberServiceFacade iWalletMemberServiceFacade;

    @HSFConsumer(serviceVersion = "1.0.0", serviceGroup = "HSF", clientTimeout = 30000)
    private IWalletMemberTagServiceFacade iWalletMemberTagServiceFacade;


    @HSFConsumer(serviceVersion = "1.0.0", serviceGroup = "HSF", clientTimeout = 30000)
    private OrderManagementRegionFacade orderManagementRegionFacade;

    @HSFConsumer(serviceVersion = "1.0.0", serviceGroup = "gps", clientTimeout = 30000)
    private com.alibaba.global.payment.api.facade.PaymentQueryFacade paymentQueryFacade;

    @HSFConsumer(serviceVersion = "1.0.0", serviceGroup = "gps", clientTimeout = 30000)
    private com.alibaba.global.payment.api.facade.simple.PaymentQueryFacade paymentQueryFacade1;

    @HSFConsumer(serviceVersion = "1.0.0", serviceGroup = "GLOBAL-AE", clientTimeout = 30000)
    private PaymentBopsToolFacade paymentBopsToolFacade;

    @HSFConsumer(serviceVersion = "1.0.0", serviceGroup = "gps", clientTimeout = 30000)
    private PaymentBootOpsFacade paymentBootOpsFacade;

    @HSFConsumer(serviceVersion = "1.0.0", serviceGroup = "gps", clientTimeout = 30000)
    private PaymentBootBizQueryFacade paymentBootBizQueryFacade;

    @HSFConsumer(serviceVersion = "1.0.0", serviceGroup = "HSF", clientTimeout = 20000)
    private ExchangeRateServiceHsf exchangeRateServiceHsf;

    @HSFConsumer(serviceVersion = "1.0.0.GLOBAL-AE", serviceGroup = "GLOBAL-AE", clientTimeout = 20000)
    private GlobalPaymentCashierMtopFacade globalPaymentCashierMtopFacade;

    @HSFConsumer(serviceVersion = "1.0.0", serviceGroup = "HSF", clientTimeout = 30000)
    private FulfillmentOrderQueryNoRoutingFacade fulfillmentOrderQueryNoRoutingFacade;

    @HSFConsumer(serviceVersion = "1.0.0", serviceGroup = "HSF", clientTimeout = 30000)
    private FulfillmentOrderStatusUpdateFacade fulfillmentOrderStatusUpdateFacade;

    @HSFConsumer(serviceVersion = "1.0.0", serviceGroup = "DUBBO", clientTimeout = 30000)
    private TpbopsArbitrationTaskService tpbopsArbitrationTaskService;

    @HSFConsumer(serviceVersion = "1.0.0.GLOBAL-AE", serviceGroup = "GLOBAL-AE", clientTimeout = 30000)
    private GlobalPaymentCashierServiceFacade globalPaymentCashierServiceFacade;

    @HSFConsumer(serviceVersion = "1.0.0", serviceGroup = "DUBBO", clientTimeout = 30000)
    private PromiseServiceForMtop promiseServiceForMtop;

    @HSFConsumer(serviceVersion = "1.0.0", serviceGroup = "HSF", clientTimeout = 30000)
    private UserAddressReadFacade userAddressReadFacade;

    @HSFConsumer(serviceVersion = "1.0.0", serviceGroup = "DUBBO", clientTimeout = 30000)
    private WlMailingAddressRemoteService wlMailingAddressRemoteService;

    @HSFConsumer(serviceVersion = "2.0.0", serviceGroup = "HSF", clientTimeout = 30000)
    private IssueInstantRefundService issueInstantRefundService;

    @HSFConsumer(serviceVersion = "1.0.0.global", serviceGroup = "HSF", clientTimeout = 30000)
    private GlobalReverseOrderQueryForSellerFacade  globalReverseOrderQueryForSellerFacade;

    @HSFConsumer(serviceVersion = "1.0.0", serviceGroup = "GLOBAL-AE", clientTimeout = 30000)
    private PaymentOpsFacade paymentOpsFacade;

    @HSFConsumer(serviceVersion = "1.0.0", serviceGroup = "HSF", clientTimeout = 30000)
    private AeIssueDisputeQueryRemoteService issueDisputeQueryRemoteService;

    @HSFConsumer(serviceVersion = "1.0.0", serviceGroup = "HSF", clientTimeout = 30000)
    private DataMigration4TestService dataMigration4TestService;

    @HSFConsumer(serviceVersion = "1.0.0", serviceGroup = "HSF", clientTimeout = 30000)
    private OrderViewFacade orderViewFacade;

    @HSFConsumer(serviceVersion = "1.0.0", serviceGroup = "HSF", clientTimeout = 30000)
    private FulfillmentOrderQueryFacade fulfillmentOrderQueryFacade;

    @HSFConsumer(serviceVersion = "1.0.0", serviceGroup = "gps", clientTimeout = 30000)
    private PaymentFacade paymentFacade;

    @HSFConsumer(serviceVersion = "1.0.0", serviceGroup = "HSF", clientTimeout = 30000)
    private OrderDataModeRemoteService orderDataModeRemoteService;

    @HSFConsumer(serviceVersion = "1.0.0", serviceGroup = "HSF", clientTimeout = 30000)
    private ICreditAssetServiceFacade iCreditAssetServiceFacade;

    @HSFConsumer(serviceVersion = "1.0.0", serviceGroup = "HSF", clientTimeout = 30000)
    private IWalletAssetServiceFacade iWalletAssetServiceFacade;

    @HSFConsumer(serviceVersion = "1.0.0", serviceGroup = "HSF", clientTimeout = 30000)
    private IMockToolsFacade iMockToolsFacade;

    @HSFConsumer(serviceVersion = "1.0.0", serviceGroup = "HSF", clientTimeout = 30000)
    private PromiseTemplateInternalService promiseTemplateInternalService;

    @HSFConsumer(serviceVersion = "1.0.0", serviceGroup = "HSF", clientTimeout = 30000)
    private SaigaCaseOperateFacade saigaCaseOperateFacade;

    @HSFConsumer(serviceVersion = "1.0.0", serviceGroup = "HSF", clientTimeout = 30000, configServerCenters = {"rg-sg-pre"})
    private ReplayService replayService;
}
