package com.aliexpress.databank.hsf.provider;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.global.qa.dc.model.builder.QueryResultBuilder;
import com.alibaba.global.qa.dc.model.param.SystemDTO;
import com.alibaba.global.qa.dc.model.result.QueryResultUnit;
import com.alibaba.global.qa.dc.model.result.ResultDTO;
import com.aliexpress.databank.constant.Constant;
import com.aliexpress.databank.hsf.MemberService;
import com.aliexpress.databank.utils.HsfUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@HSFProvider(serviceInterface = MemberService.class, serviceGroup = "HSF", serviceVersion = "1.0.0", clientTimeout = 20000)
public class MemberServiceImpl implements MemberService {

    @Override
    public ResultDTO getUserByHavanaId(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long havanaId = jsonObject.getLong(Constant.HAVANA_ID);
        JSONObject res = HsfUtil.getUserByHavanaId(havanaId);
        Map<String, QueryResultUnit> data = QueryResultBuilder.buildQueryResult("会员基本信息", null, null, res.getJSONObject("module"));
        resultDTO.setSuccess(true);
        resultDTO.setData(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
        return resultDTO;
    }

    @Override
    public ResultDTO findByAccountId(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        JSONObject res = HsfUtil.findByAccountId(buyerId);
        Map<String, QueryResultUnit> data = QueryResultBuilder.buildQueryResult("会员基本信息", null, null, res.getJSONObject("module"));
        resultDTO.setSuccess(true);
        resultDTO.setData(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
        return resultDTO;
    }

    public boolean findByAccountId(Long buyerId) throws Exception {
        JSONObject res = HsfUtil.findByAccountId(buyerId);
        if (res.isEmpty() || !res.containsKey("enabled")) {
            return false;
        }
        return res.getBoolean("enabled");
    }


    @Override
    public ResultDTO findAccount(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        JSONObject res = HsfUtil.findAccount(buyerId);
        log.info("findAccount" + "~~~~~accountInfo~~~~~~" + res.toJSONString());
        Map<String, QueryResultUnit> data = QueryResultBuilder.buildQueryResult("会员基本信息", null, null, res.getJSONObject("returnValue"));
        resultDTO.setSuccess(true);
        resultDTO.setData(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
        return resultDTO;
    }


    @Override
    public JSONObject getAccountByStr(String str) throws Exception {
        JSONObject result = new JSONObject();
        if (str.matches("^[0-9]*$")) {
            result = HsfUtil.getUserByUserId(Long.parseLong(str));
        }
        // 如果输入的是邮箱
        else if (checkMail(str)) {
            result = HsfUtil.getUserByEmail(str);
        }
        // 否则默认他输入的是logid
        else {
            result = HsfUtil.getUserByLoginId(str);
        }
        return result;
    }

    @Override
    public ResultDTO checkUser(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long buyerId = jsonObject.getLong(Constant.BUYER_ID);
        JSONObject userInfos = getAccountByStr(buyerId.toString());
        Long havanaId = userInfos.getJSONObject("module").getLong("havanaId");
        JSONObject userInfo = HsfUtil.findAccount(havanaId);
        if (userInfo.isEmpty() || !userInfo.containsKey("returnValue")) {
            log.info("~~~~~~addInto noExistList~~~~belongTo~~~~~" + userInfo.getJSONObject("returnValue").getString("belongTo"));
        } else if (userInfo.getJSONObject("returnValue").getString("belongTo").contains("13") && (userInfo.getJSONObject("returnValue").getInteger("status") != 1)) {
            log.info("~~~~~~addInto riskList~~~~belongTo~~~~~" + userInfo.getJSONObject("returnValue").getString("belongTo") + "~~~~~~addInto riskList~~~~status~~~~~" + userInfo.getJSONObject("returnValue").getString("status"));
        }
        return null;
    }

    @Override
    public ResultDTO updateUserDataTag(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        try {
            JSONObject jsonObject = JSONObject.parseObject(params);
            Long buyerId = jsonObject.getLong(Constant.SELLER_ID);
            String tag = jsonObject.getString("key");
            String value = jsonObject.getString("value");
            JSONObject userInfo = HsfUtil.saveUserTag(buyerId, tag, value);
            resultDTO.setSuccess(true);
            resultDTO.setData(userInfo.toJSONString());
            resultDTO.setMessage(userInfo.toJSONString());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return resultDTO;
    }

    @Override
    public ResultDTO deleteUserDataTag(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long buyerId = jsonObject.getLong(Constant.SELLER_ID);
        String tag = jsonObject.getString("key");
        JSONObject userInfo = HsfUtil.deleteUserTag(buyerId, tag);
        resultDTO.setSuccess(true);
        resultDTO.setData(userInfo.toJSONString());
        resultDTO.setMessage(userInfo.toJSONString());
        return resultDTO;
    }

    @Override
    public ResultDTO getSid(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO resultDTO = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long havanaId = jsonObject.getLong(Constant.HAVANA_ID);
        JSONObject request = new JSONObject();
        request.put("hid", havanaId);
        request.put("unitSupported", true);
        request.put("appName", "21272243");
        request.put("useAcitonType", true);
        request.put("gateway", "MTOP");
        JSONObject res = HsfUtil.applyLogin(request);
        String sid = res.getJSONObject("returnValue").getString("sid");
        resultDTO.setSuccess(true);
        resultDTO.setData("sid: " + sid);
        resultDTO.setMessage("sid: " + sid);
        return resultDTO;
    }

    private boolean checkMail(String email) {
        String check = "^([a-z0-9A-Z]+[-|_|\\.]?)+[a-z0-9A-Z]@([a-z0-9A-Z]+(-[a-z0-9A-Z]+)?\\.)+[a-zA-Z]{2,}$";
        Pattern regex = Pattern.compile(check);
        Matcher matcher = regex.matcher(email);
        return matcher.matches();
    }

}