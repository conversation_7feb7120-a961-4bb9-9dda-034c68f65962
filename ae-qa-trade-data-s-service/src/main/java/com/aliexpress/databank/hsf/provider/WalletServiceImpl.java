package com.aliexpress.databank.hsf.provider;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.common.lang.StringUtil;
import com.alibaba.ecommerce.module.Response;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.global.landlord.LandlordContext;
import com.alibaba.global.order.management.api.facade.OrderQueryForBuyerFacade;
import com.alibaba.global.order.management.api.model.TradeOrderDTO;
import com.alibaba.global.qa.dc.model.builder.QueryResultBuilder;
import com.alibaba.global.qa.dc.model.param.SystemDTO;
import com.alibaba.global.qa.dc.model.result.QueryResultUnit;
import com.alibaba.global.qa.dc.model.result.ResultDTO;
import com.aliexpress.databank.constant.Constant;
import com.aliexpress.databank.constant.OpenStatusEnum;
import com.aliexpress.databank.constant.WalletStatusEnum;
import com.aliexpress.databank.dataobject.UniqueRes;
import com.aliexpress.databank.filter.UserCallTracker;
import com.aliexpress.databank.hsf.DataPoolService;
import com.aliexpress.databank.hsf.MemberService;
import com.aliexpress.databank.hsf.PaymentService;
import com.aliexpress.databank.hsf.WalletService;
import com.aliexpress.databank.utils.HsfUtil;
import com.aliexpress.databank.utils.RandomNum;
import com.aliexpress.wallet.api.asset.facade.ICreditAssetServiceFacade;
import com.aliexpress.wallet.api.asset.facade.IMockToolsFacade;
import com.aliexpress.wallet.api.asset.facade.IWalletAssetServiceFacade;
import com.aliexpress.wallet.api.asset.request.*;
import com.aliexpress.wallet.api.asset.request.dto.ActivityDTO;
import com.aliexpress.wallet.api.asset.request.dto.EquityDTO;
import com.aliexpress.wallet.api.asset.request.dto.RefundDTO;
import com.aliexpress.wallet.api.asset.request.mocktools.CarryForwardRefundStatusReq;
import com.aliexpress.wallet.api.asset.response.BonusBalanceQueryResp;
import com.aliexpress.wallet.api.asset.response.BonusIssueResp;
import com.aliexpress.wallet.api.asset.response.CreditAssetFlowQueryResp;
import com.aliexpress.wallet.api.asset.response.dto.CreditAssetPaymentFlowDTO;
import com.aliexpress.wallet.api.asset.response.dto.CreditAssetPaymentRecordDTO;
import com.aliexpress.wallet.api.common.WalletServicePage;
import com.aliexpress.wallet.api.common.WalletServiceResult;
import com.aliexpress.wallet.api.member.facade.IWalletMemberServiceFacade;
import com.aliexpress.wallet.api.member.facade.IWalletMemberTagServiceFacade;
import com.aliexpress.wallet.api.member.request.WalletStatusReq;
import com.aliexpress.wallet.api.member.response.WalletStatusResp;
import com.taobao.diamond.client.Diamond;
import com.taobao.eagleeye.EagleEye;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

import static com.aliexpress.databank.constant.Constant.REPAID_PERIOD;
import static com.aliexpress.databank.constant.Constant.REPAID_REFUND_COMPLETE;


@HSFProvider(serviceInterface = WalletService.class, serviceGroup = "HSF", serviceVersion = "1.0.0", clientTimeout = 20000)
public class WalletServiceImpl implements WalletService {

    private static final Logger logger = LoggerFactory.getLogger(WalletServiceImpl.class);


    //
    @Autowired
    private IWalletMemberServiceFacade iWalletMemberServiceFacade;

    @Autowired
    private IWalletMemberTagServiceFacade iWalletMemberTagServiceFacade;

    @Autowired
    private IWalletAssetServiceFacade iWalletAssetServiceFacade;

    @Autowired
    private DataPoolService dataPoolService;

    @Autowired
    private PaymentService paymentService;

    @Autowired
    private MemberService memberService;

    @Autowired
    private ICreditAssetServiceFacade iCreditAssetServiceFacade;

    @Autowired
    private OrderQueryForBuyerFacade orderQueryForBuyerFacade;

    @Autowired
    private IMockToolsFacade iMockToolsFacade;

    /**
     * 添加pad白名单
     * @param params
     * @param systemDTO
     * @return
     * @throws Exception
     */
    @Override
    public ResultDTO addRiskWhitelist(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();

        LandlordContext.setTenantSpec("AE_GLOBAL", -1);

        JSONObject jsonObject = JSONObject.parseObject(params);
        String userStr = jsonObject.getString("userStr");
        JSONObject  json = memberService.getAccountByStr(userStr);
        try{
            if (json != null && !json.isEmpty()) {
                String userId = json.getJSONObject("module").getString("userId");
                Boolean isTest = dataPoolService.checkTestAccount(Long.parseLong(userId));
                if (!isTest){
                    result.setData("非测试账号不可操作");
                    result.setSuccess(false);
                    return result;
                }
                WalletServiceResult walletServiceResult= iWalletMemberTagServiceFacade.initOrUpdateKeyConfig("PAD_OPEN_WHITE_USER_LIST",userId,"1",0);
                if(walletServiceResult.isSuccess()){
                    result.setData("添加加pad白名单成功");
                }
            }
        }catch (Exception e){
            result.setData("add failed");
        }
        result.setSuccess(true);
        return result;
    }

    /**
     * 注销pad用户
     * @param params
     * @param systemDTO
     * @return
     * @throws Exception
     */
    @Override
    public ResultDTO deleteWalletUser(String params, SystemDTO systemDTO) throws Exception {
        ResultDTO result = new ResultDTO();

        Map<String, QueryResultUnit> data = new LinkedHashMap<>();
        LandlordContext.setTenantSpec("AE_GLOBAL", -1);


        JSONObject jsonObject = JSONObject.parseObject(params);
        String userStr = jsonObject.getString("userStr");
        JSONObject  json = memberService.getAccountByStr(userStr);
        try{
            if (json != null && !json.isEmpty()) {
                String userId = json.getJSONObject("module").getString("userId");
                Boolean isTest = dataPoolService.checkTestAccount(Long.parseLong(userId));
                if (!isTest){
                    result.setData("非测试账号不可操作");
                    result.setSuccess(false);
                    return result;
                }
                WalletStatusReq walletStatusReq = new WalletStatusReq();
                walletStatusReq.setMerchantKey("AE");
                walletStatusReq.setUserId(userId);
                walletStatusReq.setNeedPhone(true);
                WalletServiceResult<WalletStatusResp> walletServiceResult =
                    iWalletMemberServiceFacade.queryWalletStatus(walletStatusReq);
                if (walletServiceResult.isSuccess()) {
                  if (walletServiceResult.getResult().getWalletStatus() == 2 && !"BR".equals(walletServiceResult.getResult().getCountryCode())) {
                    result.setData("账号未开通钱包，无需删除");
                    result.setSuccess(true);
                    return result;
                  }
                  Long memberId = walletServiceResult.getResult().getMemberId();
                  String phone = walletServiceResult.getResult().getWalletPhone();
                  String areaCode = walletServiceResult.getResult().getWalletPhoneAreaCode();
                  String countryCode = walletServiceResult.getResult().getCountryCode();
                  // 删除
                  iWalletMemberServiceFacade.deleteMemberInfo(
                      memberId, "AE", userId, phone, areaCode, countryCode);
                  result.setData("注销success");
                } else {
                  result.setData("获取钱包信息异常");
                }
            }
        }catch (Exception e){
            e.printStackTrace();
            result.setData("delete failed");
        }
        result.setSuccess(true);
        return result;

    }

    @Override
    public ResultDTO arrivalRepaid(String params, SystemDTO systemDTO){
        ResultDTO result = new ResultDTO();

        Map<String, QueryResultUnit> data = new LinkedHashMap<>();
        LandlordContext.setTenantSpec("AE_GLOBAL", -1);

        JSONObject jsonObject = JSONObject.parseObject(params);
        //电商订单号拿到电商支付单号
        String mockType = jsonObject.getString("mockType").split(":")[1];
        switch (mockType){
            case REPAID_PERIOD:
                return arrivalRepaidPeriod(params,systemDTO);
            case REPAID_REFUND_COMPLETE:
                return carryForwardRefundStatus(params,systemDTO);
            default:
                result.setSuccess(true);
                result.setData("不支持的mock类型");
        }
        return null;
    }

    /**
     * 推进待还款期
     * @param params
     * @param systemDTO
     * @return
     * @throws Exception
     */
    @Override
    public ResultDTO arrivalRepaidPeriod(String params, SystemDTO systemDTO){
        ResultDTO result = new ResultDTO();

        Map<String, QueryResultUnit> data = new LinkedHashMap<>();
        LandlordContext.setTenantSpec("AE_GLOBAL", -1);

        JSONObject jsonObject = JSONObject.parseObject(params);
        //电商订单号拿到电商支付单号
        String orderIdStr = jsonObject.getString("orderIdStr");
        try{
            UniqueRes uniqueRes = paymentService.getPayInstructionNoByOrderId(orderIdStr);
            if (uniqueRes == null || StringUtil.isBlank(uniqueRes.getUniqueValue())){
                result.setSuccess(true);
                result.setData("请检查输入正确的电商订单号");
                return result;
            }
            CreditAssetArrivalRepayPeriodReq creditAssetArrivalRepayPeriodReq = new CreditAssetArrivalRepayPeriodReq();
            creditAssetArrivalRepayPeriodReq.setCheckRepayPeriod(false);
            creditAssetArrivalRepayPeriodReq.setMerchantKey("AE");
            creditAssetArrivalRepayPeriodReq.setUserId(uniqueRes.getPayerId().toString());
            creditAssetArrivalRepayPeriodReq.setMemberId(getMemberIdByUserId(uniqueRes.getPayerId().toString()));
            creditAssetArrivalRepayPeriodReq.setPaymentId(uniqueRes.getUniqueValue());
            WalletServiceResult<Void> serviceResult = iCreditAssetServiceFacade.arrivalRepayPeriod(creditAssetArrivalRepayPeriodReq);
            if (serviceResult.isSuccess()){
                result.setSuccess(true);
                result.setData("推进待还款期成功");
                return result;
            }
            else {
                result.setSuccess(true);
                result.setData("arrival failed");
            }

        }catch (Exception e){
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        return result;
    }


    /**
     * 推进退款完成
     * @param params
     * @param systemDTO
     * @return
     * @throws Exception
     */
    @Override
    public ResultDTO carryForwardRefundStatus(String params, SystemDTO systemDTO) {
        ResultDTO result = new ResultDTO();

        Map<String, QueryResultUnit> data = new LinkedHashMap<>();
        LandlordContext.setTenantSpec("AE_GLOBAL", -1);

        JSONObject jsonObject = JSONObject.parseObject(params);
        String repayOrderIdStr = jsonObject.getString("orderIdStr");
        try{
            String userId = jsonObject.getString(Constant.BUYER_ID);
            JSONObject repayRefundInfo = HsfUtil.queryRefundInstruction(repayOrderIdStr,Long.parseLong(userId));
            String outUniqueSeq = "";
            if (!repayRefundInfo.isEmpty() && repayRefundInfo.getJSONArray("records").size()>0){
                JSONArray list = repayRefundInfo.getJSONArray("records");
                for(int i=0;i<list.size();i++){
                    if ("SUCCEEDED".equals(list.getJSONObject(i).getString("status"))){
                        outUniqueSeq = list.getJSONObject(i).getString("requestId").split("#")[0];
                    }
                }
            }
            CarryForwardRefundStatusReq carryForwardRefundStatusReq = new CarryForwardRefundStatusReq();
            carryForwardRefundStatusReq.setMemberId(getMemberIdByUserId(userId));
            carryForwardRefundStatusReq.setMerchantKey("AE");
            carryForwardRefundStatusReq.setUserId(userId);
            carryForwardRefundStatusReq.setOutUniqueSeq(outUniqueSeq);
            WalletServiceResult<Void> serviceResult = iMockToolsFacade.carryForwardRefundStatus(carryForwardRefundStatusReq);
            if (serviceResult.isSuccess()){
                result.setSuccess(true);
                result.setData("推进退款完成：成功");
                return result;
            }

        }catch (Exception e){
            e.printStackTrace();
            throw new RuntimeException(e);
        }
        result.setSuccess(true);
        result.setData("carry failed");
        return result;
    }


    /**
     * 获取钱包信息
     * @param params
     * @param systemDTO
     * @return
     * @throws Exception
     */
    @Override
    public ResultDTO getWalletInfo(String params, SystemDTO systemDTO) throws Exception{
        ResultDTO result = new ResultDTO();
        Map<String, QueryResultUnit> data = new LinkedHashMap<>();
        LandlordContext.setTenantSpec("AE_GLOBAL", -1);

        JSONObject jsonObject = JSONObject.parseObject(params);
        String userStr = jsonObject.getString("userStr");
        JSONObject  json = memberService.getAccountByStr(userStr);
        if (json != null && !json.isEmpty()){
            String userId = json.getJSONObject("module").getString("userId");
            WalletStatusReq walletStatusReq=new WalletStatusReq();
            walletStatusReq.setMerchantKey("AE");
            walletStatusReq.setUserId(userId);
            walletStatusReq.setNeedPhone(true);
            WalletServiceResult<WalletStatusResp> walletServiceResult= iWalletMemberServiceFacade.queryWalletStatus(walletStatusReq);
            if (walletServiceResult.isSuccess()){
                JSONObject res = new JSONObject();
                res.put("userId",userId);
                res.put("钱包id",walletServiceResult.getResult().getMemberId());
                res.put("钱包状态", WalletStatusEnum.getNameByNum(walletServiceResult.getResult().getWalletStatus()));
                res.put("pad状态",WalletStatusEnum.getNameByNum(walletServiceResult.getResult().getWalletPadStatus()));
                if (walletServiceResult.getResult().getWalletPhoneAreaCode() != null){
                    res.put("钱包手机号码",walletServiceResult.getResult().getWalletPhoneAreaCode() + "+" + walletServiceResult.getResult().getWalletPhone());
                }
                res.put("钱包国家", walletServiceResult.getResult().getCountryCode());
                res.put("测试账号", dataPoolService.checkTestAccount(Long.parseLong(userId))?"是":"否");
                Map<String, QueryResultUnit> info = QueryResultBuilder.buildQueryResult("AE钱包信息", null, null, res);
                data.putAll(info);
                res = new JSONObject();
                res.put("ipay钱包状态", OpenStatusEnum.getNameByType(walletServiceResult.getResult().getOpenIpayWallet()));
                res.put("ipay余额状态", OpenStatusEnum.getNameByType(walletServiceResult.getResult().getOpenIpayBalance()));
                res.put("alipayUserId", walletServiceResult.getResult().getAlipayUserId());
                Map<String, QueryResultUnit> ipayInfo = QueryResultBuilder.buildQueryResult("IPAY钱包信息", null, null, res);
                data.putAll(ipayInfo);
                //bonus金额
                JSONObject bonusJson = getBonusInfo(walletServiceResult.getResult().getMemberId(),userId);
                if (!bonusJson.isEmpty()){
                    Map<String, QueryResultUnit> bonus = QueryResultBuilder.buildQueryResult("bonus金额信息", null, null, bonusJson);
                    data.putAll(bonus);
                }
                result.setData(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
            }
        }
        result.setSuccess(true);
        return result;
    }

    /**
     * 获取订单额度占用情况
     * @param params
     * @param systemDTO
     * @return
     * @throws Exception
     */
    @Override
    public ResultDTO getPADAmount(String params, SystemDTO systemDTO) throws Exception{
        ResultDTO result = new ResultDTO();
        Map<String, QueryResultUnit> data = new LinkedHashMap<>();
        LandlordContext.setTenantSpec("AE_GLOBAL", -1);

        JSONObject jsonObject = JSONObject.parseObject(params);
        String orderStr = jsonObject.getString("orderStr");
        String buyerId = "";
        Response<TradeOrderDTO> tradeOrderDTOResponse = orderQueryForBuyerFacade.queryTradeOrderById(Long.parseLong(orderStr));
        if (tradeOrderDTOResponse.isSuccess()){
            buyerId = tradeOrderDTOResponse.getModule().getBuyer().getBuyerId().toString();
        }

        // 得memberId
        Long memberId = getMemberIdByUserId(buyerId);
        if (memberId == null){
            result.setSuccess(false);
            result.setMessage("用户不存在");
            return result;
        }
        // 得支付单
        JSONArray payInstructionNo = getPayInstructionNo(orderStr);
        if (payInstructionNo.isEmpty()){
            result.setSuccess(false);
            result.setMessage("订单无支付信息或订单号不存在");
            return result;
        }
        for(int i =0;i<payInstructionNo.size();i++){
            JSONObject res = getAmount(payInstructionNo.getString(i),buyerId,memberId);
            Map<String, QueryResultUnit> temp = QueryResultBuilder.buildQueryResult("PAD支付单"+ i, null, null, res);
            data.putAll(temp);
        }
        result.setData(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
        result.setSuccess(true);
        return result;
    }

    private JSONObject getBonusInfo(Long memberId, String userId){
        JSONObject res = new JSONObject();
        BonusBalanceQueryReq bonusBalanceQueryReq  = new BonusBalanceQueryReq();
        bonusBalanceQueryReq.setMemberId(memberId);
        bonusBalanceQueryReq.setUserId(userId);
        bonusBalanceQueryReq.setMerchantKey("AE");
        bonusBalanceQueryReq.setLang("en_US");
        WalletServiceResult<BonusBalanceQueryResp> bonusBalance = iWalletAssetServiceFacade.getBonusBalance(bonusBalanceQueryReq);
        if (bonusBalance.isSuccess()){
            res.put("剩余可用bonus",bonusBalance.getResult().getAvailableAmount());
            res.put("待发bonus",bonusBalance.getResult().getPendingAmount());
            res.put("总bonus",bonusBalance.getResult().getTotalAmount());
            res.put("冻结bonus",bonusBalance.getResult().getFrozenAmount());
            res.put("即将过期bonus",bonusBalance.getResult().getExpiringAmount());
        }
        return res;
    }

    /**
     * 逾期pad不可用加白
     * @param params
     * @param systemDTO
     * @return
     * @throws Exception
     */
    @Override
    public ResultDTO setPADWhiteList(String params, SystemDTO systemDTO) throws Exception{
        ResultDTO result = new ResultDTO();
        Map<String, QueryResultUnit> data = new LinkedHashMap<>();
        LandlordContext.setTenantSpec("AE_GLOBAL", -1);

        JSONObject jsonObject = JSONObject.parseObject(params);
        String userStr = jsonObject.getString("userStr");
        JSONObject  json = memberService.getAccountByStr(userStr);
        if (json == null || json.isEmpty()){
            result.setData("查询用户信息异常");
            result.setSuccess(false);
            return result;
        }
        String userId = json.getJSONObject("module").getString("userId");
        Boolean isTest = dataPoolService.checkTestAccount(Long.parseLong(userId));
        if (!isTest){
            result.setData("非测试账号不可操作");
            result.setSuccess(false);
            return result;
        }
        WalletStatusReq walletStatusReq=new WalletStatusReq();
        walletStatusReq.setMerchantKey("AE");
        walletStatusReq.setUserId(userId);
        walletStatusReq.setNeedPhone(true);
        WalletServiceResult<WalletStatusResp> walletServiceResult= iWalletMemberServiceFacade.queryWalletStatus(walletStatusReq);
        if (!walletServiceResult.isSuccess()){
            result.setData("查询钱包信息异常");
            result.setSuccess(false);
            return result;
        }
        Long memberId = walletServiceResult.getResult().getMemberId();
        CreditAssetStatusRefreshReq creditAssetStatusRefreshReq = new CreditAssetStatusRefreshReq();
        creditAssetStatusRefreshReq.setMemberId(memberId);
        creditAssetStatusRefreshReq.setChangeType("Enable");
        WalletServiceResult<Void> walletServiceResult1 = iCreditAssetServiceFacade.refreshStatus(creditAssetStatusRefreshReq);
        if (walletServiceResult1.isSuccess()){
            result.setData("设置逾期白名单成功");
            result.setSuccess(true);
            return result;
        }
        else {
            result.setData("设置逾期白名单异常");
            result.setSuccess(false);
            return result;
        }
    }




    private JSONObject getAssetInfo(Long memberId){
        return null;
    }

    /**
     * 通过userId得memberId
     * @param buyerId
     * @return
     */
    @Override
    public Long getMemberIdByUserId(String buyerId){
        if (StringUtil.isNotBlank(buyerId)){
            WalletStatusReq walletStatusReq = new WalletStatusReq();
            walletStatusReq.setMerchantKey("AE");
            walletStatusReq.setUserId(buyerId);
            // 取钱包信息
            WalletServiceResult<WalletStatusResp>  walletServiceResult = iWalletMemberServiceFacade.queryWalletStatus(walletStatusReq);
            if (!walletServiceResult.isSuccess()){
                return null;
            }
            else {
                return walletServiceResult.getResult().getMemberId();
            }
        }
        return null;
    }


    /**
     * 取支付单号
     * @param orderId
     * @return
     * @throws Exception
     */
    private JSONArray getPayInstructionNo(String orderId) throws Exception{
        // 取支付单
        JSONArray payNoList = new JSONArray();
        JSONObject res = HsfUtil.queryBatchCheckoutOrder(orderId);
        if (null != res && res.containsKey("records") && !res.getJSONArray("records").isEmpty()){
            String checkoutOrderNo = res.getJSONArray("records").getJSONObject(0).getString("checkoutOrderNo");
            if (StringUtil.isNotBlank(checkoutOrderNo)){
                JSONObject payJson = HsfUtil.queryPayInstruction(checkoutOrderNo);
                if (null != payJson && payJson.containsKey("records") && !payJson.getJSONArray("records").isEmpty()){
                    JSONArray records = payJson.getJSONArray("records");
                    for(int i=0;i<records.size();i++){
                        payNoList.add(records.getJSONObject(i).getString("instructionNo"));
                    }
                }
            }
        }
        return payNoList;
    }

    /**
     * 无还款计划获取额度信息
     * @param instructionNo
     * @param buyerId
     * @param memberId
     * @return
     * @throws Exception
     */
    private String getFreezeAmount(String instructionNo,String buyerId, Long memberId)throws Exception{
        CreditAssetPaymentQueryReq creditAssetPaymentQueryReq = new CreditAssetPaymentQueryReq();
        creditAssetPaymentQueryReq.setMerchantKey("AE");
        creditAssetPaymentQueryReq.setUserId(buyerId);
        creditAssetPaymentQueryReq.setMemberId(memberId);
        creditAssetPaymentQueryReq.setPayInstructionNo(instructionNo);
        creditAssetPaymentQueryReq.setRecordTypes(Arrays.asList("Pending","ToBeRepaid","Cancel","Repaid"));
        creditAssetPaymentQueryReq.setLang("en_US");
        WalletServiceResult<WalletServicePage<CreditAssetPaymentRecordDTO>> temp = iCreditAssetServiceFacade.queryPaymentRecords(creditAssetPaymentQueryReq,1,10);
        if (temp.isSuccess()){
            List<CreditAssetPaymentRecordDTO> list = temp.getResult().getContents();
            for(CreditAssetPaymentRecordDTO creditAssetPaymentRecordDTO: list){
                if ("Pending".equals(creditAssetPaymentRecordDTO.getType())){
                    return creditAssetPaymentRecordDTO.getToBePaidAmount().toString();
                }
            }
        }
        return null;
    }

    /**
     * 创建还款计划后获取额度信息
     * @param instructionNo
     * @param buyerId
     * @param memberId
     * @return
     * @throws Exception
     */
    private JSONObject getAmountJson(String instructionNo,String buyerId, Long memberId)throws Exception{
        JSONObject res = new JSONObject();
        CreditAssetPaymentQueryReq creditAssetPaymentQueryReq = new CreditAssetPaymentQueryReq();
        creditAssetPaymentQueryReq.setPayInstructionNo(instructionNo);
        creditAssetPaymentQueryReq.setMemberId(memberId);
        creditAssetPaymentQueryReq.setUserId(buyerId);
        creditAssetPaymentQueryReq.setMerchantKey("AE");
        WalletServiceResult<CreditAssetPaymentRecordDTO> walletServiceResult = iCreditAssetServiceFacade.queryRepaymentPlan(creditAssetPaymentQueryReq);
        if (walletServiceResult.isSuccess()){
            String repayPaymentPlan = walletServiceResult.getResult().getRepaymentPlanId();
            CreditAssetPaymentFlowQueryReq creditAssetPaymentFlowQueryReq = new CreditAssetPaymentFlowQueryReq();
            creditAssetPaymentFlowQueryReq.setRepaymentPlanId(repayPaymentPlan);
            creditAssetPaymentFlowQueryReq.setTypes(Arrays.asList("Create", "Adjust", "PayRefundApply", "PayConfirmProcess"));
            creditAssetPaymentFlowQueryReq.setMemberId(memberId);
            creditAssetPaymentFlowQueryReq.setUserId(buyerId);
            creditAssetPaymentFlowQueryReq.setMerchantKey("AE");
            WalletServiceResult<List<CreditAssetPaymentFlowDTO>> listWalletServiceResult = iCreditAssetServiceFacade.queryRepaymentFlow(creditAssetPaymentFlowQueryReq);
            if (listWalletServiceResult.isSuccess()){
                List<CreditAssetPaymentFlowDTO> list = listWalletServiceResult.getResult();
                for(CreditAssetPaymentFlowDTO creditAssetPaymentFlowDTO: list){

                    if ("Create".equals(creditAssetPaymentFlowDTO.getType())){
                        res.put("冻结额度", creditAssetPaymentFlowDTO.getAmountChange());
                    }
                    else if("Adjust".equals(creditAssetPaymentFlowDTO.getType())){
                        res.put("还款前退回额度",creditAssetPaymentFlowDTO.getAmountChange());
                    }
                    else if("PayConfirmProcess".equals(creditAssetPaymentFlowDTO.getType())){
                        res.put("还款后解冻额度",creditAssetPaymentFlowDTO.getAmountChange());
                    }
                }
            }
        }
        return res;
    }

    private JSONObject getAmount(String instructionNo,String buyerId, Long memberId)throws Exception{
        JSONObject res = new JSONObject();
        res.put("支付单号",instructionNo);
        // 额度流水-查询冻结额度
        CreditAsseFlowQueryReq creditAsseFlowQueryReq = new CreditAsseFlowQueryReq();
        creditAsseFlowQueryReq.setFlowType("UnFreeze");
        creditAsseFlowQueryReq.setPaymentId(instructionNo);
        creditAsseFlowQueryReq.setMemberId(memberId);
        creditAsseFlowQueryReq.setUserId(buyerId);
        creditAsseFlowQueryReq.setMerchantKey("AE");
        WalletServiceResult<CreditAssetFlowQueryResp> assetFlowQueryRespWalletServiceResult = iCreditAssetServiceFacade.queryCreditAssetFlow(creditAsseFlowQueryReq);
        if (!assetFlowQueryRespWalletServiceResult.isSuccess()){
            return null;
        }
        else if(null == assetFlowQueryRespWalletServiceResult.getResult()){
            res = getAmountJson(instructionNo,buyerId,memberId);
            res.put("支付单号",instructionNo);
        }
        // 无冻结金额，订单支用失败或取消
        else if(assetFlowQueryRespWalletServiceResult.getResult() != null){
            res.put("解冻额度",assetFlowQueryRespWalletServiceResult.getResult().getFreezeAmountChange());
            res.put("解冻原因","支用失败或支用取消");
            creditAsseFlowQueryReq.setFlowType("Freeze");
            assetFlowQueryRespWalletServiceResult = iCreditAssetServiceFacade.queryCreditAssetFlow(creditAsseFlowQueryReq);
            res.put("冻结额度",assetFlowQueryRespWalletServiceResult.getResult().getFreezeAmountChange());
        }

        return res;

    }
    private final UserCallTracker tracker = new UserCallTracker(10, 86400000);
    @Override
    public ResultDTO mockBonus(String params, SystemDTO systemDTO) throws Exception {
        String empId = systemDTO.getOperator();
        ResultDTO result = new ResultDTO();
        JSONObject jsonObject = JSONObject.parseObject(params);
        Long amount = jsonObject.getLong("issueAmount");
        Long expiredTime = jsonObject.getLong("expiredTime");
        String currencyCode = jsonObject.getString("currency");
        String mockResult;
        //        System.out.println(equityID);
        String input = jsonObject.getString("userInput");
        JSONObject json = memberService.getAccountByStr(input);
        String config = Diamond.getConfig("com.alibaba.ae.qa.databank.auth.bonus", "ae-qa-databank", 30000);
        List<String> whitelist = Arrays.asList(config.split(","));
        boolean success = tracker.recordCall(empId);
        if (!success){
            throw new Exception("using limit reached, please wait for 24h and use again");
        }
        logger.info("empId: "+ empId + ", issue amount: " + amount);
        if (!whitelist.contains(empId)) {
            throw new NullPointerException("Unknown exception");
        }
        if (amount != null && amount > 3000L){
            throw new Exception("issue amount exceeds upper limit: 3000");
        }
        try {
            if (json != null && !json.isEmpty()) {
                String userID = json.getJSONObject("module").getString("userId");
                // 根据userID反查钱包会员ID
                BonusIssueReq bonusIssueReq = new BonusIssueReq();
                Long memberID = getMemberIdByUserId(userID);
                if (memberID != null) {
                    EquityDTO equityDTO = new EquityDTO();
                    ActivityDTO activityDTO = new ActivityDTO();
                    RefundDTO refundDTO = new RefundDTO();
                    bonusIssueReq.setMerchantKey("AE");
                    // 生成16位不重复权益ID
                    String equityID = userID + "_855e12b442e9427da11045cef19eff6f_" + RandomNum.getRandom(16) ;
                    Boolean isTest = dataPoolService.checkTestAccount(Long.parseLong(userID));

                    // 判断输入ID是否是测试用户
                    if (!isTest) {
                        result.setSuccess(false);
                        mockResult = "Input id is not a test account";
                        result.setMessage(mockResult);
                        // 限制每次发放的bonus金额
//                    } else if (amount > 10000) {
//                        result.setSuccess(false);
//                        mockResult = "Issue amount exceeds the limit";
//                        result.setMessage(mockResult);
                    } else {
                        bonusIssueReq.setEnableMock(true);
                        bonusIssueReq.setMemberId(memberID);
                        bonusIssueReq.setUserId(userID);
                        // sourceID约定写死
                        equityDTO.setSourceId("****************");
                        equityDTO.setAmount(amount);
                        equityDTO.setEquityId(equityID);
                        equityDTO.setCurrencyCode(currencyCode);
                        // 设置bonus有效期起始时间
                        equityDTO.setEffectiveTime(System.currentTimeMillis());
                        if (expiredTime != null) {
                            // 设置指定过期时间
                            equityDTO.setExpireTime(System.currentTimeMillis() + 86400L * expiredTime * 1000);
                        } else {
                            // 设置默认过期时间24h
                            equityDTO.setExpireTime(equityDTO.getEffectiveTime() + 86400L * 1000);
                        }
                        bonusIssueReq.setEquityDTO(equityDTO);
                        bonusIssueReq.setRefundDTO(refundDTO);
                        bonusIssueReq.setActivityDTO(activityDTO);
                        WalletServiceResult<BonusIssueResp> walletServiceResult = iWalletAssetServiceFacade.issueBonus(bonusIssueReq);
                        if (walletServiceResult.isSuccess()) {
                            result.setData("Bonus has been issued, item id is" + walletServiceResult.getResult().getItemId());
                            result.setSuccess(true);
                            mockResult = "Mock bonus has been issued";
                            result.setMessage(mockResult);
                        } else {
                            mockResult = "bonus发放失败";
                            result.setData(JSONObject.toJSONString(bonusIssueReq, SerializerFeature.WriteDateUseDateFormat));
                            result.setMessage(String.valueOf(walletServiceResult));
                        }
                    }
                } else {
                    mockResult = "会员信息查询异常";
                    result.setData(mockResult);
                    result.setMessage(mockResult);
                }
            }
        }catch (Exception exception){
            result.setSuccess(false);
            mockResult = "mock bonus failed";
            result.setMessage(mockResult);
        }
        return result;
    }

    @Override
    public ResultDTO getPadInfo(String params, SystemDTO systemDTO){

        return new ResultDTO();
    }


    @Override
    public ResultDTO bindCardAssetOffline(String params, SystemDTO systemDTO){
        ResultDTO result = new ResultDTO();
        try{
            JSONObject jsonObject = JSONObject.parseObject(params);
            String input = jsonObject.getString("userInput");
            String cardNo = jsonObject.getString("cardNo");
            String brand = jsonObject.getString("brand");
            String countryIssue = jsonObject.getString("countryIssue");
            String targetIp = jsonObject.getString("targetIp");
            JSONObject json = memberService.getAccountByStr(input);
            String userId = json.getJSONObject("module").getString("userId");
            Boolean isTest = dataPoolService.checkTestAccount(Long.parseLong(userId));
            if (!isTest){
                result.setData("非测试账号不可操作");
                result.setSuccess(false);
                return result;
            }
            WalletStatusReq walletStatusReq = new WalletStatusReq();
            walletStatusReq.setMerchantKey("AE");
            walletStatusReq.setUserId(userId);
            walletStatusReq.setNeedPhone(true);
            WalletServiceResult<WalletStatusResp> walletServiceResult =
                    iWalletMemberServiceFacade.queryWalletStatus(walletStatusReq);
            if (walletServiceResult.isSuccess()) {
                if (walletServiceResult.getResult().getMemberId() == null) {
                    result.setData("账号未开通钱包，无法添加aepay卡");
                    result.setSuccess(true);
                    return result;
                }
                Long memberId = walletServiceResult.getResult().getMemberId();
                String alipayUserId = walletServiceResult.getResult().getAlipayUserId();
                JSONObject res = HsfUtil.bindCardAssetOffline(memberId, cardNo,brand, countryIssue,targetIp);
                if(res == null || !res.getBoolean("success") || !res.getBoolean("result")){
                    result.setData("添加aepay卡失败：" + EagleEye.getTraceId());
                    result.setSuccess(true);
                    return result;
                }
                JSONObject queryRes = HsfUtil.queryBindCardAssets(memberId,alipayUserId,targetIp);
                if(queryRes == null || !res.getBoolean("success") || res.getJSONArray("cardAssetDetailList").size()==0){
                    result.setData("添加aepay卡成功，但查询卡列表信息失败/卡列表为空：" + EagleEye.getTraceId());
                    result.setSuccess(true);
                    return result;
                }
                JSONObject card = new JSONObject();
                JSONArray cardList = new JSONArray();
                cardList = res.getJSONArray("cardAssetDetailList");
                for(int i=0;i<cardList.size();i++){
                    String maskCardNo = cardList.getJSONObject(i).getJSONObject("cardDetail").getString("maskCardNo");
                    String[] temp = maskCardNo.split("\\*\\*\\*\\*\\*\\*");
                    if (temp.length==2 && cardNo.contains(temp[0]) && cardNo.contains(temp[1])){
                        String cardToken = cardList.getJSONObject(i).getJSONObject("cardDetail").getString("cardToken");
                        String ipayToken = cardList.getJSONObject(i).getJSONObject("cardDetail").getJSONObject("additionalInfo").getString("ipayToken");
                        card.put("cardToken",cardToken);
                        card.put("ipayToken",ipayToken);
                        if (StringUtil.isNotBlank(cardToken) && StringUtil.isNotBlank(ipayToken) && !"null".equalsIgnoreCase(cardToken)&& !"null".equalsIgnoreCase(ipayToken)){
                            card.put("绑卡情况","aepay&ipay双绑卡");
                        }
                        else if(StringUtil.isNotBlank(cardToken) && StringUtil.isBlank(ipayToken) && !"null".equalsIgnoreCase(cardToken)&& "null".equalsIgnoreCase(ipayToken)){
                            card.put("绑卡情况","aepay单绑卡");
                        }else if(StringUtil.isBlank(cardToken) && StringUtil.isNotBlank(ipayToken) && "null".equalsIgnoreCase(cardToken)&& "null".equalsIgnoreCase(ipayToken)){
                            card.put("绑卡情况","ipay单绑卡");
                        } else{
                            card.put("绑卡情况","绑卡异常");
                        }
                        result.setData(JSONObject.toJSONString(card, SerializerFeature.WriteDateUseDateFormat));
                        result.setSuccess(true);
                        return result;
                    }
                }
            }
            result.setData("绑卡兜底报错");
            result.setSuccess(true);
            return result;
        }catch (Exception e){
            result.setMessage("绑卡报错");
            logger.info(e.getMessage());
            result.setSuccess(true);
            return result;
        }
    }

    @Override
    public ResultDTO queryBindCard(String params, SystemDTO systemDTO){
        ResultDTO result = new ResultDTO();
        try{
            JSONObject jsonObject = JSONObject.parseObject(params);
            String input = jsonObject.getString("userInput");
            String targetIp = jsonObject.getString("targetIp");
            JSONObject json = memberService.getAccountByStr(input);
            String userId = json.getJSONObject("module").getString("userId");
            Boolean isTest = dataPoolService.checkTestAccount(Long.parseLong(userId));
            if (!isTest){
                result.setData("非测试账号不可操作");
                result.setSuccess(false);
                return result;
            }
            WalletStatusReq walletStatusReq = new WalletStatusReq();
            walletStatusReq.setMerchantKey("AE");
            walletStatusReq.setUserId(userId);
            walletStatusReq.setNeedPhone(true);
            WalletServiceResult<WalletStatusResp> walletServiceResult =
                    iWalletMemberServiceFacade.queryWalletStatus(walletStatusReq);
            if (walletServiceResult.isSuccess()) {
                if (walletServiceResult.getResult().getMemberId() == null) {
                    result.setData("账号未开通钱包，无法添加aepay卡");
                    result.setSuccess(true);
                    return result;
                }
                Long memberId = walletServiceResult.getResult().getMemberId();
                String alipayUserId = walletServiceResult.getResult().getAlipayUserId();
                JSONObject queryRes = HsfUtil.queryBindCardAssets(memberId,alipayUserId,targetIp);
                if (queryRes != null){
                    Map<String, QueryResultUnit> data = new LinkedHashMap<>();
                    Map<String, QueryResultUnit> paymentInstructionDetail = QueryResultBuilder.buildQueryResult("卡信息", null, null, queryRes);
                    data.putAll(paymentInstructionDetail);
                    result.setData(JSONObject.toJSONString(data, SerializerFeature.WriteDateUseDateFormat));
                    result.setSuccess(true);
                    return result;
                }
            }
            result.setMessage("查询报错");
            result.setSuccess(true);
            return result;
        }catch (Exception e){
            result.setMessage("查询异常");
            result.setSuccess(true);
            logger.info(e.getMessage());
            return result;
        }
    }
}
