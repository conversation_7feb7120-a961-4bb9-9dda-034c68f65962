package com.aliexpress.databank.hsf.provider;

import com.alibaba.boot.hsf.annotation.HSFProvider;
import com.alibaba.ecommerce.module.Response;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.global.order.management.api.facade.OrderQueryForBuyerFacade;
import com.alibaba.global.order.management.api.model.TradeOrderDTO;
import com.alibaba.global.order.management.api.model.TradeOrderLineDTO;
import com.aliexpress.databank.hsf.OrderService;
import com.aliexpress.databank.hsf.ReverseAutoService;
import com.aliexpress.databank.service.ReverseService;
import com.aliexpress.databank.utils.HsfUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

@Slf4j
@HSFProvider(serviceInterface = ReverseAutoService.class, serviceGroup = "HSF", serviceVersion = "1.0.0", clientTimeout = 20000)
public class ReverseAutoServiceImpl implements ReverseAutoService {

    @Autowired
    private OrderService orderService;

    @Autowired
    private ReverseService reverseService;

    @Autowired
    private OrderQueryForBuyerFacade orderQueryForBuyerFacade;

    @Override
    public JSONObject getCreateReverseOrderReceivedRefund(String scenario, String scenarioIndex) {
        try {
            // TODO just for demo
            List<Long> orderIds = orderService.getOrderIdsByScenarioIndexId(scenario, scenarioIndex, 1);
            if (CollectionUtils.isNotEmpty(orderIds)) {
                Long orderId = orderIds.get(0);

                Response<TradeOrderDTO> tradeOrderDTOResponse = orderQueryForBuyerFacade.queryTradeOrderById(orderId);

                List<TradeOrderLineDTO> tradeOrderLines = tradeOrderDTOResponse.getModule().getOrderLines();
                TradeOrderLineDTO tradeOrderLine = tradeOrderLines.get(0);

                Long buyerId = tradeOrderLine.getBuyer().getBuyerId();
                String returnOrRefund = "ONLY_REFUND";
                String amountStr = "0.01";
//                        tradeOrderDTOResponse.getModule().getActualFeeOfPurposeCurrency().getNumber().toString();
//                BigDecimal finalAmount = BigDecimal.valueOf(Long.parseLong(amountStr)).divide(BigDecimal.valueOf(100L));
                String currency = tradeOrderDTOResponse.getModule().getActualFeeOfPurposeCurrency().getCurrency().getCurrencyCode();
                String returnType = "";
                String returnReason = "买家原因";
                int quantity = tradeOrderLine.getQuantity();
                JSONObject createReverseReq = reverseService.getCreateReverseReq(String.valueOf(tradeOrderLine.getTradeOrderLineId()), String.valueOf(orderId), "", buyerId,
                        true, returnOrRefund, amountStr, currency, returnType, returnReason, quantity, false);
                return createReverseReq;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public JSONObject getCreateReverseOrderReceivedReturnSelfDropOff(String scenario, String scenarioIndex) {
        try {
            // TODO just for demo
            List<Long> orderIds = orderService.getOrderIdsByScenarioIndexId(scenario, scenarioIndex, 1);
            if (CollectionUtils.isNotEmpty(orderIds)) {
                Long orderId = orderIds.get(0);

                Response<TradeOrderDTO> tradeOrderDTOResponse = orderQueryForBuyerFacade.queryTradeOrderById(orderId);

                List<TradeOrderLineDTO> tradeOrderLines = tradeOrderDTOResponse.getModule().getOrderLines();
                TradeOrderLineDTO tradeOrderLine = tradeOrderLines.get(0);

                Long buyerId = tradeOrderLine.getBuyer().getBuyerId();
                String returnOrRefund = "RETURN";
                String amountStr = "0.01";
//                        tradeOrderDTOResponse.getModule().getActualFeeOfPurposeCurrency().getNumber().toString();
//                BigDecimal finalAmount = BigDecimal.valueOf(Long.parseLong(amountStr)).divide(BigDecimal.valueOf(100L));
                String currency = tradeOrderDTOResponse.getModule().getActualFeeOfPurposeCurrency().getCurrency().getCurrencyCode();
                String returnType = "自寄";
                String returnReason = "买家原因";
                int quantity = tradeOrderLine.getQuantity();
                JSONObject createReverseReq = reverseService.getCreateReverseReq(String.valueOf(tradeOrderLine.getTradeOrderLineId()), String.valueOf(orderId), "", buyerId,
                        true, returnOrRefund, amountStr, currency, returnType, returnReason, quantity, false);
                return createReverseReq;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Override
    public JSONObject getSellerAgreeSolution(Long reverseOrderLineId) throws Exception {
        JSONObject solutionRes = HsfUtil.getReverseSolution(reverseOrderLineId);
        JSONObject solution = reverseService.getActiveSolution(solutionRes);
        Long solutionId = solution.getLong("solutionId");
        Long sellerId = solution.getLong("sellerId");
        JSONObject agreeSolutionReq = new JSONObject();
        agreeSolutionReq.put("reverseOrderLineId", reverseOrderLineId);
        agreeSolutionReq.put("solutionId", solutionId);
        agreeSolutionReq.put("operatorId", sellerId);
        if (solution.getIntValue("solutionType") == 2) {
            String addressId = HsfUtil.getReturnAddress(sellerId).getJSONObject(0).getString("id");
            agreeSolutionReq.put("addressId", addressId);
        }
        return agreeSolutionReq;
    }

    @Override
    public JSONObject getBuyerAgreeSolution(Long reverseOrderLineId) throws Exception {
        JSONObject solutionRes = HsfUtil.getReverseSolution(reverseOrderLineId);
        JSONObject solution = reverseService.getActiveSolution(solutionRes);
        Long solutionId = solution.getLong("solutionId");
        Long buyer = solution.getLong("buyerId");
        JSONObject acceptSolutionReq = new JSONObject();
        acceptSolutionReq.put("reverseOrderLineId", reverseOrderLineId);
        acceptSolutionReq.put("solutionId", solutionId);
        acceptSolutionReq.put("operatorId", buyer);
        acceptSolutionReq.put("operatorType", "BUYER");
        return acceptSolutionReq;
    }

    @Override
    public JSONObject getSellerProvideSolutionRefund(Long reverseOrderLineId) throws Exception {
        JSONObject solutionRes = HsfUtil.getReverseSolution(reverseOrderLineId);
        JSONObject solution = reverseService.getActiveSolution(solutionRes);
        JSONObject refundDetail = solution.getJSONObject("features").getJSONObject("refundDetail");
        Long solutionId = solution.getLong("solutionId");
        Long sellerId = solution.getLong("sellerId");
        JSONObject refuseSolutionReq = new JSONObject();
        refuseSolutionReq.put("reverseOrderLineId", reverseOrderLineId);
        refuseSolutionReq.put("comment", "test by data bank");
        refuseSolutionReq.put("solutionId", solutionId);
        refuseSolutionReq.put("solutionType", 1);
        refuseSolutionReq.put("currencyCode", refundDetail.getJSONObject("seller").getJSONObject("goodsAmt").getString("currency"));
        refuseSolutionReq.put("operatorId", sellerId);
        refuseSolutionReq.put("refundAmount", refundDetail.getJSONObject("sellerPayTool").getJSONObject("cash").getString("amount"));
        refuseSolutionReq.put("file", "[{\"uid\":\"kqw3fvk7\",\"name\":\"截屏2021-07-09 下午4.47.43.png\",\"state\":\"done\",\"id\":\"H6b080eaa4f3c4649869cf39449fe04251.png\",\"url\":\"https://ae01.alicdn.com/kf/H6b080eaa4f3c4649869cf39449fe04251.png\",\"imgURL\":\"https://ae01.alicdn.com/kf/H6b080eaa4f3c4649869cf39449fe04251.png\",\"fileURL\":\"https://ae01.alicdn.com/kf/H6b080eaa4f3c4649869cf39449fe04251.png\",\"downloadURL\":\"https://ae01.alicdn.com/kf/H6b080eaa4f3c4649869cf39449fe04251.png\",\"fileType\":\"PICTURE\"}]");
        return refuseSolutionReq;
    }

    @Override
    public JSONObject getSellerProvideSolutionReturn(Long reverseOrderLineId) throws Exception {
        JSONObject solutionRes = HsfUtil.getReverseSolution(reverseOrderLineId);
        JSONObject solution = reverseService.getActiveSolution(solutionRes);
        JSONObject refundDetail = solution.getJSONObject("features").getJSONObject("refundDetail");
        Long solutionId = solution.getLong("solutionId");
        Long sellerId = solution.getLong("sellerId");
        JSONObject refuseSolutionReq = new JSONObject();
        refuseSolutionReq.put("reverseOrderLineId", reverseOrderLineId);
        refuseSolutionReq.put("comment", "test by data bank");
        refuseSolutionReq.put("solutionId", solutionId);
        refuseSolutionReq.put("solutionType", 2);
        refuseSolutionReq.put("currencyCode", refundDetail.getJSONObject("seller").getJSONObject("goodsAmt").getString("currency"));
        refuseSolutionReq.put("operatorId", sellerId);
        refuseSolutionReq.put("refundAmount", refundDetail.getJSONObject("sellerPayTool").getJSONObject("cash").getString("amount"));
        refuseSolutionReq.put("file", "[{\"uid\":\"kqw3fvk7\",\"name\":\"截屏2021-07-09 下午4.47.43.png\",\"state\":\"done\",\"id\":\"H6b080eaa4f3c4649869cf39449fe04251.png\",\"url\":\"https://ae01.alicdn.com/kf/H6b080eaa4f3c4649869cf39449fe04251.png\",\"imgURL\":\"https://ae01.alicdn.com/kf/H6b080eaa4f3c4649869cf39449fe04251.png\",\"fileURL\":\"https://ae01.alicdn.com/kf/H6b080eaa4f3c4649869cf39449fe04251.png\",\"downloadURL\":\"https://ae01.alicdn.com/kf/H6b080eaa4f3c4649869cf39449fe04251.png\",\"fileType\":\"PICTURE\"}]");
        String addressId = HsfUtil.getReturnAddress(sellerId).getJSONObject(0).getString("id");
        refuseSolutionReq.put("addressId", addressId);
        return refuseSolutionReq;
    }

    @Override
    public JSONObject getBuyProvideSolutionKeep(Long reverseOrderLineId) throws Exception {
        JSONObject solutionRes = HsfUtil.getReverseSolution(reverseOrderLineId);
        JSONObject solution = reverseService.getActiveSolution(solutionRes);
        Long solutionId = solution.getLong("solutionId");
        JSONObject refuseSolutionReq = new JSONObject();
        refuseSolutionReq.put("reverseOrderLineId", reverseOrderLineId);
        refuseSolutionReq.put("keepLastProposal", true);
        refuseSolutionReq.put("solutionId", solutionId);
        refuseSolutionReq.put("comment", reverseService.getComment());
        refuseSolutionReq.put("operatorId", solution.getLong("buyerId"));
        return refuseSolutionReq;
    }

    @Override
    public JSONObject getBuyProvideSolutionRefund(Long reverseOrderLineId) throws Exception {
        JSONObject solutionRes = HsfUtil.getReverseSolution(reverseOrderLineId);
        JSONObject solution = reverseService.getActiveSolution(solutionRes);
        JSONObject refundDetail = solution.getJSONObject("features").getJSONObject("refundDetail");
        Long solutionId = solution.getLong("solutionId");
        JSONObject refuseSolutionReq = new JSONObject();
        refuseSolutionReq.put("solutionId", solutionId);
        refuseSolutionReq.put("solutionType", 1);
        refuseSolutionReq.put("operatorId", solution.getLong("buyerId"));
        refuseSolutionReq.put("comment", reverseService.getComment());
        refuseSolutionReq.put("refundAmount", reverseService.getRefundAmount("0.01", refundDetail.getJSONObject("seller").getJSONObject("goodsAmt").getString("currency")));
        return refuseSolutionReq;
    }

    @Override
    public JSONObject getBuyProvideSolutionReturn(Long reverseOrderLineId) throws Exception {
        JSONObject solutionRes = HsfUtil.getReverseSolution(reverseOrderLineId);
        JSONObject solution = reverseService.getActiveSolution(solutionRes);
        JSONObject refundDetail = solution.getJSONObject("features").getJSONObject("refundDetail");
        Long solutionId = solution.getLong("solutionId");
        JSONObject refuseSolutionReq = new JSONObject();
        refuseSolutionReq.put("reverseOrderLineId", reverseOrderLineId);
        refuseSolutionReq.put("comment", reverseService.getComment());
        refuseSolutionReq.put("solutionId", solutionId);
        refuseSolutionReq.put("solutionType", 2);
        refuseSolutionReq.put("operatorId", solution.getLong("buyerId"));
        refuseSolutionReq.put("refundAmount", reverseService.getRefundAmount("0.01", refundDetail.getJSONObject("seller").getJSONObject("goodsAmt").getString("currency")));
        return refuseSolutionReq;
    }

    @Override
    public JSONObject getSellerProvideSolutionNoRefund(Long reverseOrderLineId) throws Exception {
        JSONObject solutionRes = HsfUtil.getReverseSolution(reverseOrderLineId);
        JSONObject solution = reverseService.getActiveSolution(solutionRes);
        JSONObject refundDetail = solution.getJSONObject("features").getJSONObject("refundDetail");
        JSONObject refuseSolutionReq = new JSONObject();
        refuseSolutionReq.put("reverseOrderLineId", reverseOrderLineId);
        refuseSolutionReq.put("comment", "test by dataBank");
        refuseSolutionReq.put("solutionId", solution.getLong("solutionId"));
        refuseSolutionReq.put("operatorId", solution.getLong("sellerId"));
        refuseSolutionReq.put("solutionType", 3);
        refuseSolutionReq.put("currencyCode", refundDetail.getJSONObject("sellerPayTool").getJSONObject("cash").getString("currency"));
        return refuseSolutionReq;
    }

}
