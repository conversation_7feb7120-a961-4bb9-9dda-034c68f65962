package com.aliexpress.databank.hsf;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.global.qa.dc.model.param.SystemDTO;
import com.aliexpress.databank.dataobject.ProductPool;
import com.aliexpress.databank.dataobject.SkuData;
import com.aliexpress.databank.dataobject.UserPool;
import com.lazada.imptest.api.domain.ic.SkuResponseDTO;

import java.util.List;
import java.util.Map;

public interface DataPoolService {

	JSONObject checkAccount(Long id);
	//检测是否是测试账号
	Boolean checkTestAccount(Long id);

	String checkUserTag(Long accountId);

	List<String>  insertUser(String userParams, String passwordParams, String addressParams, SystemDTO systemDTO);

	List<String> insertProduct(String productParams, SystemDTO systemDTO);

	Boolean updateUser(UserPool userPool);

	Boolean deleteUser(UserPool userPool);

	List<UserPool> queryUserInfo(UserPool userPool, Integer num);

	Boolean updateProduct(ProductPool productPool);

	Boolean deleteProduct(ProductPool productPool);

	List<ProductPool> queryProductInfo(ProductPool productPool, Integer num, Long lessPrice, Long largePrice);

	int userCount(Integer isDel, Integer enableType);

	JSONObject userCountById(Long accountId);

	int productCount(Integer isDel, Integer enableType);

	JSONObject productCountById(Long productId);

	Boolean deleteUserOrProduct(String userName, String productId);

	Boolean checkProductStatus(Long sellerId, Long productId, Integer status, Integer auditStatus);

	SkuData getSkuData(Map<Long, SkuResponseDTO> skuIdAndSkuMap);

	JSONObject queryUser(String userName, Long accountId, String address, String userTag, Integer enableType, Integer num, Integer isUsed);

	JSONObject queryProduct(Long categoryId, Long productId, Integer enableType, Integer num, Long lessPrice, Long largePrice);

	JSONObject updateUserUsed(UserPool userPool);

	Boolean updateUsed();
}
