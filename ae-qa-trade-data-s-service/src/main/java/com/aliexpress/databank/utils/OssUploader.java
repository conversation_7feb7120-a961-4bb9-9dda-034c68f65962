package com.aliexpress.databank.utils;

import com.aliyun.oss.OSSClient;

import java.io.File;

public class OssUploader {

    /**
     *
     * @param zipName 打出来的zip包的名字
     * @param filePath zip包的全路径
     * 发布
     */
    public static void publish(String zipName, String filePath) {
        /*OSSClient ossClient = new OSSClient("cn-hangzhou.oss-cdn.aliyun-inc.com", "LTAIYHIopvdy15qQ", "wW1dLqs72YjVIp5by0UFFkQCAi3U7D");
        ossClient.putObject("biz-sim-client", "doom/native/ep/"+zipName, new File(filePath));
        ossClient.shutdown();*/

        System.out.println("success");
    }

}
