package com.aliexpress.databank.utils;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.global.qa.dc.model.result.ResultDTO;
import com.aliexpress.databank.constant.ReverseFulfillmentStatus;
import com.aliexpress.databank.dataobject.MockFRFulfillmentReq;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Slf4j
public class ReverseFulfilmentUtil {


    public static final String FULFILLMENT_MSG_DEMO = "{\"buyerId\":\"**********\",\"dateTimeHappened\":*************,\"extendMap\":{\"eventOccurTime\":\"*************\"},\"fulfillmentOrderId\":\"FO3298410877088002\",\"fulfillmentOrderItemStatusUpdatedList\":[{\"fulfillmentOrderItemId\":\"FO3298411123584002\",\"reverseTradeOrderItemId\":\"*****************\",\"srcStatus\":\"2010\",\"targetStatus\":\"2020\",\"tradeOrderItemId\":\"****************\"}],\"fulfillmentOrderLineIdList\":[\"FO3298411123584002\"],\"fulfillmentOrderLineIdMap\":{\"FO3298411123584002\":{\"$ref\":\"$.fulfillmentOrderItemStatusUpdatedList[0]\"}},\"reverseTradeOrderId\":\"*****************\",\"sellerId\":\"*********\",\"siteId\":\"GLOBAL\",\"srcOrderBizStatus\":\"2010100\",\"srcOrderStatus\":\"2010\",\"targetOrderStatus\":\"2020\",\"tradeOrderId\":\"8176004330031090\"}";

    public static final String UNREACHABLE_REQ = "{\"serviceItemId\":\"5000000017142\",\"outBizId\":\"WH0810610043857017\",\"orderId\":8180809450122202,\"positiveReverseType\":\"positive\",\"eventOccurTime\":1698292761000,\"tradeOrderId\":\"8180809450122202\",\"sellerUserId\":2671514005,\"eventCode\":\"CONSO_WAREHOUSE_UNREACHABLE_RETURN_CAINIAO_DELIVERY_CALLBACK\",\"mailNo\":\"LP00616890439177\",\"sellerId\":2667888042,\"bizSource\":\"ALIEXPRESS\",\"outOrderId\":\"WH0810610043857017\",\"attributes\":{\"isItemDiscard\":\"true\",\"receiverCountryCode\":\"US\",\"outStockSubTradeOrderIds\":\"8180809450132202\",\"orderStage\":\"first\"},\"orderCode\":\"LP00616890439177\",\"eventSendTime\":1698386480000,\"sellerHavanaId\":1698386480000,\"orderStage\":\"first\",\"buyerHavanaId\":408038117353,\"tradeId\":\"8180809450122202\",\"channelCode\":\"CAINIAO_FULFILLMENT_STD\",\"parcels\":[]}";

    public static String getFulfillmentMsg(Long buyerId, Long sellerId, Long tradeOrderId, Long reverseOrderId,
                                           String foId, String warehouseType, String logisticProvider, String logisticProviderName,
                                           String scenario, Map<Long, Long> reverseOrderLineId2TradeOrderLineIds,
                                           Map<Long, String> reverseOrderLineId2FulfillmentOrderItemIds, String occurTime) {
        ReverseFulfillmentStatus reverseFulfillmentStatus = ReverseFulfillmentStatus.getReverseFulfillmentConstantByScenario(scenario);
        JSONObject msgBody = JSONObject.parseObject(FULFILLMENT_MSG_DEMO);
        msgBody.put("buyerId", String.valueOf(buyerId));
        if (StringUtils.isEmpty(occurTime) || StringUtils.isBlank(occurTime)){
            occurTime = String.valueOf(System.currentTimeMillis() - 86400);
        }
        msgBody.put("dateTimeHappened", Long.valueOf(occurTime));
        msgBody.getJSONObject("extendMap").put("eventOccurTime", occurTime);
        msgBody.getJSONObject("extendMap").put("qcOvertime_times", String.valueOf(System.currentTimeMillis() + 86400000));
        String providerCode = getLogisticProvider(logisticProvider);
        String providerType = "CAINIAO".equals(providerCode) ? "4PL" : "3PL";
        if (StringUtils.isNotBlank(reverseFulfillmentStatus.getErrorCode())) {
            msgBody.getJSONObject("extendMap").put("reverseFailErrorCode", reverseFulfillmentStatus.getErrorCode());
        }
        if (StringUtils.isNotBlank(reverseFulfillmentStatus.getErrorMsg())) {
            msgBody.getJSONObject("extendMap").put("reverseFailReason", reverseFulfillmentStatus.getErrorMsg());
        }
        if (ReverseFulfillmentStatus.PARCEL_REMOTE.getScenario().equals(scenario)
                || ReverseFulfillmentStatus.PARCEL_REMOTE_OVERSEA.getScenario().equals(scenario)) {
            msgBody.getJSONObject("extendMap").put("isRemote", "true");
        }
        if (ReverseFulfillmentStatus.PARCEL_OVERSEA.getScenario().equals(scenario)
                || ReverseFulfillmentStatus.PARCEL_REMOTE_OVERSEA.getScenario().equals(scenario)) {
            msgBody.getJSONObject("extendMap").put("islandPkg", "true");
        }
        if (!"2010".equalsIgnoreCase(reverseFulfillmentStatus.getTargetStatus())) {
            if (StringUtils.isNotBlank(logisticProviderName)) {
                msgBody.getJSONObject("extendMap").put("carriedCompanyName", logisticProviderName);
            }else{
                msgBody.getJSONObject("extendMap").put("carriedCompanyName", "WOOJININTERLOGIS");
            }
            msgBody.getJSONObject("extendMap").put("collectorName", "WOOJININTERLOGIS");
            msgBody.getJSONObject("extendMap").put("collectorPhone", "1588-0011");
            msgBody.getJSONObject("extendMap").put("carriedWaybillUrl", "https://cniwb-print-data.oss-cn-zhangjiakou.aliyuncs.com/oss_2ae1a649-86dd-4797-994a-cab5c6cb4737.png?Expires=**********&OSSAccessKeyId=LTAI5tPt9uDa45hC5A66oNW9&Signature=bw81x%2Fiy%2BDwo%2FQUxParszPfb%2Fto%3D");
            msgBody.getJSONObject("extendMap").put("qrCode", "QRTEST0306");
            msgBody.getJSONObject("extendMap").put("qrCodeUrl", "https://citi-ies-test.oss-cn-shenzhen.aliyuncs.com/**********/**********5222QRTEST0306.png");
            msgBody.getJSONObject("extendMap").put("trackingNumber", "YBEBR4000000194YQ");
            JSONObject secondWaybill = new JSONObject();
            secondWaybill.put("carriedCompanyName", "DPD");
            secondWaybill.put("trackingNumber", "1009838632426U");
            msgBody.getJSONObject("extendMap").put("secondWaybill", secondWaybill.toJSONString());
            String address = "商家仓".equals(warehouseType) ? "SELLER_ADDRESS" : "WAREHOUSE_ADDRESS";
            msgBody.getJSONObject("extendMap").put("warehouseAddress", "{\"addressType\":\"" + address + "\",\"city\":\"Incheon\",\"country\":\"KR\",\"countryISOCode\":\"KR\",\"detailAddress\":\"196, Wonseok-ro, Seo-gu, Incheon, Republic of Korea\",\"logisticsProviderCode\":\"" + providerCode + "\",\"logisticsProviderType\":\"" + providerType + "\",\"province\":\"Incheon\",\"receiverName\":\"XXX\",\"telephone\":\"01084104161\",\"warehouseCode\":\"KOR015\",\"zipCode\":\"22769\"}");

        }
        msgBody.put("fulfillmentOrderId", foId);
        JSONArray fulfillmentOrderItemStatusUpdatedList = new JSONArray();
        JSONArray fulfillmentOrderLineIdList = new JSONArray();
        Map<String, JSONObject> fulfillmentOrderLineIdMap = Maps.newConcurrentMap();
        for (Long reverseOrderLineId : reverseOrderLineId2TradeOrderLineIds.keySet()) {
            JSONObject fulfillmentOrderItemStatusUpdated = new JSONObject();
            fulfillmentOrderItemStatusUpdated.put("fulfillmentOrderItemId", reverseOrderLineId2FulfillmentOrderItemIds.get(reverseOrderLineId));
            fulfillmentOrderItemStatusUpdated.put("reverseTradeOrderItemId", String.valueOf(reverseOrderLineId));
            fulfillmentOrderItemStatusUpdated.put("srcStatus", reverseFulfillmentStatus.getSrcStatus());
            fulfillmentOrderItemStatusUpdated.put("targetStatus", reverseFulfillmentStatus.getTargetStatus());
            fulfillmentOrderItemStatusUpdated.put("tradeOrderItemId", reverseOrderLineId2TradeOrderLineIds.get(reverseOrderLineId));
            fulfillmentOrderItemStatusUpdatedList.add(fulfillmentOrderItemStatusUpdated);
            fulfillmentOrderLineIdList.add(reverseOrderLineId2FulfillmentOrderItemIds.get(reverseOrderLineId));
            fulfillmentOrderLineIdMap.put(reverseOrderLineId2FulfillmentOrderItemIds.get(reverseOrderLineId), fulfillmentOrderItemStatusUpdated);
        }
        msgBody.put("fulfillmentOrderItemStatusUpdatedList", fulfillmentOrderItemStatusUpdatedList);
        msgBody.put("fulfillmentOrderLineIdMap", fulfillmentOrderLineIdMap);
        msgBody.put("fulfillmentOrderLineIdList", fulfillmentOrderLineIdList);
        msgBody.put("srcOrderBizStatus", reverseFulfillmentStatus.getSrcOrderBizStatus());
        if (StringUtils.isNotBlank(reverseFulfillmentStatus.getTargetOrderBizStatus())) {
            msgBody.put("targetOrderBizStatus", reverseFulfillmentStatus.getTargetOrderBizStatus());
        }
        msgBody.put("srcOrderStatus", reverseFulfillmentStatus.getSrcStatus());
        msgBody.put("targetOrderStatus", reverseFulfillmentStatus.getTargetStatus());
        msgBody.put("reverseTradeOrderId", reverseOrderId);
        msgBody.put("sellerId", String.valueOf(sellerId));
        msgBody.put("tradeOrderId", String.valueOf(tradeOrderId));
        return msgBody.toJSONString();
    }

    private static String getLogisticProvider(String logisticProvider) {
        switch (logisticProvider) {
            case "CaiNiao":
                return "CAINIAO";
            case "Uspeed":
                return "uspeed";
            case "一诺":
                return "yinuo";
            case "亦邦":
                return "yibang_cs";
        }
        return "CAINIAO";
    }

    public static ResultDTO mockFR2MailNo(MockFRFulfillmentReq params) throws Exception {
        ResultDTO result = new ResultDTO();

        //根据子单id查询逆向单
        JSONObject reverseOrderLines = HsfUtil.getLastReverseOrderByTradeOrderLineId(params.getBuyerId(), params.getTradeOrderLineId());
        JSONObject lastReverseOrder = reverseOrderLines.getJSONObject("result");

        JSONObject features = lastReverseOrder.getJSONObject("features");

        // 2.0/3.0面单
        String returnType = features.getString("ae_business_type") != null ? features.getString("ae_business_type") : features.getString("return_way");
        Long version = "local_free_return".equalsIgnoreCase(returnType) ? 3L : 2L;

        List<Long> lastReverseOrderLines = getLastReverseOrderLines(params.getBuyerId(), params.getTradeOrderLineId());

        if (lastReverseOrderLines.size() == 0) {
            result.setSuccess(false);
            result.setMessage("没有可生成面单的纠纷单");
            return result;
        }

        JSONObject res = HsfUtil.mockFR2MailNo(lastReverseOrderLines, params.getBuyerId(), version, params.isIslandPkg());
        if (res.getBoolean("success")) {
            result.setMessage("创建v" + version + "面单成功" + res.toJSONString());
            result.setData("创建v" + version + "面单成功" + res.toJSONString());
            result.setSuccess(true);
        } else {
            result.setMessage("创建v" + version + "面单失败" + res.toJSONString());
            result.setData("创建v" + version + "面单失败" + res.toJSONString());
            result.setSuccess(false);
        }
        return result;
    }


    public static ResultDTO mockFR3PickUp(MockFRFulfillmentReq params) throws Exception {
        ResultDTO result = new ResultDTO();

        List<Long> lastReverseOrderLines = getLastReverseOrderLines(params.getBuyerId(), params.getTradeOrderLineId());

        if (lastReverseOrderLines.size() == 0) {
            result.setSuccess(false);
            result.setMessage("没有可接单的订单");
            return result;
        }
        JSONObject res = HsfUtil.mockFR3PickUp(lastReverseOrderLines, params.getBuyerId());
        if (res.getBoolean("success")) {
            result.setMessage("接单成功" + res.toJSONString());
            result.setData("接单成功" + res.toJSONString());
            result.setSuccess(true);
        } else {
            result.setMessage("接单失败" + res.toJSONString());
            result.setData("接单失败" + res.toJSONString());
            result.setSuccess(false);
        }
        return result;
    }


    public static ResultDTO mockFRAscan(MockFRFulfillmentReq params) throws Exception {
        ResultDTO result = new ResultDTO();

        List<Long> lastReverseOrderLines = getLastReverseOrderLines(params.getBuyerId(), params.getTradeOrderLineId());

        if (lastReverseOrderLines.size() == 0) {
            result.setSuccess(false);
            result.setMessage("没有可Ascan的订单");
            return result;
        }
        JSONObject res = HsfUtil.mockFRAscan(lastReverseOrderLines, params.getBuyerId());
        if (res.getBoolean("success")) {
            result.setMessage("ascan成功" + res.toJSONString());
            result.setData("ascan成功" + res.toJSONString());
            result.setSuccess(true);
        } else {
            result.setMessage("ascan失败" + res.toJSONString());
            result.setData("ascan失败" + res.toJSONString());
            result.setSuccess(false);
        }
        return result;
    }


    public static ResultDTO mockFRDSCAN(MockFRFulfillmentReq params) throws Exception {

        ResultDTO result = new ResultDTO();

        List<Long> lastReverseOrderLines = getLastReverseOrderLines(params.getBuyerId(), params.getTradeOrderLineId());

        if (lastReverseOrderLines.size() == 0) {
            result.setSuccess(false);
            result.setMessage("没有可Dscan的订单");
            return result;
        }

        String resultMessage = "";
        for (Long reverseOrderLineId1 : lastReverseOrderLines) {
            List<Long> list = new ArrayList<>();
            list.add(reverseOrderLineId1);
            JSONObject res = HsfUtil.mockFRDSCAN(list, params.getBuyerId());
            if (res.getBoolean("success")) {
                resultMessage = resultMessage.concat(String.valueOf(reverseOrderLineId1)).concat("返回值").concat(res.toJSONString());
                result.setSuccess(true);
            } else {
                resultMessage = resultMessage.concat(String.valueOf(reverseOrderLineId1)).concat("dscan失败");
                result.setSuccess(false);
            }
        }
        result.setData(resultMessage.concat("dscan成功"));
        return result;
    }


    public static ResultDTO mockQCCheck(MockFRFulfillmentReq params) throws Exception {
        ResultDTO result = new ResultDTO();

        List<Long> lastReverseOrderLines = getLastReverseOrderLines(params.getBuyerId(), params.getTradeOrderLineId());

        if (lastReverseOrderLines.size() == 0) {
            result.setSuccess(false);
            result.setMessage("没有可质检的订单");
            return result;
        }
        String resultMessage = "";
        for (Long reverseOrderLineId1 : lastReverseOrderLines) {
            List<Long> list = new ArrayList<>();
            list.add(reverseOrderLineId1);
            JSONObject res = HsfUtil.mockQCCheck(list, params.getBuyerId());
            if (res.getBoolean("success")) {
                resultMessage = resultMessage.concat(String.valueOf(reverseOrderLineId1)).concat("返回值").concat(res.toJSONString());
                result.setSuccess(true);
            } else {
                resultMessage = resultMessage.concat(String.valueOf(reverseOrderLineId1)).concat("dscan失败");
                result.setSuccess(false);
            }
        }
        result.setData(resultMessage.concat("质检成功"));
        return result;
    }

    // 获取订单下同一批次的逆向单id
    public static List<Long> getLastReverseOrderLines(Long buyerId, Long tradeOrderLineId) throws Exception {
        //根据子单id查询逆向单
        JSONObject reverseOrderLines = HsfUtil.getLastReverseOrderByTradeOrderLineId(buyerId, tradeOrderLineId);
        JSONObject lastReverseOrder = reverseOrderLines.getJSONObject("result");
        Long reverseOrderLineId = lastReverseOrder.getLong("reverseOrderLineId");
        Long tradeOrderId = lastReverseOrder.getLong("tradeOrderId");
        JSONObject features = lastReverseOrder.getJSONObject("features");
        // 获取批次号
        Long boi = features.getLong("b_o_i");

        List<Long> lastReverseOrderLines = new ArrayList<>();
        if (boi == null) {
            lastReverseOrderLines.add(reverseOrderLineId);
        } else {
            JSONObject reverseOrderLineList = HsfUtil.getReverseOrderLinesByOrderId(buyerId, tradeOrderId);
            if (reverseOrderLineList.size() == 0) {
                return new ArrayList<>();
            }
            JSONArray jsonArray = reverseOrderLineList.getJSONArray("result");
            if (jsonArray.size() == 0) {
                return new ArrayList<>();
            }
            for (Object object : jsonArray) {
                JSONObject o = (JSONObject) JSON.toJSON(object);
                Long orderLineId = o.getLong("reverseOrderLineId");
                Long reverseStatus = o.getLong("reverseStatus");
                JSONObject features1 = o.getJSONObject("features");
                // 获取批次号
                Long b_o_i = features1.getLong("b_o_i");

                if (reverseStatus == 2 || reverseStatus == 3 || reverseStatus == 10) {
                    continue;
                }
                if (boi.equals(b_o_i)) {
                    lastReverseOrderLines.add(orderLineId);
                }
            }
        }
        return lastReverseOrderLines;
    }

    public static JSONObject getUnreachableReq(String tradeOrderId, String tradeOrderLineId, String lpOrder, String mailNo, String warehouseOrderId) {
        JSONObject req = JSONObject.parseObject(UNREACHABLE_REQ);
        req.put("outBizId", warehouseOrderId);
        req.put("orderId", tradeOrderId);
        req.put("tradeOrderId", tradeOrderId);
        req.put("mailNo", mailNo);
        req.put("outOrderId", warehouseOrderId);
        req.getJSONObject("attributes").put("outStockSubTradeOrderIds", tradeOrderLineId);
        req.put("orderCode", lpOrder);
        req.put("tradeId", tradeOrderId);
        return req;
    }

}
