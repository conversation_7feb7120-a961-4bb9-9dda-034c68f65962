package com.aliexpress.databank.utils;

import com.alibaba.fastjson.JSONObject;
import com.aliexpress.databank.constant.Constant;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.util.Base64;
import java.util.Map;
import java.util.zip.GZIPInputStream;

public class CheckPrices {

    public static void main(String[] args) {
        String str ="H4sIAAAAAAAAALU66W6jyNavchXNP3ra1Ao10vywsfGK9300N2KzjQ0Gs3hr9VN9j/C92C2ccZLb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\\n";
        System.err.println(DecodeUtil.deCompress(str.replace("\\n","")));
    }
}
