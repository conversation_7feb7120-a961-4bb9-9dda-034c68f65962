package com.aliexpress.databank.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import java.util.List;
import java.util.function.Function;

/**
 * 工具类，用于从JSON对象中提取参数
 */
public class JsonParamExtractor {
    
    private final JSONObject jsonObject;
    
    public JsonParamExtractor(JSONObject jsonObject) {
        this.jsonObject = jsonObject;
    }
    
    public static JsonParamExtractor from(String params) {
        return new JsonParamExtractor(JSONObject.parseObject(params));
    }
    
    public static JsonParamExtractor from(JSONObject jsonObject) {
        return new JsonParamExtractor(jsonObject);
    }
    
    /**
     * 获取String类型参数
     */
    public String getString(String key) {
        return jsonObject.getString(key);
    }
    
    /**
     * 获取Long类型参数
     */
    public Long getLong(String key) {
        return jsonObject.getLong(key);
    }
    
    /**
     * 获取Integer类型参数
     */
    public Integer getInteger(String key) {
        return jsonObject.getInteger(key);
    }
    
    /**
     * 获取Boolean类型参数
     */
    public Boolean getBoolean(String key) {
        return jsonObject.getBoolean(key);
    }
    
    /**
     * 获取Double类型参数
     */
    public Double getDouble(String key) {
        return jsonObject.getDouble(key);
    }
    
    /**
     * 获取List类型参数
     */
    public <T> List<T> getList(String key, Class<T> clazz) {
        return JSON.parseArray(jsonObject.getString(key), clazz);
    }
    
    /**
     * 获取JSONObject类型参数
     */
    public JSONObject getJSONObject(String key) {
        return jsonObject.getJSONObject(key);
    }
    
    /**
     * 获取原始JSONObject
     */
    public JSONObject getJsonObject() {
        return jsonObject;
    }
    
    /**
     * 批量提取参数到对象中
     */
    public <T> T extractTo(Function<JsonParamExtractor, T> extractor) {
        return extractor.apply(this);
    }
}
