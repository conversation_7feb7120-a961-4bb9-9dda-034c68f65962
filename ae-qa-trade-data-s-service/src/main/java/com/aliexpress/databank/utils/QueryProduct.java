package com.aliexpress.databank.utils;

import com.alibaba.global.ic.api.CustomerProductServiceFacade;
import com.alibaba.global.ic.dto.scenario.query.ProductQueryRequest;
import com.alibaba.global.ic.dto.scenario.query.ProductQueryResponse;
import com.alibaba.global.ic.dto.scenario.query.SingleProductQueryCondition;
import com.alibaba.global.ic.dto.scenario.query.SkuQueryResultDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class QueryProduct {

    @Autowired
    public CustomerProductServiceFacade customerProductServiceFacade;


    public Long queryItemSku(Long itemId) {

        ProductQueryResponse result = queryProduct(itemId);

        List<SkuQueryResultDTO> skuIds = result.getModel().get(0).getSkuList();
        List<Long> skuId = new ArrayList<>();
        for (SkuQueryResultDTO id : skuIds) {
            Long sku = id.getSkuId();
            skuId.add(sku);
        }

        return skuId.get(0);
    }

    public ProductQueryResponse queryProduct(Long itemId) {

        SingleProductQueryCondition.QueryByProductIdBuilder queryByProductIdBuilder = SingleProductQueryCondition.queryByProductIdBuilder(itemId);

        ProductQueryRequest productQueryRequest = ProductQueryRequest.builder().addQueryCondition(queryByProductIdBuilder.build()).build();

        //productQueryRequest.setProductQueryOption(getProductQueryOption());
        ProductQueryResponse queryResult = customerProductServiceFacade.queryProduct(productQueryRequest);


        if (!queryResult.isSuccess()) {

            System.out.println(itemId + "query product failed.");
        }

        return queryResult;
    }

}
