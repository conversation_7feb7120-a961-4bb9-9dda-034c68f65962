package com.aliexpress.databank.utils;

import com.alibaba.global.money.Money;
import javax.money.MonetaryAmount;
import java.math.BigDecimal;

public class MoneyConvertUtil {


    public static Money calSplitAmount(MonetaryAmount originMonetaryAmount, MonetaryAmount splitMonetaryAmount, MonetaryAmount totalMonetaryAmount) {

        if(originMonetaryAmount == null || splitMonetaryAmount == null || totalMonetaryAmount == null || totalMonetaryAmount.isZero()) {
            return null;
        }

        Money originalMoney = convertMonetaryAmountToMoney(originMonetaryAmount);
        Money totalMoney = convertMonetaryAmountToMoney(totalMonetaryAmount);
        Money splitMoney = convertMonetaryAmountToMoney(splitMonetaryAmount);

        BigDecimal rate = originalMoney.getAmount().divide(totalMoney.getAmount(),8,BigDecimal.ROUND_HALF_EVEN);

        return  splitMoney.multiply(rate);
    }

    public static Money convertMonetaryAmountToMoney(MonetaryAmount monetaryAmount){
        if(monetaryAmount == null){
            return null;
        }
        String currencyCode = monetaryAmount.getCurrency().getCurrencyCode();
        return Money.of(monetaryAmount.getNumber().numberValue(BigDecimal.class),
                currencyCode);

    }



}
