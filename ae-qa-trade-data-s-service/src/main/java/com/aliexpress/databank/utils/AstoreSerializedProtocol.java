package com.aliexpress.databank.utils;

import com.alibaba.astore.protocol.ultronage.view.*;
import com.alibaba.ultron.model.protocol.ISerializedProtocol;
import com.alibaba.ultron.model.protocol.Protocol;
import com.alibaba.ultron.utils.JacksonUtils;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2020-01-02 4:57 下午
 **/
@Data
public class AstoreSerializedProtocol implements ISerializedProtocol {

    private static final Logger log = LoggerFactory.getLogger(AstoreSerializedProtocol.class);

    private Map<String, ? extends UltronModuleOutput> data;

    private UltronHierarchy hierarchy;

    private Map<String,Object> global;

    private UltronModuleContainer container;

    private UltronLinkage linkage;

    private UltronEndpoint endpoint;

    private boolean reload;

    public AstoreSerializedProtocol() {

    }

    public AstoreSerializedProtocol(UltronPageOutput ultronPageOutput) {
        this.data = ultronPageOutput.getData();
        this.hierarchy = ultronPageOutput.getHierarchy();
        this.global = ultronPageOutput.getGlobal();
        this.container = ultronPageOutput.getContainer();
        this.linkage = ultronPageOutput.getLinkage();
        this.endpoint = ultronPageOutput.getEndpoint();
        this.reload = ultronPageOutput.isReload();
    }

    @JsonIgnore
    private transient Protocol originalProtocol;

    @Override
    @JsonIgnore
    public String getOutput() {
        try {
            return JacksonUtils.serialize(this, true);
        } catch (Exception e) {
            log.error(" getOutput error ! ", e);
        }
        return null;
    }

    @Override
    @JsonIgnore
    public Protocol getOriginalProtocol() {
        return originalProtocol;
    }


}
