package com.aliexpress.databank.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliexpress.databank.dataobject.insurance.*;
import org.apache.xmlbeans.impl.xb.xsdschema.Public;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.stream.Collectors;

public class WorryFreeRetreatUtil {
    private static final Logger logger = LoggerFactory.getLogger(WorryFreeRetreatUtil.class);

    public static InsuranceOrderDTO getInsuranceOrderInfo(MockWFRV2Req params) throws Exception {
        List<Long> tradeOrderLineIds = new ArrayList<>();
        tradeOrderLineIds.add(Long.parseLong(params.getTradeOrderLineId()));

        JSONObject insuranceOrderQuery = new JSONObject();

        insuranceOrderQuery.put("buyerId", params.getBuyerId());
        insuranceOrderQuery.put("productCode", "AE_FREE_RETURN_LOCAL");
        insuranceOrderQuery.put("productPlatform", "ICBU");
        insuranceOrderQuery.put("tradeOrderId", params.getTradeOrderId());
        insuranceOrderQuery.put("tradeOrderLineIds", tradeOrderLineIds);
        JSONObject queryInsuranceOrders4Trade = HsfUtil.queryInsuranceOrders4Trade(insuranceOrderQuery);

        if (queryInsuranceOrders4Trade.getJSONArray("module") != null && queryInsuranceOrders4Trade.getJSONArray("module").size() > 0) {
            return queryInsuranceOrders4Trade.getJSONArray("module").getJSONObject(0).toJavaObject(InsuranceOrderDTO.class);
        } else {
            throw new RuntimeException("未查到无忧退保单信息");
        }

    }

    public static InsuranceCaseOrderDTO rebuildInsuranceCaseOrder(InsuranceOrderDTO insuranceOrder) throws Exception {
        InsuranceCaseOrderDTO insuranceCaseOrder = new InsuranceCaseOrderDTO();
        insuranceCaseOrder.setInsuranceCaseId(insuranceOrder.getInsuranceId());
        insuranceCaseOrder.setReportNo("01960100011026324071300000005002");
        insuranceCaseOrder.setStatus(3);
        insuranceCaseOrder.setAccidentTime(1720857455511L);
        insuranceCaseOrder.setReportTime(1720857681042L);
        insuranceCaseOrder.setReportSuccessTime(1720857455511L);
        InsuranceCaseFeeDTO insuranceCaseFee=new InsuranceCaseFeeDTO();
        insuranceCaseFee.setApplyAmount(insuranceOrder.getInsuranceFee().getOrderAmount());
        insuranceCaseOrder.setInsuranceCaseFee(insuranceCaseFee);
        Map<String, String> featureMap = new HashMap<>();
        featureMap.put("accidentReason","fake item");
        featureMap.put("expressInfo","{\"waybillNo\":\"AEPT67017722\",\"expressUrl\":\"https://download.cathay-ins.com.cn/api/download/output?id=cZENfaUzZ0nIZM80s3bJe32hnZFXaKY4ZdnxhyBsp9V5Wq0BjzsZxdvppQBply3qckmVbq0PixHU-IGaao_dn4Sy9P6ZKNna8Lx_bxw0fs8%3D&version=1\",\"logisticsCompanyCode\":\"CTT\",\"available\":true}");
        featureMap.put("outerOrderId","6213622801052202");
        featureMap.put("tu","1");
        featureMap.put("warehouseInfo","{\"country\":\"PT\",\"province\":\"Lisboa\",\"city\":\"Amadora\",\"area\":\"Amadora\",\"zipcode\":\"2700-458\",\"address\":\"1A,praça Da Igreja\",\"telephone\":\"3519****8863\",\"warehouseCode\":\"YN_PT\",\"warehouseContactName\":\"Syed Aamir\",\"warehouseContactDetails\":\"3519****8863\",\"contactName\":\"Syed Aamir\"}");
        insuranceCaseOrder.setFeatureMap(featureMap);
        return insuranceCaseOrder;
    }

    public static InsuranceLogisticsDTO rebuildInsuranceLogistics(MockWFRV2Req params) {
        InsuranceLogisticsDTO logistics = new InsuranceLogisticsDTO();
        InsuranceLogisticsWarehouseDTO insuranceLogisticsWarehouse = new InsuranceLogisticsWarehouseDTO();
        insuranceLogisticsWarehouse.setCountry("PT");
        insuranceLogisticsWarehouse.setProvince("Lisboa");
        insuranceLogisticsWarehouse.setCity("Amadora");
        insuranceLogisticsWarehouse.setArea("Amadora");
        insuranceLogisticsWarehouse.setZipcode("2700-458");
        insuranceLogisticsWarehouse.setAddress("1A,praça Da Igreja");
        insuranceLogisticsWarehouse.setTelephone("3519****8863");
        insuranceLogisticsWarehouse.setWarehouseCode("YN_PT");
        insuranceLogisticsWarehouse.setWarehouseContactDetails("Syed Aamir");
        insuranceLogisticsWarehouse.setWarehouseContactName("3519****8863");
        insuranceLogisticsWarehouse.setContactName("Syed Aamir");
        logistics.setLogisticsWarehouse(insuranceLogisticsWarehouse);
        logistics.setMailNo("AEPT67017722");
        logistics.setExpressUrl("https://download.cathay-ins.com.cn/api/download/output?id=cZENfaUzZ0nIZM80s3bJe32hnZFXaKY4ZdnxhyBsp9V5Wq0BjzsZxdvppQBply3qckmVbq0PixHU-IGaao_dn4Sy9P6ZKNna8Lx_bxw0fs8%3D&version=1");
        logistics.setLogisticsCompanyCode("CTT");

        // 上门揽件信息
        logistics.setPostManName("揽件人名称");
        logistics.setPostManPhoneNumber("揽件人联系方式");

        // 物流轨迹状态信息
        logistics.setCurrentStatus(params.getCurrentLogisticsStatus());
        logistics.setLogisticsTime(System.currentTimeMillis());

        // 寄件失败二揽信息
        logistics.setDeliveryFailedReason("联系不上寄件人");
        logistics.setDeliveryFailedCode("SENDING_ERROR_UNABLE_CONTACT");
        return logistics;
    }


    public static JSONObject rebuildCheckResult() {
        JSONObject jsonObject=new JSONObject();
        List<String> goodsAttachments=new ArrayList<>();
        goodsAttachments.add("https://imagesys.cathay-ins.com.cn/api/v1/files/preview?id=YX1jBeJFt9cgyEtAFgosV7mt0GarR5swANGGH8crBLiCoyKnWDGtXxFt0TOysJWUowZPqvz9WE39ZzzN9035f3XlPkYxcWFWLvSg06S5M5HlDKe62xg2yWocc9lHDkaeSswAxWcpTZQDsUuW3t4PCFSCmv9FC_8LGAFh1FHckV36BwQ7s3mjX7IsQf1Od3zA");
        goodsAttachments.add("https://imagesys.cathay-ins.com.cn/api/v1/files/preview?id=YX1jBeJFt9cgyEtAFgosV7mt0GarR5swANGGH8crBLjG-zLNAZp14E_ddrGDeyshowZPqvz9WE39ZzzN9035f_1kxm4Omdr-Ze7hAa8akbZRYZjDGfcAKpsK5B4C8C0lCdj-ax7EHFJ22WmD7a8ZSKCo-lgNKKAzoemX7JQSRrL6BwQ7s3mjX7IsQf1Od3zA");
        goodsAttachments.add("https://imagesys.cathay-ins.com.cn/api/v1/files/preview?id=YX1jBeJFt9cgyEtAFgosV7mt0GarR5swANGGH8crBLjRpm1Ti86uj79h3YiXTgFSowZPqvz9WE39ZzzN9035f1ugii9CCb5IGaiaBlPFAqGommn6ykOkN4BDe3PbPj6G0gq7yccHZGP930h465aB-pTrLCtgoN5QiXqINyoBZsL6BwQ7s3mjX7IsQf1Od3zA,https://imagesys.cathay-ins.com.cn/api/v1/files/preview?id=YX1jBeJFt9cgyEtAFgosV7mt0GarR5swANGGH8crBLjG-zLNAZp14E_ddrGDeyshowZPqvz9WE39ZzzN9035fxFTqoK7VRyunscw1llJVNkbaEXUVOnqhFxU4HWGULRUEFFv9AiP4G7N5lgyCZEdnBd8avSf-1bhuqpm2Ph5-0L6BwQ7s3mjX7IsQf1Od3zA");
        jsonObject.put("goodsAttachments",goodsAttachments);
        jsonObject.put("result","PASS");
        jsonObject.put("riskAdmitResult","PASS");
        jsonObject.put("riskAdmitResultDesc","");
        jsonObject.put("surveyConclusion","ACCEPTED");
        jsonObject.put("surveyDescription","ACCEPTED");
        return jsonObject;
    }

    public static InsuranceCaseOrderDTO getInsuranceCaseInfo(Long insuranceId, Long buyerId) throws Exception {
        JSONObject request = new JSONObject();
        request.put("insuranceId", insuranceId);
        request.put("buyerId", buyerId);
        JSONObject insuranceCaseOrders = HsfUtil.pageQueryInsuranceCaseOrders(request);
        JSONArray modelList = insuranceCaseOrders.getJSONArray("modelList");
        logger.info("无忧退报案单查询结果" + modelList);
        if (modelList != null && !modelList.isEmpty()) {
            List<InsuranceCaseOrderDTO> insuranceCaseOrderDTOList = JSON.parseArray(modelList.toJSONString(), InsuranceCaseOrderDTO.class);
            if (insuranceCaseOrderDTOList.size() == 1) {
                return insuranceCaseOrderDTOList.get(0);
            } else {
                List<InsuranceCaseOrderDTO> collect = insuranceCaseOrderDTOList.stream().sorted(Comparator.comparing(InsuranceCaseOrderDTO::getReportTime).reversed()).collect(Collectors.toList());
                return collect.get(0);
            }
        } else {
            throw new RuntimeException("无报案单信息");
        }
    }
}
