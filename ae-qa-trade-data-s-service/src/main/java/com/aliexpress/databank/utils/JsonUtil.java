package com.aliexpress.databank.utils;


import com.alibaba.common.lang.StringUtil;

import java.lang.reflect.Field;
import java.util.List;

public class JsonUtil {

	/**
	 *
	 * @param object 实体类或对象
	 * @return
	 */
	public static boolean checkObjAllFieldIsNullOrEmpty(Object object){
		if (null == object){
			return true;
		}
		try{
			Class cls = Object.class;
			Field[] fields = cls.getDeclaredFields();
			for(int i=0; i<fields.length; i++){
				Field f = fields[i];
				f.setAccessible(true);
				Object obj = f.get(object);
				if (null != obj ){
					if (f.getType().equals(List.class) && !((List)obj).isEmpty()){
						return false;
					}
					else if(f.getType().equals(String.class) && StringUtil.isNotBlank(f.get(object).toString())){
						return false;
					}
				}
			}
		}catch (Exception e){
			e.printStackTrace();
		}
		return true;
	}

}
