package com.aliexpress.databank.utils;


import com.alibaba.fastjson.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class IgnoreDemo {
    private static final Logger log = LoggerFactory.getLogger(IgnoreDemo.class);

    public boolean eval(Object expected, Object actual) {
        String expectedValue = expected.toString();
        String actualValue = actual.toString();
        if (expectedValue.length() == 16 && actualValue.length() == 16) {
            String a = expectedValue.substring(1, 16);
            String b = actualValue.substring(1, 16);
            return a.equals(b);
        }  return expectedValue.equals(actualValue);
    }
    public boolean eval1(Object expected, Object actual) {
        String expectedValue = expected.toString();
        String actualValue = actual.toString();
        if (expectedValue.contains("trace_id") && actualValue.contains("trace_id")) {
            expectedValue = expectedValue.split("trace_id:")[1];
            actualValue = actualValue.split("trace_id:")[1];
            return expectedValue.length()==actualValue.length();
        } else {
            return false;
        }
    }

    public boolean eval2(Object expected, Object actual) {
        String expectedValue = expected.toString();
        String actualValue = actual.toString();

        if (expectedValue.contains("trigger_event_context") && actualValue.contains("trigger_event_context")) {
            expectedValue = expectedValue.replace("#3B", ":").split("trigger_event_context:")[1];
            actualValue = actualValue.replace("#3B", ":").split("trigger_event_context:")[1];

            Map<String, String> a = JSONObject.parseObject(expectedValue, Map.class);
            Map<String, String> b = JSONObject.parseObject(actualValue, Map.class);

            List<String> keyList = new ArrayList<>();
            for (String key : a.keySet()) {
                if(key.equals("eventTriggerTime")){
                    continue;
                }
                if (!a.get(key).equals(b.get(key))) {
                    keyList.add(key);
                }
            }

            return keyList.isEmpty();
        }
        return false;
    }

    public boolean eval3(Object expected, Object actual) {
        String expectedValue = expected.toString();
        String actualValue = actual.toString();
        String newExpectedValue = expectedValue.replace("\"", "");
        String newActualValue=actualValue.replace("https://pre-csp", "https://csp");
        return newExpectedValue.equals(newActualValue);

    }

    public boolean eval4(Object expected, Object actual) {
        String expectedValue = expected.toString();
        String actualValue = actual.toString();
        if (expectedValue.contains("trace_id") && actualValue.contains("trace_id")) {
            expectedValue = expectedValue.split("trace_id:")[1];
            actualValue = actualValue.split("trace_id:")[1];
            return expectedValue.length()==actualValue.length();
        }
        if (expectedValue.contains("trace") && actualValue.contains("trace")) {
            expectedValue = expectedValue.split("trace_id:")[1];
            actualValue = actualValue.split("trace_id:")[1];
            return expectedValue.length()==actualValue.length();
        }
        return expectedValue.equals(actualValue);

    }

}
