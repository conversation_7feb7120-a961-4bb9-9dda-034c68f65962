package com.aliexpress.databank.utils;


import com.aliyun.kms.security.client.common.SecurityCloudClientManager;
import com.aliyun.kms.security.client.sls.SecuritySlsClientManager;
import com.aliyun.openservices.log.Client;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import java.io.IOException;


@Slf4j
@Component
public class SLSClientManager implements InitializingBean {
    private static SLSClientManager instance = null;
    @Getter
    private Client client;
    private static String endpoint = "zhangbei-corp-share.log.aliyuncs.com";
    private static String appName = "ae-qa-trade-data-s";
    private static String flag = "4617b18be3284b8ab04f219f67601e58";
    private static boolean finishInit = false;

    @Override
    public void afterPropertiesSet() throws Exception {
        try {
            // 一个应用仅初始化一次即可
            SecurityCloudClientManager.bindingApplication(appName);
//            SecurityCloudClientManager.bindingApplication(appName, appCmmsInfoFile);
            //静态方法通过flag获取client,client作为单例长期持有时,可放入初始化中仅获取一次,此时无须close
            client = SecuritySlsClientManager.getSlsClient(endpoint, flag);
            finishInit = true;
        } catch (Throwable e) {
            log.error("bindingApplicationError", e);
        } finally {
            Runtime.getRuntime().addShutdownHook(new Thread() {
                @Override
                public void run() {
                    if (finishInit) {
                        try {
                            SecurityCloudClientManager.shutdown();
                        } catch (IOException e) {
                            log.error("SecurityCloudClientManager#shutdown#error", e);
                        }
                    }
                }
            });
        }
        instance = this;
    }

    public static SLSClientManager Instance() {
        if (null == instance) {
            instance = new SLSClientManager();
        }
        return instance;
    }
}
