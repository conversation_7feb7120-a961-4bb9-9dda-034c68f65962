package com.aliexpress.databank.utils;

import com.alibaba.fastjson.JSONObject;
import com.aliexpress.databank.constant.Constant;
import org.apache.commons.codec.binary.Hex;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.HttpMethod;
import org.apache.commons.httpclient.methods.PostMethod;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.Base64;

@Slf4j
public class HttpClientUtil {


    public static JSONObject doPost4HsfHttp(String url, String params) {
        String response = null;
        HttpClient httpClient = new HttpClient();
        HttpMethod postMethod;
        try {
            postMethod = postMethod4HsfHttp(url, params);
            httpClient.executeMethod(postMethod);
            response = postMethod.getResponseBodyAsString();
        } catch (IOException e) {
            log.error("Fail to get response from " + url + ". Params: " + params, e);
        }
        return JSONObject.parseObject(response);
    }

    private static HttpMethod postMethod4HsfHttp(String url, String param) throws IOException {
        PostMethod post = new PostMethod(url);
        post.setRequestHeader("Content-Type", "application/fastjson");
        post.setRequestHeader("Http-Rpc-Type", "JsonContent");
        post.setRequestHeader("Http-Rpc-Timeout", "3000");
        post.setRequestHeader("app-name", "ae-qa-trade-data-s");
        post.setRequestBody(param);
        post.releaseConnection();
        log.info("postMethod4HsfHttp: " + post.getURI().toString());
        return post;
    }

    private static HttpMethod postMethod4ThubHttp(String url, JSONObject param, String payload) {
        PostMethod post = new PostMethod(url);
        post.setRequestHeader("Content-Type", "application/fastjson");
        post.setRequestHeader("Http-Rpc-Type", "JsonContent");
        post.setRequestHeader("Http-Rpc-Timeout", "3000");
        try {
            post.setRequestHeader("appname", Constant.THUB_INFO.getString("appName"));
            String token = new String(Base64.getDecoder().decode(Constant.THUB_INFO.getString("token").getBytes()));
            String timestamp = String.valueOf(System.currentTimeMillis());
            String workId = Constant.THUB_INFO.getString("workId");
            String userNick = Constant.THUB_INFO.getString("userNick");

            String data = token + workId + userNick + timestamp + payload;
            log.info("postMethod4ThubHttp: data: " + data);
            MessageDigest messageDigest = MessageDigest.getInstance("SHA-256");
            byte[] result = messageDigest.digest(data.getBytes(StandardCharsets.UTF_8));
            String sign = new String(Hex.encodeHex(result));

            post.setRequestHeader("timestamp", timestamp);
            post.setRequestHeader("workId", workId);
            post.setRequestHeader("userNick", URLEncoder.encode(userNick));
            post.setRequestHeader("version", Constant.THUB_INFO.getString("version"));
            post.setRequestHeader("sign", sign);
            post.setRequestBody(param.toJSONString());
            post.releaseConnection();
            log.info("postMethod4ThubHttp: " + post.getURI().toString());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return post;
    }

    public static JSONObject doPost4ThubHttp(String url, JSONObject params, String payload) {
        String response = null;
        HttpClient httpClient = new HttpClient();
        HttpMethod postMethod;
        try {
            postMethod = postMethod4ThubHttp(url, params, payload);
            httpClient.executeMethod(postMethod);
            response = postMethod.getResponseBodyAsString();
        } catch (IOException e) {
            log.error("Fail to get response from " + url + ". Params: " + params, e);
        }
        return JSONObject.parseObject(response);
       }

}
