package com.aliexpress.databank.utils;

import com.alibaba.ecommerce.module.Response;
import com.alibaba.fastjson.JSON;
import com.alibaba.global.order.management.api.facade.OrderQueryForBuyerFacade;
import com.alibaba.global.order.management.api.model.TradeOrderDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class PaymentUtil {

    @Autowired
    private OrderQueryForBuyerFacade orderQueryForBuyerFacade;


    public Long getBuyerIdByOrderNo(String bizOrderNo) {
        Response<TradeOrderDTO> tradeOrderDTOResponse = null;
        try {
            log.info("【orderQueryForBuyerFacade.queryTradeOrderById】request params: " + bizOrderNo);
            tradeOrderDTOResponse = orderQueryForBuyerFacade.queryTradeOrderById(Long.parseLong(bizOrderNo));
            log.info("【orderQueryForBuyerFacade.queryTradeOrderById】response: " + bizOrderNo);

        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }

        if(tradeOrderDTOResponse!= null && tradeOrderDTOResponse.getModule() !=null && tradeOrderDTOResponse.getModule().getBuyer() !=null) {
            return tradeOrderDTOResponse.getModule().getBuyer().getBuyerId();
        }

        return null;
    }
}
