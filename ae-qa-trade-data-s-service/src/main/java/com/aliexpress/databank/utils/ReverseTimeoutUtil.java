package com.aliexpress.databank.utils;

import org.testng.collections.Sets;

import java.util.Set;

public class ReverseTimeoutUtil {

//   写死的 维护的： Set<String>  = ["callback3","AeTimeoutCallbackNegotiationTimeoutCallback","AeTimeoutCallbackNegotiationTimeoutCallback","AeTimeoutCallbackNegotiationTimeoutCallback"]
//   逆向子单里的 reminder1,reminder2 callback3  ==> Set集合的使用方式  hashSet  list（ArrayList, LinkList）,map(HashMap) 这个类里面的方法
//    上面两个集合取交集  ==> callback3
//    构建groovy =>    reverseOrderLineId，timeouttype，callback类，timeoutId
//    buildScript(),支持替换callback

//    public static void main(String[] args) {
//        Set<String> set = Sets.newHashSet();
//        Set<String> set2 = Sets.newHashSet();
//        set.
//    }


    public static final String WAIT_SOLUTION_TIMEOUT_NEGOTIATION = "package com.alibaba.reverse.platform.plus.test\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.model.timeout.GlobalReverseTimeoutCallbackParam\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.model.timeout.GlobalTimeout\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.repository.ReverseOrderRepository\n" +
            "import com.alibaba.reverse.platform.kernel.server.activity.scenario.model.aggregation.GlobalReverseOrderLine\n" +
            "import com.alibaba.reverse.platform.plus.domain.timeout.AeTimeoutCallbackNegotiationTimeoutCallback\n" +
            "import org.springframework.context.ApplicationContext\n" +
            "ApplicationContext ac = context.getContext();\n" +
            "AeTimeoutCallbackNegotiationTimeoutCallback callback = ac.getBean(AeTimeoutCallbackNegotiationTimeoutCallback.class)\n" +
            "ReverseOrderRepository reverseOrderRepository = ac.getBean(ReverseOrderRepository.class)\n" +
            "GlobalReverseOrderLine globalReverseOrderLine = reverseOrderRepository.getReverseOrderLine(2200138301700182L);\n" +
            "GlobalTimeout globalTimeout = new GlobalTimeout();\n" +
            "globalTimeout.setId(1);\n" +
            "globalTimeout.setType(\"WAIT_SOLUTION_TIMEOUT\");\n" +
            "GlobalReverseTimeoutCallbackParam callbackParam = new GlobalReverseTimeoutCallbackParam();\n" +
            "callbackParam.setReverseOrderLine(globalReverseOrderLine.getReverseOrderLine());\n" +
            "callbackParam.setTimeout(globalTimeout)\n" +
            "callback.callback(callbackParam);";

    public static final String WAIT_BUYER_RECOLLECT_CONFIRM = "package com.alibaba.reverse.platform.plus.test\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.model.timeout.GlobalReverseTimeoutCallbackParam\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.model.timeout.GlobalTimeout\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.repository.ReverseOrderRepository\n" +
            "import com.alibaba.reverse.platform.kernel.server.activity.scenario.model.aggregation.GlobalReverseOrderLine\n" +
            "import com.alibaba.reverse.platform.plus.domain.timeout.AeTimeoutCallbackRaiseArbitration\n" +
            "import org.springframework.context.ApplicationContext\n" +
            "ApplicationContext ac = context.getContext();\n" +
            "AeTimeoutCallbackRaiseArbitration callback = ac.getBean(AeTimeoutCallbackRaiseArbitration.class)\n" +
            "ReverseOrderRepository reverseOrderRepository = ac.getBean(ReverseOrderRepository.class)\n" +
            "GlobalReverseOrderLine globalReverseOrderLine = reverseOrderRepository.getReverseOrderLine(2200138301700182L);\n" +
            "GlobalTimeout globalTimeout = new GlobalTimeout();\n" +
            "globalTimeout.setId(1);\n" +
            "globalTimeout.setType(\"WAIT_BUYER_RECOLLECT_CONFIRM\");\n" +
            "GlobalReverseTimeoutCallbackParam callbackParam = new GlobalReverseTimeoutCallbackParam();\n" +
            "callbackParam.setReverseOrderLine(globalReverseOrderLine.getReverseOrderLine());\n" +
            "callbackParam.setTimeout(globalTimeout)\n" +
            "callback.callback(callbackParam);";

    public static final String WAIT_SOLUTION_TIMEOUT_GUARANTEE = "package com.alibaba.reverse.platform.plus.test\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.model.timeout.GlobalReverseTimeoutCallbackParam\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.model.timeout.GlobalTimeout\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.repository.ReverseOrderRepository\n" +
            "import com.alibaba.reverse.platform.kernel.server.activity.scenario.model.aggregation.GlobalReverseOrderLine\n" +
            "import com.alibaba.reverse.platform.plus.domain.timeout.AeTimeoutCallbackRaiseArbitration\n" +
            "import org.springframework.context.ApplicationContext\n" +
            "ApplicationContext ac = context.getContext();\n" +
            "AeTimeoutCallbackRaiseArbitration callback = ac.getBean(AeTimeoutCallbackRaiseArbitration.class)\n" +
            "ReverseOrderRepository reverseOrderRepository = ac.getBean(ReverseOrderRepository.class)\n" +
            "GlobalReverseOrderLine globalReverseOrderLine = reverseOrderRepository.getReverseOrderLine(2200138301700182L);\n" +
            "GlobalTimeout globalTimeout = new GlobalTimeout();\n" +
            "globalTimeout.setId(1);\n" +
            "globalTimeout.setType(\"WAIT_SOLUTION_TIMEOUT\");\n" +
            "GlobalReverseTimeoutCallbackParam callbackParam = new GlobalReverseTimeoutCallbackParam();\n" +
            "callbackParam.setReverseOrderLine(globalReverseOrderLine.getReverseOrderLine());\n" +
            "callbackParam.setTimeout(globalTimeout)\n" +
            "callback.callback(callbackParam);";


    public static final String WAIT_SELLER_RESPONSE_CANCEL = "package com.alibaba.reverse.platform.plus.test\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.model.timeout.GlobalReverseTimeoutCallbackParam\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.model.timeout.GlobalTimeout\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.repository.ReverseOrderRepository\n" +
            "import com.alibaba.reverse.platform.kernel.server.activity.scenario.model.aggregation.GlobalReverseOrderLine\n" +
            "import com.alibaba.reverse.platform.plus.domain.timeout.AeTimeoutCallbackAgreeCancelRequest\n" +
            "import org.springframework.context.ApplicationContext\n" +
            "ApplicationContext ac = context.getContext();\n" +
            "AeTimeoutCallbackAgreeCancelRequest callback = ac.getBean(AeTimeoutCallbackAgreeCancelRequest.class)\n" +
            "ReverseOrderRepository reverseOrderRepository = ac.getBean(ReverseOrderRepository.class)\n" +
            "GlobalReverseOrderLine globalReverseOrderLine = reverseOrderRepository.getReverseOrderLine(2200138301700182L);\n" +
            "GlobalTimeout globalTimeout = new GlobalTimeout();\n" +
            "globalTimeout.setId(1);\n" +
            "globalTimeout.setType(\"WAIT_SELLER_RESPONSE_TIMEOUT\");\n" +
            "GlobalReverseTimeoutCallbackParam callbackParam = new GlobalReverseTimeoutCallbackParam();\n" +
            "callbackParam.setReverseOrderLine(globalReverseOrderLine.getReverseOrderLine());\n" +
            "callbackParam.setTimeout(globalTimeout)\n" +
            "callback.callback(callbackParam);";

    public static final String WAIT_SELLER_FILL_ADDRESS_TIMEOUT = "package com.alibaba.reverse.platform.plus.test\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.model.timeout.GlobalReverseTimeoutCallbackParam\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.model.timeout.GlobalTimeout\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.repository.ReverseOrderRepository\n" +
            "import com.alibaba.reverse.platform.kernel.server.activity.scenario.model.aggregation.GlobalReverseOrderLine\n" +
            "import com.alibaba.reverse.platform.plus.domain.timeout.AeTimeoutCallbackCheckSellerAddressAndTakeAction\n" +
            "import org.springframework.context.ApplicationContext\n" +
            "ApplicationContext ac = context.getContext();\n" +
            "AeTimeoutCallbackCheckSellerAddressAndTakeAction callback = ac.getBean(AeTimeoutCallbackCheckSellerAddressAndTakeAction.class)\n" +
            "ReverseOrderRepository reverseOrderRepository = ac.getBean(ReverseOrderRepository.class)\n" +
            "GlobalReverseOrderLine globalReverseOrderLine = reverseOrderRepository.getReverseOrderLine(2200138301700182L);\n" +
            "GlobalTimeout globalTimeout = new GlobalTimeout();\n" +
            "globalTimeout.setId(1);\n" +
            "globalTimeout.setType(\"WAIT_SELLER_FILL_ADDRESS_TIMEOUT\");\n" +
            "GlobalReverseTimeoutCallbackParam callbackParam = new GlobalReverseTimeoutCallbackParam();\n" +
            "callbackParam.setReverseOrderLine(globalReverseOrderLine.getReverseOrderLine());\n" +
            "callbackParam.setTimeout(globalTimeout)\n" +
            "callback.callback(callbackParam);";

    public static final String FREE_RETURN_WAIT_BUYER_RETURN_ITEM_TIMEOUT = "package com.alibaba.reverse.platform.plus.test\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.model.timeout.GlobalReverseTimeoutCallbackParam\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.model.timeout.GlobalTimeout\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.repository.ReverseOrderRepository\n" +
            "import com.alibaba.reverse.platform.kernel.server.activity.scenario.model.aggregation.GlobalReverseOrderLine\n" +
            "import com.alibaba.reverse.platform.plus.domain.timeout.AeTimeoutCallbackCheckReturnStatusAndTakeAction\n" +
            "import org.springframework.context.ApplicationContext\n" +
            "ApplicationContext ac = context.getContext();\n" +
            "AeTimeoutCallbackCheckReturnStatusAndTakeAction callback = ac.getBean(AeTimeoutCallbackCheckReturnStatusAndTakeAction.class)\n" +
            "ReverseOrderRepository reverseOrderRepository = ac.getBean(ReverseOrderRepository.class)\n" +
            "GlobalReverseOrderLine globalReverseOrderLine = reverseOrderRepository.getReverseOrderLine(2200138301700182L);\n" +
            "GlobalTimeout globalTimeout = new GlobalTimeout();\n" +
            "globalTimeout.setId(1);\n" +
            "globalTimeout.setType(\"WAIT_BUYER_RETURN_ITEM_TIMEOUT\");\n" +
            "GlobalReverseTimeoutCallbackParam callbackParam = new GlobalReverseTimeoutCallbackParam();\n" +
            "callbackParam.setReverseOrderLine(globalReverseOrderLine.getReverseOrderLine());\n" +
            "callbackParam.setTimeout(globalTimeout)\n" +
            "callback.callback(callbackParam);";

    public static final String FR_V3_MAIL_RETURN_WAIT_BUYER_RETURN_ITEM_TIMEOUT = "package com.alibaba.reverse.platform.plus.test\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.model.timeout.GlobalReverseTimeoutCallbackParam\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.model.timeout.GlobalTimeout\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.repository.ReverseOrderRepository\n" +
            "import com.alibaba.reverse.platform.kernel.server.activity.scenario.model.aggregation.GlobalReverseOrderLine\n" +
            "import com.alibaba.reverse.platform.plus.domain.timeout.AeTimeoutSubmitReturnItemCallback\n" +
            "import org.springframework.context.ApplicationContext\n" +
            "ApplicationContext ac = context.getContext();\n" +
            "AeTimeoutSubmitReturnItemCallback callback = ac.getBean(AeTimeoutSubmitReturnItemCallback.class)\n" +
            "ReverseOrderRepository reverseOrderRepository = ac.getBean(ReverseOrderRepository.class)\n" +
            "GlobalReverseOrderLine globalReverseOrderLine = reverseOrderRepository.getReverseOrderLine(2200138301700182L);\n" +
            "GlobalTimeout globalTimeout = new GlobalTimeout();\n" +
            "globalTimeout.setId(1);\n" +
            "globalTimeout.setType(\"WAIT_BUYER_RETURN_ITEM_TIMEOUT\");\n" +
            "GlobalReverseTimeoutCallbackParam callbackParam = new GlobalReverseTimeoutCallbackParam();\n" +
            "callbackParam.setReverseOrderLine(globalReverseOrderLine.getReverseOrderLine());\n" +
            "callbackParam.setTimeout(globalTimeout)\n" +
            "callback.callback(callbackParam);";

    public static final String WAIT_BUYER_RETURN_ITEM_TIMEOUT = "package com.alibaba.reverse.platform.plus.test\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.model.timeout.GlobalReverseTimeoutCallbackParam\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.model.timeout.GlobalTimeout\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.repository.ReverseOrderRepository\n" +
            "import com.alibaba.reverse.platform.kernel.server.activity.scenario.model.aggregation.GlobalReverseOrderLine\n" +
            "import com.alibaba.reverse.platform.plus.domain.timeout.AeTimeoutCallbackRejectRequest\n" +
            "import org.springframework.context.ApplicationContext\n" +
            "ApplicationContext ac = context.getContext();\n" +
            "AeTimeoutCallbackRejectRequest callback = ac.getBean(AeTimeoutCallbackRejectRequest.class)\n" +
            "ReverseOrderRepository reverseOrderRepository = ac.getBean(ReverseOrderRepository.class)\n" +
            "GlobalReverseOrderLine globalReverseOrderLine = reverseOrderRepository.getReverseOrderLine(2200138301700182L);\n" +
            "GlobalTimeout globalTimeout = new GlobalTimeout();\n" +
            "globalTimeout.setId(1);\n" +
            "globalTimeout.setType(\"WAIT_BUYER_RETURN_ITEM_TIMEOUT\");\n" +
            "GlobalReverseTimeoutCallbackParam callbackParam = new GlobalReverseTimeoutCallbackParam();\n" +
            "callbackParam.setReverseOrderLine(globalReverseOrderLine.getReverseOrderLine());\n" +
            "callbackParam.setTimeout(globalTimeout)\n" +
            "callback.callback(callbackParam);";

    public static final String FREE_RETURN_WAIT_BUYER_RETURN_ITEM_WILL_TIMEOUT = "package com.alibaba.reverse.platform.plus.test\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.model.timeout.GlobalReverseTimeoutCallbackParam\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.model.timeout.GlobalTimeout\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.repository.ReverseOrderRepository\n" +
            "import com.alibaba.reverse.platform.kernel.server.activity.scenario.model.aggregation.GlobalReverseOrderLine\n" +
            "import com.alibaba.reverse.platform.plus.domain.timeout.AeTimeoutCallbackNoticeBuyerReturnWillTimeout\n" +
            "import org.springframework.context.ApplicationContext\n" +
            "ApplicationContext ac = context.getContext();\n" +
            "AeTimeoutCallbackNoticeBuyerReturnWillTimeout callback = ac.getBean(AeTimeoutCallbackNoticeBuyerReturnWillTimeout.class)\n" +
            "ReverseOrderRepository reverseOrderRepository = ac.getBean(ReverseOrderRepository.class)\n" +
            "GlobalReverseOrderLine globalReverseOrderLine = reverseOrderRepository.getReverseOrderLine(2200138301700182L);\n" +
            "GlobalTimeout globalTimeout = new GlobalTimeout();\n" +
            "globalTimeout.setId(1);\n" +
            "globalTimeout.setType(\"WAIT_BUYER_RETURN_ITEM_WILL_TIMEOUT\");\n" +
            "GlobalReverseTimeoutCallbackParam callbackParam = new GlobalReverseTimeoutCallbackParam();\n" +
            "callbackParam.setReverseOrderLine(globalReverseOrderLine.getReverseOrderLine());\n" +
            "callbackParam.setTimeout(globalTimeout)\n" +
            "callback.callback(callbackParam);";

    public static final String FR_V3_SELF_SELLER_RETURN_WAIT_BUYER_RETURN_ITEM_TIMEOUT = "package com.alibaba.reverse.platform.plus.test\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.model.timeout.GlobalReverseTimeoutCallbackParam\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.model.timeout.GlobalTimeout\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.repository.ReverseOrderRepository\n" +
            "import com.alibaba.reverse.platform.kernel.server.activity.scenario.model.aggregation.GlobalReverseOrderLine\n" +
            "import com.alibaba.reverse.platform.plus.domain.timeout.AeTimeoutSubmitReturnItemCallback\n" +
            "import org.springframework.context.ApplicationContext\n" +
            "ApplicationContext ac = context.getContext();\n" +
            "AeTimeoutSubmitReturnItemCallback callback = ac.getBean(AeTimeoutSubmitReturnItemCallback.class)\n" +
            "ReverseOrderRepository reverseOrderRepository = ac.getBean(ReverseOrderRepository.class)\n" +
            "GlobalReverseOrderLine globalReverseOrderLine = reverseOrderRepository.getReverseOrderLine(2200138301700182L);\n" +
            "GlobalTimeout globalTimeout = new GlobalTimeout();\n" +
            "globalTimeout.setId(1);\n" +
            "globalTimeout.setType(\"WAIT_BUYER_RETURN_ITEM_TIMEOUT\");\n" +
            "GlobalReverseTimeoutCallbackParam callbackParam = new GlobalReverseTimeoutCallbackParam();\n" +
            "callbackParam.setReverseOrderLine(globalReverseOrderLine.getReverseOrderLine());\n" +
            "callbackParam.setTimeout(globalTimeout)\n" +
            "callback.callback(callbackParam);";

    public static final String FR_V3_MAIL_SELLER_WAIT_DSCAN_RESULT_TIMEOUT = "package com.alibaba.reverse.platform.plus.test\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.model.timeout.GlobalReverseTimeoutCallbackParam\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.model.timeout.GlobalTimeout\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.repository.ReverseOrderRepository\n" +
            "import com.alibaba.reverse.platform.kernel.server.activity.scenario.model.aggregation.GlobalReverseOrderLine\n" +
            "import com.alibaba.reverse.platform.plus.domain.timeout.AeTimeoutCallbackLocalSellerReceivedTimeoutCallback\n" +
            "import org.springframework.context.ApplicationContext\n" +
            "ApplicationContext ac = context.getContext();\n" +
            "AeTimeoutCallbackLocalSellerReceivedTimeoutCallback callback = ac.getBean(AeTimeoutCallbackLocalSellerReceivedTimeoutCallback.class)\n" +
            "ReverseOrderRepository reverseOrderRepository = ac.getBean(ReverseOrderRepository.class)\n" +
            "GlobalReverseOrderLine globalReverseOrderLine = reverseOrderRepository.getReverseOrderLine(2200138301700182L);\n" +
            "GlobalTimeout globalTimeout = new GlobalTimeout();\n" +
            "globalTimeout.setId(1);\n" +
            "globalTimeout.setType(\"WAIT_DSCAN_RESULT_TIMEOUT\");\n" +
            "GlobalReverseTimeoutCallbackParam callbackParam = new GlobalReverseTimeoutCallbackParam();\n" +
            "callbackParam.setReverseOrderLine(globalReverseOrderLine.getReverseOrderLine());\n" +
            "callbackParam.setTimeout(globalTimeout)\n" +
            "callback.callback(callbackParam);";

    public static final String WAIT_BUYER_RETURN_ITEM_WILL_TIMEOUT = "package com.alibaba.reverse.platform.plus.test\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.model.timeout.GlobalReverseTimeoutCallbackParam\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.model.timeout.GlobalTimeout\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.repository.ReverseOrderRepository\n" +
            "import com.alibaba.reverse.platform.kernel.server.activity.scenario.model.aggregation.GlobalReverseOrderLine\n" +
            "import com.alibaba.reverse.platform.plus.domain.timeout.AeTimeoutCallbackNoticeBuyerReturnWillTimeout\n" +
            "import org.springframework.context.ApplicationContext\n" +
            "ApplicationContext ac = context.getContext();\n" +
            "AeTimeoutCallbackNoticeBuyerReturnWillTimeout callback = ac.getBean(AeTimeoutCallbackNoticeBuyerReturnWillTimeout.class)\n" +
            "ReverseOrderRepository reverseOrderRepository = ac.getBean(ReverseOrderRepository.class)\n" +
            "GlobalReverseOrderLine globalReverseOrderLine = reverseOrderRepository.getReverseOrderLine(2200138301700182L);\n" +
            "GlobalTimeout globalTimeout = new GlobalTimeout();\n" +
            "globalTimeout.setId(1);\n" +
            "globalTimeout.setType(\"WAIT_BUYER_RETURN_ITEM_WILL_TIMEOUT\");\n" +
            "GlobalReverseTimeoutCallbackParam callbackParam = new GlobalReverseTimeoutCallbackParam();\n" +
            "callbackParam.setReverseOrderLine(globalReverseOrderLine.getReverseOrderLine());\n" +
            "callbackParam.setTimeout(globalTimeout)\n" +
            "callback.callback(callbackParam);";

    public static final String FREE_RETURN_WAIT_RECEIVE_ITEM_TIMEOUT = "package com.alibaba.reverse.platform.plus.test\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.model.timeout.GlobalReverseTimeoutCallbackParam\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.model.timeout.GlobalTimeout\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.repository.ReverseOrderRepository\n" +
            "import com.alibaba.reverse.platform.kernel.server.activity.scenario.model.aggregation.GlobalReverseOrderLine\n" +
            "import com.alibaba.reverse.platform.plus.domain.timeout.AeTimeoutCallbackCheckClaimsStatusAndTakeAction\n" +
            "import org.springframework.context.ApplicationContext\n" +
            "ApplicationContext ac = context.getContext();\n" +
            "AeTimeoutCallbackCheckClaimsStatusAndTakeAction callback = ac.getBean(AeTimeoutCallbackCheckClaimsStatusAndTakeAction.class)\n" +
            "ReverseOrderRepository reverseOrderRepository = ac.getBean(ReverseOrderRepository.class)\n" +
            "GlobalReverseOrderLine globalReverseOrderLine = reverseOrderRepository.getReverseOrderLine(2200138301700182L);\n" +
            "GlobalTimeout globalTimeout = new GlobalTimeout();\n" +
            "globalTimeout.setId(1);\n" +
            "globalTimeout.setType(\"WAIT_RECEIVE_ITEM_TIMEOUT\");\n" +
            "GlobalReverseTimeoutCallbackParam callbackParam = new GlobalReverseTimeoutCallbackParam();\n" +
            "callbackParam.setReverseOrderLine(globalReverseOrderLine.getReverseOrderLine());\n" +
            "callbackParam.setTimeout(globalTimeout)\n" +
            "callback.callback(callbackParam);";

    public static final String WAIT_RECEIVE_ITEM_TIMEOUT = "package com.alibaba.reverse.platform.plus.test\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.model.timeout.GlobalReverseTimeoutCallback\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.model.timeout.GlobalReverseTimeoutCallbackParam\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.model.timeout.GlobalTimeout\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.repository.ReverseOrderRepository\n" +
            "import com.alibaba.reverse.platform.kernel.server.activity.scenario.model.aggregation.GlobalReverseOrderLine\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.service.impl.GlobalReverseTimeoutAgreeRefundCallback\n" +
            "import org.springframework.context.ApplicationContext\n" +
            "ApplicationContext ac = context.getContext();\n" +
            "GlobalReverseTimeoutAgreeRefundCallback callback = ac.getBean(GlobalReverseTimeoutAgreeRefundCallback.class)\n" +
            "ReverseOrderRepository reverseOrderRepository = ac.getBean(ReverseOrderRepository.class)\n" +
            "GlobalReverseOrderLine globalReverseOrderLine = reverseOrderRepository.getReverseOrderLine(2200138301700182L);\n" +
            "GlobalTimeout globalTimeout = new GlobalTimeout();\n" +
            "globalTimeout.setId(1);\n" +
            "globalTimeout.setType(\"WAIT_RECEIVE_ITEM_TIMEOUT\");\n" +
            "GlobalReverseTimeoutCallbackParam callbackParam = new GlobalReverseTimeoutCallbackParam();\n" +
            "callbackParam.setReverseOrderLine(globalReverseOrderLine.getReverseOrderLine());\n" +
            "callbackParam.setTimeout(globalTimeout)\n" +
            "callback.callback(callbackParam);";

    public static final String WAIT_RECEIVE_ITEM_WILL_TIMEOUT = "package com.alibaba.reverse.platform.plus.test\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.model.timeout.GlobalReverseTimeoutCallbackParam\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.model.timeout.GlobalTimeout\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.repository.ReverseOrderRepository\n" +
            "import com.alibaba.reverse.platform.kernel.server.activity.scenario.model.aggregation.GlobalReverseOrderLine\n" +
            "import com.alibaba.reverse.platform.plus.domain.timeout.AeTimeoutCallbackNoticeSellerConfirmReturnWillTimeout\n" +
            "import org.springframework.context.ApplicationContext\n" +
            "ApplicationContext ac = context.getContext();\n" +
            "AeTimeoutCallbackNoticeSellerConfirmReturnWillTimeout callback = ac.getBean(AeTimeoutCallbackNoticeSellerConfirmReturnWillTimeout.class)\n" +
            "ReverseOrderRepository reverseOrderRepository = ac.getBean(ReverseOrderRepository.class)\n" +
            "GlobalReverseOrderLine globalReverseOrderLine = reverseOrderRepository.getReverseOrderLine(2200138301700182L);\n" +
            "GlobalTimeout globalTimeout = new GlobalTimeout();\n" +
            "globalTimeout.setId(1);\n" +
            "globalTimeout.setType(\"WAIT_RECEIVE_ITEM_WILL_TIMEOUT\");\n" +
            "GlobalReverseTimeoutCallbackParam callbackParam = new GlobalReverseTimeoutCallbackParam();\n" +
            "callbackParam.setReverseOrderLine(globalReverseOrderLine.getReverseOrderLine());\n" +
            "callbackParam.setTimeout(globalTimeout)\n" +
            "callback.callback(callbackParam);";

    public static final String FR_V3_SELF_WAREHOUSE_QC_TIMEOUT = "package com.alibaba.reverse.platform.plus.test\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.model.timeout.GlobalReverseTimeoutCallbackParam\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.model.timeout.GlobalTimeout\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.repository.ReverseOrderRepository\n" +
            "import com.alibaba.reverse.platform.kernel.server.activity.scenario.model.aggregation.GlobalReverseOrderLine\n" +
            "import com.alibaba.reverse.platform.plus.domain.timeout.AeTimeoutCallbackCheckQCResultCallback\n" +
            "import org.springframework.context.ApplicationContext\n" +
            "ApplicationContext ac = context.getContext();\n" +
            "AeTimeoutCallbackCheckQCResultCallback callback = ac.getBean(AeTimeoutCallbackCheckQCResultCallback.class)\n" +
            "ReverseOrderRepository reverseOrderRepository = ac.getBean(ReverseOrderRepository.class)\n" +
            "GlobalReverseOrderLine globalReverseOrderLine = reverseOrderRepository.getReverseOrderLine(2200138301700182L);\n" +
            "GlobalTimeout globalTimeout = new GlobalTimeout();\n" +
            "globalTimeout.setId(1);\n" +
            "globalTimeout.setType(\"WAIT_QUALITY_RESULT_TIMEOUT\");\n" +
            "GlobalReverseTimeoutCallbackParam callbackParam = new GlobalReverseTimeoutCallbackParam();\n" +
            "callbackParam.setReverseOrderLine(globalReverseOrderLine.getReverseOrderLine());\n" +
            "callbackParam.setTimeout(globalTimeout)\n" +
            "callback.callback(callbackParam);";

    public static final String FR_V3_MAIL_SELLER_WAIT_RECEIVE_ITEM_TIMEOUT = "package com.alibaba.reverse.platform.plus.test\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.model.timeout.GlobalReverseTimeoutCallbackParam\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.model.timeout.GlobalTimeout\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.repository.ReverseOrderRepository\n" +
            "import com.alibaba.reverse.platform.kernel.server.activity.scenario.model.aggregation.GlobalReverseOrderLine\n" +
            "import com.alibaba.reverse.platform.plus.domain.timeout.AeTimeoutCallbackLocalSellerReceivedTimeoutCallback\n" +
            "import org.springframework.context.ApplicationContext\n" +
            "ApplicationContext ac = context.getContext();\n" +
            "AeTimeoutCallbackLocalSellerReceivedTimeoutCallback callback = ac.getBean(AeTimeoutCallbackLocalSellerReceivedTimeoutCallback.class)\n" +
            "ReverseOrderRepository reverseOrderRepository = ac.getBean(ReverseOrderRepository.class)\n" +
            "GlobalReverseOrderLine globalReverseOrderLine = reverseOrderRepository.getReverseOrderLine(2200138301700182L);\n" +
            "GlobalTimeout globalTimeout = new GlobalTimeout();\n" +
            "globalTimeout.setId(1);\n" +
            "globalTimeout.setType(\"WAIT_RECEIVE_ITEM_TIMEOUT\");\n" +
            "GlobalReverseTimeoutCallbackParam callbackParam = new GlobalReverseTimeoutCallbackParam();\n" +
            "callbackParam.setReverseOrderLine(globalReverseOrderLine.getReverseOrderLine());\n" +
            "callbackParam.setTimeout(globalTimeout)\n" +
            "callback.callback(callbackParam);";

    public static final String FREE_RETURN_WAIT_RECEIVE_ITEM_WILL_TIMEOUT = "package com.alibaba.reverse.platform.plus.test\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.model.timeout.GlobalReverseTimeoutCallbackParam\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.model.timeout.GlobalTimeout\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.repository.ReverseOrderRepository\n" +
            "import com.alibaba.reverse.platform.kernel.server.activity.scenario.model.aggregation.GlobalReverseOrderLine\n" +
            "import com.alibaba.reverse.platform.plus.domain.timeout.AeTimeoutCallbackNoticeSellerConfirmReturnWillTimeout\n" +
            "import org.springframework.context.ApplicationContext\n" +
            "ApplicationContext ac = context.getContext();\n" +
            "AeTimeoutCallbackNoticeSellerConfirmReturnWillTimeout callback = ac.getBean(AeTimeoutCallbackNoticeSellerConfirmReturnWillTimeout.class)\n" +
            "ReverseOrderRepository reverseOrderRepository = ac.getBean(ReverseOrderRepository.class)\n" +
            "GlobalReverseOrderLine globalReverseOrderLine = reverseOrderRepository.getReverseOrderLine(2200138301700182L);\n" +
            "GlobalTimeout globalTimeout = new GlobalTimeout();\n" +
            "globalTimeout.setId(1);\n" +
            "globalTimeout.setType(\"WAIT_RECEIVE_ITEM_WILL_TIMEOUT\");\n" +
            "GlobalReverseTimeoutCallbackParam callbackParam = new GlobalReverseTimeoutCallbackParam();\n" +
            "callbackParam.setReverseOrderLine(globalReverseOrderLine.getReverseOrderLine());\n" +
            "callbackParam.setTimeout(globalTimeout)\n" +
            "callback.callback(callbackParam);";

    public static final String WAREHOUSE_WAIT_BUYER_RETURN_ITEM_TIMEOUT = "package com.alibaba.reverse.platform.plus.test\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.model.timeout.GlobalReverseTimeoutCallbackParam\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.model.timeout.GlobalTimeout\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.repository.ReverseOrderRepository\n" +
            "import com.alibaba.reverse.platform.kernel.server.activity.scenario.model.aggregation.GlobalReverseOrderLine\n" +
            "import com.alibaba.reverse.platform.plus.domain.timeout.AeTimeoutCallbackCheckReturnStatusForOfficialOverseasWarehouseProductAndTakeAction\n" +
            "import org.springframework.context.ApplicationContext\n" +
            "ApplicationContext ac = context.getContext();\n" +
            "AeTimeoutCallbackCheckReturnStatusForOfficialOverseasWarehouseProductAndTakeAction callback = ac.getBean(AeTimeoutCallbackCheckReturnStatusForOfficialOverseasWarehouseProductAndTakeAction.class)\n" +
            "ReverseOrderRepository reverseOrderRepository = ac.getBean(ReverseOrderRepository.class)\n" +
            "GlobalReverseOrderLine globalReverseOrderLine = reverseOrderRepository.getReverseOrderLine(2200138301700182L);\n" +
            "GlobalTimeout globalTimeout = new GlobalTimeout();\n" +
            "globalTimeout.setId(1);\n" +
            "globalTimeout.setType(\"WAIT_BUYER_RETURN_ITEM_TIMEOUT\");\n" +
            "GlobalReverseTimeoutCallbackParam callbackParam = new GlobalReverseTimeoutCallbackParam();\n" +
            "callbackParam.setReverseOrderLine(globalReverseOrderLine.getReverseOrderLine());\n" +
            "callbackParam.setTimeout(globalTimeout)\n" +
            "callback.callback(callbackParam);";

    public static final String WAIT_CREATE_CLAIMS_TIMEOUT = "package com.alibaba.reverse.platform.plus.test\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.model.timeout.GlobalReverseTimeoutCallbackParam\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.model.timeout.GlobalTimeout\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.repository.ReverseOrderRepository\n" +
            "import com.alibaba.reverse.platform.kernel.server.activity.scenario.model.aggregation.GlobalReverseOrderLine\n" +
            "import com.alibaba.reverse.platform.plus.domain.timeout.AeTimeoutCallbackCheckCreateClaimsAndTakeAction\n" +
            "import org.springframework.context.ApplicationContext\n" +
            "ApplicationContext ac = context.getContext();\n" +
            "AeTimeoutCallbackCheckCreateClaimsAndTakeAction callback = ac.getBean(AeTimeoutCallbackCheckCreateClaimsAndTakeAction.class)\n" +
            "ReverseOrderRepository reverseOrderRepository = ac.getBean(ReverseOrderRepository.class)\n" +
            "GlobalReverseOrderLine globalReverseOrderLine = reverseOrderRepository.getReverseOrderLine(2200138301700182L);\n" +
            "GlobalTimeout globalTimeout = new GlobalTimeout();\n" +
            "globalTimeout.setId(1);\n" +
            "globalTimeout.setType(\"WAIT_CREATE_CLAIMS_TIMEOUT\");\n" +
            "GlobalReverseTimeoutCallbackParam callbackParam = new GlobalReverseTimeoutCallbackParam();\n" +
            "callbackParam.setReverseOrderLine(globalReverseOrderLine.getReverseOrderLine());\n" +
            "callbackParam.setTimeout(globalTimeout)\n" +
            "callback.callback(callbackParam);";

    public static final String RETURN_CHECK_CLAIM_AND_LOGISTIC_STATUS = "package com.alibaba.reverse.platform.plus.test\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.model.timeout.GlobalReverseTimeoutCallbackParam\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.model.timeout.GlobalTimeout\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.repository.ReverseOrderRepository\n" +
            "import com.alibaba.reverse.platform.kernel.server.activity.scenario.model.aggregation.GlobalReverseOrderLine\n" +
            "import com.alibaba.reverse.platform.plus.domain.timeout.CheckReturnStatusAndTakeAction\n" +
            "import org.springframework.context.ApplicationContext\n" +
            "ApplicationContext ac = context.getContext();" +
            "CheckReturnStatusAndTakeAction callback = ac.getBean(CheckReturnStatusAndTakeAction.class)\n" +
            "ReverseOrderRepository reverseOrderRepository = ac.getBean(ReverseOrderRepository.class)\n" +
            "GlobalReverseOrderLine globalReverseOrderLine = reverseOrderRepository.getReverseOrderLine(6232489467603223);\n" +
            "GlobalTimeout globalTimeout = new GlobalTimeout();\n" +
            "globalTimeout.setId(3);\n" +
            "globalTimeout.setType(\"WAIT_BUYER_RETURN_ITEM_TIMEOUT\");\n" +
            "GlobalReverseTimeoutCallbackParam callbackParam = new GlobalReverseTimeoutCallbackParam();\n" +
            "callbackParam.setReverseOrderLine(globalReverseOrderLine.getReverseOrderLine());\n" +
            "callbackParam.setTimeout(globalTimeout)\n" +
            "callback.callback(callbackParam);";


    public static final String FR_V3_FULFILLMENT_ACCEPT_ORDER_TIMEOUT = "package com.alibaba.reverse.platform.plus.test\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.model.timeout.GlobalReverseTimeoutCallbackParam\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.model.timeout.GlobalTimeout\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.repository.ReverseOrderRepository\n" +
            "import com.alibaba.reverse.platform.kernel.server.activity.scenario.model.aggregation.GlobalReverseOrderLine\n" +
            "import com.alibaba.reverse.platform.plus.domain.timeout.AeTimeoutCallbackAcceptMailAndTakeAction\n" +
            "import org.springframework.context.ApplicationContext\n" +
            "ApplicationContext ac = context.getContext();\n" +
            "AeTimeoutCallbackAcceptMailAndTakeAction callback = ac.getBean(AeTimeoutCallbackAcceptMailAndTakeAction.class)\n" +
            "ReverseOrderRepository reverseOrderRepository = ac.getBean(ReverseOrderRepository.class)\n" +
            "GlobalReverseOrderLine globalReverseOrderLine = reverseOrderRepository.getReverseOrderLine(2200138301700182L);\n" +
            "GlobalTimeout globalTimeout = new GlobalTimeout();\n" +
            "globalTimeout.setId(1);\n" +
            "globalTimeout.setType(\"WAIT_FULFILLMENT_ACCEPT_TIMEOUT\");\n" +
            "GlobalReverseTimeoutCallbackParam callbackParam = new GlobalReverseTimeoutCallbackParam();\n" +
            "callbackParam.setReverseOrderLine(globalReverseOrderLine.getReverseOrderLine());\n" +
            "callbackParam.setTimeout(globalTimeout)\n" +
            "callback.callback(callbackParam);";

    public static final String WAIT_AFTER_WAREHOUSE_ARBITRATION_TIMEOUT = "package com.alibaba.reverse.platform.plus.test\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.model.timeout.GlobalReverseTimeoutCallbackParam\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.model.timeout.GlobalTimeout\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.repository.ReverseOrderRepository\n" +
            "import com.alibaba.reverse.platform.kernel.server.activity.scenario.model.aggregation.GlobalReverseOrderLine\n" +
            "import com.alibaba.reverse.platform.plus.domain.timeout.AeTimeoutAfterWarehouseArbitrationCallback\n" +
            "import org.springframework.context.ApplicationContext\n" +
            "ApplicationContext ac = context.getContext();\n" +
            "AeTimeoutAfterWarehouseArbitrationCallback callback = ac.getBean(AeTimeoutAfterWarehouseArbitrationCallback.class)\n" +
            "ReverseOrderRepository reverseOrderRepository = ac.getBean(ReverseOrderRepository.class)\n" +
            "GlobalReverseOrderLine globalReverseOrderLine = reverseOrderRepository.getReverseOrderLine(2200138301700182L);\n" +
            "GlobalTimeout globalTimeout = new GlobalTimeout();\n" +
            "globalTimeout.setId(1);\n" +
            "globalTimeout.setType(\"WAIT_AFTER_WAREHOUSE_ARBITRATION_TIMEOUT\");\n" +
            "GlobalReverseTimeoutCallbackParam callbackParam = new GlobalReverseTimeoutCallbackParam();\n" +
            "callbackParam.setReverseOrderLine(globalReverseOrderLine.getReverseOrderLine());\n" +
            "callbackParam.setTimeout(globalTimeout)\n" +
            "callback.callback(callbackParam);";

    public static final String FR_V3_MAIL_SELLER_WAIT_RECEIVE_ITEM_REMINDER_TIMEOUT = "package com.alibaba.reverse.platform.plus.test\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.model.timeout.GlobalReverseTimeoutCallbackParam\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.model.timeout.GlobalTimeout\n" +
            "import com.alibaba.reverse.platform.kernel.domain.dispute.repository.ReverseOrderRepository\n" +
            "import com.alibaba.reverse.platform.kernel.server.activity.scenario.model.aggregation.GlobalReverseOrderLine\n" +
            "import com.alibaba.reverse.platform.plus.domain.timeout.NewAeTimeoutCallbackNoticeSellerConfirmReturnWillTimeout\n" +
            "import org.springframework.context.ApplicationContext\n" +
            "ApplicationContext ac = context.getContext();\n" +
            "NewAeTimeoutCallbackNoticeSellerConfirmReturnWillTimeout callback = ac.getBean(NewAeTimeoutCallbackNoticeSellerConfirmReturnWillTimeout.class)\n" +
            "ReverseOrderRepository reverseOrderRepository = ac.getBean(ReverseOrderRepository.class)\n" +
            "GlobalReverseOrderLine globalReverseOrderLine = reverseOrderRepository.getReverseOrderLine(2200138301700182L);\n" +
            "GlobalTimeout globalTimeout = new GlobalTimeout();\n" +
            "globalTimeout.setId(1);\n" +
            "globalTimeout.setType(\"WAIT_RECEIVE_ITEM_WILL_TIMEOUT\");\n" +
            "GlobalReverseTimeoutCallbackParam callbackParam = new GlobalReverseTimeoutCallbackParam();\n" +
            "callbackParam.setReverseOrderLine(globalReverseOrderLine.getReverseOrderLine());\n" +
            "callbackParam.setTimeout(globalTimeout)\n" +
            "callback.callback(callbackParam);";

    public static String getScript(String timeoutType, Long reverseOrderLineId, int timeoutId) {
        switch (timeoutType) {
            case "普通协商超时":
                return buildScript(WAIT_SOLUTION_TIMEOUT_NEGOTIATION, reverseOrderLineId, timeoutId);
            case "售后宝协商超时":
                return buildScript(WAIT_SOLUTION_TIMEOUT_GUARANTEE, reverseOrderLineId, timeoutId);
            case "卖家响应买家取消订单超时":
                return buildScript(WAIT_SELLER_RESPONSE_CANCEL, reverseOrderLineId, timeoutId);
            case "买家退货超时-自寄":
                return buildScript(WAIT_BUYER_RETURN_ITEM_TIMEOUT, reverseOrderLineId, timeoutId);
            case "买家退货超时-免费退":
                return buildScript(FREE_RETURN_WAIT_BUYER_RETURN_ITEM_TIMEOUT, reverseOrderLineId, timeoutId);
            case "买家退货超时-海外仓免费退":
                return buildScript(WAREHOUSE_WAIT_BUYER_RETURN_ITEM_TIMEOUT, reverseOrderLineId, timeoutId);
            case "买家退货超时提醒-自寄":
                return buildScript(WAIT_BUYER_RETURN_ITEM_WILL_TIMEOUT, reverseOrderLineId, timeoutId);
            case "FR3.0-买家退货超时-平台仓-面单": // 本地商家to es 平台仓
                return buildScript(FR_V3_MAIL_RETURN_WAIT_BUYER_RETURN_ITEM_TIMEOUT, reverseOrderLineId, timeoutId);
            case "FR3.0-买家退货超时-商家仓-自寄":
                return buildScript(FR_V3_SELF_SELLER_RETURN_WAIT_BUYER_RETURN_ITEM_TIMEOUT, reverseOrderLineId, timeoutId);
            case "FR3.0-DSCAN超时-商家仓-面单":
                return buildScript(FR_V3_MAIL_SELLER_WAIT_DSCAN_RESULT_TIMEOUT, reverseOrderLineId, timeoutId);
            case "买家退货超时提醒-免费退":
            case "123":
                return buildScript(FREE_RETURN_WAIT_BUYER_RETURN_ITEM_WILL_TIMEOUT, reverseOrderLineId, timeoutId);
            case "卖家收货超时":
                return buildScript(WAIT_RECEIVE_ITEM_TIMEOUT, reverseOrderLineId, timeoutId);
            case "卖家收货超时提醒":
                return buildScript(WAIT_RECEIVE_ITEM_WILL_TIMEOUT, reverseOrderLineId, timeoutId);
            case "仓库收货超时提醒":
                return buildScript(FREE_RETURN_WAIT_RECEIVE_ITEM_WILL_TIMEOUT, reverseOrderLineId, timeoutId);
            case "仓库收货超时":
                return buildScript(FREE_RETURN_WAIT_RECEIVE_ITEM_TIMEOUT, reverseOrderLineId, timeoutId);
            case "卖家填写退货地址超时":
                return buildScript(WAIT_SELLER_FILL_ADDRESS_TIMEOUT, reverseOrderLineId, timeoutId);
            case "理赔报案重试":
                return buildScript(WAIT_CREATE_CLAIMS_TIMEOUT, reverseOrderLineId, timeoutId);
            case "买家退货超时-免费退-退货检查期":
                return buildScript(RETURN_CHECK_CLAIM_AND_LOGISTIC_STATUS, reverseOrderLineId, timeoutId);
            case "FR3.0-平台仓-自寄-质检超时":
                return buildScript(FR_V3_SELF_WAREHOUSE_QC_TIMEOUT, reverseOrderLineId, timeoutId);
            case "FR3.0-平台仓-自寄-卖家确认收货超时":
                return buildScript(FR_V3_MAIL_SELLER_WAIT_RECEIVE_ITEM_TIMEOUT, reverseOrderLineId, timeoutId);
            case "FR3.0-履约接单超时":
                return buildScript(FR_V3_FULFILLMENT_ACCEPT_ORDER_TIMEOUT, reverseOrderLineId, timeoutId);
            case "二揽确认超时":
                return buildScript(WAIT_BUYER_RECOLLECT_CONFIRM, reverseOrderLineId, timeoutId);
            case "仓后仲裁超时":
                return buildScript(WAIT_AFTER_WAREHOUSE_ARBITRATION_TIMEOUT, reverseOrderLineId, timeoutId);
            case "卖家确认收货超时提醒":
                return buildScript(FR_V3_MAIL_SELLER_WAIT_RECEIVE_ITEM_REMINDER_TIMEOUT, reverseOrderLineId, timeoutId);
        }
        return "";
    }

    private static String buildScript(String timeout, Long reverseOrderLineId, int timeoutId) {
        return timeout
                .replace("reverseOrderRepository.getReverseOrderLine(2200138301700182L);", "reverseOrderRepository.getReverseOrderLine(" + reverseOrderLineId + ");")
                .replace("globalTimeout.setId(1);", "globalTimeout.setId(" + timeoutId + ");");
    }

    public static String buildReverseTimeoutScript(Long reverseOrderLineId, String indexTimeoutType, String indexCallback) {
        return buildNewScript(WAIT_SOLUTION_TIMEOUT_NEGOTIATION, reverseOrderLineId, indexTimeoutType,indexCallback);
    }

    private static String buildNewScript(String timeout, Long reverseOrderLineId, String indexTimeoutType, String indexCallback) {
        return timeout
                .replace("AeTimeoutCallbackNegotiationTimeoutCallback",indexCallback)
                .replace("reverseOrderRepository.getReverseOrderLine(2200138301700182L);", "reverseOrderRepository.getReverseOrderLine(" + reverseOrderLineId + ");")
                .replace("WAIT_SOLUTION_TIMEOUT",indexTimeoutType);
    }
    public static void main(String[] args) {
        System.err.println(FR_V3_MAIL_SELLER_WAIT_RECEIVE_ITEM_REMINDER_TIMEOUT);
    }

}
