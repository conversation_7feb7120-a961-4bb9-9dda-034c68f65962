package com.aliexpress.databank.utils;

import com.alibaba.ecommerce.module.Response;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.global.buy.api.model.*;
import com.alibaba.global.buy.api.model.source.PlatformType;
import com.alibaba.global.buy.api.model.source.Source;
import com.alibaba.global.buy.api.request.CreateOrderRequest;
import com.alibaba.global.buy.api.request.RenderOrderRequest;
import com.alibaba.global.buy.api.request.param.CreateQueryParams;
import com.alibaba.global.buy.api.request.param.RenderProtocol;
import com.alibaba.global.buy.api.request.param.RenderQueryParams;
import com.alibaba.global.buy.api.response.natives.*;
import com.alibaba.global.carts.api.request.dto.CartPageOptionDTO;
import com.alibaba.global.carts.api.request.dto.CartShipToDTO;
import com.alibaba.ultron.model.protocol.Protocol;
import com.aliexpress.databank.constant.Constant;
import com.aliexpress.databank.dataobject.BuyerParamsDTO;
import com.aliexpress.databank.dataobject.GetRenderQueryParamsDTO;
import com.aliexpress.databank.dataobject.TradeDTO;
import com.aliexpress.issue.dispute.constant.CancelOrderEvent;
import com.aliexpress.issue.dispute.pojo.common.CancelOrderOperatorRequest;
import com.google.common.collect.Maps;
import com.taobao.eagleeye.EagleEye;
import com.taobao.mtop.api.domain.MessageRequest;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayOutputStream;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Stream;
import java.util.zip.GZIPOutputStream;


public class ConvertParam {

    private static final Logger log = LoggerFactory.getLogger(ConvertParam.class);

    public static TradeDTO convertParamToTradeDTO(String params) {
        JSONObject jsonObject = JSON.parseObject(params);
        TradeDTO inputParam = new TradeDTO();
        String productStr = jsonObject.getString(Constant.ITEM_IDS);
        List<Long> productlist = parseLongFromStr(productStr);
        inputParam.setProductidlist(productlist);
        inputParam.setCurrency(jsonObject.getString("currency"));
        inputParam.setBuyerId(jsonObject.getLong("buyerId"));
        inputParam.setProductcount(jsonObject.getInteger("productcount"));
        return inputParam;

    }

    /**
     * change str to list
     * input: 1,2,3
     * output: [1, 2, 3]
     */
    public static List<Long> parseLongFromStr(String str) {
        List<Long> ids = new ArrayList<>();
        for (String id : str.split(",")) {
            ids.add(Long.parseLong(id));
        }
        return ids;
    }

    public static CommonDTO getCommonDTO(Long buyerId) {
        CommonDTO commonDTO = new CommonDTO();

        HeaderDTO headerDTO = new HeaderDTO();
//        headerDTO.setIp("************");
        commonDTO.setHeaderDTO(headerDTO);

        Source source = new Source();
        source.setPlatformType(PlatformType.PC);
        source.setOrderFrom(OrderFrom.BUY_NOW);
        source.setRenderType(RenderType.PLACE_ORDER);
        commonDTO.setSource(source);

        commonDTO.setBuyerId(buyerId);
        return commonDTO;
    }

    public static Map<String, Object> getExtraParams(String currency) {
        Map<String, Object> extraParams = new ConcurrentHashMap<>();
        extraParams.put("orderFrom", "MAIN_DETAIL");
        extraParams.put("locale", "en_US");
        extraParams.put("machineType", "OTHER.PC");
        extraParams.put("intentional_currency", currency);
        return extraParams;
    }

    public static Map<String, Object> getExtraParams(String currency, String countryCode) {
        Map<String, Object> extraParams = new ConcurrentHashMap<>();
        extraParams.put("orderFrom", "MAIN_DETAIL");
        extraParams.put("locale", "en_US");
        extraParams.put("machineType", "OTHER.PC");
        extraParams.put("intentional_currency", currency);
        if (StringUtils.isNotBlank(countryCode)) {
            Map<String, String> userAddress = new HashMap<>();
            userAddress.put("countryCode", countryCode);
            extraParams.put("buyerCountry", countryCode);
            extraParams.put("userAddress", JSON.toJSONString(userAddress));
        }
        return extraParams;
    }

    // get params of create order  from render response
    public static CreateOrderRequest toCreateOrderRequest(Result renderResponse, Long buyerId, String countryCode,
                                                          String currency, String deliveryOption) throws Exception {
        CreateOrderRequest createOrderRequest = new CreateOrderRequest();
        // convert promotionOrder
        createOrderRequest.setPromotionDTO(getPromotionDto(renderResponse));

        // convert address
        createOrderRequest.setAddressDTO(getSelectedAddress(buyerId, renderResponse.getAddresses(), DeliveryAddressType.ShippingAddress, countryCode));

        // convert payment
        createOrderRequest.setPaymentDTO(getPaymentDTO(renderResponse.getPayment()));

        // convert orders
        createOrderRequest.setOrderDTOS(getOrderDTOs(renderResponse.getOrders(), deliveryOption));

        // convert extraParams
        createOrderRequest.setExtraParams(getExtraParams(currency, countryCode));

        createOrderRequest.setRequestKey(UUID.randomUUID().toString());

        createOrderRequest.setCommonDTO(getCommonDTO(buyerId));

        return createOrderRequest;
    }

    private static PromotionDTO getPromotionDto(Result renderResponse) {
        PromotionDTO promotionDTO = new PromotionDTO();
        Map<String, Long> chosenPromotionIds = new HashMap<>();
        for (Promotion promotion : renderResponse.getPromotions()) {
            if (promotion.isSelected()) {
                chosenPromotionIds.put(promotion.getTypeCode(), Long.parseLong(promotion.getActivityId()));
            }
        }

        Boolean hasPlatformCoupon = false;
        Boolean hasAcrossStoreShoppingCoupon = false;
        for (Order order : renderResponse.getOrders()) {
            for (Promotion promotion : order.getPromotions()) {
                if (promotion.isSelected()) {
                    if (promotion.getTypeCode().equalsIgnoreCase("shopCoupon")) {
                        String key = promotion.getTypeCode() + "|" + order.getSeller().getSellerId();
                        chosenPromotionIds.put(key, Long.valueOf(promotion.getId()));
                    } else {
                        chosenPromotionIds.put(promotion.getTypeCode(), Long.valueOf(promotion.getId()));
                    }
                    if ("platformCoupon".equals(promotion.getTypeCode())) {
                        hasPlatformCoupon = true;
                    }
                    if ("acrossStoreShoppingCoupon".equals(promotion.getTypeCode())) {
                        hasAcrossStoreShoppingCoupon = true;
                    }
                }
            }
        }

        if (!hasPlatformCoupon) {
            chosenPromotionIds.put("platformCoupon", 999L);
        }

        if (!hasAcrossStoreShoppingCoupon) {
            chosenPromotionIds.put("acrossStoreShoppingCoupon", 999L);
        }


        promotionDTO.setChosenPromotionIds(chosenPromotionIds);
        return promotionDTO;
    }

    private static AddressDTO getSelectedAddress(Long buyerId, List<Address> addresses, DeliveryAddressType deliveryAddressType, String countryCode) throws Exception {
        String addressId = "";
        String selectedAddressId = "";
        // 默认从
        for (Address address : addresses) {
            if (StringUtils.isNotBlank(countryCode) && address.getCountryCode().equalsIgnoreCase(countryCode)) {
                addressId = address.getAddressId();
                break;
            }
            if (address.isSelected()) {
                selectedAddressId = address.getAddressId();
            }
        }
        if (StringUtils.isNotBlank(countryCode) && addressId.isEmpty()) {
            if (addresses.size() > 1) {
                JSONObject address = getAddressDTOByCountryCode(buyerId, countryCode, addresses.get(0).getAddressId());
                HsfUtil.updateUserAddress(buyerId, address);
                addressId = addresses.get(0).getAddressId();
            }
        }
        return getAddressDTO(StringUtils.isEmpty(countryCode) ? selectedAddressId : addressId, deliveryAddressType);
    }

    private static JSONObject getAddressDTOByCountryCode(Long buyerId, String countryCode, String addressId) {
        switch (countryCode) {
            case "RU":
                return AddressUtil.getRuAddress(buyerId, "RU", addressId);
            case "US":
                return AddressUtil.getUsAddress(buyerId, "US", addressId);
            case "ES":
                return AddressUtil.getEsAddress(buyerId, "ES", addressId);
            case "FR":
                return AddressUtil.getFrAddress(buyerId, "FR", addressId);
            case "BR":
                return AddressUtil.getBrAddress(buyerId, "BR", addressId);
            case "NL":
                return AddressUtil.getNlAddress(buyerId, "NL", addressId);
            case "IL":
                return AddressUtil.getIlAddress(buyerId, "IL", addressId);
            case "PL":
                return AddressUtil.getPlAddress(buyerId, "PL", addressId);
            case "UK":
            case "GB":
                return AddressUtil.getUkAddress(buyerId, countryCode, addressId);
            case "KR":
                return AddressUtil.getKrAddress(buyerId, "KR", addressId);
            case "DE":
                return AddressUtil.getDeAddress(buyerId, "DE", addressId);
            case "IT":
                return AddressUtil.getItAddress(buyerId, "IT", addressId);
            case "CL":
                return AddressUtil.getClAddress(buyerId, "CL", addressId);
            case "CA":
                return AddressUtil.getCaAddress(buyerId, "CA", addressId);
            case "UA":
                return AddressUtil.getUaAddress(buyerId, "UA", addressId);
            case "JP":
                return AddressUtil.getJpAddress(buyerId, "JP", addressId);
            case "AU":
                return AddressUtil.getAuAddress(buyerId, "AU", addressId);
            case "MX":
                return AddressUtil.getMxAddress(buyerId, "MX", addressId);
            case "BE":
                return AddressUtil.getBeAddress(buyerId, "BE", addressId);
            case "CZ":
                return AddressUtil.getCzAddress(buyerId, "CZ", addressId);
            case "SA":
                return AddressUtil.getSaAddress(buyerId, "SA", addressId);
            case "CH":
                return AddressUtil.getChAddress(buyerId, "CH", addressId);
            case "PT":
                return AddressUtil.getPtAddress(buyerId, "PT", addressId);
            case "AE":
                return AddressUtil.getAeAddress(buyerId, "AE", addressId);
        }
        return null;
    }

    private static AddressDTO getAddressDTO(String addressId, DeliveryAddressType addressType) {
        AddressDTO addressDTO = new AddressDTO();
        addressDTO.setDeliveryAddressType(addressType);
        DeliveryAddressDTO deliveryAddressDTO = new DeliveryAddressDTO();
        deliveryAddressDTO.setAddressId(Long.parseLong(addressId));
        addressDTO.setDeliveryAddressDTO(deliveryAddressDTO);
        return addressDTO;
    }

    public static PaymentDTO getPaymentDTO(Payment payment) {
        if (payment == null) {
            return null;
        }
        PaymentDTO paymentDTO = null;
        List<String> paymentChannels = payment.getPaymentChannels();
        if (CollectionUtils.isNotEmpty(paymentChannels)) {
            // get the first payment channel which render order response return
            paymentDTO = getPayment(paymentChannels.get(0));
        }
        return paymentDTO;
    }

    private static PaymentDTO getPayment(String paymentChannel) {
        PaymentDTO paymentDTO = new PaymentDTO();
        paymentDTO.setPaymentChannel(paymentChannel);
        paymentDTO.setPaymentMethod(paymentChannel);
        CashierDTO cashierDTO = new CashierDTO();
        cashierDTO.setCashierOption(paymentChannel);
        cashierDTO.setSubCashierOption(paymentChannel);
        paymentDTO.setCashierDTO(cashierDTO);
        return paymentDTO;
    }

    private static List<OrderDTO> getOrderDTOs(List<Order> orders, String deliveryOption) {
        List<OrderDTO> orderDTOS = new ArrayList<>();
        for (Order order : orders) {
            OrderDTO orderDTO = new OrderDTO();
            List<OrderLineDTO> orderLineDTOs = new ArrayList<>();
            for (OrderLine orderLine : order.getOrderLines()) {
                OrderLineDTO orderLineDTO = new OrderLineDTO();
                Long amount = orderLine.getBuyAmount().getActualAmount();
                orderLineDTO.setQuantity(amount.intValue());
                if (orderLine.getProduct() != null) {
                    orderLineDTO.setCartId(orderLine.getProduct().getCartId());
                    orderLineDTO.setItemId(orderLine.getProduct().getProductId());
                    orderLineDTO.setSkuId(orderLine.getProduct().getSkuId());
                    Map<String, String> extraParams = Maps.newConcurrentMap();
                    if (orderLine.getProduct().getSkuAttr() != null) {
                        orderLineDTO.setSkuAttr(orderLine.getProduct().getSkuAttr());
                        extraParams.put("sku_custom_attr", orderLine.getProduct().getSkuAttr());
                    }
                    orderLineDTO.setExtraParams(extraParams);
                }
                if (StringUtils.isEmpty(deliveryOption)) {
                    orderLineDTO.setDeliveryOrderLineDTO(getDeliveryOrderLineDTO(orderLine.getTransportMethods()));
                } else {
                    // 不支持多子订单
                    orderLineDTO.setDeliveryOrderLineDTO(getDeliveryOrderLineDTO(deliveryOption));
                }
                orderLineDTOs.add(orderLineDTO);
            }
            orderDTO.setOrderLineDTOs(orderLineDTOs);
            orderDTOS.add(orderDTO);
        }
        return orderDTOS;
    }

    private static DeliveryOrderLineDTO getDeliveryOrderLineDTO(List<TransportMethod> transportMethods) {
        DeliveryOrderLineDTO deliveryOrderLineDTO = new DeliveryOrderLineDTO();
        TransportMethodDTO transportMethodDTO = new TransportMethodDTO();
        for (TransportMethod transportMethod : transportMethods) {
            if (transportMethod.isSelected()) {
                transportMethodDTO.setDeliveryProviderName(transportMethod.getDeliveryOption());
                transportMethodDTO.setDeliveryOption(transportMethod.getDeliveryOption());
                break;
            }
        }
        deliveryOrderLineDTO.setTransportMethodDTO(transportMethodDTO);
        return deliveryOrderLineDTO;
    }

    // 不支持多子
    private static DeliveryOrderLineDTO getDeliveryOrderLineDTO(String deliveryOption) {
        DeliveryOrderLineDTO deliveryOrderLineDTO = new DeliveryOrderLineDTO();
        TransportMethodDTO transportMethodDTO = new TransportMethodDTO();
        transportMethodDTO.setDeliveryProviderName(deliveryOption);
        transportMethodDTO.setDeliveryOption(deliveryOption);
        deliveryOrderLineDTO.setTransportMethodDTO(transportMethodDTO);
        return deliveryOrderLineDTO;
    }

    public static CancelOrderOperatorRequest getCancelOrderOperatorRequestBySellerSide(Long sellerId, Long orderId,
                                                                                       String cancelReason,
                                                                                       List<Long> orderLines) {
        CancelOrderOperatorRequest cancelOrderOperatorRequest = new CancelOrderOperatorRequest();
        cancelOrderOperatorRequest.setOrderLineIds(orderLines);
        cancelOrderOperatorRequest.setAdminAliId(sellerId);
        cancelOrderOperatorRequest.setOrderId(orderId);
        cancelOrderOperatorRequest.setCancelEvent(CancelOrderEvent.BuyerCancel.getValue());
        cancelOrderOperatorRequest.setCancelReason(cancelReason);
        cancelOrderOperatorRequest.setOperatorAliId(sellerId);
        cancelOrderOperatorRequest.setCurrentOperatorRole("pc");
        cancelOrderOperatorRequest.setIssueOperateSource("hsf");
        cancelOrderOperatorRequest.setOperatorRole("platform");
        cancelOrderOperatorRequest.setOperatorMemo("data bank");
        return cancelOrderOperatorRequest;
    }

    public static Map<String, String> getHttpHeaders() {
        Map<String, String> headers = new HashMap<>();
        headers.put("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_13_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/73.0.3683.103 Safari/537.36");
        headers.put("Host", "localhost:7003");
        return headers;
    }

    public static CartShipToDTO getCartShipTo(String shipToCountry) {
        CartShipToDTO cartShipToDTO = new CartShipToDTO();
        cartShipToDTO.setShipToCountry(shipToCountry);
        return cartShipToDTO;
    }

    public static CartPageOptionDTO getPageOption() {
        CartPageOptionDTO cartPageOptionDTO = new CartPageOptionDTO();
        cartPageOptionDTO.setPage(1);
        cartPageOptionDTO.setNext(true);
        cartPageOptionDTO.setEndPage(false);
        return cartPageOptionDTO;
    }

    public static Set<String> getDeliveryOptionFromRenderRes(Result renderResponse) {
        Set<String> shippingCodes = new HashSet<>();
        renderResponse.getOrders().forEach(order -> order.getOrderLines()
                .forEach(orderLines -> orderLines.getTransportMethods()
                        .forEach(transportMethod -> shippingCodes.add(transportMethod.getDeliveryOption()))));
        return shippingCodes;
    }

    public static Map<String, Object> gerExtParams(GetRenderQueryParamsDTO getRenderQueryParamsDTO) {
        Map<String, Object> extraParams = Maps.newHashMap();
        extraParams.put("locale", "en_US");
        extraParams.put("step_guide", "1");
//      TODO 后续可扩展
        extraParams.put("channelInfo", "{}");
        extraParams.put("isVirtualProduct", "false");
        extraParams.put("siteType", "pdp");
        extraParams.put("platformType", "NATIVE");
//      TODO 后续可动态
        extraParams.put("pdpBuyParams", "{\"promotionToolCodes\":[\"mockedSalePrice\"]}");
        extraParams.put("pdp-spm", "{\"spm-url\":\"a1z65.orderdetail.0.0\",\"spm-pre\":\"a1z65.orderlist.0.0\"}");
        extraParams.put("intentional_currency", getRenderQueryParamsDTO.getCurrencyCode());
        extraParams.put("buyerCountry", getRenderQueryParamsDTO.getCountryCode());
        Stream.of(new AbstractMap.SimpleEntry<>("shareGroup", getRenderQueryParamsDTO.getShareGroup()),
               new AbstractMap.SimpleEntry<>("shareGroupCode", getRenderQueryParamsDTO.getCurrencyCode()))
            .filter(entry -> !StringUtils.isEmpty(entry.getValue()))
            .forEach(entry -> extraParams.put(entry.getKey(), entry.getValue()));
        return extraParams;
    }

    public static RenderQueryParams getRenderQueryParams(GetRenderQueryParamsDTO getRenderQueryParamsDTO) throws Exception {
        RenderQueryParams params = new RenderQueryParams();
        MessageRequest messageRequest = new MessageRequest();
        Map<String, String> headers = Maps.newHashMap();
        List<BuyerParamsDTO.ItemsDTO> itemsDTOS = new ArrayList<>();
        for (String itemId : getRenderQueryParamsDTO.getItemIds()) {
            BuyerParamsDTO.ItemsDTO itemsDTO = new BuyerParamsDTO.ItemsDTO();
            JSONObject productQueryResponse = HsfUtil.getProductByProductId(Long.valueOf(itemId));
            // 为每个商品单独处理skuId
            String currentSkuId = getRenderQueryParamsDTO.getSkuId();
            if (Objects.equals(currentSkuId, "")) {
                currentSkuId = productQueryResponse.getJSONObject("singleProduct")
                        .getJSONObject("skuIdAndSkuMap").toJavaObject(Map.class).keySet().toArray()[0].toString();
            }
            itemsDTO.setSkuId(currentSkuId);
            itemsDTO.setItemId(itemId);
            itemsDTO.setQuantity(getRenderQueryParamsDTO.getQuantity());
            itemsDTO.setDeliveryOption(getRenderQueryParamsDTO.getShippingMethod());
            itemsDTOS.add(itemsDTO);
        }
        BuyerParamsDTO buyerParamsDTO = new BuyerParamsDTO();
        buyerParamsDTO.setItems(itemsDTOS);
        buyerParamsDTO.setOrderFrom("BUY_NOW");
        params.setExtraParam(JSONObject.toJSONString(ConvertParam.gerExtParams(getRenderQueryParamsDTO)));
        params.setBuyParams(JSON.toJSONString(buyerParamsDTO));
        params.setBuyerId(getRenderQueryParamsDTO.getBuyerId());
        params.setCurrency(getRenderQueryParamsDTO.getCurrencyCode());
        messageRequest.setTtid("201200@Aliexpress_iphone_8.100.0.100000243");
        headers.put("x-reqbiz-ext", "cust2%3D6%3A7619%2Cglobal_abtest%3D%7B%2522st_503056%2522%253A%25222128%2522%252C%2522st_503418%2522%253A%25222474%2522%252C%2522st_502367%2522%253A%25222033%2522%252C%2522st_25032%2522%253A%2522576%2522%252C%2522st_502860%2522%253A%25222077%2522%252C%2522st_503417%2522%253A%25222471%2522%252C%2522st_501543%2522%253A%25221719%2522%252C%2522st_503116%2522%253A%25222072%2522%7D%2Cvv%3D8.101.1%7C418%7CWIFI%7C%7C0%7C1%2Cmwua%3Di8gQBffALalHmIAgmwWB2GTQkcpjHcHANZj5pjvoQDsPXeplVn1Z6x8JAm8kvcYdaJXfp8msOm2M6wa7V9OUD2PNvx7Fpt3Y%2BWkI3bMwfUrTLJMX1bvBxJz8K1x3bwUsbc4946TuWrL9%2FAOLTxNp%2FLRLRcvRf2m7nMWf%2B0DM%2BDx3a0w%3D%3D%2Cdia%3D2175784400%2Cscenario%3DCart%2Cdm%3DiPhone%2CUser-Agent%3DAEI%2F418%28iOS%3B17.4.1%3BApple%3BiPhone%29%2Cdid%3DZUChoI6CegQDABy5hcjuZNJA%2Ccust1%3D1290x2796%2Caffi_params%3DAFFILIATE%7C1709729809423%7C0%7C4fd9c5283d9b4534a358b53f4d987334-1709729809423-05605-_mL7WjE6%7C%7C1859103223%2Cmodel%3DiPhone16%2C2");
        messageRequest.setHeaders(headers);
        params.setMtopContext(messageRequest);
        return params;

    }

    public static String transRenderOrderResponseModel(HashMap<String, Object> renderOrderResponseModel) {
        Map<String, Object> paramsMap = Maps.newHashMap();
        Map dataMap = JSON.parseObject(JSON.toJSONString(renderOrderResponseModel.get("data")), Map.class);
        dataMap.remove("aeg_summary_checkout_v2_TOTAL_SUMMARY");
        paramsMap.put("data", JSON.toJSONString(dataMap));
        paramsMap.put("linkage", JSON.toJSONString(renderOrderResponseModel.get("linkage")));
        paramsMap.put("hierarchy", JSON.toJSONString(renderOrderResponseModel.get("hierarchy")));
        paramsMap.put("operator", "aeg_order_total_v2_114019");
        String paramsString = JSON.toJSONString(paramsMap);
        ByteArrayOutputStream stream = new ByteArrayOutputStream();
        try (GZIPOutputStream gzipOutputStream = new GZIPOutputStream(stream)) {
            byte[] data = paramsString.getBytes();
            gzipOutputStream.write(data, 0, data.length);
            gzipOutputStream.flush();
        } catch (Exception e) {
            log.error("compress data error,traceId:{}", EagleEye.getTraceId(), e);
        }
        byte[] src = stream.toByteArray();
        return new String(Base64.getEncoder().encode(src));
    }

    public static CreateQueryParams getCreateQueryParams(HashMap<String, Object> renderOrderResponseModel, Long buyerId) {
        CreateQueryParams createParams = new CreateQueryParams();
        MessageRequest messageRequest = new MessageRequest();
        Map<String, String> headers = Maps.newHashMap();
        createParams.setBuyerId(buyerId);
        createParams.setPaymentOption("MIXEDCARD");
        createParams.setIp("*************");
        createParams.setCompress(true);
        createParams.setParams(ConvertParam.transRenderOrderResponseModel(renderOrderResponseModel));
        messageRequest.setTtid("201200@Aliexpress_iphone_8.100.0.100000243");
        headers.put("x-reqbiz-ext", "cust2%3D6%3A7619%2Cglobal_abtest%3D%7B%2522st_503056%2522%253A%25222128%2522%252C%2522st_503418%2522%253A%25222474%2522%252C%2522st_502367%2522%253A%25222033%2522%252C%2522st_25032%2522%253A%2522576%2522%252C%2522st_502860%2522%253A%25222077%2522%252C%2522st_503417%2522%253A%25222471%2522%252C%2522st_501543%2522%253A%25221719%2522%252C%2522st_503116%2522%253A%25222072%2522%7D%2Cvv%3D8.101.1%7C418%7CWIFI%7C%7C0%7C1%2Cmwua%3Di8gQBffALalHmIAgmwWB2GTQkcpjHcHANZj5pjvoQDsPXeplVn1Z6x8JAm8kvcYdaJXfp8msOm2M6wa7V9OUD2PNvx7Fpt3Y%2BWkI3bMwfUrTLJMX1bvBxJz8K1x3bwUsbc4946TuWrL9%2FAOLTxNp%2FLRLRcvRf2m7nMWf%2B0DM%2BDx3a0w%3D%3D%2Cdia%3D2175784400%2Cscenario%3DCart%2Cdm%3DiPhone%2CUser-Agent%3DAEI%2F418%28iOS%3B17.4.1%3BApple%3BiPhone%29%2Cdid%3DZUChoI6CegQDABy5hcjuZNJA%2Ccust1%3D1290x2796%2Caffi_params%3DAFFILIATE%7C1709729809423%7C0%7C4fd9c5283d9b4534a358b53f4d987334-1709729809423-05605-_mL7WjE6%7C%7C1859103223%2Cmodel%3DiPhone16%2C2");
        messageRequest.setHeaders(headers);
        createParams.setMtopContext(messageRequest);

        return createParams;
    }

    public static RenderOrderRequest getRenderOrderRequest(Long buyerId, List<Long> itemIds, String countryCode, String currency) throws Exception {
        RenderOrderRequest renderOrderRequest = new RenderOrderRequest();
        List<OrderLineDTO> orderLineDTOS = new ArrayList<>();

        for (Long itemId : itemIds) {
            OrderLineDTO orderLineDTO = new OrderLineDTO();
            JSONObject productQueryResponse = HsfUtil.getProductByProductId(itemId);
            Long skuId = Long.valueOf(productQueryResponse.getJSONObject("singleProduct")
                    .getJSONObject("skuIdAndSkuMap").toJavaObject(Map.class).keySet().toArray()[0].toString());
            orderLineDTO.setItemId(itemId);
            orderLineDTO.setQuantity(1);
            orderLineDTO.setSkuId(skuId);
            orderLineDTOS.add(orderLineDTO);
        }
        renderOrderRequest.setItems(orderLineDTOS);
        renderOrderRequest.setRenderProtocol(RenderProtocol.NATIVE);
        renderOrderRequest.setCommonDTO(ConvertParam.getCommonDTO(buyerId));
        renderOrderRequest.setExtraParams(ConvertParam.getExtraParams(currency, countryCode));
        renderOrderRequest.setPaymentDTO(ConvertParam.getPaymentDTO());
        return renderOrderRequest;
    }

    private static PaymentDTO getPaymentDTO() {
        PaymentDTO paymentDTO = new PaymentDTO();
        CashierDTO cashierDTO = new CashierDTO();
//        Map<String, String> extraMap = Maps.newConcurrentMap();
//        cashierDTO.setExtraMap(extraMap);
        paymentDTO.setCashierDTO(cashierDTO);
        return paymentDTO;
    }

}
