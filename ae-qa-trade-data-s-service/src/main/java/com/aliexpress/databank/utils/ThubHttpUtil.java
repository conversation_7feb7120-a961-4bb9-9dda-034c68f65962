package com.aliexpress.databank.utils;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.aliexpress.databank.constant.Constant;
import com.aliexpress.databank.dataobject.ExecuteProcessReq;
import lombok.Data;
import okhttp3.*;
import org.apache.commons.codec.binary.Hex;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

public class ThubHttpUtil {
    private static final Logger logger = LoggerFactory.getLogger(ThubHttpUtil.class);

    public static final MediaType JSON = MediaType.parse("application/json;charset=utf-8");
    private final static Integer SUCCESS_CODE = 204;
    private final static Integer SUCCESS_CODE_OTHER = 200;

    private final static String CALL_ERROR_PATH = "$.result.errors";
    private final static String CALL_SUCCESS_PATH = "$.success";
    private final static String CALL_MSG_PATH = "$.msg";
    private final static String DATA_PATH = "$.result.data.";
    private final static String FALSE = "false";

    static OkHttpClient httpClient = new OkHttpClient()
            .newBuilder()
            .connectTimeout(10, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .writeTimeout(30, TimeUnit.SECONDS)
            .build();

    public static Response httpPostWithHeader(String url, String json, Map<String, String> headerMap) throws Exception {
        logger.info("Thub入参："+json);
        RequestBody requestBody = RequestBody.create(JSON, json);
        Request.Builder builder = new Request.Builder();
        builder.url(url);
        builder.post(requestBody);
        headerMap.forEach((key, value) -> builder.addHeader(key, value));
        Request request = builder.build();
        Response response = null;
        try {
            response = httpClient.newCall(request).execute();
        } catch (IOException e) {
            throw new Exception("post error:" + e);
        }
        return response;
    }


    /**
     * 解析Http请求返回值
     *
     * @param response
     * @param key
     * @return
     */
    public static ThubResponse assembleResponse(Response response, String key) {
        ThubResponse thubResponse = new ThubResponse();
        if (response.code() != SUCCESS_CODE && response.code() != SUCCESS_CODE_OTHER) {
            thubResponse.setMessage("服务调用失败!");
            return thubResponse;
        }
        try {
            JSONObject returnObj = JSONObject.parseObject(response.body().string());
            if (null != JSONPath.eval(returnObj, CALL_ERROR_PATH)) {
                thubResponse.setMessage(JSONPath.eval(returnObj, CALL_ERROR_PATH).toString());
            } else if (null != JSONPath.eval(returnObj, CALL_SUCCESS_PATH) && FALSE.equals(JSONPath.eval(returnObj, CALL_SUCCESS_PATH).toString())) {
                thubResponse.setMessage("Fail call DHUB: " + JSONPath.eval(returnObj, CALL_MSG_PATH));
            } else {
                String querySuccessPath = DATA_PATH + key + CALL_SUCCESS_PATH;
                if (null != JSONPath.eval(returnObj, querySuccessPath) && FALSE.equals(JSONPath.eval(returnObj, querySuccessPath))) {
                    thubResponse.setMessage(JSONPath.eval(returnObj, DATA_PATH + key + ".msg").toString());
                } else {
                    thubResponse.setStatus(1);
                    thubResponse.setData(JSONPath.eval(returnObj, DATA_PATH + key + ".result"));
                }
            }
        } catch (Exception e) {
            thubResponse.setMessage("服务调用失败!" + e.getMessage());
        }
        return thubResponse;
    }

    public static Map<String, String> genHeader(String payload, String workId, String userNick)
            throws NoSuchAlgorithmException {
        MessageDigest messageDigest = MessageDigest.getInstance("SHA-256");
        Map<String, String> header = new HashMap<>();
        long timestamp = System.currentTimeMillis();
        // 拼接hash字符串
        String token = new String(Base64.getDecoder().decode(Constant.THUB_INFO.getString("token").getBytes()));
        String data = token + workId + userNick + timestamp + payload;
        byte[] result = messageDigest.digest(data.getBytes(StandardCharsets.UTF_8));
        String sign = new String(Hex.encodeHex(result));
        header.put("version", Constant.THUB_INFO.getString("version"));
        header.put("appname", Constant.THUB_INFO.getString("appName"));
        header.put("workId", workId);
        header.put("userNick", URLEncoder.encode(userNick));
        header.put("sign", sign);
        header.put("timestamp", "" + timestamp);
        return header;
    }

    public static String queryThubTestcaseInstance(String env, JSONObject variables, String url) {
        try {
            variables.put("appName", Constant.THUB_INFO.getString("appName"));
            variables.put("tenantId", Constant.THUB_INFO.getString("tenantId"));
            JSONObject body = new JSONObject();
            body.put("env", env);
            body.put("variables", variables);
            //openApi 组装payload（目前对外提供的openApi没有使用query这个字段，如果直接使用GraphQL代码而用到query字段，用query自行替换掉第二个空字符串）
            //不使用的字段用 空字符串表示 ""
            String payload = env + "" + variables.toJSONString();
            Map<String, String> header = genHeader(payload, Constant.THUB_INFO.getString("workId"), Constant.THUB_INFO.getString("userNick"));
            Response response = httpPostWithHeader(url, com.alibaba.fastjson.JSON.toJSONString(body), header);
            //打印返回结果
            return com.alibaba.fastjson.JSON.toJSONString(com.alibaba.fastjson.JSON.parseObject(response.body().string()), true)
                    .replaceAll("\t", "").replaceAll("\n", "");
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("用例实例查询 服务调用失败 {} ", e.toString(), e);
        }
        return "Fail to call thub.";
    }

    public static String disputeExecuteProcess(ExecuteProcessReq executeProcessReq, String url)throws Exception {
        try {


            //openApi 组装payload（目前对外提供的openApi没有使用query这个字段，如果直接使用GraphQL代码而用到query字段，用query自行替换掉第二个空字符串）
            //不使用的字段用 空字符串表示 ""
            String payload = executeProcessReq.getEnv() + "" + JSONObject.toJSONString(executeProcessReq.getVariables());
            Map<String, String> header = genHeader(payload, Constant.THUB_INFO.getString("workId"), Constant.THUB_INFO.getString("userNick"));

            Response response = httpPostWithHeader(url, JSONObject.toJSONString(executeProcessReq), header);

            //打印返回结果
            return com.alibaba.fastjson.JSON.toJSONString(com.alibaba.fastjson.JSON.parseObject(response.body().string()), true)
                    .replaceAll("\t", "").replaceAll("\n", "");
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("用例实例查询 服务调用失败 {} ", e.toString());
        }
        return "Fail to call thub.";
    }
}

@Data
class ThubResponse {
    String message;
    Integer status;
    Object data;

}