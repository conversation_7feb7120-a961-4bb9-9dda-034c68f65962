package com.aliexpress.databank.utils;

import com.alibaba.ecommerce.pageable.Pageable;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.metrics.StringUtils;
import com.aliexpress.databank.constant.Constant;
import com.aliexpress.qa.platform.api.util.MeasureLogger;
import com.ascp.uop.kernel.common.model.InvokeInfoDTO;
import com.taobao.eagleeye.EagleEye;
import com.taobao.hsf.app.api.util.HSFApiConsumerBean;
import com.taobao.hsf.remoting.service.GenericService;
import com.taobao.hsf.util.RequestCtxUtil;
import com.taobao.mtop.api.util.StringUtil;
import org.apache.commons.beanutils.ConvertUtils;
import org.javers.common.collections.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

public class HsfUtil {
    private static final Logger logger = LoggerFactory.getLogger(HsfUtil.class);

    public static JSONArray genericServiceInvoke2Array(
            String serviceName, String serviceVersion, String methodName, String groupId,
            String[] parameterType, Object[] parameterValue) throws Exception {
        HSFApiConsumerBean consumerBean = createConsumerBean(serviceName, serviceVersion, groupId, null);
        GenericService genericService = (GenericService) consumerBean.getObject();
        Object result = genericService.$invoke(methodName, parameterType, parameterValue);
        return JSONArray.parseArray(JSON.toJSONString(result));
    }

    public static JSONObject genericServiceInvoke(
            String serviceName, String serviceVersion, String methodName, String groupId, String targetIp,
            String[] parameterType, Object[] parameterValue) throws Exception {
        HSFApiConsumerBean consumerBean = createConsumerBean(serviceName, serviceVersion, groupId, targetIp);
        GenericService genericService = (GenericService) consumerBean.getObject();
        Object result = genericService.$invoke(methodName, parameterType, parameterValue);
        return JSONObject.parseObject(JSON.toJSONString(result));
    }


    public static String genericServiceInvoke2String(
            String serviceName, String serviceVersion, String methodName, String groupId, String targetIp,
            String[] parameterType, Object[] parameterValue) throws Exception {
        HSFApiConsumerBean consumerBean = createConsumerBean(serviceName, serviceVersion, groupId, targetIp);
        GenericService genericService = (GenericService) consumerBean.getObject();
        Object result = genericService.$invoke(methodName, parameterType, parameterValue);
        return String.valueOf(result);
    }

    public static JSONObject genericServiceInvoke(
            String serviceName, String serviceVersion, String methodName, String groupId,
            String[] parameterType, Object[] parameterValue) throws Exception {
        HSFApiConsumerBean consumerBean = createConsumerBean(serviceName, serviceVersion, groupId);
        GenericService genericService = (GenericService) consumerBean.getObject();
        Object result = genericService.$invoke(methodName, parameterType, parameterValue);
        return JSONObject.parseObject(JSON.toJSONString(result));
    }

    public static HSFApiConsumerBean createConsumerBean(String serviceName, String serviceVersion, String groupId, String targetIp) throws Exception {
        HSFApiConsumerBean consumerBean = new HSFApiConsumerBean();
        consumerBean.setInterfaceName(serviceName);
        consumerBean.setGeneric("true");
        consumerBean.setVersion(serviceVersion);
        consumerBean.setGroup(groupId);
        consumerBean.setClientTimeout(50000);
        if (StringUtils.isNotBlank(targetIp)) {
            consumerBean.setTarget(targetIp);
        }
        consumerBean.init();
        return consumerBean;
    }

    public static HSFApiConsumerBean createConsumerBean(String serviceName, String serviceVersion, String groupId) throws Exception {
        HSFApiConsumerBean consumerBean = new HSFApiConsumerBean();
        consumerBean.setInterfaceName(serviceName);
        consumerBean.setGeneric("true");
        consumerBean.setVersion(serviceVersion);
        consumerBean.setGroup(groupId);
        consumerBean.setClientTimeout(50000);
        consumerBean.init();
        return consumerBean;
    }

    public static JSONObject getUserByLoginId(String loginId) throws Exception {
        int times = 0;
        while (times < Constant.MAX_TIMES) {
            try {
                String serviceName = "com.alibaba.global.user.api.facade.UserReadFacade";
                String serviceVersion = "1.0.0";
                String methodName = "getUserByLoginId";
                String groupId = "HSF";
                String[] parameterType = {"java.lang.String"};
                List<Object> paramList = new ArrayList<>();
                paramList.add(loginId);
                return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
            } catch (Exception e) {
                times++;
                try {
                    Thread.sleep(500);
                } catch (InterruptedException interruptedException) {
                    interruptedException.printStackTrace();
                }
                if (times >= Constant.MAX_TIMES) {
                    throw new RuntimeException(e);
                }
            }
        }
        return null;
    }

    public static JSONObject getRouting(Long accountId) {
        int times = 0;
        while (times < Constant.MAX_TIMES) {
            try {
                String serviceName = "com.aliexpress.art.routeservice.interfaces.ArtRouteReadService";
                String serviceVersion = "1.0.0-ae";
                String methodName = "getRouting";
                String groupId = "HSF";
                String[] parameterType = {"java.lang.Long"};
                List<Object> paramList = new ArrayList<>();
                paramList.add(accountId);
                return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
            } catch (Exception e) {
                times++;
                try {
                    Thread.sleep(500);
                } catch (InterruptedException interruptedException) {
                    interruptedException.printStackTrace();
                }
                if (times >= Constant.MAX_TIMES) {
                    throw new RuntimeException(e);
                }
            }
        }
        return null;
    }


    public static JSONObject getUserByEmail(String email) throws Exception {
        int times = 0;
        while (times < Constant.MAX_TIMES) {
            try {
                String serviceName = "com.alibaba.global.user.api.facade.UserReadFacade";
                String serviceVersion = "1.0.0";
                String methodName = "getUserByEmail";
                String groupId = "HSF";
                String[] parameterType = {"java.lang.String"};
                List<Object> paramList = new ArrayList<>();
                paramList.add(email);
                return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
            } catch (Exception e) {
                times++;
                try {
                    Thread.sleep(500);
                } catch (InterruptedException interruptedException) {
                    interruptedException.printStackTrace();
                }
                if (times >= Constant.MAX_TIMES) {
                    throw new RuntimeException(e);
                }
            }
        }
        return null;
    }

    public static JSONObject getUserByUserId(Long userId) throws Exception {
        int times = 0;
        while (times < Constant.MAX_TIMES) {
            try {
                String serviceName = "com.alibaba.global.user.api.facade.UserReadFacade";
                String serviceVersion = "1.0.0";
                String methodName = "getUserById";
                String groupId = "HSF";
                String[] parameterType = {"java.lang.Long"};
                List<Object> paramList = new ArrayList<>();
                paramList.add(userId);
                return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
            } catch (Exception e) {
                times++;
                try {
                    Thread.sleep(500);
                } catch (InterruptedException interruptedException) {
                    interruptedException.printStackTrace();
                }
                if (times >= Constant.MAX_TIMES) {
                    throw new RuntimeException(e);
                }
            }
        }
        return null;
    }

    public static JSONObject getReverseOrderLinesByOrderId(Long buyerId, Long orderId) throws Exception {
        String serviceName = "com.alibaba.reverse.cathedral.api.facade.ReverseOrderQueryFacade";
        String serviceVersion = "1.0.0";
        String methodName = "queryReverseOrderLinesByTradeOrderId";
        String groupId = "HSF";
        String[] parameterType = {"java.lang.Long", "java.lang.Long"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(buyerId);
        paramList.add(orderId);
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }


    /**
     * 履约推单和拆单，使用乐喵评论的接口
     * https://yuque.antfin.com/wml01028991/ph8tnu/szu1ve?#rhxak
     * 仓发发货
     */
    public static JSONObject doFulfillmentOrder(String foId) throws Exception {
        String serviceName = "com.alibaba.global.uop.service.FulfillmentOrderFulfillService";
        String serviceVersion = "1.0.0";
        String methodName = "doFulfillmentOrder";
        String groupId = "HSF";
        String[] parameterTypes = {"java.lang.String"};
        List<String> paramValues = new ArrayList<>();
        paramValues.add(foId);
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterTypes, paramValues.toArray());
    }

    /**
     * 下发菜鸟
     * https://yuque.antfin.com/wml01028991/ph8tnu/szu1ve?#rhxak
     * 仓发发货
     */
    public static JSONObject sendFulfill(String foId, Long buyerId) throws Exception {
        String serviceName = "com.ascp.uop.kernel.share.service.UopFulfillmentSendService";
        String serviceVersion = "1.0.0";
        String methodName = "sendFulfill";
        String groupId = "HSF";
        String[] parameterTypes = {"com.ascp.uop.kernel.share.common.model.sendfulfill.req.SendFulfillReqDTO", "com.ascp.uop.kernel.common.model.InvokeInfoDTO"};
        List<Object> paramList = new ArrayList<>();
        JSONObject emptyJson = new JSONObject();
        emptyJson.put("", "");

        JSONObject request = new JSONObject();
        request.put("buyerId", buyerId);
        request.put("fulfillmentOrderId", foId);
        request.put("extendFields", emptyJson);
        paramList.add(request.toJavaObject(Object.class));
        JSONObject object = new JSONObject();
        JSONObject operator = new JSONObject();
        operator.put("operatorNickName", "");
        operator.put("operatorId", "");
        operator.put("operatorIdentity", "");
        operator.put("extendFields", emptyJson);
        object.put("appName", "");
        object.put("operator", operator);
        paramList.add(object.toJavaObject(Object.class));
        EagleEye.putUserData("dpath_env", "CN_AE_WH_TEST_ENV");
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterTypes, paramList.toArray());
    }

    public static JSONObject getLastReverseOrderByTradeOrderLineId(Long buyerId, Long tradeOrderLineId) throws Exception {
        String serviceName = "com.alibaba.reverse.cathedral.api.facade.ReverseOrderQueryFacade";
        String serviceVersion = "1.0.0";
        String methodName = "queryLastReverseOrderLineByTradeOrderLineId";
        String groupId = "HSF";
        String[] parameterType = {"java.lang.Long", "java.lang.Long"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(buyerId);
        paramList.add(tradeOrderLineId);
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject getReverseOrderLineByReverseOrderLineId(Long buyerId, Long reverseOrderLineId) throws Exception {
        String serviceName = "com.alibaba.reverse.cathedral.api.facade.ReverseOrderQueryFacade";
        String serviceVersion = "1.0.0";
        String methodName = "queryReverseOrderLineById";
        String groupId = "HSF";
        String[] parameterType = {"java.lang.Long", "java.lang.Long"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(buyerId);
        paramList.add(reverseOrderLineId);
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject getReverseOrderByReverseOrderId(Long buyerId, Long reverseOrderId) throws Exception {
        String serviceName = "com.alibaba.reverse.cathedral.api.facade.ReverseOrderQueryFacade";
        String serviceVersion = "1.0.0";
        String methodName = "queryReverseOrderById";
        String groupId = "HSF";
        String[] parameterType = {"java.lang.Long", "java.lang.Long"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(buyerId);
        paramList.add(reverseOrderId);
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject getReverseSolution(Long reverseOrderLineId) throws Exception {
        String serviceName = "com.alibaba.reverse.cathedral.api.facade.ReverseSolutionFacade";
        String serviceVersion = "1.0.0";
        String methodName = "querySolutions";
        String groupId = "HSF";
        String[] parameterType = {"com.alibaba.reverse.cathedral.api.request.QuerySolutionReq"};
        List<Object> paramList = new ArrayList<>();
        JSONObject param = new JSONObject();
        param.put("reverseOrderLineId", reverseOrderLineId);
        paramList.add(param.toJavaObject(Object.class));
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject acceptSolutionByBuyer(Long reverseOrderLineId, Long solutionId,
                                                   Long operatorId, String addressId) throws Exception {
        String serviceName = "com.alibaba.ae.reverse.server.api.facade.AeReverseOrderFacade";
        String serviceVersion = "1.0.0";
        String methodName = "acceptSolution";
        String groupId = "HSF";
        String[] parameterType = {"com.alibaba.ae.reverse.server.api.request.AeAcceptSolutionReq"};
        List<Object> paramList = new ArrayList<>();
        JSONObject param = new JSONObject();
        param.put("reverseOrderLineId", reverseOrderLineId);
        param.put("operatorType", "BUYER");
        param.put("solutionId", solutionId);
        param.put("operatorId", operatorId);
        if (!addressId.isEmpty()) {
            param.put("addressId", addressId);
        }
        paramList.add(param.toJavaObject(Object.class));
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject acceptSolutionBySeller(Long reverseOrderLineId, Long solutionId,
                                                    Long operatorId, String addressId) throws Exception {
        String serviceName = "com.alibaba.ae.reverse.server.api.facade.AeSellerReverseOrderFacade";
        String serviceVersion = "1.0.0";
        String methodName = "acceptSolution";
        String groupId = "HSF";
        String[] parameterType = {"com.alibaba.ae.reverse.server.api.request.AeAcceptSolutionReq"};
        List<Object> paramList = new ArrayList<>();
        JSONObject param = new JSONObject();
        param.put("reverseOrderLineId", reverseOrderLineId);
        param.put("solutionId", solutionId);
        param.put("operatorId", operatorId);
        if (!addressId.isEmpty()) {
            param.put("addressId", addressId);
        }
        paramList.add(param.toJavaObject(Object.class));
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject getReverseOrderByReverseOrderLineId(Long reverseOrderLineId) throws Exception {
        String serviceName = "com.alibaba.ae.tradeReverse.api.QueryReverseOrderFacade";
        String serviceVersion = "1.0.0";
        String methodName = "reverseOrderLineDetailRender";
        String groupId = "HSF";
        String[] parameterType = {"com.alibaba.ae.tradeReverse.model.request.DetailRenderRequest"};
        List<Object> paramList = new ArrayList<>();
        JSONObject param = new JSONObject();
        param.put("reverseOrderLineId", reverseOrderLineId);
        param.put("userId", 0);
        paramList.add(param.toJavaObject(Object.class));
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject getReverseOrderLineByTradeOrderLineId(Long buyerId, Long tradeOrderLineId) throws Exception {
        String serviceName = "com.alibaba.reverse.cathedral.api.facade.ReverseOrderQueryFacade";
        String serviceVersion = "1.0.0";
        String methodName = "queryLastReverseOrderLineByTradeOrderLineId";
        String groupId = "HSF";
        String[] parameterType = {"java.lang.Long", "java.lang.Long"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(buyerId);
        paramList.add(tradeOrderLineId);
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }


    public static JSONObject createReverse(JSONObject createReverseReq) throws Exception {
        String serviceName = "com.alibaba.ae.reverse.server.api.facade.AeReverseOrderFacade";
        String serviceVersion = "1.0.0";
        String methodName = "createReverseOrder";
        String groupId = "HSF";
        String[] parameterType = {"com.alibaba.ae.reverse.server.api.request.CreateReverseOrderReq"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(createReverseReq.toJavaObject(Object.class));
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject batchCreateReverseOrder(JSONObject createReverseReq) throws Exception {
        String serviceName = "com.alibaba.ae.reverse.server.api.AeInnerReverseOrderFacade";
        String serviceVersion = "1.0.0";
        String methodName = "batchCreateReverseOrder";
        String groupId = "HSF";
        String[] parameterType = {"com.alibaba.ae.reverse.server.api.request.CreateReverseOrderReq"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(createReverseReq.toJavaObject(Object.class));
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject cancelRequest(Long buyerId, String reverseOrderLineId) throws Exception {
        String serviceName = "com.alibaba.ae.reverse.server.api.facade.AeReverseOrderFacade";
        String serviceVersion = "1.0.0";
        String methodName = "cancelReverseOrder";
        String groupId = "HSF";
        String[] parameterType = {"com.alibaba.ae.reverse.server.api.request.CancelReverseOrderReq"};
        List<Object> paramList = new ArrayList<>();
        JSONObject cancelReverseOrderReq = new JSONObject();
        cancelReverseOrderReq.put("buyerId", buyerId);
        JSONArray reverseOrderLineIds = new JSONArray();
        reverseOrderLineIds.add(reverseOrderLineId);
        cancelReverseOrderReq.put("reverseOrderLines", reverseOrderLineIds);
        paramList.add(cancelReverseOrderReq.toJavaObject(Object.class));
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONArray getReturnAddress(Long sellerId) throws Exception {
        String serviceName = "com.alibaba.intl.logistics.open.storage.remote.LogisticsSellerAddressRemoteService";
        String serviceVersion = "1.0.0";
        String methodName = "queryAddressList";
        String groupId = "HSF";
        String[] parameterType = {"java.lang.Long", "com.alibaba.intl.logistics.open.storage.dto.domain.LogisticsSellerAddressQuery"};
        List<Object> paramList = new ArrayList<>();
        JSONObject req = new JSONObject();
        req.put("adminMemberSeq", sellerId);
        req.put("addressType", "refund");
        paramList.add(sellerId);
        paramList.add(req.toJavaObject(Object.class));
        return genericServiceInvoke2Array(serviceName, serviceVersion, methodName, groupId, parameterType, paramList.toArray());
    }

    public static JSONObject getIssueProtect(Long buyer, List<Long> tradeOrderLineIds) throws Exception {
        String serviceName = "com.aliexpress.issue.dispute.service.AeIssueDisputeQueryRemoteService";
        String serviceVersion = "1.0.0";
        String methodName = "batchQueryIssueProtectResult";
        String groupId = "HSF";
        String[] parameterType = {"java.lang.Long", "java.util.List"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(buyer);
        paramList.add(tradeOrderLineIds);
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject refuseSolutionByBuyer(JSONObject request) throws Exception {
        String serviceName = "com.alibaba.ae.reverse.server.api.facade.AeReverseOrderFacade";
        String serviceVersion = "1.0.0";
        String methodName = "refuseSolution";
        String groupId = "HSF";
        String[] parameterType = {"com.alibaba.ae.reverse.server.api.request.AeBuyerRefuseSolutionReq"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(request.toJavaObject(Object.class));
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject refuseSolutionBySeller(JSONObject request) throws Exception {
        String serviceName = "com.alibaba.ae.reverse.server.api.facade.AeSellerReverseOrderFacade";
        String serviceVersion = "1.0.0";
        String methodName = "refuseSolution";
        String groupId = "HSF";
        String[] parameterType = {"com.alibaba.ae.reverse.server.api.request.AeRefuseSolutionReq"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(request.toJavaObject(Object.class));
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject returnGoods(Long reverseOrderLineId, Long buyerId, String logisticsCompany,
                                         String logisticsCompanyCode, String logisticsTrackNum) throws Exception {
        String serviceName = "com.alibaba.ae.reverse.server.api.facade.AeReverseOrderFacade";
        String serviceVersion = "1.0.0";
        String methodName = "buyerReturnGoods";
        String groupId = "HSF";
        String[] parameterType = {"com.alibaba.ae.reverse.server.api.request.BuyerReturnGoodReq"};
        List<Object> paramList = new ArrayList<>();
        JSONObject req = new JSONObject();
        req.put("reverseOrderLineId", reverseOrderLineId);
        req.put("logisticsTrackNum", StringUtil.isEmpty(logisticsTrackNum) ? "*********" : logisticsTrackNum);
        req.put("logisticsCompany", StringUtil.isEmpty(logisticsCompany) ? "testByDataBank" : logisticsCompany);
        req.put("logisticsCompanyCode", StringUtil.isEmpty(logisticsCompanyCode) ? "dataBank" : logisticsCompanyCode);
        req.put("operatorType", "BUYER");
        req.put("operatorId", buyerId);
        paramList.add(req.toJavaObject(Object.class));
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject confirmReturn(Long reverseOrderLineId, Long sellerId) throws Exception {
        String serviceName = "com.alibaba.ae.reverse.server.api.facade.AeSellerReverseOrderFacade";
        String serviceVersion = "1.0.0";
        String methodName = "confirmDelivery";
        String groupId = "HSF";
        String[] parameterType = {"com.alibaba.ae.reverse.server.api.request.ConfirmDeliveryReq"};
        List<Object> paramList = new ArrayList<>();
        JSONObject req = new JSONObject();
        req.put("reverseOrderLineId", reverseOrderLineId);
        req.put("operatorId", sellerId);
        req.put("operatorType", "SELLER");
        paramList.add(req.toJavaObject(Object.class));
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject fundPreByOrderId(String tradeOrderId) throws Exception {
        String serviceName = "com.aliexpress.qa.automation.service.FundOperationFacade";
        String serviceVersion = "1.0.0";
        String methodName = "fundPretreatment";
        String groupId = "HSF";
        String[] parameterType = {"java.lang.String"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(tradeOrderId);
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject queryFundPretreatmentDetailByTargetAndUserId(String tradeOrderId, Long buyerId) throws Exception {
        String serviceName = "com.alibaba.global.settlement.api.trade.TradeSettlementFundPretreatmentFacadeService";
        String serviceVersion = "1.0.0";
        String methodName = "queryFundPretreatmentDetailByTargetAndUserId";
        String groupId = "HSF";
        String[] parameterType = {"com.alibaba.global.settlement.api.trade.model.request.FundPretreatmentQueryRequest"};
        List<Object> paramList = new ArrayList<>();
        JSONObject req = new JSONObject();
        req.put("routeId", buyerId);
        req.put("buyerId", buyerId);
        req.put("tradeOrderId", tradeOrderId);
        paramList.add(req.toJavaObject(Object.class));
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject queryPaymentDetail(String instructionNo) throws Exception {
        String serviceName = "com.alibaba.global.payment.api.facade.simple.PaymentQueryFacade";
        String serviceVersion = "1.0.0";
        String methodName = "queryPaymentDetail";
        String groupId = "gps";
        String[] parameterType = {"com.alibaba.global.payment.api.facade.simple.request.SinglePaymentDetailQueryRequest"};
        List<Object> paramList = new ArrayList<>();
        JSONObject req = new JSONObject();
        req.put("payInstructionNo", instructionNo);
        req.put("sourceType", "GLOBAL_TRADE");
        req.put("payerId", 0);
        paramList.add(req.toJavaObject(Object.class));
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    /***
     *
     */
    public static JSONObject chargeBackRecoverSeller(long userId, String orderId, String chargebackId, long cent, String currencyCode) throws Exception {
        String serviceName = "com.alibaba.global.settlement.api.trade.TradeSettlementRefundFacadeService";
        String serviceVersion = "1.0.0";
        String methodName = "chargeBackRecoverSeller";
        String groupId = "HSF";
        String[] parameterType = {"com.alibaba.global.settlement.api.trade.model.request.ChargeBackRecoverRequest"};
        List<Object> paramList = new ArrayList<>();
        JSONObject req = new JSONObject();
        req.put("routeId", userId);
        req.put("tradeOrderId", orderId);
        req.put("bizId", chargebackId);
        req.put("sellerDuty", true);
        req.put("buyerId", userId);

        JSONObject money = new JSONObject();
        money.put("cent", cent);
        money.put("currencyCode", currencyCode);

        req.put("chargeBackMoney", money);
        paramList.add(req.toJavaObject(Object.class));
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, parameterType, paramList.toArray());
    }

    public static JSONObject abandonGoods(Long reverseOrderLineId, Long sellerId) throws Exception {
        String serviceName = "com.alibaba.ae.reverse.server.api.facade.AeSellerReverseOrderFacade";
        String serviceVersion = "1.0.0";
        String methodName = "abandonGoods";
        String groupId = "HSF";
        String[] parameterType = {"com.alibaba.ae.reverse.server.api.request.AbandonGoodsReq"};
        List<Object> paramList = new ArrayList<>();
        JSONObject req = new JSONObject();
        req.put("reverseOrderLineId", reverseOrderLineId);
        req.put("operatorId", sellerId);
        req.put("operatorType", "SELLER");
        paramList.add(req.toJavaObject(Object.class));
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject syncFreeReturn(Long havanaId, Integer status) throws Exception {
        String serviceName = "com.alibaba.aliexpress.service.api.InsuranceSignService";
        String serviceVersion = "1.0.0";
        String methodName = "syncCJServiceSignResult";
        String groupId = "HSF";
        String[] parameterType = {"java.lang.Long", "java.lang.Long"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(havanaId);
        paramList.add(status);
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject isSignFreeReturn(Long havanaId) throws Exception {
        String serviceName = "com.alibaba.aliexpress.service.api.InsuranceSignService";
        String serviceVersion = "1.0.0";
        String methodName = "isSellerJoinCJInsurance";
        String groupId = "HSF";
        String[] parameterType = {"java.lang.Long"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(havanaId);
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }


    public static JSONObject getProductByProductId(Long productId) throws Exception {
        int times = 0;
        while (times < Constant.MAX_TIMES) {
            try {
                String serviceName = "com.alibaba.global.ic.api.CustomerProductServiceFacade";
                String serviceVersion = "1.0.0";
                String methodName = "queryProduct";
                String groupId = "HSF";
                String[] parameterType = {"com.alibaba.global.ic.dto.scenario.query.ProductQueryRequest"};
                List<Object> paramList = new ArrayList<>();
                JSONObject req = new JSONObject();
                JSONArray queryConditions = new JSONArray();
                JSONObject queryCondition = new JSONObject();
                queryCondition.put("productId", productId);
                queryCondition.put("type", 1);
                queryConditions.add(queryCondition);
                req.put("productQueryConditions", queryConditions);
                req.put("bizCode", "tradeTool_access");
                RequestCtxUtil.setAppNameOfClient("ae-qa-trade-data-s");
                paramList.add(req.toJavaObject(Object.class));
                return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
            } catch (Exception e) {
                times++;
                try {
                    Thread.sleep(500);
                } catch (InterruptedException interruptedException) {
                    interruptedException.printStackTrace();
                }
                if (times >= Constant.MAX_TIMES) {
                    throw new RuntimeException(e);
                }
            }
        }
        return null;
    }

    public static JSONObject getServiceOrder(Long tradeOrderLineId, Long promiseTemplateId) throws Exception {
        String serviceName = "com.alibaba.global.service.platform.api.facade.WorkOrderQueryFacade";
        String serviceVersion = "1.0.0";
        String methodName = "queryAllWorkOrderDetailByTradeOrderLineId";
        String groupId = "HSF";
        String[] parameterType = {"java.lang.Long", "java.lang.Long"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(tradeOrderLineId);
        paramList.add(promiseTemplateId);
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject getUserAddresses(Long buyerId) throws Exception {
        String serviceName = "com.alibaba.global.address.api.facade.UserAddressReadFacade";
        String serviceVersion = "1.0.0";
        String methodName = "listUserAddressByUserId";
        String groupId = "HSF";
        String[] parameterType = {"java.lang.Long"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(buyerId);
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject updateUserAddress(Long buyerId, JSONObject address) throws Exception {
        String serviceName = "com.alibaba.intl.ae.logistics.address.open.remote.WlMailingAddressRemoteService";
        String serviceVersion = "1.0.0";
        String methodName = "updateMailingAddressModify";
        String groupId = "DUBBO";
        String[] parameterType = {"com.alibaba.intl.ae.logistics.address.dto.WlMailingAddressDTO", "java.lang.Long"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(address.toJavaObject(Object.class));
        paramList.add(buyerId);
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject callReverseScript(String buyerId, String script) throws Exception {
        String serviceName = "com.alibaba.global.reverse.api.facade.TestFacade";
        String serviceVersion = "1.0.0.global";
        String methodName = "test";
        String groupId = "HSF";
        String[] parameterTypes = {"java.lang.String", "java.lang.String", "java.lang.String"};
        List<Object> paramValues = new ArrayList<>();
        paramValues.add(script);
        paramValues.add(buyerId);
        paramValues.add("");
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterTypes, paramValues.toArray());
    }

    public static JSONObject triggerBreachTimeout(Long breachId) throws Exception {
        String serviceName = "com.alibaba.ae.service.open.facade.InternalToolsFacade";
        String serviceVersion = "1.0.0";
        String methodName = "triggerTimeout";
        String groupId = "HSF";
        String[] parameterTypes = {"java.lang.Long"};
        List<Object> paramValues = new ArrayList<>();
        paramValues.add(breachId);
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterTypes, paramValues.toArray());
    }

    public static JSONObject extraDeduct(Long breachId) throws Exception {
        String serviceName = "com.alibaba.ae.service.open.facade.InternalToolsFacade";
        String serviceVersion = "1.0.0";
        String methodName = "extraDeduct";
        String groupId = "HSF";
        String[] parameterTypes = {"java.lang.Long"};
        List<Object> paramValues = new ArrayList<>();
        paramValues.add(breachId);
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterTypes, paramValues.toArray());
    }

    public static JSONObject getUserByHavanaId(Long havanaId) throws Exception {
        String serviceName = "com.alibaba.global.user.api.facade.UserReadFacade";
        String serviceVersion = "1.0.0";
        String methodName = "getUserByLoginId";
        String groupId = "HSF";
        String[] parameterType = {"java.lang.String"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(havanaId);
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject findByAccountId(Long havanaId) throws Exception {
        int times = 0;
        while (times < Constant.MAX_TIMES) {
            try {
                String serviceName = "com.aliexpress.uic.open.account.read.service.AccountQueryService";
                String serviceVersion = "1.0.0";
                String methodName = "findByAccountId";
                String groupId = "DUBBO";
                String[] parameterType = {"java.lang.Long"};
                List<Object> paramList = new ArrayList<>();
                paramList.add(havanaId);
                return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
            } catch (Exception e) {
                times++;
                try {
                    Thread.sleep(500);
                } catch (InterruptedException interruptedException) {
                    interruptedException.printStackTrace();
                }
                if (times >= Constant.MAX_TIMES) {
                    throw new RuntimeException(e);
                }
            }
        }
        return null;
    }

    //集团havana体系的账号信息，确认是否可登陆---集团会员接口下线
    public static JSONObject findAccount(Long buyerId) throws Exception {
//        int times = 0;
//        while (times < Constant.MAX_TIMES) {
//            try {
//                String serviceName = "com.alibaba.havana.oneid.api.account.AccountFindService";
//                String serviceVersion = "1.0.0";
//                String methodName = "findAccount";
//                String groupId = "HSF";
//                String[] parameterType = {"long"};
//                List<Object> paramList = new ArrayList<>();
//                paramList.add(buyerId);
//                return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, parameterType, paramList.toArray());
//            } catch (Exception e) {
//                times++;
//                try {
//                    Thread.sleep(500);
//                } catch (InterruptedException interruptedException) {
//                    interruptedException.printStackTrace();
//                }
//                if(times >= Constant.MAX_TIMES) {
//                    throw new RuntimeException(e);
//                }
//            }
//        }
        return null;
    }


    public static JSONObject getUserFreeReturnStatus(Long havanaId) throws Exception {
        String serviceName = "com.alibaba.global.user.api.facade.UserReadFacade";
        String serviceVersion = "1.0.0";
        String methodName = "isSellerJoinCJInsurance";
        String groupId = "HSF";
        String[] parameterType = {"java.lang.Long"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(havanaId);
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject shipOnline(JSONObject takingOrderRequest) throws Exception {
        String serviceName = "com.cainiao.lsp.order.service.GlocOrderService";
        String serviceVersion = "1.0.0";
        String methodName = "takingOrder";
        String groupId = "HSF";
        String[] parameterType = {"com.cainiao.lsp.order.request.TakingOrderRequest"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(takingOrderRequest.toJavaObject(Object.class));
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject queryRefundLimit(String tradeOrderId, String tradeOrderLineId, Long buyerId) throws Exception {
        String serviceName = "com.alibaba.global.settlement.api.trade.TradeSettlementRefundFacadeService";
        String serviceVersion = "1.0.0";
        String methodName = "queryRefundLimit";
        String groupId = "HSF";
        String[] parameterType = {"com.alibaba.global.settlement.api.trade.model.request.RefundLimitQueryRequest"};
        List<Object> paramList = new ArrayList<>();
        JSONObject request = new JSONObject();
        request.put("routeId", buyerId);
        request.put("buyerId", buyerId);
        request.put("tradeOrderId", tradeOrderId);
        request.put("tradeOrderLineId", tradeOrderLineId);
        paramList.add(request.toJavaObject(Object.class));
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject getChargebackRecords(Long tradeOrderId) throws Exception {
        String serviceName = "com.alibaba.ae.reverse.server.api.facade.InternalToolFacade";
        String serviceVersion = "1.0.0";
        String methodName = "queryChargebackRecordsByTradeOrderId";
        String groupId = "HSF";
        String[] parameterType = {"java.lang.Long"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(tradeOrderId);
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject getChargebackMessages(Long tradeOrderId) throws Exception {
        String serviceName = "com.alibaba.ae.reverse.server.api.facade.InternalToolFacade";
        String serviceVersion = "1.0.0";
        String methodName = "queryChargebackMessagesByTradeOrderId";
        String groupId = "HSF";
        String[] parameterType = {"java.lang.Long"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(tradeOrderId);
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject getServiceOrderDetail(Long serviceWorkOrderId) throws Exception {
        String serviceName = "com.alibaba.global.service.platform.api.facade.WorkOrderQueryFacade";
        String serviceVersion = "1.0.0";
        String methodName = "queryWorkOrderDetailById";
        String groupId = "HSF";
        String[] parameterType = {"java.lang.Long"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(serviceWorkOrderId);
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject queryNrCode(Long buyerId, Long tradeOrderId, Long tradeOrderLineId) throws Exception {
        String serviceName = "com.alibaba.ae.tradeReverse.api.QueryReverseOrderFacade";
        String serviceVersion = "1.0.0";
        String methodName = "queryNrInterceptRule";
        String groupId = "HSF";
        String[] parameterType = {"com.alibaba.ae.tradeReverse.model.request.NrInterceptRuleRequest"};
        List<Object> paramList = new ArrayList<>();
        JSONObject request = new JSONObject();
        request.put("buyerId", buyerId);
        request.put("orderId", tradeOrderId);
        request.put("orderLineId", tradeOrderLineId);
        paramList.add(request.toJavaObject(Object.class));
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject queryPackage(Long buyerId, String tradeOrderId, String tradeOrderLineId) throws Exception {
        String serviceName = "com.alibaba.global.uop.api.FulfillmentPackageQueryFacade";
        String serviceVersion = "1.0.0";
        String methodName = "queryFulfillmentPackage";
        String groupId = "HSF";
        String[] parameterType = {"com.alibaba.global.uop.api.request.FulfillmentPackageQueryRequest"};
        List<Object> paramList = new ArrayList<>();
        JSONObject request = new JSONObject();
        request.put("buyerId", buyerId);
        request.put("tradeOrderId", tradeOrderId);
        if (StringUtils.isNotBlank(tradeOrderLineId)) {
            request.put("tradeOrderItemId", tradeOrderLineId);
        }
        paramList.add(request.toJavaObject(Object.class));
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject queryTrackingInfo(JSONObject trackingInfoReq) throws Exception {
        String serviceName = "com.cainiao.global.tracking.service.client.tracking.BasicTrackingService";
        String serviceVersion = "1.0.0";
        String methodName = "queryBasicTrackingView";
        String groupId = "HSF";
        String[] parameterType = {"com.cainiao.global.tracking.service.client.tracking.dto.BasicTrackingViewQueryDTO"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(trackingInfoReq.toJavaObject(Object.class));
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject queryRefundInstruction(String bizOrderNo, Long payerId) throws Exception {
        String serviceName = "com.taobao.payment.bops.service.QueryRefundInstructionService";
        String serviceVersion = "1.0.0";
        String methodName = "queryRefundInstruction";
        String groupId = "HSF";
        String[] parameterType = {"com.taobao.payment.bops.controller.bean.pp.RefundQueryVO"};
        JSONObject request = new JSONObject();
        request.put("siteId", "AE_GLOBAL");
        request.put("payerId", payerId);
        request.put("bizOrderNo", bizOrderNo);
        List<Object> paramList = new ArrayList<>();
        paramList.add(request.toJavaObject(Object.class));
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }


    public static JSONObject queryRefundInstructionDist(String bizOrderNo, Long payerId) throws Exception {
        String serviceName = "com.taobao.payment.bops.service.QueryRefundInstructionService";
        String serviceVersion = "1.0.0";
        String methodName = "queryRefundInstructionDist";
        String groupId = "HSF";
        String[] parameterType = {"com.taobao.payment.bops.controller.bean.pp.RefundQueryVO"};
        JSONObject request = new JSONObject();
        request.put("siteId", "AE_GLOBAL");
        request.put("payerId", payerId);
        request.put("bizOrderNo", bizOrderNo);
        List<Object> paramList = new ArrayList<>();
        paramList.add(request.toJavaObject(Object.class));
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject queryBatchCheckoutOrder(String repayOrderId) throws Exception {
        String serviceName = "com.taobao.payment.bops.service.CheckoutService";
        String serviceVersion = "1.0.0";
        String methodName = "queryBatchCheckoutOrder";
        String groupId = "HSF";
        String[] parameterType = {"java.lang.String"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(repayOrderId);
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject queryPayInstructionDist(String checkoutOrder) throws Exception {
        String serviceName = "com.taobao.payment.bops.service.PayInstructionService";
        String serviceVersion = "1.0.0";
        String methodName = "queryPayInstructionDist";
        String groupId = "HSF";
        String[] parameterType = {"java.lang.String"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(checkoutOrder);
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject queryPayInstruction(String checkoutOrder) throws Exception {
        String serviceName = "com.taobao.payment.bops.service.PayInstructionService";
        String serviceVersion = "1.0.0";
        String methodName = "queryPayInstruction";
        String groupId = "HSF";
        String[] parameterType = {"java.lang.String"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(checkoutOrder);
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject getBoolTagByAccountId(Long accountId) {
        int times = 0;
        while (times < Constant.MAX_TIMES) {
            try {
                String serviceName = "com.aliexpress.uic.open.tag.service.read.BoolTagReadService";
                String serviceVersion = "1.0.0";
                String methodName = "getBoolTagDo";
                String groupId = "DUBBO";
                String[] parameterType = {"java.lang.Long"};
                List<Object> paramList = new ArrayList<>();
                paramList.add(accountId);
                return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
            } catch (Exception e) {
                times++;
                try {
                    Thread.sleep(500);
                } catch (InterruptedException interruptedException) {
                    interruptedException.printStackTrace();
                }
                if (times >= Constant.MAX_TIMES) {
                    throw new RuntimeException(e);
                }
            }
        }
        return null;
    }

    public static JSONObject getMailingAddressInfoV2(Long buyerId) throws Exception {
        int times = 0;
        while (times < Constant.MAX_TIMES) {
            try {
                String serviceName = "com.alibaba.intl.addressserver.service.remote.WlMailingAddressService4Ocean";
                String serviceVersion = "1.0.0";
                String methodName = "getMailingAddressInfoV2";
                String groupId = "HSF";
                String[] parameterType = {"com.alibaba.intl.addressserver.service.remote.vo.WlMailingAddressParam"};
                List<Object> paramList = new ArrayList<>();
                JSONObject request = new JSONObject();
                request.put("accountId", buyerId);
                paramList.add(request.toJavaObject(Object.class));
                return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
            } catch (Exception e) {
                times++;
                try {
                    Thread.sleep(500);
                } catch (InterruptedException interruptedException) {
                    interruptedException.printStackTrace();
                }
                if (times >= Constant.MAX_TIMES) {
                    throw new RuntimeException(e);
                }
            }
        }

        return null;
    }

    public static JSONObject analyseReverseProduct(Long reverseOrderLineId) throws Exception {
        String serviceName = "com.alibaba.ae.reverse.server.api.facade.InternalToolFacade";
        String serviceVersion = "1.0.0";
        String methodName = "analyseFirstCalculateReverseProduct";
        String groupId = "HSF";
        String[] parameterType = {"java.lang.Long"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(reverseOrderLineId);
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject analyseReverseOrder(Long tradeOrderId, Long tradeOrderLineId) throws Exception {
        String serviceName = "com.alibaba.ae.reverse.server.api.facade.InternalToolFacade";
        String serviceVersion = "1.0.0";
        String methodName = "analyseReverseOrder";
        String groupId = "HSF";
        String[] parameterType = {"java.lang.Long", "java.lang.Long"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(tradeOrderId);
        paramList.add(tradeOrderLineId);
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject analyseCancelOrderReverse(Long tradeOrderId) throws Exception {
        String serviceName = "com.alibaba.ae.reverse.server.api.facade.InternalToolFacade";
        String serviceVersion = "1.0.0";
        String methodName = "analyseCancelOrderReverse";
        String groupId = "HSF";
        String[] parameterType = {"java.lang.Long"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(tradeOrderId);
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject getUserTagsByUserIdAndTag(String userId, String tag) throws Exception {
        int times = 0;
        while (times < Constant.MAX_TIMES) {
            try {
                String serviceName = "com.alibaba.global.user.api.facade.UserBoolTagFacade";
                String serviceVersion = "1.0.0";
                String methodName = "hasUserTag";
                String groupId = "HSF";
                String[] parameterType = {"java.lang.Long", "java.lang.String"};
                List<Object> paramList = new ArrayList<>();
                paramList.add(userId);
                paramList.add(tag);
                return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
            } catch (Exception e) {
                times++;
                try {
                    Thread.sleep(500);
                } catch (InterruptedException interruptedException) {
                    interruptedException.printStackTrace();
                }
                if (times >= Constant.MAX_TIMES) {
                    throw new RuntimeException(e);
                }
            }
        }
        return null;
    }


    public static JSONObject getAllFieldValidateConfigByFieldName(String countryCode) throws Exception {
        int times = 0;
        while (times < Constant.MAX_TIMES) {
            try {
                String serviceName = "com.alibaba.global.address.api.facade.GlobalAddressValidateFacade";
                String serviceVersion = "1.0.0.ae";
                String methodName = "getAllFieldValidateConfigByFieldName";
                String groupId = "HSF";
                String[] parameterType = {"com.alibaba.global.address.api.request.GlobalAddressValidateRequest"};
                JSONObject request = new JSONObject();
                request.put("countryCode", countryCode);
                List<Object> paramList = new ArrayList<>();
                paramList.add(request.toJavaObject(Object.class));

                return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
            } catch (Exception e) {
                times++;
                try {
                    Thread.sleep(500);
                } catch (InterruptedException interruptedException) {
                    interruptedException.printStackTrace();
                }
                if (times >= Constant.MAX_TIMES) {
                    throw new RuntimeException(e);
                }
            }
        }
        return null;
    }

    public static JSONObject queryWalletStatus(Long userId) throws Exception {
        int times = 0;
        while (times < Constant.MAX_TIMES) {
            try {
                String serviceName = "com.alibaba.global.wallet.api.facade.WalletReadFacade";
                String serviceVersion = "1.0.0.global";
                String methodName = "queryWalletStatus";
                String groupId = "HSF";
                String[] parameterType = {"java.lang.Long"};
                List<Object> paramList = new ArrayList<>();
                paramList.add(userId);
                return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
            } catch (Exception e) {
                times++;
                try {
                    Thread.sleep(500);
                } catch (InterruptedException interruptedException) {
                    interruptedException.printStackTrace();
                }
                if (times >= Constant.MAX_TIMES) {
                    throw new RuntimeException(e);
                }
            }
        }
        return null;
    }

    public static JSONObject getSellerTagBySellerId(String userId, String tag) throws Exception {
        String serviceName = "com.alibaba.global.merchant.seller.api.facade.SellerReadFacadeForTrade";
        String serviceVersion = "1.0.0";
        String methodName = "getBatchSellersInfoBySellerIds";
        String groupId = "HSF";
        String[] parameterType = {"java.util.List", "java.util.List", "java.util.List"};
        List<Object> paramList = new ArrayList<>();
        List<Long> userIds = Lists.asList(Long.valueOf(userId));
        List<String> tags = Lists.asList(tag);
        List<String> flags = Lists.asList();
        paramList.add(userIds);
        paramList.add(tags);
        paramList.add(flags);
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject updateArbitration(Long buyerId, Long sellerId, String arbitrationId, String judgeType,
                                               Long reverseOrderLineId, String sponsor, Long amount, int status,
                                               String currencyCode, Boolean fake, Boolean arbitrationToSelfDrop) throws Exception {
        String serviceName = "com.alibaba.reverse.cathedral.api.facade.ReverseArbitrationFacade";
        String serviceVersion = "1.0.0";
        String methodName = "updateArbitration";
        String groupId = "HSF";
        String[] parameterType = {"com.alibaba.reverse.cathedral.api.request.UpdateReverseArbitrationRequest"};
        List<Object> paramList = new ArrayList<>();
        JSONObject request = new JSONObject();
        JSONObject invokeInfo = new JSONObject();
        invokeInfo.put("appName", "xcommerce-icbu-i18n");
        request.put("invokeInfo", invokeInfo);

        JSONObject routingInfo = new JSONObject();
        routingInfo.put("buyerId", buyerId);
        routingInfo.put("sellerId", sellerId);
        request.put("routingInfo", routingInfo);

        JSONObject operatorDTO = new JSONObject();
        operatorDTO.put("operatorType", "CUSTOMER_SERVICE");
        operatorDTO.put("operatorId", 0);
        request.put("operatorDTO", operatorDTO);

        JSONObject extendParam = new JSONObject();
        JSONObject extensionDTOMap = new JSONObject();
        extendParam.put("extensionDTOMap", extensionDTOMap);
        request.put("extendParam", extendParam);

        JSONArray lineReqs = new JSONArray();
        JSONObject lineReq = new JSONObject();
        JSONObject features = new JSONObject();
        JSONObject featureMap = new JSONObject();
        featureMap.put("responsibleParty", sponsor);
        if (fake) {
            featureMap.put("caseSubCategory", "fake_item@3rdIssueReason");
        }
        if (arbitrationToSelfDrop) {
            featureMap.put("arbitrationToSelfDrop", "1");
        }
        features.put("featureMap", featureMap);
        features.put("empty", false);

        lineReq.put("features", features);
        lineReq.put("arbitrationId", arbitrationId);
        lineReq.put("reverseOrderLineId", reverseOrderLineId);
        lineReq.put("targetStatus", String.valueOf(status));

        JSONObject arbitrationResult = new JSONObject();
        arbitrationResult.put("idempotentKey", String.valueOf(reverseOrderLineId));
        if (amount != 0L) {
            arbitrationResult.put("type", judgeType);
            JSONArray sponsors = new JSONArray();
            JSONObject sponsorJsonObject = new JSONObject();
            sponsorJsonObject.put("sponsorType", "SELLER");
            JSONObject amt = new JSONObject();
            amt.put("amount", amount);
            amt.put("currencyCode", currencyCode);
            sponsorJsonObject.put("amt", amt);
            sponsors.add(sponsorJsonObject);
            arbitrationResult.put("sponsors", sponsors);
            JSONObject refundAmt = new JSONObject();
            refundAmt.put("amount", amount);
            refundAmt.put("currencyCode", currencyCode);
            arbitrationResult.put("refundAmt", refundAmt);
        } else {
            arbitrationResult.put("type", "CLOSE_REVERSE");
        }
        lineReq.put("arbitrationResult", arbitrationResult);
        lineReqs.add(lineReq);
        request.put("lineReqs", lineReqs);
        paramList.add(request.toJavaObject(Object.class));
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject updateUserStatus(Long buyerId, Integer status) {
        int times = 0;
        while (times < Constant.MAX_TIMES) {
            try {
                String serviceName = "com.alibaba.global.user.biz.api.facade.UserBizWriteFacade";
                String serviceVersion = "2.0.0.global";
                String methodName = "updateUserStatus";
                String groupId = "HSF";
                String[] parameterType = {"java.lang.Long", "int"};
                List<Object> paramList = new ArrayList<>();
                paramList.add(buyerId);
                paramList.add(status);
                return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
            } catch (Exception e) {
                times++;
                try {
                    Thread.sleep(500);
                } catch (InterruptedException interruptedException) {
                    interruptedException.printStackTrace();
                }
                if (times >= Constant.MAX_TIMES) {
                    throw new RuntimeException(e);
                }
            }
        }
        return null;
    }


    public static JSONObject queryRepaymentDetail(String buyerId, String payInstructionNo) {
        String serviceName = "com.aliexpress.wallet.api.asset.facade.IMockToolsFacade";
        String serviceVersion = "1.0.0";
        String methodName = "queryRepaymentDetail";
        String groupId = "HSF";
        String[] parameterType = {"com.aliexpress.wallet.api.asset.request.mocktools.RepaymentDetailQueryReq"};
        List<Object> paramList = new ArrayList<>();
        JSONObject request = new JSONObject();
        request.put("merchantKey", "AE");
        request.put("userId", buyerId);
        request.put("payInstructionNo", payInstructionNo);
        paramList.add(request.toJavaObject(Object.class));
        try {
            return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static JSONObject carryForwardRefundStatus(String buyerId, String memberId, String outUniqueSeq) {
        String serviceName = "com.aliexpress.wallet.api.asset.facade.IMockToolsFacade";
        String serviceVersion = "1.0.0";
        String methodName = "carryForwardRefundStatus";
        String groupId = "HSF";
        String[] parameterType = {"com.aliexpress.wallet.api.asset.request.mocktools.CarryForwardRefundStatusReq"};
        List<Object> paramList = new ArrayList<>();
        JSONObject request = new JSONObject();
        request.put("memberId", memberId);
        request.put("userId", buyerId);
        request.put("outUniqueSeq", outUniqueSeq);
        paramList.add(request.toJavaObject(Object.class));
        try {
            return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static JSONObject addWishlistitem(Long buyerId, Long itemId) {
        int times = 0;
        while (times < Constant.MAX_TIMES) {
            try {
                String serviceName = "com.alibaba.intl.ae.biz.wishlist.service.interfaces.WishListRemoteService";
                String serviceVersion = "1.0.0-dhb";
                String methodName = "saveWishItem";
                String groupId = "HSF";
                String[] parameterType = {"com.alibaba.intl.ae.biz.wishlist.dto.WishItemSaveDTO"};
                List<Object> paramList = new ArrayList<>();
                JSONObject req = new JSONObject();
                req.put("itemId", itemId);
                req.put("itemType", "product");
                req.put("platform", "pc");
                req.put("adminMemberSeq", buyerId);
                paramList.add(req.toJavaObject(Object.class));
                return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
            } catch (Exception e) {
                times++;
                try {
                    Thread.sleep(500);
                } catch (InterruptedException interruptedException) {
                    interruptedException.printStackTrace();
                }
                if (times >= Constant.MAX_TIMES) {
                    throw new RuntimeException(e);
                }
            }
        }
        return null;
    }

    public static JSONObject consultModifyBuyerAddress(String buyerId, String tradeOrderId, String checkoutOrderId) {
        String serviceName = "com.alibaba.global.payment.api.facade.PaymentFacadeV2";
        String serviceVersion = "1.0.0";
        String methodName = "consultModifyBuyerAddress";
        String groupId = "HSF";
        String[] parameterType = {"com.alibaba.global.payment.api.request.modifyAddress.ConsultModifyBuyerAddressRequest"};
        List<Object> paramList = new ArrayList<>();
        JSONObject request = new JSONObject();
        request.put("tradeOrderId", tradeOrderId);
        request.put("routeId", buyerId);
        String str = "{\"clientIp\":\"*************\",\"clientUmid\":null,\"deviceTokenId\":\"YrAQ4miIHVEDAI/dfbuRth3a\",\"language\":\"en_US\",\"userAgent\":null,\"sessionId\":\"1864940087-5ef96fdb-d74f-4ba7-9daf-42b53fe7416b_al_b_J\",\"umidTokenId\":\"defaultToken3_init_callback_not_called@@https://pre-fn.aliexpress.com/fetch/dida-render/index@@1663938180980\",\"class\":\"com.alibaba.global.payment.api.request.refund.ClientEnv\",\"terminalType\":\"PC\"}";
        String str1 = "{\"logisticType\":null,\"country\":\"Korea\",\"lastName\":null,\"zipCode\":\"12345\",\"city\":\"Goseong-gun\",\"phoneNo\":null,\"areaName\":null,\"mobileCountryNo\":\"+82\",\"thirdLevelAddress\":\"\",\"state\":\"Gangwon-do\",\"class\":\"com.alibaba.global.payment.api.vo.LogisticsInfo\",\"email\":null,\"extAttrMap\":{},\"address2\":\"러ㅓㄹ 러ㅓㄹ\",\"contactName\":\"KR Have Customs\",\"address1\":\"로로 러ㅓㄹ\",\"fullName\":null,\"mobileNo\":\"01*********\",\"firstName\":null,\"divisionCode\":null,\"logisticAmount\":null,\"phoneCountryCode\":null,\"detailAddress\":null,\"phoneArea\":null,\"attributes\":{}}";
        request.put("clientEnv", JSONObject.parseObject(str));
        request.put("logisticsInfo", str1);
        request.put("checkoutOrderId", checkoutOrderId);
        JSONObject temp = new JSONObject();
        temp.put("aliId", buyerId);
        request.put("buyer", temp);
        paramList.add(request.toJavaObject(Object.class));
        try {
            return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    public static JSONObject addWishlistGroupItem(Long buyerId, Long itemId, String groupName, String groupType, Long targetGroupId) {
        int times = 0;
        while (times < Constant.MAX_TIMES) {
            try {
                String serviceName = "com.aliexpress.wishlist.open.mobile.service.MobileWishItemGroupRemoteService";
                String serviceVersion = "1.0.0-dhb";
                String methodName = "addWishItemGroup";
                String groupId = "HSF";
                String[] parameterType = {"com.aliexpress.wishlist.open.mobile.dto.WishItemGroupDTONew"};
                List<Object> paramList = new ArrayList<>();
                JSONObject req = new JSONObject();
                req.put("groupName", groupName);
                req.put("groupType", groupType);
                req.put("productId", itemId);
                req.put("targetGroupId", targetGroupId);
                req.put("adminMemberSeq", buyerId);
                paramList.add(req.toJavaObject(Object.class));
                return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
            } catch (Exception e) {
                times++;
                try {
                    Thread.sleep(500);
                } catch (InterruptedException interruptedException) {
                    interruptedException.printStackTrace();
                }
                if (times >= Constant.MAX_TIMES) {
                    throw new RuntimeException(e);
                }
            }
        }
        return null;
    }

    public static JSONObject removeWishlistItem(Long buyerId, Long itemId) {
        int times = 0;
        while (times < Constant.MAX_TIMES) {
            try {
                String serviceName = "com.aliexpress.wishlist.open.mobile.service.MobileProductWishListService";
                String serviceVersion = "1.0.0-dhb";
                String methodName = "addWishItemGroup";
                String groupId = "HSF";
                String[] parameterType = {"java.lang.Long", "long", "java.lang.Long"};
                List<Object> paramList = new ArrayList<>();
                paramList.add(buyerId);
                paramList.add(itemId);
                paramList.add(0);
                return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
            } catch (Exception e) {
                times++;
                try {
                    Thread.sleep(500);
                } catch (InterruptedException interruptedException) {
                    interruptedException.printStackTrace();
                }
                if (times >= Constant.MAX_TIMES) {
                    throw new RuntimeException(e);
                }
            }
        }
        return null;
    }

    public static JSONObject queryWishlistGroups(Long buyerId) {
        int times = 0;
        while (times < Constant.MAX_TIMES) {
            try {
                String serviceName = "com.aliexpress.wishlist.open.mobile.service.MobileWishListGroupService";
                String serviceVersion = "1.0.0-dhb";
                String methodName = "groupList";
                String groupId = "HSF";
                String[] parameterType = {"java.lang.Long", "java.lang.String"};
                List<Object> paramList = new ArrayList<>();
                paramList.add(buyerId);
                paramList.add("en_US");
                return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
            } catch (Exception e) {
                times++;
                try {
                    Thread.sleep(500);
                } catch (InterruptedException interruptedException) {
                    interruptedException.printStackTrace();
                }
                if (times >= Constant.MAX_TIMES) {
                    throw new RuntimeException(e);
                }
            }
        }
        return null;
    }

    public static JSONObject addWishlistItems(Long buyerId, List itemIds) {
        int times = 0;
        while (times < Constant.MAX_TIMES) {
            try {
                String serviceName = "com.aliexpress.wishlist.interfaces.facade.WishListReadServiceFacade";
                String serviceVersion = "1.0.0";
                String methodName = "itemWished";
                String groupId = "HSF";
                String[] parameterType = {"com.aliexpress.wishlist.interfaces.dto.request.ItemOpRequestDTO"};
                List<Object> paramList = new ArrayList<>();
                JSONObject req = new JSONObject();
                req.put("itemIdList", itemIds);
                req.put("adminMemberSeq", buyerId);
                paramList.add(req.toJavaObject(Object.class));
                return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
            } catch (Exception e) {
                times++;
                try {
                    Thread.sleep(500);
                } catch (InterruptedException interruptedException) {
                    interruptedException.printStackTrace();
                }
                if (times >= Constant.MAX_TIMES) {
                    throw new RuntimeException(e);
                }
            }
        }
        return null;
    }

    public static JSONObject mockRecommendAddress(String tradeOrderId, String packageId) throws Exception {
        String serviceName = "com.alibaba.ae.group.scp.dataservice.facade.ReconciliationConfigFacadeV2";
        String serviceVersion = "1.0.0";
        String methodName = "check";
        String groupId = "HSF";
        String[] parameterType = {"com.alibaba.ae.group.scp.dataservice.facade.dto.CheckRequest"};
        List<Object> paramList = new ArrayList<>();
        JSONObject req = new JSONObject();
        req.put("sceneCode", "SCENE_CODE_TEST_FO_PRE_ADDRESS");
        req.put("tradeOrderId", tradeOrderId);
        req.put("ruleType", "FULFILL_EXECUTOR");
        req.put("packageId", packageId);
        paramList.add(req.toJavaObject(Object.class));
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject queryBindCardAssets(Long memberId, String alipayUserId, String targetIp) {
        int times = 0;
        while (times < Constant.MAX_TIMES) {
            try {
                String serviceName = "com.global.card.asset.common.service.facade.api.BindCardAssetService";
                String serviceVersion = "1.0.0";
                String methodName = "queryBindCardAssets";
                String groupId = "HSF";
                String[] parameterType = {"com.global.card.asset.common.service.facade.request.BindCardAssetQueryRequest"};
                List<Object> paramList = new ArrayList<>();
                JSONObject req = new JSONObject();
                JSONObject extendInfo = new JSONObject();
                req.put("cardAssetTypeList", Arrays.asList("CARD"));
                req.put("cardAssetUseSceneList", Arrays.asList("PAY", "WITHDRAW", "REPAYMENT"));
                req.put("customerId", memberId);
                req.put("executeMode", "DEFAULT_MODE");
                extendInfo.put("alipayUserId", alipayUserId);
                req.put("alipayUserId", JSON.toJSONString(extendInfo));
                paramList.add(req.toJavaObject(Object.class));
                return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, targetIp, parameterType, paramList.toArray());
            } catch (Exception e) {
                times++;
                try {
                    Thread.sleep(500);
                } catch (InterruptedException interruptedException) {
                    interruptedException.printStackTrace();
                }
                if (times >= Constant.MAX_TIMES) {
                    throw new RuntimeException(e);
                }
            }
        }
        return null;
    }

    public static String cainiaoAccept(String foId, Long orderId) throws Exception {
        String serviceName = "com.alibaba.ae.qa.api.FulfillmentQueryFacade";
        String serviceVersion = "1.0.0";
        String methodName = "toolForAccept";
        String groupId = "HSF";
        String[] parameterType = {"java.lang.String", "java.lang.Long", "java.lang.String"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(foId);
        paramList.add(orderId);
        paramList.add("");
        return genericServiceInvoke2String(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static String cainiaoOutBound(String foId, Long buyerId, Long orderId) throws Exception {
        String serviceName = "com.alibaba.ae.qa.api.FulfillmentQueryFacade";
        String serviceVersion = "1.0.0";
        String methodName = "toolForOutBound";
        String groupId = "HSF";
        String[] parameterType = {"java.lang.String", "java.lang.Long", "java.lang.String", "java.lang.String"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(foId);
        paramList.add(buyerId);
        paramList.add("");
        paramList.add(String.valueOf(orderId));
        return genericServiceInvoke2String(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject createMiniAppOrder(String buyerId, String price, String currency, Number addressId) throws Exception {
        String serviceName = "com.alibaba.global.buy.market.api.CheckoutMiniAppFacade";
        String serviceVersion = "1.0.0-AE";
        String methodName = "createOrder";
        String groupId = "HSF";
        String[] parameterType = {"com.alibaba.global.buy.market.request.CreateOrderMiniAppRequest"};
        List<Object> paramList = new ArrayList<>();
        JSONObject req = new JSONObject();
        JSONObject total = new JSONObject();
        total.put("price", price);
        total.put("currency", currency);
        req.put("total", total);
        req.put("logisticsServiceName", "test qd");
        req.put("appKey", "qd");
        req.put("addressId", addressId);
        Long outId = System.currentTimeMillis();
        req.put("outId", outId.toString());
        req.put("buyerId", buyerId);
        paramList.add(req);
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject bindCardAssetOffline(Long memberId, String cardNo, String brand, String countryIssue, String targetIp) {
        int times = 0;
        while (times < Constant.MAX_TIMES) {
            try {
                String serviceName = "com.global.card.asset.common.service.facade.api.BindCardAssetService";
                String serviceVersion = "1.0.0";
                String methodName = "bindCardAssetOffline";
                String groupId = "HSF";
                String[] parameterType = {"com.global.card.asset.common.service.facade.request.OfflineBindCardAssetRequest"};
                List<Object> paramList = new ArrayList<>();
                JSONObject req = new JSONObject();
                JSONObject cardAssetDetail = new JSONObject();
                JSONObject cardDetail = new JSONObject();
                JSONObject userName = new JSONObject();
                JSONObject billingAddress = new JSONObject();
                req.put("executeMode", "DEFAULT_MODE"); //添加pci单绑卡
                req.put("customerId", memberId);
                cardAssetDetail.put("cardAssetType", "CARD");
                cardAssetDetail.put("cardAssetUseScene", "PAY");
                cardDetail.put("securityCode", "699");
                cardDetail.put("cardNo", cardNo); //卡号
                cardDetail.put("expiredMonth", "01");
                cardDetail.put("expiredYear", "2028");
                cardDetail.put("brand", brand); //卡品牌
                cardDetail.put("countryIssue", countryIssue);//注册卡地
                userName.put("firstName", "wang");
                userName.put("lastName", "xxx");
                cardDetail.put("userName", userName);
                billingAddress.put("region", "");
                billingAddress.put("state", "");
                billingAddress.put("city", "");
                billingAddress.put("zipCode", "");
                billingAddress.put("address1", "");
                billingAddress.put("address2", "");
                cardDetail.put("billingAddress", billingAddress);
                cardAssetDetail.put("cardDetail", cardDetail);
                req.put("cardAssetDetail", cardAssetDetail);
                paramList.add(req.toJavaObject(Object.class));
                return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, targetIp, parameterType, paramList.toArray());
            } catch (Exception e) {
                times++;
                try {
                    Thread.sleep(500);
                } catch (InterruptedException interruptedException) {
                    interruptedException.printStackTrace();
                }
                if (times >= Constant.MAX_TIMES) {
                    throw new RuntimeException(e);
                }
            }
        }
        return null;
    }

    public static JSONObject getPromiseToSellerDTOMap(Long sellerId) throws Exception {
        String serviceName = "com.alibaba.intl.ae.biz.promisetemplate.share.service.interfaces.PromiseTemplateInternalService";
        String serviceVersion = "1.0.0";
        String methodName = "getPromiseToSellerDTOMap";
        String groupId = "DUBBO";
        String[] parameterType = {"java.lang.Long"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(sellerId);
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject updatePromiseRule(Long sellerId, Long promiseId, String ruleDetail) throws Exception {
        String serviceName = "com.alibaba.intl.ae.biz.promisetemplate.share.service.interfaces.PromiseServiceForMtop";
        String serviceVersion = "1.0.0";
        String methodName = "updatePromiseRule";
        String groupId = "DUBBO";
        String[] parameterType = {"java.lang.Long", "java.lang.Long", "java.lang.String"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(sellerId);
        paramList.add(promiseId);
        paramList.add(ruleDetail);
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject sellerJoinOrExitPromise(Long sellerId, Long promiseId, Byte actionId) throws Exception {
        String serviceName = "com.alibaba.intl.ae.biz.promisetemplate.share.service.interfaces.PromiseServiceForMtop";
        String serviceVersion = "1.0.0";
        String methodName = "joinOrEditPromise";
        String groupId = "DUBBO";
        String[] parameterType = {"java.lang.Long", "java.lang.Long", "java.lang.Byte"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(sellerId);
        paramList.add(promiseId);
        paramList.add(actionId);
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject changOrderStatus(String orderId, String action) throws Exception {
        String serviceName = "com.alibaba.global.order.management.market.api.OrderManageMiniAppFacade";
        String serviceVersion = "1.0.0-AE";
        String methodName = "changeOrderStatus";
        String groupId = "HSF";
        String[] parameterType = {"com.alibaba.global.order.management.market.request.OrderStatusChangeMiniAppRequest"};
        List<Object> paramList = new ArrayList<>();
        JSONObject req = new JSONObject();
        req.put("tradeOrderId", orderId);
        req.put("action", action);
        paramList.add(req);
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject authCode(String json, String ip) throws Exception {
        String serviceName = "com.alibaba.global.payment.api.facade.PaymentOpenFacade";
        String serviceVersion = "1.0.0";
        String methodName = "auth";
        String groupId = "HSF";
        String[] parameterType = {"com.alibaba.global.payment.api.request.open.PaymentAuthRequest"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(JSON.parseObject(json));
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, ip, parameterType, paramList.toArray());
    }

    public static JSONObject getOrderDynamicPrice(Long orderId, Boolean dynamic) throws Exception {
        String serviceName = "com.alibaba.global.order.management.api.facade.OrderQueryForBuyerFacade";
        String serviceVersion = "1.0.0";
        String methodName = "queryTradeOrderById";
        String groupId = "HSF";
        String[] parameterType = {"java.lang.Long", "java.lang.Boolean"};
        List<Object> paramList = new ArrayList<>();
        JSONObject req = new JSONObject();
        req.put("tradeOrderId", orderId);
        req.put("usingDynamicPrice", dynamic);
        paramList.add(req);
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject modifyOrderInfo(Object request) throws Exception {
        String serviceName = "com.alibaba.global.order.management.api.facade.OrderManagementFacade";
        String serviceVersion = "1.0.0";
        String methodName = "modifyOrderInfo";
        String groupId = "HSF";
        String[] parameterType = {"com.alibaba.global.order.management.api.request.OrderInfoModifyBatchRequest"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(request);
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }


    public static JSONObject mockFR2MailNo(List<Long> reverseOrderLines, Long buyerId, Long version, Boolean islandPkg) throws Exception {
        String serviceName = "com.alibaba.reverse.platform.plus.server.api.AeReverseOrderPlusFacade";
        String serviceVersion = "1.0.0";
        String groupId = "HSF";
        String methodName;
        String[] parameterType;
        if (2 == version) {
            methodName = "agreeReturnPreHandle";
            parameterType = new String[]{"com.alibaba.reverse.platform.plus.server.api.request.AgreeReturnPreHandleRequest"};
        } else if (3 == version) {
            methodName = "agreeReturn";
            parameterType = new String[]{"com.alibaba.global.reverse.protocol.sdk.model.request.AgreeReturnRequest"};
        } else {
            throw new Exception("请输入正确的版本号");
        }
        JSONObject applyMailNoResult = new JSONObject();
        applyMailNoResult.put("labelUrl", "https://fpx-upload-bss-oss.oss-cn-shanghai.aliyuncs.com/0c15f595b35a4e58aa8f6151b3f58971.pdf");
        applyMailNoResult.put("mailNo", "Z7C198QG");
        applyMailNoResult.put("path", "/applyMailNoResult");
        applyMailNoResult.put("shippingProvider", "SPAIN GLS RETURN SERVICE");
        applyMailNoResult.put("islandPkg", islandPkg);
        applyMailNoResult.put("targetType", "com.alibaba.reverse.platform.plus.domain.service.model.ApplyMailNoResult");
        applyMailNoResult.put("barcodeLabel", "https://citi-ies-test.oss-cn-shenzhen.aliyuncs.com/**********/**********5222QRTEST0306.png");
        applyMailNoResult.put("selfPickupCode", "967234439");

        JSONObject extensionDTOMap = new JSONObject();
        extensionDTOMap.put("f_o_s", "2000340");
        extensionDTOMap.put("oowfr_fulfillment_status", "2015");
        extensionDTOMap.put("/applyMailNoResult", applyMailNoResult);

        JSONObject extendParam = new JSONObject();
        extendParam.put("extensionDTOMap", extensionDTOMap);

        JSONObject invokeInfo = new JSONObject();
        extendParam.put("appName", "");

        JSONObject operator = new JSONObject();
        operator.put("operatorId", "0");
        operator.put("operatorName", "local free return");
        operator.put("operatorType", "SYSTEM");


        JSONObject routingInfo = new JSONObject();
        routingInfo.put("buyerId", buyerId);

        JSONObject req = new JSONObject();
        req.put("extendParam", extendParam);
        req.put("operator", operator);
        req.put("reverseOrderLines", reverseOrderLines);
        req.put("routingInfo", routingInfo);
        req.put("invokeInfo", invokeInfo);

        List<Object> paramList = new ArrayList<>();
        paramList.add(req);
        logger.info("生成面单入参：req=" + req);
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject mockFR3PickUp(List<Long> reverseOrderLines, Long buyerId) throws Exception {
        String serviceName = "com.alibaba.reverse.platform.plus.server.api.AeReverseOrderPlusFacade";
        String serviceVersion = "1.0.0";
        String methodName = "agreeReturn";
        String groupId = "HSF";
        String[] parameterType = {"com.alibaba.global.reverse.protocol.sdk.model.request.AgreeReturnRequest"};

        JSONObject applyMailNoResult = new JSONObject();
        applyMailNoResult.put("collectorName", "树风");
        applyMailNoResult.put("collectorPhone", "********");
        applyMailNoResult.put("mailNo", "Z7C198QG");
        applyMailNoResult.put("path", "/applyMailNoResult");
        applyMailNoResult.put("shippingProvider", "SPAIN GLS RETURN SERVICE");
        applyMailNoResult.put("iconUrl", "https://ae01.alicdn.com/kf/Sfead3e2c5b964f5e8728fee329646a60w/843x177.png");
        applyMailNoResult.put("targetType", "com.alibaba.reverse.platform.plus.domain.service.model.ApplyMailNoResult");

        JSONObject extensionDTOMap = new JSONObject();
        extensionDTOMap.put("f_o_s", "2000340");
        extensionDTOMap.put("oowfr_fulfillment_status", "2015");
        extensionDTOMap.put("/applyMailNoResult", applyMailNoResult);

        JSONObject extendParam = new JSONObject();
        extendParam.put("extensionDTOMap", extensionDTOMap);

        JSONObject operator = new JSONObject();
        operator.put("operatorId", "0");
        operator.put("operatorName", "local free return");
        operator.put("operatorType", "SYSTEM");


        JSONObject routingInfo = new JSONObject();
        routingInfo.put("buyerId", buyerId);

        JSONObject req = new JSONObject();
        req.put("extendParam", extendParam);
        req.put("operator", operator);
        req.put("reverseOrderLines", reverseOrderLines);
        req.put("routingInfo", routingInfo);

        List<Object> paramList = new ArrayList<>();
        paramList.add(req);
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }


    public static JSONObject mockFRAscan(List<Long> reverseOrderLines, Long buyerId) throws Exception {
        String serviceName = "com.alibaba.global.reverse.protocol.sdk.facade.GlobalReverseOrderFacade";
        String serviceVersion = "1.0.0";
        String methodName = "submitReturnItem";
        String groupId = "HSF";
        String[] parameterType = {"com.alibaba.global.reverse.protocol.sdk.model.request.SubmitReturnItemRequest"};

        JSONObject senderAddress = new JSONObject();
        senderAddress.put("class", "com.alibaba.global.reverse.protocol.sdk.model.dto.AddressReqDTO");
        senderAddress.put("addressId", "");


        JSONObject extendParam = new JSONObject();
        extendParam.put("extensionDTOMap", null);
        extendParam.put("class", "com.alibaba.global.protocol.common.model.dto.ExtendParam");

        JSONObject routingInfo = new JSONObject();
        routingInfo.put("buyerId", buyerId);
        routingInfo.put("sellerId", 0);
        routingInfo.put("class", "com.alibaba.global.protocol.common.model.dto.RoutingInfoDTO");

        JSONObject invokeInfo = new JSONObject();
        invokeInfo.put("appName", "");
        invokeInfo.put("class", "com.alibaba.global.protocol.common.model.dto.InvokeInfoDTO");

        JSONObject fulfillmentService = new JSONObject();
        fulfillmentService.put("returnDeliveryType", "SELF_SEND");
        fulfillmentService.put("deliveryProviderName", "SPAIN GLS RETURN SERVIC");
        fulfillmentService.put("class", "com.alibaba.global.reverse.protocol.sdk.model.dto.FulfillmentServiceReqDTO");
        fulfillmentService.put("deliveryProviderCode", "SPAIN GLS RETURN SERVICE");
        fulfillmentService.put("trackingNumber", "WH2551610005905001299");

        JSONObject operator = new JSONObject();
        operator.put("operatorType", "SYSTEM");
        operator.put("operatorId", 0);
        operator.put("operatorName", "local free return");

        JSONObject req = new JSONObject();
        req.put("senderAddress", senderAddress);
        req.put("extendParam", extendParam);
        req.put("routingInfo", routingInfo);
        req.put("invokeInfo", invokeInfo);
        req.put("fulfillmentService", fulfillmentService);
        req.put("class", "com.alibaba.global.reverse.protocol.sdk.model.request.SubmitReturnItemRequest");
        req.put("operator", operator);
        req.put("reverseOrderLines", reverseOrderLines);

        List<Object> paramList = new ArrayList<>();
        paramList.add(req);
        logger.info("ascan入参：req=" + req);
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject mockFRDSCAN(List<Long> reverseOrderLines, Long buyerId) throws Exception {
        String serviceName = "com.alibaba.reverse.platform.plus.server.api.AeReverseOrderPlusFacade";
        String serviceVersion = "1.0.0";
        String methodName = "warehouseReceived";
        String groupId = "HSF";
        String[] parameterType = {"com.alibaba.reverse.platform.plus.server.api.request.FulfillmentTriggerRequest"};


        JSONObject extensionDTOMap = new JSONObject();
        extensionDTOMap.put("f_o_s", "2050100");
        extensionDTOMap.put("oowfr_fulfillment_status", "2050");

        JSONObject extendParam = new JSONObject();
        extendParam.put("extensionDTOMap", extensionDTOMap);
        extendParam.put("class", "com.alibaba.global.protocol.common.model.dto.ExtendParam");

        JSONObject routingInfo = new JSONObject();
        routingInfo.put("buyerId", buyerId);
        routingInfo.put("class", "com.alibaba.global.protocol.common.model.dto.RoutingInfoDTO");


        JSONObject operator = new JSONObject();
        operator.put("operatorId", "0");
        operator.put("operatorName", "official overseas warehouse");
        operator.put("operatorType", "SYSTEM");
        operator.put("class", "com.alibaba.global.reverse.protocol.sdk.model.dto.OperatorDTO");


        JSONObject req = new JSONObject();
        req.put("extendParam", extendParam);
        req.put("routingInfo", routingInfo);
        req.put("class", "com.alibaba.reverse.platform.plus.server.api.request.FulfillmentTriggerRequest");
        req.put("operator", operator);
        req.put("reverseOrderLines", reverseOrderLines);

        List<Object> paramList = new ArrayList<>();
        paramList.add(req);
        logger.info("Dscan入参：req=" + req);
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject mockQCCheck(List<Long> lastReverseOrderLines, Long buyerId) throws Exception {
        String serviceName = "com.alibaba.reverse.platform.plus.server.api.AeReverseOrderPlusFacade";
        String serviceVersion = "1.0.0";
        String methodName = "warehouseQCCheckFinished";
        String groupId = "HSF";
        String[] parameterType = {"com.alibaba.reverse.platform.plus.server.api.request.FulfillmentTriggerRequest"};

        JSONObject extensionDTOMap = new JSONObject();
        extensionDTOMap.put("f_o_s", "2070050");

        JSONObject extendParam = new JSONObject();
        extendParam.put("extensionDTOMap", extensionDTOMap);
        extendParam.put("class", "com.alibaba.global.protocol.common.model.dto.ExtendParam");

        JSONObject routingInfo = new JSONObject();
        routingInfo.put("buyerId", buyerId);
        routingInfo.put("class", "com.alibaba.global.protocol.common.model.dto.RoutingInfoDTO");

        JSONObject operator = new JSONObject();
        operator.put("operatorId", "0");
        operator.put("operatorName", "official overseas warehouse");
        operator.put("operatorType", "SYSTEM");
        operator.put("class", "com.alibaba.global.reverse.protocol.sdk.model.dto.OperatorDTO");

        JSONObject req = new JSONObject();
        req.put("extendParam", extendParam);
        req.put("routingInfo", routingInfo);
        req.put("class", "com.alibaba.reverse.platform.plus.server.api.request.FulfillmentTriggerRequest");
        req.put("operator", operator);
        req.put("reverseOrderLines", lastReverseOrderLines);

        List<Object> paramList = new ArrayList<>();
        paramList.add(req);
        logger.info("质检入参：req=" + req);
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject getBreachContract(String sellerId, String breachId) throws Exception {
        String serviceName = "com.alibaba.ae.service.open.facade.SellerBreachContractQueryFacade";
        String serviceVersion = "1.0.0";
        String methodName = "queryBreachContractDetail";
        String groupId = "HSF";
        String[] parameterTypes = {"com.alibaba.ae.service.open.param.SellerQueryBreachContractDetailParam"};
        List<Object> paramList = new ArrayList<>();
        JSONObject req = new JSONObject();
        req.put("sellerId", sellerId);
        req.put("breachContractId", breachId);
        req.put("lang", "zh_CN");
        paramList.add(req.toJavaObject(Object.class));
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterTypes, paramList.toArray());
    }

    public static JSONObject queryBreachContractDetail(Long breachId) throws Exception {
        String serviceName = "com.alibaba.ae.service.open.facade.BreachContractArbitrationFacade";
        String serviceVersion = "1.0.0";
        String methodName = "queryBreachContractDetail";
        String groupId = "HSF";
        String[] parameterTypes = {"java.lang.Long"};
        List<Object> paramList = new ArrayList<>();
        JSONObject req = new JSONObject();
        req.put("breachContractId", breachId);
        paramList.add(req.toJavaObject(Object.class));
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterTypes, paramList.toArray());
    }

    public static JSONObject agreeRefundCheckPoint(Long buyerId, List<Long> reverseOrderLineIds) throws Exception {
        String serviceName = "com.alibaba.reverse.platform.plus.server.api.AeReverseOrderPlusFacade";
        String serviceVersion = "1.0.0";
        String methodName = "agreeRefundCheckPoint";
        String groupId = "HSF";
        String[] parameterTypes = {"com.alibaba.reverse.platform.plus.server.api.request.AgreeRefundCheckPointRequest"};
        List<Object> paramList = new ArrayList<>();
        JSONObject req = JSONObject.parseObject(Constant.AGREE_REFUND_CHECK_POINT_REQUEST);
        req.getJSONObject("routingInfo").put("buyerId", buyerId);
        JSONArray reverseOrderLines = new JSONArray();
        reverseOrderLineIds.forEach(it -> {
            JSONObject reverseOrderLine = new JSONObject();
            reverseOrderLine.put("reverseOrderLineId", it);
            reverseOrderLines.add(reverseOrderLine);
        });
        req.put("reverseOrderLines", reverseOrderLines);
        paramList.add(req.toJavaObject(Object.class));
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterTypes, paramList.toArray());
    }

    public static JSONObject queryFulfillmentOrderByFoId(String foId) throws Exception {
        String serviceName = "com.alibaba.global.uop.ae.api.AEFulfillmentOrderQueryFacade";
        String serviceVersion = "1.0.0";
        String methodName = "queryFulfillmentOrderByFoId";
        String groupId = "HSF";
        String[] parameterTypes = {"java.lang.String", "com.alibaba.global.protocol.common.model.dto.InvokeInfoDTO"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(foId);
        JSONObject foInfo = new JSONObject();
        foInfo.put("class", "com.alibaba.global.protocol.common.model.dto.InvokeInfoDTO");
        paramList.add(foInfo.toJavaObject(Object.class));
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterTypes, paramList.toArray());

    }


    public static JSONObject batchInitBreachContractData(JSONObject req) throws Exception {
        String serviceName = "com.alibaba.ae.service.open.facade.InternalToolsFacade";
        String serviceVersion = "1.0.0";
        String methodName = "batchInitBreachContractData";
        String groupId = "HSF";
        String[] parameterTypes = {"com.alibaba.ae.service.open.param.MockBatchInitDataReq"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(req.toJavaObject(Object.class));
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterTypes, paramList.toArray());
    }

    public static JSONObject queryJitPurchaseOrder(String po) throws Exception {
        String serviceName = "com.alibaba.ae.dc.inbound.client.api.JitOrderReadService";
        String serviceVersion = "1.0.0";
        String methodName = "queryJitPurchaseOrder";
        String groupId = "HSF";
        String[] parameterTypes = {"com.alibaba.ae.dc.inbound.client.dto.JitPurchaseOrderQueryDTO"};
        List<Object> paramList = new ArrayList<>();
        JSONObject req = new JSONObject();
        req.put("purchaseOrderNo", po);
        paramList.add(req.toJavaObject(Object.class));
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterTypes, paramList.toArray());
    }

    public static JSONObject queryBreachContractDO(Long breachId) throws Exception {
        String serviceName = "com.alibaba.ae.service.facade.BreachContractQueryForConsoleFacade";
        String serviceVersion = "1.0.0";
        String methodName = "queryBreachContractDO";
        String groupId = "HSF";
        String[] parameterTypes = {"java.lang.Long"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(breachId);
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterTypes, paramList.toArray());
    }

    public static JSONArray queryBreachContractPenaltyDO(Long breachId) throws Exception {
        String serviceName = "com.alibaba.ae.service.facade.BreachContractQueryForConsoleFacade";
        String serviceVersion = "1.0.0";
        String methodName = "queryBreachContractPenaltyDO";
        String groupId = "HSF";
        String[] parameterTypes = {"java.lang.Long"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(breachId);
        return genericServiceInvoke2Array(serviceName, serviceVersion, methodName, groupId, parameterTypes, paramList.toArray());
    }

    public static JSONArray queryBreachContractFundBillDO(Long breachId) throws Exception {
        String serviceName = "com.alibaba.ae.service.facade.BreachContractQueryForConsoleFacade";
        String serviceVersion = "1.0.0";
        String methodName = "queryBreachContractFundBillDO";
        String groupId = "HSF";
        String[] parameterTypes = {"java.lang.Long"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(breachId);
        return genericServiceInvoke2Array(serviceName, serviceVersion, methodName, groupId, parameterTypes, paramList.toArray());
    }

    public static JSONArray queryBreachContractRecordDO(Long breachId) throws Exception {
        String serviceName = "com.alibaba.ae.service.facade.BreachContractQueryForConsoleFacade";
        String serviceVersion = "1.0.0";
        String methodName = "queryBreachContractRecordDO";
        String groupId = "HSF";
        String[] parameterTypes = {"java.lang.Long"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(breachId);
        return genericServiceInvoke2Array(serviceName, serviceVersion, methodName, groupId, parameterTypes, paramList.toArray());
    }

    public static JSONObject croCloseMainOrder(Long orderId, Long buyerId) throws Exception {
        String serviceName = "com.aliexpress.issue.api.AeIssueCancelOrderWriteFacade";
        String serviceVersion = "2.0.0";
        String methodName = "openCancelOrderIssue";
        String groupId = "HSF";
        String[] parameterType = {"java.lang.Long", "java.lang.String", "com.aliexpress.issue.dispute.pojo.common.CancelOrderOperatorRequest"};

        JSONObject cancelOrderOperatorRequest = new JSONObject();
        cancelOrderOperatorRequest.put("orderId", orderId);
        cancelOrderOperatorRequest.put("cancelEvent", "mteeCancel");
        cancelOrderOperatorRequest.put("operatorAliId", 0);
        cancelOrderOperatorRequest.put("currentOperatorRole", "platform");
        cancelOrderOperatorRequest.put("issueOperateSource", "CRO");
        cancelOrderOperatorRequest.put("isOrderLineCancel", false);
        cancelOrderOperatorRequest.put("adminAliId", buyerId);
        cancelOrderOperatorRequest.put("cancelReason", "security_close6");
        cancelOrderOperatorRequest.put("operatorRole", "platform");
        cancelOrderOperatorRequest.put("operatorMemo", "auto cancel by cro");


        List<Object> paramList = new ArrayList<>();
        paramList.add(buyerId);
        paramList.add("en_US");
        paramList.add(cancelOrderOperatorRequest);
        //   EagleEye.putUserData("dpath_env", "support_risk_close_after_send_goods");

        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject croCloseSubOrder(Long orderId, String subOrderIds, Long buyerId) throws Exception {
        String serviceName = "com.aliexpress.issue.api.AeIssueCancelOrderWriteFacade";
        String serviceVersion = "2.0.0";
        String methodName = "openCancelOrderIssue";
        String groupId = "HSF";
        String[] parameterType = {"java.lang.Long", "java.lang.String", "com.aliexpress.issue.dispute.pojo.common.CancelOrderOperatorRequest"};

        String[] strings = subOrderIds.split(",");
        Long[] longs = (Long[]) ConvertUtils.convert(strings, Long.class);

        JSONObject cancelOrderOperatorRequest = new JSONObject();
        cancelOrderOperatorRequest.put("orderLineIds", longs);

        cancelOrderOperatorRequest.put("orderId", orderId);
        cancelOrderOperatorRequest.put("cancelEvent", "mteeCancel");
        cancelOrderOperatorRequest.put("operatorAliId", buyerId);
        cancelOrderOperatorRequest.put("issueOperateSource", "CRO");


        List<Object> orderLines = new ArrayList<>();
        for (Long line : longs) {
            JSONObject orderLine = new JSONObject();
            orderLine.put("unit", 1);
            orderLine.put("orderLineId", line);
            orderLine.put("class", "com.aliexpress.issue.dispute.pojo.common.OrderLineParam");
            orderLines.add(orderLine);
        }
        cancelOrderOperatorRequest.put("needCanceledOrderLines", orderLines);

        cancelOrderOperatorRequest.put("adminAliId", buyerId);
        cancelOrderOperatorRequest.put("cancelReason", "security_close6");
        cancelOrderOperatorRequest.put("class", "com.aliexpress.issue.dispute.pojo.common.CancelOrderOperatorRequest");
        cancelOrderOperatorRequest.put("operatorRole", "platform");
        cancelOrderOperatorRequest.put("operatorMemo", "auto cancel by cro");


        List<Object> paramList = new ArrayList<>();
        paramList.add(buyerId);
        paramList.add("en_US");
        paramList.add(cancelOrderOperatorRequest);

        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }


    public static JSONObject queryReverseOrderLineById(Long buyerId, Long reverseOrderLineId) throws Exception {
        String serviceName = "com.alibaba.reverse.cathedral.api.facade.ReverseOrderQueryFacade";
        String serviceVersion = "1.0.0";
        String methodName = "queryReverseOrderLineById";
        String groupId = "HSF";
        String[] parameterType = {"java.lang.Long", "java.lang.Long"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(buyerId);
        paramList.add(reverseOrderLineId);
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject cartMtopFacadeAdd(long buyerId, String addItems, String currency, String shipTo, String business, String bizParams) throws Exception {
        int times = 0;
        while (times < Constant.MAX_TIMES) {
            try {
                String serviceName = "com.alibaba.global.carts.api.facade.CartMtopFacade";
                String serviceVersion = "1.0.0.sirius";
                String methodName = "add";
                String groupId = "HSF";
                String[] parameterType = {"com.alibaba.global.carts.api.request.CartMtopAddRequest"};
                List<Object> paramList = new ArrayList<>();
                JSONObject req = new JSONObject();
                req.put("shipToCountry", shipTo);
                req.put("business", business);
                req.put("leanMode", false);
                req.put("debugInfo", false);
                req.put("ttid", "201200@Aliexpress_iphone_8.79.0.3390");
                req.put("appVersion", "8.79.0.3390");
                req.put("userId", buyerId);
                req.put("system", "mobile");
                req.put("currency", currency);
                req.put("addItems", addItems);
                req.put("bizParams", bizParams);
                paramList.add(req.toJavaObject(Object.class));
                return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
            } catch (Exception e) {
                times++;
                try {
                    Thread.sleep(500);
                } catch (InterruptedException interruptedException) {
                    interruptedException.printStackTrace();
                }
                if (times >= Constant.MAX_TIMES) {
                    throw new RuntimeException(e);
                }
            }
        }
        return null;
    }

    public static JSONObject cartMtopFacadeQuery(long buyerId, String business) throws Exception {
        int times = 0;
        while (times < Constant.MAX_TIMES) {
            try {
                String serviceName = "com.alibaba.global.carts.api.facade.CartMtopFacade";
                String serviceVersion = "1.0.0.sirius";
                String methodName = "queryCartLines";
                String groupId = "HSF";
                String[] parameterType = {"com.alibaba.global.carts.api.request.CartQueryRequest"};
                List<Object> paramList = new ArrayList<>();
                JSONObject req = new JSONObject();
                req.put("token", "aHNmb3Bz");
                req.put("userId", buyerId);
                req.put("business", business);
                paramList.add(req.toJavaObject(Object.class));
                return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
            } catch (Exception e) {
                times++;
                try {
                    Thread.sleep(500);
                } catch (InterruptedException interruptedException) {
                    interruptedException.printStackTrace();
                }
                if (times >= Constant.MAX_TIMES) {
                    throw new RuntimeException(e);
                }
            }
        }
        return null;
    }

    public static JSONObject queryAEBrainIdRealtimeGroups(String country, List<Long> itemIds, List<Long> skuIds) throws Exception {
        int times = 0;
        while (times < Constant.MAX_TIMES) {
            try {
                String serviceName = "com.aliexpress.aebrain.provider.client.AEBrainIdCheckService";
                String serviceVersion = "1.0.0";
                String methodName = "queryAEBrainIdRealtimeGroups";
                String groupId = "HSF";
                String[] parameterType = {"com.aliexpress.aebrain.provider.model.AEBrainIdQueryRequest"};
                List<Object> paramList = new ArrayList<>();
                JSONObject req = new JSONObject();
                req.put("country", country);
                req.put("sourceType", "CART");
                req.put("itemIds", itemIds);
                req.put("skuIds", skuIds);
                paramList.add(req.toJavaObject(Object.class));
                return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
            } catch (Exception e) {
                times++;
                try {
                    Thread.sleep(500);
                } catch (InterruptedException interruptedException) {
                    interruptedException.printStackTrace();
                }
                if (times >= Constant.MAX_TIMES) {
                    throw new RuntimeException(e);
                }
            }
        }
        return null;
    }

    public static JSONObject timeOutFacadeTest(String orderID) throws Exception {
        String serviceName = "com.alibaba.global.order.management.api.facade.TestFacade";
        String serviceVersion = "1.0.0";
        String methodName = "test";
        String groupId = "HSF";
        String[] parameterType = {"java.lang.String"};
        String params = "package com.alibaba.ae.trade.om.facade.message.executor.uop\n" +
                "\n" +
                "import com.alibaba.global.timeout.dto.AlarmTaskDTO\n" +
                "import com.alibaba.global.timeout.module.AlarmClockEvent\n" +
                "import com.alibaba.global.trade.platform.om.message.executor.timeout.SendGoodsTimeoutCallback\n" +
                "import org.springframework.context.ApplicationContext\n" +
                "\n" +
                "ApplicationContext ac = context.getContext();\n" +
                "\n" +
                "try {\n" +
                "    SendGoodsTimeoutCallback callbackBean = ac.getBean(SendGoodsTimeoutCallback.class);\n" +
                "    AlarmTaskDTO taskDTO = new AlarmTaskDTO();\n" +
                "    taskDTO.setAlarmName(\"trade-ship-time-out\");\n" +
                "    taskDTO.setAlarmBizOrderId(" + orderID + ")\n" +
                "    AlarmClockEvent event = new AlarmClockEvent(taskDTO);\n" +
                "    Boolean timeoutResult = callbackBean.onTimeout(event);\n" +
                "    context.setAttribute(\"result\", timeoutResult)\n" +
                "}\n" +
                "catch (Throwable e) {\n" +
                "    context.setAttribute(\"e\", e.toString())\n" +
                "}";
        List<Object> paramList = new ArrayList<>();
        paramList.add(params);
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject getReverseInfoByReverseOrderLineId(Long reverseOrderLineId) throws Exception {
        String serviceName = "com.alibaba.ae.reverse.server.api.facade.InternalToolFacade";
        String serviceVersion = "1.0.0";
        String methodName = "getReverseData";
        String groupId = "HSF";
        String[] parameterType = {"java.lang.Long"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(reverseOrderLineId);
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject applyBreach(JSONObject request) throws Exception {
        String serviceName = "com.alibaba.ae.service.open.facade.BreachContractFacade";
        String serviceVersion = "1.0.0";
        String methodName = "apply";
        String groupId = "HSF";
        String[] parameterType = {"com.alibaba.ae.service.open.param.ApplyBreachContractParam"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(request.toJavaObject(Object.class));
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject putOrDelFeature(Long buyerId, Long addressId, String cpfStatus) throws Exception {
        String serviceName = "com.alibaba.global.address.biz.api.facade.UserAddressOpsFacade";
        String serviceVersion = "2.0.0.global";
        String methodName = "putOrDelFeature";
        String groupId = "HSF";
        String[] parameterType = {"java.lang.Long", "java.lang.Long", "java.lang.String", "java.lang.String", "java.lang.String"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(buyerId);
        paramList.add(addressId);
        paramList.add("put");
        paramList.add("cpfStatus");
        paramList.add(cpfStatus);
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject mockInitLogisticOnlineOrderDTO(JSONObject request) throws Exception {
        String serviceName = "com.alibaba.ae.service.facade.ForwardSellerFacade";
        String serviceVersion = "1.0.0";
        String methodName = "mockInitLogisticOnlineOrderDTO";
        String groupId = "HSF";
        String[] parameterType = {"com.alibaba.ae.service.domain.dto.LogisticOnlineOrderDTO"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(request.toJavaObject(Object.class));
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject queryWarehouseOrdersByTradeOrderId(Long buyerId, String tradeOrderId) throws Exception {
        String serviceName = "com.alibaba.global.uop.ae.api.WarehouseOrderServiceFacade";
        String serviceVersion = "1.0.0";
        String methodName = "queryWoByToIdAndBuyerId";
        String groupId = "HSF";
        String[] parameterType = {"java.lang.String", "java.lang.Long"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(tradeOrderId);
        paramList.add(buyerId);
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject manualEventHandleMsg(JSONObject request) throws Exception {
        String serviceName = "com.alibaba.global.uop.ae.api.tool.fix.ReverseToolFacade";
        String serviceVersion = "1.0.1-new";
        String methodName = "manualEventHandleMsg";
        String groupId = "HSF";
        String[] parameterType = {"com.alibaba.fastjson.JSONObject"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(request.toJavaObject(Object.class));
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject updateOrderLineFeatures(Long buyerId, JSONObject scenarioReq) throws Exception {
        String serviceName = "com.alibaba.global.order.management.api.facade.FeaturesOperateRegionFacade";
        String serviceVersion = "1.0.0";
        String methodName = "updateOrderFeatures";
        String groupId = "HSF";
        String[] parameterType = {"com.alibaba.global.order.management.api.request.UpdateOrderFeaturesRequest", "java.lang.Long"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(scenarioReq.toJavaObject(Object.class));
        paramList.add(buyerId);
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static void measureAll(String content, String empId) {
        try {
            MeasureLogger measureLogger = MeasureLogger.start("ACCESS_LOG", "PVUV", MeasureLogger.Level.INFO);
            measureLogger.setInvokeStartTime(System.currentTimeMillis());
            measureLogger.setSuccess(true).setInvokeEndTime(System.currentTimeMillis()).setEmpId(empId).setContent(content).end();
            measureLogger.isSuccess();
            logger.info("measure info:" + measureLogger.getContent() + "  " + "user:" + measureLogger.getEmpId());
        } catch (Exception e) {
            logger.error("Measure exception", e);
        }
    }

    public static JSONObject modifyAddress(JSONObject reqJson) throws Exception {
        String serviceName = "com.alibaba.global.order.management.api.facade.OrderManagementRegionFacade";
        String serviceVersion = "1.0.0";
        String methodName = "modifyAddress";
        String groupId = "HSF";
        String[] parameterType = {"com.alibaba.global.order.management.api.request.ModifyOrderAddressRequest"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(reqJson.toJavaObject(Object.class));
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject refundRecordDetails(Long buyerId, Long tradeOrderId, Long tradeOrderLineId) throws Exception {
        String serviceName = "com.alibaba.ae.reverse.server.api.facade.AeRefundRecordFacade";
        String serviceVersion = "1.0.0";
        String methodName = "refundRecordDetails";
        String groupId = "HSF";
        String[] parameterType = {"com.alibaba.ae.reverse.server.api.request.AeRefundRecordReq"};
        List<Object> paramList = new ArrayList<>();
        JSONObject request = new JSONObject();
        request.put("timeZone", "");
        request.put("locale", "en_US");
        request.put("buyerId", buyerId);
        request.put("tradeOrderId", tradeOrderId);
        request.put("tradeOrderLineId", tradeOrderLineId);
        paramList.add(request.toJavaObject(Object.class));
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }


    public static JSONObject triggerXdayAlarmTimeout(Long buyerId, Long tradeOrderId) throws Exception {
        String serviceName = "com.alibaba.ae.service.open.facade.InternalToolsFacade";
        String serviceVersion = "1.0.0";
        String methodName = "triggerXdayAlarmTimeout";
        String groupId = "HSF";
        String[] parameterType = {"java.lang.Long", "java.lang.Long"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(buyerId);
        paramList.add(tradeOrderId);
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject triggerLogisticTrajectoryMessage(String msgBody) throws Exception {
        String serviceName = "com.alibaba.ae.service.open.facade.InternalToolsFacade";
        String serviceVersion = "1.0.0";
        String methodName = "triggerLogisticTrajectoryMessage";
        String groupId = "HSF";
        String[] parameterType = {"java.lang.String"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(msgBody);
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject triggerOnTimeGuarantee(Long buyerId, Long sellerId, Long tradeOrderId, Long tradeOderLineId, Map<String, String> ext) throws Exception {
        String serviceName = "com.alibaba.ae.service.open.facade.InternalToolsFacade";
        String serviceVersion = "1.0.0";
        String methodName = "triggerOnTimeGuarantee";
        String groupId = "HSF";
        String[] parameterType = {"java.lang.Long", "java.lang.Long", "java.lang.Long", "java.lang.Long", "java.lang.String", "java.lang.String", "java.util.Map"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(buyerId);
        paramList.add(sellerId);
        paramList.add(tradeOrderId);
        paramList.add(tradeOderLineId);
        paramList.add("AE_MAIN_ORDER");
        paramList.add("TRADE");
        paramList.add(ext);
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject triggerXdayReverse(Long buyerId, Long tradeOrderId, List<Long> lineIdList, Long timestamp) throws Exception {
        String serviceName = "com.alibaba.ae.service.open.facade.InternalToolsFacade";
        String serviceVersion = "1.0.0";
        String methodName = "triggerXdayReverse";
        String groupId = "HSF";
        String[] parameterType = {"java.lang.Long", "java.lang.Long", "java.util.List", "java.lang.Boolean", "java.lang.Long"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(buyerId);
        paramList.add(tradeOrderId);
        paramList.add(lineIdList);
        paramList.add(false);
        paramList.add(timestamp);
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject triggerXdayCanApply(Long buyerId, Long tradeOrderId, List<Long> lineIdList, Long timestamp) throws Exception {
        String serviceName = "com.alibaba.ae.service.open.facade.InternalToolsFacade";
        String serviceVersion = "1.0.0";
        String methodName = "triggerXdayCanApply";
        String groupId = "HSF";
        String[] parameterType = {"java.lang.Long", "java.lang.Long", "java.util.List", "java.lang.Long"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(buyerId);
        paramList.add(tradeOrderId);
        paramList.add(lineIdList);
        paramList.add(timestamp);
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject triggerShipMsg(Long tradeOrderId, Long buyerId) throws Exception {
        String serviceName = "com.alibaba.ae.service.open.facade.InternalToolsFacade";
        String serviceVersion = "1.0.0";
        String methodName = "triggerShipMsg";
        String groupId = "HSF";
        String[] parameterType = {"java.lang.Long", "java.lang.Long"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(tradeOrderId);
        paramList.add(buyerId);
        logger.info("发货监听创建超时任务：req=" + paramList);
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject reConsumeMsgByUop(JSONArray request) throws Exception {
        String serviceName = "com.alibaba.global.uop.ae.api.tool.fix.FulfillmentOrderToolFacade";
        String serviceVersion = "1.0.0";
        String methodName = "reConsumeMsg";
        String groupId = "HSF";
        String[] parameterType = {"java.util.List", "java.lang.String"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(request);
        paramList.add("cainiao_event");
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject saveUserTag(Long buyerId, String tag, String value) throws Exception {
        String serviceName = "com.alibaba.global.user.api.facade.UserDataTagFacade";
        String serviceVersion = "1.0.0";
        String methodName = "saveUserTag";
        String groupId = "HSF";
        String[] parameterType = {"java.lang.Long", "java.lang.String", "java.lang.String"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(buyerId);
        paramList.add(tag);
        paramList.add(value);
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject deleteUserTag(Long buyerId, String tag) throws Exception {
        String serviceName = "com.alibaba.global.user.api.facade.UserDataTagFacade";
        String serviceVersion = "1.0.0";
        String methodName = "deleteUserTag";
        String groupId = "HSF";
        String[] parameterType = {"java.lang.Long", "java.lang.String"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(buyerId);
        paramList.add(tag);
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject findUserIdByEmail(String userEmail) throws Exception {
        int times = 0;
        while (times < Constant.MAX_TIMES) {
            try {
                String serviceName = "com.alibaba.global.user.api.facade.UserReadFacade";
                String serviceVersion = "1.0.0";
                String methodName = "getUserByEmail";
                String groupId = "HSF";
                String[] parameterType = {"java.lang.String"};
                List<Object> paramList = new ArrayList<>();
                paramList.add(userEmail);
                return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
            } catch (Exception e) {
                times++;
                try {
                    Thread.sleep(500);
                } catch (InterruptedException interruptedException) {
                    interruptedException.printStackTrace();
                }
                if (times >= Constant.MAX_TIMES) {
                    throw new RuntimeException(e);
                }
            }
        }
        return null;
    }

    public static JSONObject nonOrderOpenGroup(Long showThreshold, List<Long> itemIds, Long threshold, String shareGroupCode, Long groupConfigId, Long businessGroupType, String starHeadIcon, String nickNameEn, String nickName, String squareStarHeadIconUrl, String crownPicUrl, String starHeadIconWithCrown, Long openGroupTime, String surroundPicUrl) throws Exception {
        int times = 0;
        while (times < Constant.MAX_TIMES) {
            try {
                String serviceName = "com.aidc.ae.group.api.service.IGroupService";
                String serviceVersion = "1.0.0";
                String methodName = "nonOrderOpenGroup";
                String groupId = "HSF";
                String[] parameterType = {"com.aidc.ae.group.api.request.OpenGroupRequest"};
                List<Object> paramList = new ArrayList<>();
                JSONObject req = new JSONObject();
                req.put("showThreshold", showThreshold);
                req.put("itemIds", itemIds);
                req.put("threshold", threshold);
                if (shareGroupCode != null) {
                    req.put("shareGroupCode", shareGroupCode);
                }
                req.put("groupConfigId", groupConfigId);
                req.put("businessGroupType", businessGroupType);
                req.put("starHeadIcon", starHeadIcon);
                req.put("nickNameEn", nickNameEn);
                req.put("nickName", nickName);
                req.put("squareStarHeadIconUrl", squareStarHeadIconUrl);
                req.put("crownPicUrl", crownPicUrl);
                req.put("starHeadIconWithCrown", starHeadIconWithCrown);
                req.put("openGroupTime", openGroupTime);
                req.put("surroundPicUrl", surroundPicUrl);
                paramList.add(req.toJavaObject(Object.class));
                return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
            } catch (Exception e) {
                times++;
                try {
                    Thread.sleep(500);
                } catch (InterruptedException interruptedException) {
                    interruptedException.printStackTrace();
                }
                if (times >= Constant.MAX_TIMES) {
                    throw new RuntimeException(e);
                }
            }
        }
        return null;
    }

    public static JSONObject createGroupConfig(Long allowJoinSatisfy, Long groupExpireTime, Long groupMode, Long threshold, Long promotionId, Long openType, Long timeUnit, Long status) throws Exception {
        int times = 0;
        while (times < Constant.MAX_TIMES) {
            try {
                String serviceName = "com.aidc.ae.group.api.service.IGroupService";
                String serviceVersion = "1.0.0";
                String methodName = "creatGroupConfig";
                String groupId = "HSF";
                String[] parameterType = {"com.aidc.ae.group.api.request.GroupConfigRequest"};
                List<Object> paramList = new ArrayList<>();
                JSONObject req = new JSONObject();
                req.put("allowJoinSatisfy", allowJoinSatisfy);
                req.put("groupExpireTime", groupExpireTime);
                req.put("groupMode", groupMode);
                req.put("threshold", threshold);
                req.put("promotionId", promotionId);
                req.put("openType", openType);
                req.put("timeUnit", timeUnit);
                req.put("status", status);
                paramList.add(req.toJavaObject(Object.class));
                return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
            } catch (Exception e) {
                times++;
                try {
                    Thread.sleep(500);
                } catch (InterruptedException interruptedException) {
                    interruptedException.printStackTrace();
                }
                if (times >= Constant.MAX_TIMES) {
                    throw new RuntimeException(e);
                }
            }
        }
        return null;
    }

    public static JSONObject updateGroupConfig(Long id, Long allowJoinSatisfy, Long groupExpireTime, Long groupMode, Long threshold, Long promotionId, Long openType, Long timeUnit, Long status) throws Exception {
        int times = 0;
        while (times < Constant.MAX_TIMES) {
            try {
                String serviceName = "com.aidc.ae.group.api.service.IGroupService";
                String serviceVersion = "1.0.0";
                String methodName = "updateGroupConfig";
                String groupId = "HSF";
                String[] parameterType = {"com.aidc.ae.group.api.request.GroupConfigRequest"};
                List<Object> paramList = new ArrayList<>();
                JSONObject req = new JSONObject();
                req.put("id", id);
                req.put("allowJoinSatisfy", allowJoinSatisfy);
                req.put("groupExpireTime", groupExpireTime);
                req.put("groupMode", groupMode);
                req.put("threshold", threshold);
                req.put("promotionId", promotionId);
                req.put("openType", openType);
                req.put("timeUnit", timeUnit);
                req.put("status", status);
                paramList.add(req.toJavaObject(Object.class));
                return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
            } catch (Exception e) {
                times++;
                try {
                    Thread.sleep(500);
                } catch (InterruptedException interruptedException) {
                    interruptedException.printStackTrace();
                }
                if (times >= Constant.MAX_TIMES) {
                    throw new RuntimeException(e);
                }
            }
        }
        return null;
    }

    public static JSONObject processToOpenSuccess(String groupCode) throws Exception {
        int times = 0;
        while (times < Constant.MAX_TIMES) {
            try {
                String serviceName = "com.aidc.ae.group.api.service.IGroupService";
                String serviceVersion = "1.0.0";
                String methodName = "processToOpenSuccess";
                String groupId = "HSF";
                String[] parameterType = {"java.lang.String"};
                List<Object> paramList = new ArrayList<>();
                paramList.add(groupCode);
                return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
            } catch (Exception e) {
                times++;
                try {
                    Thread.sleep(500);
                } catch (InterruptedException interruptedException) {
                    interruptedException.printStackTrace();
                }
                if (times >= Constant.MAX_TIMES) {
                    throw new RuntimeException(e);
                }
            }
        }
        return null;
    }

    public static JSONObject updateNonOrderOpenGroupInfo(Long cent, Long shareGroupId, String crownPicUrl, String currency) throws Exception {
        int times = 0;
        while (times < Constant.MAX_TIMES) {
            try {
                String serviceName = "com.aidc.ae.group.api.service.IGroupService";
                String serviceVersion = "1.0.0";
                String methodName = "updateNonOrderOpenGroupInfo";
                String groupId = "HSF";
                String[] parameterType = {"com.aidc.ae.group.api.request.NonOrderGroupInfoFeatureRequest"};
                List<Object> paramList = new ArrayList<>();
                JSONObject req = new JSONObject();
                req.put("cent", cent);
                req.put("groupId", shareGroupId);
                req.put("crownPicUrl", crownPicUrl);
                req.put("currency", currency);
                paramList.add(req.toJavaObject(Object.class));
                return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
            } catch (Exception e) {
                times++;
                try {
                    Thread.sleep(500);
                } catch (InterruptedException interruptedException) {
                    interruptedException.printStackTrace();
                }
                if (times >= Constant.MAX_TIMES) {
                    throw new RuntimeException(e);
                }
            }
        }
        return null;
    }

    public static JSONObject updateNonOrderOpenGroupInfoThreshold(Long joinSatisfy, Long shareGroupId, Long threshold) throws Exception {
        int times = 0;
        while (times < Constant.MAX_TIMES) {
            try {
                String serviceName = "com.aidc.ae.group.api.service.IGroupService";
                String serviceVersion = "1.0.0";
                String methodName = "updateNonOrderOpenGroupInfoThreshold";
                String groupId = "HSF";
                String[] parameterType = {"com.aidc.ae.group.api.request.NonOrderGroupInfoFeatureRequest"};
                List<Object> paramList = new ArrayList<>();
                JSONObject req = new JSONObject();
                req.put("joinSatisfy", joinSatisfy);
                req.put("groupId", shareGroupId);
                req.put("threshold", threshold);
                paramList.add(req.toJavaObject(Object.class));
                return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
            } catch (Exception e) {
                times++;
                try {
                    Thread.sleep(500);
                } catch (InterruptedException interruptedException) {
                    interruptedException.printStackTrace();
                }
                if (times >= Constant.MAX_TIMES) {
                    throw new RuntimeException(e);
                }
            }
        }
        return null;
    }

    public static JSONObject createPriceProtectionCompensate(JSONObject request) throws Exception {
        String serviceName = "com.alibaba.ae.reverse.server.api.facade.AePriceProtectionFacade";
        String serviceVersion = "1.0.0";
        String methodName = "createPriceProtectionCompensate";
        String groupId = "HSF";
        String[] parameterType = {"com.alibaba.ae.reverse.request.CreatePriceProtectionCompensateRequest"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(request.toJavaObject(Object.class));
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject triggerAudit(Long orderId, Boolean result, String type) throws Exception {
        String serviceName = "com.alibaba.xspace.intl.solution.ae.consult.cod.devops.CodDevOpsService";
        String serviceVersion = "1.0.0";
        String methodName = "triggerCodAuditFinish";
        String groupId = "HSF";
        String[] parameterType = {"java.lang.Long", "java.lang.Boolean", "java.lang.String"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(orderId);
        paramList.add(result);
        paramList.add(type);
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject queryTradeOrderById(Long orderId) throws Exception {
        String serviceName = "com.alibaba.global.order.management.api.facade.OrderQueryForBuyerFacade";
        String serviceVersion = "1.0.0";
        String methodName = "queryTradeOrderById";
        String groupId = "HSF";
        String[] parameterType = {"java.lang.Long"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(orderId);
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject initiateReverseRenderLocal(JSONObject initiateReverseRenderLocalRequest) throws Exception {
        String serviceName = "com.alibaba.ae.tradeReverse.api.AeBuyerRenderReverseOrderFacade";
        String serviceVersion = "1.0.0";
        String methodName = "initiateReverseRenderLocal";
        String groupId = "HSF";
        String[] parameterType = {"com.alibaba.ae.tradeReverse.model.request.InitiateReverseRenderLocalRequest"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(initiateReverseRenderLocalRequest.toJavaObject(Object.class));
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject queryReverseRefundInfo(JSONObject queryReverseRefundInfoRequest) throws Exception {
        String serviceName = "com.alibaba.ae.tradeReverse.api.InnerQueryReverseOrderFacade";
        String serviceVersion = "1.0.0";
        String methodName = "queryReverseRefundInfo";
        String groupId = "HSF";
        String[] parameterType = {"com.alibaba.ae.tradeReverse.model.request.RefundInfoRequest"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(queryReverseRefundInfoRequest.toJavaObject(Object.class));
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject triggerQtgQualityPunish(Long reverseOrderLineId) throws Exception {
        String serviceName = "com.alibaba.ae.service.open.facade.InternalToolsFacade";
        String serviceVersion = "1.0.0";
        String methodName = "triggerQtgQualityPunish";
        String groupId = "HSF";
        String[] parameterType = {"java.lang.Long"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(reverseOrderLineId);
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject queryInsuranceOrders4Trade(JSONObject insuranceOrderQuery) throws Exception {
        String serviceName = "com.alibaba.ae.care.insurance.client.service.om.facade.InsuranceOrderQueryFacade";
        String serviceVersion = "1.0.0";
        String methodName = "queryInsuranceOrders4Trade";
        String groupId = "HSF";
        String[] parameterType = {"com.alibaba.ae.care.insurance.client.service.om.request.InsuranceOrderQuery4TradeRequest"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(insuranceOrderQuery.toJavaObject(Object.class));
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject pageQueryInsuranceCaseOrders(JSONObject request) throws Exception {
        String serviceName = "com.alibaba.ae.care.insurance.client.service.om.facade.InsuranceOrderQueryFacade";
        String serviceVersion = "1.0.0";
        String methodName = "pageQueryInsuranceCaseOrders";
        String groupId = "HSF";
        String[] parameterType = {"com.alibaba.ae.care.insurance.client.service.om.request.InsuranceCaseOrderQueryRequest"};
        Pageable pageable = new Pageable();
        pageable.setPageNo(1);
        pageable.setPageSize(20);
        request.put("pageable", pageable);
        List<Object> paramList = new ArrayList<>();
        paramList.add(request.toJavaObject(Object.class));
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject triggerReverseTimeout(Long reverseOrderLineId, String timeoutType) throws Exception {
        String serviceName = "com.alibaba.ae.reverse.server.api.facade.InternalToolFacade";
        String serviceVersion = "1.0.0";
        String methodName = "triggerReverseTimeout";
        String groupId = "HSF";
        String[] parameterType = {"java.lang.Long", "java.lang.String"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(reverseOrderLineId);
        paramList.add(timeoutType);
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject fastInsure(Long tradeOrderId, Long buyerId) throws Exception {
        String serviceName = "com.alibaba.ae.care.insurance.client.script.facade.InsuranceScriptFacade";
        String serviceVersion = "1.0.0";
        String methodName = "fastInsure";
        String groupId = "HSF";
        String[] parameterType = {"com.alibaba.ae.care.insurance.client.script.request.FastInsureRequest"};
        JSONObject fastInsureRequest = new JSONObject();
        fastInsureRequest.put("tradeOrderId", tradeOrderId);
        fastInsureRequest.put("buyerId", buyerId);
        List<Object> paramList = new ArrayList<>();
        paramList.add(fastInsureRequest.toJavaObject(Object.class));
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject solutionReached(JSONObject solutionReached) throws Exception {
        String serviceName = "com.alibaba.reverse.platform.plus.server.api.AeReverseSolutionFacade";
        String serviceVersion = "1.0.0";
        String methodName = "solutionReached";
        String groupId = "HSF";
        String[] parameterType = {"com.alibaba.reverse.platform.plus.server.api.request.SolutionReachedReq"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(solutionReached);
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }


    public static JSONObject applyCompensate(JSONObject applyBreachCompensateParam) throws Exception {
        String serviceName = "com.alibaba.ae.service.open.facade.BreachContractFacade";
        String serviceVersion = "1.0.0";
        String methodName = "applyCompensate";
        String groupId = "HSF";
        String[] parameterType = {"com.alibaba.ae.service.open.param.ApplyBreachCompensateParam"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(applyBreachCompensateParam.toJavaObject(Object.class));
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject cainiaoSuggestAddress(Long buyerId, Long snapshotId, JSONObject suggestAddress) throws Exception {
        String serviceName = "com.alibaba.global.address.api.facade.UserAddressFixFacade";
        String serviceVersion = "1.0.0";
        String methodName = "cainiaoSuggestAddress";
        String groupId = "HSF";
        String[] parameterType = {"java.lang.Long", "java.lang.Long", "com.alibaba.global.address.api.request.SuggestAddressRequest"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(buyerId);
        paramList.add(snapshotId);
        paramList.add(suggestAddress);
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject collectAgain(Long buyerId, Long reverseOrderLineId) throws Exception {
        String serviceName = "com.alibaba.ae.reverse.server.api.facade.AeBuyerReverseOrderFacade";
        String serviceVersion = "1.0.0";
        String methodName = "collectAgain";
        String groupId = "HSF";
        String[] parameterType = {"com.alibaba.ae.reverse.server.api.request.CollectAgainRequest"};
        List<Object> paramList = new ArrayList<>();
        JSONObject req = new JSONObject();
        req.put("reverseOrderLineId", reverseOrderLineId);
        req.put("operatorId", buyerId);
        paramList.add(req.toJavaObject(Object.class));
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject uploadMail(JSONObject request) throws Exception {
        String serviceName = "com.alibaba.ae.reverse.server.api.facade.AeSellerReverseOrderFacade";
        String serviceVersion = "1.0.0";
        String methodName = "uploadMail";
        String groupId = "HSF";
        String[] parameterType = {"com.alibaba.ae.reverse.request.AeSellerUploadMailReq"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(request.toJavaObject(Object.class));
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject applyLogin(JSONObject request) throws Exception {
        String serviceName = "com.alibaba.havana.biz.api.login.AppApplyLoginService";
        String serviceVersion = "1.0.0.AE";
        String methodName = "applyLogin";
        String groupId = "HSF";
        String[] parameterType = {"com.alibaba.havana.biz.api.login.LoginRequestBase", "java.util.Map"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(request.toJavaObject(Object.class));
        paramList.add(new HashMap());
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject orderChangeReport(JSONObject request) throws Exception {
        String serviceName = "com.aidc.fulfill.gateway.client.service.PickUpOrderService";
        String serviceVersion = "1.0.0.aidc";
        String methodName = "orderChangeReport";
        String groupId = "HSF";
        String[] parameterType = {"com.aidc.fulfill.gateway.client.dto.PickUpFulfillReportDTO"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(request.toJavaObject(Object.class));
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONArray manualCreateSnapshot(List<Long> orderIdS) throws Exception {
        String serviceName = "com.aliexpress.snapshot.business.exposed.service.SnapshotService";
        String serviceVersion = "1.0.0";
        String methodName = "manualCreateSnapshotByOrderId";
        String groupId = "HSF";
        String[] parameterType = {"com.aliexpress.snapshot.business.inner.dto.CreateSnapshotOrderRequest"};
        List<Object> paramList = new ArrayList<>();
        JSONObject req = new JSONObject();
        req.put("orderIdS", orderIdS);
        req.put("buyerId", null);
        paramList.add(req.toJavaObject(Object.class));
        return genericServiceInvoke2Array(serviceName, serviceVersion, methodName, groupId, parameterType, paramList.toArray());
    }

    public static JSONArray releaseHoldOrder(Long buyerId, Long tradeOrderId, String deliveryMode, List<String> scenes) throws Exception {
        String serviceName = "com.alibaba.global.order.management.api.facade.OrderManagementRegionFacade";
        String serviceVersion = "1.0.0";
        String methodName = "releaseHoldOrder";
        String groupId = "HSF";
        String[] parameterType = {"com.alibaba.global.order.management.api.request.ReleaseHoldOrderRequest"};
        List<Object> paramList = new ArrayList<>();
        JSONObject req = new JSONObject();
        req.put("tradeOrderId", tradeOrderId);
        req.put("buyerId", buyerId);
        req.put("deliveryMode", deliveryMode);
        req.put("scenes", scenes);
        paramList.add(req.toJavaObject(Object.class));
        return genericServiceInvoke2Array(serviceName, serviceVersion, methodName, groupId, parameterType, paramList.toArray());
    }


    public static JSONObject reverseOrderLineRenderForSeller(JSONObject request) throws Exception {
        String serviceName = "com.alibaba.ae.tradeReverse.api.InnerQueryReverseOrderFacade";
        String serviceVersion = "1.0.0";
        String methodName = "reverseOrderLineRenderForSeller";
        String groupId = "HSF";
        String[] parameterType = {"com.alibaba.ae.tradeReverse.model.request.DetailRenderRequest"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(request.toJavaObject(Object.class));
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject handleBreachContract(JSONObject request) throws Exception {
        String serviceName = "com.alibaba.ae.service.open.facade.SellerBreachContractQueryFacade";
        String serviceVersion = "1.0.0";
        String methodName = "handleBreachContract";
        String groupId = "HSF";
        String[] parameterType = {"com.alibaba.ae.service.open.param.HandleBreachContractRequest"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(request.toJavaObject(Object.class));
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject querySellerStrategiesFromDb(JSONObject request) throws Exception {
        String serviceName = "com.alibaba.ae.reverse.api.AeAfterSaleStrategyFacade";
        String serviceVersion = "1.0.0";
        String methodName = "queryStrategiesFromDb";
        String groupId = "HSF";
        String[] parameterType = {"com.alibaba.ae.reverse.request.AeAfterSaleSellerStrategyQueryReq"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(request.toJavaObject(Object.class));
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject deleteSellerStrategies(JSONObject request) throws Exception {
        String serviceName = "com.alibaba.ae.reverse.api.AeAfterSaleStrategyFacade";
        String serviceVersion = "1.0.0";
        String methodName = "deleteStrategies";
        String groupId = "HSF";
        String[] parameterType = {"com.alibaba.ae.reverse.request.AeAfterSaleSellerStrategyCommandReq"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(request.toJavaObject(Object.class));
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject modifySellerStrategy(JSONObject request) throws Exception {
        String serviceName = "com.alibaba.ae.reverse.api.AeAfterSaleStrategyFacade";
        String serviceVersion = "1.0.0";
        String methodName = "modifyStrategies";
        String groupId = "HSF";
        String[] parameterType = {"com.alibaba.ae.reverse.request.AeAfterSaleSellerStrategyCommandReq"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(request.toJavaObject(Object.class));
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject queryBreachContractDetail(String tradeOrderLineId, Long sellerId, String scenario) throws Exception {
        String serviceName = "com.alibaba.ae.service.open.facade.BreachContractArbitrationFacade";
        String serviceVersion = "1.0.0";
        String methodName = "queryBreachContractDetail";
        String groupId = "HSF";
        String[] parameterType = {"com.alibaba.ae.service.open.param.CcoQueryBusinessDataParam"};
        JSONObject request = new JSONObject();
        request.put("responsibilityCode", scenario);
        request.put("sellerId", sellerId);
        request.put("targetId", tradeOrderLineId);
        List<Object> paramList = new ArrayList<>();
        paramList.add(request.toJavaObject(Object.class));
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject judgeBreachContract(JSONObject request) throws Exception {
        String serviceName = "com.alibaba.ae.service.open.facade.BreachContractArbitrationFacade";
        String serviceVersion = "1.0.0";
        String methodName = "judgeBreachContract";
        String groupId = "HSF";
        String[] parameterType = {"com.alibaba.ae.service.open.param.JudgeBreachContractParam"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(request.toJavaObject(Object.class));
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject calculatePenaltyType(JSONObject request) throws Exception {
        String serviceName = "com.alibaba.ae.service.open.facade.InternalToolsFacade";
        String serviceVersion = "1.0.0";
        String methodName = "calculatePenaltyType";
        String groupId = "HSF";
        String[] parameterType = {"com.alibaba.ae.service.open.param.MockBatchInitDataReq"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(request.toJavaObject(Object.class));
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject switchStrategy(JSONObject request) throws Exception {
        String serviceName = "com.alibaba.ae.reverse.api.AeAfterSaleStrategyService";
        String serviceVersion = "1.0.0";
        String methodName = "switchStrategy";
        String groupId = "HSF";
        String[] parameterType = {"com.alibaba.ae.reverse.request.AeAfterSaleStrategyRequest"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(request.toJavaObject(Object.class));
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject onEventByRegion(JSONObject request) throws Exception {
        String serviceName = "com.alibaba.global.uop.api.FulfillmentOrderStatusUpdateFacade";
        String serviceVersion = "1.0.0";
        String methodName = "onEventByRegion";
        String groupId = "HSF";
        String[] parameterType = {"java.lang.String", "com.alibaba.global.uop.api.request.FulfillmentOrderStatusUpdateRequest"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(request.toJavaObject(Object.class));
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject queryFulfillmentOrderByTradeId(Long buyerId, Long tradeOrderId) throws Exception {
        String serviceName = "com.alibaba.global.uop.api.FulfillmentOrderQueryFacade";
        String serviceVersion = "1.0.0";
        String methodName = "queryFulfillmentOrderByTradeId";
        String groupId = "HSF";
        String[] parameterType = {"java.lang.Long", "java.lang.Long", "com.ascp.uop.kernel.common.model.InvokeInfoDTO"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(buyerId);
        paramList.add(tradeOrderId);
        InvokeInfoDTO invokeInfoDTO = new InvokeInfoDTO();
        invokeInfoDTO.setAppName("ae-qa-trade-data-s");
        paramList.add(invokeInfoDTO);
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject queryNrInterceptRule(JSONObject request) throws Exception {
        String serviceName = "com.alibaba.ae.tradeReverse.api.AeBuyerRenderReverseOrderFacade";
        String serviceVersion = "1.0.0";
        String methodName = "queryNrInterceptRule";
        String groupId = "HSF";
        String[] parameterType = {"com.alibaba.ae.tradeReverse.model.request.NrInterceptRuleRequest"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(request.toJavaObject(Object.class));
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject applyBreachContracts(JSONObject request) throws Exception {
        String serviceName = "com.alibaba.ae.service.open.facade.BreachContractFacade";
        String serviceVersion = "1.0.0";
        String methodName = "applyBreachContracts";
        String groupId = "HSF";
        String[] parameterType = {"com.alibaba.ae.service.open.param.BatchApplyBreachContractsParam"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(request.toJavaObject(Object.class));
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject getStaticCashierPaymentMethod(Long buyerId) throws Exception {
        String serviceName = "com.alibaba.global.payment.api.facade.admin.PaymentOpsFacade";
        String serviceVersion = "1.0.0";
        String methodName = "getStaticCashierPaymentMethod";
        String groupId = "GLOBAL-AE";
        String[] parameterType = {"java.lang.Long"};
        List<Object> parameterValue = new ArrayList<>();
        parameterValue.add(buyerId);

        // modified genericServiceInvoke for array output
        HSFApiConsumerBean consumerBean = createConsumerBean(serviceName, serviceVersion, groupId);
        GenericService genericService = (GenericService) consumerBean.getObject();
        Object result = genericService.$invoke(methodName, parameterType, parameterValue.toArray());
        JSONObject res = new JSONObject();

        if (result != null) {
            JSONArray jsonArray = JSONArray.parseArray(String.valueOf(result));
            res.put("data", jsonArray);
        } else {
            res.put("data", new JSONArray());
        }

        return res;
    }

    public static JSONObject openFundAssetAccount(JSONObject request) throws Exception {
        String serviceName = "com.alibaba.aepay.facade.AeWalletFacade";
        String serviceVersion = "1.0.0";
        String methodName = "openFundAssetAccount";
        String groupId = "HSF";
        String[] parameterType = {"java.util.Map<java.lang.String, java.lang.Object>"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(request.toJavaObject(Object.class));
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject writeComplaintTicketsInfo(Long buyerId, Long tradeOrderId, String ticketId, int status, Long reverseOrderLineId, Long reverseOrderId) throws Exception {
        String serviceName = "com.alibaba.ae.reverse.server.api.facade.AeReverseOrderFacade";
        String serviceVersion = "1.0.0";
        String methodName = "writeComplaintTicketsInfo";
        String groupId = "HSF";
        String[] parameterType = {"com.alibaba.ae.reverse.server.api.request.ComplaintTicketsInfoWriteRequest"};
        List<Object> paramList = new ArrayList<>();
        JSONObject request = new JSONObject();
        request.put("buyerId", buyerId);
        request.put("tradeOrderId", tradeOrderId);
        request.put("reverseOrderId", reverseOrderId);
        JSONArray reverseOrderLineIds = new JSONArray();
        reverseOrderLineIds.add(reverseOrderLineId);
        request.put("reverseOrderLineIds", reverseOrderLineIds);
        JSONObject ticketInfo = new JSONObject();
        ticketInfo.put("id", StringUtils.isNotBlank(ticketId) ? ticketId : "2503804577582521");
        ticketInfo.put("status", status);
        request.put("ticketInfo", ticketInfo);
        paramList.add(request.toJavaObject(Object.class));
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());

    }

    public static JSONObject batchQueryQualifiedTime(JSONObject request) throws Exception {
        String serviceName = "com.alibaba.ae.service.open.facade.TimeoutCalculateFacade";
        String serviceVersion = "1.0.0";
        String methodName = "batchQueryQualifiedTime";
        String groupId = "HSF";
        String[] parameterType = {"com.alibaba.ae.service.open.param.QualifiedTimeRequest"};
        List<Object> paramList = new ArrayList<>();
        JSONArray array = new JSONArray();
        array.add(request);
        paramList.add(array);
        logger.info(JSONArray.toJSONString(paramList));
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject getAllSlsMsg4Self(JSONObject request) throws Exception {
        String serviceName = "com.aliexpress.qa.accurate.atmosphere.hsf.IAtmosphereService";
        String serviceVersion = "1.0.0";
        String methodName = "getAllSlsMsg4Self";
        String groupId = "HSF";
        String[] parameterType = {"com.aliexpress.qa.accurate.atmosphere.model.Sls4SelfReq"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(request.toJavaObject(Object.class));
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }

    public static JSONObject match(String identifier, String value) throws Exception {
        String serviceName = "com.alibaba.aidc.ipinfo.client.DataService";
        String serviceVersion = "1.0.0";
        String methodName = "match";
        String groupId = "HSF";
        String[] parameterType = {"java.lang.String","java.lang.String"};
        List<Object> paramList = new ArrayList<>();
        paramList.add(identifier);
        paramList.add(value);
        return genericServiceInvoke(serviceName, serviceVersion, methodName, groupId, null, parameterType, paramList.toArray());
    }
}
