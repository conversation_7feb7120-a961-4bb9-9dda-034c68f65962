package com.aliexpress.databank.utils;

import com.alibaba.common.lang.StringUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.global.money.Money;
import com.alibaba.global.order.management.api.model.PromotionFeeInfoDTO;
import com.alibaba.global.order.management.api.model.TradeOrderDTO;
import com.alibaba.global.order.management.api.model.TradeOrderLineDTO;
import com.aliexpress.databank.dataobject.TradeLineDynamicDTO;
import com.aliexpress.databank.dataobject.TradeLineFeatureDTO;
import com.aliexpress.databank.dataobject.TradeLinePriceDTO;
import com.aliexpress.databank.dataobject.TradePriceDTO;
import javax.money.MonetaryAmount;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class GetPrices {


    public static List<String> getOrdertotalPricekey() {
        //主单金额字段
        List<String> orderTotalPricekey = new ArrayList<>();
        orderTotalPricekey.add("tradeOrderId");
        orderTotalPricekey.add("orderStatus");
        orderTotalPricekey.add("payStatus");
        orderTotalPricekey.add("deliverStatus");
        orderTotalPricekey.add("saleOriginalFee");
        orderTotalPricekey.add("shippingActualFee");
        orderTotalPricekey.add("shippingDiscountFee");
        orderTotalPricekey.add("taxActualFee");
        orderTotalPricekey.add("taxRebateFee");
        orderTotalPricekey.add("saleDiscountFee");
        orderTotalPricekey.add("orderAmount");
        orderTotalPricekey.add("actualFee");
        orderTotalPricekey.add("actualFeeOfPurposeCurrency");
        orderTotalPricekey.add("payOrderDTO");
        orderTotalPricekey.add("otherOrderInfo");

        return orderTotalPricekey;

    }
    //子单金额字段
    public static List<String> getOrderPricekey() {
        List<String> orderPricekey = new ArrayList<>();
        orderPricekey.add("tradeOrderLineId");
        orderPricekey.add("payStatus");
        orderPricekey.add("deliveryStatus");
        orderPricekey.add("exchangeInfo");
        orderPricekey.add("unitFee");
        orderPricekey.add("quantity");
        orderPricekey.add("shippingActualFee");
        orderPricekey.add("shippingFee");
        orderPricekey.add("shippingDiscountFee");
        orderPricekey.add("taxActualFee");
        orderPricekey.add("taxFee");
        orderPricekey.add("taxRebateFee");
        orderPricekey.add("adjustFee");
        orderPricekey.add("saleDiscountFee");
        orderPricekey.add("promotionOrderDTO");
        orderPricekey.add("payableFee");
        orderPricekey.add("actualFee");
        orderPricekey.add("actualFeeOfPurposeCurrency");
        orderPricekey.add("paymentDiscountFee");
        orderPricekey.add("originalShippingDiscountInfo");
        orderPricekey.add("shippingDiscountInfo");
        orderPricekey.add("originalSaleDiscountInfo");
        orderPricekey.add("saleDiscountInfo");
        orderPricekey.add("buyer");
        return orderPricekey;

    }

    //子单feature金额字段
    public static List<String> getPdfPricekey() {
        List<String> pdfPricekey = new ArrayList<>();
        pdfPricekey.add("intent_pay_cur");
        pdfPricekey.add("intent_pay_rate");
        pdfPricekey.add("pdf");
        pdfPricekey.add("pcpdf");
        pdfPricekey.add("ppf");//支付优惠
        pdfPricekey.add("pcf");//支付手续费
        pdfPricekey.add("gaf");
        pdfPricekey.add("paf");
        pdfPricekey.add("gt");
        pdfPricekey.add("pft");
        pdfPricekey.add("cbImport");
        pdfPricekey.add("otherTaxDetail");
        pdfPricekey.add("tafc");
        pdfPricekey.add("br_ddp");
        pdfPricekey.add("t_tax_fee_ev");
        pdfPricekey.add("ddp_tax");
        pdfPricekey.add("include_tax");
        pdfPricekey.add("tax_detail");
        pdfPricekey.add("v_o_tax_detail");
        pdfPricekey.add("tax_time");
        pdfPricekey.add("tax_rate");
        pdfPricekey.add("doubleCounting");//双算标
        pdfPricekey.add("exchange_counting_df");//双算差值
        pdfPricekey.add("intention_price");//意向币种金额
        pdfPricekey.add("submitPaymentBatch");//支付流水
        pdfPricekey.add("proRetailPrice");//原价
        pdfPricekey.add("d_p");//量价
        pdfPricekey.add("v_p");//关联量价标
        pdfPricekey.add("r_v_p");//非量价-合并下单
        pdfPricekey.add("_dp_pcpdf");
        pdfPricekey.add("_dynamic_price");
        pdfPricekey.add("d_p_shipping_settle");
        pdfPricekey.add("o_p_shipping_settle");
        pdfPricekey.add("o_b_m");//全托管
        pdfPricekey.add("p_p_o_c");//原始成本
        pdfPricekey.add("p_p_a_c");//拆分后成本
        pdfPricekey.add("tax_rate");
        pdfPricekey.add("tax_time");
        pdfPricekey.add("already_taxed");
        pdfPricekey.add("hs_code");
        pdfPricekey.add("hscode_by_ewtp");
        pdfPricekey.add("featuresForTrade");
        pdfPricekey.add("wt");//仓类型
        pdfPricekey.add("wtn");
        pdfPricekey.add("subWarehouseType");
        pdfPricekey.add("freightCommitDay");
        pdfPricekey.add("logisticsHBATag");
        pdfPricekey.add("cross_border_warehouse_type");
        //pdfPricekey.add("_product_feature");
        //pdfPricekey.add("_sku_tag");
        pdfPricekey.add("promiseTemplate");

        return pdfPricekey;
    }

    //获取主单金额
    public static TradePriceDTO getTradePrice(TradeOrderDTO tradeOrderDTO) throws Exception{
        TradePriceDTO tradePriceDTO = new TradePriceDTO();
        tradePriceDTO.setTradeOrderId(tradeOrderDTO.getTradeOrderId());
        tradePriceDTO.setSaleOriginalFee(tradeOrderDTO.getSaleOriginalFee().toString());
        tradePriceDTO.setShippingActualFee(tradeOrderDTO.getShippingActualFee().toString());
        if (tradeOrderDTO.getShippingDiscountFee() != null) {
            tradePriceDTO.setShippingDiscountFee(tradeOrderDTO.getShippingDiscountFee().toString());
        }
        tradePriceDTO.setTaxActualFee(tradeOrderDTO.getTaxActualFee().toString());
        tradePriceDTO.setTaxRebateFee(tradeOrderDTO.getTaxRebateFee().toString());

        if (tradeOrderDTO.getSaleDiscountFee() != null) {
            tradePriceDTO.setSaleDiscountFee(tradeOrderDTO.getSaleDiscountFee().toString());
        }
        tradePriceDTO.setActualFee(tradeOrderDTO.getActualFee().toString());
        tradePriceDTO.setActualFeeOfPurposeCurrency(tradeOrderDTO.getActualFeeOfPurposeCurrency().toString());
        tradePriceDTO.setOrderAmount(tradeOrderDTO.getOrderAmount().toString());
        tradePriceDTO.setPayOrderDTO(tradeOrderDTO.getPayOrderDTO());
        tradePriceDTO.setOrderStatus(tradeOrderDTO.getOrderStatus());
        tradePriceDTO.setPayStatus(tradeOrderDTO.getPayStatus());
        tradePriceDTO.setDeliverStatus(tradeOrderDTO.getDeliverStatus());
        tradePriceDTO.setSearchStatus(tradeOrderDTO.getSearchStatus());
        tradePriceDTO.setOtherOrderInfo(tradeOrderDTO.getOtherOrderInfo());

        tradePriceDTO.setTradeLinePriceDTO(getTradeLinePriceList(tradeOrderDTO.getOrderLines()));

        return tradePriceDTO;

    }

    //获取所有子单金额
    public static List<TradeLinePriceDTO> getTradeLinePriceList(List<TradeOrderLineDTO> tradeOrderLineDTOs) throws Exception{

        List<TradeLinePriceDTO> tradeLinePriceDTOS = new ArrayList<>();
        for (TradeOrderLineDTO tradeOrderLineDTO : tradeOrderLineDTOs) {
            TradeLinePriceDTO tradeLinePriceDTO = getTradeLinePrice(tradeOrderLineDTO);
            tradeLinePriceDTOS.add(tradeLinePriceDTO);
        }
        return tradeLinePriceDTOS;

    }

    public static TradeLinePriceDTO getTradeLinePrice(TradeOrderLineDTO tradeOrderLineDTO) throws Exception{

        TradeLinePriceDTO tradeLinePriceDTO = new TradeLinePriceDTO();
        tradeLinePriceDTO.setTradeOrderId(tradeOrderLineDTO.getTradeOrderId());
        tradeLinePriceDTO.setTradeOrderLineId(tradeOrderLineDTO.getTradeOrderLineId());
        tradeLinePriceDTO.setExchangeInfo(tradeOrderLineDTO.getExchangeInfo());
        tradeLinePriceDTO.setUnitFee(tradeOrderLineDTO.getUnitFee().toString());
        tradeLinePriceDTO.setQuantity(tradeOrderLineDTO.getQuantity());
        if (tradeOrderLineDTO.getShippingActualFee() != null) {
            tradeLinePriceDTO.setShippingActualFee(tradeOrderLineDTO.getShippingActualFee().toString());

        }
        tradeLinePriceDTO.setShippingFee(tradeOrderLineDTO.getShippingFee().toString());
        if (tradeOrderLineDTO.getShippingDiscountFee() != null) {
            tradeLinePriceDTO.setShippingDiscountFee(tradeOrderLineDTO.getShippingDiscountFee().toString());
        }
        if (tradeOrderLineDTO.getShippingDiscountInfo() != null) {
            tradeLinePriceDTO.setShippingDiscountInfo(tradeOrderLineDTO.getShippingDiscountInfo());
        }
        if (tradeOrderLineDTO.getSaleDiscountFee() != null) {
            tradeLinePriceDTO.setSaleDiscountFee(tradeOrderLineDTO.getSaleDiscountFee().toString());
        }

        tradeLinePriceDTO.setOriginalSaleDiscountInfo(tradeOrderLineDTO.getOriginalSaleDiscountInfo());
        tradeLinePriceDTO.setSaleDiscountInfo(tradeOrderLineDTO.getSaleDiscountInfo());
        tradeLinePriceDTO.setTaxFee(tradeOrderLineDTO.getTaxFee().toString());
        tradeLinePriceDTO.setTaxActualFee(tradeOrderLineDTO.getTaxActualFee().toString());
        tradeLinePriceDTO.setActualFee(tradeOrderLineDTO.getActualFee().toString());
        tradeLinePriceDTO.setTaxRebateFee(tradeOrderLineDTO.getTaxRebateFee().toString());
        tradeLinePriceDTO.setActualFeeOfPurposeCurrency(tradeOrderLineDTO.getActualFeeOfPurposeCurrency().toString());
        tradeLinePriceDTO.setPayableFee(tradeOrderLineDTO.getPayableFee().toString());
        if (tradeOrderLineDTO.getAdjustFee() != null) {
            tradeLinePriceDTO.setAdjustFee(tradeOrderLineDTO.getAdjustFee().toString());
        }
        if (tradeOrderLineDTO.getPaymentDiscountFee() != null) {
            tradeLinePriceDTO.setPaymentDiscountFee(tradeOrderLineDTO.getPaymentDiscountFee().toString());

        }
        tradeLinePriceDTO.setPromotionOrderDTO(tradeOrderLineDTO.getPromotionOrderDTO());
        tradeLinePriceDTO.setPayStatus(tradeOrderLineDTO.getPayStatus());
        tradeLinePriceDTO.setDeliveryStatus(tradeOrderLineDTO.getDeliveryStatus());
        tradeLinePriceDTO.setEndReason(tradeOrderLineDTO.getEndReason());
        tradeLinePriceDTO.setBuyer(tradeOrderLineDTO.getBuyer());
        tradeLinePriceDTO.setSeller(tradeOrderLineDTO.getSeller());
        tradeLinePriceDTO.setTradeLineFeatureDTO(getOrderLineFeatures(tradeOrderLineDTO));
        tradeLinePriceDTO.setVolumePrice(tradeOrderLineDTO.getVolumePrice());

        // 解析交易优惠
        MonetaryAmount nonGoldStandardDiscountFee = Money.zero(tradeOrderLineDTO.getSaleOriginalFee().getCurrency());
        MonetaryAmount goldStandardDiscountFee = Money.zero(tradeOrderLineDTO.getSaleOriginalFee().getCurrency());

        MonetaryAmount payNonGoldStandardDiscountFee = Money.zero(tradeOrderLineDTO.getActualFeeOfPurposeCurrency().getCurrency());
        MonetaryAmount payGoldStandardDiscountFee = Money.zero(tradeOrderLineDTO.getActualFeeOfPurposeCurrency().getCurrency());

        if (tradeOrderLineDTO.getOriginalSaleDiscountInfo() != null) {
            for (PromotionFeeInfoDTO promotionFeeInfoDTO : tradeOrderLineDTO.getOriginalSaleDiscountInfo()) {
                nonGoldStandardDiscountFee = nonGoldStandardDiscountFee.add(promotionFeeInfoDTO.getNonGoldStandardDiscountFee());
                goldStandardDiscountFee=goldStandardDiscountFee.add(promotionFeeInfoDTO.getGoldStandardDiscountFee());
            }
        }


        if (tradeOrderLineDTO.getSaleDiscountInfo() != null) {

            for (PromotionFeeInfoDTO promotionFeeInfoDTO : tradeOrderLineDTO.getSaleDiscountInfo()) {
                if (promotionFeeInfoDTO.getNonGoldStandardDiscountFee() != null) {
                    payNonGoldStandardDiscountFee = payNonGoldStandardDiscountFee.add(promotionFeeInfoDTO.getNonGoldStandardDiscountFee());
                }

                if (promotionFeeInfoDTO.getGoldStandardDiscountFee() != null) {
                    payGoldStandardDiscountFee = payGoldStandardDiscountFee.add(promotionFeeInfoDTO.getGoldStandardDiscountFee());

                }
            }

        }

        tradeLinePriceDTO.setNonGoldStandardDiscountFee(nonGoldStandardDiscountFee);
        tradeLinePriceDTO.setGoldStandardDiscountFee(goldStandardDiscountFee);
        tradeLinePriceDTO.setPayNonGoldStandardDiscountFee(payNonGoldStandardDiscountFee);
        tradeLinePriceDTO.setPayGoldStandardDiscountFee(payGoldStandardDiscountFee);

        // 解析运费优惠
        MonetaryAmount nonGoldShippingDiscountFee = Money.zero(tradeOrderLineDTO.getSaleOriginalFee().getCurrency());
        MonetaryAmount goldShippingDiscountFee = Money.zero(tradeOrderLineDTO.getSaleOriginalFee().getCurrency());

        MonetaryAmount payNonGoldShippingDiscountFee  = Money.zero(tradeOrderLineDTO.getActualFeeOfPurposeCurrency().getCurrency());
        MonetaryAmount payGoldShippingDiscountFee  = Money.zero(tradeOrderLineDTO.getActualFeeOfPurposeCurrency().getCurrency());

        if(tradeOrderLineDTO.getOriginalShippingDiscountInfo()!=null){

            for (PromotionFeeInfoDTO promotionFeeInfoDTO : tradeOrderLineDTO.getOriginalShippingDiscountInfo()) {
                if (promotionFeeInfoDTO.getNonGoldStandardDiscountFee() != null) {
                    nonGoldShippingDiscountFee = nonGoldShippingDiscountFee.add(promotionFeeInfoDTO.getNonGoldStandardDiscountFee());
                }
                if (promotionFeeInfoDTO.getGoldStandardDiscountFee() != null) {
                    goldShippingDiscountFee = goldShippingDiscountFee.add(promotionFeeInfoDTO.getGoldStandardDiscountFee());
                }
            }

        }

        if(tradeLinePriceDTO.getShippingDiscountInfo()!=null){
            for (PromotionFeeInfoDTO promotionFeeInfoDTO : tradeOrderLineDTO.getShippingDiscountInfo()) {
                if (promotionFeeInfoDTO.getNonGoldStandardDiscountFee() != null) {
                    payNonGoldShippingDiscountFee = payNonGoldShippingDiscountFee.add(promotionFeeInfoDTO.getNonGoldStandardDiscountFee());
                }

                if (promotionFeeInfoDTO.getGoldStandardDiscountFee() != null) {
                    payGoldShippingDiscountFee = payGoldShippingDiscountFee.add(promotionFeeInfoDTO.getGoldStandardDiscountFee());

                }
            }

        }

        tradeLinePriceDTO.setNonGoldShippingDiscountFee(nonGoldShippingDiscountFee);
        tradeLinePriceDTO.setGoldShippingDiscountFee(goldShippingDiscountFee);
        tradeLinePriceDTO.setPayNonGoldShippingDiscountFee(payNonGoldShippingDiscountFee);
        tradeLinePriceDTO.setPayGoldShippingDiscountFee(payGoldShippingDiscountFee);


        if(StringUtil.isNotBlank(tradeLinePriceDTO.getTradeLineFeatureDTO().getD_p()) && tradeLinePriceDTO.getTradeLineFeatureDTO().getD_p().equals("1")){
            tradeLinePriceDTO.setTradeLineDynamicDTO(getTradeLineDynamicDTO(tradeOrderLineDTO));
        }

//        IntentionPriceDTO tradeIntentionPriceDTO = tradeOrderLineDTO.getIntentionPriceDTO();
//        tradeLinePriceDTO.setTradeIntentionPriceDTO(tradeIntentionPriceDTO);

        return tradeLinePriceDTO;
    }


    public static TradeLineFeatureDTO getOrderLineFeatures(TradeOrderLineDTO tradeOrderLineDTO) throws Exception{
        TradeLineFeatureDTO tradeLineFeature = new TradeLineFeatureDTO();
        Map<String, String> orderLineFeature = tradeOrderLineDTO.getFeatures().getFeatureMap();
        tradeLineFeature.setTradeOrderLineId(tradeOrderLineDTO.getTradeOrderLineId());
        tradeLineFeature.setIntent_pay_cur(orderLineFeature.get("intent_pay_cur"));
        tradeLineFeature.setIntent_pay_rate(BigDecimal.valueOf(Double.parseDouble(orderLineFeature.get("intent_pay_rate"))));
        if (StringUtil.isNotBlank(orderLineFeature.get("pdf"))) {
            JSONObject pdf = JSONObject.parseObject(orderLineFeature.get("pdf"));
            tradeLineFeature.setPdf(pdf);
        }

        if (StringUtil.isNotBlank(orderLineFeature.get("pcpdf"))) {
            List<JSONObject> pcpdf = JSONObject.parseArray(orderLineFeature.get("pcpdf"), JSONObject.class);
            tradeLineFeature.setPcpdf(pcpdf);
        }


        tradeLineFeature.setPpf(orderLineFeature.get("ppf"));
        tradeLineFeature.setPcf(orderLineFeature.get("pcf"));
        tradeLineFeature.setCbImport(orderLineFeature.get("cbImport"));
        if (StringUtil.isNotBlank(orderLineFeature.get("gaf"))) {
            tradeLineFeature.setGaf(orderLineFeature.get("gaf"));
        }

        if (StringUtil.isNotBlank(orderLineFeature.get("paf"))) {
            tradeLineFeature.setPaf(orderLineFeature.get("paf"));
        }

        if (StringUtil.isNotBlank(orderLineFeature.get("gt"))) {
            tradeLineFeature.setGt(orderLineFeature.get("gt"));
        }
        tradeLineFeature.setPft(orderLineFeature.get("pft"));
        tradeLineFeature.setInclude_tax(orderLineFeature.get("include_tax"));

        if(StringUtil.isNotBlank(orderLineFeature.get("tax_detail"))){
            List<JSONObject> taxDetails = JSONObject.parseArray(orderLineFeature.get("tax_detail"), JSONObject.class);
            tradeLineFeature.setTax_detail(taxDetails);
        }
        if(StringUtil.isNotBlank(orderLineFeature.get("v_o_tax_detail"))){
            List<JSONObject> v_o_tax_detail = JSONObject.parseArray(orderLineFeature.get("v_o_tax_detail"), JSONObject.class);
            tradeLineFeature.setV_o_tax_detail(v_o_tax_detail);
        }



        if(StringUtil.isNotBlank(orderLineFeature.get("_dynamic_price"))){
            JSONObject _dynamic_price = JSONObject.parseObject(orderLineFeature.get("_dynamic_price"));
            tradeLineFeature.set_dynamic_price(_dynamic_price);
        }
        tradeLineFeature.setProRetailPrice(Money.of(orderLineFeature.get("proRetailPrice")));
        tradeLineFeature.setD_p(orderLineFeature.get("d_p"));
        tradeLineFeature.setV_P(orderLineFeature.get("v_p"));
        tradeLineFeature.setR_v_p(orderLineFeature.get("r_v_p"));
        if(StringUtil.isNotBlank(orderLineFeature.get("_dp_pcpdf"))){
            List<JSONObject> _dp_pcpdf = JSONObject.parseArray(orderLineFeature.get("_dp_pcpdf"), JSONObject.class);
            tradeLineFeature.set_dp_pcpdf(_dp_pcpdf);
        }

        tradeLineFeature.setD_p_shipping_settle(orderLineFeature.get("d_p_shipping_settle"));
        tradeLineFeature.setO_p_shipping_settle(orderLineFeature.get("o_p_shipping_settle"));
        tradeLineFeature.set_product_feature(JSONObject.parseObject(orderLineFeature.get("_product_feature")));

        if(StringUtil.isNotBlank(orderLineFeature.get("_sku_tag"))){
            String _sku_tag = orderLineFeature.get("_sku_tag");
            tradeLineFeature.set_sku_tag(_sku_tag);
        }

        if(StringUtil.isNotBlank(orderLineFeature.get("promiseTemplate"))){
            String promiseTemplate = orderLineFeature.get("promiseTemplate");
            tradeLineFeature.setPromiseTemplate(promiseTemplate);
        }

        if(StringUtil.isNotBlank(orderLineFeature.get("intention_price"))){
            JSONObject intention_price = JSONObject.parseObject(orderLineFeature.get("intention_price"));
            tradeLineFeature.setIntention_price(intention_price);
        }

        return  tradeLineFeature;

    }

    private static TradeLineDynamicDTO getTradeLineDynamicDTO(TradeOrderLineDTO tradeOrderLineDTO) {

        TradeLineDynamicDTO tradeLineDynamicDTO = new TradeLineDynamicDTO();
        tradeLineDynamicDTO.setTradeOrderLineId(tradeOrderLineDTO.getTradeOrderLineId());
        JSONObject _dynamic_price = JSONObject.parseObject(tradeOrderLineDTO.getFeatures().getFeatureMap().get("_dynamic_price"));

        if (_dynamic_price.getJSONObject("payCurrDiscountFee")!=null && StringUtil.isNotBlank(_dynamic_price.getJSONObject("payCurrDiscountFee").toJSONString())) {
            JSONObject dynamic_payCurrDiscountFee = _dynamic_price.getJSONObject("payCurrDiscountFee");
            tradeLineDynamicDTO.setPayCurrDiscountFee(dynamic_payCurrDiscountFee);

            MonetaryAmount pay_ItemGoldStandardFee = Money.ofMinorUnit(dynamic_payCurrDiscountFee.getJSONObject("itemGoldStandardFee").getString("currencyCode"), dynamic_payCurrDiscountFee.getJSONObject("itemGoldStandardFee").getLongValue("amount"));
            tradeLineDynamicDTO.setPay_itemGoldStandardFee(pay_ItemGoldStandardFee);

            MonetaryAmount pay_ItemNoGoldStandardFee = Money.ofMinorUnit(dynamic_payCurrDiscountFee.getJSONObject("itemNonGoldStandardFee").getString("currencyCode"), dynamic_payCurrDiscountFee.getJSONObject("itemNonGoldStandardFee").getLongValue("amount"));
            tradeLineDynamicDTO.setPay_itemNonGoldStandardFee(pay_ItemNoGoldStandardFee);

            MonetaryAmount pay_ItemSingleDiscountFee = Money.ofMinorUnit(dynamic_payCurrDiscountFee.getJSONObject("itemSingleDiscountFee").getString("currencyCode"), dynamic_payCurrDiscountFee.getJSONObject("itemSingleDiscountFee").getLongValue("amount"));
            tradeLineDynamicDTO.setPay_itemSingleDiscountFee(pay_ItemSingleDiscountFee);

            MonetaryAmount pay_shippingNonGoldStandardFee = Money.ofMinorUnit(dynamic_payCurrDiscountFee.getJSONObject("shippingNonGoldStandardFee").getString("currencyCode"), dynamic_payCurrDiscountFee.getJSONObject("shippingNonGoldStandardFee").getLongValue("amount"));
            tradeLineDynamicDTO.setPay_shippingNonGoldStandardFee(pay_shippingNonGoldStandardFee);

            MonetaryAmount pay_shippingGoldStandardFee = Money.ofMinorUnit(dynamic_payCurrDiscountFee.getJSONObject("shippingGoldStandardFee").getString("currencyCode"), dynamic_payCurrDiscountFee.getJSONObject("shippingGoldStandardFee").getLongValue("amount"));
            tradeLineDynamicDTO.setPay_shippingGoldStandardFee(pay_shippingGoldStandardFee);



        }

        MonetaryAmount shippingDiscountFee = JSONObject.parseObject(_dynamic_price.getJSONObject("shippingDiscountFee").toJSONString(), MonetaryAmount.class);
        tradeLineDynamicDTO.setShippingDiscountFee(shippingDiscountFee);
        MonetaryAmount originalFee = JSONObject.parseObject(_dynamic_price.getJSONObject("originalFee").toJSONString(), MonetaryAmount.class);
        tradeLineDynamicDTO.setOriginalFee(originalFee);
        MonetaryAmount payableFee = JSONObject.parseObject(_dynamic_price.getJSONObject("payableFee").toJSONString(),  MonetaryAmount.class);
        tradeLineDynamicDTO.setPayableFee(payableFee);
        MonetaryAmount adjustFee = JSONObject.parseObject(_dynamic_price.getJSONObject("adjustFee").toJSONString(),  MonetaryAmount.class);
        tradeLineDynamicDTO.setAdjustFee(adjustFee);

        MonetaryAmount actualFee = JSONObject.parseObject(_dynamic_price.getJSONObject("actualFee").toJSONString(),  MonetaryAmount.class);
        tradeLineDynamicDTO.setActualFee(actualFee);

        //discountFee
        if(StringUtil.isNotBlank(_dynamic_price.getJSONObject("discountFee").toJSONString())){
            JSONObject discountFee = _dynamic_price.getJSONObject("discountFee");
            tradeLineDynamicDTO.setDiscountFee(discountFee);
            MonetaryAmount itemSingleDiscountFee = Money.ofMinorUnit(discountFee.getJSONObject("itemSingleDiscountFee").getString("currencyCode"), discountFee.getJSONObject("itemSingleDiscountFee").getLongValue("amount"));
            tradeLineDynamicDTO.setItemSingleDiscountFee(itemSingleDiscountFee);
            MonetaryAmount itemNonGoldStandardFee = Money.ofMinorUnit(discountFee.getJSONObject("itemNonGoldStandardFee").getString("currencyCode"), discountFee.getJSONObject("itemNonGoldStandardFee").getLongValue("amount"));
            tradeLineDynamicDTO.setItemNonGoldStandardFee(itemNonGoldStandardFee);
            MonetaryAmount itemGoldStandardFee = Money.ofMinorUnit(discountFee.getJSONObject("itemGoldStandardFee").getString("currencyCode"), discountFee.getJSONObject("itemGoldStandardFee").getLongValue("amount"));
            tradeLineDynamicDTO.setItemGoldStandardFee(itemGoldStandardFee);

            MonetaryAmount shippingGoldStandardFee= Money.ofMinorUnit(discountFee.getJSONObject("shippingGoldStandardFee").getString("currencyCode"), discountFee.getJSONObject("shippingGoldStandardFee").getLongValue("amount"));
            tradeLineDynamicDTO.setShippingGoldStandardFee(shippingGoldStandardFee);

            MonetaryAmount shippingNonGoldStandardFee= Money.ofMinorUnit(discountFee.getJSONObject("shippingNonGoldStandardFee").getString("currencyCode"), discountFee.getJSONObject("shippingNonGoldStandardFee").getLongValue("amount"));
            tradeLineDynamicDTO.setShippingNonGoldStandardFee(shippingNonGoldStandardFee);

        }


        if (StringUtil.isNotBlank(_dynamic_price.getJSONArray("promotionFeeDetails").toJSONString())) {
            List<JSONObject>  promotionFeeDetails = JSONObject.parseArray(_dynamic_price.get("promotionFeeDetails").toString(),JSONObject.class);
            tradeLineDynamicDTO.setPromotionFeeDetails(promotionFeeDetails);


        }
        tradeLineDynamicDTO.setUnitFee(tradeOrderLineDTO.getVolumePrice().getUnitFee());
        tradeLineDynamicDTO.setOriUnitFee(tradeOrderLineDTO.getVolumePrice().getOriUnitFee());
        tradeLineDynamicDTO.setFeatures(tradeOrderLineDTO.getVolumePrice().getFeatures().getFeatureMap());

        return tradeLineDynamicDTO;

    }

//    public static List<IntentionPriceDTO> getIntentionPrice(TradePriceDTO tradePriceDTO) {
//        List<IntentionPriceDTO> list = new ArrayList<>();
//
//        for (TradeLinePriceDTO tradeLinePriceDTO : tradePriceDTO.getTradeLinePriceDTO()) {
//
//            list.add(tradeLinePriceDTO.getTradeIntentionPriceDTO());
//
//        }
//        return list;
//    }
}
