package com.aliexpress.databank.utils;

import com.alibaba.common.lang.StringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.aliexpress.databank.constant.ThubScenesEnum;
import com.aliexpress.databank.dataobject.ReverseOrderLineDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;

public class ThubAdaptationUtils {
    private static final Logger logger = LoggerFactory.getLogger(ThubAdaptationUtils.class);


    // 查询逆向单信息
    public static ReverseOrderLineDTO getLastReverseOrderByTradeOrderLineId(Long buyerId, Long tradeOrderLineId) throws Exception {
        //根据子单id查询逆向单
        JSONObject reverseOrderLine = HsfUtil.getLastReverseOrderByTradeOrderLineId(buyerId, tradeOrderLineId);
        JSONObject lastReverseOrder = reverseOrderLine.getJSONObject("result");
        if (lastReverseOrder == null) {
            return null;
        }
        Integer reverseStatus = lastReverseOrder.getInteger("reverseStatus");
        if (3 == reverseStatus) {
            return null;
        }
        ReverseOrderLineDTO lastReverseOrderDTO = new ReverseOrderLineDTO();
        lastReverseOrderDTO.setReverseOrderId(lastReverseOrder.getLong("reverseOrderId"));
        lastReverseOrderDTO.setReverseOrderLineId(lastReverseOrder.getLong("reverseOrderLineId"));
        lastReverseOrderDTO.setTradeOrderId(lastReverseOrder.getLong("tradeOrderId"));
        lastReverseOrderDTO.setTradeOrderLineId(lastReverseOrder.getLong("tradeOrderLineId"));
        lastReverseOrderDTO.setReverseStatus(reverseStatus);
        lastReverseOrderDTO.setReverseType(lastReverseOrder.getInteger("reverseType"));
        lastReverseOrderDTO.setBuyerId(lastReverseOrder.getLong("buyerId"));
        Map maps = (Map) JSON.parse(lastReverseOrder.getString("features"));
        lastReverseOrderDTO.setFeatures(maps);
        lastReverseOrderDTO.setSellerId(lastReverseOrder.getLong("sellerId"));
        lastReverseOrderDTO.setDisputeStatus(lastReverseOrder.getString("disputeStatus"));
        // 没直接转是因为有特殊字符 转不了
        //  JSON.parseObject(lastReverseOrder.toJSONString(), ReverseOrderLineDTO.class);
        return lastReverseOrderDTO;
    }

    // 当前逆向单是否完结
    public static boolean isReverseOrderFinished(Long buyerId, Long tradeOrderLineId) throws Exception {
        ReverseOrderLineDTO reverseOrderLine = getLastReverseOrderByTradeOrderLineId(buyerId, tradeOrderLineId);
        if (reverseOrderLine == null) {
            return true;
        }
        return 10 == reverseOrderLine.getReverseStatus();
    }

    // 获取物流商
    public static String getLogisticsServiceProvider(Long buyerId, Long tradeOrderLineId) throws Exception {
        ReverseOrderLineDTO reverseOrderLine = getLastReverseOrderByTradeOrderLineId(buyerId, tradeOrderLineId);
        if (reverseOrderLine == null) {
            return null;
        }
        Map<String, String> features = reverseOrderLine.getFeatures();
        return features.get("f_l_p_c");
    }

    // 是否自寄
    public static boolean isSelfDropOff(Long buyerId, Long tradeOrderLineId) throws Exception {
        ReverseOrderLineDTO reverseOrderLine = getLastReverseOrderByTradeOrderLineId(buyerId, tradeOrderLineId);
        if (reverseOrderLine == null) {
            return true;
        }
        Map<String, String> features = reverseOrderLine.getFeatures();
        String returnWay = features.get("return_way");
        return "self_drop-off".equals(returnWay);
    }

    // 是否上门揽件
    public static boolean isVisitCollect(Long buyerId, Long tradeOrderLineId) throws Exception {
        ReverseOrderLineDTO reverseOrderLine = getLastReverseOrderByTradeOrderLineId(buyerId, tradeOrderLineId);
        if (reverseOrderLine == null) {
            return true;
        }
        Map<String, String> features = reverseOrderLine.getFeatures();
        String returnWay = features.get("return_way");
        return "visit_collect".equals(returnWay);
    }


    // 是否商家仓
    public static boolean isSellerAddress(Long buyerId, Long tradeOrderLineId) throws Exception {
        ReverseOrderLineDTO reverseOrderLine = getLastReverseOrderByTradeOrderLineId(buyerId, tradeOrderLineId);
        if (reverseOrderLine == null) {
            return true;
        }
        Map<String, String> features = reverseOrderLine.getFeatures();
        String f_r_a_t = features.get("f_r_a_t");
        return "SELLER_ADDRESS".equals(f_r_a_t);
    }

    // 仓类型
    public static String addressType(Long buyerId, Long tradeOrderLineId) throws Exception {
        ReverseOrderLineDTO reverseOrderLine = getLastReverseOrderByTradeOrderLineId(buyerId, tradeOrderLineId);
        if (reverseOrderLine == null) {
            return null;
        }
        Map<String, String> features = reverseOrderLine.getFeatures();
        return features.get("f_r_a_t");
    }

    // 本地退or无忧退
    public static String getAeBusinessType(Long buyerId, Long tradeOrderLineId) throws Exception {
        ReverseOrderLineDTO reverseOrderLine = getLastReverseOrderByTradeOrderLineId(buyerId, tradeOrderLineId);
        if (reverseOrderLine == null) {
            return null;
        }
        Map<String, String> features = reverseOrderLine.getFeatures();
        return features.get("ae_business_type");
    }

    // 是否无忧退
    public static boolean isInsuranceReturn(Long buyerId, Long tradeOrderLineId) throws Exception {
        ReverseOrderLineDTO reverseOrderLine = getLastReverseOrderByTradeOrderLineId(buyerId, tradeOrderLineId);
        Map<String, String> features = reverseOrderLine.getFeatures();
        String aeBusinessType = features.get("ae_business_type");
        return "insurance_return".equals(aeBusinessType);
    }


    // 是否上升仲裁
    public static boolean isCreateArbitration(Long buyerId, Long tradeOrderLineId) throws Exception {
        ReverseOrderLineDTO reverseOrderLine = getLastReverseOrderByTradeOrderLineId(buyerId, tradeOrderLineId);
        Map<String, String> features = reverseOrderLine.getFeatures();
        String activity = features.get("activity");

        return "CreateArbitration".equals(activity);
    }

    // 仓库是否收到货
    public static boolean isWarehouseReceived(Long buyerId, Long tradeOrderLineId) throws Exception {
        ReverseOrderLineDTO reverseOrderLine = getLastReverseOrderByTradeOrderLineId(buyerId, tradeOrderLineId);
        Map<String, String> features = reverseOrderLine.getFeatures();
        String activity = features.get("activity");

        return "WarehouseReceived".equals(activity);
    }

    // 是否等待二揽
    public static boolean isReCollectConfirm(Long buyerId, Long tradeOrderLineId) throws Exception {
        ReverseOrderLineDTO reverseOrderLine = getLastReverseOrderByTradeOrderLineId(buyerId, tradeOrderLineId);
        Map<String, String> features = reverseOrderLine.getFeatures();
        String activity = features.get("activity");

        return "ReCollectConfirm".equals(activity);
    }

    // 是否免质检
    public static boolean isNoQcFee(Long buyerId, Long tradeOrderLineId) throws Exception {
        ReverseOrderLineDTO reverseOrderLine = getLastReverseOrderByTradeOrderLineId(buyerId, tradeOrderLineId);
        Map<String, String> features = reverseOrderLine.getFeatures();
        String noQcFee = features.get("no_qc_fee");
        return "true".equals(noQcFee);
    }

    // 商配
    public static boolean isInformSellerUploadMail(Long buyerId, Long tradeOrderLineId) throws Exception {
        ReverseOrderLineDTO reverseOrderLine = getLastReverseOrderByTradeOrderLineId(buyerId, tradeOrderLineId);
        Map<String, String> features = reverseOrderLine.getFeatures();
        String activity = features.get("activity");
        return "InformSellerUploadMail".equals(activity);
    }

    // 当前执行节点是否失效
    public static boolean isInvalid(Long buyerId, Long tradeOrderLineId, String scenes) throws Exception {
        ReverseOrderLineDTO reverseOrderLine = getLastReverseOrderByTradeOrderLineId(buyerId, tradeOrderLineId);
        if (reverseOrderLine == null) {
            return true;
        }

        Map<String, String> features = reverseOrderLine.getFeatures();
        String f_o_s = features.get("f_o_s");
        String activity = features.get("activity");
        // 仓前仲裁 直接跳过
        if (StringUtil.isEmpty(f_o_s)) {
            return false;
        }

        if ("CalculateProduct".equals(activity)) {
            return true;
        }

        String fulfillmentStatus = ThubScenesEnum.getFulfillmentStatusByScenes(scenes);
        if (StringUtil.isEmpty(fulfillmentStatus)) {
            return true;
        }
        return Math.abs(Long.parseLong(f_o_s)) > Math.abs(Long.parseLong(fulfillmentStatus));
    }
}
