package com.aliexpress.databank.utils;

import com.alibaba.common.lang.StringUtil;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastvalidator.constraints.utils.StringUtils;
import com.aliexpress.databank.constant.PayCheckSceneEnum;
import com.aliyun.openservices.log.Client;
import com.aliyun.openservices.log.common.LogContent;
import com.aliyun.openservices.log.common.LogItem;
import com.aliyun.openservices.log.common.QueriedLog;
import com.aliyun.openservices.log.exception.LogException;
import com.aliyun.openservices.log.response.GetLogsResponse;

import java.util.*;

public class SlsClientUtil {


    public static ArrayList<QueriedLog> query(String queryParam) {

     //   String accessKeyId = "LTAI4FqSkxF5p7DZkXAzHSW5";

        String accessKeySecret = "******************************";

        String project = "global-payment-center-s";

        String logstore = "payment";

        String endpoint = "zhangbei-corp-share.log.aliyuncs.com";
        //3个月
        int from = (int) (System.currentTimeMillis() / 1000 - 3600 * 24 * 90);
        //现在
        int to = (int) (System.currentTimeMillis() / 1000);
        Client client = SLSClientManager.Instance().getClient();
        GetLogsResponse getLogsResponse = null;

        getLogsResponse = logsResponse(project, logstore, from, to, client, getLogsResponse, queryParam);

        //担心sls日志延时，尝试10次查询
        if (getLogsResponse.GetLogs().size() == 0) {
            for (int i = 0; i <= 10; i++) {
                getLogsResponse = logsResponse(project, logstore, from, to, client, getLogsResponse, queryParam);
                if (getLogsResponse.GetLogs().size() != 0) {
                    break;
                }
            }
        }
        if (getLogsResponse.GetLogs().size() == 0) {
            return null;
        }
        return getLogsResponse.GetLogs();
    }


    private static GetLogsResponse logsResponse(String project, String logstore, int from, int to, Client client, GetLogsResponse getLogsResponse, String query) {
        try {
            getLogsResponse = client.GetLogs(project, logstore, from, to, "", query);
        } catch (LogException e) {
            e.printStackTrace();
        }
        return getLogsResponse;
    }

    //获取content内容
    public static List<String> slsResponse(ArrayList<QueriedLog> mItem) {
        List<String> list = new ArrayList<>();
        if (mItem.size() != 0) {
            for (QueriedLog log : mItem) {
                LogItem item = log.GetLogItem();
                for (LogContent content : item.mContents) {
                    if ("content".equals(content.mKey)) {
                        list.add(content.mValue);
                        break;
                    }
                }
            }
        }
        return list;
    }


    //根据消息id-key，去重返回
    public static List<Object> msgRemoveRepeat(List<String> msgList) {
        String msgKeyPattern = "key: (.*), msg:";
        String msgPattern = ", msg:(.*), res";
        List<String> tempList = new ArrayList<>();
        List<Object> result = new ArrayList<>();
        if (msgList.size() > 1) {
            for (int i = 0; i < msgList.size(); i++) {
                String temp = RegUtil.getRegRes(msgKeyPattern, msgList.get(i));
                if (!tempList.contains(temp)) {
                    result.add(JSONObject.parseObject(RegUtil.getRegRes(msgPattern, msgList.get(i))));
                }
                tempList.add(temp);
            }
        } else if ((msgList.size() == 1)) {
            result.add(JSONObject.parseObject(RegUtil.getRegRes(msgPattern, msgList.get(0))));
        }
        return result;
    }

    //获取msg对象
    public static List<Object> getmsg(List<String> msgList) {
        List<Object> result = new ArrayList<>();

        String msgres = msgList.get(0);
        int startnum = msgres.indexOf("msg:{");
        int endnum = msgres.indexOf("res:");
        String res1 = msgres.substring(startnum, endnum);
        String res2 = StringUtils.substringBeforeLast(res1, ",");
        int left = res2.indexOf("{");
        String res = res2.substring(left);

        result.add(JSONObject.parseObject(res));


        return result;

    }

    //获取content中request的data对象
    public static List<JSONObject> getRequestData(List<String> requestList, String requestPattern) {
        List<JSONObject> list = new ArrayList<>();
        if (requestList.size() != 0) {
            for (int i = 0; i < requestList.size(); i++) {
                String res = RegUtil.getRegRes(requestPattern, requestList.get(i));
                JSONObject resJson = JSONObject.parseObject(res);
                if (resJson.containsKey("data")) {
                    JSONObject data = JSONObject.parseObject(resJson.getString("data"));
                    list.add(data);
                }
            }
        }
        return list;
    }

    public static List<Object> slsSceneData(String scene, String uniqueID) {
        if (StringUtil.isEmpty(scene)) {
            return null;
        }
        //接口请求参数
        String requestPattern = "__input__: (.*),  __output__";

        String queryParam = "";
        List<String> response = null;
        List<Object> result = new ArrayList<>();
        if (scene.contains("REQUEST")) {
            if (scene.equals(PayCheckSceneEnum.CREATE_AND_PAY_REQUEST.name())) {
                queryParam = "__input__ and ipayagh.pay createAndPay and " + uniqueID;
            }
            if (scene.equals(PayCheckSceneEnum.IPAY_REFUND_REQUEST.name())) {
                queryParam = "ipayagh.refund  __input__ Channel-Invoke NOT consult NOT query and " + uniqueID;
            }

            ArrayList<QueriedLog> logArrayList = query(queryParam);
            if (logArrayList != null) {
                response = slsResponse(logArrayList);
                result.addAll(getRequestData(response, requestPattern));
            }
        } else if (scene.contains("MESSAGE")) {
            if (scene.equals(PayCheckSceneEnum.PAY_SUCCESS_AUTH_MESSAGE.name())) {
                queryParam = "global_payment_event_topic and PAY and AUTHORIZED and " + uniqueID;
            } else if (scene.equals(PayCheckSceneEnum.PAY_SUCCESS_CAPTURE_MESSAGE.name())) {
                queryParam = "global_payment_event_topic and PAY and SUCCEED and " + uniqueID;
            } else if (scene.equals(PayCheckSceneEnum.PROMOTION_REVERSE_MESSAGE.name())) {
                queryParam = "global_payment_event_topic and promotionReverse and " + uniqueID;
            }
            ArrayList<QueriedLog> logArrayList = query(queryParam);
            if (logArrayList != null) {
                response = slsResponse(logArrayList);
                result.addAll(msgRemoveRepeat(response));
            }

        }
        return result;
    }


    public static void main(String[] args) {
        //createAndPay
        List<Object> res = slsSceneData(PayCheckSceneEnum.CREATE_AND_PAY_REQUEST.name(), "12990501103211024876322883851");
        System.out.println(res);

        //refund
        res = slsSceneData(PayCheckSceneEnum.IPAY_REFUND_REQUEST.name(), "20211025154010801300188660211480267");
        System.out.println(res);


        //支付成功消息
        res = slsSceneData(PayCheckSceneEnum.PAY_SUCCESS_AUTH_MESSAGE.name(), "1000199960070033");
        System.out.println(res);


        //优惠撤销消息
        res = slsSceneData(PayCheckSceneEnum.PROMOTION_REVERSE_MESSAGE.name(), "1000199960070033");
        System.out.println(res);


    }
}
