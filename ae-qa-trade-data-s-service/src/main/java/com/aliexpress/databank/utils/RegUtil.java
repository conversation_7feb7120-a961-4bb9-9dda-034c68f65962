package com.aliexpress.databank.utils;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class RegUtil {

    //返回唯一匹配的
    public static String getRegRes(String regPattern, String res){
        Pattern p = Pattern.compile(regPattern);
        Matcher m = p.matcher(res);
        if (m.find()){
            return m.group(1);
        }
        return null;
    }

    //列表每个数据匹配返回
    public static List<String> getRegResList(String regPattern, List<String> res){
        List<String> result = new ArrayList<>();
        Pattern p = Pattern.compile(regPattern);
        for(int i=0;i<res.size();i++){
            Matcher m = p.matcher(res.get(i));
            if (m.find()){
                result.add(m.group(1));
            }
        }
        return null;
    }

    public static  String getNumber(String param) {
        String[] params = param.split("\\d+");
        for (String p : params) {
            param = p;
        }
        return param;
    }

}
