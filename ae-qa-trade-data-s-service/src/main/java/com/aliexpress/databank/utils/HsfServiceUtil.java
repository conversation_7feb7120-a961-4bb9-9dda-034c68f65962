package com.aliexpress.databank.utils;

/**
 * 跨单元泛化调用
 */
public class HsfServiceUtil {

//  public static ServiceParams buildServiceParams(
//      String interfaceName, String version, String dailySuffix) throws Exception {
//	    ServiceParams serviceParams = new ServiceParams();
//	    serviceParams.setServiceName(interfaceName);
//	    serviceParams.setServiceVersion(version);
//	    serviceParams.setGroup("HSF");
//	    // [设置] 泛化配置
//	    serviceParams.setDailyVersionSuffix(dailySuffix);
//	    // 设置configServiceCenter
//	    serviceParams.setConfigserverCenter(Arrays.asList("vpc-sg-pre", "aliyun-vpc-de-pre"));
//	    serviceParams.setIncludeRouters(Arrays.asList("cluster-router"));
//	    serviceParams.setIncludeFilters(Arrays.asList("cluster-filter"));
//	    return serviceParams;
//	}

//	@Bean(name="fundPreByOrderId")
//	public static JSONObject fundPreByOrderId(String tradeOrderId) throws Exception {
//		String serviceName = "com.aliexpress.qa.automation.service.FundOperationFacade";
//		String serviceVersion = "1.0.0";
//		String methodName = "fundPretreatment";
//		String[] parameterType = {"java.lang.String"};
//		List<Object> paramList = new ArrayList<>();
//		paramList.add(tradeOrderId);
//		GenericService genericService = ServiceFactory.createGenericService(serviceName, buildServiceParams(serviceName, serviceVersion, ""));
//		// 调用
//		RequestCtxUtil.setTargetCluster("default");
//		return (JSONObject) genericService.$invoke(methodName, parameterType, paramList.toArray());
//	}

}
