package com.aliexpress.databank.utils;

import com.alibaba.fastjson.JSONObject;

public class AddressUtil {

    public static JSONObject getEsAddress(Long buyerId, String countryCode, String addressId) {
        JSONObject address = new JSONObject();
        address.put("address", "test from data bank");
        address.put("appname", "ilogisticsaddress");
        address.put("contactPerson", "data bank");
        address.put("id", Long.parseLong(addressId));
        address.put("country", countryCode);
        address.put("isDefault", false);
        address.put("isForeigner", false);
        address.put("locale", "en_US");
        address.put("ownerSeq", buyerId);
        address.put("mobileNumberVerified", false);
        address.put("platform", "PC");
        address.put("phoneCountry", "+34");
        address.put("mobileNo", "**********");
        address.put("city", "Madrid");
        address.put("province", "Madrid");
        address.put("zip", "12345");
        return address;
    }

    public static JSONObject getUsAddress(Long buyerId, String countryCode, String addressId) {
        JSONObject address = new JSONObject();
        address.put("address", "test from data bank");
        address.put("appname", "ilogisticsaddress");
        address.put("contactPerson", "data bank");
        address.put("id", Long.parseLong(addressId));
        address.put("country", countryCode);
        address.put("isDefault", false);
        address.put("isForeigner", false);
        address.put("locale", "en_US");
        address.put("ownerSeq", buyerId);
        address.put("mobileNumberVerified", false);
        address.put("platform", "PC");
        address.put("phoneCountry", "+1");
        address.put("mobileNo", "**********");
        address.put("city", "Afton");
        address.put("province", "New York");
        address.put("zip", "123456");
        return address;
    }

    public static JSONObject getRuAddress(Long buyerId, String countryCode, String addressId) {
        JSONObject address = new JSONObject();
        address.put("address", "test from data bank");
        address.put("appname", "ilogisticsaddress");
        address.put("contactPerson", "data bank");
        address.put("id", Long.parseLong(addressId));
        address.put("country", countryCode);
        address.put("isDefault", false);
        address.put("isForeigner", false);
        address.put("locale", "en_US");
        address.put("ownerSeq", buyerId);
        address.put("mobileNumberVerified", false);
        address.put("platform", "PC");
        address.put("phoneCountry", "+7");
        address.put("mobileNo", "**********");
        address.put("city", "Moscow");
        address.put("province", "Moscow");
        address.put("zip", "123456");
        return address;
    }

    public static JSONObject getFrAddress(Long buyerId, String countryCode, String addressId) {
        JSONObject address = new JSONObject();
        address.put("address", "test from data bank");
        address.put("appname", "ilogisticsaddress");
        address.put("contactPerson", "data bank");
        address.put("id", Long.parseLong(addressId));
        address.put("country", countryCode);
        address.put("isDefault", false);
        address.put("isForeigner", false);
        address.put("locale", "en_US");
        address.put("ownerSeq", buyerId);
        address.put("mobileNumberVerified", false);
        address.put("platform", "PC");
        address.put("phoneCountry", "+33");
        address.put("mobileNo", "**********");
        address.put("city", "Ambleon");
        address.put("province", "Ain");
        address.put("zip", "12345");
        return address;
    }

    public static JSONObject getBrAddress(Long buyerId, String countryCode, String addressId) {
        JSONObject address = new JSONObject();
        address.put("address", "test from data bank");
        address.put("appname", "ilogisticsaddress");
        address.put("contactPerson", "data bank");
        address.put("id", Long.parseLong(addressId));
        address.put("country", countryCode);
        address.put("isDefault", false);
        address.put("isForeigner", false);
        address.put("locale", "en_US");
        address.put("ownerSeq", buyerId);
        address.put("mobileNumberVerified", false);
        address.put("platform", "PC");
        address.put("phoneCountry", "+55");
        address.put("mobileNo", "**********");
        address.put("city", "Aracaju");
        address.put("province", "Sergipe");
        address.put("zip", "12345");
        // TODO cpf -- ***********
        return address;
    }

    public static JSONObject getNlAddress(Long buyerId, String countryCode, String addressId) {
        JSONObject address = new JSONObject();
        address.put("address", "test from data bank");
        address.put("appname", "ilogisticsaddress");
        address.put("contactPerson", "data bank");
        address.put("id", Long.parseLong(addressId));
        address.put("country", countryCode);
        address.put("isDefault", false);
        address.put("isForeigner", false);
        address.put("locale", "en_US");
        address.put("ownerSeq", buyerId);
        address.put("mobileNumberVerified", false);
        address.put("platform", "PC");
        address.put("phoneCountry", "+31");
        address.put("mobileNo", "**********");
        address.put("city", "ALDE LEIE");
        address.put("province", "Friesland");
        address.put("zip", "1234AA");
        return address;
    }

    public static JSONObject getIlAddress(Long buyerId, String countryCode, String addressId) {
        JSONObject address = new JSONObject();
        address.put("address", "test from data bank");
        address.put("appname", "ilogisticsaddress");
        address.put("contactPerson", "data bank");
        address.put("id", Long.parseLong(addressId));
        address.put("country", countryCode);
        address.put("isDefault", false);
        address.put("isForeigner", false);
        address.put("locale", "en_US");
        address.put("ownerSeq", buyerId);
        address.put("mobileNumberVerified", false);
        address.put("platform", "PC");
        address.put("phoneCountry", "+39");
        address.put("mobileNo", "**********");
        address.put("city", "Fermo");
        address.put("province", "Marche");
        address.put("zip", "12345");
        return address;
    }

    public static JSONObject getPlAddress(Long buyerId, String countryCode, String addressId) {
        JSONObject address = new JSONObject();
        address.put("address", "test from data bank");
        address.put("appname", "ilogisticsaddress");
        address.put("contactPerson", "data bank");
        address.put("id", Long.parseLong(addressId));
        address.put("country", countryCode);
        address.put("isDefault", false);
        address.put("isForeigner", false);
        address.put("locale", "en_US");
        address.put("ownerSeq", buyerId);
        address.put("mobileNumberVerified", false);
        address.put("platform", "PC");
        address.put("phoneCountry", "+55");
        address.put("mobileNo", "**********");
        address.put("city", "Budzyn");
        address.put("province", "Wielkopolskie");
        address.put("zip", "12345");
        return address;
    }

    public static JSONObject getUkAddress(Long buyerId, String countryCode, String addressId) {
        JSONObject address = new JSONObject();
        address.put("address", "test from data bank");
        address.put("appname", "ilogisticsaddress");
        address.put("contactPerson", "data bank");
        address.put("id", Long.parseLong(addressId));
        address.put("country", countryCode);
        address.put("isDefault", false);
        address.put("isForeigner", false);
        address.put("locale", "en_US");
        address.put("ownerSeq", buyerId);
        address.put("mobileNumberVerified", false);
        address.put("platform", "PC");
        address.put("phoneCountry", "+44");
        address.put("mobileNo", "**********");
        address.put("city", "Fife");
        address.put("province", "Scotland");
        address.put("zip", "12345");
        return address;
    }

    public static JSONObject getKrAddress(Long buyerId, String countryCode, String addressId) {
        JSONObject address = new JSONObject();
        address.put("address", "test from data bank");
        address.put("appname", "ilogisticsaddress");
        address.put("contactPerson", "data bank");
        address.put("id", Long.parseLong(addressId));
        address.put("country", countryCode);
        address.put("isDefault", false);
        address.put("isForeigner", false);
        address.put("locale", "en_US");
        address.put("ownerSeq", buyerId);
        address.put("mobileNumberVerified", false);
        address.put("platform", "PC");
        address.put("phoneCountry", "+34");
        address.put("mobileNo", "**********");
        address.put("city", "Gangdong-gu");
        address.put("province", "Seoul");
        address.put("zip", "12345");
        return address;
    }

    public static JSONObject getDeAddress(Long buyerId, String countryCode, String addressId) {
        JSONObject address = new JSONObject();
        address.put("address", "test from data bank");
        address.put("appname", "ilogisticsaddress");
        address.put("contactPerson", "data bank");
        address.put("id", Long.parseLong(addressId));
        address.put("country", countryCode);
        address.put("isDefault", false);
        address.put("isForeigner", false);
        address.put("locale", "en_US");
        address.put("ownerSeq", buyerId);
        address.put("mobileNumberVerified", false);
        address.put("platform", "PC");
        address.put("phoneCountry", "+49");
        address.put("mobileNo", "**********");
        address.put("city", "Berlin");
        address.put("province", "Berlin");
        address.put("zip", "12345");
        return address;
    }

    public static JSONObject getItAddress(Long buyerId, String countryCode, String addressId) {
        JSONObject address = new JSONObject();
        address.put("address", "test from data bank");
        address.put("appname", "ilogisticsaddress");
        address.put("contactPerson", "data bank");
        address.put("id", Long.parseLong(addressId));
        address.put("country", countryCode);
        address.put("isDefault", false);
        address.put("isForeigner", false);
        address.put("locale", "en_US");
        address.put("ownerSeq", buyerId);
        address.put("mobileNumberVerified", false);
        address.put("platform", "PC");
        address.put("phoneCountry", "+39");
        address.put("mobileNo", "**********");
        address.put("city", "Campobasso");
        address.put("province", "Molise");
        address.put("zip", "12345");
        return address;
    }

    public static JSONObject getClAddress(Long buyerId, String countryCode, String addressId) {
        JSONObject address = new JSONObject();
        address.put("address", "test from data bank");
        address.put("appname", "ilogisticsaddress");
        address.put("contactPerson", "data bank");
        address.put("id", Long.parseLong(addressId));
        address.put("country", countryCode);
        address.put("isDefault", false);
        address.put("isForeigner", false);
        address.put("locale", "en_US");
        address.put("ownerSeq", buyerId);
        address.put("mobileNumberVerified", false);
        address.put("platform", "PC");
        address.put("phoneCountry", "+56");
        address.put("mobileNo", "**********");
        address.put("city", "Arica");
        address.put("province", "Arica y Parinacota");
        address.put("zip", "1234567");
        // TODO rub num
        return address;
    }

    public static JSONObject getCaAddress(Long buyerId, String countryCode, String addressId) {
        JSONObject address = new JSONObject();
        address.put("address", "test from data bank");
        address.put("appname", "ilogisticsaddress");
        address.put("contactPerson", "data bank");
        address.put("id", Long.parseLong(addressId));
        address.put("country", countryCode);
        address.put("isDefault", false);
        address.put("isForeigner", false);
        address.put("locale", "en_US");
        address.put("ownerSeq", buyerId);
        address.put("mobileNumberVerified", false);
        address.put("platform", "PC");
        address.put("phoneCountry", "+1");
        address.put("mobileNo", "**********");
        address.put("city", "Admiral");
        address.put("province", "Saskatchewan");
        address.put("zip", "A1B2C3");
        return address;
    }

    public static JSONObject getUaAddress(Long buyerId, String countryCode, String addressId) {
        JSONObject address = new JSONObject();
        address.put("address", "test from data bank");
        address.put("appname", "ilogisticsaddress");
        address.put("contactPerson", "data bank");
        address.put("id", Long.parseLong(addressId));
        address.put("country", countryCode);
        address.put("isDefault", false);
        address.put("isForeigner", false);
        address.put("locale", "en_US");
        address.put("ownerSeq", buyerId);
        address.put("mobileNumberVerified", false);
        address.put("platform", "PC");
        address.put("phoneCountry", "+380");
        address.put("mobileNo", "**********");
        address.put("city", "Izmailskyi");
        address.put("province", "Odeska");
        address.put("zip", "12345");
        return address;
    }

    public static JSONObject getJpAddress(Long buyerId, String countryCode, String addressId) {
        JSONObject address = new JSONObject();
        address.put("address", "test from data bank");
        address.put("appname", "ilogisticsaddress");
        address.put("contactPerson", "data bank");
        address.put("id", Long.parseLong(addressId));
        address.put("country", countryCode);
        address.put("isDefault", false);
        address.put("isForeigner", false);
        address.put("locale", "en_US");
        address.put("ownerSeq", buyerId);
        address.put("mobileNumberVerified", false);
        address.put("platform", "PC");
        address.put("phoneCountry", "+81");
        address.put("mobileNo", "**********");
        address.put("city", "Mikasa shi");
        address.put("province", "Hokkaido");
        address.put("zip", "1234567");
        return address;
    }

    public static JSONObject getAuAddress(Long buyerId, String countryCode, String addressId) {
        JSONObject address = new JSONObject();
        address.put("address", "test from data bank");
        address.put("appname", "ilogisticsaddress");
        address.put("contactPerson", "data bank");
        address.put("id", Long.parseLong(addressId));
        address.put("country", countryCode);
        address.put("isDefault", false);
        address.put("isForeigner", false);
        address.put("locale", "en_US");
        address.put("ownerSeq", buyerId);
        address.put("mobileNumberVerified", false);
        address.put("platform", "PC");
        address.put("phoneCountry", "+61");
        address.put("mobileNo", "**********");
        address.put("city", "Mikasa shi");
        address.put("province", "Victoria");
        address.put("zip", "1234");
        return address;
    }

    public static JSONObject getMxAddress(Long buyerId, String countryCode, String addressId) {
        JSONObject address = new JSONObject();
        address.put("address", "test from data bank");
        address.put("appname", "ilogisticsaddress");
        address.put("contactPerson", "data bank");
        address.put("id", Long.parseLong(addressId));
        address.put("country", countryCode);
        address.put("isDefault", false);
        address.put("isForeigner", false);
        address.put("locale", "en_US");
        address.put("ownerSeq", buyerId);
        address.put("mobileNumberVerified", false);
        address.put("platform", "PC");
        address.put("phoneCountry", "+52");
        address.put("mobileNo", "**********");
        address.put("city", "Armeria");
        address.put("province", "Colima");
        address.put("zip", "12345");
        return address;
    }

    public static JSONObject getBeAddress(Long buyerId, String countryCode, String addressId) {
        JSONObject address = new JSONObject();
        address.put("address", "test from data bank");
        address.put("appname", "ilogisticsaddress");
        address.put("contactPerson", "data bank");
        address.put("id", Long.parseLong(addressId));
        address.put("country", countryCode);
        address.put("isDefault", false);
        address.put("isForeigner", false);
        address.put("locale", "en_US");
        address.put("ownerSeq", buyerId);
        address.put("mobileNumberVerified", false);
        address.put("platform", "PC");
        address.put("phoneCountry", "+32");
        address.put("mobileNo", "**********");
        address.put("city", "test city");
        address.put("province", "test province");
        address.put("zip", "1234");
        return address;
    }

    public static JSONObject getCzAddress(Long buyerId, String countryCode, String addressId) {
        JSONObject address = new JSONObject();
        address.put("address", "test from data bank");
        address.put("appname", "ilogisticsaddress");
        address.put("contactPerson", "data bank");
        address.put("id", Long.parseLong(addressId));
        address.put("country", countryCode);
        address.put("isDefault", false);
        address.put("isForeigner", false);
        address.put("locale", "en_US");
        address.put("ownerSeq", buyerId);
        address.put("mobileNumberVerified", false);
        address.put("platform", "PC");
        address.put("phoneCountry", "+420");
        address.put("mobileNo", "**********");
        address.put("city", "test city");
        address.put("province", "test province");
        address.put("zip", "12345");
        return address;
    }

    public static JSONObject getSaAddress(Long buyerId, String countryCode, String addressId) {
        JSONObject address = new JSONObject();
        address.put("address", "test from data bank");
        address.put("appname", "ilogisticsaddress");
        address.put("contactPerson", "data bank");
        address.put("id", Long.parseLong(addressId));
        address.put("country", countryCode);
        address.put("isDefault", false);
        address.put("isForeigner", false);
        address.put("locale", "en_US");
        address.put("ownerSeq", buyerId);
        address.put("mobileNumberVerified", false);
        address.put("platform", "PC");
        address.put("phoneCountry", "+966");
        address.put("mobileNo", "*********");
        address.put("city", "Hazm Al Jalamid");
        address.put("province", "Northern Border");
        address.put("zip", "12345");
        return address;
    }

    // TODO ？？？
    public static JSONObject getChAddress(Long buyerId, String countryCode, String addressId) {
        JSONObject address = new JSONObject();
        address.put("address", "test from data bank");
        address.put("appname", "ilogisticsaddress");
        address.put("contactPerson", "data bank");
        address.put("id", Long.parseLong(addressId));
        address.put("country", countryCode);
        address.put("isDefault", false);
        address.put("isForeigner", false);
        address.put("locale", "en_US");
        address.put("ownerSeq", buyerId);
        address.put("mobileNumberVerified", false);
        address.put("platform", "PC");
        address.put("phoneCountry", "+34");
        address.put("mobileNo", "**********");
        address.put("city", "test city");
        address.put("province", "test province");
        address.put("zip", "12345");
        return address;
    }

    public static JSONObject getPtAddress(Long buyerId, String countryCode, String addressId) {
        JSONObject address = new JSONObject();
        address.put("address", "test from data bank");
        address.put("appname", "ilogisticsaddress");
        address.put("contactPerson", "data bank");
        address.put("id", Long.parseLong(addressId));
        address.put("country", countryCode);
        address.put("isDefault", false);
        address.put("isForeigner", false);
        address.put("locale", "en_US");
        address.put("ownerSeq", buyerId);
        address.put("mobileNumberVerified", false);
        address.put("platform", "PC");
        address.put("phoneCountry", "+351");
        address.put("mobileNo", "**********");
        address.put("city", "test city");
        address.put("province", "test province");
        address.put("zip", "1234567");
        return address;
    }

    public static JSONObject getAeAddress(Long buyerId, String countryCode, String addressId) {
        JSONObject address = new JSONObject();
        address.put("address", "test from data bank");
        address.put("appname", "ilogisticsaddress");
        address.put("contactPerson", "data bank");
        address.put("id", Long.parseLong(addressId));
        address.put("country", countryCode);
        address.put("isDefault", false);
        address.put("isForeigner", false);
        address.put("locale", "en_US");
        address.put("ownerSeq", buyerId);
        address.put("mobileNumberVerified", false);
        address.put("platform", "PC");
        address.put("phoneCountry", "+971");
        address.put("mobileNo", "**********");
        address.put("city", "Al Ruwaiya");
        address.put("province", "Dubai");
        address.put("zip", "12345");
        return address;
    }

}
