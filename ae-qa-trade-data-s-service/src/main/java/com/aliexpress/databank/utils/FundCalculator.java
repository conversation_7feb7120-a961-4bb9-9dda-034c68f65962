package com.aliexpress.databank.utils;

import com.alibaba.global.money.Money;
import com.alibaba.global.payment.calculator.domain.FundCalculateService;
import com.alibaba.global.payment.calculator.domain.response.CalResponse;

import javax.money.MonetaryAmount;
import java.math.BigDecimal;

public class FundCalculator {

    /**
     * Calculate monetary amount via exchange rate and source monetary amount.
     * <p>
     *
     * @param exchangeRate   exchange rate, if exchangeRate is null, will return null
     * @param sourceAmount   source monetary, if sourceAmount is null, will return null
     * @param targetCurrency target currency, if targetCurrency is null, will return null; if targetCurrency eqs
     *                       sourceAmount.currency will return sourceAmount
     * @param isPost2Pay
     * @return target monetary, e.g: source is post amount, target is payment amount.
     */
    public static MonetaryAmount calculateAmountByExchange(BigDecimal exchangeRate, MonetaryAmount sourceAmount,
                                                           String targetCurrency, Boolean isPost2Pay) {
        if (null == exchangeRate || null == sourceAmount || null == targetCurrency) {
            return null;
        }

        if (targetCurrency.equals(sourceAmount.getCurrency().getCurrencyCode())) {
            return sourceAmount;
        }

        if (null == isPost2Pay || isPost2Pay) {
            return convert(sourceAmount, targetCurrency, exchangeRate);
        } else {
            return revert(sourceAmount, targetCurrency, exchangeRate);
        }
    }

    /**
     * From post to pay currency.
     *
     * @param base
     * @param currency
     * @param exchangeRate
     * @return
     */
    private static MonetaryAmount convert(MonetaryAmount base, String currency, BigDecimal exchangeRate) {
        if (base.getCurrency().getCurrencyCode().equals(currency)) {
            return base;
        }
        Money baseMoney = Money.of(base);
        CalResponse calResponse = FundCalculateService.transferMoneyFromPostCurToPayCur(baseMoney, currency,
                exchangeRate);
        if (calResponse.isSucceed()) {
            return calResponse.getTargetAmount();
        } else {
            return null;
        }
    }

    /**
     * From pay to post currency.
     *
     * @param pay
     * @param currency
     * @param exchangeRate
     * @return
     */
    private static MonetaryAmount revert(MonetaryAmount pay, String currency, BigDecimal exchangeRate) {
        if (pay.getCurrency().getCurrencyCode().equals(currency)) {
            return pay;
        }
        Money payMoney = Money.of(pay);
        CalResponse calResponse = FundCalculateService.transferMoneyFromPayCurToPostCur(payMoney, currency,
                exchangeRate);
        if (calResponse.isSucceed()) {
            return calResponse.getTargetAmount();
        } else {
            return null;
        }
    }

}
