package com.aliexpress.databank.utils;

import com.alibaba.global.payment.api.facade.PaymentBopsToolFacade;
import com.alibaba.global.satellite.proxy.message.metaq.SatelliteMetaProducer;
import com.alibaba.rocketmq.client.producer.SendResult;
import com.alibaba.rocketmq.client.producer.SendStatus;
import com.alibaba.rocketmq.common.message.Message;
import com.taobao.eagleeye.EagleEye;
import org.springframework.beans.factory.annotation.Autowired;

import java.nio.charset.Charset;
import java.util.List;

/**
 * Created by limeng on 2022/3/8.
 */
public class AeResendMessage {
    private SatelliteMetaProducer metaProducer;

    @Autowired
    private PaymentBopsToolFacade paymentBopsToolFacade;

//    public String send(Long userId, String topic, String messageBody, String messageTag, String keys) {
//        try{
//            LandlordHSFUtils.setTenantIdForNextInvoke("AE_GLOBAL");
//            return paymentBopsToolFacade.sendMessage(userId,topic,messageBody,messageTag,keys) + " ,traceId:"+ EagleEye.getTraceId();
//        }catch (Exception e){
//            return "重试失败, traceId:"+ EagleEye.getTraceId() +" ,error:"+ e.getMessage();
//        }
//    }
    public  String sendMessage(String topic, String messageBody, String messageTag, String keys) {
        try {
            Message msg = new Message(topic, messageTag, messageBody.getBytes(Charset.forName("UTF-8")));
//            msg.setKeys(keys);
            SendResult sendResult = metaProducer.send(msg);

//            log(messageBody, topic, messageTag, keys, sendResult, true);
            boolean status=sendResult != null && SendStatus.SEND_OK.name().equals(sendResult.getSendStatus().name());

            return status + EagleEye.getTraceId();

        } catch (Exception e) {
//            log(messageBody, topic, messageTag, keys, null, false);
            System.out.println("send message failed, messageTopic:" + topic + "; key:" + keys+e.getMessage());
            return "false"+ EagleEye.getTraceId() ;
        }
    }

}
