package com.aliexpress.databank.reverse;

import com.alibaba.fastjson.JSON;

import java.util.List;

import static com.google.common.collect.Lists.newArrayList;

public class DataFix {
    public static void main(String[] args) {
        // TODO ids
        String str = "";
        String test = str.replace("\n", "");
        List ids = newArrayList(test.split(","));
        System.err.println("total: " + ids.size());
        int gap = 100;
        for (int i = 0; i < ids.size(); i += gap) {
            List newList = ids.subList(i, Math.min(i + gap, ids.size()));
            String idsStr = JSON.toJSONString(newList).replace("[", "").replace("]", "");
            // TODO scripts
            String script = "";
            // TODO replace
            String finalScript = script.replaceAll("Arrays.asList", "Arrays.asList(" + idsStr.replaceAll("\"", "") + ");");
            System.out.println(newList);
            System.out.println(newList.size());
            System.err.println(finalScript);
            System.err.println("==================================================================================================================");
        }
    }
}
