package com.aliexpress.databank.reverse;

import com.alibaba.fastjson.JSON;
import com.aliexpress.databank.dataobject.Warranty;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Type;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import static com.google.common.collect.Lists.newArrayList;

public class WarrantyService {

    public static void main(String[] args) {
//        String diamondStrPre = "";
//        String diamondStrOnline = "";
//
//        Gson gson = new Gson();
//        Type typeToken = new TypeToken<List<Warranty>>() {
//        }.getType();
//        List<Warranty> warrantiesPre = gson.fromJson(diamondStrPre, typeToken);
//        List<Warranty> warrantiesOnline = gson.fromJson(diamondStrOnline, typeToken);
//        Map<String, String> warrantiesPreMap = toMap(warrantiesPre);
//        Map<String, String> warrantiesOnlineMap = toMap(warrantiesOnline);
//        showDiff(warrantiesOnlineMap, warrantiesPreMap, "Online compare to Pre");
//        showDiff(warrantiesPreMap, warrantiesOnlineMap, "Pre compare to Online");
    }


    private static void showDiff(Map<String, String> one, Map<String, String> two, String context) {
        System.err.println(context);
        one.keySet().forEach(item -> {
            if (two.get(item) == null) {
                System.err.println("Second Missing: Key: " + item + " Value：" + JSON.toJSONString(one.get(item)));
            } else if (!two.get(item).equals(one.get(item))) {
                System.err.println("Diff:  First：Key: " + item + " Value：" + JSON.toJSONString(one.get(item)) +
                        ". Second: Key: " + item + " Value：" + JSON.toJSONString(two.get(item)));
            }
        });
    }

    private static Map<String, String> toMap(List<Warranty> warranties) {
        Map<String, String> map = new ConcurrentHashMap<>();
        for (Warranty warranty : warranties) {
            if (map.containsKey(warranty.getId())) {
                map.put(warranty.getId(), map.get(warranty.getId()) + warranty.getExpr());
            } else {
                map.put(warranty.getId(), warranty.getExpr());
            }
        }
        return map;
    }



}
