package com.aliexpress.databank.mapper;

import com.aliexpress.databank.dataobject.UserPool;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface UserPoolMapper {

    Boolean insertUser(@Param("userPool") UserPool userPool);

    Boolean insertUserList(@Param("userPoolList") List<UserPool> userPoolList);

    Boolean updateUser(@Param("userPool") UserPool userPool);

    Boolean updateUsed();

    List<UserPool> userList(@Param("userPool") UserPool userPool, @Param("num") Integer num);

    int userCount(@Param("isDel") Integer isDel, @Param("enableType") Integer enableType);

    int userCountById(@Param("accountId") Long accountId);

    Boolean deleteUserByUserName(@Param("userName") String userName);

    Boolean deleteUser(@Param("userPool") UserPool userPool);
}
