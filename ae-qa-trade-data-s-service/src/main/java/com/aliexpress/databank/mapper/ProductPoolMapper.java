package com.aliexpress.databank.mapper;

import com.aliexpress.databank.dataobject.ProductPool;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ProductPoolMapper {

    Boolean insertProduct(@Param("productPool") ProductPool productPool);

    Boolean insertProductList(@Param("productPoolList") List<ProductPool> productPoolList);

    Boolean updateProduct(@Param("productPool") ProductPool productPool);

    List<ProductPool> productList(@Param("productPool") ProductPool productPool, @Param("num") Integer num, @Param("lessPrice") Long lessPrice, @Param("largePrice") Long largePrice);

    int productCount(@Param("isDel") Integer isDel, @Param("enableType") Integer enableType);

    int productCountById(@Param("productId") Long productId);

    Boolean deleteProductByProductId(@Param("productId") Long productId);

    Boolean deleteProduct(@Param("productPool") ProductPool productPool);
}
