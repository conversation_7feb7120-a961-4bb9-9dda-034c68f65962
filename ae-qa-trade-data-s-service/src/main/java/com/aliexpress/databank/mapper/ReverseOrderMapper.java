package com.aliexpress.databank.mapper;

import com.aliexpress.databank.dataobject.ReverseOrder;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface ReverseOrderMapper {

    void insertReverseOrders(@Param("reverseOrders") List<ReverseOrder> reverseOrders);

    int findOrderCountByBuyerIdAndScenario(@Param("buyerId") Long buyerId, @Param("scenario") String scenario,
                                           @Param("scenarioIndex") int scenarioIndex);

    ReverseOrder findById(@Param("id") Long id);

    List<ReverseOrder> findByIds(@Param("ids") List<Long> ids);

    List<ReverseOrder> findUnShipmentOrder();

    List<ReverseOrder> findUnConfirmOrder();

    int updateReverseOrder(@Param("reverseOrder") ReverseOrder reverseOrder);

    List<Long> getOrderIdsByScenarioIndex(@Param("scenario") String scenario, @Param("scenarioIndex") String scenarioIndex, @Param("size") int size);

    List<Long> getOrderIdsByScenario(@Param("scenario") String scenario, @Param("size") int size);

    void updateUsedByOrderId(@Param("orderId") Long orderId);
}
