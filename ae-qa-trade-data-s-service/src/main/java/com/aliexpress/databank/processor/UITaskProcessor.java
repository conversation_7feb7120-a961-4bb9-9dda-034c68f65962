package com.aliexpress.databank.processor;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.aliexpress.databank.constant.Constant;
import com.aliexpress.databank.dataobject.UITask;
import com.aliexpress.databank.hsf.DataPoolService;
import net.dongliu.requests.Requests;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;


/**
 * <AUTHOR>
 * @function ui巡检
 */
@Component
public class UITaskProcessor extends JavaProcessor {

  private static final Logger log = LoggerFactory.getLogger(UITaskProcessor.class);

  @Autowired
  private DataPoolService dataPoolService;
  @Override
  public ProcessResult process(JobContext context) throws Exception {
    try {
        log.info("Begin ui task Processor...");
        //释放被占用的用户
        log.info("释放被占用的用户");
        dataPoolService.updateUsed();
        //参数配置case目录
        String param = context.getJobParameters();
        if (!param.contains(",")){
            return new ProcessResult(false, "任务配置参数不正确");
        }
        String moduleName = param.split(",")[0];
        Integer timeout = Integer.parseInt(param.split(",")[1]);
        log.info("任务参数：" + moduleName);
        JSONObject res0 = new JSONObject();
        JSONObject res1 = new JSONObject();
        Map<String,String> headers = new HashMap<>();
        headers.put("Content-Type","application/json");
        //ios
        for(UITask uiTask : Constant.IosTaskConfig){
            if (uiTask.getModuleName().equals(moduleName)){
                log.info("运行ios-ui自动化任务：" + uiTask.getTaskPath());
                Map<String, Object> taskHttpParams = new ConcurrentHashMap<>();
                taskHttpParams.put("target", "ae_basic_business_ios");
                JSONObject taskJson = new JSONObject();
                taskJson.put("iosPackage",Constant.iosPackageUrl);
                taskJson.put("taskContent",uiTask.getTaskPath());
                taskJson.put("timeout",timeout);
                taskHttpParams.put("body",JSON.toJSONString(taskJson));
                String data = JSON.toJSONString(taskHttpParams);
                log.info("ios创建任务请求参数：" + data);
                res0  = JSONObject.parseObject(Requests.post("http://pre-aemit.alibaba-inc.com/api/landing/job/create").headers(headers).body(data).send().readToText());
                log.info("ios创建任务返回结果：" + res0.toJSONString());
                break;
            }
        }
        //android
        for(UITask uiTask : Constant.AndroidTaskConfig){
            if (uiTask.getModuleName().equals(moduleName)){
                log.info("运行android-ui自动化任务：" + uiTask.getTaskPath());
                Map<String, Object> taskHttpParams = new ConcurrentHashMap<>();
                taskHttpParams.put("target", "ae_basic_business");
                JSONObject taskJson = new JSONObject();
                taskJson.put("androidPackage",Constant.androidPackageUrl);
                taskJson.put("taskContent",uiTask.getTaskPath());
                taskJson.put("timeout",timeout);
                taskHttpParams.put("body",JSON.toJSONString(taskJson));
                String data = JSON.toJSONString(taskHttpParams);
                log.info("android创建任务请求参数：" + data);
                try{
                    res1  = JSONObject.parseObject(Requests.post("http://pre-aemit.alibaba-inc.com/api/landing/job/create").headers(headers).body(data).send().readToText());
                }catch (Exception e) {
                    log.error("unknown exception, ", e);

                }
                log.info("android创建任务返回结果：" + res1.toJSONString());
                break;
            }
        }
        log.info("Finish ui task Processor.");
        String sequence0 = res0.getJSONObject("result").getString("sequence");
        String sequence1 = res1.getJSONObject("result").getString("sequence");
        Date date=new Date();
        SimpleDateFormat dateFormat=new SimpleDateFormat("yyyyMM");
        return new ProcessResult(true, ("ios执行日志："+ "https://aemit.oss-cn-hangzhou.aliyuncs.com/landing/logs/"+ dateFormat.format(date) + "/"+ sequence0 + ".tar.gz\n\n" +
                "android执行日志："+ "https://aemit.oss-cn-hangzhou.aliyuncs.com/landing/logs/"+ dateFormat.format(date) + "/"+ sequence1 + ".tar.gz"));

    } catch (Exception e) {
        log.error("unknown exception, ", e);
        return new ProcessResult(false, e.toString());
    }
  }
}

