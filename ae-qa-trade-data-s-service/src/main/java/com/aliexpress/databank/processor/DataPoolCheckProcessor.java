package com.aliexpress.databank.processor;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.aliexpress.databank.constant.DataDisableScenario;
import com.aliexpress.databank.dataobject.DataDisableDTO;
import com.aliexpress.databank.dataobject.ProductPool;
import com.aliexpress.databank.dataobject.UserPool;
import com.aliexpress.databank.hsf.DataApiService;
import com.aliexpress.databank.hsf.DataPoolService;
import com.aliexpress.databank.hsf.SendDingMsgService;
import com.dingtalk.api.response.OapiRobotSendResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.List;

@Component
public class DataPoolCheckProcessor extends JavaProcessor{

    private static final Logger log = LoggerFactory.getLogger(DataPoolCheckProcessor.class);

    @Autowired
    DataApiService dataApiService;

    @Autowired
    DataPoolService dataPoolService;

    @Autowired
    SendDingMsgService sendDingMsgService;

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        try {
            log.info("Begin Check DataPool Processor...");
            //查询用户
            List<UserPool> userPoolList = dataPoolService.queryUserInfo(new UserPool(),1000);
            StringBuilder content = new StringBuilder();
            content.append("用户检测结果：\r\n");
            DataDisableDTO userResult = dataApiService.checkUser(userPoolList);
            log.info("用户检测结果：\n"+userResult);
            int user_flag = 0;
            int product_flag = 0;
            if (!isAllEmpty(userResult)){
                if (!userResult.getUserNoExist().isEmpty()){
                    content.append("\t\n【").append(DataDisableScenario.USER_NO_EXIST.getName()).append("】：\t");
                    content.append(String.join("\n",userResult.getUserNoExist())).append("\n\n");
                }
                if(!userResult.getUserRiskControl().isEmpty()){
                    content.append("\t\n【").append(DataDisableScenario.USER_RISK_CONTROL.getName()).append("】：\t");
                    content.append(String.join("\n",userResult.getUserRiskControl())).append("\n");
                }
                if(!userResult.getAddressNotMatch().isEmpty()){
                    content.append("\t\n【").append(DataDisableScenario.ADDRESS_NOT_MATCH.getName()).append("】：\t");
                    content.append(String.join("\n",userResult.getUserRiskControl())).append("\n\n");
                }
                if(!userResult.getNeedConfirm().isEmpty()){
                    content.append("\t\n【").append(DataDisableScenario.NEED_CONFIRM.getName()).append("】：\t");
                    content.append(String.join("\n",userResult.getNeedConfirm())).append("\n\n");
                }
            }
            else {
                user_flag = 1;
                log.info("user全通过");
            }
            //查询商品
            List<ProductPool> productPoolList = dataPoolService.queryProductInfo(new ProductPool(),1000, 0L, 0L);
            log.info("商品："+productPoolList);
            DataDisableDTO productResult = dataApiService.checkProduct(productPoolList);
            log.info("商品检测结果："+productResult);
            content.append("\n\n商品检测结果：\n");
            if (!isAllEmpty(productResult)){
                if (!productResult.getProductNoExist().isEmpty()){
                    content.append("\t\n【").append(DataDisableScenario.PRODUCT_NO_EXIST.getName()).append("】：\t");
                    content.append(String.join("\n",productResult.getProductNoExist())).append("\n\n");
                }
                if (!productResult.getProductStatusError().isEmpty()){
                    content.append("\t\n【").append(DataDisableScenario.PRODUCT_STATUS_ERROR.getName()).append("】：\t");
                    content.append(String.join("\n",productResult.getProductStatusError())).append("\n\n");
                }
                if (!productResult.getProductNotEnough().isEmpty()){
                    content.append("\t\n【").append(DataDisableScenario.PRODUCT_NOT_ENOUGH.getName()).append("】：\t");
                    content.append(String.join("\n",productResult.getProductNotEnough())).append("\n\n");
                }
                if (!productResult.getProductStockWarn().isEmpty()){
                    content.append("\t\n【").append(DataDisableScenario.PRODUCT_STOCK_WARN.getName()).append("】：\t");
                    content.append(String.join("\n",productResult.getProductStockWarn())).append("\n\n");
                }
            }else {
                product_flag = 1;
                log.info("product全通过");
            }

            // 统计不可用数量
            content.append("\n用户统计：\n");
            content.append("\t\t可用：").append(dataPoolService.userCount(0, 1)).append("\n");
            content.append("\t\t不可用：").append(dataPoolService.userCount(0, 0)).append("\n");
//            content.append("\t可删除：").append(dataPoolService.userCount(1, null)).append("\n");
            content.append("\n商品统计：\n");
            content.append("\t\t可用：").append(dataPoolService.productCount(0, 1)).append("\n");
            content.append("\t\t不可用：").append(dataPoolService.productCount(0, 0)).append("\n");
//            content.append("\t可删除：").append(dataPoolService.productCount(1, null)).append("\n");
            log.info("Finish Check DataPool Processor.");

            log.info("检测结果：" + content);

            if (user_flag == 0 || product_flag == 0){
                //发送钉钉
                OapiRobotSendResponse oapiRobotSendResponse = sendDingMsgService.sendDingTalkGroup(content.toString());
                log.info("钉钉消息："+oapiRobotSendResponse);
                if (oapiRobotSendResponse.isSuccess()){
                    return new ProcessResult(true,content.toString());
                }
                else {
                    return new ProcessResult(false);
                }
            }
            else {
                return new ProcessResult(true,"验证全通过");
            }

        } catch (Exception e) {
            log.error("unknown exception, ", e);
            return new ProcessResult(false,e.toString());
        }
    }
    public Boolean isAllEmpty(DataDisableDTO dataDisableDTO) throws Exception{
        Class cls = DataDisableDTO.class;
        Field[] fields = cls.getDeclaredFields();
        for(int i=0; i<fields.length; i++){
            Field f = fields[i];
            f.setAccessible(true);
            Object obj = f.get(dataDisableDTO);
            if (null != obj ){
                if (f.getType().equals(List.class) && !((List)obj).isEmpty()){
                    return false;
                }
            }
        }
        return true;
    }

//    public static void main(String[] args)throws Exception {
//        DataDisableDTO dataDisableDTO = new DataDisableDTO();
//        List<String> noExistList = new ArrayList<>();
//        List<String> statusErrorList = new ArrayList<>();
//        List<String> notEnoughList = new ArrayList<>();
//        List<String> stockWarnList = new ArrayList<>();
//        noExistList.add("1212");
//        noExistList.add("232432423");
//        System.out.println(String.join("\n",noExistList));
//
//    }
}
