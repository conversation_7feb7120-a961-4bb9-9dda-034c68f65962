package com.aliexpress.databank.processor;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.aliexpress.databank.service.PlaceOrderTemplate;
import com.aliexpress.databank.service.impl.BasePlaceOrderStrategy;
import org.javers.common.collections.Lists;
import org.reflections.Reflections;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

@Component
public class PlaceOrderByReverseProcessor extends JavaProcessor implements ApplicationContextAware {

    @Autowired
    private PlaceOrderTemplate placeOrderTemplate;

    private ApplicationContext applicationContext;

    private Logger log = LoggerFactory.getLogger(this.getClass());

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        try {
            log.error("Begin Place Reverse Order Processor...");
            List<Long> buyerIds = Lists.asList(1859103223L);
            log.error("placeOrderTemplate: " + JSONObject.toJSONString(placeOrderTemplate));
            buyerIds.forEach(buyerId -> {
                Reflections reflections = new Reflections("com.aliexpress.databank.service.impl");
                Set<Class<? extends BasePlaceOrderStrategy>> classes = reflections.getSubTypesOf(BasePlaceOrderStrategy.class);
                classes.stream().forEach(clazz -> {
                    BasePlaceOrderStrategy placeOrderStrategy = applicationContext.getBean(clazz);
                    try {
                        log.error("placeOrderStrategy: " + JSONObject.toJSONString(placeOrderStrategy));
                        placeOrderTemplate.placeOrder(buyerId, placeOrderStrategy);
                    }catch (Exception e){
                        log.error("Fail to place order: " + JSONObject.toJSONString(placeOrderStrategy));
                    }
                });
            });
            log.error("Finish Place Reverse Order Processor.");
            return new ProcessResult(true);
        } catch (Exception e) {
            log.error("unknown exception, ", e);
            return new ProcessResult(false);
        }
    }
}
