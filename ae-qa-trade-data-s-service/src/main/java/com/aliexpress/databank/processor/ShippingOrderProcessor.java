package com.aliexpress.databank.processor;

import com.alibaba.global.order.management.api.facade.OrderQueryForBuyerFacade;
import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.JavaProcessor;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import com.aliexpress.databank.service.ShipOrderService;
import com.aliexpress.databank.service.TradeService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

@Component
public class ShippingOrderProcessor extends JavaProcessor implements ApplicationContextAware {

    private ApplicationContext applicationContext;

    private Logger log = LoggerFactory.getLogger(this.getClass());

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    @Override
    public ProcessResult process(JobContext context) throws Exception {
        try {
            log.info("Begin Shipping Order Processor...");

            ShipOrderService shipOrderService = applicationContext.getBean(ShipOrderService.class);
            shipOrderService.shipOrders();

            log.info("Finish Shipping Order Processor.");
            return new ProcessResult(true);
        } catch (Exception e) {
            log.error("unknown exception, ", e);
            return new ProcessResult(false);
        }
    }
}
