[{"country": "BR", "data": {"country": "BR", "province": "Acre", "zip": "13165-000", "countryName": "Brazil", "passportOrganization": null, "city": "Acrelandia", "cityCode": "917465689977000000", "contactPerson": "BR NOCPF", "phoneCountry": "+55", "selfPickUpPointstatus": null, "passportNo": null, "provinceCode": "917465680000000000", "mobileNo": "1111111111", "buyerAddressType": "residential", "ownerSeq": 1864160462, "platform": "PC", "class": "com.alibaba.intl.ae.logistics.address.dto.WlMailingAddressDTO", "address": "gfdgdf", "address2": null}}, {"country": "KR", "data": {"country": "KR", "townCode": null, "fieldErrorMessageString": "[]", "placeId": null, "buyerAddressType": "residential", "mobileNumberVerified": false, "isForeigner": false, "ownerSeq": 1860742465, "features": {"mobile_no_verified": "false", "latitude": null, "locale": "local", "longitude": null}, "province": "Gangwon-do", "appname": "ilogist<PERSON><PERSON>dress", "expressCode": null, "cpf": null, "tag": null, "selfPickupPointName": null, "longitude": null, "zip": "12345", "encryptCpf": "", "town": null, "featuresString": "{\"mobile_no_verified\":\"false\",\"locale\":\"local\"}", "taxNumber": null, "selfPickupPointContact": null, "version": 0, "firstName": null, "faxArea": null, "isDefault": false, "phoneNumber": null, "selfPickupPointPhone": null, "passportNoDate": null, "fieldErrorMessageList": [], "district": null, "faxCountry": null, "phoneArea": null, "longitudeStr": null, "countryName": "Korea", "passportOrganization": null, "birthday": null, "lastName": null, "gmtModified": 1596540393000, "districtCode": null, "distance": null, "encryptPassportNo": "***81***", "city": "<PERSON><PERSON><PERSON><PERSON>gun", "cityCode": "919800010002000000", "latitude": null, "distanceString": null, "contactPerson": "KR  NO Customs", "idNumber": null, "locale": "local", "platform": "PC", "phoneCountry": "+82", "defaultShipping": null, "class": "com.alibaba.intl.ae.logistics.address.dto.WlMailingAddressDTO", "selfPickUpPointstatus": null, "email": null, "passportNo": "", "snapshotId": 5242460001, "passportVisaURL": null, "address": "sd", "address2": null, "provinceCode": "919800010000000000", "fullName": null, "mobileNo": "01*********", "gmtCreate": 1596540393000, "cityInLocalLanguage": null, "latitudeStr": null, "selfPickUpPointId": null, "faxNumber": null, "middleName": null, "provinceInLocalLanguage": null, "relatedAddressId": null, "selfPickupPointOfficeTime": null, "needValidateMobileNumber": null, "passportPhotoURL": null}}, {"country": "FR", "data": {"country": "FR", "townCode": null, "fieldErrorMessageString": "[]", "locationTreeName": null, "placeId": null, "buyerAddressType": "residential", "mobileNumberVerified": false, "isForeigner": false, "ownerSeq": 1864940087, "features": {"suggestAddress": "{\"locationTreeAddressId\":\"907202900001000000-907202900001222000\",\"locationTreeAddressName\":\"Ain,Bohas-meyriat-rignat\"}", "mobile_no_verified": "false", "appName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "globalAddressStatus": "ERROR", "certificateVerified": "false", "tag": "local", "locale": "local", "mobileNoVerified": "false", "platform": "MOBILE"}, "province": "Ain", "appname": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "expressCode": null, "cpf": null, "tag": "local", "id": 1367620013, "selfPickupPointName": null, "longitude": null, "locationTreeId": null, "zip": "01250", "encryptCpf": null, "town": null, "featuresString": "{\"suggestAddress\":\"{\\\"locationTreeAddressId\\\":\\\"907202900001000000-907202900001222000\\\",\\\"locationTreeAddressName\\\":\\\"Ain,Bohas-meyriat-rignat\\\"}\",\"mobile_no_verified\":\"false\",\"appName\":\"wsmobileserver\",\"globalAddressStatus\":\"ERROR\",\"certificateVerified\":\"false\",\"tag\":\"local\",\"locale\":\"local\",\"mobileNoVerified\":\"false\",\"platform\":\"MOBILE\"}", "taxNumber": null, "selfPickupPointContact": null, "version": 1, "faxArea": null, "firstName": null, "isDefault": false, "phoneNumber": null, "selfPickupPointPhone": null, "fieldErrorMessageList": [], "passportNoDate": null, "faxCountry": null, "district": "", "phoneArea": null, "longitudeStr": null, "countryName": "France", "passportOrganization": null, "birthday": null, "lastName": null, "gmtModified": 1634626126917, "districtCode": "", "distance": null, "encryptPassportNo": null, "city": "Ambleon", "cityCode": "907202900001009000", "latitude": null, "distanceString": null, "contactPerson": "Fr test", "idNumber": null, "locale": "local", "platform": "MOBILE", "phoneCountry": "+33", "rutNo": null, "addressAddition": null, "defaultShipping": null, "class": "com.alibaba.intl.ae.logistics.address.dto.WlMailingAddressDTO", "selfPickUpPointstatus": null, "email": null, "passportNo": null, "snapshotId": null, "passportVisaURL": null, "address": "140 RUE DU FOUR", "foreignerPassportNo": null, "address2": "Suite", "provinceCode": "907202900001000000", "fullName": null, "mobileNo": "*********0", "cityInLocalLanguage": null, "gmtCreate": 1634626126789, "taxCompany": null, "selfPickUpPointId": null, "latitudeStr": null, "faxNumber": null, "middleName": null, "provinceInLocalLanguage": null, "selfPickupPointOfficeTime": null, "relatedAddressId": null, "needValidateMobileNumber": null, "passportPhotoURL": null}}, {"country": "RU", "data": {"country": "RU", "townCode": null, "fieldErrorMessageString": "[]", "locationTreeName": null, "placeId": null, "buyerAddressType": "residential", "mobileNumberVerified": false, "isForeigner": false, "ownerSeq": 1619174001, "features": {"mobile_no_verified": "false", "appName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "certificateVerified": "false", "tag": "local", "locale": "local", "mobileNoVerified": "false", "platform": "MOBILE"}, "province": "Moscow", "appname": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cpf": null, "expressCode": null, "tag": "local", "id": 3342096106, "selfPickupPointName": null, "longitude": null, "locationTreeId": null, "zip": "123455", "encryptCpf": null, "town": null, "featuresString": "{\"mobile_no_verified\":\"false\",\"appName\":\"wsmobileserver\",\"certificateVerified\":\"false\",\"tag\":\"local\",\"locale\":\"local\",\"mobileNoVerified\":\"false\",\"platform\":\"MOBILE\"}", "taxNumber": null, "selfPickupPointContact": null, "version": 0, "faxArea": null, "firstName": null, "isDefault": false, "phoneNumber": null, "fieldErrorMessageList": [], "passportNoDate": null, "selfPickupPointPhone": null, "faxCountry": null, "district": "", "phoneArea": null, "longitudeStr": null, "countryName": "Russian Federation", "passportOrganization": null, "birthday": null, "lastName": null, "gmtModified": 1640663353435, "districtCode": "", "encryptPassportNo": null, "distance": null, "city": "Moscow", "cityCode": "917477679070000000", "latitude": null, "distanceString": null, "contactPerson": "Tets tets", "idNumber": null, "locale": "local", "platform": "MOBILE", "phoneCountry": "+7", "rutNo": null, "addressAddition": null, "defaultShipping": null, "class": "com.alibaba.intl.ae.logistics.address.dto.WlMailingAddressDTO", "selfPickUpPointstatus": null, "email": null, "passportNo": null, "passportVisaURL": null, "snapshotId": null, "address": "Iijjdjdj", "foreignerPassportNo": null, "address2": "Nsbdbd", "provinceCode": "917477670000000000", "fullName": null, "mobileNo": "1234561234", "cityInLocalLanguage": null, "gmtCreate": 1640663353435, "taxCompany": null, "latitudeStr": null, "selfPickUpPointId": null, "faxNumber": null, "middleName": null, "provinceInLocalLanguage": null, "relatedAddressId": null, "selfPickupPointOfficeTime": null, "needValidateMobileNumber": null, "passportPhotoURL": null}}, {"country": "ES", "data": {"country": "ES", "townCode": null, "fieldErrorMessageString": "[]", "locationTreeName": null, "placeId": null, "buyerAddressType": "residential", "mobileNumberVerified": false, "isForeigner": false, "ownerSeq": 1619174001, "features": {"mobile_no_verified": "false", "appName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "certificateVerified": "false", "tag": "local", "locale": "local", "mobileNoVerified": "false", "platform": "MOBILE"}, "province": "A Coruna", "appname": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cpf": null, "expressCode": null, "tag": "local", "id": 3342332264, "selfPickupPointName": null, "longitude": null, "locationTreeId": null, "zip": "12345", "encryptCpf": null, "town": null, "featuresString": "{\"mobile_no_verified\":\"false\",\"appName\":\"wsmobileserver\",\"certificateVerified\":\"false\",\"tag\":\"local\",\"locale\":\"local\",\"mobileNoVerified\":\"false\",\"platform\":\"MOBILE\"}", "taxNumber": null, "selfPickupPointContact": null, "version": 0, "faxArea": null, "firstName": null, "isDefault": false, "phoneNumber": null, "fieldErrorMessageList": [], "passportNoDate": null, "selfPickupPointPhone": null, "faxCountry": null, "district": "", "phoneArea": null, "longitudeStr": null, "countryName": "Spain", "passportOrganization": null, "birthday": null, "lastName": null, "gmtModified": 1640590955321, "districtCode": "", "encryptPassportNo": null, "distance": null, "city": "A Bana", "cityCode": "919971656567047000", "latitude": null, "distanceString": null, "contactPerson": "Dhhd yehd", "idNumber": null, "locale": "local", "platform": "MOBILE", "phoneCountry": "+34", "rutNo": null, "addressAddition": null, "defaultShipping": null, "class": "com.alibaba.intl.ae.logistics.address.dto.WlMailingAddressDTO", "selfPickUpPointstatus": null, "email": null, "passportNo": null, "passportVisaURL": null, "snapshotId": null, "address": "Djjnnd", "foreignerPassportNo": null, "address2": "Hdh BBC ch", "provinceCode": "919971656567000000", "fullName": null, "mobileNo": "*********", "cityInLocalLanguage": null, "gmtCreate": 1640590955321, "taxCompany": null, "latitudeStr": null, "selfPickUpPointId": null, "faxNumber": null, "middleName": null, "provinceInLocalLanguage": null, "relatedAddressId": null, "selfPickupPointOfficeTime": null, "needValidateMobileNumber": null, "passportPhotoURL": null}}, {"country": "US", "data": {"country": "US", "townCode": null, "fieldErrorMessageString": "[]", "locationTreeName": null, "placeId": null, "buyerAddressType": "residential", "mobileNumberVerified": false, "isForeigner": false, "ownerSeq": 1619174001, "features": {"mobile_no_verified": "false", "certificateVerified": "false", "tag": "local", "locale": "local", "mobileNoVerified": "false", "platform": "PC"}, "province": "Alabama", "appname": null, "cpf": null, "expressCode": null, "tag": "local", "id": 3341960020, "selfPickupPointName": null, "longitude": null, "locationTreeId": null, "zip": "12345", "encryptCpf": null, "town": null, "featuresString": "{\"mobile_no_verified\":\"false\",\"certificateVerified\":\"false\",\"tag\":\"local\",\"locale\":\"local\",\"mobileNoVerified\":\"false\",\"platform\":\"PC\"}", "taxNumber": null, "selfPickupPointContact": null, "version": 0, "faxArea": null, "firstName": null, "isDefault": false, "phoneNumber": null, "fieldErrorMessageList": [], "passportNoDate": null, "selfPickupPointPhone": null, "faxCountry": null, "district": "", "phoneArea": null, "longitudeStr": null, "countryName": "United States", "passportOrganization": null, "birthday": null, "lastName": null, "gmtModified": 1640590553346, "districtCode": "", "encryptPassportNo": null, "distance": null, "city": "3 notch", "cityCode": "922865766013000000", "latitude": null, "distanceString": null, "contactPerson": "Fgvv", "idNumber": null, "locale": "local", "platform": "PC", "phoneCountry": "+1", "rutNo": null, "addressAddition": null, "defaultShipping": null, "class": "com.alibaba.intl.ae.logistics.address.dto.WlMailingAddressDTO", "selfPickUpPointstatus": null, "email": null, "passportNo": null, "passportVisaURL": null, "snapshotId": null, "address": "Qedddf", "foreignerPassportNo": null, "address2": "Fggvbbb", "provinceCode": "922865760000000000", "fullName": null, "mobileNo": "1234567590", "cityInLocalLanguage": null, "gmtCreate": 1640590553346, "taxCompany": null, "latitudeStr": null, "selfPickUpPointId": null, "faxNumber": null, "middleName": null, "provinceInLocalLanguage": null, "relatedAddressId": null, "selfPickupPointOfficeTime": null, "needValidateMobileNumber": null, "passportPhotoURL": null}}]